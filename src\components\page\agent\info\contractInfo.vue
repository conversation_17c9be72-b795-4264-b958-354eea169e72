<template>
    <div>
        <el-row  :gutter="20" style="background: #fff;margin: 10px">
            <el-col :span="24">
                <el-footer>
                    <el-button size="mini" @click="contractSupplyEdit(contract)" type="primary">补签</el-button>
                    <el-button size="mini" @click="getUrl(contract)" type="primary">签名</el-button>

                    <el-button v-show="contract.holidayId ==null && contract.contractType==0" size="mini" @click="contractHoliday(contract)" type="info" >放假规则</el-button>
                    <el-button v-show="contract.holidayId!=null && contract.contractType==0" size="mini" @click="contractHoliday(contract)" type="primary" >放假规则</el-button>
                    <el-button size="mini" type="primary"   @click="downloadTest(contract.id)" v-show=" contract.contractType==0">下载</el-button>
                    <el-button size="mini" type="primary"   @click="downloadYueSaoTest(contract.id)" v-show=" contract.contractType==1">下载</el-button>
                    <el-button v-if="contract.status==1||contract.status==null" size="mini" @click="save(contract,0)" type="primary">暂存</el-button>
                    <el-button v-if="contract.status==0" size="mini" @click="save(contract,1)" type="info">生效</el-button>
                    <el-button  size="mini" @click="contractEnclosure(contract)" :type="contract.enCount==null?'info':'primary'" >纸质</el-button>
                </el-footer>
            </el-col>
            <el-col :span="8">
                <el-card shadow="hover" body-style="padding: 5px" >
                    <center>
                        <iframe
                                id="iframeId" :src="url" frameborder="0" class="pc iframe"  scrolling="auto" v-if="url!==null">
                        </iframe>
                    </center>
                </el-card>
            </el-col>
            <el-col :span="16">
                <el-card shadow="hover" body-style="padding: 5px" >
                    <contractUpdate v-if="contract!==null" @close-modal="closeCurrModal" :model="contract" style="width: 100%"></contractUpdate>
                </el-card>
            </el-col>



        </el-row>
        <Modal v-model="contractSupplyModal" class="Modal" :width="screenWidth"  title="保姆合同补充" :mask-closable="false" >
            <div class="addBody">
                <contractSupply v-if="contractSupplyModal" @init-choose="initChooseProject"
                                @close-modal="closeCurrModal" :model="show"></contractSupply>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <el-dialog
                title="签名Url地址"
                :visible.sync="dialogVisible"
                :modal="false"
                width="50%"
        >
            <!--                    <el-input placeholder="请输入技能说明" v-model="url"  id="urlinput">-->
            <!--                    </el-input>-->
            <el-button v-if="mImageUrl!=null" size="mini" @click="reMemberSign()" type="primary">重置客户签名</el-button>
            <el-button v-if="eImageUrl!=null" size="mini" @click="reEmployeeSign()" type="primary">重置阿姨签名</el-button>
            <h2>阿姨签名地址</h2>
            <img width="50%" :src="eImageUrl" alt="">
            <div>点击复制Url,并把地址发送给指定的阿姨。完成合同的阿姨签名。</div><hr>
            <article id="article">
                {{url}}
            </article>
            <br>
            <br>
            <br>
            <h2>客户签名地址</h2>
            <img width="50%" :src="mImageUrl" alt="">
            <div>点击复制Url,并把地址发送给指定的客户。完成合同的客户签名。</div><hr>
            <article id="article2">
                {{url2}}
            </article>
            <span slot="footer" class="dialog-footer">
                        <el-button @click="dialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="dialogVisible = false,copyurl()">复制阿姨Url</el-button>
                        <el-button type="primary" @click="dialogVisible = false,copyurl2()">复制客户Url</el-button>
            </span>
        </el-dialog>
        <Modal v-model="contractHolidayModal" class="Modal" :width="screenWidth"  title="保姆合同假期规则" :mask-closable="false" >
            <div class="addBody">
                <contractHoliday v-if="contractHolidayModal" @init-choose="initChooseProject"
                                 @close-modal="closeCurrModal" :model="show"></contractHoliday>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="contractEnclosureModal" class="Modal" :width="screenWidth"  title="合同上传纸质合同" :mask-closable="false" >
            <div class="addBody">
                <contract-enclosure v-if="contractEnclosureModal" @init-choose="initChooseProject"
                                    @close-modal="closeCurrModal" :model="show"></contract-enclosure>
            </div>
            <div slot="footer">
            </div>
        </Modal>
    </div>

</template>

<script>
    import contractAdd from '@/components/page/agent/choose/contractAdd.vue'
    import contractYuesaoAdd from '@/components/page/agent/choose/contractYuesaoAdd.vue'
    import contractUpdate from '@/components/page/agent/choose/contractUpdate.vue'
    import contractYuesaoUpdate from '@/components/page/agent/choose/contractYuesaoUpdate.vue'
    import contractSupply from '@/components/page/agent/choose/contractSupply.vue'
    import orderUpdate from '@/components/page/agent/choose/orderUpdate.vue'
    import contractSiginCommom from '@/components/page/agent/choose/contractSiginCommom.vue'
    import contractOrder from '@/components/page/agent/choose/contractOrder.vue'
    import contractHoliday from '@/components/page/agent/choose/contractHoliday.vue'
    import contractEnclosure from '@/components/page/agent/choose/contractEnclosure.vue'
    export default {
        props:['contractId'],
        name: "contractInfo",
        components: {
            'contractAdd': contractAdd,
            'contractYuesaoAdd': contractYuesaoAdd,
            'contractUpdate': contractUpdate,
            'contractYuesaoUpdate': contractYuesaoUpdate,
            'orderUpdate':orderUpdate,
            'contractSiginCommom':contractSiginCommom,
            'contractOrder':contractOrder,
            'contractHoliday':contractHoliday,
            'contractSupply':contractSupply,
            'contractEnclosure':contractEnclosure,
        },
        data() {
            return {
                url:null,
                id:this.contractId,
                contract:null,
                show:null,
                contractEnclosureModal:false,
                contractSupplyModal:false,
                contractHolidayModal:false,
                dialogVisible:false,
                screenWidth: '80%',//新增对话框 宽度
                eImageUrl:null,
                mImageUrl:null,
                url:null,
                url2:null,
                contractId:this.contractId,
            }
        },
        created(){
            this.getContract(this.id)
        },
        methods:{
            contractEnclosure(id){
                this.show=id;
                this.contractEnclosureModal=true;
            },
            save(dom,state) {

                //console.log(dom)
                if (dom.holidayId==null && dom.contractType==0){
                    return  this.$Message.error('请先选择放假规则');
                }
                dom.status=state;
                this.$postData("update_contract", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.$Message.success('修改成功');
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })

            },
            downloadTest(id){
                this.$postUrl("download_contract",id, {}, {
                    responseType: "arraybuffer"
                }).then(res => {

                    if (res.byteLength==0){
                        return this.$message.error("请先把合同填写完整！");
                    }
                    this.$message.success("请稍等，马上开始下载！");
                    const aLink = document.createElement("a");
                    let blob = new Blob([res]);
                    aLink.href = URL.createObjectURL(blob);
                    aLink.download = "服务合同" + ".docx";
                    document.body.appendChild(aLink);
                    aLink.click();
                    document.body.removeChild(aLink);

                });
            },
            contractHoliday(id){
                this.show=id;
                this.contractHolidayModal=true;
            },
            getSigin(id,contractId) {
                if (id==null){
                    this.$message.error("该合同还未绑定用户，");
                }
                let show={
                    contractId:contractId,
                    status:1,
                    // memberId:id,
                    siginType:0
                }
                this.$postData("get_contractSigin", show, {}).then(res => {
                    if (res.status == 200) {
                        if (res.data.length==0){
                            return  this.$message.error("客户暂未签名" );
                        }else {
                            console.log(res.data[0].siginUrl)
                            // this.dialogVisible=true
                            this.mImageUrl=res.data[0].siginUrl
                        }

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getESigin(id,contractId) {
                if (id==null){
                    return  this.$message.error("该合同还未绑定用户，");
                }
                let show={
                    contractId:contractId,
                    status:1,
                    employeeId:id,
                    siginType:1
                }
                this.$postData("get_contractSigin", show, {}).then(res => {
                    if (res.status == 200) {
                        if (res.data.length==0){
                            return  this.$message.error("阿姨暂未签名" );
                        }else {
                            console.log(res.data[0].siginUrl)
                            // this.dialogVisible=true
                            this.eImageUrl=res.data[0].siginUrl
                        }

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            reMemberSign(){
                this.$alert('是否确认重置客户签名？', '提示', {
                    confirmButtonText: '确定',
                    callback: action => {
                        if (action!= 'cancel'){
                            this.$getUrl("reMemberSign", this.contractId, {}).then(res => {
                                if (res.status == 200) {
                                    this.dialogVisible=false
                                    this.getContract()
                                    this.$message.success("重置客户签名成功，" + res.msg);
                                } else {
                                    this.$message.error("重置客户签名失败，" + res.msg);
                                }
                            })
                        }

                    }
                });



            },
            reEmployeeSign(){
                this.$alert('是否确认重置阿姨签名？', '提示', {
                    confirmButtonText: '确定',
                    callback: action => {
                        if (action!= 'cancel'){
                            this.$getUrl("reEmployeeSign", this.contractId, {}).then(res => {
                                if (res.status == 200) {
                                    this.dialogVisible=false
                                    this.getContract()
                                    this.$message.success("重置客户签名成功，" + res.msg);
                                } else {
                                    this.$message.error("重置客户签名失败，" + res.msg);
                                }
                            })
                        }

                    }
                });


            },
            getUrl(row){
                console.log(row)
                this.eImageUrl=null
                this.mImageUrl=null
                this.getSigin(row.memberId,row.id)
                this.getESigin(row.employeeId,row.id)
                this.contractId=row.id
                this.dialogVisible=true
                this.url="https://agent.xiaoyujia.com/upbaomu/baomuContractInfo/"+row.id
                this.url2="https://agent.xiaoyujia.com/upbaomu/contractInfo/"+row.id
            },
            initChooseProject(data) {
                this.closeCurrModal();

            },
            contractSupplyEdit(id){
                this.show=id;
                this.contractSupplyModal=true;
            },
            closeCurrModal() {
                this.getContract(this.id)
            },
            getContract(id){
                this.$getData("getContractById", {id:id}, {}).then(res => {
                    if (res.status == 200) {
                        if (res.data!==null) {
                            this.contract = res.data
                            this.url=null
                            this.url="https://agent.xiaoyujia.com/upbaomu/contractInfo/"+this.contract.id
                        }
                    }else {
                        this.$notify({
                            title: '提示',
                            message:res.msg,
                            duration: 0,
                            type: 'warning'
                        });
                    }
                })
            },
        }
    }
</script>

<style scoped>
    .el-header, .el-footer {
        position: sticky;
        bottom: 10px;
        color: #333;
        text-align: center;
        line-height: 60px;
    }
    .iframe {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        border: 1px solid #ddd;
        padding: 10px;
        top: 200px;
        right: 200px;
        width: 375px;
        height: 667px;
        background: #fff;
        overflow-y: hidden;
    }
</style>
