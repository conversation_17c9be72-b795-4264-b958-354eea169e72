<template>
  <div style="height:100%">
    <el-scrollbar style="height:100%">
    <div ref="imageDom">
      <el-form ref="form" :model="dom" label-width="100px" style="background:#FFFFFF" label-position="right">
        <div style="padding  : 10px 0  0  30px;font-weight: 700;font-size: 20px;">服务签收单</div>
        <el-divider></el-divider>
        <el-row :gutter="40">
          <el-col :span="7">
            <el-form-item label="订单编号">
              <el-input v-model="dom.orderNo" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="服务时间">
              <el-input v-model="dom.serviceTime" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="支付费用"  v-if="null===dom.paymentCost">
              <el-input value="未支付" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="支付费用"  v-if="null!==dom.paymentCost">
              <el-input :value="dom.paymentCost+'元'" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="服务队长">
              <el-input v-model="dom.serviceStaffCaptain" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="材料总额">
              <el-input :value="dom.panTotal+'元'" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="客户签名">
              <div class="a">
             <span v-for="item in dom.autographPicture">
               <el-image style="width: 200px; " :src="item" :preview-src-list="dom.autographPicture">
               </el-image>
             </span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="服务项目">
              <el-input v-model="dom.serviceProject" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="服务人数">
              <el-input :value="dom.serviceNumberOfPeople+'人'" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="售后(保修期)">
              <el-input :value="dom.guarantee+'天'" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="服务员工">
              <el-input v-model="dom.serviceStaffTeamMember" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="代购材料情况">
              <div v-if="dom.panDetails===null" class="a">无</div>
              <div v-if="dom.panDetails!==null" class="a"> {{dom.panDetails}}</div>
            </el-form-item>
            <el-form-item label="物品丢失情况">
              <div v-if="dom.itemsLostDetails===null" class="a">无</div>
              <div v-if="dom.itemsLostDetails!==null" class="a"> {{dom.itemsLostDetails}}</div>
            </el-form-item>
            <el-form-item label="物品损坏情况">
              <div v-if="dom.itemsLostDetails===null" class="a">无</div>
              <div v-if="dom.itemsLostDetails!==null" class="a"> {{dom.itemsLostDetails}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="服务日期">
              <el-input v-model="dom.serviceData" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="服务车次">
              <el-input v-model="dom.serviceTrainNumber" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="服务内容完成">
              <el-input v-model="dom.serviceContent" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="服务内容情况">
              <div v-if="dom.serviceContentDetailed===null" class="a">无</div>
              <div v-if="dom.serviceContentDetailed!==null" class="a"> {{dom.serviceContentDetailed}}</div>
            </el-form-item>
            <el-form-item label="代购材料图片">
              <div v-if="dom.purchasingAgentPicture[0]===''" class="a">
                无
              </div>
              <div v-if="dom.purchasingAgentPicture[0]!==''" class="a">
                  <span v-for="item in dom.purchasingAgentPicture" >
                    <el-image  style="height: 60px;" :src="item" :preview-src-list="dom.purchasingAgentPicture"></el-image>
                  </span>
              </div>
            </el-form-item>
            <el-form-item label="物品丢失图片">
              <div v-if="dom.losePicture[0]===''" class="a">
                无
              </div>
              <div v-if="dom.losePicture[0]!==''" class="a">
                  <span v-for="item in dom.losePicture" >
                    <el-image  style="height: 60px;" :src="item" :preview-src-list="dom.losePicture"></el-image>
                  </span>
              </div>
            </el-form-item>
            <el-form-item label="物品损坏图片">
              <div v-if="dom.damagedPicture[0]===''" class="a">
                无
              </div>
              <div v-if="dom.damagedPicture[0]!==''" class="a">
                  <span v-for="item in dom.damagedPicture" >
                    <el-image  style="height: 60px;" :src="item" :preview-src-list="dom.damagedPicture"></el-image>
                  </span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="height: 20px"></div>
      <el-form ref="form" :model="dom" label-width="100px" style="background:#FFFFFF" label-position="right">
        <div style="padding  : 10px 0  0  30px;font-weight: 700;font-size: 20px;">客户评价</div>
        <el-divider></el-divider>
        <el-row :gutter="40">
          <el-col :span="7">
            <el-form-item label="客户评价">
              <div v-if="dom.evaluate===null" class="a">无</div>
              <div v-if="dom.evaluate!==null" class="a"> {{dom.evaluate}}</div>
            </el-form-item>
            <el-form-item label="产品名称">
              <el-input v-model="dom.productName" :readonly="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客户评价等级">
              <el-input v-model="dom.evaluateGrade" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="产品价格">
              <el-input v-model="dom.productPrice" :readonly="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客户建议">
              <el-input v-model="dom.customerSuggestionsId" :readonly="true"></el-input>
            </el-form-item>
            <el-form-item label="产品类型">
              <div v-if="dom.productType===null" class="a">无</div>
              <div v-if="dom.productType!==null" class="a"> {{dom.productType}}</div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 服务评价详情 -->
        <div v-if="dom.serviceEvaluation">
          <el-divider></el-divider>
          <div style="padding: 10px 0 0 30px;font-weight: 700;font-size: 18px;">服务评价详情</div>
          <el-row :gutter="5" style="margin-top: 15px;">
            <el-col :span="4" v-if="dom.serviceEvaluation.cleanScore">
              <div class="evaluation-item">
                <div class="evaluation-title">清洁程度</div>
                <div class="evaluation-score">
                  <span :class="{'score-text': true, 'score-5': dom.serviceEvaluation.cleanScore === 5, 'score-4': dom.serviceEvaluation.cleanScore === 4, 'score-3': dom.serviceEvaluation.cleanScore === 3, 'score-2': dom.serviceEvaluation.cleanScore === 2, 'score-1': dom.serviceEvaluation.cleanScore === 1}">
                    {{dom.serviceEvaluation.cleanScore === 5 ? '非常满意' :
                      dom.serviceEvaluation.cleanScore === 4 ? '满意' :
                      dom.serviceEvaluation.cleanScore === 3 ? '一般' :
                      dom.serviceEvaluation.cleanScore === 2 ? '不满意' : '非常不满意'}}
                  </span>
                </div>
              </div>
            </el-col>
            <el-col :span="4" v-if="dom.serviceEvaluation.efficiencyScore">
              <div class="evaluation-item">
                <div class="evaluation-title">工作效率</div>
                <div class="evaluation-score">
                  <span :class="{'score-text': true, 'score-5': dom.serviceEvaluation.efficiencyScore === 5, 'score-4': dom.serviceEvaluation.efficiencyScore === 4, 'score-3': dom.serviceEvaluation.efficiencyScore === 3, 'score-2': dom.serviceEvaluation.efficiencyScore === 2, 'score-1': dom.serviceEvaluation.efficiencyScore === 1}">
                    {{dom.serviceEvaluation.efficiencyScore === 5 ? '非常满意' :
                      dom.serviceEvaluation.efficiencyScore === 4 ? '满意' :
                      dom.serviceEvaluation.efficiencyScore === 3 ? '一般' :
                      dom.serviceEvaluation.efficiencyScore === 2 ? '不满意' : '非常不满意'}}
                  </span>
                </div>
              </div>
            </el-col>
            <el-col :span="4" v-if="dom.serviceEvaluation.attitudeScore">
              <div class="evaluation-item">
                <div class="evaluation-title">服务态度</div>
                <div class="evaluation-score">
                  <span :class="{'score-text': true, 'score-5': dom.serviceEvaluation.attitudeScore === 5, 'score-4': dom.serviceEvaluation.attitudeScore === 4, 'score-3': dom.serviceEvaluation.attitudeScore === 3, 'score-2': dom.serviceEvaluation.attitudeScore === 2, 'score-1': dom.serviceEvaluation.attitudeScore === 1}">
                    {{dom.serviceEvaluation.attitudeScore === 5 ? '非常满意' :
                      dom.serviceEvaluation.attitudeScore === 4 ? '满意' :
                      dom.serviceEvaluation.attitudeScore === 3 ? '一般' :
                      dom.serviceEvaluation.attitudeScore === 2 ? '不满意' : '非常不满意'}}
                  </span>
                </div>
              </div>
            </el-col>

          </el-row>
          <el-row :gutter="5" style="margin-top: 15px;">
            <el-col :span="4" v-if="dom.serviceEvaluation.toolScore">
              <div class="evaluation-item">
                <div class="evaluation-title">工具使用</div>
                <div class="evaluation-score">
                  <span :class="{'score-text': true, 'score-5': dom.serviceEvaluation.toolScore === 5, 'score-4': dom.serviceEvaluation.toolScore === 4, 'score-3': dom.serviceEvaluation.toolScore === 3, 'score-2': dom.serviceEvaluation.toolScore === 2, 'score-1': dom.serviceEvaluation.toolScore === 1}">
                    {{dom.serviceEvaluation.toolScore === 5 ? '非常满意' :
                      dom.serviceEvaluation.toolScore === 4 ? '满意' :
                          dom.serviceEvaluation.toolScore === 3 ? '一般' :
                              dom.serviceEvaluation.toolScore === 2 ? '不满意' : '非常不满意'}}
                  </span>
                </div>
              </div>
            </el-col>
            <el-col :span="4" v-if="dom.serviceEvaluation.safetyScore">
              <div class="evaluation-item">
                <div class="evaluation-title">服务操作安全意识</div>
                <div class="evaluation-score">
                  <span :class="{'score-text': true, 'score-5': dom.serviceEvaluation.safetyScore === 5, 'score-4': dom.serviceEvaluation.safetyScore === 4, 'score-3': dom.serviceEvaluation.safetyScore === 3, 'score-2': dom.serviceEvaluation.safetyScore === 2, 'score-1': dom.serviceEvaluation.safetyScore === 1}">
                    {{dom.serviceEvaluation.safetyScore === 5 ? '非常满意' :
                      dom.serviceEvaluation.safetyScore === 4 ? '满意' :
                          dom.serviceEvaluation.safetyScore === 3 ? '一般' :
                              dom.serviceEvaluation.safetyScore === 2 ? '不满意' : '非常不满意'}}
                  </span>
                </div>
              </div>
            </el-col>
            <el-col :span="4" v-if="dom.serviceEvaluation.willChooseAgain !== undefined">
              <div class="evaluation-item">
                <div class="evaluation-title">是否会再次选择小羽佳</div>
                <div class="evaluation-score">
                  <span :class="{'score-text': true, 'score-5': dom.serviceEvaluation.willChooseAgain === 1, 'score-1': dom.serviceEvaluation.willChooseAgain === 0}">
                    {{dom.serviceEvaluation.willChooseAgain === 1 ? '是' : '否'}}
                  </span>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
      <div>
    <el-row style="text-align:center;">
      <el-button round @click="shareBtn()" class="b">下载</el-button>
    </el-row>
      </div>
    </el-scrollbar>

  </div>
</template>

<script>
import html2canvas from "html2canvas";
// import axios from 'axios';
export default {
  props: ['order'],
  // 注册组件
  components: {
    html2canvas,
  },
  name: "display",
  data() {
    return {
      orderNo: this.$route.query.orderNo,
      signature: true,
      imgUrl: null,
      //客户建议
      customerSuggestionsId: [],
      dom: {
        productName: '',
        productType: null,
        productPrice: null,
        orderNo: null,
        serviceProject: null,
        serviceData: null,
        serviceTime: null,
        serviceTimeStart: null,
        serviceTimeEnd: null,
        serviceNumberOfPeople: null,
        serviceTrainNumber: null,
        paymentCost: null,
        serviceStaffCaptain: null,
        serviceStaffTeamMember: null,
        guarantee: null,
        evaluate: null,
        // （满意，一般，不满意）
        evaluateGrade: null,
        customerSuggestionsId: null,
        serviceContentDetailed: null,
        panTotal: null,
        panDetails: null,
        itemsLostDetails: null,
        itemsDamagedDetails: null,
        purchasingAgentPicture: [],
        damagedPicture: [],
        losePicture: [],
        autographPicture: [],

        materialBought: null,
        itemsLost: null,
        itemsDamaged: null,
        serviceContent: null,
      }
    };
  },
  created() {
    if (this.orderNo !== null && this.orderNo !== '' && this.orderNo !== undefined) {
      this.selectByOrderNo(this.orderNo);
    } else {
      this.selectByOrderNo(this.order);
    }
  },
  watch: {
    order(curVal, oldVal) {
      if (curVal) {
        this.order = curVal;
        if (this.orderNo !== null && this.orderNo !== '' && this.orderNo !== undefined) {
          this.selectByOrderNo(this.orderNo);
        } else {
          this.selectByOrderNo(this.order);
        }
      }
    },
  },
  methods: {
    selectByOrderNo(orderNo) {
      this.$getData("selectServiceAcceptanceFormByOrderNo", {orderNo: orderNo}).then(res => {
        if (res.status === 200) {
          if (res.data === null) {
            return this.signature = !this.signature;
          } else {
            this.dom = res.data;
            if (!this.dom.purchasingAgentPicture){
              this.dom.purchasingAgentPicture = '';
            }
            if (!this.dom.damagedPicture){
              this.dom.damagedPicture = '';
            }
            if (!this.dom.losePicture){
              this.dom.losePicture = ''
            }
            if (!this.dom.autographPicture){
              this.dom.autographPicture = ''
            }
            this.dom.serviceTime = this.dom.serviceTimeStart + '-' + this.dom.serviceTimeEnd
            this.dom.purchasingAgentPicture = this.dom.purchasingAgentPicture.split(',')
            this.dom.damagedPicture = this.dom.damagedPicture.split(',')
            this.dom.losePicture = this.dom.losePicture.split(',')
            this.dom.autographPicture = this.dom.autographPicture.split(',')
            // let url = []
            // for (let i = 0; i < this.dom.autographPicture.length; i++) {
            //   let imgUrl = this.dom.autographPicture[i]
            //   axios.get(imgUrl, { responseType: 'arraybuffer' })
            //       .then(response => {
            //         const data = response.data;
            //         const base64Image = btoa(String.fromCharCode.apply(null, new Uint8Array(data)));
            //         url.push(base64Image)
            //       })
            //       .catch(error => {
            //         console.error('Error fetching image:', error);
            //       });
            // }
            // this.dom.autographPicture = url
            if (this.dom.customerSuggestionsId) {
              this.selectByIds(this.dom.customerSuggestionsId);
            }
            if (this.dom.evaluateGrade === 0) {
              this.dom.evaluateGrade = '不满意'
            } else if (this.dom.evaluateGrade === 1) {
              this.dom.evaluateGrade = '一般'
            } else if (this.dom.evaluateGrade === 2) {
              this.dom.evaluateGrade = '非常满意'
            } else {
              this.dom.evaluateGrade = '客户没有评价'
            }
            if (this.dom.serviceContent === false) {
              this.dom.serviceContent = '未完成'
            } else {
              this.dom.serviceContent = '完成'
            }


          }
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    },
    selectByIds(ids) {
      this.$getData("selectByIds", {ids: ids}).then(res => {
        if (res.status === 200) {
          let aa = [];
          res.data.forEach(function (e) {
            aa.push(e.proposalName);
          })
          this.dom.customerSuggestionsId = aa.toString()
        } else {
          this.$toast.fail(res.msg);
        }
      })
    },
    shareBtn() {
      // html2canvas(this.$refs.imageDom).then(canvas => {
      //   let imgData = canvas.toDataURL('image/png');
      //   console.log(12312312312312)
      //   console.log(imgData)
      //   // Now you can do whatever you want with imgData (save to server, download, etc.)
      // });
      html2canvas(this.$refs.imageDom, {
        useCORS: true,
        // allowTaint: true,
      }).then(canvas => {
        //获取当前HTML页面图片
        this.imgUrl = canvas.toDataURL("image/png")
        //下载当前HTML页面图片
        var image = new Image();
        image.setAttribute("crossOrigin", "anonymous");
        var _this = this;
        image.onload = function () {
          var canvas = document.createElement("canvas");
          canvas.width = image.width;
          canvas.height = image.height;
          var context = canvas.getContext("2d");
          context.drawImage(image, 0, 0, image.width, image.height);
          var url = canvas.toDataURL("image/png"); //得到图片的base64编码数据
          var a = document.createElement("a"); // 生成一个a元素
          var event = new MouseEvent("click"); // 创建一个单击事件
          a.download = _this.projectName || "photo"; // 设置图片名称
          a.href = url; // 将生成的URL设置为a.href属性
          a.dispatchEvent(event); // 触发a的单击事件
        };
        image.src = this.imgUrl;
      })
    }
  },
}
</script>

<style scoped>
.a{
  border:1px solid #CCCCCC;
  border-radius: 5px;
}
.b {
  width: 120px;
  height: 50px;
  font-size: 20px;
  margin-top: 40px;
  margin-bottom: 40px;
}
.el-scrollbar__wrap {
  overflow-x: hidden;
}
.evaluation-item {
  background: #f9f9f9;
  border-radius: 5px;
  padding: 10px;
  text-align: center;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.evaluation-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}
.evaluation-score {
  font-size: 16px;
  font-weight: bold;
}
.score-text {
  padding: 5px 10px;
  border-radius: 4px;
}
.score-5 {
  background-color: #67C23A;
  color: white;
}
.score-4 {
  background-color: #85ce61;
  color: white;
}
.score-3 {
  background-color: #E6A23C;
  color: white;
}
.score-2 {
  background-color: #F56C6C;
  color: white;
}
.score-1 {
  background-color: #ff4949;
  color: white;
}
</style>
