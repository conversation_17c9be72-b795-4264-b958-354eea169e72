<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col span="24">
                    <div>
                        <div id="operBtnDiv">
                            <div id="searchDiv">
                                <el-form ref="form" :model="pageInfo">
                                    <el-row>
                                        <el-col :span="13">
                                            <el-form-item label="名称"  v-if="isAdd!=0">
                                                <Select v-if="isAdd==1"  v-model="formItem.costName" filterable style="width: 150px"
                                                        @on-change="change" :label-in-value="true">
                                                    <Option v-for="item in list" :value="item.name">{{ item.name}}</Option>
                                                </Select>
                                                <el-input v-else-if="isAdd==2"
                                                        v-model="formItem.costName"
                                                        placeholder="名称"
                                                        style="width:150px"
                                                        class="handle-input mr10"
                                                ></el-input>
                                                <span style="margin-left: 10px">金额</span>
                                                <el-input
                                                        v-model="formItem.money"
                                                        placeholder="金额"
                                                        style="width:150px;margin-left: 10px"
                                                        class="handle-input mr10"
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col style="text-align:right" :span="addSpan">
                                            <el-button v-if="isAdd!=0" @click="save">确定</el-button>
                                            <el-button v-if="isAdd!=0" @click="cancel">取消</el-button>
                                            <!--<el-button type="success" v-if="isAdd==0" @click="addBudget">新增明细</el-button>-->
                                            <el-button type="success" :loading="ding" @click="exportList">导出Excel</el-button>
                                        </el-col>


                                    </el-row>
                                </el-form>
                                <div style="text-align:right">{{summary}}</div>
                                <el-table :data="data" border class="table" ref="multipleTable">
                                    <el-table-column
                                            prop="order.billNo"
                                            label="订单号"
                                            width="140">
                                    </el-table-column>
                                    <el-table-column
                                            prop="createTime"
                                            label="下单时间"
                                            width="140">
                                    </el-table-column>
                                    <el-table-column
                                            prop="typeDesc"
                                            label="类型"
                                            width="100">
                                    </el-table-column>
                                    <el-table-column
                                            prop="costName"
                                            label="费用名称"
                                            width="148"
                                    ></el-table-column>
                                    <el-table-column
                                            prop="money"
                                            label="费用金额"
                                            width="100"
                                    ></el-table-column>
                                    <el-table-column
                                            label="操作"
                                            width="130">
                                        <template slot-scope="scope">
                                          <!--  <el-button size="mini" @click="update(scope.row.id)" type="primary">编辑
                                            </el-button>-->
                                            <el-button size="mini" @click="deleteDetail(scope.row.id)" type="danger">删除
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <div class="pagination">
                                    <Page :total="pageInfo.total"
                                          @on-change="onChange"
                                          :show-total="true"
                                          :show-sizer="true"
                                          :page-size-opts="pageSizeOpts"
                                          @on-page-size-change="onPageSizeChange"
                                          :page-size="pageInfo.size"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
    </div>
</template>


<script>

    export default {
        props: ['incomeDetail'],
        data() {
            return {
                summary:null,
                ding:false,
                isAdd: 0,
                addSpan: 24,
                data: null,
                pageSizeOpts: [5, 10, 15],
                pageInfo: {total: 0, size: 5, current: 1, pages: 1},
                formItem: {
                    costName:null,
                    money:null,
                    projectIncomeId:this.incomeDetail.incomeId,
                    pageSize:5,
                    pageNum:1,
                    type:2,
                },
                list: null,

                ruleValidate: {
                    type: [
                        {required: true, message: '请选择类型', trigger: 'blur', type: "number"}
                    ],
                    name: [
                        {required: true, message: '请输入名称', trigger: 'change'},
                    ],
                }
            }
        },
        components: {},
        created: function () {
            this.getData();
        },
        methods: {
            getDictionaryList() {
                let list = {
                    type: 2
                }
                this.$postData("dictionary_getList", list, {}).then(res => {
                    if (res.status == 200) {
                        this.list = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },

            exportList(){
                this.ding=true;
                let list={
                    projectIncomeId:this.incomeDetail.incomeId
                }
                this.$postData("income_detail_export", list, {
                    responseType: "arraybuffer"
                }).then(res => {
                        this.ding=false;
                        this.blobExport({
                            tablename: this.incomeDetail.costName+"明细列表",
                            res: res
                        });
                });
            },
            blobExport({ tablename, res }) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], { type: "application/vnd.ms-excel" });
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },


            getData() {
                this.$postData("income_detail_getList", this.formItem, {}).then(res => {
                    if (res.status == 200) {
                        this.data = res.data.list;
                        this.summary=res.data.summary
                      //  this.getAmount();//计算合计
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                        console.log(this.isAdd)
                    } else {
                        this.$message.error("获取失败，" + res.msg);
                    }
                })
            },
            deleteDetail(id){
                this.$confirm('此操作将永久性删除, 是否继续?', '提示', {
                    confirmButtonText: '删除',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$getData("income_detail_delete", {id:id}).then(res => {
                        if (res.status == 200) {
                            this.$message.success("删除成功");
                            this.getData();
                        } else {
                            this.$message.error("删除失败" + res.msg);
                        }
                    })
                }).catch(() => {

                });
            },
            save() {
                if (this.formItem.costName != null && this.formItem.money != null) {
                    this.$postData("income_detail_save", this.formItem, {}).then(res => {
                        if (res.status == 200) {
                            this.$Message.success('添加成功');
                            this.cancel();
                            this.getData()
                            this.formItem.costName = null;
                            this.formItem.money = null
                        } else {
                            this.$message.error("添加失败，" + res.msg);
                        }
                    })
                } else {
                    this.$Message.success('请补全新增信息');
                }


            },
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.formItem.pageSize = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                console.log(index)
                this.formItem.pageNum = index;
                this.getData();
            },
            addBudget() {
                if(this.incomeDetail.type==2){
                    this.getDictionaryList();
                    this.isAdd = 1
                }else {
                    this.isAdd = 2
                }

                this.addSpan = 11
            },
            cancel() {
                this.isAdd = 0;
                this.addSpan = 24;
                this.formItem.costName = null;
                this.formItem.money = null
            },
            change(data) {
                console.log(data)
                this.formItem.name = data.label;
            }
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        /* padding-left: 20px;*/
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

