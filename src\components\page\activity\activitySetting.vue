<template>
  <div class="container">
    <div class="handle-box">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="活动名称">
          <el-input v-model="dom.activity_name" placeholder="模糊搜索活动名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="活动类型">
          <el-select v-model="dom.activity_type" placeholder="请选择">
            <el-option
                v-for="item in activityOption"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="onQuery" v-loading="loading">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-refresh" @click="reset()" v-loading="loading">
            重置
          </el-button>
          <el-button icon="el-icon-refresh" @click="createActivity()" v-loading="loading">
            新建活动
          </el-button>
        </el-form-item>
      </el-form>


      <el-table :data="dataList" height="500px" v-loading="loading"
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
        <el-table-column type="expand">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" @click="edit(scope.row)">
              编辑
            </el-button>
            <el-button @click="updateState(scope.row, 1)" type="success" size="mini"
                       v-if="scope.row.activity_state === 0 || scope.row.activity_state === 2">
              上线
            </el-button>
            <el-button type="danger" size="mini" @click="updateState(scope.row, 0)"
                       v-if="scope.row.activity_state === 1">
              下线
            </el-button>
            <el-button type="danger" size="mini" @click="updateState(scope.row, 2)"
                       v-if="scope.row.activity_state === 1">
              暂停
            </el-button>

          </template>
        </el-table-column>
        <el-table-column
            label="序号"
            type="index"
            align="center">
        </el-table-column>
        <el-table-column
            label="活动名称"
            align="center"
            prop="activity_name">

        </el-table-column>
        <el-table-column
            label="活动类型"
            align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.activity_type === 1" >门店活动</span>
            <span v-if="scope.row.activity_type === 4" >平台活动</span>
          </template>
        </el-table-column>
        <el-table-column
            label="活动状态"
            align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.activity_state == null">未知</span>
            <span v-else-if="scope.row.activity_state === 0" style="color: #f00f14;">下线</span>
            <span v-else-if="scope.row.activity_state === 1" style="color: #089117;">上线</span>
            <span v-else-if="scope.row.activity_state === 2" style="color: #f00f14;">暂停</span>
            <span v-else>未知</span>
          </template>
        </el-table-column>
        <el-table-column
            prop="activity_hot"
            label="活动热度"
            align="center">
        </el-table-column>
        <el-table-column
            prop="apply_start_time"
            label="活动开始时间"
            align="center">
        </el-table-column>
        <el-table-column
            prop="apply_end_time"
            label="活动结束时间"
            align="center">
        </el-table-column>
        <el-table-column
            prop="activity_label"
            label="活动标签"
            align="right">
        </el-table-column>
        <el-table-column
            prop="activity_topic"
            label="活动话题关键词"
            align="right">
        </el-table-column>
          <el-table-column
              prop="activity_skil_code"
              label="活动技能代码"
              align="left">
          </el-table-column>
          <el-table-column
                           prop="activity_coupon"
                           label="代金券批次"
                           align="left">
          </el-table-column>


      </el-table>

      <el-dialog title="编辑/新增" :visible.sync="dialogEditVisible" width="60%">
        <el-form :model="editData" label-width="100px"  :rules="rules"  ref="editData" >
          <el-form-item label="活动名称"  prop="activity_name">
            <el-input v-model="editData.activity_name" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="活动类型"  prop="activity_name">
            <el-select v-model="editData.activity_type" placeholder="请选择">
              <el-option
                  v-for="item in activityOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>

          </el-form-item>
          <el-form-item label="活动热度" prop="activity_hot" required>
            <el-input-number v-model="editData.activity_hot" :min="1"  ></el-input-number>
          </el-form-item>
          <el-form-item label="活动开始时间">
            <el-date-picker
                v-model="editData.apply_start_time"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="活动结束时间" >
            <el-date-picker
                v-model="editData.apply_end_time"
                value-format="yyyy-MM-dd"
                type="date"
                placeholder="选择日期">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="活动描述" >
            <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="editData.activity_desc">
            </el-input>
          </el-form-item>
          <el-form-item label="活动标签" >
            <el-input v-model="editData.activity_label" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="话题关键词" >
            <el-input v-model="editData.activity_topic" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="活动奖励说明" >
            <el-input type="textarea" v-model="editData.activity_reward_desc" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="活动技能代码" prop="activity_skil_code" required v-if="editData.activity_type === 1">
            <el-input  v-model="editData.activity_skil_code" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="关联优惠券批次(多个用,隔开)" label-width="auto" required v-if="editData.activity_type === 4">
            <el-input  v-model="editData.activity_coupon" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="封面图" required>
            <el-upload
                class="avatar-uploader"
                action="https://api.xiaoyujia.com/system/imageUpload"
                :show-file-list="true"
                :on-success="getImagesSuccess">
              <img v-if="editData.activity_banner" :src="editData.activity_banner" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>

          </el-form-item>

          <el-form-item label="海报分享背景图"  required>
            <el-link href="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1679621242132snhb.jpg" target="_blank" type="warning">图片必须是750 x 1632的长海报形式。点我查看图片示例</el-link>
            <el-upload
                class="avatar-uploader"
                action="https://api.xiaoyujia.com/system/imageUpload"
                :show-file-list="true"
                :on-success="getImagesSuccess1">
              <img v-if="editData.activity_demo_url" :src="editData.activity_demo_url" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>

          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogEditVisible = false">取 消</el-button>
          <el-button type="primary" @click="editInfo('editData')">确 定</el-button>
        </div>
      </el-dialog>



      <div class="pagination">
        <Page :total="pageInfo.total"
              @on-change="onChangePage"
              :current="dom.current"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onPageSizeChange"
              :page-size="dom.size"/>
      </div>



    </div>

  </div>
</template>

<script>


export default {
  name: "activitySetting",

  data() {
    return {
      activityOption: [
        {
          value: 1,
          label: '门店活动'
        }, {
          value: 4,
          label: '平台活动'
        },
      ],
      dom:{
        activity_name:null,
        current: 1,
        size:10
      },
      dataList:[],
      dialogEditVisible:false,
      loading:false,
      pageSizeOpts: [10, 15, 20, 30, 50, 100],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      editData:{},
      rules: {
        activity_name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ],
        activity_hot: [
          { required: true, message: '活动热度必填', trigger: 'blur' }
        ],
        // activity_skil_code: [
        //   { required: true, message: '活动代码必填', trigger: 'blur' }
        // ],

        // region: [
        //   { required: true, message: '请选择活动区域', trigger: 'change' }
        // ],

        // type: [
        //   { type: 'array', required: true, message: '请至少选择一个活动性质', trigger: 'change' }
        // ],
        // resource: [
        //   { required: true, message: '请选择活动资源', trigger: 'change' }
        // ],
        // desc: [
        //   { required: true, message: '请填写活动形式', trigger: 'blur' }
        // ]
      }
    }
  },
   created() {
     this.onQuery();
  },
  methods: {
    createActivity(){
      this.editData = {};
      this.dialogEditVisible = true;
    },
    editInfo(formName){
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (!this.editData.activity_banner){
           return  this.$message.error("封面图必传")
          }
          if (!this.editData.activity_demo_url){
            return  this.$message.error("海报图必传")
          }
          if (!this.editData.apply_end_time || !this.editData.apply_start_time){
            return  this.$message.error("开始时间/结束时间必填")
          }
          //更新
          if (this.editData.id){
            this.$postData("updateActivityUnion", this.editData, {}).then((res) => {
              if (res.status === 200) {
                this.loading = true;
                this.$message.success("更新状态成功");
                this.dialogEditVisible = false;
                this.onQuery();
              } else {
                this.$message.error(res.msg);
                this.loading = false;
              }
            });
          } else {
            //插入
            this.editData.activity_state = 1;
            this.editData.condition_type = 0;
            this.editData.condition_value = 0;
            this.editData.activity_level = 0;
            this.editData.activity_dyid = 0;
            this.editData.createor = localStorage.getItem('id')
            this.$postData("insertActivityUnion", this.editData, {}).then((res) => {
              if (res.status === 200) {
                this.loading = true;
                this.$message.success("更新状态成功");
                this.dialogEditVisible = false;
                this.onQuery();
              } else {
                this.$message.error(res.msg);
                this.loading = false;
              }
            });
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    getImagesSuccess1(res,file){
      this.editData.activity_demo_url = res.data;
    },
    getImagesSuccess(res,file){
      this.editData.activity_banner = res.data;
    },
    edit(row){
      this.editData = row;
      this.dialogEditVisible = true;
    },
    updateState(row,state){
      row.activity_state = state;
      this.$postData("updateActivityUnion", row, {}).then((res) => {
        if (res.status === 200) {
          this.loading = true;
          this.$message.success("更新状态成功");
          this.onQuery();
        } else {
          this.$message.error(res.msg);
          this.loading = false;
        }
      });
    },
    reset() {
      this.pageInfo.current = 1;
      this.dom.current = 1;
      this.dom.activity_name = null;
      this.onQuery();
    },
    onQuery() {
    this.loading = true;
      this.$postData("ActivityUnionList", JSON.stringify(this.dom), {}).then((res) => {
        if (res.status === 200) {
          this.dataList = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total;
          this.loading = false;
        } else {
          this.$message.error(res.msg);
          this.loading = false;
        }
      });
    },
    onChangePage(index) {
      this.dom.current = index;
      this.onQuery();
    },
    onPageSizeChange(size) {
      this.loading = true;
      this.dom.size = size;
      this.onQuery();
    },
  }
}
</script>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
