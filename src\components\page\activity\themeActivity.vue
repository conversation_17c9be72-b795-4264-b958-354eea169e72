<template>
	<div class="container">
		<div class="handle-box">
			<el-form ref="form" :model="pageInfo">
				<el-row>
					<el-col :span="3">
						<el-form-item label="搜索关键词" style="margin-right: 20px">
							<el-input v-model="quer.search" placeholder="输入店长名、服务项目等进行搜索"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="3">
						<el-form-item label="签到门店" style="margin-right: 20px">
							<el-select v-model="quer.signInStoreId" filterable>
								<el-option label="" value=""></el-option>
								<el-option v-for="(item,index) in storeList" :key="index" :label="
									item.id+'：'+item.storeName" :value="item.id"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="3">
						<el-form-item label="员工姓名" style="margin-right: 20px">
							<el-input v-model="quer.employeeName" placeholder="请输入员工姓名"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="3">
						<el-form-item label="所属门店" style="margin-right: 20px">
							<el-select v-model="quer.storeId" filterable>
								<el-option label="" value=""></el-option>
								<el-option v-for="(item,index) in storeList" :key="index" :label="
									item.id+'：'+item.storeName" :value="item.id"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="筛选时间" style="margin-right: 20px">
							<div style="width: 180px;display: block;">
								<el-date-picker v-model="searchTime" type="datetimerange"
									:picker-options="pickerOptions1" range-separator="至" start-placeholder="开始日期"
									end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd">
								</el-date-picker>
							</div>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-button type="primary" @click="query()" icon="el-icon-search">搜索
						</el-button>
						<el-button icon="el-icon-refresh" @click="reset()">重置
						</el-button>
						<el-button type="danger" @click="recordExport()">导出</el-button>
					</el-col>
				</el-row>

			</el-form>
		</div>

		<el-table :data="list" v-loading="loading" border stripe
			:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
			:expand-row-keys="expands">
			<el-table-column prop="signTime" label="签到日期">
			</el-table-column>
			<el-table-column prop="signInStoreName" label="签到门店">
				<template slot-scope="scope">
					<span>{{scope.row.signInStoreName||'-'}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="employeeName" label="员工姓名">
			</el-table-column>
			<el-table-column prop="storeName" label="所属门店">
			</el-table-column>
			<el-table-column prop="storeManagerName" label="门店店长">
			</el-table-column>
			<el-table-column prop="productName" label="服务项目">
				<template slot-scope="scope">
					<span>{{scope.row.productName||'-'}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="workDays" label="在户时长" sortable>
				<template slot-scope="scope">
					<span>{{scope.row.workDays||'-'}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="introduceNum" label="转介绍员工数" sortable>
			</el-table-column>
		</el-table>
	</div>
	</div>
</template>

<script>
	export default {
		name: "themeActivity",
		data() {
			return {
				list: [],
				storeList: [],
				expands: [],
				loading: false,
				pageSizeOpts: [20, 50, 100, 200],
				getRowKeys(row) {
					return row.id
				},
				quer: {
					storeId: null,
					signInStoreId: null,
					employeeName: '',
					startTime: null,
					endTime: null,
					orderBy: 't.SignTime DESC'
				},
				pageInfo: {
					total: 10,
					size: 10,
					current: 1,
					pages: 1
				},
				searchTime: '',
				pickerOptions1: {
					shortcuts: [{
						text: '今天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '昨天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}],
				},
			}
		},
		created() {
			const start = new Date();
			const end = new Date();
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 3)
			this.searchTime = []
			this.searchTime.push(this.formatDate(start) + ' 00:00:00.000')
			this.searchTime.push(this.formatDate(end) + ' 00:00:00.000')
			this.getData()
			this.listStore()
		},
		methods: {
			getData() {
				// 处理一下时间筛选字段
				if (this.searchTime && this.searchTime.length >= 2) {
					this.quer.startTime = this.searchTime[0]
					this.quer.endTime = this.searchTime[1]
				} else {
					this.quer.startTime = null
					this.quer.endTime = null
				}
				this.$postData("listEmployeeSigninVo", this.quer, {}).then(res => {
					if (res.status == 200) {
						this.list = res.data
						this.$message.success(res.msg)
					} else {
						this.$message.error("没有查询到相关数据！")
					}
					this.loading = false
				})
			},
			// 时间格式化
			formatDate(value) {
				if (!value) {
					return ""
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '-' + MM + '-' + d
			},
			// 搜索
			query() {
				this.loading = true
				this.getData()
			},
			// 重置
			reset() {
				this.loading = true
				this.quer.search = null
				this.quer.storeId = null
				this.quer.signInStoreId = null
				this.quer.employeeName = null
				this.quer.startTime = null
				this.quer.endTime = null

				this.getData()
			},
			listStore() {
				this.$postData("store_getByList", {}, {}).then(res => {
					if (res.status == 200) {
						this.storeList = res.data;
					}
				});
			},
			// 联盟试题模板下载
			recordExport() {
				let time = (this.formatDate(this.quer.startTime) || '') + '-' + (this.formatDate(this.quer.endTime) || '')
				this.$postData("listEmployeeSigninVoExport", this.quer, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('签到记录下载成功!')
					this.blobExport({
						tablename: "签到记录" + time,
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
		}
	}
</script>

<style>

</style>