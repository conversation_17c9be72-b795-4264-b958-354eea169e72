<template>
	<div class="container">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">成就管理</el-menu-item>
			<el-menu-item index="1">类型管理</el-menu-item>
			<el-menu-item index="2">领取记录</el-menu-item>
		</el-menu>

		<!-- 搜索栏目 -->
		<div v-if="choiceIndexNow==0">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="成就标题、内容"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="成就类型" style="margin-right: 20px">
								<el-select v-model="searchType">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="成就状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="是否推荐" style="margin-right: 20px">
								<el-select v-model="searchPush">
									<el-option label="" value=""></el-option>
									<el-option label="关闭" value="0"></el-option>
									<el-option label="开启" value="1"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(0,0)">添加
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column prop="id" width="60" label="编号">
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="100" prop="achvTitle" label="成就标题">
					<template slot-scope="scope">
						<span>{{scope.row.achvTitle}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="achvContent" label="成就内容">
					<template slot-scope="scope">
						<span>{{scope.row.achvContent||'暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="achvType" label="成就类型" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-if="item.id == scope.row.achvType" v-for="(item,index) in typeList" :key="index"
								:type="item.type">{{formatType(scope.row)}}</el-tag>
						</div>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="achvIcon" label="成就图标" :render-header="renderHeader">
					<template slot-scope="scope">
						<img :src="scope.row.achvIcon||blankImg" style="width: 100px;height: 100px;"
							@click="openImg(scope.row.achvIcon)">
					</template>
				</el-table-column>

				<el-table-column width="140" prop="achvImg" label="成就图片" :render-header="renderHeader">
					<template slot-scope="scope">
						<img :src="scope.row.achvImg||blankImg" style="width: 100px;height: 100px;"
							@click="openImg(scope.row.achvImg)">
					</template>
				</el-table-column>

				<el-table-column width="102" label="状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.achvState == 0" type="info">下架</el-tag>
						<el-tag v-if="scope.row.achvState == 1" type="success">上架</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="102" prop="achvLevel" label="等级" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.achvLevel}}</span>级
					</template>
				</el-table-column>

				<el-table-column width="102" prop="integral" label="积分" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.integral||'暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="160" label="查看权限" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="scope.row.authList!='0'">
							<ul v-for="(item,index) in authList" :key="index">
								<li class="tags-li" v-for="(item1,index1) in scope.row.authListArray" :key="index1"
									v-if="item.id==parseInt(item1)">
									<span>
										{{ item.name }}
									</span>
									<span class="tags-li-icon" @click="closeAuthTags(scope.$index,index1)"><i
											class="el-icon-close" v-if="scope.row.isEdit"></i></span>
								</li>
							</ul>
						</div>

						<div v-if="scope.row.authList=='0'">
							<span>暂未设置</span>
						</div>
					</template>
				</el-table-column>

				<el-table-column prop="push" width="102" label="推荐">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.push == 0" type="info">关闭</el-tag>
						<el-tag v-if="scope.row.push == 1" type="success">开启</el-tag>
					</template>
				</el-table-column>

				<el-table-column prop="limitedTime" label="有效期" width="100" sortable>
					<template slot-scope="scope">
						<span>{{formatValidity(scope.row.limitedTime)}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creator" label="创建人">
					<template slot-scope="scope">
						<span v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
						<span v-else>{{scope.row.creator||''}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
					<template slot-scope="scope">
						<el-button @click="openModal(1,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
						<el-popconfirm title="确定删除该成就吗？删除后无法恢复，请谨慎操作!" @confirm="deleteAchievement(scope.row)">
							<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
								icon="el-icon-delete">删除
							</el-button>
						</el-popconfirm>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<div v-if="choiceIndexNow==1">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="成就类型标题、内容"></el-input>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="类型状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>
							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>
							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(3,0)">添加
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column prop="id" width="60" label="编号">
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="100" prop="typeName" label="类型标题">
					<template slot-scope="scope">
						<span>{{scope.row.typeName}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="typeContent" label="类型内容">
					<template slot-scope="scope">
						<span>{{scope.row.typeContent||'暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="102" label="状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.typeState == 0" type="info">下架</el-tag>
						<el-tag v-if="scope.row.typeState == 1" type="success">上架</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="160" label="查看权限" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="scope.row.authList!='0'">
							<ul v-for="(item,index) in authList" :key="index">
								<li class="tags-li" v-for="(item1,index1) in scope.row.authListArray" :key="index1"
									v-if="item.id==parseInt(item1)">
									<span>
										{{ item.name }}
									</span>
									<span class="tags-li-icon" @click="closeAuthTags(scope.$index,index1)"><i
											class="el-icon-close" v-if="scope.row.isEdit"></i></span>
								</li>
							</ul>
						</div>
						<div v-if="scope.row.authList=='0'">
							<span>暂未设置</span>
						</div>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creator" label="创建人">
					<template slot-scope="scope">
						<span v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
						<span v-else>{{scope.row.creator||''}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
					<template slot-scope="scope">
						<el-button @click="openModal(4,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
						<el-popconfirm title="确定删除该成就类型吗？删除后无法恢复，请谨慎操作!（删除后将对应类型的成就转移至默认分组）"
							@confirm="deleteAchievementType(scope.row)">
							<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
								icon="el-icon-delete">删除
							</el-button>
						</el-popconfirm>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<div v-if="choiceIndexNow==2">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="成就类型标题、内容、达成者姓名等"></el-input>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="记录状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option label="失效" value="0"></el-option>
									<el-option label="生效" value="1"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>
							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>
						</el-col>

					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column prop="id" width="60" label="编号">
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="100" prop="achvTitle" label="成就标题">
					<template slot-scope="scope">
						<span>{{scope.row.achvTitle}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="achvContent" label="成就内容">
					<template slot-scope="scope">
						<span>{{scope.row.achvContent||'暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="achvType" label="成就类型">
					<template slot-scope="scope">
						<div>
							<el-tag v-if="item.id == scope.row.achvType" v-for="(item,index) in typeList" :key="index"
								:type="item.type">{{formatType(scope.row)}}</el-tag>
						</div>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="achvIcon" label="成就图标">
					<template slot-scope="scope">
						<img :src="scope.row.achvIcon||blankImg" style="width: 100px;height: 100px;"
							@click="openImg(scope.row.achvIcon)">
					</template>
				</el-table-column>

				<el-table-column width="102" prop="recordState" label="状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.recordState == 0" type="info">失效</el-tag>
						<el-tag v-if="scope.row.recordState == 1" type="success">生效</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="102" prop="achvLevel" label="等级" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.achvLevel}}</span>级
					</template>
				</el-table-column>

				<el-table-column width="130" prop="employeeName" label="领取者">
					<template slot-scope="scope">
						<span v-if="scope.row.employeeName">{{scope.row.employeeName}}({{scope.row.employeeId}})</span>
						<span v-else>{{scope.row.memberName}}({{scope.row.memberId}})</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="receiveNum" label="达成指标" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.receiveNum||'0'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="integral" label="获得积分" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.receiveIntegral||'暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="achvIcon" label="头像">
					<template slot-scope="scope">
						<img :src="scope.row.headImg||blankImg" style="width: 100px;height: 100px;"
							@click="openImg(scope.row.headImg)">
					</template>
				</el-table-column>

				<el-table-column width="140" prop="receiveTime" label="领取时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.receiveTime}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="expireTime" label="到期时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.expireTime.includes('2099')?'永久有效':scope.row.expireTime}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作">
					<template slot-scope="scope">
						<span>暂无可操作项</span>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--添加成就-->
		<el-drawer size="60%" :with-header="false" :visible.sync="achvModal" direction="rtl">
			<div>
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">成就信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 成就标题：">
								<el-input v-model="choiceItem.achvTitle" type="textarea" class="handle-input mr10"
									placeholder="请输入成就标题">
								</el-input>
							</el-form-item>

							<el-form-item label="* 成就内容：">
								<el-input v-model="choiceItem.achvContent" type="textarea" class="handle-input mr10"
									placeholder="请输入成就内容描述">
								</el-input>
							</el-form-item>

							<el-form-item label="跳转位置：">
								<el-input v-model="choiceItem.linkAppId" type="textarea" class="handle-input mr10"
									placeholder="请输入跳转小程序appId">
								</el-input>
								<el-tooltip class="item" effect="dark" content="小程序appId，若不配置则默认在当前小程序内跳转"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="跳转链接：">
								<el-input v-model="choiceItem.linkUrl" type="textarea" class="handle-input mr10"
									placeholder="请输入成就跳转链接">
								</el-input>
								<el-tooltip class="item" effect="dark" content="和“跳转位置”联动，支持小程序内跳转也可跳出到其他小程序"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="* 成就图标：">
								<el-upload class="upload-demo" :action="uploadUrl" list-type="picture"
									:on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="choiceItem.achvIcon?choiceItem.achvIcon:blankImg"
										style="width: 200px;height: 200px;" @click="openImgUpload(0,0)">
								</el-upload>
							</el-form-item>

							<el-form-item label="成就图片：">
								<el-upload class="upload-demo" :action="uploadUrl" list-type="picture"
									:on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="choiceItem.achvImg?choiceItem.achvImg:blankImg"
										style="width: 200px;height: 200px;" @click="openImgUpload(1,0)">
								</el-upload>
							</el-form-item>

						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 成就类型：">
								<el-select v-model="choiceItem.achvType" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="可根据类型进行分类" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="* 成就等级：">
								<el-input-number v-model="choiceItem.achvLevel" :min="1" :max="8"
									label="描述文字"></el-input-number> 级
								<el-tooltip class="item" effect="dark" content="数字越大等级越高，同名成就将进行多等级聚合"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="成就状态：">
								<el-select v-model="choiceItem.achvState" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="可根据类型进行分类" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="查看权限：">
								<div>
									<ul v-for="(item,index) in authList" :key="index">
										<li class="tags-li" v-for="(item1,index1) in choiceItem.authListArray"
											:key="index1" v-if="item.id==parseInt(item1)">
											<span>
												{{ item.name }}
											</span>
											<span class="tags-li-icon" @click="closeAuthTags1(index1)"><i
													class="el-icon-close"></i></span>
										</li>
									</ul>
									<el-button type="primary" @click="openModal(2,0)" plain
										icon="el-icon-plus">添加角色</el-button>
								</div>

								<div v-if="!choiceItem.authList||choiceItem.authList=='0'">
									<span>暂未设置</span>
								</div>

							</el-form-item>

							<el-form-item label="奖励积分：">
								<el-input v-model="choiceItem.integral" class="handle-input mr10"
									placeholder="奖励积分（默认为0）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 个
								<el-tooltip class="item" effect="dark" content="达成后将发放至用户账户" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="校验字段：">
								<el-input v-model="choiceItem.checkField" type="text" class="handle-input mr10"
									placeholder="请输入校验字段">
								</el-input>
								<el-tooltip class="item" effect="dark" content="校验字段，由技术人员进行填写" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="校验SQL：">
								<el-input v-model="choiceItem.checkSql" type="textarea" class="handle-input mr10"
									placeholder="请输入校验SQL" :autosize="{ minRows: 6, maxRows: 15}">
								</el-input>
								<el-tooltip class="item" effect="dark" content="校验语句，由技术人员进行填写" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="成立条件：">
								<el-select v-model="choiceItem.establish" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in establishList" :key="index" :label="item.name"
										:value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="与达标数量进行联动，判定是否达成" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="达标数量：">
								<el-input v-model="choiceItem.reachFlag" class="handle-input mr10"
									placeholder="达标数量（默认为999）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 个
							</el-form-item>

							<el-form-item label="有效期：">
								<el-input v-model="choiceItem.limitedTime" class="handle-input mr10"
									placeholder="单位：天（默认为999天-永久）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 天
								<el-tooltip class="item" effect="dark" content="单位：天（默认为999天-永久）" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="首页推荐：">
								<el-select v-model="choiceItem.push" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in switchStateList" :key="index"
										:label="item.typeName" :value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="开启则在成就首页推荐列表展示" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="achvModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="insertAchievement()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateAchievement(choiceItem)" type="success" size="small"
						v-if="detailType==1">保存
					</el-button>
				</div>

			</div>
		</el-drawer>

		<!--添加成就类型-->
		<el-drawer size="60%" :with-header="false" :visible.sync="achvTypeModal" direction="rtl">
			<div>
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">成就类型信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 类型标题：">
								<el-input v-model="choiceItem.typeName" type="textarea" class="handle-input mr10"
									placeholder="请输入成就类型标题">
								</el-input>
							</el-form-item>

							<el-form-item label="类型内容：">
								<el-input v-model="choiceItem.typeContent" type="textarea" class="handle-input mr10"
									placeholder="请输入成就类型内容描述">
								</el-input>
							</el-form-item>
						</el-form>

					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="类型状态：">
								<el-select v-model="choiceItem.typeState" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="请选择成就类型" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="排序序号：">
								<el-input v-model="choiceItem.sort" class="handle-input mr10" placeholder="排序序号（默认为0）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input>
								<el-tooltip class="item" effect="dark" content="数字越大排序靠后" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="查看权限：">
								<div>
									<ul v-for="(item,index) in authList" :key="index">
										<li class="tags-li" v-for="(item1,index1) in choiceItem.authListArray"
											:key="index1" v-if="item.id==parseInt(item1)">
											<span>
												{{ item.name }}
											</span>
											<span class="tags-li-icon" @click="closeAuthTags1(index1)"><i
													class="el-icon-close"></i></span>
										</li>
									</ul>
									<el-button type="primary" @click="openModal(5,0)" plain
										icon="el-icon-plus">添加角色</el-button>
								</div>

								<div v-if="!choiceItem.authList||choiceItem.authList=='0'">
									<span>暂未设置</span>
								</div>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="achvTypeModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="insertAchievementType()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateAchievementType(choiceItem)" type="success" size="small"
						v-if="detailType==1">保存
					</el-button>
				</div>

			</div>
		</el-drawer>

		<!-- 添加查看权限 -->
		<el-dialog :visible.sync="authIdListModal" width="60%" title="添加查看权限" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;" v-if="choiceItem">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-checkbox-group v-model="choiceItem.authListArray" value-key="id">
						<el-checkbox-button v-for="(val,index) in authList" :key="index" :label="val.id">{{ val.name }}
						</el-checkbox-button>
					</el-checkbox-group>
				</el-row>

				<div style="margin: 20px 400px;width: 100%;">
					<el-button @click="authIdListModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addAuthIdList()" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

	</div>

</template>

<script>
	import Vue from 'vue';

	export default {
		name: "certificate",

		data() {
			return {
				url: '',
				imageUrl: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				excelUploadId: 1,
				excelUploadUrl: "",

				// 测试时使用
				// uploadUrl: "http://localhost:8063/files/uploadFiles",
				// excelUploadUrlOrgin: "http://localhost:8063/courseContent/excelImport",

				uploadUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
				excelUploadUrlOrgin: "https://biapi.xiaoyujia.com/courseContent/excelImport",
				deleteTips: "确定删除选中成就吗？该操作无法恢复，请谨慎操作!",
				isEdit: false,
				showEdit: true,
				imgModal: false,
				achvModal: false,
				achvTypeModal: false,
				authIdListModal: false,
				detailType: 0,
				loading: true,
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				pageSizeOpts: [10, 20, 50, 100],
				list: [],
				typeList: [],
				authList: [{
					id: 1,
					name: '普通会员'
				}, {
					id: 2,
					name: '行政员工'
				}, {
					id: 3,
					name: '服务员工'
				}, {
					id: 4,
					name: '保姆月嫂'
				}, {
					id: 5,
					name: '共创店'
				}, {
					id: 6,
					name: '合伙人'
				}, {
					id: 7,
					name: '门店店长'
				}, {
					id: 8,
					name: '陪跑店店长'
				}],
				establishList: [{
					id: 0,
					name: '小于'
				}, {
					id: 1,
					name: '等于'
				}, {
					id: 2,
					name: '大于'
				}, {
					id: 3,
					name: '小于等于'
				}, {
					id: 4,
					name: '大于等于'
				}, {
					id: 5,
					name: '不为空'
				}, {
					id: 6,
					name: '为空'
				}],
				switchStateList: [{
						id: 0,
						typeName: "关闭",
						type: "info"
					},
					{
						id: 1,
						typeName: "开启",
						type: "success"
					}
				],
				choiceIndex: '0',
				choiceIndexNow: '0',
				choiceItemIndex: 0,
				choiceItem: {},
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				searchType: null,
				searchState: null,
				searchPush: null,
				quer: {
					search: "",
					authList: null,
					achvState: null,
					typeState: null,
					orderBy: "createTime ASC",
					current: 1,
					size: 10
				},
				stateList: [{
					value: 0,
					text: "下架",
					type: "info"
				}, {
					value: 1,
					text: "上架",
					type: "success"
				}],
				typeStyleList: [{
						id: 0,
						type: "success"
					},
					{
						id: 1,
						type: "info"
					},
					{
						id: 2,
						type: "warning"
					}, {
						id: 3,
						type: "primary"
					}
				],
			}
		},
		created() {
			this.getData()
		},
		methods: {
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceIndex = key
				this.expands = []
				this.showEdit = true
				this.reset()
			},
			getData() {
				this.choiceIndexNow = 3
				if (this.choiceIndex == 0) {
					this.$postData("pageAchievement", this.quer).then(res => {
						this.loading = false
						if (res.status == 200) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
							for (let item of this.list) {
								this.$set(item, "isEdit", false)
								this.$set(item, "authListArray", this.strToArray(item.authList))
							}
							this.choiceIndexNow = this.choiceIndex
						} else {
							this.list = []
							this.$message.error('未查询到相关成就!')
							this.choiceIndexNow = this.choiceIndex
						}
					})
					this.listAchievementType()
				} else if (this.choiceIndex == 1) {
					this.$postData("pageAchievementType", this.quer).then(res => {
						this.loading = false
						if (res.status == 200) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
							for (let item of this.list) {
								this.$set(item, "isEdit", false)
								this.$set(item, "authListArray", this.strToArray(item.authList))
							}
							this.choiceIndexNow = this.choiceIndex
						} else {
							this.list = []
							this.$message.error('未查询到相关成就类型!')
							this.choiceIndexNow = this.choiceIndex
						}
					})
				} else if (this.choiceIndex == 2) {
					this.quer.achvState = null
					this.$postData("pageAchievementRecord", this.quer).then(res => {
						this.loading = false
						if (res.status == 200) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
							this.choiceIndexNow = this.choiceIndex
						} else {
							this.list = []
							this.$message.error('未查询到成就领取记录!')
							this.choiceIndexNow = this.choiceIndex
						}
					})
				}
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.quer.achvType = this.searchType
				this.quer.achvState = this.searchState
				this.quer.typeState = this.searchState
				this.quer.recordState = this.searchState
				this.quer.push = this.searchPush
				this.getData()
			},
			// 重置
			reset() {
				this.loading = true
				this.quer.search = ""
				this.quer.achvType = null
				this.quer.achvState = null
				this.quer.typeState = null
				this.quer.recordState = null
				this.quer.push = null
				this.quer.orderBy = "createTime ASC"
				this.quer.current = 1
				this.searchType = null
				this.searchState = null
				this.searchPush = null
				this.isEdit = false
				this.getData()
			},
			listAchievementType() {
				this.$postData("listAchievementType", {
					// typeState: 1
				}).then(res => {
					if (res.status == 200) {
						this.typeList = res.data
					} else {
						this.typeList = []
						this.$message.error('未查询到相关成就类型!')
					}
				})
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "成就图标") {
					tips = "推荐尺寸：200*200"
				} else if (column.label == "成就图片") {
					tips = "推荐尺寸：200*200"
				} else if (column.label == "等级") {
					tips = "数字越大，等级越高"
				} else if (column.label == "成就类型") {
					tips = "可根据类型进行分类"
				} else if (column.label == "成立条件") {
					tips = "与达标数量进行联动，判定是否达成"
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			// 将字符串转化为数组
			strToArray(val) {
				let arrayString = []
				let array = []
				arrayString = val.split(',')
				arrayString.forEach(it => {
					let value = parseInt(it)
					if (value != null && value != 0) {
						array.push(value)
					}
				})
				return array
			},
			// 格式化有效期
			formatValidity(validity) {
				let result = validity ? validity + "天" : "暂未录入"
				if (result == '999天') {
					result = '永久'
				}
				return result
			},
			// 格式化成就类型
			formatType(val) {
				let result = "暂无"
				let id = val.achvType
				for (let item of this.typeList) {
					if (id == item.id) {
						result = item.typeName
						break
					}
				}
				return result
			},
			formatTypeStyle(type) {
				return this.typeStyleList[type % 4].type
			},
			// 成就Excel导入
			excelImport(index) {
				let id = this.list[index].id
				let excelUploadUrl = this.excelUploadUrlOrgin
				excelUploadUrl += "/" + id
				this.excelUploadUrl = excelUploadUrl
				this.excelUploadId = index
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 成就Excel模板下载
			excelDownload() {
				this.$postData("excelDownload", null, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('成就Excel模板下载成功!')
					this.blobExport({
						tablename: "成就Excel模板",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.$set(this.choiceItem, "achvIcon", res.data)
				} else if (this.uploadImgType == 1) {
					this.$set(this.choiceItem, "achvImg", res.data)
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadId = index

				if (this.uploadImgType == 0) {
					this.$message.info('推荐上传尺寸：250*250')
				} else if (this.uploadImgType == 1) {
					this.$message.info('推荐上传尺寸：250*250')
				}
			},
			// 打开图片预览
			openImg(url) {
				this.imageUrl = url || this.blankImg
				this.imgModal = true
			},
			// 打开视频预览
			openVideo(url) {
				if (url != null && url != '') {
					this.videoUrl = url
				}
				this.videoModal = true
			},
			openModal(index, index1) {
				if (index == 0) {
					this.detailType = 0
					this.choiceItem = {}
					this.$set(this.choiceItem, "authListArray", [])
					this.achvModal = true
				} else if (index == 1) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.achvModal = true
				} else if (index == 2) {
					if (this.detailType == 0) {
						if (!this.choiceItem.authListArray || this.choiceItem.authListArray.length == 0) {
							this.$set(this.choiceItem, "authListArray", [])
						}
					} else if (this.detailType == 1) {
						this.choiceItem = this.list[this.choiceItemIndex]
					}
					this.authIdListModal = true
				} else if (index == 3) {
					this.detailType = 0
					this.choiceItem = {}
					this.$set(this.choiceItem, "authListArray", [])
					this.achvTypeModal = true
				} else if (index == 4) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.achvTypeModal = true
				} else if (index == 5) {
					if (this.detailType == 0) {
						if (!this.choiceItem.authListArray || this.choiceItem.authListArray.length == 0) {
							this.$set(this.choiceItem, "authListArray", [])
						}
					} else if (this.detailType == 1) {
						this.choiceItem = this.list[this.choiceItemIndex]
					}
					this.authIdListModal = true
				}
			},
			//打开员工信息详情
			rowClick(id) {
				// this.baomuId = baomuId
				// this.achvModal = true
				// this.loading = true
				// this.getBaomuDetail(baomuId)
			},
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			// 添加权限列表
			addAuthIdList() {
				this.authIdListModal = false
			},
			// 删除权限标签
			closeAuthTags(index, index1) {
				this.list[index].authListArray.splice(index1, 1)
			},
			closeAuthTags1(index, index1) {
				this.choiceItem.authListArray.splice(index1, 1)
			},
			// 添加成就
			insertAchievement() {
				let achievement = this.choiceItem
				if (!achievement.achvTitle) {
					this.$message.error('请填写成就标题！')
				} else if (!achievement.achvContent) {
					this.$message.error('请填写成就内容！')
				} else if (!achievement.achvIcon) {
					this.$message.error('请上传成就图标！')
				} else if (!achievement.achvType) {
					this.$message.error('请选择成就类型！')
				} else if (!achievement.achvLevel) {
					this.$message.error('请填写成就等级！')
				} else {
					let no = localStorage.getItem('account') || 'admin'
					this.$set(achievement, "creator", no)
					this.$set(achievement, "state", 1)

					// 格式化权限角色列表
					if (achievement.authListArray) {
						achievement.authList = achievement.authListArray.join(',')
						if (!achievement.authList.length) {
							achievement.authList = '0'
						}
					}
					this.$postData("insertAchievement", achievement).then(res => {
						if (res.status == 200) {
							this.$message.success('成就添加成功!')
							this.achvModal = false
							this.list.push(res.data)
						} else {
							this.$message.error('成就添加失败！' + res.msg)
						}
					})
				}
			},
			// 成就类型
			insertAchievementType() {
				let achievementType = this.choiceItem
				if (!achievementType.typeName) {
					this.$message.error('请填写成就类型名称！')
				} else {
					let no = localStorage.getItem('account') || 'admin'
					this.$set(achievementType, "creator", no)
					// 格式化权限角色列表
					if (achievementType.authListArray) {
						achievementType.authList = achievementType.authListArray.join(',')
						if (!achievementType.authList.length) {
							achievementType.authList = '0'
						}
					}
					this.$postData("insertAchievementType", achievementType).then(res => {
						if (res.status == 200) {
							this.$message.success('成就类型添加成功!')
							this.achvTypeModal = false
							this.list.push(res.data)
						} else {
							this.$message.error('成就类型添加失败！' + res.msg)
						}
					})
				}
			},
			// 删除成就
			deleteAchievement(val) {
				this.$postData("deleteAchievement", val).then(res => {
					if (res.status == 200) {
						this.$message.success('成就删除成功!')
						this.$delete(this.list, this.getIndex(val.id))
					} else {
						this.$message.error('成就删除失败！' + res.msg)
					}
				})
			},
			// 删除成就类型
			deleteAchievementType(val) {
				this.$postData("deleteAchievementType", val).then(res => {
					if (res.status == 200) {
						this.$message.success('成就类型删除成功!')
						this.$delete(this.list, this.getIndex(val.id))
					} else {
						this.$message.error('成就类型删除失败！' + res.msg)
					}
				})
			},
			// 更改成就
			updateAchievement(val) {
				// 格式化权限角色列表
				if (val.authListArray) {
					val.authList = val.authListArray.join(',')
					if (!val.authList.length) {
						val.authList = '0'
					}
				}
				this.$postData("updateAchievement", val).then(res => {
					if (res.status == 200) {
						this.$message.success('成就更新成功!')
						this.list[this.choiceItemIndex] = this.choiceItem
					} else {
						this.$message.error('成就更新失败！' + res.msg)
					}
				})
			},
			// 更改成就类型
			updateAchievementType(val) {
				// 格式化权限角色列表
				if (val.authListArray) {
					val.authList = val.authListArray.join(',')
					if (!val.authList.length) {
						val.authList = '0'
					}
				}
				this.$postData("updateAchievementType", val).then(res => {
					if (res.status == 200) {
						this.$message.success('成就类型更新成功!')
						this.list[this.choiceItemIndex] = this.choiceItem
					} else {
						this.$message.error('成就类型更新失败！' + res.msg)
					}
				})
			},
			beforeImgUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!');
				}
				return isLt2M;
			}
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>