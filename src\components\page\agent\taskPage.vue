<template>
    <div class="table">
        <div class="container">
            <el-form ref="form">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="处理人">
                            <el-input
                                    clearable
                                    v-model="form.employeeName"
                                    @keyup.enter.native="form.current=1,getData()"
                                    placeholder="处理人"
                                    style="width:200px"
                                    class="handle-input mr10"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="任务名称">
                            <el-input
                                    clearable
                                    @keyup.enter.native="form.current=1,getData()"
                                    v-model="form.title"
                                    placeholder="任务名称"
                                    style="width:190px"
                                    class="handle-input mr10"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="任务对象">
                            <el-input
                                    clearable
                                    @keyup.enter.native="form.current=1,getData()"
                                    v-model="form.search"
                                    placeholder="任务对象"
                                    style="width:200px"
                                    class="handle-input mr10"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="处理门店">
                            <el-input
                                    clearable
                                    @keyup.enter.native="form.current=1,getData()"
                                    v-model="form.storeName"
                                    placeholder="处理门店"
                                    style="width:200px"
                                    class="handle-input mr10"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="创建时间">
                            <el-date-picker
                                    v-model="value2"
                                    type="datetimerange"
                                    :picker-options="pickerOptions"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    align="right">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="处理时间">
                            <el-date-picker
                                    v-model="value3"
                                    type="datetimerange"
                                    :picker-options="pickerOptions"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    align="right">
                            </el-date-picker>
                        </el-form-item>


                    </el-col>
                    <el-col :span="8">
                        <el-radio-group v-model="form.roleId">
                            <el-radio-button :label="null" border>全部</el-radio-button>
                            <el-radio-button :label="66" border>家政任务</el-radio-button>
                            <el-radio-button :label="3" border>客服任务</el-radio-button>
                            <el-radio-button :label="98" border>销售任务</el-radio-button>
                        </el-radio-group>
                    </el-col>
                    <el-col :span="8">
                        <el-radio-group v-model="form.status">
                            <el-radio :label="null">全部</el-radio>
                            <el-radio :label="1">已处理</el-radio>
                            <el-radio :label="0">未处理</el-radio>
                            <el-radio :label="2">无人接听</el-radio>
                        </el-radio-group>
                        <el-divider direction="vertical"></el-divider>

                    </el-col>
                    <el-col :span="8">

                        <el-form-item>
                            <el-button type="success" round @click="form.current=1,getData()" :loading="searchLoading">
                                搜索
                            </el-button>
                            <div style="color: red;font-weight: bold;font-size: 16px">请先进行筛选条件搜索查看数据</div>
                        </el-form-item>

                    </el-col>
                </el-row>
            </el-form>

            <el-divider direction="vertical"></el-divider>
            <el-button type="primary" @click="shouShare=true" v-if="multipleSelection.length>0">一键分发</el-button>
            <!--            {{multipleSelection}}-->
            <br>
            <br>
            <el-tabs type="border-card">

                <el-tab-pane label="任务记录">
                    <el-table :data="taskList" @selection-change="handleSelectionChange"
                              v-loading="searchLoading"
                              :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable"
                              @cell-dblclick="">
                        <el-table-column
                                type="selection"
                                width="40">
                        </el-table-column>
                        <el-table-column
                                prop="title"
                                label="任务名称"
                                width="150">
                        </el-table-column>
                        <el-table-column
                                prop="toType"
                                label="处理对象"
                                width="300">
                            <template slot-scope="scope">
                                <div v-if="scope.row.toType==1">
                                    <el-tag type="success">客户</el-tag>
                                    <el-link type="primary" @click="toMember(scope.row.toObjectId)">
                                        {{scope.row.realName}}( {{scope.row.no}})
                                        {{scope.row.phone}}
                                    </el-link>
                                </div>
                                <div v-if="scope.row.toType==0">
                                    <el-tag type="warning">员工</el-tag>
                                    <el-link type="primary" @click="toBaomuInfo(scope.row.toObjectId)">
                                        {{scope.row.realName}}( {{scope.row.no}})
                                        {{scope.row.phone}}
                                    </el-link>

                                </div>


                            </template>
                        </el-table-column>
                        <el-table-column
                                prop="status"
                                label="状态"
                                width="80">
                            <template slot-scope="scope">
                                <el-tag v-if="scope.row.status==1" type="success">已处理</el-tag>
                                <el-tag v-else-if="scope.row.status==0" type="warning">未处理</el-tag>
                                <el-tag v-else-if="scope.row.status==2" type="danger">未接听</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column
                                prop="creatTime"
                                label="创建时间"
                                width="90"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="actualProcessingTime"
                                label="处理时间"
                                width="100"
                        ></el-table-column>
                        <el-table-column
                                prop="employeeName"
                                label="处理人"
                                width="80">
                        </el-table-column>
                        <el-table-column
                                prop="storeName"
                                label="处理门店"
                                width="180">
                        </el-table-column>
                        <el-table-column
                                prop="billNo"
                                label="订单号"
                                width="180">
                        </el-table-column>

                        <el-table-column
                                label="操作"
                                fixed="right"
                                min-width="180">
                            <template slot-scope="scope">
                                <el-button size="mini" @click="dialogVisible=true,changeDom=scope.row" type="primary"
                                           v-if="checkRoleId(scope.row.status)">任务分发
                                </el-button>
                                <el-button
                                        v-if="scope.row.status==1"
                                        @click.native.prevent="showRow(scope.row)"
                                        type="text"
                                        size="small">
                                    查看详情
                                </el-button>
                            </template>

                        </el-table-column>
                    </el-table>
                    <div class="pagination">
                        <Page :total="pageInfo.total"
                              @on-change="onChange"
                              :show-total="true"
                              :show-sizer="true"
                              :page-size-opts="pageSizeOpts"
                              @on-page-size-change="onPageSizeChange"
                              :page-size="pageInfo.size"/>
                    </div>
                </el-tab-pane>

                <el-tab-pane label="任务分析">
                    <el-row>
                        <el-col :span="8">
                            <el-card shadow="hover">
                                <div>未处理 <span class="allTaskData-num">{{allTaskData.status0}}</span></div>
                            </el-card>
                        </el-col>
                        <el-col :span="8">
                            <el-card shadow="hover">
                                <div>已处理<span class="allTaskData-num">{{allTaskData.status1}}</span></div>
                            </el-card>
                        </el-col>
                        <el-col :span="8">
                            <el-card shadow="hover">
                                <div>未接听<span class="allTaskData-num">{{allTaskData.status2}}</span></div>
                            </el-card>
                        </el-col>
                    </el-row>
                    <el-divider></el-divider>
                    <el-row>
                        <div v-for="(item,index) in taskDataList" :key="index">

                            <el-col :span="8">
                                <el-card class="box-card" style="min-height: 350px">
                                    <div slot="header" shadow="hover">
                                        <span><b>{{item.realName}}</b></span>
                                    </div>
                                    <div v-for="(taskData,index) in item.taskDataDtos" :key="index" class="text item">
                                        {{ taskData.title }}
                                        <el-card shadow="hover">
                                            <el-tag type="info">未处理 - {{ taskData.status0==null?'0':taskData.status0}}
                                            </el-tag>
                                            <el-divider direction="vertical"></el-divider>

                                            <el-tag type="success">已处理 - {{
                                                taskData.status1==null?'0':taskData.status1}}
                                            </el-tag>
                                            <el-divider direction="vertical"></el-divider>

                                            <el-tag type="danger">未接听 - {{
                                                taskData.status2==null?'0':taskData.status2}}
                                            </el-tag>
                                        </el-card>
                                    </div>
                                </el-card>

                            </el-col>


                        </div>
                    </el-row>
                </el-tab-pane>

            </el-tabs>
        </div>
        <Modal v-model="responseInfoModal" class="Modal" title="回访详情" :mask-closable="false">
            <div class="addBody" style="min-height: 400px;">
                <responseInfo v-if="responseInfoModal"
                              :id="show"></responseInfo>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <el-dialog
                title="请选择转发给哪个经纪人："
                :visible.sync="dialogVisible"
                width="30%">
            <div v-for="(item,index) in agentList" class="agent-box" :key="index">
                {{item.no}}--{{item.realName}}
                <el-button size="mini" type="primary" @click="changeDom.employeeId=item.id,save(changeDom)">任务分发
                </el-button>
            </div>
        </el-dialog>
        <el-dialog
                title="请选择转发给哪个经纪人："
                :visible.sync="shouShare"
                width="30%">
            <div>已选择<span style="color:red;">{{multipleSelection.length}}</span>条任务</div>
            <el-divider></el-divider>

            <el-autocomplete
                    style="width: 70%"
                    v-model="realName"
                    :fetch-suggestions="querySearchAsync"
                    placeholder="请输入接受分发人员名称"
                    @select="handleSelect"
                    :trigger-on-focus="false">
            </el-autocomplete>
            <el-button size="mini" type="primary" @click="saveTaskShareDto" v-if="shareId!==null">任务分发</el-button>
        </el-dialog>
    </div>
</template>

<script>
    import responseInfo from '@/components/page/agent/info/responseInfo.vue'

    export default {
        name: "taskPage",
        data() {
            return {
                list: [],
                realName: null,
                shareId: null,
                shouShare: false,
                searchLoading: false,
                allTaskData: {
                    status0: 0,
                    status1: 0,
                    status2: 0,
                },
                taskDataList: [],
                agentList: [],
                changeDom: null,
                dialogVisible: false,
                responseInfoModal: false,
                show: null,
                taskList: [],
                pageSizeOpts: [10, 20, 30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                form: {
                    apStartTime: null,
                    startTime: null,
                    apEndTime: null,
                    endTime: null,
                    roleId: null,
                    storeName: null,
                    employeeId: null,
                    storeId: null,
                    title: null,
                    search: null,
                    employeeName: null,
                    status: null,
                    current: 1,
                    size: 10
                },
                user: {
                    id: localStorage.getItem("id"),
                    storeId: localStorage.getItem("storeId"),
                    roleId: localStorage.getItem("roleId"),
                },
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                value2: null,
                value3: null,
            }
        },
        components: {
            'responseInfo': responseInfo,
        },
        created() {
            if (this.user.roleId == 66) {
                this.form.roleId = 66
            }
            // this.getData();
            this.getAgent();
        },
        methods: {
            checkRoleId(status) {
                if (status == 0) {
                    return false;
                }
                const roleId = this.user.roleId;
                return roleId == 1 || roleId == 66;
            },
            saveTaskShareDto() {
                let taskIds = []
                this.multipleSelection.forEach(v => {
                    taskIds.push(v.id)
                })
                let dom = {
                    id: this.shareId,
                    taskIds: taskIds,
                }
                this.$postData("saveTaskShareDto", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.list = []
                        this.realName = null
                        this.shareId = null
                        this.multipleSelection = []
                        this.shouShare = false
                        this.$message.success("保存成功")
                        this.getData();

                    } else {
                        this.$message.error("保存失败，" + res.msg);
                    }
                })
            },
            handleSelect(item) {
                this.shareId = item.name;


            },
            createStateFilter(queryString) {
                return (state) => {
                    return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                };
            },
            querySearchAsync(queryString, cb) {
                this.restaurants = []
                let baomuModel = {
                    realName: this.realName,
                }
                this.$postData("getEmployeeSelectDtoByName", baomuModel, {}).then(res => {
                    if (res.status == 200) {
                        this.list = []
                        this.list = res.data;
                        this.list.forEach((item, index, arr) => {
                            var a = {}
                            a.value = item.realName;
                            a.name = item.id;
                            this.restaurants.push(a);
                        });

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                var restaurants = this.restaurants;
                var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants;
                cb(restaurants);
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            toBaomuInfo(id) {
                this.$router.push({path: '/baomuInfo', query: {"id": id}})
            },
            toMember(id) {
                this.$router.push({path: '/memberInfo', query: {"id": id}})
            },
            save(dom) {
                dom.creatTime = null
                this.$postData("saveTask", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.dialogVisible = false
                        this.$message.success("保存成功")
                        this.getData();

                    } else {
                        this.$message.error("保存失败，" + res.msg);
                    }
                })
            },

            getAgent() {
                let store = Number(localStorage.getItem("storeId"));
                this.$postData("agent_list", {storeId: store, state: 1}, {}).then(res => {
                    if (res.status == 200) {
                        this.agentList = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            showRow(row) {
                this.show = row.id
                this.responseInfoModal = true
            },
            toMember(id) {
                this.$router.push({path: '/memberInfo', query: {"id": id}})
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.form.size = size;
                this.getData();
            },
            onChange(index) {
                console.log(index)
                this.form.current = index;
                this.getData();
            },
            async getData() {
                this.searchLoading = true
                this.form.startTime = null
                this.form.endTime = null
                if (this.value2 == null) {
                    this.form.startTime = null
                    this.form.endTime = null
                } else {
                    this.form.startTime = this.value2[0]
                    this.form.endTime = this.value2[1]
                }

                this.form.apStartTime = null
                this.form.apEndTime = null
                if (this.value3 == null) {
                    this.form.apStartTime = null
                    this.form.apEndTime = null
                } else {
                    this.form.apStartTime = this.value3[0]
                    this.form.apEndTime = this.value3[1]
                }

                // if (this.user.roleId !== '1' && this.user.roleId !== '12') {
                // if (this.user.roleId !== '66' || this.user.roleId !== '80') {
                //     this.form.employeeId = this.user.id
                // }
                // else {
                //     if (this.user.id != "28511") {
                //         this.form.storeId = this.user.storeId
                //     }
                // }
                // }
                await this.$postData("taskPage", this.form, {}).then(res => {
                    if (res.status == 200) {
                        this.taskList = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                await this.$postData("getTaskDataDto", this.form, {}).then(res => {
                    if (res.status == 200) {
                        this.taskDataList = res.data
                        this.allTaskData.status0 = 0
                        this.allTaskData.status1 = 0
                        this.allTaskData.status2 = 0

                        this.taskDataList.forEach(v => {
                            v.taskDataDtos.forEach(h => {
                                this.allTaskData.status0 += h.status0
                                this.allTaskData.status1 += h.status1
                                this.allTaskData.status2 += h.status2
                            })
                        })
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                this.searchLoading = false;
            },
        }

    }
</script>

<style scoped>
    .allTaskData-num {
        padding: 0 20px;
        color: red;
        font-size: 24px;
        font-weight: 700;
    }

    .agent-box {
        padding: 10px;
        border: 1px solid #ddd;
    }
</style>
