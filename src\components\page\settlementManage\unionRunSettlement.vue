<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="6">
            <el-form-item label="合伙人名称">
              <el-input
                  clearable
                  v-model="form.franchiseName"
                  placeholder="请输入合伙人名称"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商户号">
              <el-input
                  clearable
                  v-model="form.merchantCode"
                  placeholder="请输入商户号"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="项目经理">
              <el-input
                  clearable
                  v-model="form.storeManager"
                  placeholder="请输入项目经理"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
            <el-button type="info"
                       style="margin-bottom: 15px"
                       @click="exportUnionCashJournalList"
                       icon="el-icon-download">导出
            </el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="流量经理">
              <el-input
                  clearable
                  v-model="form.storeTraffic"
                  placeholder="请输入流量经理"
                  style="width:160px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="技术支持">
              <el-input
                  clearable
                  v-model="form.support"
                  placeholder="请输入技术支持"
                  style="width:160px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="陪跑项目列表">
          <el-table v-loading="loading" :data="logList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column prop="name" label="合伙人名称" width="97"></el-table-column>
            <el-table-column prop="merchantCode" label="商户号" width="130"></el-table-column>
            <el-table-column prop="storeManager" label="项目经理" width="120"></el-table-column>
            <el-table-column prop="storeTraffic" label="流量经理" width="120"></el-table-column>
            <el-table-column prop="support" label="技术支持" width="120"></el-table-column>
            <el-table-column prop="sumAmount" label="提现总额" width="100"></el-table-column>
            <el-table-column prop="" label="粉丝总数" width="110"></el-table-column>
            <el-table-column prop="openAccountDay" label="开户时长" width="100"></el-table-column>
            <el-table-column prop="nowFlow" label="当前阶段" width="100">
              <template slot-scope="scope">
                <div>{{scope.row.nowFlow==1?'开户期':scope.row.nowFlow==2?'涨粉期':scope.row.nowFlow==3?'业务期':'推广期'}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="allocationReward" label="已发奖金" width="110"></el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-link :href="hrefUrl" target="_blank" id="elLink"/>
  </div>
</template>

<script>
import moment from "moment";
export default {
  name: "register",
  data() {
    return {
      loading: true,
      logList: [],
      nowFlow: '涨粉期',
      roleId: localStorage.getItem('roleId'),
      hrefUrl: "",
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      form: {
        merchantCode: '',
        storeManager: '',
        franchiseName: '',
        support: '',
        storeTraffic: '',
        current: 1,
        size: 10
      },
    }
  },
  created() {
    this.getData();
  },
  methods: {
    exportUnionCashJournalList(){
      this.hrefUrl = "http://localhost:8063/franchiseAudit/exportAllUnionRunData?merchantCode="+this.form.merchantCode+
          "&storeManager="+this.form.storeManager+"&franchiseName="+this.form.franchiseName+"&support="+this.form.support+
          "&storeTraffic="+this.form.storeTraffic
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.form.size = size;
      this.getData();
    },
    onChange(index) {
      this.loading = true;
      this.form.current = index;
      this.getData();
    },
    getData() {
      this.loading = true
      this.form.startTime = '';
      this.form.endTime = '';
      this.$getData("getAllUnionRunData", this.form, {}).then(res => {
        this.loading = false
        if (res.code == 0) {
          this.logList = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    }
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
