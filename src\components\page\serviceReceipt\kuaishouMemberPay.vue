<template>
  <div class="container">
    <div class="handle-box">
      <el-form :inline="true" class="demo-form-inline">

        <el-form-item label="时间">
          <el-date-picker
              v-model="time"
              type="datetimerange"
              range-separator="至"
              value-format="yyyy-MM-dd HH:mm:ss"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="券状态">
          <el-select  filterable v-model="dom.status" placeholder="状态" clearable>
            <el-option
                v-for="(item,index) in statusList"
                :key="index"
                :label="item.name"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="产品分类">
          <el-select  filterable v-model="dom.productCateGoryId" placeholder="状态" clearable>
            <el-option
                v-for="(item,index) in productCategoryList"
                :key="index"
                :label="item.name"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="会员账号">
         <el-input v-model="dom.account"></el-input>
        </el-form-item>

        <el-form-item label="券号">
          <el-input v-model="dom.ticketNo"></el-input>
        </el-form-item>

        <el-form-item label="订单号">
          <el-input v-model="dom.billNo"></el-input>
        </el-form-item>


        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="onQuery" v-loading="loading">
            查询
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-refresh" @click="reset()" v-loading="loading">
            重置
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-download" type="primary" plain @click="exportExcel" v-loading="loading">
            导出
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button  type="primary" plain @click="writeVisible = true">
            核销券
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card>
          合计订单量:<span style="color: red">{{ sumData.orderNum }}</span>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          合计营业额:<span style="color: red">{{ sumData.totalAmount }}</span>
        </el-card>
      </el-col>
    </el-row>

    <el-table :data="dataList" v-loading="loading" style="width: 100%"
              :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
      <el-table-column
          label="序号"
          type="index"
          align="center">
      </el-table-column>
      <el-table-column
          prop="account"
          label="会员账户"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="bindTel"
          label="联系电话"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="ticketNo"
          label="快手券号"
          width="200"
          align="center">

      </el-table-column>
      <el-table-column
          label="订单金额"
          prop="amount"
          align="center">
      </el-table-column>
      <el-table-column
          prop="title"
          label="产品名称"
          align="center">
      </el-table-column>
        <el-table-column
            prop="status"
            label="券状态"
            align="center">
          <template slot-scope="scope">
            <el-tag type="info" v-if="scope.row.status === 1">未使用</el-tag>
            <el-tag type="success" v-if="scope.row.status === 2">已关联</el-tag>
            <el-tag type="warning" v-if="scope.row.status === 3">已解绑</el-tag>
          </template>
        </el-table-column>
          <el-table-column
              prop="billNo"
              label="关联订单号"
              width="200"
              align="center">
          </el-table-column>
      <el-table-column
          prop="orderStatus"
          label="订单状态"
          align="center">
      </el-table-column>
            <el-table-column
                prop="createTime"
                label="创建时间"
                align="center">
      </el-table-column>
      <el-table-column
          prop="remark"
          label="备注"
          align="center">
      </el-table-column>
      <el-table-column
          fixed="right"
          label="操作"
          width="200">
        <template slot-scope="scope">
          <el-button v-if="timeExpre(scope.row)" type="warning" size="small" @click="cancelTicket(scope.row)">解绑券</el-button>
          <el-button type="success" size="small" @click="writeOff(scope.row)" v-if="!scope.row.billNo && scope.row.status === 1">订单核销</el-button>
          <el-button type="primary" size="small" @click="remarkInfo(scope.row)">备注</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChangePage"
            :current="dom.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="dom.size"/>
    </div>

    <el-dialog
        title="编辑"
        :visible.sync="editModal"
        width="30%"
        center>
      <el-input v-model="editRow.remark" placeholder="请输入内容"></el-input>
      <span slot="footer" class="dialog-footer">
    <el-button @click="editModal = false">取 消</el-button>
    <el-button type="primary" @click="confineEdit()">确 定</el-button>
  </span>
    </el-dialog>



    <el-dialog
        title="券核销"
        :visible.sync="writeVisible"
        width="30%">
      <span style="color: red">注:绑券时请先查询,未注册用户请去客户管理添加客户</span>
      <el-row :gutter="10">
        <el-col :span="12">
          绑定手机号查询:<el-select
              v-model="bandInfo.memberId"
              filterable
              remote
              reserve-keyword
              placeholder="输入用户手机查找用户"
              :remote-method="memberremoteMethod"
              @change="changmember"
              :loading="memberloading">
            <el-option
                v-for="item in memberoptions"
                :key="item.id"
                :label="item.value +'-'+ item.label "
                :value="item.value">
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <br>
         券号: <el-input v-model="bandInfo.code" placeholder="券号"></el-input>
        </el-col>
      </el-row>




      <span slot="footer" class="dialog-footer">
    <el-button @click="writeVisible = false">取 消</el-button>
    <el-button type="primary"  @click="bindTicket">绑 定</el-button>
  </span>
    </el-dialog>
  </div>
</template>

<script>
const moment = require('moment');
export default {
  data() {
    return {
      memberloading:false,
      memberoptions:[],
      writeVisible:false,
      editModal:false,
      editRow:{},
      sumData:{
        totalAmount:0,
        orderNum:0
      },
      bandInfo:{
        phone:'',
        memberId:'',
        code:'',
      },
      statusList:[
        {id:1,name:'未使用'},
        {id:2,name:'已使用'},
        {id:3,name:'已退款'},
      ],
      loading: false,
      time:[],
      dom: {
        size: 15,
        current: 1,
        startDate:null,
        endDate:null,
        ticketNo:null,
        status:null,
        account:null,
        productCateGoryId:null,
        billNo:null
      },
      dataList: [],
      typeList:[{id:1,name:'增加'},{id:2,name: '扣减'}],
      pageSizeOpts: [10, 20, 50, 100, 150, 200],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      productCategoryList:[]
    }
  },
  created() {
    this.getData();
    this.getsumData();
    this.getAllProductCategory();

  },
  methods:{

    bindTicket() {

      this.loading = true;
      this.$getData("bindKuaiShouTicket", this.bandInfo).then(res => {
        if (res.code === 0) {
          this.loading = false;
          this.$message({
            type: 'success',
            message: '操作成功'
          });
          this.getData();
        } else {
          this.loading = false;
          this.$message({
            type: 'error',
            message: res.msg
          });
        }
      })
    },
    changmember(item){
      this.bandInfo.memberId = item;
      this.memberoptions.forEach(v=>{
        if (v.value== item){
          this.bandInfo.phone= v.bindTel

        }
      })
    },
    memberremoteMethod(query) {
      this.memberoptions = [];
      if (query.length<8){
        return
      }
      if (query !== '') {
        this.memberloading = true;
        this.$postUrl("get_memberbyBindTel",query,null, {}).then(res => {
          if (res.status == 200) {
            this.memberloading = false;
            res.data.forEach((item, index, arr) => {
              // console.log(item);
              var a={};
              a.id=item.id;
              a.value=item.id;
              a.label=item.name;
              a.bindTel=item.bindTel;
              this.memberoptions.push(a);
            });
            // console.log(this.memberoptions)
          } else {
            this.$message.error("修改失败，" + res.msg);
          }
        })
      } else {
        this.memberoptions = [];
      }
    },
    cancelTicket(row) {
      if (!row.orderId) {
        return this.$message({
          type: 'danger',
          message: '快手orderId为空'
        });
      }
      this.loading = true;
      this.$getData("cancelKuaiShouTicket",{orderId:row.orderId}).then(res => {
        if (res.code === 0) {
          this.loading = false;
          this.$message({
            type: 'success',
            message: '操作成功'
          });
          this.getData();
        } else {
          this.loading = false;
          this.$message({
            type: 'error',
            message: res.msg
          });
        }
      })

    },
    timeExpre(row) {
      if (row.billNo || row.status === 3) {
        return false;
      }
      const givenTime = moment(row.createTime, 'YYYY-MM-DD HH:mm:ss');
      const currentTime = moment();
      //计算时间是否超过一小时
      return this.isWithinAnHour(givenTime,currentTime);
    },
    isWithinAnHour(givenTime, currentTime) {
      // 使用 moment.js 的 diff 方法计算时间差
      const diffInMinutes = givenTime.diff(currentTime, 'minutes');
      // 检查时间差是否在负60到0之间（即一小时内）
      return diffInMinutes >= -60 && diffInMinutes <= 0;
    },
    getAllProductCategory() {
      this.$getData("getAllCategory",{}).then(res => {
        if (res.status === 200) {
          this.productCategoryList = res.data;
        }
      })
    },
    changeQK() {
      if (!this.dom.arrears) {
        this.dom.status = null;
      }
    },
    getsumData() {
      this.loading = true;
      this.$postData("getKuaiShouSumAll", this.dom).then(res => {
        if (res.status === 200) {
          this.loading = false;
          this.sumData = [];
          this.sumData = res.data;
        }
      })
    },
    writeOff(row) {
      this.editRow = row;
      this.$prompt('请输入订单号(一经确认无法回逆,请谨慎操作)', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern:  /^\d+$/,
        inputErrorMessage: '订单号输入错误'
      }).then(({ value }) => {
        this.editRow.billNo = value;
        this.editRow.employeeName = localStorage.getItem("realName");
        this.editRow.employeeId = localStorage.getItem("id");
        this.loading = true;
        this.$postData("writeoffKuaiShouNo", this.editRow).then(res => {
          if (res.status === 200) {
            this.loading = false;
            this.$message({
              type: 'success',
              message: '操作成功'
            });
            this.getData();
          } else {
            this.loading = false;
            this.$message({
              type: 'error',
              message: res.msg
            });
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消输入'
        });
      });
    },
    confineEdit() {
      this.loading = true;
      this.$postData("editRemarkKuaiShouTicket", {id: this.editRow.id, remark: this.editRow.remark}).then(res => {
        if (res.status === 200) {
          this.loading = false;
          this.$message({
            type: 'success',
            message: '操作成功'
          });
          this.editModal = false;
          this.getData();
        } else {
          this.loading = false;
          this.$message({
            type: 'error',
            message: res.data
          });
        }
      })
    },
    remarkInfo(row) {
    this.editRow = row;
    this.editModal = true;
    },

    reset() {
      this.dom.size = 15;
      this.dom.current = 1;
      this.dom.startDate = null;
      this.dom.endDate = null;
      this.dom.ticketNo = null;
      this.dom.status = null;
      this.dom.account = null;
      this.dom.productCateGoryId = null;
      this.getsumData();
      this.getData();
    },
    onChangePage(index) {
      this.dom.current = index;
      this.getData();
    },
    onPageSizeChange(size) {
      this.dom.size = size;
      this.getData();
    },
    getData() {
      this.loading = true;
      this.$postData("getKuaiShouMemberOrderList", this.dom).then(res => {
        if (res.status === 200) {
          this.loading = false;
          this.dataList = [];
          this.dataList = res.data.records;
          this.pageInfo.total = res.data.total;
        }
      })
    },
    exportExcel() {
      if (this.time){
        this.dom.startDate = this.time[0];
        this.dom.endDate = this.time[1];
      }
      this.loading = true;
      this.$postData("getKuaiShouMemberOrderExport", JSON.stringify(this.dom), {
        responseType: "arraybuffer"
      }).then(res => {
        this.blobExport({
          tableName: "快手先买后核销记录",
          res: res
        });
      });
    },
    onQuery() {
      if (this.time){
        this.dom.startDate = this.time[0];
        this.dom.endDate = this.time[1];
      }
      this.dom.current = 1;
      this.getData();
      this.getsumData();
    },
    blobExport({tableName, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tableName + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
      const that = this;
      setTimeout(function () {
        that.loading = false;
      }, 2500);

    },
  }

}
</script>

<style scoped>
.allAmount {
  font-weight: bold;
  /*padding-left: 10px;*/
  color: red;
  font-size: 24px;
}
</style>
