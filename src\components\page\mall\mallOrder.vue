<template>
<div class="app-container">
  <el-card>
    <div class="filter-container">
      <el-input
        v-model="searchForm.billNo"
        placeholder="订单号"
        style="width: 200px;"
        @keyup.enter.native="handleSearch"
      />
      <el-select
        v-model="searchForm.state"
        placeholder="订单状态"
        clearable
        style="width: 120px;margin-left:10px"
      >
        <el-option label="待付款" :value="1" />
        <el-option label="待发货" :value="2" />
        <el-option label="待收货" :value="3" />
        <el-option label="已完成" :value="4" />
        <el-option label="已取消" :value="5" />
      </el-select>

      <el-input
        v-model="searchForm.account"
        placeholder="下单账号"
        style="width: 200px;margin-left:10px"
        @keyup.enter.native="handleSearch"
      />

      <el-button type="primary" @click="handleSearch" style="margin-left:10px">搜索</el-button>
    </div>

    <el-table
      :data="tableData"
      border
      style="width: 100%;margin-top:20px;"
    >
      <el-table-column prop="billNo" label="订单号" />
      <el-table-column prop="memberAccount" label="会员账号" />
      <el-table-column label="订单状态">
        <template slot-scope="scope">
          {{ scope.row.state | statusFilter }}
        </template>
      </el-table-column>
      <el-table-column prop="orderPrice" label="订单金额(元)"  />
      <el-table-column prop="price" label="已付金额(元)"  />
      <el-table-column prop="createTime" label="创建时间"  />
<!--      <el-table-column prop="paymentTime" label="支付时间" width="180" />-->
<!--      <el-table-column prop="logisticsNo" label="物流单号" width="200" />-->
      <el-table-column label="操作" >
        <template slot-scope="scope">
          <el-button
            type="primary"
            size="mini"
            @click="handleDetail(scope.row)"
          >详情</el-button>
          <el-button
              v-if="scope.row.state === 2"
              type="primary"
              size="mini"
              @click="handleLogisisc(scope.row)"
          >发货</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      style="margin-top:20px;"
      @current-change="handleCurrentChange"
      :current-page.sync="pagination.current"
      :page-size="pagination.size"
      layout="total, prev, pager, next"
      :total="pagination.total"
    />
  </el-card>

  <el-dialog
    :visible.sync="detailDialogVisible"
    title="订单详情"
    width="1000px"
    @close="handleCloseDetail"
  >
    <div v-if="detailData.mallOrder && detailData.mallOrderReceive" class="order-detail-layout">
      <!-- 上半部分 -->
      <div class="order-detail-top-beauty">
        <!-- 左侧：订单信息 -->
        <div class="detail-block left-block">
          <div class="detail-title">订单信息</div>
          <div class="detail-row"><span class="label">订单号：</span><span>{{ detailData.mallOrder.billNo }}</span></div>
          <div class="detail-row"><span class="label">订单状态：</span><span>{{ detailData.mallOrder.state | statusFilter }}</span></div>
          <div class="detail-row"><span class="label">创建时间：</span><span>{{ detailData.mallOrder.createTime }}</span></div>
          <div class="detail-row"><span class="label">支付时间：</span><span>{{ detailData.mallOrder.paymentTime || '-' }}</span></div>
          <div class="detail-row"><span class="label">订单金额：</span><span>￥{{ detailData.mallOrder.orderPrice }}</span></div>
          <div class="detail-row"><span class="label">已付金额：</span><span>￥{{ detailData.mallOrder.price }}</span></div>
          <div class="detail-row"><span class="label">备注：</span><span>{{ detailData.mallOrder.remark || '-' }}</span></div>
        </div>
        <!-- 右侧：下单人信息 + 收货信息 -->
        <div class="detail-block right-block">
          <div>
            <div class="detail-title">下单人信息</div>
            <div class="detail-row"><span class="label">下单账号：</span><span>{{ detailData.mallOrder.memberAccount }}</span></div>
            <div class="detail-row"><span class="label">会员ID：</span><span>{{ detailData.mallOrder.memberId }}</span></div>
          </div>
          <div style="margin-top: 24px;">
            <div class="detail-title">收货信息</div>
            <div class="detail-row"><span class="label">姓名：</span><span>{{ detailData.mallOrderReceive.contact }}</span></div>
            <div class="detail-row"><span class="label">手机号：</span><span>{{ detailData.mallOrderReceive.tel }}</span></div>
            <div class="detail-row">
              <span class="label">地址：</span>
              <span>
                {{ detailData.mallOrderReceive.cityName || '' }}
                {{ detailData.mallOrderReceive.areaName || '' }}<br>
                {{ detailData.mallOrderReceive.street || '' }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <!-- 下半部分：商品信息 -->
      <div class="detail-block goods-block">
        <div class="detail-title">商品信息</div>
        <el-table :data="detailData.mallOrderSkus" border style="width: 100%;margin-top:10px;" class="goods-table">
          <el-table-column label="图片" width="80" align="center">
            <template slot-scope="scope">
              <img :src="scope.row.skuImage" alt="" style="width:60px;height:60px;object-fit:cover;border-radius:8px;" />
            </template>
          </el-table-column>
          <el-table-column prop="skuName" label="商品名称" align="center" />
          <el-table-column prop="number" label="数量" align="center" />
          <el-table-column prop="productPrice" label="单价" align="center" />
          <el-table-column prop="actualPrice" label="实付单价" align="center" />
        </el-table>
      </div>
    </div>
  </el-dialog>
</div>
</template>

<script>
export default {
  filters: {
    statusFilter(state) {
      const statusMap = {
        1: '待付款',
        2: '待发货',
        3: '待收货',
        4: '已完成',
        5: '已取消'
      }
      return statusMap[state] || '未知'
    }
  },

  data() {
    return {
      tableData: [],
      searchForm: {
        billNo: '',
        state: '',
        account: ''
      },
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      detailDialogVisible: false,
      detailData: {
        mallOrder: {},
        mallOrderReceive: {},
        mallOrderSkus: []
      }
    }
  },

  mounted() {
    this.getList()
  },

  methods: {
    handleLogisisc(row) {


      this.$prompt('请输入发货单号(若自提请输入0即可)', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(({ value }) => {
        this.$postData("updateLogisticsNo", {billNo:row.billNo,logisticsNo:value}).then(res => {
          if (res.status === 200) {
            this.$message({
              type: 'success',
              message: '更新成功'
            });
            this.getList();
          } else {
            this.$message({
              type: 'error',
              message: '发货失败'
            });
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消输入'
        });
      });
    },
    getList() {
      const params = {
        ...this.searchForm,
        ...this.pagination
      }
      this.$postData("mallOrderList", params).then(res => {
        if (res.data) {
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        }
      })
    },

    handleSearch() {
      this.pagination.current = 1
      this.getList()
    },

    handleCurrentChange(val) {
      this.pagination.current = val
      this.getList()
    },

    async handleDetail(row) {
      const res = await this.$getData('mallOrderDetail', { orderId: row.orderId || row.id });
      if (res.data) {
        this.detailData = res.data;
        this.detailDialogVisible = true;
      }
    },

    handleCloseDetail() {
      this.detailDialogVisible = false;
      this.detailData = {
        mallOrder: {},
        mallOrderReceive: {},
        mallOrderSkus: []
      };
    }
  }
}
</script>

<style scoped>
.filter-container {
  display: flex;
  gap: 10px;
}
.app-container {
  padding: 20px;
}
.order-detail-layout {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.08);
  padding-bottom: 10px;
}
.order-detail-top-beauty {
  display: flex;
  flex-direction: row;
  gap: 32px;
  background: #f7f8fa;
  border-radius: 10px;
  padding: 32px 32px 18px 32px;
  margin-bottom: 24px;
}
.detail-block {
  background: transparent;
  border-radius: 0;
  margin-bottom: 0;
  padding: 0;
}
.left-block {
  flex: 1.2;
  min-width: 0;
}
.right-block {
  flex: 1;
  min-width: 0;
}
.detail-title {
  font-size: 20px;
  font-weight: bold;
  color: #222;
  margin-bottom: 16px;
  border-bottom: 1px solid #ececec;
  padding-bottom: 6px;
}
.detail-row {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  margin-bottom: 10px;
  font-size: 15px;
  color: #555;
  line-height: 1.7;
}
.detail-row .label {
  min-width: 90px;
  color: #999;
  font-weight: 500;
  text-align: right;
  display: inline-block;
}
.goods-block {
  background: #fff;
  border-radius: 10px;
  margin-top: 0;
  padding: 24px 24px 10px 24px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.03);
}
.goods-table ::v-deep .el-table th {
  background: #f5f6fa;
  color: #333;
  font-weight: bold;
  font-size: 15px;
}
.goods-table ::v-deep .el-table td {
  background: #fff;
  color: #444;
  font-size: 15px;
}
</style>
