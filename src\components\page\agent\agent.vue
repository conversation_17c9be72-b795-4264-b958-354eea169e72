<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="编号:">
                                <el-input
                                        v-model="model.No"
                                        placeholder="编号"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="名称:">
                                <el-input
                                        v-model="model.RealName"
                                        placeholder="名称"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item   prop="state" label="员工状态">

                                <el-select v-model="model.state" clearable placeholder="请选择">
                                    <el-option
                                            v-for="item in states"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>

                        </el-col>
                        <el-col :span="16">
                            <el-form-item   prop="time" label="入职时间">

                                <el-date-picker
                                        :unlink-panels="true"
                                        v-model="model.time"
                                        type="datetimerange"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        :default-time="['00:00:00']">
                                </el-date-picker>
                            </el-form-item>

                        </el-col>
                        <el-col :span="8" >

                            <el-form-item>
                                <el-button type="success"  round @click="query()">搜索</el-button>
                                <el-button type="info"  round @click="re()">重置</el-button>
                                <el-button type="success" round  @click="addModal=true">添加</el-button>
                            </el-form-item>

                        </el-col>
                    </el-row>

                </el-form>
            </div>

            <el-table :data="list" border
                      @sort-change="sortChange"
                      class="table" ref="multipleTable"
                      :header-cell-style="{background:'#ddd'}"
                      @cell-dblclick="edit">
                <el-table-column
                        type="selection"
                        width="35">
                </el-table-column>
                <el-table-column
                        fixed="left"
                        prop="no"
                        label="编号"
                        width="120"
                        sortable="custom"
                ></el-table-column>
                <el-table-column

                        prop="realName"
                        label="名称"
                        sortable="custom"
                        width="120"
                ></el-table-column>
                <el-table-column
                        prop="phone"
                        label="联系方式"
                        width="150"
                        sortable="custom"
                ></el-table-column>
<!--                <el-table-column-->
<!--                        prop="address"-->
<!--                        label="地址"-->
<!--                        width="220"-->
<!--                        sortable="custom"-->
<!--                ></el-table-column>-->
<!--                <el-table-column-->
<!--                        prop="cityName"-->
<!--                        label="城市"-->
<!--                        width="150"-->
<!--                        sortable="custom"-->
<!--                ></el-table-column>-->
<!--                <el-table-column-->
<!--                        prop="areaName"-->
<!--                        label="区域"-->
<!--                        width="150"-->
<!--                        sortable="custom"-->
<!--                ></el-table-column>-->
<!--                <el-table-column-->
<!--                        prop="workRemark"-->
<!--                        label="工作记录"-->
<!--                        width="150"-->
<!--                        sortable="custom"-->
<!--                ></el-table-column>-->
                <el-table-column
                        prop="entryTime"
                        label="入职时间"
                        width="180"
                        sortable="custom">
                </el-table-column>
                <el-table-column
                        prop="sites"
                        label="所属站点"
                        width="180"

                ></el-table-column>
                <el-table-column
                        prop="baomuSum"
                        label="管理保姆数"
                        width="150"

                ></el-table-column>
                <el-table-column
                        prop="communicateSum"
                        label="咨询总量"
                        width="110"

                ></el-table-column>
                <el-table-column
                        prop="orderSum"
                        label="总成交订单数"
                        width="150"

                ></el-table-column>
                <el-table-column
                        prop="orderAmount"
                        label="总成交额"
                        width="110"

                ></el-table-column>
                <el-table-column
                        prop="state"
                        label="状态"
                        width="120"
                        sortable="custom">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.state==1" type="success">上架</el-tag>
                        <el-tag v-else-if="scope.row.state==2" type="warning">下架</el-tag>
                        <el-tag v-else-if="scope.row.state==3" type="warning">离职</el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                        fixed="right"
                        label="操作"
                        min-width="340">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="edit(scope.row)" type="primary">详情</el-button>
                        <el-button size="mini" @click="editbaomu(scope.row)" type="primary">关联保姆</el-button>
                        <el-button size="mini" @click="editSite(scope.row)" type="primary">关联站点</el-button>
                        <el-button v-if="scope.row.state==1" size="mini" @click="save(scope.row,2)" type="primary">下架</el-button>
                        <el-button v-if="scope.row.state==2" size="mini" @click="save(scope.row,1)" type="info">上架</el-button>
                   </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>

        <Modal v-model="addModal" class="Modal" :width="screenWidth"   title="添加经纪人" :mask-closable="false" >
            <div class="addBody">
                <agentAdd v-if="addModal" @init-choose="initChooseProject"
                          @close-modal="closeCurrModal" ></agentAdd>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="saveModal" class="Modal" :width="screenWidth"   title="经纪人详情" :mask-closable="false" >
            <div class="addBody" style="min-height: 400px;">
                <agentUpdate v-if="saveModal" @init-choose="initChooseProject"
                                @close-modal="closeCurrModal" :model="show"></agentUpdate>
            </div>
            <div slot="footer">
            </div>
        </Modal>

        <Modal v-model="siteModal" class="Modal" width="600px"   title="关联站点" :mask-closable="false" >
            <div class="addBody" style="min-height: 400px;">
                <siteModal v-if="siteModal" @init-choose="initChooseProject"
                          @close-modal="closeCurrModal" :model="show"></siteModal>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="baomuModal" class="Modal" width="600px"   title="关联保姆" :mask-closable="false" >
            <div class="addBody" style="min-height: 400px;">
                <baomuModal v-if="baomuModal" @init-choose="initChooseProject"
                           @close-modal="closeCurrModal" :model="show"></baomuModal>
            </div>
            <div slot="footer">
            </div>
        </Modal>




    </div>
</template>

<script>
    import siteModal from '@/components/page/agent/minichoose/siteModal.vue'
    import baomuModal from '@/components/page/agent/minichoose/baomuModal.vue'
    import agentUpdate from '@/components/page/agent/choose/agentUpdate.vue'
    import agentAdd from '@/components/page/agent/choose/agentAdd.vue'
    import {formatDate} from '@/components/common/utils.js'
    export default {

        data() {
            return {
                states:[{
                    value:null,
                    label: '全部'
                },{
                    value:1,
                    label: '上架'
                }, {
                    value: 2,
                    label: '下架'
                }, {
                    value: 3,
                    label: '离职'
                },],
                tableData: [],
                pageSizeOpts:[10,20,30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                screenWidth: '80%',//新增对话框 宽度
                saveModal: false,
                addModal:false,
                siteModal:false,
                baomuModal:false,
                show:{},
                model: {
                    storeId:Number(localStorage.getItem("storeId")) ,
                    time:null,
                    startTime:null,
                    endTime:null,
                    No: null,
                    RealName:null,
                    current: 1,
                    size: 10,
                    state:null,

                },
                update:{
                    id:null,
                    realName:null,

                },
                list:null,

            };
        },
        components: {
            'agentUpdate': agentUpdate,
            'agentAdd':agentAdd,
            'siteModal':siteModal,
            'baomuModal':baomuModal

        },
        created() {

            this.getData();
        },
        computed: {
        },
        methods: {

            save(dom,state) {
                dom.state=state;
                this.$postData("update_employee", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.$Message.success('修改成功');
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })

            },
            sortChange: function(column, prop, order) {

                if (column == null) {
                    this.model.orderBy = "";
                } else {
                    if (column.order == "ascending") {
                        this.model.orderBy = column.prop + " ASC";
                    }
                    if (column.order == "descending") {
                        this.model.orderBy = column.prop + " DESC";
                    }
                }
                this.getData();
            },
            getData() {
                this.$postData("agent_page", this.model, {}).then(res => {
                    if (res.status == 200) {

                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            dblhandleCurrentChange(row, column, cell, event){
                console.log(row)
            },
            re(){
                this.model.No='';
                this.model.RealName='';
                this.model.state=null;
                this.model.time=null;
                this.getData();
            },
            query() {
                if (this.model.time!=null){
                    this.model.startTime=this.model.time[0]
                    this.model.endTime=this.model.time[1]
                }

                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.model.size = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                console.log(index)
                this.model.current = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
                this.addModal=false;
                this.siteModal=false;
                this.baomuModal=false;
            },
            initChooseProject(data) {
                this.getData()
                this.closeCurrModal();

            },
            edit(id){
                this.show=id;
                this.saveModal=true;
            },
            editSite(id){
                this.show=id;
                this.siteModal=true;
            },
            editbaomu(id){
                this.show=id;
                this.baomuModal=true;
            },
            add(){

                this.addModal=true;
            }
        }
    };
</script>

<style scoped>
    .el-tabs__item is-top is-active {
        color: #ffffff;
        background: #409eff;
    }
    .startWord{
        background-color: #F5F7FA;
        color: #909399;
        vertical-align: middle;
        position: relative;
        border: 1px solid #DCDFE6;
        border-radius: 4px;
        padding: 0 20px;
        width: 1px;
        white-space: nowrap;
        float: left;
        padding-right: 50px;
    }
    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
