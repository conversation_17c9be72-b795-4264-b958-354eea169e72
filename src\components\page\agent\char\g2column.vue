<template>

        <div :id="id"  :style="'height: '+heightSize+'px'" v-if="charData.length" v-show="show">
        </div>

</template>

<script>
    import { Chart, Util } from '@antv/g2';
    export default {
        props:{
                setCharData:{
                        type: Array,
                        default: () => []
                },
                heightSize: {
                        type: Number,
                        default: 200
                },
                showLine: {
                        type: Boolean,
                        default: false
                },
                showColumn: {
                        type: Boolean,
                        default: true
                },
                showArea:{
                        type: Boolean,
                        default: false
                },
                showPoint:{
                        type: <PERSON>olean,
                        default: true
                }
        },
        name: "g2column",
        watch:{
            option(){
                this.$nextTick(() => {
                    this.changeChar()
                })
            }
        },
        data() {
            return {
                show:true,
                chart:null,
                id:"id"+Math.ceil(Math.random()*1000),
                sum:100,
                charData : [
                    { response: '加载中', num: 100},
                ]
            }
        },
        created(){
            let self=this
            setTimeout(function (){
                self.sum=0
                self.show=false
                self.charData=self.setCharData
                self.charData.forEach(v=>{
                    self.sum=v.num+self.sum
                })
                self.changeChar()
                self.show=true

            }, 1000);

        },
        mounted(){
            this.inintChar(this.charData)
        },
        methods:{
            changeChar(){
                    this.charData.forEach(v=>{
                            if (v.type==undefined){
                                    v.type='主要'
                            }
                    })
                this.chart.changeData(this.charData)
                this.chart.render();
            },
            inintChar(charData){
                    this.chart = new Chart({
                            container: this.id,
                            autoFit: true,
                            height: 300,
                            padding: [50, 50, 50, 50],
                    });

              this.chart.legend({
                position: 'bottom',
                custom: true,
                items: [
                ]
              });

                    this.chart.data(charData);
                    this.chart.scale('num', {
                            alias: '需求量',
                    });
                    this.chart.scale('putnum', {
                alias: '上架数',
                  });
              this.chart.scale('downnum', {
                alias: '下架数',
              });

                    this.chart.axis('response', {
                            tickLine: {
                                    alignTick: true,
                            },
                    });
                    this.chart.axis('num',  {
                            tickLine: null
                    });

                    this.chart.tooltip({
                            showMarkers: false
                    });
                    if (this.showColumn){
                            this.chart.interval().position('response*num').color('type', ['#ff7345']);
                      this.chart.interval().position('response*putnum').color('type', ['#a4b81b']).style({
                        opacity: 0.5,
                      });
                      this.chart.interval().position('response*downnum').color('type', ['#ffc40d']).style({
                        opacity: 0.5,
                      });
                    }

                    this.chart.interaction('active-region');


                    // 添加文本标注
                    this.setCharData.forEach((item,index) => {
                            let a=0
                            if (this.setCharData.length>40){
                                    return
                            }
                            if (index> 0 && this.setCharData[(index-1)].num>0){
                                    a=(this.setCharData[index].num-this.setCharData[(index-1)].num)/this.setCharData[(index-1)].num
                            }
                            this.chart.annotation().text({
                                            position: [item.response, item.num],
                                            content: '注册数：'+item.num,
                                            style: {
                                                    textAlign: 'right',
                                            },
                                            offsetY:-15,
                                    })
                      this.chart.annotation().text({
                        position: [item.response, item.putnum],
                        content: '上架数：'+item.putnum,
                        style: {
                          textAlign: 'left',
                        },
                        offsetY: -15,
                      })

                      this.chart.annotation().text({
                        position: [item.response, item.downnum],
                        content: '下架数：'+item.downnum,
                        style: {
                          textAlign: 'center',
                        },
                        offsetY: -15,
                      })
                    });


                    if (this.showLine){
                        this.chart.line().position('response*num').color('type');


                    }
                    if (this.showArea){
                            this.chart.area().position('response*num').color('type');
                    }
                    if (this.showPoint){
                            this.chart.point().position('response*num').color('type');
                    }

              this.chart.axis('response', {
                tickLine: {
                  alignTick: true,
                },
                label: {
                  autoRotate: true, // 自动旋转 X 轴文字
                  offset: 6, // 调整文字与轴线的距离
                },
              });
                   // this.chart.render();
            }
        }
    }
</script>

<style scoped>

</style>
