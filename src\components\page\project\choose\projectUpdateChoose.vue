<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col span="24">
                    <div>
                        <div id="operBtnDiv">
                            <div id="searchDiv">
                                <Form ref="formItem" :model="formItem" :label-width="80" label-position="right"
                                      :rules="ruleValidate">
                                    <Row>
                                        <Col :span="12">
                                            <FormItem label="项目编号" prop="projectNo">
                                                <Input placeholder="请输入项目编号" v-model="formItem.projectNo"
                                                       style="width: 200px">
                                                </Input>
                                            </FormItem>
                                        </Col>
                                        <Col :span="12">
                                            <FormItem label="项目名称" prop="name">
                                                <Input placeholder="请输入项目名称" v-model="formItem.name" style="width: 200px">
                                                </Input>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col :span="12">
                                            <FormItem label="负责人" prop="employeeId">
                                                <Select v-model="formItem.employeeId" filterable style="width: 200px">
                                                    <Option v-for="item in employees" :value="item.id">{{ item.realName}}</Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                        <Col :span="12">
                                            <FormItem label="项目资金" prop="capital">
                                                <Input placeholder="请输入项目资金" v-model="formItem.capital" style="width: 200px">
                                                </Input>
                                            </FormItem>
                                        </Col>
                                    </Row>

                                    <Row>
                                        <Col :span="12">

                                            <FormItem label="开始时间" prop="cycleStartTime">
                                                <FormItem prop="date">
                                                    <DatePicker type="date" placeholder="开始时间" style="width: 200px"
                                                                v-model="formItem.cycleStartTime"></DatePicker>
                                                </FormItem>
                                            </FormItem>
                                        </Col>
                                        <Col :span="12">

                                            <FormItem label="结束时间" prop="cycleEndTime">
                                                <FormItem prop="date">
                                                    <DatePicker type="date" placeholder="开始时间" style="width: 200px"
                                                                v-model="formItem.cycleEndTime"></DatePicker>
                                                </FormItem>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col :span="12">
                                            <FormItem label="项目性质" prop="natureType">
                                                <Select v-model="formItem.natureType" filterable style="width: 200px">
                                                    <Option v-for="item in natureList" :value="item.id">{{ item.name}}</Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                        <Col :span="12">
                                            <FormItem label="收入类型" prop="incomeType">
                                                <Select v-model="formItem.incomeType" filterable style="width: 200px">
                                                    <Option v-for="item in incomeList" :value="item.id">{{ item.name}}</Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                    </Row>

                                    <Row>
                                        <Col :span="12">
                                            <FormItem label="扣减类型" prop="reduces">
                                                <Select v-model="formItem.reduces" filterable style="width: 200px"
                                                        multiple>
                                                    <Option v-for="item in reducesList" :value="item.id">{{item.name}}</Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                        <Col :span="12">
                                            <FormItem label="结算类型" prop="cycleType">
                                                <Select v-model="formItem.cycleType" filterable style="width: 200px">
                                                    <Option v-for="item in cycleList" :value="item.id">{{ item.name}}</Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                    </Row>

                                    <Row>
                                        <Col  :span="12">

                                            <FormItem label="产品类别">
                                                <Select v-model="formItem.productCategoryId" multiple filterable
                                                        style="width: 200px">
                                                    <Option :value="0">全部</Option>
                                                    <Option v-for="item in productList" :value="item.id">{{ item.name}}</Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                        <Col :span="12">
                                            <FormItem label="立项时间" prop="createTime">
                                                <DatePicker type="date" placeholder="请选择立项时间"
                                                            format="yyyy-MM-dd HH:mm:ss"
                                                            v-model="formItem.createTime"
                                                            style="width: 200px"></DatePicker>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                    <FormItem label="所属门店">
                                        <Select v-model="formItem.stores" multiple filterable
                                                style="width: 200px">
                                            <Option :value="0">全部</Option>
                                            <Option v-for="item in storeList" :value="item.id">{{ item.storeName}}</Option>
                                        </Select>
                                    </FormItem>

                                    <FormItem>
                                        <Button type="primary" @click="saveProject('formItem')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </FormItem>

                                </Form>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
        <!--  <Modal v-model="userModal" class="Modal" :width="screenWidth" title="负责人" :mask-closable="false">
              <div class="addBody">

                  <employee-choose v-if="userModal" @init-choose="initChooseProject"
                                   @close-modal="closeCurrModal"></employee-choose>
              </div>
              <div slot="footer">
                  <el-button @click="closeCurrModal">取消</el-button>
                  <el-button type="primary" :loading="userLoading" @click="closeCurrModal">确定</el-button>
              </div>
          </Modal>-->
    </div>
</template>


<script>
    import employeeChoose from '@/components/page/project/choose/employeeChoose.vue'

    export default {
        props: ['projectId'],
        data() {
            return {
                userModal: false,
                userLoading: false,
                screenWidth: '30%',
                formItem: {
                    id:this.projectId,
                    projectNo: null,
                    employeeId: null,   //负责人id
                    name: null,    //项目名称
                    cycleStartTime: null,   //周期开始时间
                    cycleEndTime: null,   //周期结束时间
                    employeeName: null,
                    createTime:null,
                    capital:null,
                    natureType: null,  //性质
                    cycleType: null, //周期类型
                    reduces: [],  // 扣减类型
                    incomeType: null, //收入类型
                    productCategoryId: [],
                    stores:[],
                },
                employees: null,
                natureList: [],  //性质
                cycleList: [], //周期类型
                reducesList: [],  // 扣减类型
                incomeList: [], //收入类型
                productList: null,//产品类别
                storeList:null,
                ruleValidate: {
                    projectNo: [
                        {required: true, message: '项目编号错误请联系管理员', trigger: 'blur'}
                    ],
                    name: [
                        {required: true, message: '请输入项目名称', trigger: 'blur'},
                    ],
                    capital: [
                        {required: true, message: '请输入项目资金', trigger: 'blur'},
                        {required: true, pattern: /(^[\-0-9][0-9]*(.[0-9]+)?)$/, message: '请输入数字', trigger: 'change'}
                    ],
                    employeeId: [
                        {required: true, message: '请选择负责人', trigger: 'change', type: "number"}
                    ],
                    natureType: [
                        {required: true, message: '请选择项目性质', trigger: 'change', type: "number"}
                    ],
                    cycleType: [
                        {required: true, message: '请选择结算类型', trigger: 'change', type: "number"}
                    ],
                    incomeType: [
                        {required: true, message: '请选择收入类型', trigger: 'change', type: "number"}
                    ],
                    reduces: [
                        {required: true, message: '请选择扣减类型', trigger: 'change', type: "array"}
                    ],
                    productCategoryId: [
                        {required: true, message: '请选择产品类别', trigger: 'change', type: "array"}
                    ],
                    createTime: [
                        {required: true, message: '请选择立项时间', trigger: 'change', type: "date"}
                    ],
                    cycleStartTime: [
                        {
                            required: true, trigger: 'change',
                            type: 'date',

                        }
                    ],
                    cycleEndTime: [
                        {
                            required: true, trigger: 'change',
                            type: 'date',

                        }
                    ],
                }
            }
        },
        components: {
            'employeeChoose': employeeChoose
        },
        created: function () {
            this.getData();
            this.getByEmployees();
            this.getDictionaryList();
            this.getProduct();
            this.getStore();
        },
        methods: {
            getProduct() {
                this.$postData("category_queryList", {}, {}).then(res => {
                    if (res.status == 200) {

                        this.productList = res.data;
                    }
                });
            },
            getStore() {
                this.$postData("store_getByList", {}, {}).then(res => {
                    if (res.status == 200) {

                        this.storeList = res.data;
                    }
                });
            },
            getDictionaryList() {
                this.$postData("dictionary_getList", {}, {}).then(res => {
                    if (res.status == 200) {
                        for (let i = 0; i < res.data.length; i++) {
                            let item = res.data[i];
                            if (item.type == 3) {
                                this.natureList.push(item)
                            } else if (item.type == 4) {
                                this.incomeList.push(item)
                            } else if (item.type == 5) {
                                this.reducesList.push(item)
                            } else if (item.type == 6) {
                                this.cycleList.push(item)
                            }
                        }
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getData(){
                this.$getData("project_getById", {id: this.projectId}).then(res => {
                    if (res.status == 200) {
                       ;
                        this.formItem=res.data
                    /*    this.formItem.projectNo = res.data.projectNo;
                        this.formItem.name = res.data.name
                        this.formItem.capital=res.data.capital
                        this.formItem.employeeId = res.data.employeeId;
                        this.formItem.employeeName = res.data.employee.realName
                        this.formItem.cycleStartTime = res.data.CycleStartTime
                        this.formItem.cycleEndTime = res.data.CycleEndTime*/
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getByEmployees() {
                this.$postData("employee_getByList", {}, {}).then(res => {
                    if (res.status == 200) {
                       ;
                        this.employees = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.userModal = false;
            },

            saveProject(name) {
                this.formItem.capital=this.formItem.capital+"";
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        this.$postData("project_update", this.formItem, {}).then(res => {
                            if (res.status == 200) {
                                this.$Message.success('修改成功');
                                this.chooseThisModel();
                            } else {
                                this.$message.error("修改失败，" + res.msg);
                            }
                        })
                    }
                })
            },
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            }
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

