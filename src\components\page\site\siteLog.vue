<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
            </div>
            <el-table :data="list" class="table" ref="multipleTable"
                      :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}"
                      v-loading="loading">
                <el-table-column
                        width="200"
                        prop="createTime"
                        label="操作时间">
                </el-table-column>
                <el-table-column
                        width="130"
                        prop="operatorName"
                        label="操作人"
                ></el-table-column>
                <el-table-column
                        width="170"
                        prop="title"
                        label="操作类型"
                ></el-table-column>
                <el-table-column
                        width="600"
                        prop="message"
                        label="操作内容"
                ></el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
    export default {
        props: ['projectId'],
        data() {
            return {
                loading: true,
                list: null,
            };
        },
        components: {},
        watch: {
            projectId: {
                handler(newValue, oldValue) {
                    this.projectId = newValue;
                    this.getData();
                }
            }
        },
        created() {
            this.getData();
        },
        computed: {},
        methods: {
            getData() {
                this.loading = true;
                this.$postUrl("siteLog_getByProjectId", this.projectId).then(res => {
                    this.loading = false;
                    if (res.status === 200) {
                       ;
                        this.list = res.data;
                    }
                });
            },
        }
    };
</script>

<style scoped>
    .handle-box {
        /*margin-bottom: 10px;*/
        font-weight: bold !important;
    }

    .el-form-item__label {
        font-weight: bold !important;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;

    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }

    .el-date-editor .el-range-separator {
        padding: 0 5px;
        line-height: 32px;
        width: 7%;
        color: #303133;
    }

    .container {
        padding: 0px !important;
        background: #fff;
        border: none !important;
        border-radius: 5px;
    }
</style>
