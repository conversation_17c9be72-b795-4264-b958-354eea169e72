<template>
    <div>
        <el-drawer title="新增" :visible.sync="drawer"
                direction="ttb" size="90%" :before-close="chooseThisModel">
            <Row>
                <Col span="16">
                    <Form :model="formItem" :label-width="80" ref="formItem"
                          label-position="left" :rules="ruleValidate">
                        <div class="searchDiv">
                            <FormItem label="合同名称" prop="name">
                                <Input placeholder="合同名称"
                                       size="large"
                                       v-model="formItem.name">
                                </Input>
                            </FormItem>
                            <FormItem label="总订单号" prop="no">
                                <Row>
                                    <Col span="15">
                                        <Input placeholder="总订单号"
                                               size="large"
                                               v-model="formItem.no">
                                        </Input>
                                    </Col>
                                    <Col span="3" offset="1">
                                        <Button type="primary" @click="getList">绑定子单/员工</Button>
                                    </Col>
                                </Row>

                            </FormItem>
                            <FormItem label="基本工资" prop="basePay">
                                <Input placeholder="基本工资"
                                       size="large"
                                       v-model="formItem.basePay">
                                </Input>
                            </FormItem>
                            <FormItem label="合同金额" prop="contractAmount">
                                <Input size="large" v-model="formItem.contractAmount"
                                       placeholder="合同金额"/>
                            </FormItem>
                            <FormItem label="经理" prop="managerName">
                                <Input size="large" v-model="formItem.managerName"
                                       @on-focus="isService=true"
                                       placeholder="经理"/>
                            </FormItem>
                            <FormItem label="签订时间" prop="times">
                                <el-date-picker
                                        :picker-options="pickerOptions"
                                        v-model="formItem.times"
                                        type="datetimerange"
                                        format="yyyy-MM-dd HH:mm:ss"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期">
                                </el-date-picker>
                            </FormItem>
                            <FormItem label="订单支付">
                              <el-radio v-model="formItem.balancePayment" label="true">一次性支付完成</el-radio>
                              <el-radio v-model="formItem.balancePayment" label="false">未支付</el-radio>
                            </FormItem>
                            <FormItem label="备注">
                                <Input size="large" v-model="formItem.remarks"
                                       type="textarea" :rows="3"
                                       placeholder="备注"/>
                            </FormItem>
                            <FormItem>
                                <Button style="margin-right: 8px" @click="chooseThisModel">取消</Button>
                                <Button type="primary" @click="insertByNo('formItem')">确定</Button>
                            </FormItem>
                        </div>
                    </Form>
                </Col>
                <Col span="7">
                    <div>
                        <el-card class="box-card">
                            <div slot="header" class="clearfix">
                                <span>子单列表({{billNos.length}})</span>
                                <!--<el-button style="float: right; padding: 3px 0" type="text">绑定</el-button>-->
                            </div>
                            <div class="item">
                                <div v-for="item in billNos" style="padding: 2px">
                                    {{item}}
                                </div>
                            </div>
                        </el-card>
                        <el-card class="box-card">
                            <div slot="header" class="clearfix">
                                <span>员工列表({{employees.length}})</span>
                                <!--<el-button style="float: right; padding: 3px 0" type="text">绑定</el-button>-->
                            </div>
                            <div class="item">
                                <div v-for="item in employees" style="padding: 2px">
                                    {{item.no}}{{item.realName}}
                                </div>
                            </div>
                        </el-card>
                    </div>

                </Col>
            </Row>
            <Modal v-model="isService" class="Modal" :width="width" :z-index="9999"
                   title="行政人员"
                   :mask-closable="false">
                <div class="addBody">
                    <employee-choose v-if="isService" @init-choose="initChooseProject"
                                     @close-modal="closeCurrModal"></employee-choose>
                </div>
                <div slot="footer">
                </div>
            </Modal>
        </el-drawer>
    </div>
</template>
<script>
    import employeeChoose from '@/components/page/site/employeeChoose.vue'

    export default {
        data() {
            return {
                loading: null,
                pickerOptions: {
                    onPick: obj => {
                        this.pickerMinDate = new Date(obj.minDate).getTime();
                    },
                    disabledDate: time => {
                        if (this.pickerMinDate) {
                            const day1 = 366 * 10 * 24 * 3600 * 1000;
                            let maxTime = this.pickerMinDate + day1;
                            let minTime = this.pickerMinDate - day1;
                            return time.getTime() > maxTime || time.getTime() < minTime;
                        }
                    }
                },
                billNos: [],
                employees: [],
                isService: false,
                width: "32%",
                ruleValidate: {
                    managerName: [
                        {required: true, message: '请选择经理 ', trigger: 'change'}
                    ],
                    name: [
                        {required: true, message: '请填写名称', trigger: 'blur'}
                    ],
                    contractAmount: [
                        {required: true, message: '请输入合同金额', trigger: 'blur'},
                        {required: true, pattern: /(^[\-0-9][0-9]*(.[0-9]+)?)$/, message: '请输入数字', trigger: 'change'}
                    ],
                    basePay: [
                        {required: true, message: '请输入基本工资', trigger: 'blur'},
                        {required: true, pattern: /(^[\-0-9][0-9]*(.[0-9]+)?)$/, message: '请输入数字', trigger: 'change'}
                    ],
                    no: [
                        {required: true, message: '请输入总订单号 ', trigger: 'blur'},
                        {required: true, pattern: /(^[\-0-9][0-9]*([0-9]+)?)$/, message: '请输入数字', trigger: 'change'}
                    ],
                    times: [
                        {required: true, message: '请选择签订时间', trigger: 'blur', type: "array"}
                    ]
                },
                drawer: true,
                formItem: {
                    times: null,
                    name: null,
                    no: null,
                    manager: 13713,
                    managerName: "杨松芝547",
                    contractAmount: null,
                    signTime: null,
                    basePay: null,
                    expireTime: null,
                    remarks: null,
                    billNos: [],
                    employees: [],
                    balancePayment:'false',
                },
            }
        },
        created: function () {
        },
        components: {
            'employeeChoose': employeeChoose,
        },
        methods: {
            getList() {
                if (!this.formItem.no) {
                    this.$message.error("输入总订单");
                    return
                }
                this.getByNo();
                this.getByEmployee()
            },
            getByEmployee() {
                this.$postData("siteProject_getByEmployee", {
                    no: this.formItem.no
                }).then(res => {
                    if (res.status === 200) {
                        this.employees = res.data;
                    }
                })
            },
            getByNo() {
                this.$postData("siteProject_getByNo", {
                    no: this.formItem.no
                }).then(res => {
                    if (res.status === 200) {
                        this.billNos = res.data;
                    }
                })
            },
            insertByNo(name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        this.loading = this.$loading({
                            lock: true,
                            text: '拼命加载中',
                            spinner: 'el-icon-loading',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });
                        this.formItem.signTime = this.formItem.times[0];
                        this.formItem.expireTime = this.formItem.times[1];
                        this.formItem.billNos = this.billNos;
                        this.formItem.employees = this.employees;
                        this.$postData("siteProject_insert", this.formItem).then(res => {
                            this.loading.close();
                            if (res.status === 200) {
                                this.$message({
                                    type: 'success',
                                    message: '保存成功!'
                                });
                                this.chooseThisModel()
                            } else {
                                this.$message.error("保存失败，" + res.msg);
                            }
                        })
                    }
                })
            },
            initChooseProject(data) {
                this.formItem.manager = data.id;
                this.formItem.managerName = data.realName + data.no;
                this.closeCurrModal();
            },
            closeCurrModal(data) {
                this.isService = false;
            },
            getById() {
                this.$getData("salarySetUp_getById", {
                    id: 1
                }).then(res => {
                    if (res.status === 200) {
                        this.formItem = res.data;
                    }
                })
            },
            update() {
                this.$postData("salarySetUp_update", this.formItem).then(res => {
                        if (res.status === 200) {
                            this.$message({
                                type: 'success',
                                message: '修改成功!'
                            });
                            this.chooseThisModel()
                        } else {
                            this.$message.error("保存失败，" + res.msg);
                        }
                    }
                )
            },
            chooseThisModel() {
                this.drawer = false;
                this.$emit('init-choose', "");
            },
        }
    }
</script>
<style>


    .demo-drawer-footer {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        border-top: 1px solid #e8e8e8;
        padding: 10px 16px;
        text-align: left;
        background: #fff;
    }

    .searchDiv {
        width: 70%;
        margin: 20px 200px;
        font-weight: bold;
        font-size: 17px !important;
    }

    .box-card {
        width: 250px;
        margin: 15px 0 30px;
    }

    .item {
        /*margin-bottom: 10px;*/
        height: 150px;
        overflow-x: hidden;
    }

    .el-card__body {
        padding: 10px 20px;
    }

</style>