<template>
	<div class="container">
		<divider></divider>
		<div class="handle-box">
			<el-form ref="form" :model="pageInfo">
				<el-row>
					<el-col :span="6">
						<el-form-item label="搜索关键词">
							<el-input v-model="quer.search" placeholder="工号、姓名、手机号、认证标题和内容等"></el-input>
						</el-form-item>
					</el-col>
					<!-- 					<el-col :span="3">
						<el-form-item label="姓名">
							<el-input v-model="quer.realName" placeholder="请输入姓名"></el-input>
						</el-form-item>
					</el-col> -->
					<el-col :span="3">
						<el-form-item label="状态">
							<el-select v-model="searchState">
								<el-option label="" value=""></el-option>
								<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
									:value="item.value"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="3">
						<el-form-item label="认证等级">
							<el-select v-model="searchLevel">
								<el-option label="" value=""></el-option>
								<el-option v-for="(item,index) in levelList" :key="index" :label="item.text"
									:value="item.value"></el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :span="6">
						<el-button type="primary" @click="query()" icon="el-icon-search" style="margin-left: 20px">搜索
						</el-button>
						<el-button icon="el-icon-refresh" @click="reset()" style="margin-left: 20px">重置</el-button>
						<!-- 						<el-button type="primary" icon="el-icon-edit" @click="createInfo()" style="margin-left: 20px">创建
						</el-button> -->
					</el-col>
				</el-row>

			</el-form>
		</div>
		<el-table :data="list" height="500px" v-loading="loading" border
			:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">


			<el-table-column prop="realName" width="80" label="姓名">
				<template slot-scope="scope">
					<span @click="rowClick(scope.row.baomuId)">{{scope.row.realName}}</span>
				</template>
			</el-table-column>

			<el-table-column width="100" prop="no" label="工号">
				<template slot-scope="scope">
					<span @click="rowClick(scope.row.baomuId)">{{scope.row.no}}</span>
				</template>
			</el-table-column>

			<el-table-column width="140" prop="creTime" label="创建时间">
				<template slot-scope="scope">
					<span @click="rowClick(scope.row.baomuId)">{{scope.row.creTime}}</span>
				</template>
			</el-table-column>
			<el-table-column width="140" prop="updateTime" label="认证时间">
				<template slot-scope="scope">
					<el-tooltip class="item" effect="dark" :content="'认证人ID：'+scope.row.authEmployeeId" placement="top"
						v-if="scope.row.authEmployeeId!==null&&scope.row.authEmployeeId!==''">
						<span @click="rowClick(scope.row.baomuId)">{{scope.row.updateTime}}</span>
					</el-tooltip>
					<el-tooltip class="item" effect="dark" content="暂未认证" placement="top"
						v-if="scope.row.authEmployeeId==null||scope.row.authEmployeeId==''">
						<span @click="rowClick(scope.row.baomuId)">{{scope.row.updateTime}}</span>
					</el-tooltip>
				</template>
			</el-table-column>

			<el-table-column width="80" label="状态">
				<template slot-scope="scope">
					<!-- 					<el-tag v-for="(item,index) in stateList" :key="index" v-show="scope.row.state == item.value"
						:type="item.type">{{item.text}}</el-tag> -->
					<el-tag :type="formatState(0,scope.row.state)" @click="rowClick(scope.row.baomuId)">
						{{formatState(1,scope.row.state)}}
					</el-tag>
				</template>
			</el-table-column>

			<el-table-column width="125" prop="headPortrait" label="头像">
				<template slot-scope="scope">
					<img :src="scope.row.headPortrait" style="width: 100px;height: 100px;"
						@click="rowClick(scope.row.baomuId)">
				</template>
			</el-table-column>

			<el-table-column prop="authName" label="认证标题" width="100">
				<template slot-scope="scope">
					<span @click="rowClick(scope.row.baomuId)">{{scope.row.authName}}</span>
				</template>
			</el-table-column>

			<el-table-column prop="authContent" label="认证内容" width="180">
				<template slot-scope="scope">
					<span @click="rowClick(scope.row.baomuId)">{{scope.row.authContent}}</span>
				</template>
			</el-table-column>

			<el-table-column width="200" label="认证等级">
				<template slot-scope="scope">
					<!-- 					<el-tag v-for="(item,index) in levelList" :key="index"
					v-if="scope.row.level == item.text" :type="item.type">{{item.text}}</el-tag>
					<el-tag v-for="(item,index) in levelList" :key="index"
					v-if="scope.row.level == item.value" :type="item.type">{{item.text}}</el-tag> -->

					<el-tag v-if="scope.row.level == '未认证'" type="info">未认证</el-tag>
					<el-tag v-if="scope.row.level == '不通过'" type="info">不通过</el-tag>
					<el-tag v-if="scope.row.level == '普通'" type="danger"> 普通</el-tag>
					<el-tag v-if="scope.row.level == '金牌'" type="warning">金牌</el-tag>
					<el-tag v-if="scope.row.level == '翡翠'" type="success">翡翠</el-tag>
					<el-tag v-if="scope.row.level == '钻石'" type="">钻石</el-tag>

					<el-tag v-if="scope.row.level == 0" type="info">未认证</el-tag>
					<el-tag v-if="scope.row.level == 1" type="info">不通过</el-tag>
					<el-tag v-if="scope.row.level == 2" type="danger"> 普通</el-tag>
					<el-tag v-if="scope.row.level == 3" type="warning">金牌</el-tag>
					<el-tag v-if="scope.row.level == 4" type="success">翡翠</el-tag>
					<el-tag v-if="scope.row.level == 5">钻石</el-tag>

					<el-select v-model="scope.row.level" placeholder="请选择" style="margin-left: 20px;width: 90px;">
						<el-option v-for="(item,index) in levelList" :key="index" :label="item.text"
							:value="item.value"></el-option>
					</el-select>
				</template>

			</el-table-column>
			<el-table-column prop="authSalary" label="薪资评定" width="100">
				<template slot-scope="scope">
					<el-input v-model="scope.row.authSalary" style="width: 70px;"></el-input>
				</template>

			</el-table-column>

			<el-table-column fixed="right" label="操作">
				<template slot-scope="scope">
					<el-button @click="updateAuth(scope.row)" type="primary" size="small">更新评定
					</el-button>
					<!-- 					<el-button @click="updateInfo(scope.row)" type="primary" size="small">修改资料
					</el-button> -->
				</template>
			</el-table-column>

		</el-table>
		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="this.dto.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="10" />
		</div>


		<!--员工详细信息-->
		<el-dialog :visible.sync="detailModal" width="60%" title="员工详细信息" :mask-closable="false">
			<div class="container" style="height: 500px;overflow: hidden;overflow-y: scroll;">
				<el-row style="width:100%;height: auto;margin-bottom: 80px;">
					<h2 class="detail-title">基本信息</h2>
					<el-col :span="12">
						<el-form :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="工号：">
								<el-input v-model="employee.no" placeholder="工号" class="handle-input mr10" disabled>
								</el-input>
							</el-form-item>
							<el-form-item label="姓名：">
								<el-input v-model="employee.realName" placeholder="姓名" class="handle-input mr10"
									disabled>
								</el-input>
							</el-form-item>
							<el-form-item label="入职时间：">
								<el-input v-model="employee.entryTime" placeholder="入职时间" class="handle-input mr10"
									disabled>
								</el-input>
							</el-form-item>
							<el-form-item label="头像：">
								<img :src="employee.headPortrait" class="avatar">
							</el-form-item>
						</el-form>
					</el-col>
					<el-col :span="12">
						<el-form :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="地址：">
								<el-input v-model="employee.address" class="handle-input mr10" disabled>
								</el-input>
							</el-form-item>
							<el-form-item label="手机：">
								<el-input v-model="employee.phone" class="handle-input mr10" disabled>
								</el-input>
							</el-form-item>
							<el-form-item label="出生年月：">
								<el-input v-model="employeeInfo.birthTime" class="handle-input mr10" disabled>
								</el-input>
							</el-form-item>
							<el-form-item label="身份证：">
								<el-input v-model="employeeInfo.idcard" class="handle-input mr10" disabled>
								</el-input>
							</el-form-item>
							<el-form-item label="介绍人ID：">
								<el-input v-model="employee.introducer" class="handle-input mr10" disabled>
								</el-input>
							</el-form-item>
							<el-form-item label="备注：">
								<el-input v-model="employee.remark" class="handle-input mr10" disabled>
								</el-input>
							</el-form-item>
							<el-form-item label="个人简历：">
								<el-button type="primary" @click="resume()" icon="el-icon-search">扫码查看</el-button>
							</el-form-item>
						</el-form>
					</el-col>

				</el-row>
				<el-row style="width:100%;height: auto;margin-bottom: 80px;">
					<h2 class="detail-title">求职意向</h2>

					<el-col :span="12">
						<el-form :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="个人简介：">
								<el-input v-model="baomuInfo.introduce" class="big-input mr10" disabled type="textarea"
									autosize>
								</el-input>
							</el-form-item>
							<el-form-item label="期望工作：">
								<el-input v-model="baomuInfo.workType" class="big-input mr10" disabled type="textarea"
									autosize>
								</el-input>
							</el-form-item>
							<el-form-item label="工作内容：">
								<el-input v-model="baomuInfo.serverContent" class="big-input mr10" disabled
									type="textarea" autosize>
								</el-input>
							</el-form-item>
							<el-form-item label="其他技能：">
								<el-input v-model="baomuInfo.otherSkills" class="handle-input mr10" disabled>
								</el-input>
							</el-form-item>
						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="工作类型：">
								<el-input v-model="baomuExpectedWork.workModel" class="big-input mr10" disabled
									type="textarea" autosize>
								</el-input>
							</el-form-item>
							<el-form-item label="主要技能：">
								<el-input v-model="baomuExpectedWork.otherSkills" class="big-input mr10" disabled
									type="textarea" autosize>
								</el-input>
							</el-form-item>
							<el-form-item label="最低月薪：">
								<el-input v-model="baomuExpectedWork.salaryExpectation" class="big-input mr10" disabled
									type="textarea" autosize>
								</el-input>
							</el-form-item>
						</el-form>
					</el-col>

				</el-row>

				<el-row style="width:100%;height: auto;margin-bottom: 80px;">
					<h2 class="detail-title">工作经历</h2>

					<el-col :span="24">
						<el-table :data="baomuWorkExperience" height="400px" v-loading="loading" border
							:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" stripe>
							<el-table-column prop="startWorkTime" label="工作类型" width="150">
								<template slot-scope="scope">
									<span>{{formatWorkType(scope.row.workType)}}</span>
								</template>
							</el-table-column>
							<el-table-column prop="startWorkTime" label="开始时间" width="150">
							</el-table-column>
							<el-table-column prop="endWorkTime" label="结束时间" width="150">
							</el-table-column>
							<el-table-column prop="workContent" label="工作内容" width="436">
							</el-table-column>
						</el-table>
					</el-col>
				</el-row>


			</div>
		</el-dialog>

		<el-dialog :visible.sync="resumeModal" width="440px" height="auto" title="扫描二维码，查看员工个人简历"
			:mask-closable="false">
			<img v-if="resumeImg" :src="resumeImg" style="width: 400px;height:auto">
		</el-dialog>

	</div>

</template>

<script>
	import Vue from 'vue';

	export default {
		name: "baomuAuth",

		data() {
			return {
				url: '',
				imageUrl: '',
				detailModal: false,
				resumeModal: false,
				resumeImg: null,
				baomuId: null,
				list: null,
				loading: true,
				pageSizeOpts: [10, 20, 30],
				employee: {},
				employeeInfo: {},
				baomuInfo: {},
				baomuExpectedWork: {},
				baomuWorkExperience: [],
				baomuDetail: {},
				pageInfo: {
					total: 10,
					size: 10,
					current: 1,
					pages: 1
				},
				searchState: null,
				searchLevel: null,
				quer: {
					"realName": "",
					"search": "",
					"level": null,
					"state": null,
					"authType": null,
					"current": 1,
					"size": 10
				},
				dto: {
					id: null,
					name: null,
					no: null,
					entryTime: null,
					position: null,
					workAge: null,
					headPortrait: null,
					introduce: null,
					workDeeds: null,
					photo1: null,
					photo2: null,
					photo3: null,
					videoUrl: null,
					status: null,
				},
				levelList: [{
					value: 0,
					text: "未认证",
					type: "info"
				}, {
					value: 1,
					text: "不通过",
					type: "info"
				}, {
					value: 2,
					text: "普通",
					type: "danger"
				}, {
					value: 3,
					text: "金牌",
					type: "warning"
				}, {
					value: 4,
					text: "翡翠",
					type: "success"
				}, {
					value: 5,
					text: "钻石",
					type: ""
				}],
				stateList: [{
					value: 0,
					text: "待认证",
					type: "info"
				}, {
					value: 1,
					text: "未通过",
					type: "danger"
				}, {
					value: 2,
					text: "已通过",
					type: "success"
				}],
				rowData: null,
				journalInfoLoading: true,
				journalList: null,
				journalInfoSizeOpts: [10, 20, 30],
				journalInfo: {
					total: 10,
					size: 10,
					current: 1,
					pages: 1
				},
				journalInfoDrawer: false,
				rules: {
					name: [{
						required: true,
						message: '请输入姓名',
						trigger: 'blur'
					}, ],

					date1: [{
						type: 'date',
						required: true,
						message: '请选择日期',
						trigger: 'change'
					}],
					position: [{
						required: true,
						message: '请输入职位',
						trigger: 'blur'
					}],
					introduce: [{
						required: true,
						message: '内容不能为空',
						trigger: 'blur'
					}],
					workDeeds: [{
						required: true,
						message: '内容不能为空',
						trigger: 'blur'
					}],
				}

			}
		},
		created() {

			this.getData()
		},
		methods: {

			getData() {

				this.$postData("getBaomuAuthPage", this.quer).then(res => {
					this.loading = false
					if (res.status === 200) {
						this.list = res.data.records;
						this.pageInfo.current = res.data.current;
						this.pageInfo.size = res.data.size;
						this.pageInfo.total = res.data.total
						this.formatLevel()
					} else {
						this.list = []
						this.$message({
							message: '未查询到相关记录!',
							type: 'error'
						});
					}
				})
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				let data = []
				// 格式化搜索项
				for (let i in [0, 1, 2, 3, 4, 5]) {
					if (this.searchState == i + "") {
						data = []
						data.push(Number(i))
						this.quer.state = data
					} else if (this.searchLevel == i + "") {
						data = []
						data.push(Number(i))
						this.quer.level = data
					} else {
						if (this.searchState == "" && this.searchState !== 0) {
							this.quer.state = null
						}
						if (this.searchLevel == "" && this.searchLevel !== 0) {
							this.quer.level = null
						}
					}
				}
				this.getData()
			},
			// 重置
			reset() {
				this.quer.realName = ""
				this.quer.search = ""
				this.quer.level = null;
				this.quer.state = null
				this.quer.authType = null
				this.quer.current = 1
				this.searchState = null
				this.searchLevel = null
				this.getData()
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size;
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index;
				this.getData()
			},
			// 格式化工作类型
			formatWorkType(value) {
				let data = ["保姆", "月嫂", "育婴师", "护工", "其他"]
				switch (value) {
					case 10:
						return data[0]
						break
					case 20:
						return data[1]
						break
					case 30:
						return data[2]
						break
					case 40:
						return data[3]
						break
					case 50:
						return data[4]
						break
				}
			},
			// 格式化薪资
			formatSalary(value) {
				if (value !== "0") {
					return value
				} else {
					return "暂未评定"
				}
			},
			// 格式化等级
			formatLevel() {
				// let data = ["不通过", "未认证", "普通", "金牌", "翡翠", "钻石"]
				for (let item of this.list) {
					for (let item1 of this.levelList) {
						if (item.level == item1.value) {
							item.levle = item1.text
						}
					}
				}
			},
			// 格式化认证状态
			formatState(index, state) {
				for (let item of this.stateList) {
					if (item.value == state) {
						if (index == 0) {
							return item.type
						} else if (index == 1) {
							return item.text
						}
					}
				}
			},

			// 查看简历二维码
			resume() {
				this.resumeModal = true
				let param = {
					source: "aygj",
					path: "pages-mine/resume/preview",
					type: 1,
					scene: "id/" + this.baomuId,
					title: "小羽佳-家姐联盟"
				}
				$.ajax({
					type: "post", //type可以为post也可以为get
					url: "https://api.xiaoyujia.com/product/getEmployeePoster",
					data: JSON.stringify(param), //这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
					dataType: "json", //这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
					headers: {
						"Content-Type": "application/json"
					},
					success: res => {
						if (res.code == 0) {
							this.resumeImg = res.data
						} else {
							this.$message({
								message: '获取简历二维码失败!' + res,
								type: 'error'
							});
						}
					},
					error: res => {
						this.$message({
							message: '获取简历二维码失败!' + res,
							type: 'error'
						});
					}
				})
			},
			getBaomuDetail(baomuId) {
				this.$postUrl("getBaomuDetail", baomuId, {}).then(res => {
					this.loading = false
					if (res.status === 200) {
						this.baomuDetail = res.data[0]
						this.employee = this.baomuDetail.employee
						this.employeeInfo = this.baomuDetail.employeeInfo
						this.baomuInfo = this.baomuDetail.baomuInfo
						this.baomuExpectedWork = this.baomuDetail.baomuExpectedWork
						this.baomuWorkExperience = this.baomuDetail.baomuWorkExperience
						this.formatLevel()
					} else {
						this.baomuDetail = []
						this.$message({
							message: '未查询到该员工信息!',
							type: 'error'
						});
					}
				})

			},
			//打开员工信息详情
			rowClick(baomuId) {
				this.baomuId = baomuId
				this.detailModal = true
				this.loading = true
				this.getBaomuDetail(baomuId)
			},
			updateId(val, status) {
				this.dto.id = val;
				this.dto.status = status;
				this.$postData("updateExcellentEmployee", this.dto).then(res => {
					if (res.status === 200) {
						this.$message({
							message: '更新成功',
							type: 'success'
						});
					} else {
						this.$message.error('更新失败');
					}
					this.dto.id = null;
					this.dto.status = null;
					this.getData()
				})
			},
			// 更改认证状态
			updateAuth(val) {
				// 根据认证等级动态调整认证状态
				if (val.level == 0) {
					val.state = 0
					val.authSalary = "0"
				} else if (val.level == 1) {
					val.state = 1
					val.authSalary = "0"
				} else if (val.level > 1) {
					val.state = 2
				}
				val.authEmployeeId = localStorage.getItem("id")
				this.$postData("updateBaomuAuth", val).then(res => {
					if (res.status === 200) {
						this.$message({
							message: '更新成功',
							type: 'success'
						});
					} else {
						this.$message.error('更新失败');
					}
				})
			},
			updateInfo(val) {
				this.detailModal = true;
				this.dto = val;
			},
			createInfo() {
				this.dto = [];
				this.detailModal = true;
			},
			handleAvatarSuccessByHeadPortrait(response, res, file) {
				this.dto.headPortrait = response.data;
			},
			beforeAvatarUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;

				if (!isLt2M) {
					this.$message.error('上传头像图片大小不能超过 2MB!');
				}
				return isLt2M;
			},
			submitForm(formName) {
				this.$refs[formName].validate((valid) => {
					if (valid) {
						this.$postData("createExcellentEmployee", this.dto).then(res => {
							if (res.status === 200) {
								this.$message({
									message: '更新成功',
									type: 'success'
								});
							} else {
								this.$message.error('更新失败');
							}
							this.$refs[formName].resetFields();
							this.detailModal = false;
							this.getData()
						})
					} else {
						console.log('error submit!!');
						return false;
					}
				});
			},
			resetForm(formName) {
				//   console.info(formName)
				this.$refs[formName].resetFields();
			}


		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 178px;
		height: 178px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}
</style>
