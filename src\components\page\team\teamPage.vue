<template>
    <div class="table">
        <el-row :gutter="20">
<!--            <el-col :span="3">-->
<!--                <employeeList></employeeList>-->
<!--            </el-col>-->
            <el-col :span="24">
                <div class="container" style="padding:20px 30px 0 30px">
                    <h2>队伍分类
                        &nbsp;&nbsp;&nbsp;
                        <el-radio-group v-model="radio1">
                            <el-radio-button label="当日数据"></el-radio-button>
                            <el-radio-button label="门店参数"></el-radio-button>
                        </el-radio-group>
                    </h2>
                    <el-tabs v-model="activeName" @tab-click="handleClick">
                        <el-tab-pane label="全部" :name="null"></el-tab-pane>
                        <el-tab-pane :label="item.name" :name="item.name"  v-for="(item,index) in types" :key="index"></el-tab-pane>
                    </el-tabs>
                </div>
                <div style="text-align: right;color: #2d8cf0;padding-top: 5px" @click="showMore=!showMore">
                    {{showMore?'收起全部队伍':'展示全部队伍'}} &nbsp;<i :class="showMore?'el-icon-arrow-up':'el-icon-arrow-down'"></i>
                </div>
                <div class="mybox">
                    <el-row :gutter="20">

                        <el-col :span="6"  v-for="(item,index) in teamList" :key="index" v-if="showMore?index<2:true && showType==0?true:item.team.type==showType" >
                            <el-card shadow="hover">
                                <div slot="header" class="clearfix">
                                    <el-avatar :src="item.team.headImg" size="large" fit="cover" style="float: right"></el-avatar>
                                    <h3>{{item.team.name}}</h3>
                                    <h4>队长：{{item.team.leaderName}}</h4>

                                </div>
                                <el-row  v-show="radio1=='当日数据'">
                                    <el-col :span="8"  v-for="(dataItem,dataIndex) in item.teamShowDataList" :key="dataIndex">
                                        <div style="text-align: center;height: 80px;border: 1px solid #f5f6f7">
                                            <div style="font-size: 20px;color: #2d8cf0"> {{dataItem.num}}</div>
                                            <div style="font-size: 10px;color: #6f7180"> {{dataItem.paramName}}</div>
                                        </div>

                                    </el-col>
                                </el-row>
                                <el-row  v-show="radio1=='门店参数'">
                                    <el-col :span="8">
                                        <div style="text-align: center;height: 80px;border: 1px solid #f5f6f7">
                                            <div style="font-size: 20px;color: #2d8cf0"> {{item.team.areaNum}}</div>
                                            <div style="font-size: 10px;color: #6f7180"> 面积</div>
                                        </div>
                                    </el-col>
                                    <el-col :span="8">
                                        <div style="text-align: center;height: 80px;border: 1px solid #f5f6f7">
                                            <div style="font-size: 20px;color: #2d8cf0"> {{item.team.rentNum}}</div>
                                            <div style="font-size: 10px;color: #6f7180"> 租金</div>
                                        </div>
                                    </el-col>
                                    <el-col :span="8">
                                        <div style="text-align: center;height: 80px;border: 1px solid #f5f6f7">
                                            <div style="font-size: 20px;color: #2d8cf0"> {{item.team.otherCosts}}</div>
                                            <div style="font-size: 10px;color: #6f7180"> 其他成本</div>
                                        </div>
                                    </el-col>
                                </el-row>


                                <el-divider ></el-divider>
                                <el-row>
                                    <el-col :span="8" class="box-bottom">
                                        <el-button type="primary" plain>
                                            <router-link  :to="{ path: 'teamEdit', query: { id: item.team.id }}">
                                                编辑修改
                                            </router-link>
                                        </el-button>
                                    </el-col>
                                    <el-col :span="8" class="box-bottom">
                                        <el-button type="primary" plain>邀请PK</el-button>
                                    </el-col>
                                    <el-col :span="8" class="box-bottom">
                                        <el-button type="primary" plain>
                                            <router-link  :to="{ path: 'teamInfo', query: { id: item.team.id }}">
                                                查看详情
                                            </router-link>
                                        </el-button>
                                    </el-col>


                                </el-row>
                            </el-card>

                        </el-col>
                        <el-col :span="8" >
                            <el-card shadow="hover" style="height: 280px;text-align: center">
                                <div style="margin-top: 80px;padding: 20px;border: 1px dashed #ddd;border-radius: 5px">
                                    <router-link to='/teamEdit'>
                                    <div style="font-size: 30px"><i class="el-icon-plus"></i></div>
                                    添加队伍
                                    </router-link>
                                </div>

                            </el-card>
                        </el-col>
                    </el-row>
                </div>
                <div class="container" style="padding:20px 30px 20px 30px;margin-top: 20px">
                    <h2>Top榜 &nbsp;
                        <el-radio-group v-model="charDom.passDay" @change="getSumParamsToteamId">
                            <el-radio-button :label="0">今天</el-radio-button>
                            <el-radio-button :label="1">昨天</el-radio-button>
                            <el-radio-button :label="7">七天</el-radio-button>
                            <el-radio-button :label="15">十五天</el-radio-button>
                            <el-radio-button :label="30">三十天</el-radio-button>
                        </el-radio-group>
                        <el-input-number v-model="charDom.passDay" @change="getSumParamsToteamId" :min="0" :max="1000" label="描述文字"></el-input-number>
                        <el-divider direction="vertical"></el-divider>
                        <el-radio-group v-model="charType" @change="getSumParamsToteamId">
                            <el-radio-button label="团队"></el-radio-button>
                            <el-radio-button label="个人"></el-radio-button>

                        </el-radio-group>
                    </h2>
                    <el-row :gutter="20">
                        <el-col :span="16">
                            <el-tabs v-model="activeParamName" style="padding-top: 10px"  @tab-click="changeChar">
                                <el-tab-pane :label="item.paramName" :name="item.paramKey"  v-for="(item,index) in paramList" :key="index"></el-tab-pane>
                            </el-tabs>
                            <div>
                                <el-table
                                        v-loading="getParamsDataCharLoading"
                                        element-loading-text="拼命加载中"
                                        element-loading-spinner="el-icon-loading"
                                        element-loading-background="rgba(0, 0, 0, 0.8)"
                                        ref="singleTable"
                                        :data="charList"
                                        highlight-current-row
                                        style="width: 100%">
                                    <el-table-column
                                            label="排名"
                                            type="index"
                                           >
                                    </el-table-column>
                                    <el-table-column
                                            property="name"
                                            label="名称"
                                           >
                                    </el-table-column>
                                    <el-table-column
                                            property="sumParams"
                                            label="参数值"
                                          >
                                    </el-table-column>


                                </el-table>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <g2char :setCharData="g2CharList" :heightSize="350"  v-if="!getParamsDataCharLoading"></g2char>
                        </el-col>
                    </el-row>

                </div>
            </el-col>

        </el-row>

    </div>

</template>

<script>
    import employeeList from "./common/employeeList";
    import g2char from "../agent/char/g2char";
    export default {
        name: "teamPage",
        components:{
            "g2char":g2char,
            employeeList
        },
        data() {
            return {
                radio1:'当日数据',
                charType:"团队",
                charDom:{
                    teamParamId:null,
                    passDay:7,
                },
                topDay:'昨日',
                showType:0,
                showMore:false,
                getParamsDataCharLoading:false,
                teamList:[],
                paramList:[],
                charList:[],
                g2CharList:[],
                loading:false,
                activeName:null,
                activeParamName:null,
                types: [
                    {
                        id: 1,
                        name: "新零售"
                    },
                    {
                        id: 2,
                        name: "消费者"
                    },
                    {
                        id: 3,
                        name: "大客户"
                    },
                    {
                        id: 4,
                        name: "一线"
                    },
                ],
            }
        },
        created(){
            this.getTeamList()
            this.teamParamList()
        },
        methods:{
            changeChar(ar){
                this.charDom.teamParamId=this.paramList[ar.index].id
                this.getSumParamsToteamId()
                location.href="#char"
            },
            getSumParamsToteamId(){
                if (this.charType=='个人'){
                    return this.getSumParamsToEmployeeId()
                }
                this.getParamsDataCharLoading=true

                this.$postData("getSumParamsToteamId",this.charDom,null).then(res => {
                    if (res.status == 200) {
                        this.charList=res.data
                        this.g2CharList=[]
                        if (this.charList.length>0){
                            this.charList.forEach(v=>{
                                let item={
                                    response:v.name,
                                    num:v.sumParams,
                                }
                                this.g2CharList.push(item)
                            })
                        }
                        console.log(this.g2CharList)
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                    this.getParamsDataCharLoading=false

                })
            },
            getSumParamsToEmployeeId(){
                this.getParamsDataCharLoading=true

                this.$postData("getSumParamsToEmployeeId",this.charDom,null).then(res => {
                    if (res.status == 200) {
                        this.charList=res.data
                        this.g2CharList=[]
                        if (this.charList.length>0){
                            this.charList.forEach(v=>{
                                let item={
                                    response:v.name,
                                    num:v.sumParams,
                                }
                                this.g2CharList.push(item)
                            })
                        }
                        console.log(this.g2CharList)
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                    this.getParamsDataCharLoading=false

                })
            },
            teamParamList(){
                this.$getData("teamParamList", {}).then(res => {
                    if (res.status == 200) {
                        this.paramList = res.data;
                        this.activeParamName=this.paramList[0].paramKey
                        this.charDom.teamParamId=this.paramList[0].id
                        this.getSumParamsToteamId()
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            handleClick(tab, event) {
                this.showType=Number(tab.index)
            },
            getTeamList() {
                this.loading = true;
                this.$postData("teamList",{},null).then(res => {
                    this.loading = false;
                    if (res.status === 200) {
                        this.teamList=res.data
                        this.$message({
                            type: 'success',
                            message: '获取成功!'
                        });
                    }
                })
            },
        }

    }
</script>
<style>
    .el-card__header{
        color: white;
        background: #2d8cf0;
    }
     {
        background: #fff;
        color: #2d8cf0;;
    }
</style>

<style scoped>
    .el-divider--horizontal {
        display: block;
        height: 1px;
        width: 100%;
        margin: 12px 0;
    }
    .box-bottom{
        text-align: center;
    }
    .mybox{
        margin-top: 5px;
    }
</style>
