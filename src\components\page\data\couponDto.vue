<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">

                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="订单编号">
                                <el-input
                                        clearable=""
                                        v-model="model.billNo"
                                        placeholder="订单编号"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="来源渠道">
                                <el-input
                                        clearable=""
                                        v-model="model.remark"
                                        placeholder="来源渠道"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="结算日期">
                                <el-date-picker
                                        v-model="value2"
                                        type="daterange"
                                        :picker-options="pickerOptions"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        align="right">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" >

                            <el-form-item>
                                <el-button type="success"   @click="query()" icon="el-icon-search">搜索</el-button>
                                <el-button type="info"   @click="getDown()" icon="el-icon-download">一键下载</el-button>
                            </el-form-item>

                        </el-col>
                    </el-row>

                </el-form>
            </div>
            <el-row>
                <el-col :span="8">
                    <el-card shadow="hover">
                        <i class="el-icon-money"></i>
                        总订单金额：￥<span class="red">{{all.allRealTotalAmount}}</span>
                    </el-card>

                </el-col>
                <el-col :span="8">
                    <el-card shadow="hover">
                        <i class="el-icon-money"></i>
                        总实收金额：￥<span class="red">{{all.allPayAmount}}</span>
                    </el-card>

                </el-col>
                <el-col :span="8">
                    <el-card shadow="hover">
                        <i class="el-icon-money"></i>
                        总优惠金额：￥<span class="red">{{all.allPayValue}}</span>
                    </el-card>

                </el-col>
            </el-row>
            <el-table :data="list" :header-cell-style="{background:'#ddd'}"  border class="table" ref="multipleTable" v-loading="loading">
                <el-table-column
                        type="selection"
                        width="35">
                </el-table-column>
                <el-table-column
                        prop="billNo"
                        fixed="left"
                        label="订单号"
                        width="150"
                ></el-table-column>

                <el-table-column
                        prop="paySettlementTime"
                        label="结算日期"
                        width="160"
                ></el-table-column>
                <el-table-column
                        prop="realTotalAmount"
                        label="订单金额"
                        width="80"
                ></el-table-column>
                <el-table-column
                        prop="payAmount"
                        label="实收金额"
                        width="80"
                ></el-table-column>
                <el-table-column
                        prop="payValue"
                        label="优惠金额"
                        width="80"
                ></el-table-column>
                <el-table-column
                        prop="remark"
                        label="来源渠道"
                ></el-table-column>
                <el-table-column
                        prop="couponId"
                        label="优惠券批次"
                ></el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>

    </div>
</template>

<script>
    export default {
        data() {
            return {
                all:{
                    allPayAmount:0,
                    allPayValue:0,
                    allRealTotalAmount:0,
                },
                value2:[],
                loading:true,
                tableData: [],
                pageSizeOpts:[10,20,30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                screenWidth: '80%',//新增对话框 宽度
                doscreenWidth:'50%',

                show:{},
                model: {
                    billNo:null,
                    remark:null,
                    current: 1,
                    size: 10,
                    startTime:null,
                    endTime:null,
                },
                list:null,
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
            };
        },

        created() {
            this.getData();
        },
        computed: {
        },
        methods: {
            sortChange: function(column, prop, order) {

                if (column == null) {
                    this.model.orderBy = "";
                } else {
                    if (column.order == "ascending") {
                        this.model.orderBy = column.prop + " ASC";
                    }
                    if (column.order == "descending") {
                        this.model.orderBy = column.prop + " DESC";
                    }
                }
                this.getData();
            },
            getData() {
                this.setTime()
                this.loading=true
                this.$postData("seletCouponDto", this.model, {}).then(res => {
                    if (res.status == 200) {

                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                    this.loading=false
                })
                this.$postData("dataCouponDto", this.model, {}).then(res => {
                    if (res.status == 200) {

                        this.all=res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                    this.loading=false
                })
            },

            getDown() {
                this.setTime()
                if(this.value2.length==0){
                    return this.$message.error("数据量过大。请选择结算时间区间再下载。")
                }
                console.log(this.value2)
                this.loading = true;
                this.$postData("exportCouponDto", this.model, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.loading = false;
                    this.blobExport({
                        tablename:"优惠券信息",
                        res: res
                    });
                })
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
            setTime(){
                this.model.startTime=null
                this.model.endTime=null
                if (this.value2.length==0) {
                    this.model.startTime=null
                    this.model.endTime=null
                }else {
                    this.model.startTime=this.value2[0]
                    this.model.endTime=this.value2[1]
                }
            },
            query() {
                this.getData();
            },
            re(){
                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.model.size = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                console.log(index)
                this.model.current = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {

            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },

        }
    };
</script>

<style scoped>

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        font-size: 15px;
        font-weight: bold;
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
