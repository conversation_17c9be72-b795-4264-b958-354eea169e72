<template>
    <div style="background:#fff;padding: 20px">
        <!--        <hotChar :points="listHeatMap" v-if="showChar" style="height: 500px"></hotChar>-->
        <!--        <hr>-->
        <!--        <addr-map :markers="listHeatMap" v-if="showChar"  ></addr-map>-->
        <el-card shadow="hover" body-style="padding: 5px 20px ">
            <div class="card-title">本月需求数量 （ 共 <span style="color: #f00f14">{{allNumByMM}}</span>，平均 <span
                    style="color: #f00f14">{{(allNumByMM/numByMM.length).toFixed(2)}}</span>）
            </div>
            <g2column :setCharData="numByMM" :heightSize="300" v-if="showChar" :showLine="true"></g2column>
        </el-card>
        <br>
        <div class="block">
            <span class="demonstration">条件区间</span>&nbsp;
            <el-date-picker
                    v-model="showDate"
                    type="datetimerange"
                    :picker-options="pickerOptions"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    align="right">
            </el-date-picker>&nbsp;
            <Select filterable style="width: 130px" v-model="orderNeeds.storeId" placeholder="归属门店">
                <Option value="">请选择门店</Option>
                <Option value="1">平台</Option>
                <Option v-for="(item,index) in storeList" :value="item.id" :key="index">{{ item.storeName}}</Option>
            </Select>
          <Select filterable style="width: 130px" v-model="orderNeeds.storeType" placeholder="类型:直营/加盟">
            <Option value="">全部</Option>
            <Option value="1">直营</Option>
            <Option value="2">加盟</Option>

          </Select>
            <Select filterable style="width: 130px" v-model="orderNeeds.status" placeholder="需求状态">
                <Option value="">全部</Option>
                <Option value="0">已取消</Option>
                <Option value="1">未处理</Option>
                <Option value="2">已开单</Option>
                <Option value="3">已读</Option>

            </Select>
            <Select filterable style="width: 130px" v-model="orderNeeds.split" placeholder="拆单状态">
                <Option value="">全部</Option>
                <Option value="1">已拆单</Option>

            </Select>
          <Select filterable style="width: 130px" v-model="orderNeeds.haveDev" placeholder="是否开发">
            <Option value="">全部</Option>
            <Option value="1">是</Option>
            <Option value="2">否</Option>

          </Select>
            <el-input v-model="orderNeeds.tg" style="width: 130px"
                      placeholder="来源渠道">

            </el-input>
            <el-input v-model="orderNeeds.realName" style="width: 130px"
                      placeholder="经纪人名称">

            </el-input>
            <el-button type="primary" @click="searchDate()">区间分析</el-button>
        </div>
        <br>

        <el-card shadow="hover" body-style="padding: 5px 20px ">
            <div class="card-title">需求 - 统计（ 共 <span style="color: #f00f14">{{allNumByStatus}}</span>）</div>
            <el-row :gutter="20">
                <el-col :span="4">
                    <g2char :setCharData="numByStatus" :heightSize="180" v-if="showChar"></g2char>
                </el-col>
                <el-col :span="4">
                    <!--                    <el-card shadow="hover" body-style="padding: 5px 20px " >-->
                    <!--                        <div class="card-title">需求总数:-->
                    <!--                            <span style="color: #f00f14">{{allNumByStatus}}</span></div>-->
                    <!--                    </el-card>-->
                    <el-card shadow="hover" body-style="padding: 5px 20px " v-for="(item,index) in numByStatus"
                             :key="index">
                        <div class="card-title">{{item.response}}:
                            <span style="color: #f00f14">{{item.num}}</span></div>
                    </el-card>

                </el-col>
                <el-col :span="4">
                    <g2char :setCharData="numBySplit" :heightSize="180" v-if="showChar"></g2char>
                </el-col>
                <el-col :span="4">
                    <!--                    <el-card shadow="hover" body-style="padding: 5px 20px " >-->
                    <!--                        <div class="card-title">需求总数:-->
                    <!--                            <span style="color: #f00f14">{{allNumByStatus}}</span></div>-->
                    <!--                    </el-card>-->
                    <el-card shadow="hover" body-style="padding: 5px 20px " v-for="(item,index) in numBySplit"
                             :key="index">
                        <div class="card-title">{{item.response}}:
                            <span style="color: #f00f14">{{item.num}}</span></div>
                    </el-card>

                </el-col>
                <el-col :span="4">
                    <g2char :setCharData="numByProduct" :heightSize="180" v-if="showChar"></g2char>
                </el-col>
                <el-col :span="4">

                    <el-card shadow="hover" body-style="padding: 5px 20px " v-for="(item,index) in numByProduct"
                             :key="index">
                        <div class="card-title">{{item.response}}:
                            <span style="color: #f00f14">{{item.num}}</span></div>
                    </el-card>

                </el-col>
            </el-row>
        </el-card>
        <el-card shadow="hover" body-style="padding: 5px 20px " style="height: 450px;overflow: auto">
            <div class="card-title">需求 - 地区渠道分布</div>
            <el-row :gutter="20">
                <el-col :span="4">
                    <el-card shadow="hover" body-style="padding: 5px 20px " v-for="(item,index) in numByArear"
                             :key="index">
                        <div class="card-title" style="font-size: 12px">{{item.response}}:
                            <span style="color: #f00f14">{{item.num}}</span></div>
                    </el-card>
                </el-col>
                <el-col :span="4">
                    <g2char :setCharData="numByArear" :heightSize="350" v-if="showChar"></g2char>
                </el-col>
                <el-col :span="4">
                    <el-card shadow="hover" body-style="padding: 5px 20px " v-for="(item,index) in numByNeedStatus"
                             :key="index">
                        <div class="card-title" style="font-size: 12px">{{item.response}}:
                            <span style="color: #f00f14">{{item.num}}</span></div>
                    </el-card>
                </el-col>
                <el-col :span="4">
                    <g2char :setCharData="numByNeedStatus" :heightSize="350" v-if="showChar"></g2char>
                </el-col>
                <el-col :span="4">
                    <el-card shadow="hover" body-style="padding: 5px 20px " v-for="(item,index) in numByTg"
                             :key="index">
                        <div class="card-title" style="font-size: 12px">{{item.response}}:
                            <span style="color: #f00f14">{{item.num}}</span></div>
                    </el-card>
                </el-col>
                <el-col :span="4">
                    <g2char :setCharData="numByTg" :heightSize="350" v-if="showChar"></g2char>
                </el-col>

            </el-row>
        </el-card>
        <el-card shadow="hover" body-style="padding: 5px 20px ">
            <div class="card-title">需求 - 经纪人分布</div>
            <g2column :setCharData="numByResponses" :heightSize="500" ref="g2column" v-if="showChar"></g2column>
            <g2char :setCharData="numByResponses" :heightSize="500" ref="g2char" v-if="showChar"></g2char>
        </el-card>
      <el-card shadow="hover" body-style="padding: 5px 20px ">
        <div class="card-title">需求 - 开发人分布</div>
        <g2column :setCharData="numStoreByResponses" :heightSize="500" ref="g2column" v-if="showChar"></g2column>
        <g2char :setCharData="numStoreByResponses" :heightSize="560" ref="g2char" v-if="showChar"></g2char>
      </el-card>
    </div>

</template>

<script>
    import g2char from "../char/g2char";
    import g2column from "../char/g2column";
    import baiduHotChar from "../char/g2column";
    import hotChar from "../char/hotChar";
    import addrMap from "../char/addrMap";

    export default {
        components: {
            "g2char": g2char,
            "g2column": g2column,
            "baiduHotChar": baiduHotChar,
            "hotChar": hotChar,
            "addrMap": addrMap,
        },
        name: "orderNeedData",
        data() {
            return {
                showChar: false,
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                showDate: '',
                allNumBySplit: 0,
                allNumByStatus: 0,
                allNumByMM: 0,
                numByTg: [],
                numByProduct: [],
                numByArear: [],
                numByStatus: [],
                numByResponses: [],
                numStoreByResponses: [],
                listHeatMap: [],
                numByMM: [],
                numBySplit: [],
                numByNeedStatus: [],
                storeList: [],
                orderNeeds: {
                    realName: null,
                    tg: null,
                    split: null,
                    status: null,
                    storeId: null,
                    agentId: localStorage.getItem("id"),
                    startDateTime: null,
                    endDateTime: null,
                    storeType:null,
                    haveDev:null
                }
            }
        },
        created() {
            if (localStorage.getItem("roleId") !== "42") {
                this.orderNeeds.agentId = null;
            }
            this.getNumByStatus();
            this.getStore();
        },
        methods: {
            getStore() {
                this.$postData("store_getByList", {}, {}).then(res => {
                    if (res.status == 200) {
                        this.storeList = res.data;
                    }
                });
            },
            searchDate() {
                this.numByResponses = [];
                this.numStoreByResponses = [];
                if (this.showDate != null) {
                    this.orderNeeds.startDateTime = this.showDate[0];
                    this.orderNeeds.endDateTime = this.showDate[1];
                } else {
                    this.orderNeeds.startDateTime = null;
                    this.orderNeeds.endDateTime = null;
                }
                this.getNumByStatus();
            },
            getNumByStatus() {
                this.showChar = false;
                this.$postData("getNumByRealName", this.orderNeeds, {}).then(res => {
                    if (res.status == 200) {
                        this.numByResponses = res.data.numByRealName;
                        this.numByArear = res.data.numByArear;
                        this.numByProduct = res.data.numByProduct;
                        this.numByMM = res.data.numByMM;
                        this.numByStatus = res.data.numByStatus;
                        this.listHeatMap = res.data.listHeatMap;
                        this.numBySplit = res.data.numBySplit;
                        this.numByTg = res.data.numByTg;
                        this.numByNeedStatus = res.data.numByNeedStatus;
                        this.numStoreByResponses = res.data.numByStoreName;
                        this.allNumByMM = 0;
                        this.numByMM.forEach(v => {
                            this.allNumByMM += v.num;
                            v.response = v.response + '日';
                        });
                        this.allNumByStatus = 0;
                        this.allNumBySplit = 0;
                        this.numBySplit.forEach(v => {
                            this.allNumBySplit += v.num;
                            if (v.response == 0) {
                                v.response = "未拆单"
                            }
                            if (v.response == 1) {
                                v.response = "已拆单"
                            }
                            if (v.response == null) {
                                v.response = "未成单"
                            }
                        });
                        this.numByTg.forEach(v => {
                            if (v.response == null || v.response == '') {
                                v.response = "未知"
                            }
                        });
                        this.numByStatus.forEach(v => {
                            this.allNumByStatus += v.num;
                            if (v.response == 0) {
                                v.response = "已取消"
                            }
                            if (v.response == 1) {
                                v.response = "未处理"
                            }
                            if (v.response == 2) {
                                v.response = "已开单"
                            }
                            if (v.response == 3) {
                                v.response = "已读"
                            }
                            if (v.response == 4) {
                                v.response = "待支付"
                            }
                        });
                        this.showChar = true
                    } else {
                        this.showChar = true;
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
        }
    }
</script>

<style scoped>
    .card-title {
        font-size: 18px;
        font-weight: bold;
        padding-left: 10px;
        border-left: 5px solid #409eff;
        margin: 10px 0;
    }

</style>
