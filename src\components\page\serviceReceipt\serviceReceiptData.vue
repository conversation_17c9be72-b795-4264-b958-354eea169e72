<template>
  <div class="container">
    <div id="report" style="width: 1000px;height: 400px;"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: "serviceReceiptData",
  data() {
    return {
      title: {
        text: '客户评价等级比例',
        left: 'center',
        top: 'center'
      },
      satisfied: null,
      commonly: null,
      dissatisfied: null,
    }
  },
  mounted() {
    this.$nextTick(function () {
      this.draw("report");
    });
  },
  methods: {
    draw(id) {
// 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById(id));
// 绘制图表
      this.$getData("selectCount").then(res => {
        if (res.status === 200) {
          myChart.setOption({
            tooltip:{
              trigger:'item',
              formatter:'{a} <br/>:{c} ({d}%)'
            },
            legend:{
              orient:'vertical',
              left: 10,
              data:['不满意','一般','满意']
            },
            title: this.title,
            series: [
              {
                name:'客户评价',
                type: 'pie',
                data: [
                  { name: '不满意',value:res.data.不满意,},
                  { name: '一般',value:res.data.一般,},
                  { name: '满意',value:res.data.满意,},
                ],
                label:{
                  formatter: '{b|{b}: }{c} ({d}%)',
                  rich:{
                    a:{
                      color:'#999',
                      lineHeight:22,
                      align:'center'
                    },
                    b:{
                      fontSize:16,
                      lineHeight:33
                    },
                  }
                },
                radius: ['40%', '70%']
              }
            ]
          })
        }
      })
    },
  },
}
</script>

<style scoped>
</style>