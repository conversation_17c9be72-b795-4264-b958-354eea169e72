<template>
    <div class="container">
        <div class="handle-box">
            <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="手机号">
                    <el-input v-model="dom.phone" placeholder="请输入团长手机号" clearable
                              @keyup.enter.native="onQuery"></el-input>
                </el-form-item>
                <el-form-item label="姓名">
                    <el-input v-model="dom.name" placeholder="请输入团长姓名" clearable
                              @keyup.enter.native="onQuery"></el-input>
                </el-form-item>
                <el-form-item label="归属门店">
                    <el-select ref="storeNameSel" filterable v-model="dom.storeId" placeholder="请选择门店">
                        <el-option
                                v-for="(item,index) in storeList"
                                :key="index"
                                :label="item.text"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="审核状态">
                    <el-select ref="stateNameSel" filterable v-model="dom.state" placeholder="请选择状态">
                        <el-option
                                v-for="(item,index) in stateList"
                                :key="index"
                                :label="item.text"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-search" type="primary" @click="onQuery" v-loading="loading">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-refresh" @click="reset()" v-loading="loading">
                        重置
                    </el-button>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-upload2" type="primary" @click="isModal = true" v-loading="loading">导入
                    </el-button>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-download" type="primary" @click="exportExcel" v-loading="loading">导出
                    </el-button>
                </el-form-item>
            </el-form>

            <Modal v-model="isModal" class="Modal" :width="screenWidth" title="批量导入"
                   :mask-closable="false"
                   @on-cancel="onQuery()">
                <div class="addBody">
                    <community-head-excel
                            v-if="isModal"
                            @init-choose="initChooseProject"
                            @close-modal="closeCurrModal">
                    </community-head-excel>
                </div>
                <div slot="footer"></div>
            </Modal>

            <el-table :data="commDataList" height="500px" v-loading="loading"
                      :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
                <el-table-column type="expand">
                    <template slot-scope="scope">
                        <el-button @click="dialogEditVisible = true; rowData = JSON.parse(JSON.stringify(scope.row)); "
                                   type="primary" size="mini">
                            编辑
                        </el-button>
                        <el-button @click="delInfo(scope.row.id)" type="danger" size="mini">删除</el-button>
                        <el-button @click="viewAttach(scope.row.id)" type="info" size="mini">查看附件</el-button>
                        <el-button @click="reviewHead(scope.row, 1)" type="primary" size="mini"
                                   v-if="scope.row.state === 0 || scope.row.state === 2">
                            通过
                        </el-button>
                        <el-button @click="dialogFormVisible = true;" type="warning" size="mini"
                                   v-if="scope.row.state === 0">
                            不通过
                        </el-button>
                        <el-button size="mini" @click="handleCopy(scope.$index, scope.row)">
                            复制补充资料链接
                        </el-button>

                        <!--<div>-->
                        <!--<a @click="dialogEditVisible = true; rowData = JSON.parse(JSON.stringify(scope.row)); ">编辑</a>-->
                        &nbsp;
                        <!--<a @click="delInfo(scope.row.id)">删除</a>&nbsp;-->
                        <!--<a @click="viewAttach(scope.row.id)">附件</a>-->
                        <!--</div>-->
                        <el-dialog title="审核" :visible.sync="dialogFormVisible">
                            <el-form>
                                <el-form-item label="不通过原因">
                                    <el-input v-model="reviewMsg" autocomplete="off"></el-input>
                                </el-form-item>
                            </el-form>
                            <div slot="footer" class="dialog-footer">
                                <el-button @click="dialogFormVisible = false">取 消</el-button>
                                <el-button type="primary" @click="reviewHead(scope.row, 2)">确 定</el-button>
                            </div>
                        </el-dialog>
                        <!--<div v-if="scope.row.state === 0 || scope.row.state === 2">-->
                        <!--    <a @click="reviewHead(scope.row, 1)" v-if="scope.row.state === 0 || scope.row.state === 2">-->
                        <!--        通过-->
                        <!--    </a>-->
                        <!--    &nbsp;-->
                        <!--    <a @click="dialogFormVisible = true;" v-if="scope.row.state === 0">-->
                        <!--        不通过-->
                        <!--    </a>-->
                        <!--    -->
                        <!--</div>-->
                    </template>
                </el-table-column>
                <el-table-column
                        label="序号"
                        type="index"
                        width="80"
                        align="center">
                </el-table-column>
                <el-table-column
                        label="归属门店"
                        align="center"
                >
                    <template slot-scope="scope">
                        {{getStoreName(scope.row.storeId)}}
                    </template>
                </el-table-column>
                <el-table-column
                        width="70"
                        prop="employeeName"
                        label="归属店长"
                        align="center">
                </el-table-column>
                <el-table-column
                        width="70"
                        prop="name"
                        label="团长姓名"
                        align="center">
                </el-table-column>
                <el-table-column
                        prop="phone"
                        label="团长手机号"
                        align="center">
                </el-table-column>
                <el-table-column
                        width="70"
                        label="审核状态"
                        align="center">
                    <template slot-scope="scope">
                        <span v-if="scope.row.state == null">未知</span>
                        <span v-else-if="scope.row.state === 0">未审核</span>
                        <span v-else-if="scope.row.state === 1" style="color: #00D1B2;">审核成功</span>
                        <span v-else-if="scope.row.state === 2" style="color: #f00f14;">审核失败</span>
                        <span v-else>未知</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="wxNum"
                        label="朋友圈人数"
                        width="80"
                        align="right">
                </el-table-column>
                <el-table-column
                        label="团长标签"
                        align="center">
                    <template slot-scope="scope">
                        <span v-if="scope.row.labels == null">未知</span>
                        <span v-else-if="scope.row.labels === 0">其他</span>
                        <span v-else-if="scope.row.labels === 1">宝妈客户</span>
                        <span v-else-if="scope.row.labels === 2">老客户</span>
                        <span v-else-if="scope.row.labels === 3">在户保姆用户</span>
                        <span v-else-if="scope.row.labels === 4">小家政</span>
                        <span v-else-if="scope.row.labels === 5">物业</span>
                        <span v-else-if="scope.row.labels === 6">异业商户</span>
                        <span v-else>未知</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="reviewerName"
                        label="审核人"
                        align="left">
                </el-table-column>
                <el-table-column
                        show-overflow-tooltip
                        prop="reviewReason"
                        label="不通过理由"
                        align="left">
                </el-table-column>
                <el-table-column
                        width="125"
                        prop="reviewTime"
                        label="审核时间"
                        align="center">
                </el-table-column>
                <el-table-column
                        width="125"
                        prop="createTime"
                        label="提交时间"
                        align="center">
                </el-table-column>
                <el-table-column label="身份证" align="center"
                                 show-overflow-tooltip>
                    <template slot-scope="scope">
                        {{ scope.row.idCard }}
                    </template>
                </el-table-column>
                <el-table-column label="银行卡" align="center"
                                 show-overflow-tooltip>
                    <template slot-scope="scope">
                        {{ scope.row.bankCard }}
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChangePage"
                      :current="dom.current"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="dom.size"/>
            </div>

            <el-dialog title="编辑" :visible.sync="dialogEditVisible" width="30%">
                <el-form label-width="100px">
                    <el-form-item label="归属门店" style="width: 350px;">
                        <!--<el-input v-model="rowData.storeName" autocomplete="off"></el-input>-->
                        <el-select ref="storeNameSel" filterable v-model="rowData.storeId"
                                   placeholder="请选择门店"
                                   filterable
                                   style="width: 250px;"
                                   clearable>
                            <el-option
                                    v-for="(item,index) in storeList"
                                    :key="index"
                                    :label="item.text"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="归属店长" style="width: 350px;">
                        <!--<el-input v-model="rowData.employeeName" autocomplete="off"></el-input>-->
                        <el-select ref="storeNameSel" filterable v-model="rowData.employeeID"
                                   placeholder="请选择店长名称"
                                   style="width: 250px;"
                                   clearable>
                            <el-option
                                    v-for="(item,index) in employeeList"
                                    :key="index"
                                    :label="item.text"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="团长姓名" style="width: 350px;">
                        <el-input v-model="rowData.name" autocomplete="off"></el-input>
                    </el-form-item>
                    <el-form-item label="团长手机号" style="width: 350px;">
                        <el-input v-model="rowData.phone" autocomplete="off"></el-input>
                    </el-form-item>
                    <el-form-item label="朋友圈人数" style="width: 350px;">
                        <el-input-number v-model="rowData.wxNum" :min="1" :max="100000" label="朋友圈人数"></el-input-number>
                        <!--<el-input v-model="rowData.wxNum" autocomplete="off" type="number"></el-input>-->
                    </el-form-item>
                    <el-form-item label="团长标签" style="width: 350px;">
                        <!--<el-input v-model="rowData.labels" autocomplete="off"></el-input>-->
                        <el-select ref="storeNameSel" filterable v-model="rowData.labels"
                                   placeholder="请选择团长标签"
                                   style="width: 250px;"
                                   clearable>
                            <el-option
                                    v-for="(item,index) in labelList"
                                    :key="index"
                                    :label="item.text"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="不通过原因" v-if="rowData.state === 2" style="width: 350px;">
                        <el-input v-model="rowData.reviewReason" autocomplete="off"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogEditVisible = false">取 消</el-button>
                    <el-button type="primary" @click="editInfo()">确 定</el-button>
                </div>
            </el-dialog>

            <el-dialog title="附件" :visible.sync="showAttachDialog" width="60%">
                <div style="height: 300px;width:100%;margin: 0 auto;">
                    <div style="float: left;padding: 0 25px;">
                        <span>身份证正面：</span>
                        <div v-if="attachData.idCardFrontUrl != null">
                            <el-image
                                    style="width: 200px; height: 200px"
                                    :src="attachData.idCardFrontUrl"
                                    :fit="fits"
                                    :preview-src-list="attachData.idCardFrontUrlList"></el-image>
                        </div>
                    </div>
                    <div style="float: left;padding: 0 25px;">
                        <span>身份证反面：</span>
                        <div v-if="attachData.idCardBackUrl != null">
                            <el-image
                                    style="width: 200px; height: 200px"
                                    :src="attachData.idCardBackUrl"
                                    :fit="fits"
                                    :preview-src-list="attachData.idCardBackUrlList"></el-image>
                        </div>
                    </div>
                    <div style="float: left;padding: 0 25px;">
                        <span>银行卡：</span>
                        <div v-if="attachData.bankCardUrl != null">
                            <el-image
                                    style="width: 200px; height: 200px"
                                    :src="attachData.bankCardUrl"
                                    :fit="fits"
                                    :preview-src-list="attachData.bankCardUrlList"></el-image>
                        </div>
                    </div>
                </div>
            </el-dialog>
        </div>

    </div>
</template>

<script>
    import communityHeadExcel from '@/components/page/community/communityHeadExcel.vue'

    export default {
        name: "communityHeadReview",
        components: {
            "communityHeadExcel": communityHeadExcel
        },
        data() {
            return {
                isModal: false,
                screenWidth: "35%",
                dialogEditVisible: false,
                dialogFormVisible: false,
                loading: false,
                dom: {
                    eId: localStorage.getItem("id"),
                    phone: null,
                    name: null,
                    state: null,
                    storeId: null,
                    size: 15,
                    current: 1,
                },
                reviewMsg: null,
                storeList: [],
                employeeList: [],
                stateList: [
                    {text: '未审核', value: 0},
                    {text: '审核通过', value: 1},
                    {text: '审核失败', value: 2},
                ],
                labelList: [
                    {text: '其他', value: 0},
                    {text: '宝妈客户', value: 1},
                    {text: '老客户', value: 2},
                    {text: '在户保姆用户', value: 3},
                    {text: '小家政', value: 4},
                    {text: '物业', value: 5},
                    {text: '异业商户', value: 6}
                ],
                pageSizeOpts: [10, 15, 20, 30, 50, 100],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                commDataList: [],
                rowData: {},
                showAttachDialog: false,
                attachData: {
                    idCardFrontUrl: null,
                    idCardFrontUrlList: [],
                    idCardBackUrl: null,
                    idCardBackUrlList: [],
                    bankCardUrl: null,
                    bankCardUrlList: [],

                },
                fits: "scale-down"
            }
        },
        async created() {
            this.loadStart();
            await this.getStoreData();
            await this.onQuery();
            this.getCommEmployee();
            this.loadEnd();
        },
        mounted() {
        },
        methods: {
            async getStoreData() {
                // await this.$postData("getAllStore", JSON.stringify({id: this.dom.eId}), {}).then((res) => {
                //     this.dom.storeIds = [];
                //     if (res.status === 200) {
                //         this.storeList = res.data;
                //     } else {
                //         this.$message.error(res.msg);
                //     }
                // });
                await this.$getData("getAllStore", {}, {}).then(res => {
                    if (res.status === 200) {
                        this.storeList = res.data;
                        this.storeList.push({text: "自荐", value: -1})
                    }
                });
            },
            getCommEmployee() {
                this.$getData("getCommEmployee").then(res => {
                    if (res.status === 200) {
                        this.employeeList = res.data;
                    }
                });
            },
            onQuery() {
                this.loadStart();
                this.$postData("getCommunityHeadPage", JSON.stringify(this.dom), {}).then((res) => {
                    if (res.status === 200) {
                        this.commDataList = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total;
                        this.loadEnd();
                    } else {
                        this.$message.error(res.msg);
                        this.loadEnd();
                    }
                });
            },
            copyPhoneLink() {

            },
            onCopy(e) {
                console.info(e);
                this.$message.success("内容已复制到剪切板！")
            },
            onError(e) {
                this.$message.error("抱歉，复制失败！")
            },
            handleCopy(index, row) {
                if (row == null || row.phone == null || row.phone === '') {
                    return this.$message.error("手机号码为空，复制失败！");
                }
                const fixContext = 'https://task.xiaoyujia.com/collageEnroll?queryPhone=';
                this.copyData = fixContext + row.phone;
                this.copyDatas(this.copyData);
            },
            copyDatas(data) {
                let url = data;
                let oInput = document.createElement('input');
                oInput.value = url;
                document.body.appendChild(oInput);
                oInput.select(); // 选择对象;
                // console.log(oInput.value);
                document.execCommand("Copy"); // 执行浏览器复制命令
                this.$message({
                    message: '复制成功',
                    type: 'success'
                });
                oInput.remove();
            },
            reviewHead(row, state) {
                if (state === 2 && (this.reviewMsg == null || this.reviewMsg === '')) {
                    return this.$message.error('请输入审核不通过原因');
                }
                this.$confirm("是否确认此操作, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    const param = {
                        eId: localStorage.getItem("id"),
                        id: row.id,
                        state: state,
                        reviewMsg: this.reviewMsg
                    };
                    this.$postData("updateCommHead", JSON.stringify(param), {}).then((res) => {
                        if (res.status === 200) {
                            this.dialogFormVisible = false;
                            this.$message({type: "success", message: "操作成功!"});
                            this.onQuery();
                        } else {
                            this.$message.error(res.msg);
                        }
                    });

                }).catch(() => {
                    // this.$message({type: "info", message: "已取消操作"});
                });
            },
            editInfo() {
                // console.info(this.rowData);
                if (this.rowData.name == null || this.rowData.name === '') {
                    return this.$message.error("团长姓名不能为空");
                }
                if (this.rowData.phone == null || this.rowData.phone === '') {
                    return this.$message.error("团长手机号不能为空");
                }
                this.rowData.currentEid = this.dom.eId;
                if (this.rowData.currentEid == null || this.rowData.currentEid === '') {
                    return this.$message.error("请重新登录");
                }
                if (this.rowData.wxNum == null || this.rowData.wxNum === '') {
                    return this.$message.error("朋友圈人数不能为空");
                }
                if (this.rowData.labels == null || this.rowData.labels === '') {
                    return this.$message.error("标签不能为空");
                }
                this.$postData("updateComm", JSON.stringify(this.rowData), {}).then((res) => {
                    if (res.status === 200) {
                        this.dialogEditVisible = false;
                        this.$message({type: "success", message: "编辑成功!"});
                        this.onQuery();
                    } else if (res.status === 500 && res.msg != null && res.msg !== '') {
                        this.$message.error(res.msg);
                    } else {
                        this.$message.error("编辑失败");
                    }
                });
            },
            delInfo(id) {
                this.$confirm("是否确认删除?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    if (this.dom.eId == null || this.dom.eId === '') {
                        return this.$message.error("请重新登录");
                    }
                    const param = {
                        id: id,
                        eId: this.dom.eId
                    };
                    this.$postData("delCommHead", JSON.stringify(param), {}).then((res) => {
                        if (res.status === 200) {
                            this.dialogEditVisible = false;
                            this.$message({type: "success", message: "删除成功!"});
                            this.onQuery();
                        } else {
                            this.$message.error("删除失败");
                        }
                    });
                }).catch(() => {
                })
            },
            viewAttach(id) {
                if (this.dom.eId == null || this.dom.eId === '') {
                    return this.$message.error("请重新登录");
                }
                const param = {
                    id: id,
                    eId: this.dom.eId,
                    token: localStorage.getItem('token')
                };
                this.$postData("viewCommAttach", JSON.stringify(param), {}).then((res) => {
                    if (res.status === 200) {
                        if (res.data == null) {
                            return this.$message.info("暂无附件可供查看");
                        }
                        this.showAttachDialog = true;
                        this.attachData = res.data;
                        const bankCardUrl = this.attachData.bankCardUrl;
                        if (bankCardUrl != null) {
                            this.attachData.bankCardUrlList = [];
                            this.attachData.bankCardUrlList.push(bankCardUrl);
                        }
                        const idCardFrontUrl = this.attachData.idCardFrontUrl;
                        if (idCardFrontUrl != null) {
                            this.attachData.idCardFrontUrlList = [];
                            this.attachData.idCardFrontUrlList.push(idCardFrontUrl);
                        }
                        const idCardBackUrl = this.attachData.idCardBackUrl;
                        if (idCardBackUrl != null) {
                            this.attachData.idCardBackUrlList = [];
                            this.attachData.idCardBackUrlList.push(idCardBackUrl);
                        }
                    } else {
                        this.$message.error("查看附件失败 " + res.msg);
                    }
                });
            },
            getStoreName(val) {
                let name = "";
                this.storeList.find(e => {
                    if (e.value === val) {
                        name = e.text;
                    }
                });
                if (val === 2) {
                    name = "厦门小羽佳家政"
                }
                if (val === -1) {
                    name = "自荐"
                }
                return name;
            },
            reset() {
                this.pageInfo.current = 1;
                this.dom.current = 1;
                this.dom.storeId = null;
                this.dom.phone = null;
                this.dom.name = null;
                this.dom.state = null;
                this.onQuery();
            },
            exportExcel() {
                this.loadStart();
                this.$confirm("是否要导出当前条件下所有的数据, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    this.$postData("getComHeadListExport", JSON.stringify(this.dom), {
                        responseType: "arraybuffer"
                    }).then(res => {
                        this.blobExport({
                            tableName: "社区负责人列表",
                            res: res
                        });
                    })
                }).catch(() => {
                    this.loadEnd();
                    // this.$message({type: "info", message: "已取消操作"});
                });
            },
            onChangePage(index) {
                this.dom.current = index;
                this.onQuery();
            },
            onPageSizeChange(size) {
                this.loading = true;
                this.dom.size = size;
                this.onQuery();
            },
            loadStart() {
                this.loading = true;
            },
            loadEnd() {
                this.loading = false;
            },
            blobExport({tableName, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tableName + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
                this.$message({type: "info", message: "导出成功"});
                this.loadEnd();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal(data) {
                this.isModal = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.onQuery();
            },
        }
    }
</script>

<style>

</style>
