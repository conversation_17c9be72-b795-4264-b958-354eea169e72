{"name": "vue-manage-system", "version": "4.0.1", "private": true, "scripts": {"dev": "npm run serve", "serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@antv/data-set": "^0.11.8", "@antv/g2": "^4.1.1", "@tinymce/tinymce-vue": "^4.0.2", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "^0.18.0", "babel-polyfill": "^6.26.0", "clipboard": "^2.0.8", "echarts": "^4.8.0", "element-ui": "^2.15.5", "goeasy": "^1.1.1", "html2canvas": "^1.0.0-rc.5", "image-viewer-vue": "^2.0.6", "iview": "^3.5.4", "jquery": "^3.5.1", "jspdf": "^1.5.3", "mavon-editor": "^2.9.0", "moment": "^2.29.1", "pinyin-pro": "^3.11.0", "qrcodejs2": "0.0.2", "quill": "^1.3.7", "quill-image-drop-module": "^1.0.3", "quill-image-resize-module": "^3.0.0", "save": "^2.4.0", "tinymce": "^5.8.0", "v-viewer": "^1.6.4", "vue": "^2.6.11", "vue-amap": "^0.5.10", "vue-clipboard": "0.0.1", "vue-clipboard2": "^0.3.1", "vue-cropperjs": "^3.0.0", "vue-echarts": "^4.1.0", "vue-i18n": "^8.18.2", "vue-json-excel": "^0.3.0", "vue-pdf": "^4.3.0", "vue-qr": "^2.2.1", "vue-quill-editor": "^3.0.6", "vue-router": "^3.3.4", "vue-schart": "^1.0.0", "vue-video-player": "^5.0.2", "vuedraggable": "^2.24.3", "vux": "^2.9.4", "wangeditor": "^4.7.15"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.12.1", "@vue/cli-service": "^3.12.1", "less": "^3.12.2", "less-loader": "^7.1.0", "vue-directive-image-previewer": "^2.2.2", "vue-template-compiler": "^2.6.11"}}