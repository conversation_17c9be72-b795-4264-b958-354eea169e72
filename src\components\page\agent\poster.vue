<template>
    <div class="table">
        <el-dialog
                title="创建海报活动"
                :visible.sync="dialogVisible">
            <el-form :model="posterActivity" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="活动名称" prop="billName">
                    <el-input v-model="posterActivity.billName"></el-input>
                </el-form-item>
                <el-form-item label="活动说明" prop="billDesc">
                    <el-input v-model="posterActivity.billDesc"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
                    <el-button @click="resetForm('ruleForm')">重置</el-button>
                </el-form-item>
            </el-form>

        </el-dialog>
        <el-dialog
                title="创建海报活动"
                :visible.sync="posterDialogVisible">
            <el-form :model="poster" :rules="posterRules" ref="poster" label-width="120px" class="demo-ruleForm">
                <el-form-item label="海报名称" prop="name">
                    <el-input v-model="poster.name"></el-input>
                </el-form-item>
                <el-form-item label="海报说明" prop="detail">
                    <el-input v-model="poster.detail"></el-input>
                </el-form-item>
                <el-form-item label="海报二维码链接" prop="billUrl">
                    <el-input v-model="poster.billUrl"></el-input>
                    <span style="color: red;font-size: 12px;">注意: 链接中不能带有下面勾选的海报链接参数(
                      <span v-for="(param,index) in posterParamList" :key="index">
                        <span>{{param.paramName}}、</span>
                      </span>
                      )</span>
                  <div>tip:若只勾选门店二维码,会自动获取海报人的门店信息直达小程序，无需配置其他任何参数</div>
                </el-form-item>
                <el-form-item label="海报链接参数" prop="type">
                    <el-checkbox-group v-model="poster.posterParamIds">
                        <el-checkbox :label="param.id" :name="param.id.toString()"
                                     v-for="(param,index) in posterParamList" :key="index">{{param.paramDesc}}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="海报二维码位置" prop="type">
                    <el-radio-group v-model="poster.type">
                        <el-radio-button :label="0">居中</el-radio-button>
                        <el-radio-button :label="1">右侧</el-radio-button>
                        <el-radio-button :label="2">居中(小)</el-radio-button>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="海报底图">
                    <el-upload
                            class="upload-demo"
                            action="https://biapi.xiaoyujia.com/files/uploadFiles"
                            list-type="picture"
                            :on-success="handleAvatarSuccess">
                        <el-button size="small" type="primary">点击选择海报</el-button>
                    </el-upload>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('poster')">立即创建</el-button>
                    <el-button @click="resetForm('poster')">重置</el-button>
                </el-form-item>
            </el-form>

        </el-dialog>
        <div class="container">
            <el-button type="primary" icon="el-icon-circle-plus-outline" @click="dialogVisible=true">创建海报活动</el-button>

            <br>
            <br>
            <el-collapse v-model="activeNames" @change="handleChange" accordion>
                <el-collapse-item :title="item.billName" :name="item.id" v-for="(item,index) in activityList"
                                  :key="index">
                    <template slot="title">
                        <div style="width: 60%" @click="poster.billActivityId=item.id">
                            <el-tag :type="item.status==1?'success':'warning'">{{item.status==1?'上架中':'下架中'}}</el-tag>
                            <el-divider direction="vertical"></el-divider>
                            {{item.billName}}
                            <el-divider direction="vertical"></el-divider>
                            <span style="color: #6f7180;font-size: 12px">介绍：{{item.billDesc}}</span>
                        </div>

                        <div style="width: 40%">
                            <el-button type="primary" class="button" icon="el-icon-circle-plus-outline"
                                       @click="poster.billActivityId=item.id,posterDialogVisible=true">创建海报
                            </el-button>
                            <el-button type="danger" class="button"
                                       @click="item.status==1?item.status=0:item.status=1,savePosterActivity(item)">
                                {{item.status==1?'下架':'上架'}}
                            </el-button>
                        </div>
                    </template>


                    <el-row :gutter="20">
                        <el-col :span="4" v-for="(posterItem,index) in posterList" :key="index">
                            <div class="grid-content bg-purple">
                                <el-card :body-style="{ padding: '10px' }">
                                    <img :src="posterItem.bill" class="image">
                                    <div style="padding: 14px;">
                                        <span>{{posterItem.name}}<el-tag
                                                :type="posterItem.status==1?'success':'warning'">{{posterItem.status==1?'上架中':'下架中'}}</el-tag></span>
                                        <div>海报地址：<a :href="posterItem.billUrl">{{posterItem.billUrl}}</a></div>
                                        <div class="bottom clearfix">
                                            <time class="time">{{ posterItem.creatTime }}</time>
                                            <div style="margin-top: 5px;">
                                                <el-button type="danger" class="button"
                                                           @click="posterItem.status==1?posterItem.status=0:posterItem.status=1,savePoster(posterItem)">
                                                    {{posterItem.status==1?'下架':'上架'}}
                                                </el-button>
                                                <el-button type="danger" class="button"
                                                           @click="deleltePoster(posterItem)">
                                                    删除
                                                </el-button>
                                            </div>
                                        </div>
                                    </div>
                                </el-card>
                            </div>
                        </el-col>

                    </el-row>
                    <center>
                        <h2>暂无更多海报</h2>
                    </center>

                </el-collapse-item>
            </el-collapse>
        </div>
    </div>

</template>

<script>
    export default {
        name: "poster",
        data() {
            return {
                posterActivity: {
                    billName: null,
                    billDesc: null,
                },
                rules: {
                    billName: [
                        {required: true, message: '请输入活动名称', trigger: 'blur'},
                        {min: 3, max: 10, message: '长度在 3 到 10 个字符', trigger: 'blur'}
                    ],
                    billDesc: [
                        {required: true, message: '请输入活动内容', trigger: 'blur'},
                        {min: 3, max: 15, message: '长度在 3 到 15 个字符', trigger: 'blur'}
                    ],
                },
                posterRules: {
                    name: [
                        {required: true, message: '请输入海报名称', trigger: 'blur'},
                        {min: 3, max: 10, message: '长度在 3 到 10 个字符', trigger: 'blur'}
                    ],
                    detail: [
                        {required: true, message: '请输入海报说明', trigger: 'blur'},
                        {min: 3, max: 15, message: '长度在 3 到 20 个字符', trigger: 'blur'}
                    ],

                },
                posterDialogVisible: false,
                dialogVisible: false,
                posterParamList: [],
                activeNames: [],
                activityList: [],
                posterList: [],
                active: 0,
                form: {
                    billName: null,
                },
                poster: {
                    type: 0,
                    name: null,
                    detail: null,
                    bill: null,
                    billUrl: null,
                    billActivityId: null,
                    posterParamIds: [],
                }
            }
        },
        created() {
            this.getPosterParamList();
            this.posterActivityList();
        },
        methods: {
            handleAvatarSuccess(res, file) {
                this.poster.bill = res.data;
            },
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        if (formName === 'ruleForm') {
                            this.savePosterActivity(this.posterActivity)
                        }
                        if (formName === 'poster') {
                            if (this.poster.bill == null) {
                                return this.$message.error("请上传海报底图");
                            }
                            this.insertPoster(this.poster)
                        }
                        // this.savePosterActivity(this.posterActivity)
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            resetForm(formName) {
                this.$refs[formName].resetFields();
            },
            savePoster() {
                this.$message.error("查询失败，");
            },
            deleltePoster(item) {
                this.$confirm("是否确认此操作, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    this.$postData("delPoster", item, {}).then(res => {
                        if (res.status === 200) {
                            this.$message.success("操作成功");
                            this.getPosterList();
                        } else {
                            this.$message.error("操作失败，" + res.msg);
                        }
                    })
                }).catch(() => {
                    // this.$message({type: "info", message: "已取消操作"});
                });
            },
            handleChange(name) {
                // console.log(name);
                //this.poster.billActivityId=name
                this.getPosterList();
                // console.log(name);
            },
            getPosterList() {
                this.$postData("posterList", this.poster, {}).then(res => {
                    if (res.status === 200) {
                        this.posterList = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            insertPoster(form) {
                this.$postData("insertPoster", form, {}).then(res => {
                    if (res.status === 200) {
                        this.posterDialogVisible = false;
                        this.$message.success("保存成功");
                        this.resetForm('poster');
                        this.getPosterList();

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            savePosterActivity(form) {
                this.$postData("savePosterActivity", form, {}).then(res => {
                    if (res.status === 200) {
                        this.dialogVisible = false;
                        this.$message.success("保存成功");
                        this.posterActivityList();

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            savePoster(form) {
                this.$postData("savePoster", form, {}).then(res => {
                    if (res.status === 200) {
                        this.$message.success("保存成功");
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            posterActivityList() {
                this.$postData("posterActivityList", this.form, {}).then(res => {
                    if (res.status === 200) {
                        this.activityList = res.data;
                        // if (this.activityList!==null){
                        //     this.poster.billActivityId=this.activityList[0].id
                        //     this.getPosterList()
                        // }
                        this.getPosterList();
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getPosterParamList() {
                this.$postData("posterParamList", {}, {}).then(res => {
                    if (res.status === 200) {
                        this.posterParamList = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            onSearch() {
                this.getPosterList();
            }
        }
    }
</script>

<style scoped>
    .time {
        font-size: 13px;
        color: #999;
    }

    .bottom {
        margin-top: 13px;
        line-height: 12px;
    }

    .button {
        float: right;
        margin-left: 5px;
    }

    .image {
        width: 100%;
        display: block;
    }

    .clearfix:before,
    .clearfix:after {
        display: table;
        content: "";
    }

    .clearfix:after {
        clear: both
    }
</style>
