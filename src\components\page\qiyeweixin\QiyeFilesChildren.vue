<template>
  <div>
    <div style="margin: 10px 10px 10px 10px">
      <el-button type="primary" @click="directoryDialogVisible = true">新增目录</el-button>
      <el-button type="primary" @click="dialogVisible = true">新增文件</el-button>
      <el-button type="info" @click="getdata">刷新</el-button>
    </div>

    <div  v-for="(item,index) in dataList" :key="index" style="float: left; margin-left: 10px;margin-bottom: 10px">
      <el-card :body-style="{ padding: '0px'}" shadow="hover" v-if="item.fileType==1">
        <div @click="openCard(item)">
          <img src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/icon/icon-document.png" class="image">
          <div style="padding: 14px;">
            <span>{{item.name}}</span>
            <div class="bottom clearfix">
              <time class="time">{{ item.createTime }}</time>
            </div>
          </div>
        </div>
        <div style="padding: 14px;">
          <el-popconfirm title="确定要删除改文件夹？请确保文件里没有文件内容" @confirm="deleteCatalog(item.id)">
            <el-button type="danger" slot="reference">删除</el-button>
          </el-popconfirm>
        </div>
      </el-card>
    </div>

    <div  v-for="(item,index) in dataList" :key="index" style="float: left; margin-left: 10px;margin-bottom: 10px">
      <el-card :body-style="{ padding: '0px'}" shadow="hover" v-if="item.fileType==2">
        <div>
          <img :src="item.coverUrl" class="image">
          <div style="padding: 14px;">
            <span>{{item.name}}</span>
            <div class="bottom clearfix">
              <time class="time">{{ item.createTime }}</time>
            </div>
          </div>
        </div>
        <div style="padding: 14px;">
          <el-popconfirm title="确定要删除文件？删除后不可恢复" @confirm="deleteCatalog(item.id)">
            <el-button type="danger" slot="reference">删除</el-button>
          </el-popconfirm>
          <el-button type="success" @click="downloadFile(item.fileUrl)">下载文件</el-button>
        </div>
      </el-card>
    </div>

    <el-dialog
        title="新增文件目录"
        :visible.sync="directoryDialogVisible"
        width="30%">
      文件夹名称
      <el-input v-model="directoryQiyeFiles.name" placeholder="请输入文件夹名称,不允许重复"></el-input>
      <span slot="footer" class="dialog-footer">
    <el-button @click="directoryDialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="addcatelog">确 定</el-button>
  </span>
    </el-dialog>

    <el-dialog
        title="新增文件"
        :visible.sync="dialogVisible"
        width="30%">
      <el-form >
        <el-form-item label="文件名">
        <el-input v-model="qiyeFiles.name"></el-input>
        </el-form-item>
        <el-form-item label="预览图">

          <el-upload
              action="https://api.xiaoyujia.com/system/imageUpload/"
              :before-upload="beforeUploadImg"
              :file-list="fileimgList"
              :on-remove="imgRemove"
              :on-success="imgSuccess"
              :limit="1"
              accept=".jpg,.png"
              list-type="picture">
            <el-button size="small" type="primary">点击上传预览图</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过3M</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="文件资源">
          <el-upload
              :before-upload="beforeUploadFile"
              action="https://api.xiaoyujia.com/system/imageUpload/"
              :file-list="filesList"
              :on-success="filesSuccess"
              :on-remove="fileRemove"
              :limit="1">
            <el-button size="small" type="primary">点击上传文件</el-button>
            <div slot="tip" class="el-upload__tip">文件大小不可超过512M</div>
          </el-upload>
        </el-form-item>
      </el-form>



      <span slot="footer" class="dialog-footer">
    <el-button @click="dialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="addChildrenFile">确 定</el-button>
  </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "QiyeFilesChildren",
  data() {
    return {
      dataList:[],
      dialogVisible:false,
      directoryDialogVisible:false,
      qiyeFiles:{
        creater:localStorage.getItem('id'),
        name:'',
        childrenId:null,
        fileUrl:'',
        coverUrl:'',
        nowLevel: null,
        fileType: 2
      },
      directoryQiyeFiles:{
        creater:localStorage.getItem('id'),
        name:'',
        childrenId: null,
        nowLevel: null,
        fileType: 1
      },
      fileimgList:[],
      filesList:[]
    }


  },
  created() {
    this.getdata();
  },
  methods:{
    openCard(item){
      this.$router.push({path:'/qiyeFilesChildren',query:{"id":item.id,"nowLevel": item.nowLevel}})
      this.getdata();
    },
    addcatelog(){
      if (!this.directoryQiyeFiles.name){
        this.$message.error('名称不能为空');
      }

      this.$postData("addCatalog", this.directoryQiyeFiles, {}).then(res => {
        if ( res.data && res.data > 0){
          this.$message.success('操作成功');
          this.directoryQiyeFiles.name = '';
          this.directoryDialogVisible = false;
          this.getdata();
        } else {
          this.$message.error(res.msg);
        }
      })

    },
    getdata(){
      this.fileimgList=[]
      this.filesList=[]
      this.directoryQiyeFiles.nowLevel=this.$route.query.nowLevel+1
      this.directoryQiyeFiles.childrenId=this.$route.query.id
      this.directoryQiyeFiles.name=''
      this.qiyeFiles.nowLevel=this.$route.query.nowLevel+1
      this.qiyeFiles.childrenId=this.$route.query.id
      this.qiyeFiles.name=''
      this.qiyeFiles.fileUrl=''
      this.qiyeFiles.coverUrl=''
      this.$getData("getQiyeFilesByChildrenId", {id:this.$route.query.id,nowLevel:parseInt(this.$route.query.nowLevel)+1},null).then(res => {
        if (res.status == 200) {
          this.dataList = res.data;
        } else {
          this.$message.error("查询失败，" + res.msg);
        }

      })
    },
    imgSuccess(obj){
     console.log(obj)
      this.qiyeFiles.coverUrl = obj.data;
    },
    filesSuccess(obj){
      console.log(obj)
      this.qiyeFiles.fileUrl = obj.data;
    },
    imgRemove(){
      this.qiyeFiles.coverUrl = '';
    },
    fileRemove(){
      this.qiyeFiles.fileUrl = '';
    },
    beforeUploadImg(file){
      const fileSuffix = file.name.substring(file.name.lastIndexOf(".") + 1);
      const whiteList = ["jpg", "png"];
      const isSuffix = whiteList.indexOf(fileSuffix) === -1;
      if (isSuffix) {
        this.$message.error('文件格式错误');
        /* 验证不通过删除此文件 */
        const currIdx = this.fileimgList.indexOf(file);
        this.fileimgList.splice(currIdx, 1);
        return false;
      }
    console.log(file)
    },
    beforeUploadFile(file){
      const isLt100M = file.size / 1024 / 1024 > 512;
      if (isLt100M) {
        this.$message.error('上传文件大小不能超过 512MB!');
        return false;
      }
    },
    addChildrenFile(){
      if (!this.qiyeFiles.name || !this.qiyeFiles.coverUrl || !this.qiyeFiles.fileUrl){
        this.$message.error('参数不能为空');
      }
      this.$postData("addChildrenFile", this.qiyeFiles, {}).then(res => {
        if ( res.data && res.data > 0){
          this.$message.success('操作成功');
          this.qiyeFiles.name = '';
          this.qiyeFiles.coverUrl = '';
          this.qiyeFiles.fileUrl = '';
          this.dialogVisible = false;
          this.getdata();
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    deleteCatalog(id){
      this.$postData("deleteCatalog",{id:id}, {}).then(res => {
        if ( res.data && res.data > 0){
          this.$message.success('删除成功');
          this.getdata();
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    downloadFile(url){
      return window.location.href = url;
    }

  }
}
</script>

<style scoped>
.time {
  font-size: 13px;
  color: #999;
}

.bottom {
  margin-top: 13px;
  line-height: 12px;
}

.image {
  width: 150px;
  height: 150px;
  display: block;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}
</style>
