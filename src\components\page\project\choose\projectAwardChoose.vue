<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col span="24">
                    <div>
                        <div id="operBtnDiv">
                            <div id="searchDiv">
                                <Form ref="formItem" :model="formItem" :label-width="80" label-position="left"
                                      :rules="ruleValidate">

                                    <FormItem label="费用类型" prop="type">
                                        <Select v-model="formItem.type" filterable @on-change="onChange">
                                            <Option v-for="item in cityList1" :value="item.value" :key="item.value">{{ item.label}}</Option>
                                        </Select>
                                    </FormItem>

                                    <FormItem label="项目名称" prop="projectId">
                                        <Select v-model="formItem.projectId" filterable>
                                            <Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label}}</Option>
                                        </Select>
                                    </FormItem>

                                    <FormItem label="奖励金额" prop="money" v-if="isShow">
                                        <Input placeholder="请输入收入金额" v-model="formItem.money" @on-change="getNegative">
                                        </Input>
                                    </FormItem>

                                    <FormItem label="惩罚金额" prop="money" v-if="!isShow">
                                        <Input placeholder="请输入支出金额" v-model="formItem.money" @on-change="getNegative">
                                        </Input>
                                    </FormItem>

                                    <FormItem label="费用名称" prop="costName">
                                        <Input placeholder="请输入费用名称" v-model="formItem.costName">
                                        </Input>
                                    </FormItem>

                                    <FormItem label="结算时间" prop="settlementTime">
                                        <DatePicker type="date" placeholder="请选择结算时间" v-model="formItem.settlementTime">
                                        </DatePicker>
                                    </FormItem>

                                    <FormItem>
                                        <Button type="primary" @click="saveProjectAward('formItem')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </FormItem>
                                </Form>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
    </div>
</template>


<script>
    import employeeChoose from '@/components/page/project/choose/employeeChoose.vue'


    export default {
        data() {
            const validateSequence = (rule, value, callback) => {
                if (value === '') {
                    callback(new Error('请输入金额'));
                } else if (!Number.isInteger(+value)) {
                    callback(new Error('请输入数字'));
                }
            };
            return {
                isShow: true,
                usersModal: false,
                userLoading: false,
                screenWidth: '30%',
                cityList: [],
                cityList1: [
                    {
                        value: '1',
                        label: '奖励'
                    },
                    {
                        value: '2',
                        label: '惩罚'
                    },
                ],
                formItem: {
                    employeeId: null,   //负责人id
                    name: null,    //项目名称
                    type: "1",
                    money: null,
                    costName: null,
                    projectId: null,
                    projectNo: null,
                    employeeName: null,
                    settlementTime: null
                },
                projectManage: {
                    projectId: null,
                    name: null,
                    state:1,
                },
                ruleValidate: {
                    projectId: [
                        {required: true, message: '请输入项目名称', trigger: 'change', type: 'number'},
                    ],
                    type: [
                        {required: true, message: '请选择费用类型', trigger: 'change'}
                    ],
                    costName: [
                        {required: true, message: '请输入费用名称', trigger: 'blur'}
                    ],
                    money: [
                        {required: true, message: '请输入金额', trigger: 'blur'},
                        {required: true, pattern: /(^[\-0-9][0-9]*(.[0-9]+)?)$/, message: '请输入数字', trigger: 'change'}
                    ],
                    settlementTime: [
                        { required: true, type: 'date', message: '请选择结算时间', trigger: 'change' }
                    ],
                },


            }
        },
        components: {
            'employeeChoose': employeeChoose
        },
        created: function () {
            this.getData();
        },
        methods: {
            getData() {
                this.$postData("project_selectList", this.projectManage, {}).then(res => {
                    if (res.meta.state == 200) {

                        this.getSelect(res.data.lists);
                    } else {
                        this.$message.error("查询失败，" + res.meta.msg);
                    }
                })
            },

            getSelect(data) {
                console.log(data)
                if (data) {
                    for (let i = 0; i < data.length; i++) {
                        let line = {
                            value: data[i].id,
                            label: data[i].name
                        }
                        this.cityList.push(line)
                    }
                }
                console.log(this.cityList)

            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.usersModal = false;
            },
            saveProjectAward(name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        this.$postData("projectAward_saveProject", this.formItem, {}).then(res => {
                            if (res.meta.state == 200) {
                                this.$Message.success('添加成功');
                                this.chooseThisModel()
                            } else {
                                this.$message.error("添加失败，" + res.meta.msg);
                            }
                        })
                    }
                })
            },
            onChange(value) {
                if (value == 2) {
                    this.isShow = false
                    this.formItem.money = null
                } else {
                    this.isShow = true
                    this.formItem.money = null
                }

            },

            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            },
            /*
            * 得到负数值
            * */
            getNegative() {
                if (this.formItem.type == 2) {
                    if (this.formItem.money.indexOf("-") == -1) {
                        this.formItem.money = "-" + this.formItem.money
                    }
                }
                console.log(this.formItem.money)
            }
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

