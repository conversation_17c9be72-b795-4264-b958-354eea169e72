import axios from 'axios';
import Vue from 'vue'
import api from "./api"


// 微服务接口地址，这个地址导出到 vue.config.js 作为跨域配置的基准地址
// [生产环境]: 'http://apitest.xiaoyujia.com:9999'
// [本地环境]: 'http://localhost:9999'
// [203服务器测试环境]: 'https://api.xiaoyujia.com'

// const baseUrl = 'http://localhost:8085';
const baseUrl = 'http://localhost:8063';
// const baseUrl = 'https://biapi.xiaoyujia.com';

// const otherUrl = 'http://localhost:9999';

const otherUrl = 'https://api.xiaoyujia.com';
// const otherUrl = 'https://apitest.xiaoyujia.com:9999';

axios.defaults.baseURL = baseUrl;

axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8';
axios.defaults.headers.post['Content-Type'] = 'application/json;charset=UTF-8';

axios.defaults.timeout = 200000;
// // 添加请求拦截器(axios请求前)
axios.interceptors.request.use(config => {
    let token = localStorage.getItem('token');
    if (token) {
        config.headers['token'] = token;
    }
    return config;
}, error => {
    // 对请求错误做些什么
    return Promise.reject(error);
});
// 添加响应拦截器(axios请求后)
axios.interceptors.response.use(response => {
    // 对响应数据做点什么
    if (response.data.status === 502 || response.data.status === 401) {
        alert("登录过期。请重新登录！");
        localStorage.clear();
        location.reload();
    }

    if (response.data.meta !== undefined) {
        if (response.data.meta.state === 401) {
            alert("登录过期。请重新登录！");
            localStorage.clear();
            location.reload();
        }
    }

    return response.data;
}, error => {
    console.log(error);
    // 对响应错误做点什么
    return Promise.reject(error);
});

// axios.interceptors.request.use(
//   config => {
//     const token = utils.getLocalStorage('token');
//     console.log(token)
//     if (!config.headers) {
//       if (token) {
//         config.headers = { token };
//       }
//     } else {
//       if (token) {
//         console.log(config)
//         config.headers.token = token;
//       }
//     }

//     return config;
//   },
//   error => {
//     return Promise.resolve(error);
//   }
// );

const postData = (url, data, config = {}) => {
    // return axios.post(api[url], qs.stringify(data));
    return axios.post(api[url], data, config);
};
//  /movie/in_theaters
const getData = (url, params) => {
    return axios.get(api[url], {
        params
    });
};
const postUrl = (url, path, data, config = {}) => {
    // return axios.post(api[url], qs.stringify(data));
    return axios.post(api[url] + path, data, config);
};
const getUrl = (url, path, params) => {
    // return axios.post(api[url], qs.stringify(data));
    return axios.get(api[url] + path, {
        params
    });
};

// 自定义的url，但是要在 vue.config.js 配置跨域
const postDataSelfUrl = (url, data, config = {}) => {
    // return axios.post(api[url], qs.stringify(data));
    return axios.post(url, data, config);
};

const getDataSelfUrl = (url, params) => {
    return axios.get(url, {params});
};

Vue.prototype.$postDataSelfUrl = postDataSelfUrl;
Vue.prototype.$getDataSelfUrl = getDataSelfUrl;
Vue.prototype.$postData = postData;
Vue.prototype.$postUrl = postUrl;
Vue.prototype.$getData = getData;
Vue.prototype.$getUrl = getUrl;


// axios.post('/v2/movie/in_theaters', qs.stringify({
//   num: 1
// })).then(result => {
//   console.log(result)
// })

// axios.post('/v2/movie/in_theaters12313', qs.stringify({
//   num: 14564
// })).then(result => {
//   console.log(result)
// })


// 发送 POST 请求
// axios({
//     method: 'post',
//     url: '/v2/movie/in_theaters',
//     data: {s
//       num:1
//     }
//   });

//   axios({
//     method: 'get',
//     url: '/v2/movie/in_theaters',
//     params: {
//       num:1
//     }
//   });

export default {
    postDataSelfUrl, getDataSelfUrl, postData, getData, postUrl, getUrl, otherUrl
};
