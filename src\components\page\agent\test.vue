<template>
    <div class="input-card" style='width:28rem;'>
        <label style='color:grey'>地理编码，根据地址获取经纬度坐标</label>
        <div class="input-item">
            <div class="input-item-prepend"><span class="input-item-text" >地址</span></div>
            <el-input placeholder="请输入地址" v-model="address"  style="width: 70%"  id='address' @change="geoCode">
            </el-input>
        </div>
        <div class="input-item">
            <div class="input-item-prepend"><span class="input-item-text">lng</span></div>
            <el-input placeholder="请输入经纬度" v-model="lng"  style="width: 70%"  id='lng'>
            </el-input>
            <div class="input-item-prepend"><span class="input-item-text">lat</span></div>
            <el-input placeholder="请输入经纬度" v-model="lat"  style="width: 70%"  id='lat'>
            </el-input>
        </div>
        <input id="geo" type="button" class="btn" value="地址 -> 经纬度" @click="geoCode"/>
    </div>

</template>

<script>
    export default {
        name: "test",
        data() {
            return {
                address:'北京市朝阳区阜荣街10号',
                lnglat:null,
                lng:null,
                lat:null,

            }
        },
        created(){

        },
        methods: {
            geoCode() {
                var geocoder = new AMap.Geocoder({
                    city: "", //城市设为北京，默认：“全国”
                });
                var address  = this.address;
                geocoder.getLocation(address, function(status, result) {
                    if (status === 'complete'&&result.geocodes.length) {
                        document.getElementById('lat').value = result.geocodes[0].location.lat;
                        document.getElementById('lng').value = result.geocodes[0].location.lng;

                    }else{
                        log.error('根据地址查询位置失败');
                    }
                });
            }
        }
    }
</script>

<style scoped>

</style>
