<template>
<div>
  <el-tabs type="border-card">
    <el-tab-pane label="员工积分扣分排行">
      <div class="block">
        <span class="demonstration">扣分时间</span>
        <el-date-picker v-model="days" type="daterange" @change="screen"
             :editable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </div>
      <el-table :data="list" class="table" v-loading="loading"
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
        <el-table-column label="排名" prop="" width="60">
          <template slot-scope="scope">
            <span v-text="getIndex(scope.$index)"> </span>
          </template>
        </el-table-column>
        <el-table-column prop="employeeNo" width="80" label="员工号">
        </el-table-column>
        <el-table-column width="100" prop="department" label="所属部门">
        </el-table-column>
        <el-table-column width="70" prop="totalDeduction" label="总扣分">
        </el-table-column>
        <el-table-column width="70" prop="score" label="剩余分">
        </el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="pageInfo.total"
              @on-change="onChange"
              :current="this.dto.current"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onPageSizeChange"
              :page-size="10"/>
      </div>
    </el-tab-pane>
    <el-tab-pane label="员工好评订单排行">
      <el-table :data="list2" class="table" v-loading="loading2"
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
        <el-table-column label="排名" prop="" width="60">
          <template slot-scope="scope">
            <span v-text="getIndex2(scope.$index)"> </span>
          </template>
        </el-table-column>
        <el-table-column prop="count" width="80" label="好评单数">
        </el-table-column>
        <el-table-column width="100" prop="no" label="员工号">
        </el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="pageInfo2.total"
              @on-change="onChange2"
              :current="this.dto2.current"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts2"
              @on-page-size-change="onPageSizeChange2"
              :page-size="10"/>
      </div>
    </el-tab-pane>
  </el-tabs>
</div>
</template>

<script>
export default {
  name: "praiseOrder",
  data() {
    return {
      list: null,
      loading: false,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      days: [],
      dto: {
        startTime: null,
        endTime: null,
        size: 10,
        current: 1,
      },

      list2: null,
      loading2: false,
      pageSizeOpts2: [10, 20, 30],
      pageInfo2: {total: 10, size: 10, current: 1, pages: 1},
      dto2: {
        startTime: null,
        endTime: null,
        size: 10,
        current: 1,
      },
    };
  },
  created() {
    this.employeeDeductPointsRanking();
    this.getServiceAcceptanceForm();
  },
  methods: {
    screen(){
      this.employeeDeductPointsRanking();
      this.getServiceAcceptanceForm();
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dto.size = size;
      this.employeeDeductPointsRanking();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dto.current = index;
      this.employeeDeductPointsRanking();
    },
    //获取表格序号
    getIndex($index) {
      return (this.pageInfo.current - 1) * this.pageInfo.size + $index + 1
    },
    employeeDeductPointsRanking(){
      if (this.days != null && this.days.length > 0) {
        this.dto.startTime = this.days[0];
        this.dto.endTime = this.days[1];
      }else {
        this.dto.startTime =null;
        this.dto.endTime =null;
      }
      this.$postData("employeeDeductPointsRanking", this.dto).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    getServiceAcceptanceForm(){
      if (this.days != null && this.days.length > 0) {
        this.dto2.startTime = this.days[0];
        this.dto2.endTime = this.days[1];
      }else {
        this.dto2.startTime =null;
        this.dto2.endTime =null;
      }
      this.$postData("getServiceAcceptanceForm", this.dto2).then(res => {
        this.loading2 = false;
        if (res.status === 200) {
          this.list2 = res.data.records;
          this.pageInfo2.current = res.data.current;
          this.pageInfo2.size = res.data.size;
          this.pageInfo2.total = res.data.total
        }
      })
    },
    // 页码大小
    onPageSizeChange2(size) {
      this.loading2 = true;
      this.dto2.size = size;
      this.getServiceAcceptanceForm();
    },
    // 跳转页码
    onChange2(index) {
      this.loading2 = true;
      this.dto2.current = index;
      this.getServiceAcceptanceForm();
    },
    //获取表格序号
    getIndex2($index) {
      return (this.pageInfo2.current - 1) * this.pageInfo2.size + $index + 1
    },
  },
}
</script>

<style scoped>

</style>