import Vue from 'vue'
import App from './App.vue'
import router from './router'
import axios from 'axios';
import ElementUI from 'element-ui';
import VueRouter from 'vue-router'
import VueI18n from 'vue-i18n';
import { messages } from './components/common/i18n';
import 'element-ui/lib/theme-chalk/index.css'; // 默认主题
// import '../static/css/theme-green/index.css';       // 浅绿色主题
import './assets/css/icon.css';
import './components/common/directives';
import "babel-polyfill";
import './config/axios';
import iView from 'iview';


import 'iview/dist/styles/iview.css';
import GoEasy from 'goeasy';

Vue.use(iView);

import VueClipboard from 'vue-clipboard2'

Vue.use(VueClipboard);

//图片放大组件
import VueDirectiveImagePreviewer from 'vue-directive-image-previewer'
import 'vue-directive-image-previewer/dist/assets/style.css'
Vue.use(VueDirectiveImagePreviewer)

import JsonExcel from 'vue-json-excel'

Vue.component('downloadExcel', JsonExcel);

import VideoPlayer from 'vue-video-player'
import 'video.js/dist/video-js.css'
Vue.use(VideoPlayer);

import ECharts from 'vue-echarts/components/ECharts'
import echarts from 'echarts' //引入echarts
import VueAMap from 'vue-amap'
Vue.use(VueAMap);
import htmlToPdf from "./components/common/htmlToPdf";
Vue.use(htmlToPdf)

import VueQuillEditor from 'vue-quill-editor'

// require styles
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'

Vue.use(VueQuillEditor)
import Quill from "quill";
import ImageResize from "quill-image-resize-module"; // 引用
import { ImageDrop } from "quill-image-drop-module";
Quill.register("modules/imageDrop", ImageDrop);
Quill.register("modules/imageResize", ImageResize); // 注册

// 初始化vue-amap
VueAMap.initAMapApiLoader({
    key: 'bbbf8bd84dcb9edbf4d0c147ac124622',
    //'AMap.Scale', 'AMap.OverView', 'AMap.ToolBar', 'AMap.MapType', 'AMap.PlaceSearch',
    plugin: ['AMap.Geocoder','AMap.Scale', 'AMap.OverView', 'AMap.ToolBar', 'AMap.MapType', 'AMap.PlaceSearch', 'AMap.Geolocation','AMap.Transfer','AMap.Autocomplete'],
    uiVersion: '1.1' // 版本号
})
Vue.prototype.$echarts = echarts //引入组件
Vue.component('chart', ECharts)

Vue.config.productionTip = false
Vue.use(VueI18n);
Vue.use(ElementUI, {
    size: 'small'
    , zIndex: 3000
});
const routers = new VueRouter({
    mode: 'history',
    routes: routers
});

Vue.prototype.$axios = axios;

const i18n = new VueI18n({
    locale: 'zh',
    messages
})

//使用钩子函数对路由进行权限跳转
router.beforeEach((to, from, next) => {
    const token = localStorage.getItem('token');
    if( to.path === '/display'){
        next();
    }
    else if (!token && to.path !== '/login') {
        next('/login');
    } else {
        // 简单的判断IE10及以下不进入富文本编辑器，该组件不兼容
        if (navigator.userAgent.indexOf('MSIE') > -1 && to.path === '/editor') {
            Vue.prototype.$alert('vue-quill-editor组件不兼容IE10及以下浏览器，请使用更高版本的浏览器查看', '浏览器不兼容通知', {
                confirmButtonText: '确定'
            });
        } else {
            if (to.meta.title) {
                document.title = to.meta.title
            }
            next();
        }
    }
})


//创建全局GoEasy对象
Vue.prototype.$goEasy = new GoEasy({
    host: 'hangzhou.goeasy.io',
    appkey: "BC-f04ea3b3f2804ac1a8b49d0dde3f8e4e",
    forceTLS: true,
    onConnected: function() {
        console.log('连接成功！')
    },
    onDisconnected: function() {
        console.log('连接断开！')
    },
    onConnectFailed: function(error) {
        console.log('连接失败或错误！')
    }
});


new Vue({
    router,
    i18n,
    el: '#app',
    render: h => h(App)
}).$mount('#app')
