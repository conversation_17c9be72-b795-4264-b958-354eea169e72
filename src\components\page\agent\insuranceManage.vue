<template>
  <div class="container">
    <div class="handle-box">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="订单号">
          <el-input v-model="dom.billNo" placeholder="订单号" clearable
                    @keyup.enter.native="onQuery"></el-input>
        </el-form-item>

        <el-form-item label="选择门店" v-if="roleId == 1">
          <el-select ref="storeNameSel"  filterable v-model="dom.storeId" placeholder="请选择">
            <el-option
                v-for="(item, index) in storeList"
                :key="index"
                :label="item.storeName"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="onQuery" v-loading="loading">查询</el-button>
        </el-form-item>
      </el-form>


      <el-table :data="rowData" height="500px" v-loading="loading"
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">

        <el-table-column
            label="序号"
            type="index"
            width="80"
            align="center">
        </el-table-column>
        <el-table-column
            prop="billNo"
            label="上保订单号"
            align="center">
        </el-table-column>
        <el-table-column
            prop="employeeName"
            label="员工姓名"
            align="center">
        </el-table-column>
        <el-table-column
            label="投保状态"
           >
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.callBackStatus === 8">成功</el-tag>
            <el-tag type="danger" v-if="scope.row.callBackStatus === 15">失败</el-tag>
          </template>
        </el-table-column>
        <el-table-column
            label="保单链接"
           >
          <template slot-scope="scope">
            <a :href="scope.row.policyUrl">查看</a>
          </template>
        </el-table-column>
        <el-table-column
            prop="upTime"
            label="上保时间"
            align="center">
        </el-table-column>
        <el-table-column
          prop="oldStoreAmount"
          label="上保前门店余额"
          align="center">
      </el-table-column>
        <el-table-column
            prop="storeAmount"
            label="上保后门店余额"
            align="center">
        </el-table-column>


      </el-table>
      <div class="pagination">
        <Page :total="pageInfo.total"
              @on-change="onChangePage"
              :current="dom.current"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onPageSizeChange"
              :page-size="dom.size"/>
      </div>

  </div>
  </div>
</template>

<script>

export default {
  name: "insuranceManage",
  data() {
    return {
      roleId: localStorage.getItem('roleId'),
      loading: false,
      storeList:[],
      dom: {
        billNo:'',
        size: 15,
        current: 1,
        storeId:localStorage.getItem('storeId')
      },

      pageSizeOpts: [10, 15, 20, 30, 50, 100],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      rowData: [],
    }
  },
   created() {
    this.onQuery();
    this.getStoreList();
  },
  mounted() {

  },
  methods: {
    getStoreList(){

      this.$getData("getFranchiseStoreList", {}).then((res) => {
        if (res.status === 200) {
          this.storeList = res.data;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    onQuery() {
      this.loading = true;
      this.$postData("contractManagePage", JSON.stringify(this.dom), {}).then((res) => {
        if (res.status === 200) {
          this.rowData = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total;
          this.loading = false;
        } else {
          this.$message.error(res.msg);
          this.loading = false;
        }
      });
    },
    onChangePage(index) {
      this.dom.current = index;
      this.onQuery();
    },
    onPageSizeChange(size) {
      this.loading = true;
      this.dom.size = size;
      this.onQuery();
    },


  }
}
</script>

<style>

</style>
