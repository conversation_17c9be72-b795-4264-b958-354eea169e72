<template>
  <div>
    <div style="margin: 10px 10px 10px 10px">
      <el-button type="primary" @click="dialogVisible = true">新增目录</el-button>
      <el-button type="info" @click="getdata">刷新</el-button>
    </div>
      <div  v-for="(item,index) in dataList" :key="index" style="float: left; margin-left: 10px;margin-bottom: 10px">
        <el-card :body-style="{ padding: '0px'}" shadow="hover">
          <div @click="openCard(item)">
            <img src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/icon/icon-document.png" class="image">
            <div style="padding: 14px;">
              <span>{{item.name}}</span>
              <div class="bottom clearfix">
                <time class="time">{{ item.createTime }}</time>
              </div>
            </div>
          </div>
          <div style="padding: 14px;">
            <el-popconfirm title="确定要删除改文件夹？请确保文件里没有文件内容" @confirm="deleteCatalog(item.id)">
              <el-button type="danger" slot="reference">删除</el-button>
            </el-popconfirm>
              <el-button type="primary " style="margin-left: 30px;" @click="updateCataLog(item)" >编辑</el-button>
          </div>
        </el-card>
      </div>

    <el-dialog :visible.sync="certModal" width="60%" title="编辑文件夹" :mask-closable="false">
      <div style="height: 400px;overflow: hidden;overflow-y: scroll;">
        <el-row style="width:100%;height: auto;margin-bottom: 0px;">
          <el-col :span="12">
            <el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">

              <el-form-item label="文件名称：">
                <el-input v-model="updateFiles.name" type="textarea" class="handle-input mr10"
                          placeholder="请输入文件名称">
                </el-input>
              </el-form-item>

              <el-form-item label="权限管理：">
                <el-select v-model="selectRoles" filterable  multiple placeholder="针对角色展示分类">
                  <el-option
                      v-for="item in roleList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <div style="margin: 0 400px;width: 100%;">
          <el-button @click="updateQiyeFiles()" type="success" size="small">保存修改
          </el-button>
        </div>

      </div>
    </el-dialog>

    <el-dialog
        title="新增文件目录"
        :visible.sync="dialogVisible"
        width="30%">
        文件夹名称
      <el-input v-model="qiyeFiles.name" placeholder="请输入文件夹名称,不允许重复"></el-input>
      <span slot="footer" class="dialog-footer">
    <el-button @click="dialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="addcatelog">确 定</el-button>
  </span>
    </el-dialog>


  </div>
</template>

<script>
export default {
  name: "qiyeFiles",
  data() {
    return {
      dataList:[],
      roleList:[],
      selectRoles:[],
      updateFiles:{},
      certModal: false,
      dialogVisible:false,
      qiyeFiles:{
        name:'',
        nowLevel:0,
        childrenId: 0,
        creater:localStorage.getItem('id')

      }

    }


  },
  created() {
    this.getdata();
  },
  methods:{
    updateQiyeFiles(){
      if(!this.updateFiles.name){
        return  this.$message.error('名称不能为空！');
      }
      if(!this.selectRoles){
        return this.$message.error('请设置查看权限后保存修改！');
      }
      this.$set(this.updateFiles,'roleIdList',this.selectRoles)
      this.$postData("updateQiyeFile",this.updateFiles, {}).then(res => {
        if ( res.status == 200){
          this.$message.success('编辑成功');
          this.certModal = false
          this.getdata();
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    updateCataLog(item){
      this.selectRoles = []
      this.getAllRole()
      this.certModal = true
      this.updateFiles = item
      let arr = item.roles.split(",");
      for (let i = 0; i < arr.length; i++) {
        this.selectRoles.push(parseInt(arr[i]));
      }
    },
    getAllRole(){
      this.$getData("getAllRole").then(res => {
        let resultArr = [];
        res.data.forEach(v =>{
          let param = {
            value:v.id,
            label:v.name
          }
          resultArr.push(param);
        })
        this.roleList = resultArr;
      });
    },
    getdata(){
      this.$getData("getQiyeFileList", null,null).then(res => {
        if (res.status == 200) {
          this.dataList = res.data;
        } else {
          this.$message.error("查询失败，" + res.msg);
        }

      })
    },
    openCard(item){
      this.$router.push({path:'/qiyeFilesChildren',query:{"id":item.id,"nowLevel": item.nowLevel}})
    },
    addcatelog(){
      if (!this.qiyeFiles.name){
        this.$message.error('名称不能为空');
      }

      this.$postData("addCatalog", this.qiyeFiles, {}).then(res => {
        if ( res.data && res.data > 0){
          this.$message.success('操作成功');
          this.qiyeFiles.name = '';
          this.dialogVisible = false;
          this.getdata();
        } else {
          this.$message.error(res.msg);
        }
      })

    },
    deleteCatalog(id){
      this.$postData("deleteCatalog",{id:id}, {}).then(res => {
        if ( res.data && res.data > 0){
          this.$message.success('删除成功');
          this.getdata();
        } else {
          this.$message.error(res.msg);
        }
      })
    }

  }
}
</script>

<style scoped>
.time {
  font-size: 13px;
  color: #999;
}

.bottom {
  margin-top: 13px;
  line-height: 12px;
}

.image {
  width: 100%;
  display: block;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}
</style>
