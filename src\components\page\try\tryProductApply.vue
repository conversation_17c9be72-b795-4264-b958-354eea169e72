<template>
    <div class="table">
        <!--<div class="crumbs">-->
            <!--<el-breadcrumb separator="/">-->
                <!--<el-breadcrumb-item>-->
                    <!--<i class="el-icon-lx-cascades"></i> 项目管理-->
                <!--</el-breadcrumb-item>-->
            <!--</el-breadcrumb>-->
        <!--</div>-->
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">
                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="主标题">
                                <el-input
                                        v-model="apply.firstTitle"
                                        placeholder="主标题"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="类型">
                                <Select v-model="apply.type" filterable style="width: 200px">
                                    <Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label}}
                                    </Option>
                                </Select>
                            </el-form-item>
                        </el-col>


                        <el-col :span="6" style="text-align:right">
                            <el-form-item>
                                <el-button type="success" style="width:40%" round @click="exportList" >导出</el-button>
                                <el-button type="success" style="width:40%" round @click="saveModal=true">添加中奖名单</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>

                        <el-col :span="6">
                            <el-form-item label="会员号">
                                <el-input
                                        v-model="apply.number"
                                        placeholder="会员号"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="手机号码">
                                <el-input
                                        v-model="apply.phone"
                                        placeholder="手机号码"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="会员名称">
                                <el-input
                                        v-model="apply.name"
                                        placeholder="会员名称"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6" style="text-align:right">
                            <el-form-item>
                                <el-button type="success" style="width:40%" round @click="query()">搜索</el-button>
                            </el-form-item>
                        </el-col>


                    </el-row>
                </el-form>
            </div>
            <el-table :data="list" border class="table" ref="multipleTable">
                <el-table-column
                        prop="member.account"
                        label="会员号"
                        width="200"
                ></el-table-column>
                <el-table-column
                        prop="name"
                        label="会员名称"
                        width="120"
                ></el-table-column>
                <el-table-column
                        prop="member.registerTime"
                        label="注册时间"
                        width="135"
                ></el-table-column>
                <el-table-column
                        prop="phone"
                        label="手机号码"
                        width="180"
                ></el-table-column>
                <el-table-column
                        prop="tryProduct.firstTitle"
                        label="主标题"
                        width="250"
                ></el-table-column>
                <el-table-column
                        prop="tryProduct.typeDesc"
                        label="类型"
                        width="140"
                ></el-table-column>

                <el-table-column
                        prop="isWinDesc"
                        label="是否中奖"
                        width="120"
                ></el-table-column>
                <el-table-column
                        label="操作"
                        width="130">
                    <template slot-scope="scope">
                        <el-button v-if="!scope.row.in&&scope.row.isWin==1" size="mini"
                                   @click="binding(scope.row.id,scope.row.member.account,scope.row.name,scope.row.member.id)"
                                   type="primary">绑定优惠券</el-button>
                    </template>

                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>
        <!--添加-->
        <Modal v-model="saveModal" class="Modal" :width="screenWidth"  title="添加" :mask-closable="false" >
            <div class="addBody">

                <try-product-apply-add v-if="saveModal" @init-choose="initChooseProject"
                                @close-modal="closeCurrModal"></try-product-apply-add>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <!--绑定优惠券-->
        <Modal v-model="bindModal" class="Modal" :width="screenWidth"  title="绑定优惠券" :mask-closable="false" >
            <div class="addBody">

                <try-product-apply-binding v-if="bindModal" @init-choose="initChooseProject" :binds="binds"
                                       @close-modal="closeCurrModal"></try-product-apply-binding>
            </div>
            <div slot="footer">
            </div>
        </Modal>
    </div>
</template>

<script>
    import tryProductApplyBinding from '@/components/page/try/choose/tryProductApplyBinding.vue'
    import tryProductApplyAdd from '@/components/page/try/choose/tryProductApplyAdd.vue'
    import {formatDate} from '@/components/common/utils.js'
    export default {
        data() {
            return {
                productId:null,
                pageSizeOpts:[10,20,30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                screenWidth: '35%',//新增对话框 宽度
                saveModal: false,
                bindModal:false,
                binds:null,
                apply: {
                    firstTitle: null,
                    account: null,
                    type: null,
                    pageSize:10,
                    pageNum:1,
                    name:null,
                    phone:null,
                },
                cityList: [
                    {
                        value: '',
                        label: '请选择'
                    },
                    {
                        value: '1',
                        label: '试吃'
                    },
                    {
                        value: '2',
                        label: '试用'
                    },
                ],
                list:null
            };
        },
        components: {
            'tryProductApplyAdd': tryProductApplyAdd,
            'tryProductApplyBinding': tryProductApplyBinding,
        },
        created() {
            this.getData();
        },
        computed: {
        },
        methods: {
            getData() {
                this.$postData("apply_getByList", this.apply, {}).then(res => {
                    if (res.status == 200) {

                        this.gePage(res) //分页
                        this.list = res.data.list;

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            exportList(){
                this.$postData("apply_export", {}, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tablename: "中奖名单",
                        res: res
                    });
                });
            },
            blobExport({ tablename, res }) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], { type: "application/vnd.ms-excel" });
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
            gePage(res){
                this.pageInfo.current = res.data.pageNum;
                this.pageInfo.size = res.data.pageSize;
                this.pageInfo.total = res.data.total
            },
            query() {
                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                this.apply.pageSize = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                console.log(index)
                this.apply.pageNum = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
                this.bindModal=false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },
            binding(id,account,name,memberId){
                this.bindModal=true;
                let bindList={
                    tryProductApplyId:id,
                    account:account,
                    name:name,
                    memberId:memberId
                }
                this.binds=bindList;
            }
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
