<template>
    <div class="container">
        <div class="handle-box">
            <el-form ref="form" :model="pageInfo">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="服务类别">
                            <Select v-model="region" style="width: 130px">
                                <Option v-for="(item,index) in serverTypeList" :value="item.name" :key="index">
                                    {{ item.name}}
                                </Option>
                            </Select>
                        </el-form-item>
                        <el-form-item label="服务产品">
                            <el-select style="width: 130px" v-model="valueSelect">
                                <el-option
                                        v-for="item in options"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="9" class="block">
                        <el-form-item label="申请时间">
                            <el-date-picker
                                    v-model="quer.applyTime"
                                    type="daterange"
                                    format="yyyy 年 MM 月 dd 日"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    :default-time="['00:00:00', '23:59:59']">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="9" class="block">
                        <el-form-item label="服务时间">
                            <el-date-picker
                                    v-model="quer.serviceTime"
                                    type="daterange"
                                    format="yyyy 年 MM 月 dd 日"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    :default-time="['00:00:00', '23:59:59']">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="9" class="block">
                        <el-form-item label="发放时间">
                            <el-date-picker
                                    v-model="quer.senderTime"
                                    type="daterange"
                                    format="yyyy 年 MM 月 dd 日"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    :default-time="['00:00:00', '23:59:59']">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>

                    <el-button type="primary" @click="query()" icon="el-icon-search" style="margin-left: 20px">搜索
                    </el-button>
                    <el-button icon="el-icon-refresh" @click="reset()" style="margin-left: 20px">
                        重置
                    </el-button>
                    <el-button type="info" :loading="loadingExcel" @click="download" icon="el-icon-download">导出
                    </el-button>
                </el-row>

            </el-form>
        </div>
        <el-table :data="list" height="500px" v-loading="loading" border
                  :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
            <el-table-column
                    prop="billNo"
                    width="150"
                    label="订单号">
            </el-table-column>
            <el-table-column
                    width="70"
                    prop="orderState"
                    label="订单状态">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.orderState=='10'" type="success" disable-transitions>
                        <span>已接单</span>
                    </el-tag>
                    <el-tag v-if="scope.row.orderState=='80'" type="success" disable-transitions>
                        <span>已完成</span>
                    </el-tag>
                    <el-tag v-if="scope.row.orderState=='99'" type="danger" disable-transitions>
                        <span>已取消</span>
                    </el-tag>
                    <el-tag v-if="scope.row.orderState=='90'" disable-transitions>
                        <span>已评价</span>
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    width="110"
                    prop="productName"
                    label="服务项目"
            ></el-table-column>
            <el-table-column
                    width="80"
                    label="是否欠款">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.realTotalAmount==scope.row.amount" type="success">否</el-tag>
                    <el-tag v-else-if="scope.row.realTotalAmount!=scope.row.amount" type="warning">是</el-tag>
                </template>

            </el-table-column>
            <el-table-column
                    width="120"
                    prop="account"
                    label="会员账号">
            </el-table-column>
            <!--      <el-table-column-->
            <!--          width="122"-->
            <!--          label="服务员工">-->
            <!--      <template slot-scope="scope">-->
            <!--        {{scope.row.serviceName}}({{scope.row.serviceNo}})-->
            <!--      </template>-->
            <!--      </el-table-column>-->
            <el-table-column
                    width="200"
                    label="服务地址">
                <template slot-scope="scope">
                    {{scope.row.city}}{{scope.row.area}}{{scope.row.street}}
                </template>
            </el-table-column>
            <el-table-column
                    prop="realTotalAmount"
                    label="订单金额"
                    width="75">

            </el-table-column>
            <el-table-column
                    prop="preferentialAmount"
                    label="优惠金额"
                    width="75">

            </el-table-column>
            <el-table-column
                    width="120"
                    prop="remark"
                    label="订单备注">
            </el-table-column>
            <el-table-column
                    label="快递单号"
                    width="100"
                    prop="expressOrderNo"
            >

            </el-table-column>
            <el-table-column
                    label="是否发放"
                    prop="isSend">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.isSend==1" type="success">是</el-tag>
                    <el-tag v-else-if="scope.row.isSend == null" type="warning">否</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    width="150"
                    label="发放时间"
                    prop="sendTime"
            >

            </el-table-column>
            <el-table-column
                    label="发放人"
                    prop="sender"
            >

            </el-table-column>

            <el-table-column
                    fixed="right"
                    label="操作">
                <template slot-scope="scope">
                    <el-button
                            v-if="scope.row.isSend == null"
                            @click="updateSender(scope.row)"
                            type="text"
                            size="small">发放
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :current="this.dto.current"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="10"/>
        </div>


        <!--编辑-->
        <el-dialog :visible.sync="editModal" width="40%" title="快递单号填写" :mask-closable="false">
            <div>
                <el-input prop="name" v-model="inputExpress" placeholder="请输入快递单号" :validate-event="false"></el-input>
            </div>
            <span slot="footer" class="dialog-footer">

          <el-button @click="editModal = false,inputExpress=''">取 消</el-button>
          <el-button type="primary" @click="dialogVisible = false,addExpress()">确 定</el-button>
       </span>

        </el-dialog>

    </div>

</template>

<script>
    import Vue from 'vue';

    export default {
        name: "experienceManagement",

        data() {
            return {
                serverTypeList: [{name: "居家保洁"}],
                region: '居家保洁',
                inputExpress: null,
                valueSelect: null,
                editModal: false,
                loadingExcel: false,
                options: [{
                    value: '133',
                    label: '居家保洁A26卡'
                }, {
                    value: '229',
                    label: '翡翠保洁指定卡'
                }, {
                    value: '298',
                    label: '保洁指定卡'
                }, {
                    value: '299',
                    label: '居家保洁A5'
                }],

                list: null,
                loading: true,
                pageSizeOpts: [10, 20, 30],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                quer: {
                    applyTime: null,
                    serviceTime: null,
                    senderTime: null,
                },
                dto: {
                    applyStartTime: null,
                    applyEndTime: null,
                    serviceStartTime: null,
                    serviceEndTime: null,
                    senderStartTime: null,
                    senderEndTime: null,
                    productId: null,
                    size: 10,
                    current: 1,
                },
                rowData: null,
                journalInfoLoading: true,
                journalList: null,
                journalInfoSizeOpts: [10, 20, 30],
                journalInfo: {total: 10, size: 10, current: 1, pages: 1},
                journalInfoDrawer: false,

            }
        },
        created() {

            this.getData();
        },
        methods: {

            getData() {
                if (this.valueSelect == null) {
                    this.dto.productId = this.options[0].value;

                } else {
                    this.dto.productId = this.valueSelect
                }
                this.dto.applyStartTime = null
                this.dto.applyEndTime = null
                this.dto.serviceStartTime = null
                this.dto.serviceEndTime = null
                this.dto.senderStartTime = null
                this.dto.senderEndTime = null
                if (this.quer.applyTime != null && this.quer.applyTime.length > 0) {
                    this.dto.applyStartTime = this.quer.applyTime[0];
                    this.dto.applyEndTime = this.quer.applyTime[1] + 1;
                }
                if (this.quer.serviceTime != null && this.quer.serviceTime.length > 0) {
                    this.dto.serviceStartTime = this.quer.serviceTime[0];
                    this.dto.serviceEndTime = this.quer.serviceTime[1] + 1;
                }
                if (this.quer.senderTime != null && this.quer.senderTime.length > 0) {
                    this.dto.senderStartTime = this.quer.senderTime[0];
                    this.dto.senderEndTime = this.quer.senderTime[1];
                }

                this.$postData("getExperienceInfo", this.dto).then(res => {
                    this.loading = false;
                    if (res.status === 200) {
                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                    }
                })
            },
            query() {
                this.loading = true;
                this.dto.current = 1;
                this.getData();
            },
            reset() {
                this.quer.applyTime = [];
                this.quer.serviceTime = [];
                this.quer.senderTime = [];
                this.applyStartTime = [];
                this.applyEndTime = [];
                this.serviceStartTime = [];
                this.serviceEndTime = [];
                this.senderStartTime = [];
                this.senderEndTime = [];
                this.this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                this.loading = true;
                this.dto.size = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                this.loading = true;
                this.dto.current = index;
                this.getData();
            },

            updateSender(val) {
                this.rowData = val;
                this.editModal = true;
            }
            , addExpress() {
                if (this.inputExpress == null || this.inputExpress == '') {
                    return this.$alert('快递单号不能为空')
                }
                let uid = localStorage.getItem("id");
                let uname = localStorage.getItem("realName");
                let date = {
                    sender: uname + "(" + uid + ")",
                    expressNo: this.inputExpress,
                    billNo: this.rowData.billNo
                };

                this.$postData("updateExperienceInfo", date).then(res => {

                    if (res.status === 200) {
                        this.$message({message: '更新成功', type: 'success'});
                    } else {
                        this.$message.error('更新失败');
                    }

                    this.editModal = false;
                    this.inputExpress = null;
                    this.getData();
                })

            },
            download() {
                if (this.valueSelect == null) {
                    this.dto.productId = 133
                } else {
                    this.dto.productId = this.valueSelect
                }
                this.applyStartTime = null
                this.applyEndTime = null
                this.serviceStartTime = null
                this.serviceEndTime = null
                this.senderStartTime = null
                this.senderEndTime = null
                if (this.quer.applyTime != null && this.quer.applyTime.length > 0) {
                    this.dto.applyStartTime = this.quer.applyTime[0];
                    this.dto.applyEndTime = this.quer.applyTime[1];
                }
                if (this.quer.serviceTime != null && this.quer.serviceTime.length > 0) {
                    this.dto.serviceStartTime = this.quer.serviceTime[0];
                    this.dto.serviceEndTime = this.quer.serviceTime[1];
                }
                if (this.quer.senderTime != null && this.quer.senderTime.length > 0) {
                    this.dto.senderStartTime = this.quer.senderTime[0];
                    this.dto.senderEndTime = this.quer.senderTime[1];
                }

                this.loadingExcel = true;
                this.$postData("downloadExperienceInfo", this.dto, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.loadingExcel = false;
                    this.blobExport({
                        tablename: "体验管理",
                        res: res
                    });
                })
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },

        },
    }
</script>

<style scoped>
    .handle-box {
        /*margin-bottom: 10px;*/
        font-weight: bold !important;
    }

    table {
        border-collapse: collapse; /*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
        border-left: #C8B9AE solid 1px;
        border-top: #C8B9AE solid 1px;
    }

    table td {
        border-right: #C8B9AE solid 1px;
        border-bottom: #C8B9AE solid 1px;
    }

    th {
        background: #eee;
        font-weight: normal;
    }

    tr {
        background: #fff;
    }

    tr:hover {
        background: #cc0;
    }
</style>
