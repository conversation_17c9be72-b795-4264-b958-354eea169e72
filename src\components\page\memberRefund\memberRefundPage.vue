<template>
    <div class="table">
        <div class="container">
            <el-form ref="form">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="手机号">
                            <el-input
                                    clearable
                                    v-model="form.bindTel"
                                    placeholder="手机号"
                                    style="width:200px"
                                    class="handle-input mr10"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="订单号">
                            <el-input
                                    clearable
                                    v-model="form.billNo"
                                    placeholder="订单号"
                                    style="width:200px"
                                    class="handle-input mr10"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" style="padding-left: 1rem">
                        <el-form-item label="最后操作时间">
                            <el-date-picker
                                    style="width: 290px;"
                                    v-model="form.lastUpDates"
                                    type="daterange"
                                    :picker-options="pickerOptions"
                                    range-separator="-"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    value-format="yyyy-MM-dd"
                                    align="right">
                            </el-date-picker>&nbsp;
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item>
                            <el-button type="success" @click="form.current=1,getData()">搜索</el-button>
                            <el-button type="primary" plain @click="exportData()">导出</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <el-tabs type="border-card">
                <el-tab-pane label="全部">
                </el-tab-pane>
                <el-table :data="list" border class="table" ref="multipleTable" @cell-dblclick="" v-loading="loading">
                    <el-table-column
                            prop="totalAmount"
                            label="申请金额"
                    >
                        <template slot-scope="scope">
                            ￥<span style="color: #f00f14;font-weight: bold">{{scope.row.totalAmount}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="status"
                            label="状态"
                            width="80">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.refundStatus==0" type="danger">未通过</el-tag>
                            <el-tag v-if="scope.row.refundStatus==90" type="success">已转账</el-tag>
                            <el-tag v-if="scope.row.refundStatus==20" type="success">已通过</el-tag>
                            <el-tag v-else-if="scope.row.refundStatus==1" type="warning">未处理</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="headImg"
                            label="客户"
                            width="280">
                        <template slot-scope="scope">

                            <el-avatar size="small" :src="scope.row.headImg"></el-avatar>
                            <el-divider direction="vertical"></el-divider>
                            <el-link type="primary" @click="toMember(scope.row.memberId)">{{scope.row.bindTel}}
                            </el-link>
                            <el-divider direction="vertical"></el-divider>
                            <el-tag type="danger" effect="dark" v-if="scope.row.level=='6'">VIP客户</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="creatDate"
                            label="填报时间"
                            width="180"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="lastUpDate"
                            label="最后操作时间"
                            width="180"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="billNo"
                            label="订单号"
                            width="180"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="memberRemark"
                            label="客户备注"
                            width="180"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="operatorRemark"
                            label="系统备注"
                            width="180"
                    >
                    </el-table-column>


                    <el-table-column
                            label="操作"
                            fixed="right"
                            min-width="180">
                        <template slot-scope="scope">
                            <el-button
                                    icon="el-icon-s-order"
                                    @click.native.prevent="getMemberRefundLog(scope.row.id),drawerLog=true"
                                    type="text"
                                    size="small">
                                日志
                            </el-button>
                            <el-divider direction="vertical"></el-divider>
                            <el-button
                                    icon="el-icon-s-claim"
                                    @click.native.prevent="showRow(scope.row)"
                                    type="text"
                                    size="small">
                                操作
                            </el-button>

                        </template>

                    </el-table-column>
                </el-table>

            </el-tabs>
            <el-drawer
                    size="60%"
                    title="申请详情"
                    :visible.sync="drawerInfo"
                    :with-header="false">
                <div style="padding: 20px">
                    <div class="memberInfo">
                        <el-avatar size="small" :src="showInfo.headImg"></el-avatar>
                        <el-divider direction="vertical"></el-divider>
                        <el-link style="color: white" @click="toMember(showInfo.memberId)">{{showInfo.bindTel}}
                        </el-link>
                        <el-divider direction="vertical"></el-divider>
                        <el-tag type="danger" effect="dark" v-if="showInfo.level=='6'">VIP客户</el-tag>
                        <el-divider></el-divider>
                        <div>
                            <el-tag v-if="!vdom.verification" type="danger">系统查询未通过</el-tag>
                            <el-tag v-if="vdom.verification" type="success">系统查询通过</el-tag>
                            <span style="color: white;margin-left: 10px">说明：{{vdom.msg}}</span>
                        </div>
                    </div>
                    <div>
                        <el-descriptions class="margin-top" title="基础信息" :column="3" size="medium" border>
                            <el-descriptions-item>
                                <template slot="label">
                                    <i class="el-icon-user"></i>
                                    申请金额
                                </template>
                                ￥<span style="color: #f00f14;font-weight: bold">{{showInfo.totalAmount}}</span>
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    <i class="el-icon-mobile-phone"></i>
                                    流程状态
                                </template>
                                <el-tag v-if="showInfo.refundStatus==0" type="danger">未通过</el-tag>
                                <el-tag v-if="showInfo.refundStatus==90" type="success">已转账</el-tag>
                                <el-tag v-if="showInfo.refundStatus==20" type="success">已通过</el-tag>
                                <el-tag v-else-if="showInfo.refundStatus==1" type="warning">未处理</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    <i class="el-icon-mobile-phone"></i>
                                    发放人
                                </template>
                                {{showInfo.operatorName}}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    <i class="el-icon-mobile-phone"></i>
                                    订单号
                                </template>
                                {{showInfo.billNo}}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    <i class="el-icon-user"></i>
                                    申请时间
                                </template>
                                {{showInfo.creatDate}}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    <i class="el-icon-mobile-phone"></i>
                                    发放日期
                                </template>
                                {{showInfo.operatorDate}}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    <i class="el-icon-mobile-phone"></i>
                                    最后更新时间
                                </template>
                                {{showInfo.lastUpDate}}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    <i class="el-icon-tickets"></i>
                                    申请备注
                                </template>
                                {{showInfo.memberRemark}}
                            </el-descriptions-item>
                            <el-descriptions-item>
                                <template slot="label">
                                    <i class="el-icon-tickets"></i>
                                    系统备注
                                </template>
                                <el-input
                                        type="textarea"
                                        :rows="2"
                                        placeholder="请输入内容"
                                        v-model="showInfo.operatorRemark">
                                </el-input>
                                <el-button type="primary" style="float: right;margin-top: 5px"
                                           @click="setOperatorRemark(showInfo.id,showInfo.operatorRemark)">更新备注
                                </el-button>
                            </el-descriptions-item>

                        </el-descriptions>
                    </div>
                    <div style="margin-top: 10px">
                        <h2>云平台-快捷入口</h2>
                        <el-divider>订单入口：</el-divider>
                        <li>订单详情：<a target="view_window"
                                    :href="'http://yun.xiaoyujia.com/order/baseinfo?BillNo='+showInfo.billNo">http://yun.xiaoyujia.com/order/baseinfo?BillNo={{showInfo.billNo}}</a>
                        </li>
                        <li>订单流水：<a target="view_window"
                                    :href="'http://yun.xiaoyujia.com/order/Consumption?BillNo='+showInfo.billNo">http://yun.xiaoyujia.com/order/Consumption?BillNo={{showInfo.billNo}}</a>
                        </li>

                        <el-divider>用户入口：</el-divider>
                        <li>用户详情：<a target="view_window"
                                    :href="'http://yun.xiaoyujia.com/Member/MemberEdit?id='+showInfo.memberId">http://yun.xiaoyujia.com/Member/MemberEdit?id={{showInfo.memberId}}</a>
                        </li>
                        <li>用户流水：<a target="view_window"
                                    :href="'https://yun.xiaoyujia.com/member/MemberCashJournalList?memberId='+showInfo.memberId">https://yun.xiaoyujia.com/member/MemberCashJournalList?memberId={{showInfo.memberId}}</a>
                        </li>
                    </div>
                    <div style="margin-top: 10px">
                        <h2>流程操作</h2>
                        <el-button type="primary" :disabled="showInfo.refundStatus!=1"
                                   @click="pastMemberRefund(showInfo,20)">审核通过
                        </el-button>
                        <el-button type="danger" :disabled="showInfo.refundStatus!=1"
                                   @click="pastMemberRefund(showInfo,0)">审核不通过
                        </el-button>
                        <el-button type="success" :disabled="showInfo.refundStatus!=20"
                                   @click="wxMemberRefundToTransfers(showInfo)" :loading="loading">商户转账
                        </el-button>
                        <el-button type="info" @click="getMemberRefundLog(showInfo.id),drawerLog=true">操作日志</el-button>

                    </div>
                </div>


            </el-drawer>
            <el-drawer
                    title="申请操作日志!"
                    :visible.sync="drawerLog"
                    direction="rtl"
                    size="50%">
                <el-table :data="logList">
                    <el-table-column property="creatDate" label="操作时间"></el-table-column>
                    <el-table-column property="title" label="操作内容"></el-table-column>
                    <el-table-column property="logMsg" label="操作说明"></el-table-column>
                    <el-table-column property="operatorName" label="操作人"></el-table-column>
                </el-table>
            </el-drawer>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: "memberRefundPage",
        data() {
            return {
                logList: [],
                loading: false,
                vdom: {},
                showInfo: {},
                drawerInfo: false,
                drawerLog: false,
                list: [],
                pageSizeOpts: [10, 20, 30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                form: {
                    refundType: null,
                    billNo: null,
                    bindTel: null,
                    lastUpDates: null,
                    lastUpStartDate: null,
                    lastUpEndDate: null,
                    current: 1,
                    size: 10
                },
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
            }
        },
        created() {
            this.getData();
        },
        methods: {
            showRow(dom) {
                this.showInfo = dom;
                this.drawerInfo = true;
                this.vMemberRefund(dom.id)
            },
            toMember(id) {
                // console.log(id)
                let routeData = this.$router.resolve({path: '/memberInfo', query: {"id": id}}); //path：跳转页面的相对路径，query：参数
                window.open(routeData.href, '_blank');
            },

            getData() {
                this.loading = true;
                if (this.form.lastUpDates != null) {
                    this.form.lastUpStartDate = this.form.lastUpDates[0] + ' 00:00:00';
                    this.form.lastUpEndDate = this.form.lastUpDates[1] + ' 23:59:59';
                } else {
                    this.form.lastUpStartDate = null;
                    this.form.lastUpEndDate = null;
                }
                this.$postData("memberRefundPage", this.form, {}).then(res => {
                    if (res.status == 200) {
                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total;
                        // console.log(this.logList)
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                    this.loading = false;
                })
            },
            wxMemberRefundToTransfers(dom) {

                if (dom.operatorRemark == null) {
                    this.$message.error("请先输入操作备注，");
                }
                dom.operatorId = localStorage.getItem("id");
                dom.operatorName = localStorage.getItem("realName");
                this.loading = true;
                this.$postData("wxMemberRefundToTransfers", dom, {}).then(res => {
                    if (res.status == 200) {
                        dom.refundStatus = 90;
                        console.log(this.logList);
                        this.$message.success("转账成功");
                    } else {
                        this.$message.error("转账失败，" + res.msg);
                    }
                    this.loading = false;
                })

            },
            setOperatorRemark(id, msg) {
                this.$getData("setOperatorRemark", {id: id, msg: msg}, {}).then(res => {
                    if (res.status == 200) {
                        this.vdom = res.data;
                        this.$message.success("更新成功");
                    } else {
                        this.$message.error("更新失败，" + res.msg);
                    }
                })
            },
            getMemberRefundLog(id) {
                this.$getData("getMemberRefundLog", {id: id}, {}).then(res => {
                    if (res.status == 200) {
                        this.logList = res.data;
                    } else {
                        this.$message.error("更新失败，" + res.msg);
                    }
                })
            },
            pastMemberRefund(dom, status) {
                this.$getData("pastMemberRefund", {id: dom.id, status: status}, {}).then(res => {
                    if (res.status == 200) {
                        dom.refundStatus = status;
                        this.$message.success("操作成功");
                    } else {
                        this.$message.error("更新失败，" + res.msg);
                    }
                })
            },
            vMemberRefund(id) {
                this.$getData("vMemberRefund", {id: id, eid: localStorage.getItem("id")}, {}).then(res => {
                    if (res.status == 200) {
                        this.vdom = res.data
                    } else {
                        this.$message.error("获取系统审核失败，" + res.msg);
                    }
                })
            },
            // 页码大小
            onPageSizeChange(size) {
                // console.log(size)
                this.form.size = size;
                this.getData();
            },
            onChange(index) {
                // console.log(index)
                this.form.current = index;
                this.getData();
            },
            exportData() {
                this.loading = true;
                this.loading = true;
                if (this.form.lastUpDates != null) {
                    this.form.lastUpStartDate = this.form.lastUpDates[0] + ' 00:00:00';
                    this.form.lastUpEndDate = this.form.lastUpDates[1] + ' 23:59:59';
                } else {
                    this.form.lastUpStartDate = null;
                    this.form.lastUpEndDate = null;
                }
                this.$postData("memberRefund_export", this.form, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tableName: "退款数据",
                        res: res
                    });
                })
            },
            blobExport({tableName, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tableName + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
                this.loading = false;
            },
        }
    }
</script>

<style scoped>
    li {
        font-size: 13px;
        line-height: 25px;
    }

    .el-divider--horizontal {
        display: block;
        height: 1px;
        width: 100%;
        margin: 5px 0;
    }

    .memberInfo {
        margin-bottom: 10px;
        background: #2d8cf0;
        padding: 10px;
        border-radius: 20px;
    }

</style>
