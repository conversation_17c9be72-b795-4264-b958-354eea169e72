<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="6">
            <el-form-item label="手机号">
              <el-input
                  clearable
                  v-model="form.applicantPhone"
                  placeholder="手机号"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="名称">
              <el-input
                  clearable
                  v-model="form.applicantName"
                  placeholder="名称"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="介绍人">
              <el-input
                  clearable
                  v-model="form.introducerName"
                  placeholder="介绍人"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="申请城市">
              <el-input
                  clearable
                  v-model="form.cityName"
                  placeholder="申请城市"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报名时间">
              <el-date-picker v-model="form.days" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="报名类型">
              <el-input
                  clearable
                  v-model="form.applicantType"
                  placeholder="报名类型"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-radio-group v-model="form.status" @change="form.current=1,getData()">
              <el-radio :label="null">全部</el-radio>
              <el-radio :label="0">未通过</el-radio>
              <el-radio :label="1">未处理</el-radio>
              <el-radio :label="2">已通过</el-radio>
              <el-radio :label="3">已上架</el-radio>
            </el-radio-group>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <br>
      <el-tabs type="border-card">
        <el-tab-pane label="填报记录">
          <el-table :data="logList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable"
                    @cell-dblclick="" :row-key='getRowKeys' :expand-row-keys="expands" @expand-change="expandChange">
            <el-table-column type="expand">
              <template slot-scope="scope">
                <el-form label-position="left" inline class="demo-table-expand">
                  <el-form-item label="处理结果">
                    <el-radio-group v-model="scope.row.status">
                      <el-radio label="0">不通过</el-radio>
                      <el-radio label="2">通过</el-radio>
                      <el-radio label="3">上架</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <br>
                  <el-form-item label="处理根据">
                    <el-input type="textarea" v-model="scope.row.handlerDetail" maxlength="30" :rows="5"
                              show-word-limit></el-input>
                  </el-form-item>
                  <br>
                  <el-button type="primary" @click="saveRecruit(scope.row)">更新</el-button>
                </el-form>
              </template>
            </el-table-column>

            <el-table-column
                prop="creatDate"
                label="填报时间"
                width="180"
            >
            </el-table-column>
            <el-table-column
                prop="applicantType"
                label="报名类型"
            >
            </el-table-column>
            <el-table-column
                prop="status"
                label="状态"
                width="80">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status=='0'" type="danger">未通过</el-tag>
                <el-tag v-if="scope.row.status=='2'" type="success">已通过</el-tag>
                <el-tag v-else-if="scope.row.status=='1'" type="warning">未处理</el-tag>
              </template>
            </el-table-column>
            <el-table-column
                prop="applicantName"
                label="名称"
            ></el-table-column>
            <el-table-column
                prop="applicantPhone"
                label="手机号"
                width="100">
            </el-table-column>
            <el-table-column
                prop="applicantIdCard"
                label="身份证"
                width="150">
            </el-table-column>
            <el-table-column
                prop="introducerName"
                label="介绍人">
            </el-table-column>
            <el-table-column
                prop="cityName"
                label="申请城市">
            </el-table-column>
            <el-table-column
                prop="channel"
                label="申请渠道">
            </el-table-column>
            <el-table-column
                prop="handlerDetail"
                label="处理根据">
            </el-table-column>

            <el-table-column
                label="操作"
                fixed="right"
                min-width="180">
              <template slot-scope="scope">
                <el-button

                    @click.native.prevent="showRow(scope.row)"
                    type="text"
                    size="small">
                  操作
                </el-button>
                <el-button @click.native.prevent="upload(scope.row)" type="text" size="small">
                  上传
                </el-button>
              </template>

            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>

      </el-tabs>
    </div>
  </div>
</template>

<script>
export default {
  name: "recruitPage",
  data() {
    return {
      expands: [],
      responseInfoModal: false,
      show: null,
      logList: [],
      pageSizeOpts: [10, 20, 30],
      multipleSelection: [],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      form: {
        days: [],
        applicantName: null,
        applicantPhone: null,
        introducerName: null,
        applicantType: null,
        status: null,
        startTime: null,
        endTime: null,
        current: 1,
        size: 10
      },
      user: {
        id: localStorage.getItem("id"),
        storeId: localStorage.getItem("storeId"),
        roleId: localStorage.getItem("roleId"),
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },

  created() {
    this.getData();
  },
  methods: {
    showRow(row) {
      this.$refs.multipleTable.toggleRowExpansion(row);
    },
    upload(row) {
      this.insertTrialStaffByRecruitDto(row);
    },
    expandChange(row, expandedRows) {//
      let that = this;
      if (expandedRows.length) {//此时展开
        that.expands = [];
        if (row) {
          that.expands.push(row.id);
        }
      } else {//折叠
        that.expands = [];
      }
      this.getData()
    },
    getRowKeys(row) {//行数据的key，用于优化table的渲染
      return row.id;
    },
    // 页码大小
    onPageSizeChange(size) {
      console.log(size)
      this.form.size = size;
      this.getData();
    },
    onChange(index) {
      console.log(index)
      this.form.current = index;
      this.getData();
    },
    saveRecruit(row) {
      row.handlerId = localStorage.getItem("id")
      this.$postData("saveRecruit", row, {}).then(res => {
        if (res.status == 200) {
          this.$message.success("保存成功");
          this.getData()
        } else {
          this.$message.error("保存失败，" + res.msg);
        }
      })
    },
    getData() {
      this.form.startTime = null;
      this.form.endTime = null;
      if (this.form.days != null && this.form.days.length > 0) {
        this.form.startTime = this.form.days[0];
        this.form.endTime = this.form.days[1]
      }
      this.$postData("recruitPage", this.form, {}).then(res => {
        if (res.status == 200) {
          this.logList = res.data.records;

          this.logList.forEach(v => {
            v.status = v.status.toString()
          })
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
          console.log(this.logList)
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    },
    insertTrialStaffByRecruitDto(recruitDto) {
      this.$postData("insertTrialStaffByRecruitDto", recruitDto).then(res => {
        if (res.status === 200) {
          this.$message.error("插入成功，" + res.msg);
          console.log(res.data)
        } else {
          this.$message.error("插入失败，" + res.msg);
        }
      })
    },
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
