<template>
  <div class="container">
    <div class="handle-box">
      <el-form :inline="true" class="demo-form-inline">

        <el-form-item label="时间">
          <el-date-picker
              v-model="time"
              type="datetimerange"
              range-separator="至"
              value-format="yyyy-MM-dd HH:mm:ss"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="订单状态">
          <el-select  filterable v-model="dom.status" placeholder="状态" clearable>
            <el-option
                v-for="(item,index) in statusList"
                :key="index"
                :label="item.name"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="欠款状态">
          <el-select  filterable v-model="dom.arrears" placeholder="状态" @change="changeQK()" clearable >
            <el-option
                v-for="(item,index) in qiankuanList"
                :key="index"
                :label="item.name"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否新客">
          <el-select  filterable v-model="dom.newcustomer" placeholder="状态"  clearable >
            <el-option
                v-for="(item,index) in newcustomer"
                :key="index"
                :label="item.name"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="产品分类">
          <el-select  filterable v-model="dom.productCateGoryId" placeholder="状态" clearable>
            <el-option
                v-for="(item,index) in productCategoryList"
                :key="index"
                :label="item.name"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="会员账号">
         <el-input v-model="dom.account"></el-input>
        </el-form-item>

        <el-form-item label="券号">
          <el-input v-model="dom.douyinNo"></el-input>
        </el-form-item>
        <el-form-item label="抖音单号">
          <el-input v-model="dom.douyinOrderId"></el-input>
        </el-form-item>

        <el-form-item label="订单号">
          <el-input v-model="dom.billNo"></el-input>
        </el-form-item>


        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="onQuery" v-loading="loading">
            查询
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-refresh" @click="reset()" v-loading="loading">
            重置
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-download" type="primary" plain @click="exportExcel" v-loading="loading">
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card>
          合计订单量:<span style="color: red">{{ sumData.orderNum }}</span>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          合计营业额:<span style="color: red">{{ sumData.totalAmount }}</span>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          营业额:<span style="color: red">{{ sumData.turnover }}</span>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          销售额:<span style="color: red">{{ sumData.sales }}</span>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          新客量:<span style="color: red">{{ sumData.newCustomerCount }}</span>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          新客占比率:<span style="color: red">{{ sumData.newcunstomerper }}%</span>
        </el-card>
      </el-col>
    </el-row>

    <el-table :data="dataList" v-loading="loading" style="width: 100%"
              :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">

      <el-table-column
          prop="account"
          label="会员账户"
          align="center"
          width="120"
      ></el-table-column>
      <el-table-column
          prop="bindTel"
          label="联系电话"
          align="center"
          width="120"
      ></el-table-column>
      <el-table-column
          prop="douyinNo"
          label="券号"
          width="200"
          align="center">

      </el-table-column>
      <el-table-column
          prop="douyinOrderId"
          label="抖音单号"
          width="100"
          align="center">

      </el-table-column>
      <el-table-column
          label="订单金额"
          align="center">
        <template slot-scope="scope">
          <span>{{scope.row.amount}}</span>
        </template>

      </el-table-column>
      <el-table-column
          label="实付金额"
          align="center">
        <template slot-scope="scope">
          <span>{{scope.row.amount - scope.row.discountAmount}}</span>
        </template>

      </el-table-column>
      <el-table-column
          prop="productName"
          label="产品名称"
          width="120"
          align="center">
      </el-table-column>
        <el-table-column
            prop="status"
            label="核销状态"
            align="center">
          <template slot-scope="scope">
            <el-tag type="info" v-if="scope.row.status === 0">未支付</el-tag>
            <el-tag type="success" v-if="scope.row.status === 1">已支付</el-tag>
            <el-tag type="success" v-if="scope.row.status === 2">已关联</el-tag>
            <el-tag type="danger" v-if="scope.row.status === 3">已取消</el-tag>
            <el-tag type="warning" v-if="scope.row.status === 4">退款已退回</el-tag>
            <el-tag type="warning" v-if="scope.row.status === 5">已分账</el-tag>
          </template>
        </el-table-column>
          <el-table-column
              prop="billNo"
              label="关联订单号"
              width="200"
              align="center">
          </el-table-column>
      <el-table-column
          prop="orderStatus"
          label="订单状态"
          align="center">
      </el-table-column>
            <el-table-column
                prop="createTime"
                label="创建时间"
                width="90"
                align="center">
      </el-table-column>
      <el-table-column
          prop="registerTime"
          label="注册时间"
          align="center">
      </el-table-column>
      <el-table-column
          label="获客渠道"
          prop="sourceName"
          align="center">

      </el-table-column>
      <el-table-column
          prop="remark"
          label="备注"
          align="center">
      </el-table-column>
      <el-table-column
          fixed="right"
          label="操作"
          width="200">
        <template slot-scope="scope">
          <el-button type="success" size="small" @click="writeOff(scope.row)" v-if="!scope.row.billNo && (scope.row.status === 1 || scope.row.status === 5)">订单核销</el-button>
          <el-button type="primary" size="small" @click="remarkInfo(scope.row)">备注</el-button>
          <el-button type="danger" size="small" @click="refund(scope.row)" v-if="scope.row.status === 1 || scope.row.status === 2">原路退款</el-button>
<!--          <el-button type="warning" size="small" @click="btnStateUpdate(scope.row)" v-if="scope.row.status === 5 && (roleId == 1 || roleId ==2)">分账状态变更退回</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChangePage"
            :current="dom.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="dom.size"/>
    </div>

    <el-dialog
        title="编辑"
        :visible.sync="editModal"
        width="30%"
        center>
      <el-input v-model="editRow.remark" placeholder="请输入内容"></el-input>
      <span slot="footer" class="dialog-footer">
    <el-button @click="editModal = false">取 消</el-button>
    <el-button type="primary" @click="confineEdit()">确 定</el-button>
  </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "earnestMoney",
  data() {
    return {
      roleId: localStorage.getItem('roleId'),
      editModal:false,
      editRow:{},
      sumData:{
        totalAmount:0,
        orderNum:0
      },
      newcustomer: [{id:null,name:'全部'},{id:0,name:'是'},{id:1,name:'否'}],
      qiankuanList: [{id:null,name:'全部'},{id:0,name:'是'},{id:1,name:'否'}],
      statusList:[
        {id:0,name:'未支付'},
        {id:1,name:'已支付'},
        {id:2,name:'已关联'},
        {id:3,name:'已取消'},
        {id:4,name:'退款已退回'},
        {id:5,name:'已分账'}
      ],
      loading: false,
      time:[],
      dom: {
        size: 15,
        current: 1,
        startDate:null,
        endDate:null,
        douyinNo:null,
        status:null,
        account:null,
        arrears:null,
        productCateGoryId:null,
        billNo:null
      },
      dataList: [],
      typeList:[{id:1,name:'增加'},{id:2,name: '扣减'}],
      pageSizeOpts: [10, 20, 50, 100, 150, 200],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      productCategoryList:[]
    }
  },
  created() {
    this.getData();
    this.getsumData();
    this.getAllProductCategory();
  },
  methods:{
    getAllProductCategory() {
      this.$getData("getAllCategory",{}).then(res => {
        if (res.status === 200) {
          this.productCategoryList = res.data;
        }
      })
    },
    changeQK() {
      if (!this.dom.arrears) {
        this.dom.status = null;
      }
    },
    getsumData() {
      this.loading = true;
      this.$postData("getjgSumAll", this.dom).then(res => {
        if (res.status === 200) {
          this.loading = false;
          this.sumData = [];
          this.sumData = res.data;
        }
      })
    },
    writeOff(row) {
      this.editRow = row;
      this.$prompt('请输入订单号(一经确认无法回逆,请谨慎操作)', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern:  /^\d+$/,
        inputErrorMessage: '订单号输入错误'
      }).then(({ value }) => {
        this.editRow.billNo = value;
        this.editRow.employeeName = localStorage.getItem("realName");
        this.editRow.employeeId = localStorage.getItem("id");
        this.loading = true;
        this.$postData("writeoffjgDouyinNo", this.editRow).then(res => {
          if (res.status === 200) {
            this.loading = false;
            this.$message({
              type: 'success',
              message: '操作成功'
            });
            this.getData();
          } else {
            this.loading = false;
            this.$message({
              type: 'error',
              message: res.msg
            });
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消输入'
        });
      });
    },
    confineEdit() {
      this.loading = true;
      this.$postData("editRemarkjgDouyinByFirst", {id: this.editRow.id, remark: this.editRow.remark}).then(res => {
        if (res.status === 200) {
          this.loading = false;
          this.$message({
            type: 'success',
            message: '操作成功'
          });
          this.editModal = false;
          this.getData();
        } else {
          this.loading = false;
          this.$message({
            type: 'error',
            message: res.data
          });
        }
      })
    },
    remarkInfo(row) {
    this.editRow = row;
    this.editModal = true;
    },
    refund(row) {
      this.$confirm('此操作将该券和订单取消并且账户原路退回, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        this.$postData("refundJinGangDouyinByNo",
            {memberId: row.memberId,
                  douyinNo: row.douyinNo,
                  opId: localStorage.getItem("account"),
                  OpName: localStorage.getItem("realName")

            }).then(res => {
          if (res.code === 0) {
            this.loading = false;
            this.$message({
              type: 'success',
              message: '操作成功'
            });
            this.getData();
          } else {
            this.loading = false;
            this.$message({
              type: 'error',
              message: res.msg
            });
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    reset() {
      this.dom.size = 15;
      this.dom.current = 1;
      this.dom.startDate = null;
      this.dom.endDate = null;
      this.dom.douyinNo = null;
      this.dom.status = null;
      this.dom.account = null;
      this.dom.productCateGoryId = null;
      this.dom.arrears = null;
      this.getsumData();
      this.getData();
    },
    onChangePage(index) {
      this.dom.current = index;
      this.getData();
    },
    onPageSizeChange(size) {
      this.dom.size = size;
      this.getData();
    },
    getData() {
      this.loading = true;
      this.$postData("getjgDouyinMemberOrderList", this.dom).then(res => {
        if (res.status === 200) {
          this.loading = false;
          this.dataList = [];
          this.dataList = res.data.records;
          this.pageInfo.total = res.data.total;
        }
      })
    },
    exportExcel() {
      if (this.time){
        this.dom.startDate = this.time[0];
        this.dom.endDate = this.time[1];
      }
      this.loading = true;
      this.$postData("getjgDouyinMemberOrderExport", JSON.stringify(this.dom), {
        responseType: "arraybuffer"
      }).then(res => {
        this.blobExport({
          tableName: "抖音先买后核销记录",
          res: res
        });
      });
    },
    onQuery() {
      if (this.time){
        this.dom.startDate = this.time[0];
        this.dom.endDate = this.time[1];
      }
      this.dom.current = 1;
      this.getData();
      this.getsumData();
    },
    blobExport({tableName, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tableName + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
      const that = this;
      setTimeout(function () {
        that.loading = false;
      }, 2500);

    },
    btnStateUpdate(data) {

      this.$confirm(data.douyinNo+'变更为退款已退回状态, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        data.status = 4
        this.loading = true;
        this.$postData("updatejgDouYinMemberPay", data).then(res => {
          if (res.status === 200) {
            this.loading = false;
            this.$message({
              type: 'success',
              message: '操作成功'
            });
            this.getData();
          } else {
            this.loading = false;
            this.$message({
              type: 'error',
              message: res.data
            });
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });


    }
  }

}
</script>

<style scoped>
.allAmount {
  font-weight: bold;
  /*padding-left: 10px;*/
  color: red;
  font-size: 24px;
}
</style>
