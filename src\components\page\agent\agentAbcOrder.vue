<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">
                    <div style="color: #f00f14">注意：时间条件检索示例 -> 查询1月1号的结算内容，开始日期为1月1号，结束日期为1月2号。</div>

                    <el-row>
                        <!--所属门店（自营门店）、开发人工号、服务分类、服务项目、下单时间、服务时间、结算时间、会员账号-->
                        <el-col :span="8">
                            <!--<el-form-item label="员工姓名">-->
                            <!--    <el-input-->
                            <!--            v-model="model.realName"-->
                            <!--            placeholder="经纪人姓名"-->
                            <!--            style="width:250px"-->
                            <!--            class="handle-input mr10"-->
                            <!--    ></el-input>-->
                            <!--</el-form-item>-->
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="开发工号">
                                <el-input
                                        v-model="model.kfrNo"
                                        clearable
                                        placeholder="开发人工号"
                                        style="width:250px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8" v-show="showSelect">
                            <el-form-item label="门店名称">
                                <el-select filterable style="width:250px" v-model="model.storeId">
                                    <!--<el-option value="">请选择</el-option>-->
                                    <!--<el-option value="1">平台</el-option>-->
                                    <el-option v-for="(item,index) in storeList" :value="item.id" :key="index"
                                               :label="item.storeName"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="会员账号">
                                <el-input
                                        v-model="model.bindTel"
                                        clearable
                                        placeholder="会员手机号"
                                        style="width:250px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" v-show="showSelect">
                            <el-form-item label="产品分类">
                                <el-select clearable filterable style="width:250px" v-model="model.productCategoryId"
                                           @change="clickProductCategoryId()" @clear="clearProductCategoryId()">
                                    <!--<el-option value="">请选择</el-option>-->
                                    <el-option v-for="(item,index) in productCategoryList" :value="item.id" :key="index"
                                               :label="item.name"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" v-show="showSelect">
                            <el-form-item label="服务项目">
                                <el-select clearable filterable style="width:250px" v-model="model.productId"
                                           placeholder="请选择产品分类后再选择此选项" @focus="focusProductId()">
                                    <el-option v-for="(item,index) in productIdList" :value="item.id" :key="index"
                                               :label="item.name"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <!--<el-col :span="8">-->
                        <!--    <el-form-item label="完成时间">-->
                        <!--        <el-date-picker-->
                        <!--                v-model="showDate"-->
                        <!--                type="daterange"-->
                        <!--                style="width: 250px"-->
                        <!--                :picker-options="pickerOptions"-->
                        <!--                range-separator="~"-->
                        <!--                start-placeholder="开始日期"-->
                        <!--                end-placeholder="结束日期"-->
                        <!--                value-format="yyyy-MM-dd"-->
                        <!--                align="right">-->
                        <!--        </el-date-picker>&nbsp;-->
                        <!--    </el-form-item>-->
                        <!--</el-col>-->
                        <el-col :span="8">
                            <el-form-item label="下单时间">
                                <el-date-picker
                                        v-model="createDate"
                                        type="daterange"
                                        style="width: 250px"
                                        :picker-options="pickerOptions"
                                        range-separator="~"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        value-format="yyyy-MM-dd"
                                        align="right">
                                </el-date-picker>&nbsp;
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="服务时间">
                                <el-date-picker
                                        v-model="serviceDate"
                                        type="daterange"
                                        style="width: 250px"
                                        :picker-options="pickerOptions"
                                        range-separator="~"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        value-format="yyyy-MM-dd"
                                        align="right">
                                </el-date-picker>&nbsp;
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="结算时间">
                                <el-date-picker
                                        v-model="settlementDate"
                                        type="daterange"
                                        style="width: 250px"
                                        :picker-options="pickerOptions"
                                        range-separator="~"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        value-format="yyyy-MM-dd"
                                        align="right">
                                </el-date-picker>&nbsp;
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">

                            <el-form-item>
                                <el-button type="primary" @click="query()">搜索</el-button>
                                <el-button type="info" plain @click="re()">重置</el-button>
                                <el-button type="primary" plain @click="exportExcel()">导出</el-button>
                                <div style="color: #f00f14">注意：导出必须带有时间条件，且范围在一年以内。</div>
                            </el-form-item>

                        </el-col>
                    </el-row>

                </el-form>
            </div>
            <el-row>
                <center>
                    <el-col :span="12">
                        <el-card shadow="hover">
                            订单数：<span class="allAmount">{{pageInfo.total}}</span>

                        </el-card>
                    </el-col>

                    <el-col :span="12">
                        <el-card shadow="hover">
                            订单金额：<span class="allAmount">￥{{sum}}</span>
                            <span style="color: #6f7180">（均价： ￥<span style="color: #f00f14">{{(sum/pageInfo.total).toFixed(2)}}</span> ）</span>
                            <!--                        抽成比例：￥<span class="allAmount">{{(settlementData.allBonus/settlementData.allAmount).toFixed(2)}}%</span>-->
                        </el-card>
                    </el-col>

                </center>
            </el-row>
            <el-table :data="list" :header-cell-style="{background:'#d2d3d5'}" border class="table" ref="multipleTable"
                      v-loading="loading">
                <!--所属门店、订单号、会员账号（带***）、服务项目、服务金额、服务地址、服务员工、开发人、下单时间、服务时间、结算时间-->
                <el-table-column
                        fixed="left"
                        prop="orderStoreName"
                        align="center"
                        label="所属门店"
                        width="120"
                ></el-table-column>
                <!--<el-table-column-->
                <!--        fixed="left"-->
                <!--        prop="order"-->
                <!--        label="所属门店"-->
                <!--        width="100"-->
                <!--&gt;</el-table-column>-->
                <el-table-column
                        fixed="left"
                        prop="billNo"
                        label="订单编号"
                        width="150"
                ></el-table-column>
                <el-table-column
                        prop="account"
                        label="会员账号"
                        width="120"
                >
                    <template slot-scope="scope">
                        <el-link type="primary">{{scope.row.bindTel}}</el-link>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="ProductName"
                        label="服务产品"
                        width="120"
                >
                </el-table-column>
                <el-table-column
                        prop="RealTotalAmount"
                        label="付款金额"
                        width="80"
                        align="right"
                ></el-table-column>
                <el-table-column
                        prop="Street"
                        label="服务地址"
                        width="200"
                ></el-table-column>
                <el-table-column
                        prop="serviceEmployee"
                        label="服务员工"
                        width="120"
                ></el-table-column>

                <el-table-column
                        prop="No"
                        label="开发人"
                        width="120"
                >
                    <template slot-scope="scope">
                        <span v-if="scope.row.RealName != undefined
                         && scope.row.RealName != null">
                            {{scope.row.RealName}}({{scope.row.NO}})
                        </span>
                        <span v-else>--</span>
                    </template>
                </el-table-column>


                <!--                <el-table-column-->
                <!--                        prop="totalAmount"-->
                <!--                        label="订单金额"-->
                <!--                        width="120"-->
                <!--                        sortable="custom"-->
                <!--                ></el-table-column>-->


                <!--                <el-table-column-->
                <!--                        prop="realTotalAmount"-->
                <!--                        label="最终金额"-->
                <!--                        width="120"-->
                <!--                        sortable="custom"-->
                <!--                ></el-table-column>-->
                <el-table-column
                        prop="CreateTime"
                        label="下单时间"
                ></el-table-column>
                <el-table-column
                        prop="StartTime"
                        label="服务时间"
                >
                </el-table-column>


                <!--                <el-table-column-->
                <!--                        prop="workRemark"-->
                <!--                        label="工作记录"-->
                <!--                        width="150"-->
                <!--                >-->
                <!--                </el-table-column>-->

                <el-table-column
                        prop="PaySettlementTime"
                        label="结算时间">
                    <template slot-scope="scope">
                        <span v-if="scope.row.PaySettlementTime != undefined
                        && scope.row.PaySettlementTime != null">
                            {{scope.row.PaySettlementTime}}
                        </span>
                        <span v-else>--</span>
                    </template>
                </el-table-column>

            </el-table>
            <!--<div class="pagination">-->
                <!--<Page :total="pageInfo.total"-->
                <!--      @on-change="onChange"-->
                <!--      :show-total="true"-->
                <!--      :show-sizer="true"-->
                <!--      :page-size-opts="pageSizeOpts"-->
                <!--      @on-page-size-change="onPageSizeChange"-->
                <!--      :page-size="pageInfo.size"/>-->
                <el-pagination
                        style="margin-top: 10px; "
                        @size-change="onPageSizeChange"
                        @current-change="onChange"
                        :current-page="pageInfo.current"
                        :page-sizes="pageSizeOpts"
                        :page-size="pageInfo.size"
                        layout="->, total, prev, pager, next, sizes"
                        :total="pageInfo.total">
                </el-pagination>
            <!--</div>-->
        </div>
    </div>

</template>

<script>
    export default {
        name: "agentAbcOrder",
        data() {
            return {
                showSelect: false,
                role: localStorage.getItem("roleId"),
                account: localStorage.getItem("account"),
                loading: false,
                sum: 0,
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                showDate: null,
                createDate: null,
                serviceDate: null,
                settlementDate: null,
                list: [],
                storeList: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                pageSizeOpts: [10, 20, 30],
                model: {
                    storeId: Number(localStorage.getItem("storeId")),
                    productCategoryId: null,
                    productId: null,
                    orderBy: null,
                    bindTel: null,
                    realName: null,
                    kfrNo: null,
                    startTime: null,
                    endTime: null,
                    createStartTime: null,
                    createEndTime: null,
                    serviceStartTime: null,
                    serviceEndTime: null,
                    settlementStartTime: null,
                    settlementEndTime: null,
                    current: 1,
                    size: 10,
                    roleId: localStorage.getItem("roleId"),
                },
                // 17,18,19,20,22,23,27,37
                //  getABCSKOrderProductCategoryId
                productCategoryList: [],
                productIdList: [],
            }
        },
        created() {
            if (this.role == "1" || this.account == "005") {
                this.model.storeId = null;
                this.showSelect = true;
            }
            if (this.model.storeId != null && this.model.storeId == 1) {
                this.model.storeId = null;
            }
            this.getStore();
            this.getABCSKOrderProductCategoryIdData();
            this.getData();
        },
        methods: {
            focusProductId() {
                if (this.model.productCategoryId == null || this.model.productCategoryId === '') {
                    return this.$message.error('请选择【产品分类】后再选择服务产品');
                } else {
                    if (this.productIdList == null || this.productIdList.length < 1) {
                        return this.$message.error('该产品分类中没有上架的服务产品');
                    }
                }
            },
            clearProductCategoryId() {
                this.model.productCategoryId = null;
                this.model.productId = null;
                this.model.productIdList = [];
            },
            clickProductCategoryId() {
                this.loading = true;
                const pcId = this.model.productCategoryId;
                if (pcId == null || pcId === '') {
                    this.loading = false;
                    return;
                }
                this.$getData("product_getByProductCategoryId",
                    {productCategoryId: pcId}, {}).then(res => {
                    if (res.status == 200) {
                        this.productIdList = res.data;
                    }
                }).finally(_ => {
                    this.loading = false;
                });
            },
            getABCSKOrderProductCategoryIdData() {
                this.loading = true;
                this.$getData("getABCSKOrderProductCategoryId", {}, {}).then(res => {
                    if (res.status == 200) {
                        this.productCategoryList = res.data;
                    }
                }).finally(_ => {
                    this.loading = false;
                });
            },
            onPageSizeChange(size) {
                // console.log(size)
                this.model.size = size;
                this.getData();
            },
            onChange(index) {
                // console.log(index)
                this.model.current = index;
                this.getData();
            },
            query() {
                if (this.showDate != null) {
                    this.model.startTime = this.showDate[0];
                    this.model.endTime = this.showDate[1];
                } else {
                    this.model.startTime = null;
                    this.model.endTime = null;
                }
                if (this.createDate != null) {
                    this.model.createStartTime = this.createDate[0];
                    this.model.createEndTime = this.createDate[1];
                } else {
                    this.model.createStartTime = null;
                    this.model.createEndTime = null;
                }
                if (this.serviceDate != null) {
                    this.model.serviceStartTime = this.serviceDate[0];
                    this.model.serviceEndTime = this.serviceDate[1];
                } else {
                    this.model.serviceStartTime = null;
                    this.model.serviceEndTime = null;
                }
                if (this.settlementDate != null) {
                    this.model.settlementStartTime = this.settlementDate[0];
                    this.model.settlementEndTime = this.settlementDate[1];
                } else {
                    this.model.settlementStartTime = null;
                    this.model.settlementEndTime = null;
                }
                this.model.current = 1;
                this.pageInfo.current = 1;
                this.getData();
            },
            re() {
                this.showDate = null;
                this.model.storeId = null;
                this.model.bindTel = null;
                this.model.realName = null;

                this.model.kfrNo = null;
                this.model.startTime = null;
                this.model.endTime = null;
                this.createDate = null;
                this.serviceDate = null;
                this.settlementDate = null;
                this.model.createStartTime = null;
                this.model.createEndTime = null;
                this.model.serviceStartTime = null;
                this.model.serviceEndTime = null;
                this.model.settlementStartTime = null;
                this.model.settlementEndTime = null;
                this.model.productCategoryId = null;
                this.model.productId = null;
                this.model.productIdList = [];
                this.model.current = 1;
                this.pageInfo.current = 1;
                this.getData();
            },
            getStore() {
                this.$postData("store_getByList", {}, {}).then(res => {
                    if (res.status == 200) {
                        this.storeList = res.data;
                    }
                });
            },
            getData() {

                if (localStorage.getItem("roleId") == "42") {
                    this.model.id = localStorage.getItem("id")
                }
                if (localStorage.getItem("roleId") == '66' && localStorage.getItem("account") !== '005') {
                    this.model.storeId = localStorage.getItem("storeId")
                }

                this.loading = true;
                this.$postData("getAgentABC", this.model, {}).then(res => {
                    if (res.status == 200) {
                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                    this.loading = false;
                });
                this.$postData("getAgentABCSum", this.model, {}).then(res => {
                    if (res.status == 200) {
                        this.sum = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            exportExcel() {
                if (this.createDate == null && this.serviceDate == null
                    && this.settlementDate == null) {
                    return this.$message.error('导出必须带有时间条件');
                }

                // if (this.showDate != null) {
                //     this.model.startTime = this.showDate[0];
                //     this.model.endTime = this.showDate[1];
                // } else {
                //     this.model.startTime = null;
                //     this.model.endTime = null;
                // }
                if (this.createDate != null) {
                    this.model.createStartTime = this.createDate[0];
                    this.model.createEndTime = this.createDate[1];
                    const diffDay = this.getDiffDay(this.model.createStartTime, this.model.createEndTime);
                    if (diffDay > 366) {
                        return this.$message.error('导出的数据 时间范围需在一年以内');
                    }
                } else {
                    this.model.createStartTime = null;
                    this.model.createEndTime = null;
                }
                if (this.serviceDate != null) {
                    this.model.serviceStartTime = this.serviceDate[0];
                    this.model.serviceEndTime = this.serviceDate[1];
                    const diffDay = this.getDiffDay(this.model.serviceStartTime, this.model.serviceEndTime);
                    if (diffDay > 366) {
                        return this.$message.error('导出的数据 时间范围需在一年以内');
                    }
                } else {
                    this.model.serviceStartTime = null;
                    this.model.serviceEndTime = null;
                }
                if (this.settlementDate != null) {
                    this.model.settlementStartTime = this.settlementDate[0];
                    this.model.settlementEndTime = this.settlementDate[1];
                    const diffDay = this.getDiffDay(this.model.settlementStartTime, this.model.settlementEndTime);
                    if (diffDay > 366) {
                        return this.$message.error('导出的数据 时间范围需在一年以内');
                    }
                } else {
                    this.model.settlementStartTime = null;
                    this.model.settlementEndTime = null;
                }
                this.$message.success('正在导出中,可能需要1~2分钟，请稍等...');
                this.loading = true;
                this.$postData("exportAgentABC", this.model, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tablename: "ABC订单数据",
                        res: res
                    });
                });
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
            getDiffDay(date_1, date_2) {
                // 计算两个日期之间的差值
                let totalDays, diffDate;
                let myDate_1 = Date.parse(date_1);
                let myDate_2 = Date.parse(date_2);
                // 将两个日期都转换为毫秒格式，然后做差
                // 取相差毫秒数的绝对值
                diffDate = Math.abs(myDate_1 - myDate_2);
                // 向下取整
                totalDays = Math.floor(diffDate / (1000 * 3600 * 24));
                // console.log(totalDays)
                // 相差的天数
                return totalDays
            }
        }
    }
</script>

<style scoped>
    >>> .el-date-editor .el-range-separator {
        padding: 0;
    }
</style>
