<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">
                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="订单编号">
                                <el-input
                                        clearable
                                        v-model="dto.billNo"
                                        placeholder="订单编号"
                                        style="width:170px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="5">
                            <el-form-item label="会员账号">
                                <el-input
                                        clearable
                                        v-model="dto.accountName"
                                        placeholder="支持账号"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="投诉类型">
                                <Select filterable clearable style="width: 120px" v-model="dto.tsType">
                                    <Option value="">请选择</Option>
                                    <Option v-for="item in typeList" :value="item.id">{{ item.text}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="投诉状态">
                                <Select filterable clearable style="width: 120px" v-model="dto.tsState"
                                        @on-change="query()">
                                    <Option value="">请选择</Option>
                                    <Option value="1">已接单</Option>
                                    <Option value="2">审核中</Option>
                                    <Option value="3">处理中</Option>
                                    <Option value="4">申诉中</Option>
                                    <Option value="5">申诉成功</Option>
                                    <Option value="6">申诉失败</Option>
                                    <Option value="7">已结案</Option>
                                    <!--<Option v-for="item in stateList" :value="item.id">{{ item.text}}</Option>-->
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="投诉渠道">
                                <Select filterable clearable style="width: 120px" v-model="dto.tsChannel">
                                    <Option value="">请选择</Option>
                                    <Option v-for="item in channelList" :value="item.id">{{ item.text}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="4">
                            <el-form-item label="员工工号">
                                <el-input
                                        clearable
                                        v-model="dto.no"
                                        placeholder="支持工号"
                                        style="width:130px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" style="padding-left: 1rem">
                            <el-form-item label="服务时间">
                                <el-date-picker
                                        v-model="dto.serviceTime"
                                        type="datetime"
                                        placeholder="开始时间"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        style="width:180px"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" style="padding-left: 1rem">
                            <el-form-item label="至">
                                <el-date-picker
                                        v-model="dto.serviceEndTime"
                                        type="datetime"
                                        placeholder="结束时间"
                                        style="width:180px"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>

                        <el-col :span="5">
                            <el-form-item label="投诉时间">
                                <el-date-picker
                                        v-model="dto.tsStartTime"
                                        type="datetime"
                                        placeholder="开始时间"
                                        style="width:180px"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" style="text-align: left">
                            <el-form-item label="至">
                                <el-date-picker
                                        v-model="dto.tsEndTime"
                                        type="datetime"
                                        style="width:180px"
                                        placeholder="结束时间"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="4" style="text-align: left">
                            <el-form-item label="投诉判定">
                                <Select filterable clearable style="width: 120px" v-model="dto.violation">
                                    <Option value="">请选择</Option>
                                    <Option value="1">有效投诉</Option>
                                    <Option value="4">其他投诉</Option>
                                    <Option value="2">无效投诉</Option>
                                    <!--<Option value="3">违纪处罚</Option>-->
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="是否交付部处理">
                                <Select filterable clearable style="width: 120px" v-model="dto.isTransfer">
                                    <Option value="">请选择</Option>
                                    <Option value="1">是</Option>
                                    <Option value="2">否</Option>>
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="服务项目">
                                <Select filterable style="width: 110px" @on-change="query"
                                        v-model="dto.productCategoryId">
                                    <Option value="0">请选择</Option>
                                    <Option v-for="item in options" :value="item.id" :key="item.id">{{item.name}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="9" style="text-align: right">
                            <el-form-item>
                                <el-button type="primary" @click="query()">搜索</el-button>
                                <el-button type="success" :loading="loadingExcel" @click="exportExcel">导出Excel
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-table :data="list" border class="table" ref="multipleTable"
                      v-loading="loading">
                <!--<el-table-column-->
                <!--type="selection"-->
                <!--width="55">-->
                <!--</el-table-column>-->
                <el-table-column
                        width="100"
                        label="操作">
                    <template slot-scope="scope">
                        <div style="text-align: center">
                            <div>
                                <el-button size="mini" @click="edit(scope.row)" type="primary">查看</el-button>
                            </div>
                            <div style="margin-top: 10px">
                                <el-button size="mini" v-if="scope.row.appealReason!=null&&scope.row.appealReason!==''"
                                           @click="appealMush(scope.row)"
                                           type="warning">申诉处理
                                </el-button>
                            </div>
                            <div style="margin-top: 10px">
                                <el-button size="mini" @click="deleteTs(scope.row.id)" type="danger">删除</el-button>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="billNo"
                        width="140"
                        label="订单编号">
                    <template slot-scope="scope">
                        <a style="text-decoration:underline;"
                           :href="url+scope.row.billNo" target="_Blank">{{scope.row.order.billNo}}</a>
                    </template>
                </el-table-column>
                <!--<el-table-column-->
                        <!--width="80"-->
                        <!--prop="memberAccount"-->
                        <!--:show-overflow-tooltip="true"-->
                        <!--label="会员账户"-->
                <!--&gt;</el-table-column>-->
                <el-table-column
                        width="100"
                        prop="product.name"
                        label="服务项目">
                    <template slot-scope="scope">
                        <div @click="edit(scope.row)">{{scope.row.product.name}}</div>
                    </template>
                </el-table-column>
                <!--<el-table-column-->
                        <!--width="100"-->
                        <!--prop="order.startTime"-->
                        <!--label="服务时间"-->
                        <!--:show-overflow-tooltip="true"-->
                <!--&gt;</el-table-column>-->
                <el-table-column
                        width="80"
                        prop="typeDesc"
                        label="投诉类型"
                ></el-table-column>
                <!--<el-table-column-->
                        <!--width="80"-->
                        <!--prop="channelDesc"-->
                        <!--label="投诉渠道"-->
                <!--&gt;</el-table-column>-->
                <el-table-column
                        width="80"
                        prop="violationDesc"
                        label="投诉判断"
                ></el-table-column>
                <el-table-column
                        width="80"
                        label="投诉状态"
                        prop="stateDesc"
                ></el-table-column>
                <el-table-column
                        width="110"
                        prop="employeeNo"
                        label="被投诉人">
                    <template slot-scope="scope">
                        <p style="font-size: 12px" v-for="item in scope.row.tsEmployee">
                            {{item.no}} ({{item.realName}})
                        </p>
                    </template>
                </el-table-column>
                <el-table-column
                        width="100"
                        prop="employeeNo"
                        label="工龄">
                    <template slot-scope="scope">
                        <p style="font-size: 12px" v-for="item in scope.row.orderTsEmployees">
                            {{item.workingYear}}
                        </p>
                    </template>
                </el-table-column>
                <el-table-column
                        width="150"
                        prop="createTime"
                        label="投诉时间"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        width="150"
                        prop="lastTime"
                        label="完结时间"
                ></el-table-column>

                <el-table-column
                        width="90"
                        prop="dealName"
                        label="处理人"
                ></el-table-column>
                <el-table-column
                        width="90"
                        prop="lastDealName"
                        label="最后处理人">
                    <template slot-scope="scope">
                        <span v-if="scope.row.lastDealName==null">
                            {{scope.row.lastDealNo}}
                        </span>
                        <span v-else>
                            {{scope.row.lastDealName}}
                        </span>
                    </template>
                </el-table-column>
                <!--<el-table-column-->
                        <!--width="100"-->
                        <!--prop="result"-->
                        <!--label="处理结果"-->
                <!--&gt;</el-table-column>-->
            </el-table>

            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :current="this.dto.pageNum"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="10"/>
            </div>
        </div>
        <Modal v-model="tsModal" class="Modal" :width="screenWidth" title="查看"
               :mask-closable="false"
               @on-cancel="getData()">
            <div class="addBody">
                <ts-order-choose v-if="tsModal" @init-choose="initChooseProject" :stage="stage"
                                 @close-modal="closeCurrModal"></ts-order-choose>
            </div>
            <div slot="footer">
            </div>
        </Modal>

        <Modal v-model="appeal" class="Modal" :width="screenWidth1" title="申诉"
               :mask-closable="false"
               @on-cancel="getData()">
            <div class="addBody">
                <ts-order-appeal v-if="appeal" @init-choose="initChooseProject" :stage="stage"
                                 @close-modal="closeCurrModal"></ts-order-appeal>
            </div>
            <div slot="footer">
            </div>
        </Modal>
    </div>

</template>

<script>
    import tsOrderChoose from '@/components/page/wordOrder/choose/tsOrderChoose.vue'
    import tsOrderAppeal from '@/components/page/wordOrder/choose/tsOrderAppeal.vue'

    export default {
        data() {
            return {
                loading: true,
                loadingExcel: false,
                stage: null,
                tsId: null,
                screenWidth: '88%',
                screenWidth1: '50%',
                tsModal: false,
                appeal: false,
                list: null,
                url: "https://yun.xiaoyujia.com/Account/TokenLogin?Token=" + localStorage.getItem('token') + "&returnUrl=/order/baseinfo?BillNo=",
                pageSizeOpts: [10, 20, 30],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                dto: {
                    isTransfer:"",
                    productCategoryId:"",
                    billNo: null,
                    no: null,
                    accountName: null,
                    tsStartTime: null,
                    tsEndTime: null,
                    serviceTime: null,
                    serviceEndTime: null,
                    tsState: null,
                    tsType: null,
                    tsChannel: null,
                    pageSize: 10,
                    pageNum: 1,
                    violation: null
                },
                typeList: [],
                stateList: [],
                channelList: [],
            };
        },
        components: {
            'tsOrderChoose': tsOrderChoose,
            'tsOrderAppeal': tsOrderAppeal
        },
        created() {
            let id=this.$route.query.id;
            if(id){
                this.loading=false;
                this.stage={
                    id:id,
                    stage:1
                };
                this.tsModal=true;
                return
            }
            this.getData();
            this.getTypeList();
            this.getStateList();
            this.getChannelList();
            this.getProduct();
        },
        computed: {},
        methods: {
            getTypeList() {
                this.$postUrl("get_dict", 112, null, {}).then(res => {
                    if (res.status === 200) {
                       ;
                        this.typeList = res.data
                    }
                })
            },
            deleteTs(id) {
                this.$confirm('此操作将永久删除该投诉, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$getData("tsOrderDelete", {id: id}).then(res => {
                        this.getData()
                    });
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },
            getStateList() {
                this.$postUrl("get_dict", 113, null, {}).then(res => {
                    if (res.status === 200) {
                       ;
                        this.stateList = res.data
                    }
                })
            },
            getChannelList() {
                this.$postUrl("get_dict", 114, null, {}).then(res => {
                    if (res.status === 200) {
                       ;
                        this.channelList = res.data
                    }
                })
            },
            getData() {
                this.$postData("tsOrderList", this.dto, {}).then(res => {
                    this.loading = false;
                    if (res.status === 200) {
                       ;
                        this.list = res.data.list;
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    }
                });
            },
            edit(stage) {
                this.stage = stage;
                this.tsModal = true;
            },
            appealMush(stage) {
                this.stage = stage;
                this.appeal = true;
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal(data) {
                this.tsModal = false;
                this.appeal = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },
            getProduct() {
                this.$getData("category_getByRoleId").then(res => {
                    this.options = res.data;
                    console.log(this.options)
                });
            },
            query() {
                this.loading = true;
                this.dto.pageNum = 1;
                this.getData();
            },

            // 页码大小
            onPageSizeChange(size) {
                this.loading = true;
                this.dto.pageSize = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                this.loading = true;
                this.dto.pageNum = index;
                this.getData();
            },
            exportExcel() {
                this.loadingExcel = true;
                this.$postData("tsExportList", this.dto, {
                    responseType: "arraybuffer"
                }).then(res => {
                   ;
                    this.loadingExcel = false;
                    this.blobExport({
                        tablename: "投诉工单",
                        res: res
                    });
                });
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
