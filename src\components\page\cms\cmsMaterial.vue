<template>
    <div class="table">
        <el-dialog :visible.sync="dialogVisible" append-to-body>
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
        <div class="container">
            <div>
                <div style="margin-top: 15px;">
                    <el-input placeholder="请输入内容" v-model="form.title" class="input-with-select">
                        <el-select v-model="form.type" slot="prepend" placeholder="请选择" style="width: 100px">
                            <el-option label="全部" :value="null"></el-option>
                        </el-select>
                        <el-button slot="append" icon="el-icon-search" @click="form.current=1,getData()">搜索</el-button>
                    </el-input>
                </div>
            </div>

            <div style="margin-top: 10px">
                <el-tabs v-model="activeName" type="border-card" >
                    <el-tab-pane label="素材管理" name="first" >
                        <div>
                            <el-tabs v-model="form.groupName" @tab-click="form.current=1,getData()">
                                <el-tab-pane :label="item" :name="item"  v-for="(item,index) in groupNames" :key="index"></el-tab-pane>
                            </el-tabs>

                            <el-row>
                                <el-col :span="4"  v-for="(item,index) in cmsMaterialList" :key="index">
                                    <el-card :body-style="{ padding: '0px' }">
                                        <img :src="item.url" class="image" @click="handlePictureCardPreview(item)">
                                        <div style="padding: 0px;text-align: center">
                                            <span>{{item.title}}</span>
                                            <div class="bottom clearfix">
                                                <el-button type="text" class="button"
                                                           v-clipboard:copy="item.url"
                                                           v-clipboard:success="onCopy"
                                                           v-clipboard:error="onError">复制链接</el-button>
                                            </div>
                                            <el-button type="primary" style="width: 100%" v-if="type=='edit'" @click="chooseThisModel(item)">选择图片</el-button>
                                        </div>
                                    </el-card>
                                </el-col>
                            </el-row>
                            <div class="pagination">
                                <Page :total="pageInfo.total"
                                      @on-change="onChange"
                                      :show-total="true"
                                      :show-sizer="true"
                                      :page-size-opts="pageSizeOpts"
                                      @on-page-size-change="onPageSizeChange"
                                      :page-size="pageInfo.size"/>
                            </div>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="上传素材" name="second">
                        <div>
                            <el-form :model="dom" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                                <el-form-item label="素材名称" prop="title">
                                    <el-input v-model="dom.title"></el-input>
                                </el-form-item>
                                <el-form-item label="素材上传" >
<!--                                    action="http://biapi.xiaoyujia.com/files/cmsUploadFiles"-->

                                    <el-upload
                                            action="https://biapi.xiaoyujia.com/files/cmsUploadFiles"
                                            :on-success="handleAvatarSuccess"
                                            :limit="1"
                                            ref="upload"
                                            :on-change="changeUpload"
                                            list-type="picture-card"
                                            :auto-upload="false">
                                        <i slot="default" class="el-icon-plus" ></i>
                                        <div slot="file" slot-scope="{file}">
                                            <img
                                                    class="el-upload-list__item-thumbnail"
                                                    :src="file.url" alt=""
                                            >
                                            <span class="el-upload-list__item-actions">
                                            <span
                                                    class="el-upload-list__item-preview"
                                                    @click="handlePictureCardPreview(file)"
                                            >
                                              <i class="el-icon-zoom-in"></i>
                                            </span>

                                            <span
                                                    v-if="!disabled"
                                                    class="el-upload-list__item-delete"
                                                    @click="handleRemove(file)"
                                            >
                                              <i class="el-icon-delete"></i>
                                            </span>
                                          </span>
                                           </div>
                                            </el-upload>

                                </el-form-item>
                                <el-form-item label="类型选择" prop="typeName">
                                    <span  v-for="(item,index) in groupNames" :key="index" style="margin: 5px">
                                        <el-radio v-model="dom.groupName" :label="item" border >{{item}}</el-radio>
                                    </span>
                                                <el-divider></el-divider>
                                                <el-input v-model="dom.groupName"
                                                          style="width: 68%"
                                                          placeholder="自定义类型"
                                                          clearable
                                                ></el-input>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
                                    <el-button @click="resetForm('ruleForm')">重置</el-button>
                                </el-form-item>
                            </el-form>

                        </div>
                    </el-tab-pane>


                </el-tabs>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: "cmsMaterial",
        props:['type'],
        data() {
            return {
                groupNames:[],
                pageSizeOpts:[18,36,72],
                cmsMaterialList:[],
                pageInfo: {total: 18, size: 18, current: 1, pages: 1},
                dialogImageUrl: '',
                dialogVisible: false,
                disabled: false,
                fileList:[],
                activeName:"first",
                form:{
                    groupName:'默认',
                    current: 1,
                    size: 18,
                  type:null,
                  title:null,
                },
                dom:{
                    groupName:'默认',
                    title:null,
                    type:null,
                    url:null,
                    crePerson:localStorage.getItem("realName"),
                    channel:null,

                },
                rules: {
                    title: [
                        {required: true, message: '请输入素材名称', trigger: 'blur'},
                        {min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur'}
                    ],
                    url: [
                        {required: true, message: '上传信息不能为空', trigger: 'blur'}

                    ],
                }
            }
        },
        created(){
            this.getData()
            this.groupNameList()
        },
        methods:{
            chooseThisModel(name) {

                this.$emit('init-choose', name);
            },
            onCopy: function (e) {
                this.$message.success('复制成功: ' + e.text)
            },
            onError: function (e) {
                this.$message.error('复制失败，请重试')
            },
            groupNameList(){
                this.$getData("groupNameList", null).then(res => {
                    if (res.status == 200) {
                        this.groupNames = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })

            },
            getData(){

                this.$postData("cmsMaterialPage", this.form, {}).then(res => {
                    if (res.status == 200) {
                        this.cmsMaterialList = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.form.size = size;
                this.getData();
            },
            onChange(index) {
                console.log(index)
                this.form.current = index;
                this.getData();
            },
            changeUpload(a,b){
                console.log(a)
                this.dom.type=a.raw.type
                console.log( this.dom.type)
            },
            submitForm(formName) {
              if(null==this.dom.title){
                this.$message.error('请填写素材名称');
              }else if(this.dom.title.length>=3&&this.dom.title.length<=20){
                 this.$refs.upload.submit();
              }else{
                 this.$message.error('请注意素材名称的长度');
              }
            },
            resetForm(formName) {
                this.$refs[formName].resetFields();
            },
            handleRemove(file) {
                console.log(file);
                this.$refs.upload.clearFiles();
            },
            handlePictureCardPreview(file) {
                this.dialogImageUrl = file.url;
                this.dialogVisible = true;
            },
            handlePreview(file) {
                console.log(file);
            },
            handleAvatarSuccess(res, file) {
                this.dom.url = res.data;
                this.$refs['ruleForm'].validate((valid) => {
                    if (valid) {
                        this.$postData("saveCmsMaterial", this.dom, {}).then(res => {
                            if (res.status === 200) {
                                this.$message.success("上传素材成功" );
                                this.$refs.upload.clearFiles()
                                this.resetForm('ruleForm')
                                this.form.current=1
                                this.getData()
                            } else {
                                this.$message.error("上传素材失败。请稍后重试");
                            }
                        })
                    } else {
                        this.$message.error("上传素材失败。请稍后重试")
                       return false;
                    }
                });
            },
        }
    }
</script>
<style>
    .el-card__header{
        color: white;
        background: #2d8cf0;
    }
     {
        background: #fff;
        color: #2d8cf0;;
    }
</style>
<style scoped>
    .image{
        width: 100%;
        height: 100px;
    }
    .el-select .el-input {
        width: 130px;
    }
    .input-with-select .el-input-group__prepend {
        background-color: #fff;
    }
</style>
