<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="合同编号">
                                <el-input
                                        v-model="model.no"
                                        placeholder="合同编号"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="经理名称">
                                <el-input
                                        v-model="model.agentName"
                                        placeholder="经理名称"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="客户名称">
                                <el-input
                                        v-model="model.memberName"
                                        placeholder="客户名称"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item label="保姆信息">
                                <el-input
                                        v-model="model.employeeName"
                                        placeholder="保姆名称或者工号"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <!--                        <el-col :span="8">-->
                        <!--                            <el-form-item  label="合同状态">-->
                        <!--                                <el-select v-model="model.status" clearable placeholder="请选择"   style="width:200px;" class="handle-input mr10">-->
                        <!--                                    <el-option-->
                        <!--                                            v-for="item in states"-->
                        <!--                                            :key="item.value"-->
                        <!--                                            :label="item.label"-->
                        <!--                                            :value="item.value">-->
                        <!--                                    </el-option>-->
                        <!--                                </el-select>-->
                        <!--                            </el-form-item >-->
                        <!--                        </el-col>-->
                        <el-col :span="8">
                            <el-form-item prop="time" label="创建年月">
                                <el-date-picker
                                        style="width: 190px;"
                                        v-model="model.yearMonth"
                                        type="month"
                                        placeholder="选择年月">
                                </el-date-picker>
                            </el-form-item>

                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="合同类型">
                                <el-select v-model="model.contractType" clearable placeholder="请选择" style="width:200px;"
                                           class="handle-input mr10">
                                    <el-option
                                            v-for="item in contractTypes"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="合同版本">
                                <el-checkbox-group v-model="model.enCountList" @change="query()">
                                    <el-checkbox-button label="0" name="0" border>电子单</el-checkbox-button>
                                    <el-checkbox-button label="1" name="1" border>纸质单</el-checkbox-button>
                                </el-checkbox-group>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="门店名称" v-if="storeId==1||storeId==2">
                                <Select filterable style="width:190px" v-model="model.storeId">
                                    <Option value="">请选择</Option>
                                    <Option value="1">平台</Option>
                                    <Option v-for="(item,index) in storeList" :value="item.id" :key="index">
                                        {{ item.storeName }}
                                    </Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="客户手机">
                                <el-input
                                        v-model="model.memberPhone"
                                        placeholder="客户手机"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="合同状态">
                                <el-checkbox-group v-model="model.statusList" @change="query()">
                                    <el-checkbox-button label="0" name="0" border>暂存</el-checkbox-button>
                                    <el-checkbox-button label="1" name="1" border>生效中</el-checkbox-button>
                                    <el-checkbox-button label="3" name="3" border>补签单</el-checkbox-button>
                                    <el-checkbox-button label="2" name="2" border>已完成</el-checkbox-button>
                                    <el-checkbox-button label="4" name="4" border>未续签</el-checkbox-button>
                                    <el-checkbox-button label="5" name="5" border>合同终止</el-checkbox-button>
                                    <el-checkbox-button label="99" name="99" border>作废单</el-checkbox-button>
                                    <el-checkbox-button label="100" name="100" border>暂存下户</el-checkbox-button>
                                </el-checkbox-group>
                            </el-form-item>

                        </el-col>
                        <el-col :span="12">
                            <el-form-item prop="time" label="创建时间">

                                <el-date-picker
                                        :unlink-panels="true"
                                        v-model="model.time"
                                        type="datetimerange"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        :default-time="['00:00:00']">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <div>&emsp;</div>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item>
                                <el-button type="primary" @click="query()">搜索</el-button>
                                <el-button type="info" @click="re()">重置</el-button>
                                <el-button type="primary" @click="addModal=true">添加保姆合同</el-button>
                                <el-button type="warning" @click="addYuesaoModal=true">添加月嫂合同</el-button>
                                <!--<el-button type="success" @click="addInstallmentModal=true">添加分期合同</el-button>-->
                                <el-button type="danger"
                                           @click="show.siginType=3,contractSiginCommomModal=true,siginTitle='公章-法人签名管理'">
                                    公章-法人签名管理
                                </el-button>
                            </el-form-item>
                        </el-col>
                        <div style="float: right">
                            <!--                            <el-button type="success" round  @click="show.siginType=3,contractSiginCommomModal=true,siginTitle='公章-法人签名管理'">公章-法人签名管理</el-button>-->
                            <!--                            <el-button type="success" round  @click="show.siginType=2,contractSiginCommomModal=true,siginTitle='个人签名管理'">个人签名管理</el-button>-->
                            <!--                            <el-button type="success" round  @click="show.siginType=1,contractSiginCommomModal=true,siginTitle='合作保姆签名管理'" >合作保姆签名管理</el-button>-->
                        </div>
                    </el-row>

                </el-form>
            </div>
            <el-dialog
                    title="请选择转发给哪个经纪人："
                    :visible.sync="ffAialogVisible"
                    width="30%">
                <div>
                    <p style="color: #f00f14">注意：合同转移不会改变合同原有内容。只做合同操作人权限归属转移。显示的经纪人为原经纪人</p>
                    <!--                {{changeDom}}-->
                    合同编号：{{changeDom.no}}<br>
                    客户号码：{{changeDom.memberPhone}}<br>
                    当前经纪人：{{changeDom.agentName}}
                    （ {{changeDom.agentNo}} ）<br>
                </div>
                <el-tabs v-model="activeName" type="border-card">
                    <el-tab-pane label="本门店" name="first">
                        <div v-for="item in agentList" class="agent-box" v-if="item.storeId==storeId">
                            {{item.no}}--{{item.realName}}
                            <el-button size="mini" type="primary"
                                       @click="changeDom.agentId=item.id,save()">合同转移
                            </el-button>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="全部" name="second">
                        <div v-for="item in agentList" class="agent-box">
                            {{item.no}}--{{item.realName}}
                            <el-button size="mini" type="primary"
                                       @click="changeDom.agentId=item.id,save(changeDom.status)">合同转移
                            </el-button>
                        </div>
                    </el-tab-pane>

                </el-tabs>

            </el-dialog>
            <div style="background: #ddd;padding: 12px">当前数目：<b>{{pageInfo.total}}</b></div>
            <el-table :data="list" :header-cell-style="{background:'#ddd'}" border @sort-change="sortChange"
                      class="table" ref="multipleTable" @cell-dblclick="edit" v-loading="loading">
                <el-table-column type="expand" label="操作" min-width="80">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="contractSupplyEdit(scope.row)" type="primary">补签</el-button>
                        <el-button size="mini" @click="getUrl(scope.row)" type="primary">签名</el-button>

                        <el-button v-show="scope.row.holidayId ==null && scope.row.contractType==0" size="mini"
                                   @click="contractHoliday(scope.row)" type="info">放假规则
                        </el-button>
                        <el-button v-show="scope.row.holidayId!=null && scope.row.contractType==0" size="mini"
                                   @click="contractHoliday(scope.row)" type="primary">放假规则
                        </el-button>
                        <el-button size="mini" @click="contractOrder(scope.row)" type="primary"
                                   :type="scope.row.orderId ==null?'info':'primary'">绑单
                        </el-button>
                        <el-button size="mini" type="primary" @click="downloadTest(scope.row.id)"
                                   v-show=" scope.row.contractType==0">下载
                        </el-button>
                        <el-button size="mini" type="primary" @click="downloadYueSaoTest(scope.row.id)"
                                   v-show=" scope.row.contractType==1">下载
                        </el-button>
                        <el-button size="mini" @click="edit(scope.row)" type="info" v-show="scope.row.contractType==0">
                            编辑
                        </el-button>
                        <el-button size="mini" @click="editYuesao(scope.row)" type="info"
                                   v-show="scope.row.contractType==1">编辑
                        </el-button>

                        <el-button v-if="scope.row.status==1||scope.row.status==null" size="mini"
                                   @click="save(scope.row,0)" type="primary">暂存
                        </el-button>
<!--                        <el-button v-if="scope.row.status==0" size="mini" @click="changeDom = scope.row,save()" type="info">生效-->
<!--                        </el-button>-->
                        <el-button size="mini" @click="contractEnclosure(scope.row)"
                                   :type="scope.row.enCount==null?'info':'primary'">纸质
                        </el-button>
                        <el-button size="mini" @click="changeAgent(scope.row)">合同转移</el-button>
                        <el-button size="mini" @click="showFdd(scope.row)" type="primary">查看法大大签名</el-button>
                        <el-button size="mini" @click="downloadFadadaContract(scope.row)" type="primary">法大大合同下载</el-button>
                        <el-button size="mini" @click="downloadTencentContract(scope.row)" type="primary">腾讯电子签合同下载</el-button>
                    </template>
                </el-table-column>
                <!--                <el-table-column-->
                <!--                        label="操作"-->
                <!--                        fixed="right"-->
                <!--                        min-width="580">-->
                <!--                    <template slot-scope="scope">-->
                <!--                        &lt;!&ndash;                        <el-button size="mini" @click="getSigin(scope.row.memberId,scope.row.id)" type="primary">客户签名</el-button>&ndash;&gt;-->
                <!--                        <el-button size="mini" @click="contractSupplyEdit(scope.row)" type="primary">补签</el-button>-->
                <!--                        <el-button size="mini" @click="getUrl(scope.row)" type="primary">签名</el-button>-->

                <!--                        <el-button v-show="scope.row.holidayId ==null && scope.row.contractType==0" size="mini" @click="contractHoliday(scope.row)" type="info" >放假规则</el-button>-->
                <!--                        <el-button v-show="scope.row.holidayId!=null && scope.row.contractType==0" size="mini" @click="contractHoliday(scope.row)" type="primary" >放假规则</el-button>-->
                <!--                        <el-button size="mini" @click="contractOrder(scope.row)" type="primary" :type="scope.row.orderId ==null?'info':'primary'">绑单</el-button>-->
                <!--                        <el-button size="mini" type="primary"   @click="downloadTest(scope.row.id)" v-show=" scope.row.contractType==0">下载</el-button>-->
                <!--                        <el-button size="mini" type="primary"   @click="downloadYueSaoTest(scope.row.id)" v-show=" scope.row.contractType==1">下载</el-button>-->
                <!--                        <el-button size="mini" @click="edit(scope.row)" type="info" v-show="scope.row.contractType==0">编辑</el-button>-->
                <!--                        <el-button size="mini" @click="editYuesao(scope.row)" type="info" v-show="scope.row.contractType==1">编辑</el-button>-->

                <!--                        <el-button v-if="scope.row.status==1||scope.row.status==null" size="mini" @click="save(scope.row,0)" type="primary">暂存</el-button>-->
                <!--                        <el-button v-if="scope.row.status==0" size="mini" @click="save(scope.row,1)" type="info">生效</el-button>-->
                <!--                        <el-button  size="mini" @click="contractEnclosure(scope.row)" :type="scope.row.enCount==null?'info':'primary'" >纸质</el-button>-->

                <!--                        &lt;!&ndash;                        <el-button v-if="scope.row.status==1" size="mini" @click="save(scope.row,2)" type="info">完成</el-button>&ndash;&gt;-->

                <!--                        &lt;!&ndash;                        <el-button size="mini" @click="showOrder(scope.row.orderId)" type="primary">订单</el-button>&ndash;&gt;-->

                <!--                    </template>-->

                <!--                </el-table-column>-->
                <el-table-column
                        prop="no"
                        label="合同编号"
                        width="150">
                </el-table-column>
                <el-table-column
                        prop="status"
                        label="状态"
                        width="80"
                        sortable="custom">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.status==1" type="success">生效中</el-tag>
                        <el-tag v-else-if="scope.row.status==0" type="warning">暂存中</el-tag>
                        <el-tag v-else-if="scope.row.status==2 && scope.row.employeeSignDate!==null" type="primary">
                            已完成
                        </el-tag>
                        <el-tag v-else-if="scope.row.status==2 && scope.row.employeeSignDate==null" type="primary">
                            完成中
                        </el-tag>
                        <el-tag v-else-if="scope.row.status==3" type="info">补签单</el-tag>
                        <!--<el-tag v-else-if="scope.row.status==4" type="warning">已过期（未续签）</el-tag>-->
                        <el-tag v-else-if="scope.row.status==99" type="info">作废单</el-tag>
                        <el-tag v-else-if="scope.row.status==100" type="warning">暂存下户</el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="contractDay"
                        label="合同天数"
                        width="50"
                >
                </el-table-column>
                <el-table-column
                        prop="memberName"
                        label="客户名称"
                        width="190"
                >
                    <template slot-scope="scope">
                        <el-link type="primary" @click="toMember(scope.row.memberId)">{{scope.row.memberName}}</el-link>
                        （{{scope.row.memberPhone}}）
                    </template>
                </el-table-column>

                <el-table-column
                        prop="agentName"
                        label="经理名称"
                        width="90"
                ></el-table-column>
                <el-table-column
                        prop="contractType"
                        label="合同类型"
                        width="100"
                >
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.contractType==1" type="success">月嫂合同</el-tag>
                        <el-tag v-else-if="scope.row.contractType==0" type="primary">保姆合同</el-tag>

                    </template>
                </el-table-column>
                <el-table-column
                        prop="employeeName"
                        label="保姆名称"
                        width="180"
                >
                    <template slot-scope="scope">
                        <el-link type="primary" @click="toBaomuInfo(scope.row.employeeId)">{{scope.row.employeeName}}
                            ({{scope.row.employeeNo}})
                        </el-link>
                    </template>
                </el-table-column>

                <el-table-column
                        prop="employeeNames"
                        label="补签保姆"
                        width="100"
                >
                </el-table-column>
                <el-table-column
                        prop="servicePay"
                        label="资费"
                        sortable="custom"
                        show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                        prop="serviceStarDate"
                        label="合同开始时间"
                        width="180"
                        sortable="custom"
                ></el-table-column>
                <el-table-column
                        prop="year"
                        label="创建年份"
                        width="80"
                ></el-table-column>
                <el-table-column
                        prop="month"
                        label="创建月份"
                        width="80"
                ></el-table-column>

                <el-table-column
                        prop="serviceContent"
                        label="服务内容"
                        min-width="320"
                        sortable="custom"
                ></el-table-column>

                <el-table-column
                        prop="inPay"
                        label="中介费"
                        min-width="120"
                        sortable="custom"
                ></el-table-column>
                <el-table-column
                        prop="createDate"
                        label="创建时间"
                        min-width="180"
                        sortable="custom"
                ></el-table-column>

            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>

        <Modal v-model="contractSupplyModal" class="Modal" :width="screenWidth" title="保姆合同补充" :mask-closable="false">
            <div class="addBody">

                <contractSupply v-if="contractSupplyModal" @init-choose="initChooseProject"
                                @close-modal="closeCurrModal" :model="show"></contractSupply>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="saveModal" class="Modal" :width="screenWidth" title="保姆合同详情" :mask-closable="false">
            <div class="addBody">

                <contractUpdate v-if="saveModal" @init-choose="initChooseProject"
                                @close-modal="closeCurrModal" :model="show"></contractUpdate>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="saveYuesaoModal" class="Modal" :width="screenWidth" title="月嫂合同详情" :mask-closable="false">
            <div class="addBody">

                <contractYuesaoUpdate v-if="saveYuesaoModal" @init-choose="initChooseProject"
                                      @close-modal="closeCurrModal" :model="show"></contractYuesaoUpdate>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="addModal" class="Modal" :width="screenWidth" title="添加保姆合同" :mask-closable="false">
            <div class="addBody" style="min-height:400px">
                <contractAdd v-if="addModal" @init-choose="initChooseProject"
                             @close-modal="closeCurrModal"></contractAdd>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="addInstallmentModal" class="Modal" :width="screenWidth" title="添加分期合同" :mask-closable="false">
            <div class="addBody" style="min-height:400px">
                <contractInstallmentAdd v-if="addInstallmentModal" @init-choose="initChooseProject"
                                        @close-modal="closeCurrModal"></contractInstallmentAdd>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="addYuesaoModal" class="Modal" :width="screenWidth" title="添加月嫂合同" :mask-closable="false">
            <div class="addBody" style="min-height:400px">
                <contractYuesaoAdd v-if="addYuesaoModal" @init-choose="initChooseProject"
                                   @close-modal="closeCurrModal"></contractYuesaoAdd>
            </div>
            <div slot="footer">
            </div>
        </Modal>

        <Modal v-model="orderModal" class="Modal" :width="screenWidth" title="添加" :mask-closable="false">
            <div class="addBody">

                <orderUpdate v-if="orderModal" @init-choose="initChooseProject"
                             @close-modal="closeCurrModal" :model="show"></orderUpdate>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="contractSiginCommomModal" class="Modal" :width="screenWidth" :title="siginTitle"
               :mask-closable="false">
            <div class="addBody">
                <contractSiginCommom v-if="contractSiginCommomModal" @init-choose="initChooseProject"
                                     @close-modal="closeCurrModal" :model="show"></contractSiginCommom>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="contractHolidayModal" class="Modal" :width="screenWidth" title="保姆合同假期规则"
               :mask-closable="false">
            <div class="addBody">
                <contractHoliday v-if="contractHolidayModal" @init-choose="initChooseProject"
                                 @close-modal="closeCurrModal" :model="show"></contractHoliday>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="contractOrderModal" class="Modal" :width="screenWidth" title="合同绑定订单" :mask-closable="false">
            <div class="addBody">
                <contractOrder v-if="contractOrderModal" @init-choose="initChooseProject"
                               @close-modal="closeCurrModal" :model="show"></contractOrder>
            </div>
            <div slot="footer">
            </div>
        </Modal>

        <Modal v-model="contractEnclosureModal" class="Modal" :width="screenWidth" title="合同上传纸质合同"
               :mask-closable="false">
            <div class="addBody">
                <contract-enclosure v-if="contractEnclosureModal" @init-choose="initChooseProject"
                                    @close-modal="closeCurrModal" :model="show"></contract-enclosure>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <!--        <el-dialog :visible.sync="dialogVisible">-->
        <!--            <img width="100%" :src="dialogImageUrl" alt="">-->
        <!--        </el-dialog>-->

        <el-dialog
                title="签名Url地址"
                :visible.sync="dialogVisible"
                :modal="false"
                width="50%"
        >
            <!--                    <el-input placeholder="请输入技能说明" v-model="url"  id="urlinput">-->
            <!--                    </el-input>-->
            <el-button v-if="mImageUrl!=null" size="mini" @click="reMemberSign()" type="primary">重置客户签名</el-button>
            <el-button v-if="eImageUrl!=null" size="mini" @click="reEmployeeSign()" type="primary">重置阿姨签名</el-button>
            <h2>阿姨签名地址</h2>
            <img width="50%" :src="eImageUrl" alt="">
            <div>点击复制Url,并把地址发送给指定的阿姨。完成合同的阿姨签名。</div>
            <hr>
            <article id="article"> {{url}}</article>
            <br>
            <br>
            <br>
            <h2>客户签名地址</h2>
            <img width="50%" :src="mImageUrl" alt="">
            <div>点击复制Url,并把地址发送给指定的客户。完成合同的客户签名。</div>
            <hr>
            <article id="article2">
                {{url2}}
            </article>
            <span slot="footer" class="dialog-footer">
                        <el-button @click="dialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="dialogVisible = false,copyurl()">复制阿姨Url</el-button>
                        <el-button type="primary" @click="dialogVisible = false,copyurl2()">复制客户Url</el-button>
            </span>
        </el-dialog>


      <el-link :href="hrefUrl" target="_blank" id="elLink"/>

    </div>
</template>

<script>
    import contractAdd from '@/components/page/agent/choose/contractAdd.vue'
    import contractInstallmentAdd from '@/components/page/agent/choose/contractInstallmentAdd.vue'
    import contractInstallmentUpdate from '@/components/page/agent/choose/contractInstallmentUpdate.vue'
    import contractYuesaoAdd from '@/components/page/agent/choose/contractYuesaoAdd.vue'
    import contractUpdate from '@/components/page/agent/choose/contractUpdate.vue'
    import contractYuesaoUpdate from '@/components/page/agent/choose/contractYuesaoUpdate.vue'
    import contractSupply from '@/components/page/agent/choose/contractSupply.vue'
    import orderUpdate from '@/components/page/agent/choose/orderUpdate.vue'
    import contractSiginCommom from '@/components/page/agent/choose/contractSiginCommom.vue'
    import contractOrder from '@/components/page/agent/choose/contractOrder.vue'
    import contractHoliday from '@/components/page/agent/choose/contractHoliday.vue'
    import contractEnclosure from '@/components/page/agent/choose/contractEnclosure.vue'
    import {formatDate} from '@/components/common/utils.js'

    export default {
        data() {
            return {
                changeDom: {},
                activeName: 'first',
                hrefUrl: "",
                agentList: [],
                ffAialogVisible: false,
                value2: null,
                contractId: null,
                states: [{
                    value: null,
                    label: '全部'
                }, {
                    value: '0',
                    label: '暂存'
                }, {
                    value: '1',
                    label: '生效中'
                }, {
                    value: '2',
                    label: '已完成'
                }, {
                    value: '3',
                    label: '补签单'
                }, {
                    value: '4',
                    label: '已过期(未续签)'
                }, {
                    value: '99',
                    label: '作废单'
                }],
                contractTypes: [{
                    value: null,
                    label: '全部'
                }, {
                    value: '0',
                    label: '保姆合同'
                }, {
                    value: '1',
                    label: '月嫂合同'
                }],
                url: null,
                url2: null,
                dialogVisible: false,
                eImageUrl: null,
                mImageUrl: null,
                siginTitle: null,
                show: {
                    siginType: null,
                },
                tableData: [],
                pageSizeOpts: [10, 20, 30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                screenWidth: '80%',//新增对话框 宽度
                saveModal: false,
                saveYuesaoModal: false,
                addModal: false,
                addYuesaoModal: false,
                addInstallmentModal: false,
                orderModal: false,
                contractSupplyModal: false,
                contractSiginCommomModal: false,
                contractHolidayModal: false,
                contractOrderModal: false,
                contractEnclosureModal: false,
                // show: {},
                storeId: Number(localStorage.getItem("storeId")),
                storeList: [],
                model: {
                    memberPhone: null,
                    enCountList: ['0', '1'],
                    enCount: null,
                    yearMonth: null,
                    year: null,
                    month: null,
                    startDateTime: null,
                    endDateTime: null,
                    statusList: ["0", "1", "3"],
                    storeId: Number(localStorage.getItem("storeId")),
                    contractType: null,
                    no: null,
                    time: null,
                    status: null,
                    agentName: null,
                    memberName: null,
                    employeeName: null,
                    orderBy: 'createDate desc',
                    current: 1,
                    size: 10
                },
                update: {
                    id: null,
                    realName: null,

                },
                list: null,
                loading: false
            };
        },
        components: {
            'contractAdd': contractAdd,
            'contractInstallmentAdd': contractInstallmentAdd,
            'contractYuesaoAdd': contractYuesaoAdd,
            'contractUpdate': contractUpdate,
            'contractInstallmentUpdate': contractInstallmentUpdate,
            'contractYuesaoUpdate': contractYuesaoUpdate,
            'orderUpdate': orderUpdate,
            'contractSiginCommom': contractSiginCommom,
            'contractOrder': contractOrder,
            'contractHoliday': contractHoliday,
            'contractSupply': contractSupply,
            'contractEnclosure': contractEnclosure,
        },
        created() {
            this.getData();
            this.getStore();
            this.getAgent();
        },
        computed: {},
        methods: {
          downloadFadadaContract(item) {
            if(item.downloadUrl){
            this.hrefUrl = item.downloadUrl
            setTimeout(() => {
              document.getElementById("elLink").click();
            }, 1000);
            }else{
              this.$message.error("该合同暂未生成法大大合同！");
            }
          },downloadTencentContract(item) {
            this.$getData("viewTencentSignPdf", {no:item.no}).then(res => {
              if (res.code == 0) {
                window.open(res.data.url)
              } else {
                this.$message.error("查询失败，" + res.msg);
              }
            })
          },
          showFdd(row){
            this.$getData("pdfUrl", {contractId:row.no}).then(res => {
              if (res.code == 0) {
                window.open(res.data)
              } else {
                this.$message.error("查询失败，" + res.msg);
              }
            })
          },
            changeAgent(dom) {
                this.changeDom = dom;
                this.ffAialogVisible = true;
            },
            getStore() {
                this.$postData("store_getByList", {}, {}).then(res => {
                    if (res.status == 200) {
                        this.storeList = res.data;
                    }
                });
            },
            toBaomuInfo(id) {
                this.$router.push({path: '/baomuInfo', query: {"id": id}});
            },
            toMember(id) {
                this.$router.push({path: '/memberInfo', query: {"id": id}});
            },
            getAgent() {
                let store = Number(localStorage.getItem("storeId"));
                this.$postData("agent_list", {storeId: null}, {}).then(res => {
                    if (res.status == 200) {
                        this.agentList = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            reMemberSign() {
                this.$alert('是否确认重置客户签名？', '提示', {
                    confirmButtonText: '确定',
                    callback: action => {
                        if (action !== 'cancel') {
                            this.$getUrl("reMemberSign", this.contractId, {}).then(res => {
                                if (res.status == 200) {
                                    this.dialogVisible = false;
                                    this.getData();
                                    this.$message.success("重置客户签名成功，" + res.msg);
                                } else {
                                    this.$message.error("重置客户签名失败，" + res.msg);
                                }
                            })
                        }

                    }
                });
            },
            reEmployeeSign() {
                this.$alert('是否确认重置阿姨签名？', '提示', {
                    confirmButtonText: '确定',
                    callback: action => {
                        if (action !== 'cancel') {
                            this.$getUrl("reEmployeeSign", this.contractId, {}).then(res => {
                                if (res.status == 200) {
                                    this.dialogVisible = false;
                                    this.getData();
                                    this.$message.success("重置客户签名成功，" + res.msg);
                                } else {
                                    this.$message.error("重置客户签名失败，" + res.msg);
                                }
                            })
                        }

                    }
                });


            },
            getUrl(row) {
                // console.log(row)
                this.eImageUrl = null;
                this.mImageUrl = null;
                this.getSigin(row.memberId, row.id);
                this.getESigin(row.employeeId, row.id);
                this.contractId = row.id;
                this.dialogVisible = true;


              const type = Number(row.contractType);
              if (type === 0 || type === 1) {
               this.url2 =  'https://agent.xiaoyujia.com/upbaomu/contractInfo/' + row.id
               this.url =  'https://agent.xiaoyujia.com/upbaomu/baomucontractInfo/' + row.id
              } else if (type === 2) {
                this.url2 =  'https://agent.xiaoyujia.com/upbaomu/contractEscrowInfo/' + row.id
                this.url =  'https://agent.xiaoyujia.com/upbaomu/baomucontractEscrowInfo/' + row.id
              } else if (type === 4) {
                this.url2 =  'https://agent.xiaoyujia.com/upbaomu/contractInstallmentInfo/' + row.id
                this.url =  'https://agent.xiaoyujia.com/upbaomu/baomucontractInstallmentInfo/' + row.id
              }
            },
            copyurl() {
                this.copyArticle();
                return this.$message.success("复制成功。");
            },
            copyurl2() {
                this.copyArticle2();
                return this.$message.success("复制成功。");
            },
            copyArticle(event) {
                const range = document.createRange();
                range.selectNode(document.getElementById('article'));
                const selection = window.getSelection();
                if (selection.rangeCount > 0) selection.removeAllRanges();
                selection.addRange(range);
                document.execCommand('copy');
            },
            copyArticle2(event) {
                const range = document.createRange();
                range.selectNode(document.getElementById('article2'));
                const selection = window.getSelection();
                if (selection.rangeCount > 0) selection.removeAllRanges();
                selection.addRange(range);
                document.execCommand('copy');
            },
            getSigin(id, contractId) {
                if (id == null) {
                    this.$message.error("该合同还未绑定用户，");
                }
                let show = {
                    contractId: contractId,
                    status: 1,
                    // memberId:id,
                    siginType: 0
                };
                this.$postData("get_contractSigin", show, {}).then(res => {
                    if (res.status == 200) {
                        if (res.data == null || res.data.length === 0) {
                            return this.$message.error("客户暂未签名");
                        } else {
                            // console.log(res.data[0].siginUrl);
                            // this.dialogVisible=true
                            this.mImageUrl = res.data[0].siginUrl;
                        }
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getESigin(id, contractId) {
                if (id == null) {
                    return this.$message.error("该合同还未绑定用户，");
                }
                let show = {
                    contractId: contractId,
                    status: 1,
                    employeeId: id,
                    siginType: 1
                };
                this.$postData("get_contractSigin", show, {}).then(res => {
                    if (res.status == 200) {
                        if (res.data == null || res.data.length === 0) {
                            return this.$message.error("阿姨暂未签名");
                        } else {
                            // console.log(res.data[0].siginUrl);
                            // this.dialogVisible=true
                            this.eImageUrl = res.data[0].siginUrl;
                        }

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            save() {
                let dom = this.changeDom;
                let state = this.changeDom.status;
                //console.log(dom)
                if (dom.holidayId == null && dom.contractType == 0) {
                    return this.$Message.error('请先选择放假规则');
                }
                dom.status = state;
                this.$postData("update_contract", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.ffAialogVisible = false;
                        this.$Message.success('修改成功');
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })

            },
            sortChange: function (column, prop, order) {
                if (column == null) {
                    this.model.orderBy = "";
                } else {
                    if (column.order === "ascending") {
                        this.model.orderBy = column.prop + " ASC";
                    }
                    if (column.order === "descending") {
                        this.model.orderBy = column.prop + " DESC";
                    }
                }
                this.getData();
            },
            downloadYueSaoTest(id) {
                this.$postUrl("download_yuesao_contract", id, {}, {
                    responseType: "arraybuffer"
                }).then(res => {
                    if (res == null || res.byteLength === 0) {
                        return this.$message.error("请先把合同填写完整！");
                    }
                    this.$message.success("请稍等，马上开始下载！");
                    const aLink = document.createElement("a");
                    let blob = new Blob([res]);
                    aLink.href = URL.createObjectURL(blob);
                    aLink.download = "服务合同" + ".docx";
                    document.body.appendChild(aLink);
                    aLink.click();
                    document.body.removeChild(aLink);

                });
            },
            downloadTest(id) {
                this.$postUrl("download_contract", id, {}, {
                    responseType: "arraybuffer"
                }).then(res => {
                    if (res == null || res.byteLength === 0) {
                        return this.$message.error("请先把合同填写完整！");
                    }
                    this.$message.success("请稍等，马上开始下载！");
                    const aLink = document.createElement("a");
                    let blob = new Blob([res]);
                    aLink.href = URL.createObjectURL(blob);
                    aLink.download = "服务合同" + ".docx";
                    document.body.appendChild(aLink);
                    aLink.click();
                    document.body.removeChild(aLink);

                });
            },
            getData() {
                this.loading = true;
                this.$postData("contract_page", this.model, {}).then(res => {
                    if (res.status == 200) {
                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                }).finally(_ => {
                    this.loading = false;
                })
            },
            query() {
                if (this.model.yearMonth !== null) {
                    this.model.year = this.model.yearMonth.getFullYear();
                    this.model.month = this.model.yearMonth.getMonth() + 1;
                }

                if (this.model.time != null) {
                    this.model.startDateTime = this.model.time[0];
                    this.model.endDateTime = this.model.time[1];
                }
                if (this.model.enCountList.length === 0 || this.model.enCountList.length === 2) {
                    this.model.enCount = null;
                } else {
                    this.model.enCount = this.model.enCountList[0];
                }
                this.getData();
            },
            re() {
                this.model.memberPhone = null;
                this.model.yearMonth = null;
                this.model.year = null;
                this.model.month = null;
                this.model.time = null;
                this.model.status = null;
                this.model.contractType = null;
                this.model.no = null;
                this.model.baomuName = null;
                this.model.agentName = null;
                this.model.memberName = null;
                this.model.employeeName = null;
                this.model.statusList = [];
                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                // console.log(size)
                this.model.size = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                // console.log(index)
                this.model.current = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.addModal = false;
                this.addInstallmentModal = false;
                this.saveYuesaoModal = false;
                this.addYuesaoModal = false;
                this.orderModal = false;
                this.saveModal = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },
            contractSupplyEdit(id) {
                this.show = id;
                // console.log(this.model)
                this.contractSupplyModal = true;
            },
            edit(id) {
                this.show = id;
                // console.log(this.model)
                this.saveModal = true;
            },
            editYuesao(id) {
                this.show = id;
                // console.log(this.model)
                this.saveYuesaoModal = true;
            },
            contractOrder(id) {
                this.show = id;
                this.contractOrderModal = true;
            },
            contractHoliday(id) {
                this.show = id;
                this.contractHolidayModal = true;
            },
            contractEnclosure(id) {
                this.show = id;
                this.contractEnclosureModal = true;
            },
            showOrder(id) {
                this.$postUrl("get_order", id, {}).then(res => {
                    if (res.status == 200) {
                        this.show = res.data;
                        this.orderModal = true;
                    } else {
                        return this.$message.error("查询失败，" + res.msg);
                    }
                });
                // console.log(id)
            }
        }
    };
</script>

<style scoped>

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }

    .agent-box {
        padding: 10px;
        border: 1px solid #ddd;
    }
</style>
