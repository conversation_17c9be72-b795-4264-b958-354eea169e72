<template>
  <div style=" border: 1px solid #eee;width: 100%">
    <el-container>
      <el-container style="background: white">
        <el-main>
          <h1>小程序</h1>
          <el-button type="primary" size="small" @click="dialogVisible = true">添加小程序</el-button>
          <el-divider></el-divider>
          <div>
            <el-row :gutter="20" type="flex" justify="center">
              <el-col :span="10">
                <el-input placeholder="请输入内容" prefix-icon="el-icon-search" clearable
                          width="100" v-model="qiyeState.miniprogramTitle">
                </el-input>
              </el-col>
              <el-col :span="8">
                <el-button type="primary" @click="qiyeAppletByCondition()">查询</el-button>
                <el-button type="primary" plain @click="reset()">重置</el-button>
                <el-button type="warning" v-if="!delectImg" icon="el-icon-delete" @click="delectImg=!delectImg" plain>
                  删除小程序
                </el-button>
                <el-button v-if="delectImg" @click="delectImg=!delectImg" plain>取消删除</el-button>
                <el-button type="danger" v-if="delectImg" icon="el-icon-delete" @click="determine()" plain>确定删除
                </el-button>
              </el-col>
            </el-row>
          </div>
          <el-row v-loading="loading">
            <el-col :span="6" v-for="(item,index) in list" :key="index" style="margin: 30px">
              <el-card :body-style="{ padding: '0px'}" shadow="hover">
                <el-image class="image" :src="item.sparePicture" fit="contain"></el-image>
                <div style="padding:  0 0 0 10px">
                  <el-checkbox-group v-model="checkList" v-if="delectImg" size="large">
                    <el-checkbox-button :label="item.id">
                      <span style="font-size: 20px;font-weight:bold">{{ item.miniprogramTitle }}</span>
                    </el-checkbox-button>
                  </el-checkbox-group>
                  <span style="font-size:20px" v-if="!delectImg">{{ item.miniprogramTitle }}</span>
                  <el-button type="primary" v-if="choiceImg2" plain round class="button"
                             @click="determinePicture(item)">选择小程序
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :current="this.qiyeState.current"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="9"/>
          </div>
        </el-main>
        <el-dialog title="小程序上传" :visible.sync="dialogVisible" width="50%" center>
          <el-form label-width="100px">
            <el-form-item label="标题" required>
              <el-input v-model="qiyeState.miniprogramTitle" maxlength="64" style="width:245px">
              </el-input>
            </el-form-item>
            <el-form-item label="page路径" required>
              <el-input v-model="qiyeState.page" maxlength="64" style="width:245px">
              </el-input>
            </el-form-item>
            <el-form-item label="小程序" required>
              <el-select v-model="qiyeState.appid" placeholder="请选择">
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="封面图片" required>
              <el-card class="box-card" shadow="always">
                <div class="text item" @click="selectPicture = true">
                  <i style=" font-size: 40px; " class="el-icon-plus" v-if="qiyeState.sparePicture===null"></i>
                  <el-image class="image" :src=qiyeState.sparePicture fit="contain"
                            v-if="qiyeState.sparePicture!==null"></el-image>
                </div>
              </el-card>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm()">立即创建</el-button>
          </span>
          <el-dialog title="选择图片" :visible.sync="selectPicture" width="70%" append-to-body>
            <pictureSelection :choiceImg="choiceImg" @picture="picture"></pictureSelection>
          </el-dialog>
        </el-dialog>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import pictureSelection from "./pictureSelection";

export default {
  name: "applet",
  props: ['choiceImg2'],
  // 注册组件
  components: {
    pictureSelection,
  },
  data() {
    return {
      options: [{
        label: '小羽佳家政',
        value: 'wx9272efff9f958cc0'
      },{
        label: '保姆管家',
        value: 'wxc33730c9e09594f8'
      },{
        label: '家姐联盟',
        value: 'wx8342ef8b403dec4e'
      },{
        label: '家姐课堂',
        value: 'wxd64d55d779f303d0'
      }],
      dialogVisible: false,
      choiceImg: true,
      selectPicture: false,
      loading: false,
      delectImg: false,
      checkList: [],
      pageSizeOpts: [9, 18, 27],
      pageInfo: {total: 9, size: 9, current: 1, pages: 1},
      dom: {
        type: null,
      },
      qiyeState: {
        sparePicture: null,
        miniprogramTitle: null,
        pic_media_id: null,
        appid: "wx9272efff9f958cc0",
        page: null,
        size: 9,
        current: 1,
        operatorId: null,
      },
      list: [],
    }
  },
  created() {
    this.qiyeState.operatorId = localStorage.getItem("id")
    this.qiyeAppletByCondition()
  },
  methods: {
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.qiyeState.current = index;
      this.qiyeAppletByCondition();
    },
    //选择小程序
    determinePicture(app) {
      this.$emit("applet", app)
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.qiyeState.size = size;
      this.qiyeAppletByCondition();
    },
    //选择图片
    picture(val) {
      this.qiyeState.sparePicture = val
      this.selectPicture = false
    },
    //提交
    submitForm() {
      //图片链接转企业微信mediaid
      this.$getData("pic_media_id", {sparePicture: this.qiyeState.sparePicture}).then(res => {
        if (res.status === 200) {
          this.qiyeState.pic_media_id = res.data
          this.addQiyeApplet(this.qiyeState)
        }
      })
    },
    //数据库查询
    qiyeAppletByCondition() {
      this.$postData("qiyeAppletByCondition", this.qiyeState).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    //保存到数据库
    addQiyeApplet(qiyeApplet) {
      this.$postData("addQiyeApplet", qiyeApplet).then(res => {
        if (res.status === 200) {
          this.$message({message: '保存成功', type: 'success'});
          this.reset()
          this.dialogVisible = false
        }
      })
    },
    //重置
    reset() {
      this.qiyeState.sparePicture = null;
      this.qiyeState.miniprogramTitle = null;
      this.qiyeState.pic_media_id = null;
      this.qiyeState.page = null;
      this.qiyeAppletByCondition()
    },
    //删除小程序
    determine() {
      this.delectImg = !this.delectImg
      this.$postData("delectQiyeApplet", this.checkList).then(res => {
        if (res.status === 200) {
          this.reset()
          if (res.data === 1) {
            this.$message.success("删除小程序成功");
          } else {
            this.$message.error("删除小程序失败");
          }
        }
      })
    },
  },
}
</script>

<style scoped>
.text {
  font-size: 10px;
  text-align: center;
}

.item {
  padding: 10px 0;
}

.box-card {
  width: 180px;
}

.image {
  width: 100%;
  height: 180px;
  display: block;
}

.button {
  float: right;
}
</style>
