<template>
	<div class="table">
		<el-drawer size="50%" :with-header="false" :visible.sync="drawer" direction="rtl">
			<div class="drawerForm">
				<h1>门店信息</h1>
				<el-divider></el-divider>
				<el-form ref="form">
					<el-form-item label="门店选择：">
						<Select filterable style="width:190px" v-model="agentStore.storeId">
							<Option value="">请选择</Option>
							<Option value="1">平台</Option>
							<Option v-for="(item,index) in storeList" :value="item.id" :key="index">{{
                                item.storeName}}
							</Option>
						</Select>
					</el-form-item>

					<el-form-item label="接收人员：" >
						<el-autocomplete v-model="agentStore.agentName" :fetch-suggestions="querySearchAsync"
							placeholder="请输入内容" @select="handleSelect" :trigger-on-focus="false" style="width: 500px">
						</el-autocomplete>
					</el-form-item>

					<el-form-item label="门店人数：">
						<el-input-number v-model="agentStore.num" :min="1" :max="1000" label="描述文字"></el-input-number>
					</el-form-item>

					<el-form-item label="门店积分：">
						<el-input-number v-model="agentStore.integral" :min="0" :max="1000"
							label="描述文字"></el-input-number>
					</el-form-item>

					<el-form-item>
						<el-button type="primary" @click="onSubmit(agentStore)">立即创建</el-button>
						<el-button @click="drawer=false">取消</el-button>
					</el-form-item>

				</el-form>

			</div>

		</el-drawer>

		<el-drawer size="50%" :with-header="false" :visible.sync="drawerRule" direction="rtl">
			<div class="drawerForm">
				<h1>添加服务分规则</h1>
				<el-divider></el-divider>
				<el-form ref="form">
					<el-form-item label="* 规则类型：">
						<el-select v-model="agentStoreRule.ruleType" placeholder="请选择" style="width: 100px;">
							<el-option v-for="(item,index) in ruleTypeList" :key="index" :label="item.text"
								:value="item.id"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="* 规则分组：">
						<el-select v-model="agentStoreRule.ruleGroup" placeholder="请选择" style="width: 100px;">
							<el-option v-for="(item,index) in ruleGroupList" :key="index" :label="item.text"
								:value="item.id"></el-option>
						</el-select>
					</el-form-item>

					<el-form-item label="* 规则标题：">
						<el-input v-model="agentStoreRule.ruleTitle" style="width: 50%;" type="textarea"
							placeholder="请输入规则标题" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</el-form-item>

					<el-form-item label="* 规则内容：">
						<el-input v-model="agentStoreRule.ruleContent" style="width: 50%;" type="textarea"
							placeholder="请输入规则内容" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</el-form-item>

					<el-form-item label="规则备注：">
						<el-input v-model="agentStoreRule.ruleRemark" style="width: 50%;" type="textarea"
							placeholder="请输入规则备注" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</el-form-item>

					<el-form-item label="* 奖惩金额：">
						<div>{{agentStoreRule.ruleType==0?'-':'+'}}￥<el-input v-model="agentStoreRule.ruleAmount"
								style="width: 20%;" placeholder="单位：元"
								onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
							</el-input>
						</div>
					</el-form-item>

					<el-form-item label="* 奖惩积分：">
						<div>{{agentStoreRule.ruleType==0?'-':'+'}}<el-input v-model="agentStoreRule.ruleIntegral"
								style="width: 20%;" placeholder="单位：元"
								onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
							</el-input>分
						</div>
					</el-form-item>

					<el-form-item label="关联课程：">
						<el-select v-model="agentStoreRule.courseId" placeholder="请选择" style="width: 100px;">
							<el-option v-for="(item,index) in courseList" :key="index"
								:label="item.id+'：'+item.courseTitle" :value="item.id"></el-option>
						</el-select>
					</el-form-item>

					<el-form-item label="关联考试：">
						<el-select v-model="agentStoreRule.examId" placeholder="请选择" style="width: 100px;">
							<el-option v-for="(item,index) in examList" :key="index" :label="item.id+'：'+item.examTitle"
								:value="item.id"></el-option>
						</el-select>
					</el-form-item>

					<el-form-item>
						<el-button type="primary" @click="insertAgentStoreRule()">立即添加</el-button>
						<el-button @click="drawerRule=false">取消</el-button>
					</el-form-item>

				</el-form>
			</div>
		</el-drawer>

		<el-drawer size="50%" :with-header="false" :visible.sync="drawerRuleLog" direction="rtl">
			<div class="drawerForm">
				<h1>添加服务分奖惩</h1>
				<el-divider></el-divider>
				<el-form ref="form">
					<el-form-item label="* 奖惩规则：">
						<el-select v-model="agentStoreRuleLog.ruleId" placeholder="请选择" style="width: 100px;">
							<el-option v-for="(item,index) in agentStoreRuleList" :key="index"
								:label="item.id+'：'+item.ruleTitle" :value="item.id"></el-option>
						</el-select>
					</el-form-item>

					<el-form-item label="* 奖惩标题：">
						<el-input v-model="agentStoreRuleLog.logTitle" style="width: 50%;" type="textarea"
							placeholder="请输入奖惩标题" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</el-form-item>

					<el-form-item label="* 奖惩内容：">
						<el-input v-model="agentStoreRuleLog.logContent" style="width: 50%;" type="textarea"
							placeholder="请输入奖惩内容" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</el-form-item>

					<el-form-item label="奖惩备注：">
						<el-input v-model="agentStoreRuleLog.logRemark" style="width: 50%;" type="textarea"
							placeholder="请输入奖惩备注" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</el-form-item>

					<el-form-item label="订单编号：">
						<el-input v-model="agentStoreRuleLog.billNo" style="width: 50%;" type="text"
							placeholder="请输入奖惩关联订单编号">
						</el-input>
					</el-form-item>

					<el-form-item>
						<el-button type="primary" @click="insertAgentStoreRuleLog()">立即添加</el-button>
						<el-button @click="drawerRuleLog=false">取消</el-button>
					</el-form-item>
				</el-form>
			</div>
		</el-drawer>

		<el-drawer size="70%" :with-header="false" :visible.sync="drawerServiceScoreRule" direction="rtl">
			<div class="drawerForm">
				<h1>服务分规则详情</h1>
				<el-divider></el-divider>
				<el-form ref="form">
					<el-form-item label="* 规则标题：">
						<el-input v-model="choiceItem.ruleTitle" style="width: 50%;" type="textarea"
							placeholder="请输入规则标题" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</el-form-item>

					<el-form-item label="* 规则内容：">
						<el-input v-model="choiceItem.ruleContent" style="width: 50%;" type="textarea"
							placeholder="请输入规则内容" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</el-form-item>

					<el-form-item label="* 规则分组：">
						<el-select v-model="choiceItem.ruleGroupId" placeholder="请选择" style="width: 100px;">
							<el-option v-for="(item,index) in serviceScoreRuleGroupList" :key="index"
								:label="item.groupName" :value="item.id"></el-option>
						</el-select>
					</el-form-item>

					<el-form-item label="规则类型：">
						<el-select v-model="choiceItem.ruleType" placeholder="请选择" style="width: 100px;">
							<el-option v-for="(item,index) in ruleTypeList1" :key="index" :label="item.text"
								:value="item.id"></el-option>
						</el-select>
					</el-form-item>

					<el-form-item label="分值预设：">
						<el-input-number v-model="choiceItem.ruleIntegralPresets" :min="0" :max="999999"
							label="分值预设"></el-input-number>
						<el-tooltip class="item" effect="dark" content="优先级高于分值比例，设定后，分值比例栏目将失效" placement="top-start">
							<i class="el-icon-question" style="margin-left:10px;"></i>
						</el-tooltip>
					</el-form-item>

					<el-form-item label="分值比例：">
						<el-input-number v-model="choiceItem.ruleIntegralRate" :precision="2" :step="0.1" :max="1"
							label="分值比例（小数）"></el-input-number>
						<span v-if="choiceItem.ruleIntegralRate">
							{{choiceItem.ruleIntegralRate*100}}%
						</span>
						<span v-else>
							暂未设置
						</span>
						<el-tooltip class="item" effect="dark" content="按照比例计算分值（计算规则：总分-100分*规则分组比例*规则分值比例）"
							placement="top-start">
							<i class="el-icon-question" style="margin-left:10px;"></i>
						</el-tooltip>
					</el-form-item>

					<el-form-item label=" 校验sql：">
						<el-input v-model="choiceItem.checkSql" style="width: 50%;" type="textarea"
							placeholder="请输入校验sql" :autosize="{ minRows: 4, maxRows: 10}" disabled='true'>
						</el-input>
						<el-tooltip class="item" effect="dark" content="由技术人员填写，对员工规则达成情况进行数据校验" placement="top-start">
							<i class="el-icon-question" style="margin-left:10px;"></i>
						</el-tooltip>
					</el-form-item>

					<el-form-item label="达标数量：">
						<el-input-number v-model="choiceItem.reachFlag" :min="0" :max="999999"
							label="达标数量"></el-input-number>
						<el-tooltip class="item" effect="dark" content="规则达标所需达到的值" placement="top-start">
							<i class="el-icon-question" style="margin-left:10px;"></i>
						</el-tooltip>
					</el-form-item>

					<el-form-item label="默认达标：">
						<el-select v-model="choiceItem.defaultReach" placeholder="请选择" style="width: 100px;">
							<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
								:value="item.id"></el-option>
						</el-select>
						<el-tooltip class="item" effect="dark" content="在未查询到员工相关数据时，是否默认达标（默认关闭）"
							placement="top-start">
							<i class="el-icon-question" style="margin-left:10px;"></i>
						</el-tooltip>
					</el-form-item>

					<el-form-item label="需要审批：">
						<el-select v-model="choiceItem.needApproval" placeholder="请选择" style="width: 100px;">
							<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
								:value="item.id"></el-option>
						</el-select>
						<el-tooltip class="item" effect="dark" content="不启用：系统自动计算分值；启用：需由用户提交资料，并在【加分审批】栏目中进行审批"
							placement="top-start">
							<i class="el-icon-question" style="margin-left:10px;"></i>
						</el-tooltip>
					</el-form-item>

					<el-form-item label="规则状态：" v-if="detailType==1">
						<el-select v-model="choiceItem.ruleState" placeholder="请选择" style="width: 100px;">
							<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
								:value="item.id"></el-option>
						</el-select>
					</el-form-item>
				</el-form>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="drawerServiceScoreRule=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="insertServiceScoreRule()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateServiceScoreRule(choiceItem)" type="success" size="small"
						v-if="detailType==1">保存
					</el-button>
				</div>
			</div>
		</el-drawer>

		<el-drawer size="70%" :with-header="false" :visible.sync="drawerServiceScoreRuleGroup" direction="rtl">
			<div class="drawerForm">
				<h1>规则分组详情</h1>
				<el-divider></el-divider>
				<el-form ref="form">
					<el-form-item label="* 分组标题：">
						<el-input v-model="choiceItem.groupName" style="width: 50%;" type="textarea"
							placeholder="请输入分组标题" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</el-form-item>

					<el-form-item label="* 分组内容：">
						<el-input v-model="choiceItem.groupContent" style="width: 50%;" type="textarea"
							placeholder="请输入分组内容" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</el-form-item>

					<el-form-item label="* 分值比例：">
						<el-input-number v-model="choiceItem.groupIntegralRate" :precision="2" :step="0.1" :max="1"
							label="分值比例（小数）"></el-input-number>
						<span v-if="choiceItem.groupIntegralRate">
							{{choiceItem.groupIntegralRate*100}}%
						</span>
						<span v-else>
							暂未设置
						</span>
						<el-tooltip class="item" effect="dark" content="按照比例计算分值（计算规则：总分-100分*规则分组比例*规则分值比例）"
							placement="top-start">
							<i class="el-icon-question" style="margin-left:10px;"></i>
						</el-tooltip>
					</el-form-item>

					<el-form-item label="分组状态：" v-if="detailType==1">
						<el-select v-model="choiceItem.groupState" placeholder="请选择" style="width: 100px;">
							<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
								:value="item.id"></el-option>
						</el-select>
					</el-form-item>
				</el-form>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="drawerServiceScoreRuleGroup=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="insertServiceScoreRuleGroup()" type="success" size="small"
						v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateServiceScoreRuleGroup(choiceItem)" type="success" size="small"
						v-if="detailType==1">保存
					</el-button>
				</div>
			</div>
		</el-drawer>

		<el-drawer size="70%" :with-header="false" :visible.sync="drawerAgentStore" direction="rtl">
			<div class="drawerForm">
				<h1>经纪人服务分详情</h1>
				<el-divider></el-divider>

				<h2>服务分项目明细{{scoreResult||''}}</h2>
				<h3>基本项目分</h3>
				<div v-if="scoreDetail" style="display: flex;line-height: 35px;">
					<div v-for="(item,index) in scoreDetail.serviceScoreRuleList" :key="index"
						style="display: flex;flex-direction:column;width: 280px;"
						v-if="item.groupId<5||(item.groupId>=5&&item.groupIntegral)">
						<div style="color: #ff4d4b;font-weight: bold;">{{formatGroupScore(item)}}</div>
						<div style="display: flex;align-items: center;font-weight: bold;">
							<span style="width: 70%;">子项目</span>
							<span style="width:30%;">得分</span>
						</div>
						<div style="display: flex;align-items: center;" v-for="(item1,index1) in item.ruleDetailList"
							:key="index1">
							<span style="width: 70%;">{{index1+1}}、{{item1.ruleTitle}}
							</span>
							<span style="width: 30%;">{{Number(item1.ruleIntegralAwarded).toFixed(2)}}</view>
							</span>
						</div>
					</div>
				</div>
				<div v-else style="display: flex;line-height: 35px;">
					<span>
						暂无明细记录
					</span>
				</div>

				<h3>加分审批项（已通过的）</h3>
				<el-form ref="form">
					<el-table :data="ruleApprovalList" style="width: 100%">
						<el-table-column fixed="" prop="id" label="编号" width="60">
							<template slot-scope="scope">
								<div>{{scope.row.id}}</div>
							</template>
						</el-table-column>

						<el-table-column prop="ruleTitle" label="项目名称">
							<template slot-scope="scope">
								<div>{{scope.row.ruleTitle}}</div>
							</template>
						</el-table-column>
						<el-table-column prop="ruleIntegralPresets" label="加分值" sortable>
							<template slot-scope="scope">
								<div>{{scope.row.ruleIntegralPresets}}</div>
							</template>
						</el-table-column>
						<el-table-column prop="employeeName" label="提交人">
							<template slot-scope="scope">
								<div>{{scope.row.employeeName}}</div>
							</template>
						</el-table-column>
						<el-table-column prop="approvalContent" label="提交内容">
							<template slot-scope="scope">
								<div>{{scope.row.approvalContent}}</div>
							</template>
						</el-table-column>
						<el-table-column prop="createTime" label="提交时间" sortable>
							<template slot-scope="scope">
								<div>
									{{scope.row.createTime}}
								</div>
							</template>
						</el-table-column>
						<el-table-column prop="approvalorName" label="审批人">
							<template slot-scope="scope">
								<div>{{scope.row.approvalorName||'系统管理员'}}</div>
							</template>
						</el-table-column>
						<el-table-column prop="updateTime" label="审批时间" sortable>
							<template slot-scope="scope">
								<div>
									{{scope.row.updateTime}}
								</div>
							</template>
						</el-table-column>
					</el-table>
				</el-form>

				<h2>服务分更新日志</h2>
				<el-form ref="form">
					<el-table :data="agentStoreLogList" style="width: 100%">
						<el-table-column prop="id" label="编号" width="60">
							<template slot-scope="scope">
								<div>{{scope.row.id}}</div>
							</template>
						</el-table-column>

						<el-table-column prop="logTitle" label="日志标题">
							<template slot-scope="scope">
								<div>{{scope.row.logTitle}}</div>
							</template>
						</el-table-column>
						<el-table-column prop="logContent" label="日志内容">
							<template slot-scope="scope">
								<div>{{scope.row.logContent}}</div>
							</template>
						</el-table-column>
						<el-table-column prop="employeeName" label="操作人">
							<template slot-scope="scope">
								<div>{{scope.row.employeeName||'系统'}}</div>
							</template>
						</el-table-column>
						<el-table-column prop="createTime" label="操作时间" sortable>
							<template slot-scope="scope">
								<div>
									{{scope.row.createTime}}
								</div>
							</template>
						</el-table-column>
					</el-table>
				</el-form>

				<h2>门店星级日志</h2>
				<el-form ref="form">
					<el-table :data="storeLevelRecordList" style="width: 100%">
						<el-table-column prop="id" label="编号" width="60">
						</el-table-column>
						<el-table-column prop="integral" label="积分" width="160" sortable>
						</el-table-column>
						<el-table-column prop="amount" label="营业额" width="160" sortable>
						</el-table-column>
						<el-table-column prop="putNum" label="上架人数" width="160" sortable>
						</el-table-column>
						<el-table-column prop="storeLevel" label="星级" width="160" sortable>
						</el-table-column>
						<el-table-column prop="createTime" label="记录时间" sortable>
						</el-table-column>
					</el-table>
				</el-form>

				<h2>服务分奖惩日志</h2>
				<el-form ref="form">
					<el-table :data="agentStoreRuleLogList" style="width: 100%">
						<el-table-column prop="id" label="编号" width="60">
							<template slot-scope="scope">
								<div>{{scope.row.id}}</div>
							</template>
						</el-table-column>

						<el-table-column prop="ruleId" label="奖惩规则" width="120">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">
									<div v-if="scope.row.ruleId==item.id" v-for="(item,index) in agentStoreRuleList"
										:key="index">
										{{item.ruleTitle}}
									</div>
								</div>

								<el-select v-if="scope.row.isEdit" v-model="scope.row.ruleId" placeholder="请选择"
									style="width: 100px;">
									<el-option v-for="(item,index) in agentStoreRuleList" :key="index"
										:label="item.id+'：'+item.ruleTitle" :value="item.id"></el-option>
								</el-select>
							</template>
						</el-table-column>

						<el-table-column prop="logTitle" label="奖惩标题">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">{{scope.row.logTitle}}</div>
								<el-input v-model="scope.row.logTitle" style="width: 100%;" type="textarea"
									v-if="scope.row.isEdit" placeholder="请输入奖惩标题"
									:autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</template>
						</el-table-column>
						<el-table-column prop="logContent" label="奖惩内容">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">{{scope.row.logContent}}</div>
								<el-input v-model="scope.row.logContent" style="width: 100%;" type="textarea"
									v-if="scope.row.isEdit" placeholder="请输入奖惩内容"
									:autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</template>
						</el-table-column>
						<el-table-column prop="logRemark" label="奖惩备注">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">{{scope.row.logRemark||'暂无'}}</div>
								<el-input v-model="scope.row.logRemark" style="width: 100%;" type="textarea"
									v-if="scope.row.isEdit" placeholder="请输入奖惩备注"
									:autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</template>
						</el-table-column>
						<el-table-column prop="billNo" label="订单编号">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">{{scope.row.billNo||'暂无'}}</div>
								<el-input v-model="scope.row.billNo" style="width: 100%;" type="text"
									v-if="scope.row.isEdit" placeholder="请输入奖惩关联订单编号">
								</el-input>
							</template>
						</el-table-column>
						<el-table-column prop="ruleType" label="日志状态" width="120" :render-header="renderHeader">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">
									<el-tag v-if="item.id == scope.row.logState" v-for="(item,index) in stateList"
										:key="index" :type="item.type">{{formatType(3,scope.row)}}</el-tag>
								</div>

								<el-select v-model="scope.row.logState" placeholder="请选择" style="width: 100px;"
									v-if="scope.row.isEdit">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.id"></el-option>
								</el-select>
							</template>
						</el-table-column>

						<el-table-column prop="ruleAmount" label="奖惩金额">
							<template slot-scope="scope">
								<div>
									{{scope.row.ruleType==0?'-':'+'}}￥{{scope.row.ruleAmount}}
								</div>
							</template>
						</el-table-column>
						<el-table-column prop="ruleIntegral" label="奖惩积分">
							<template slot-scope="scope">
								<div>
									{{scope.row.ruleType==0?'-':'+'}}{{scope.row.ruleIntegral}}分
								</div>
							</template>
						</el-table-column>

						<el-table-column prop="createTime" label="创建时间" sortable>
							<template slot-scope="scope">
								<div>
									{{scope.row.createTime}}
								</div>
							</template>
						</el-table-column>

						<el-table-column fixed="right" label="操作" width="160">
							<template slot-scope="scope">
								<el-button @click="scope.row.isEdit = true" type="success" round size="small"
									:disabled="scope.row.isEdit" v-if="!scope.row.isEdit" icon="el-icon-edit">修改
								</el-button>
								<el-button @click="updateAgentStoreRuleLog(scope.row)" type="primary" round size="small"
									v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
								</el-button>
								<el-popconfirm title="确定删除该日志吗？删除后将撤销奖惩，请谨慎操作!"
									@confirm="deleteAgentStoreRuleLog(scope.$index)">
									<el-button type="text" size="small" slot="reference" style="margin-left: 10px"
										:disabled="!scope.row.isEdit" icon="el-icon-delete">删除
									</el-button>
								</el-popconfirm>
							</template>
						</el-table-column>
					</el-table>

					<el-form-item>
						<el-button type="primary" @click="drawerRuleLog=true">添加奖惩</el-button>
						<el-button @click="drawerAgentStore=false">取消</el-button>
					</el-form-item>
				</el-form>
			</div>
		</el-drawer>


		<div class="container">
			<div>
				<el-alert title="提示：积分分发指按经纪人的现有积分顺序分发。请在人员管理中查看排名。门店分发指按门店分发。依据分发跟踪排序分发！一次只能选择一种分发类型。" type="warning"
					effect="dark">
				</el-alert>
				<el-divider></el-divider>

				积分分发：
				<el-switch v-model="agentStoreSetting.status" active-text="启动" inactive-text="关闭" :active-value="2"
					:inactive-value="0" @change="saveAgentStoreSetting">
				</el-switch>
				<el-divider direction="vertical"></el-divider>
				<el-divider direction="vertical"></el-divider>
				<el-divider direction="vertical"></el-divider>
				<el-divider direction="vertical"></el-divider>
				门店分发：
				<el-switch v-model="agentStoreSetting.status" active-text="启动" inactive-text="关闭" :active-value="1"
					:inactive-value="0" @change="saveAgentStoreSetting">
				</el-switch>
				<el-divider direction="vertical"></el-divider>
				飞鸟分发：
				<el-switch v-model="agentStoreSetting1.isFly" active-text="启动" inactive-text="关闭" :active-value="1"
					:inactive-value="0" @change="feiniaoTask">
				</el-switch>
				<el-divider direction="vertical"></el-divider>
				<el-divider direction="vertical"></el-divider>
				<el-divider direction="vertical"></el-divider>
				<el-button type="primary" round icon="el-icon-refresh" @click="agentStoreShareInint"
					:loading="inintLoading">立即更新规则
				</el-button>
				<el-divider direction="vertical"></el-divider>
				<el-divider direction="vertical"></el-divider>
				<el-divider direction="vertical"></el-divider>
				<el-divider direction="vertical"></el-divider>
				当前进行至：
				<span class="sortClass">{{agentStoreSetting.indexNum}}</span>
				<el-divider direction="vertical"></el-divider>
				<el-divider direction="vertical"></el-divider>
				<el-divider direction="vertical"></el-divider>
				<el-divider direction="vertical"></el-divider>
				<el-button type="primary" round icon="el-icon-refresh-right"
					@click="etAgentStoreList(),getStore(),getAgentStoreShareList(),getLos()">刷新
				</el-button>
				<el-divider></el-divider>
			</div>
			<el-tabs v-model="activeName" type="border-card">
				<el-tab-pane label="人员管理" name="zero">
					<el-input v-model="searchEmployee.realName" placeholder="请输人员名称" style="width: 80%"></el-input>
					<el-button type="primary" icon="el-icon-search" @click="getEmployeeList">搜索</el-button>
					<el-button v-if="roleId != null && roleId === '1'" @click="employeeDrawer = true" type="primary"
						style="margin-left: 16px;" plain>
						人员操作
					</el-button>
					<el-button v-if="agentStoreSetting1.isFly === 1" @click="employeeDrawer1 = true" type="primary"
						plain>
						差评任务分配
					</el-button>
					<el-drawer title="提示：将差评任务按积分和门店分配给对应经纪人。优先匹配同门店订单且按积分高低分发" :visible.sync="employeeDrawer1"
						direction="rtl" size="60%">
						<test-select @init-flyBrid="submitFlyBrid"></test-select>
					</el-drawer>
					<el-drawer title="人员选择" :visible.sync="employeeDrawer" direction="rtl" size="60%">
						<employeeSelect :selectList="ids" @init-choose="teamEmployee"></employeeSelect>
					</el-drawer>
					<el-divider></el-divider>
					<el-alert title="提示：积分越高。排序越高，越优先排序！" type="warning" effect="dark">
					</el-alert>

					<el-table :data="employees" style="width: 100%">
						<el-table-column type="index" width="50" />
						<el-table-column prop="realName" label="人员名称" />
						<el-table-column prop="no" label="工号" />
						<el-table-column prop="sort" label="积分">
							<template slot-scope="scope">
								<el-input-number v-model="scope.row.sort" :min="0" :max="1000"
									label="描述文字"></el-input-number>
							</template>
						</el-table-column>
						<el-table-column fixed="right" label="操作" width="120">
							<template slot-scope="scope">
								<el-button v-if="roleId != null && roleId === '1'" type="primary" round
									@click="updateAgentEmployeeShare(scope.row)">更新
								</el-button>
								<el-button v-if="roleId != null && roleId === '1'"
									@click.native.prevent="scope.row.status=0,updateAgentEmployeeShare(scope.row)"
									type="text" size="small">
									移除
								</el-button>
							</template>
						</el-table-column>
					</el-table>

				</el-tab-pane>
				<el-tab-pane label="门店管理" name="first">
					<el-input v-model="search.search" placeholder="请输入门店名称/管理人员/项目经理等" style="width: 70%;"></el-input>
					<el-button type="primary" icon="el-icon-search" @click="etAgentStoreList"
						style="margin-left: 16rpx;">搜索</el-button>
					<el-button @click="drawer = true" type="primary" style="margin-left: 16px;" plain>
						新增门店
					</el-button>
					<el-button @click="agentStoreDownload()" type="warning" style="margin-left: 16px;" plain>
						导出
					</el-button>
					<el-button v-if="roleId != null && roleId === '1'" @click="showOrderNeedsAlogSetting = true"
						type="danger" style="margin-left: 16px;" plain>
						权重配置
					</el-button>
					<el-divider></el-divider>
					<el-alert title="警告：相同门店只允许一个,若有多个请立马删除否则派发异常" type="warning" effect="dark">
					</el-alert>
					<el-table :data="agentStoreList" style="width: 100%">
						<el-table-column fixed prop="id" label="编号" width="120" sortable>
						</el-table-column>
						<el-table-column prop="storeId" label="门店id" width="120" sortable>
						</el-table-column>
						<el-table-column prop="storeName" label="门店名称" width="200">
						</el-table-column>
						<el-table-column prop="openingTime" label="开业时间" width="140" sortable>
							<template slot-scope="scope">
								<span>{{formatDate(scope.row.openingTime)}}</span>
							</template>
						</el-table-column>
						<el-table-column prop="realName" label="管理人员" width="120">
						</el-table-column>
						<el-table-column prop="storeManagerName" label="项目经理" width="120">
						</el-table-column>
						<!-- <el-table-column prop="num" label="门店人数">
							<template slot-scope="scope">
								<el-input-number v-model="scope.row.num" :min="1" :max="1000"
									label="描述文字"></el-input-number>
							</template>
						</el-table-column> -->
						<el-table-column prop="integral" label="积分" width="200" sortable>
							<template slot-scope="scope">
								<el-input-number v-model="scope.row.integral" :min="1" :max="1000"
									label="门店服务分"></el-input-number>
							</template>
						</el-table-column>
						<el-table-column prop="protectWeek" label="保护周" width="200" sortable>
							<template slot-scope="scope">
								<el-input-number v-model="scope.row.protectWeek" :min="0" :max="16"
									label="积分将在该周期内保持不变"></el-input-number>
							</template>
						</el-table-column>
						<el-table-column prop="storeLevel" label="星级" width="200" sortable>
							<template slot-scope="scope">
								<el-input-number v-model="scope.row.storeLevel" :min="1" :max="5"
									label="每月15号自动更新，也可手动变更"></el-input-number>
							</template>
						</el-table-column>
						<el-table-column prop="pause" label="线索停派" width="200">
							<template slot-scope="scope">
								<el-switch v-model="scope.row.pause" active-text="启动" inactive-text="关闭"
									:active-value="1" :inactive-value="0">
								</el-switch>
							</template>
						</el-table-column>
						<el-table-column prop="amount" label="营业额" width="160" sortable>
						</el-table-column>
						<el-table-column prop="putNum" label="上架人数" width="120" sortable>
						</el-table-column>

						<el-table-column fixed="right" label="操作" width="160">
							<template slot-scope="scope">
								<el-button type="primary" round @click="onSubmit(scope.row)">更新</el-button>
								<el-button @click.native.prevent="deleteRow(scope.$index, agentStoreList)" type="text"
									size="small">
									移除
								</el-button>
								<el-button @click="openDetail(scope.row.id)" type="text" size="small">
									详情
								</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>
				<el-tab-pane label="分发跟踪" name="second">
					<el-row :gutter="20">
						<el-col :span="5" v-for="(item,index) in agentStoreShareList" :key="index">
							<el-card shadow="hover" :style="index<indexNum?'background: #c3e0ff':'background: #ddd'">
								<span
									class="sortClass">{{item.sort+1}}</span>{{item.storeName==null?'小羽佳·佳管家（人才中心）':item.storeName}}
							</el-card>
						</el-col>
					</el-row>
				</el-tab-pane>
				<el-tab-pane label="跟踪日志" name="third">
					<div style="margin-top: 15px;">

						<el-row :gutter="20">
							<el-col :span="3">
								<el-select v-model="valueSelect" @change="selectLog" filterable placeholder="请选择日志类型">
									<el-option v-for="item in options" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-col>
							<el-col :span="6">
								<el-date-picker v-model="dateTime" value-format="yyyy-MM-dd" type="datetimerange"
									range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
								</el-date-picker>
							</el-col>
							<el-col :span="1">
								<el-button type="primary" @click="searchLog">查询</el-button>
							</el-col>
							<el-col :span="1">
								<el-button type="success" @click="exportLog" :loading="loadingExcel">导出</el-button>
							</el-col>
						</el-row>
					</div>

					<el-table :data="logList" :header-cell-style="{background:'#ddd'}" border class="table"
						ref="multipleTable" v-loading="loading2">
						<el-table-column fixed="left" prop="orderNeedsId" label="需求编号" width="80"></el-table-column>
						<el-table-column fixed="left" prop="operator" label="操作人" width="80"></el-table-column>
						<el-table-column prop="title" label="类型" width="80"></el-table-column>
						<!--                        <el-table-column-->
						<!--                                prop="type"-->
						<!--                                label="操作途径"-->
						<!--                                width="80"-->
						<!--                        >-->
						<!--                            <template slot-scope="scope">-->
						<!--                                <el-tag v-if="scope.row.type=='1'" type="danger">移动端</el-tag>-->
						<!--                                <el-tag v-if="scope.row.type=='0'" type="success">web端</el-tag>-->
						<!--                            </template>-->
						<!--                        </el-table-column>-->
						<el-table-column prop="createTime" label="操作时间" width="180"></el-table-column>
						<el-table-column prop="message" label="操作内容"></el-table-column>
					</el-table>


					<div class="pagination">
						<Page :total="pageInfo2.total" @on-change="onChange2" :show-total="true" :show-sizer="true"
							:page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange2"
							:page-size="pageInfo2.size" />
					</div>
				</el-tab-pane>

				<el-tab-pane label="服务分规则" name="fourth">
					<el-input v-model="quer.search" placeholder="请输入规则名称/内容或分组"
						style="width: 20%;margin-right: 20px;"></el-input>
					<el-select v-model="searchGroup" placeholder="请选择规则分组" style="margin-right: 20px">
						<el-option label="" value=""></el-option>
						<el-option v-for="(item,index) in serviceScoreRuleGroupList" :key="index"
							:label="item.groupName" :value="item.id"></el-option>
					</el-select>
					<el-select v-model="searchNeedApproval" placeholder="请选择是否需要审批" style="margin-right: 20px">
						<el-option label="" value=""></el-option>
						<el-option label="不启用" value="0"></el-option>
						<el-option label="启用" value="1"></el-option>
					</el-select>
					<el-select v-model="searchState" placeholder="请选择规则状态" style="margin-right: 20px">
						<el-option label="" value=""></el-option>
						<el-option label="不启用" value="0"></el-option>
						<el-option label="启用" value="1"></el-option>
					</el-select>
					<el-button type="primary" icon="el-icon-search" @click="listServiceScoreRule">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="reset">重置</el-button>
					<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(0,0)">添加</el-button>

					<el-table :data="serviceScoreRuleList" style="width: 100%">
						<el-table-column prop="id" label="规则编号" width="80">
							<template slot-scope="scope">
								<span>{{scope.row.id}}</span>
							</template>
						</el-table-column>
						<el-table-column prop="ruleTitle" label="规则标题">
							<template slot-scope="scope">
								<span>{{scope.row.ruleTitle}}</span>
							</template>
						</el-table-column>

						<el-table-column prop="ruleContent" label="规则内容">
							<template slot-scope="scope">
								<span>{{formatLongStr(scope.row.ruleContent)}}</span>
							</template>
						</el-table-column>

						<el-table-column prop="ruleGroupId" label="规则分组">
							<template slot-scope="scope">
								<el-tag v-if="item.id == scope.row.ruleGroupId"
									v-for="(item,index) in serviceScoreRuleGroupList" :key="index"
									:type="typeStyleList[index%4]">{{item.groupName}}</el-tag>
							</template>
						</el-table-column>

						<el-table-column prop="ruleType" label="规则类型">
							<template slot-scope="scope">
								<el-tag v-if="item.id == scope.row.ruleType" v-for="(item,index) in ruleTypeList1"
									:key="index" :type="item.type">{{item.text}}</el-tag>
							</template>
						</el-table-column>

						<el-table-column prop="ruleGroupId" label="分值预设" :render-header="renderHeader">
							<template slot-scope="scope">
								<span>
									{{scope.row.ruleIntegralPresets||'暂未设置'}}
								</span>
							</template>
						</el-table-column>

						<el-table-column prop="ruleIntegralRate" label="分值比例" :render-header="renderHeader">
							<template slot-scope="scope">
								<span v-if="scope.row.ruleIntegralRate">
									{{scope.row.ruleIntegralRate*100}}%
								</span>
								<span v-else>
									暂未设置
								</span>
							</template>
						</el-table-column>

						<el-table-column prop="checkSql" label="校验sql" :render-header="renderHeader">
							<template slot-scope="scope">
								<span>{{ formatLongStr(scope.row.checkSql) }}</span>
							</template>
						</el-table-column>

						<el-table-column prop="reachFlag" label="达标数量" :render-header="renderHeader">
							<template slot-scope="scope">
								<span>
									{{scope.row.reachFlag||'暂未设置'}}
								</span>
							</template>
						</el-table-column>

						<el-table-column prop="defaultReach" label="默认达标" :render-header="renderHeader">
							<template slot-scope="scope">
								<el-tag v-if="item.id == scope.row.defaultReach" v-for="(item,index) in stateList"
									:key="index" :type="item.type">{{formatType(4,scope.row)}}</el-tag>
							</template>
						</el-table-column>

						<el-table-column prop="needApproval" label="需要审批" :render-header="renderHeader">
							<template slot-scope="scope">
								<el-tag v-if="item.id == scope.row.needApproval" v-for="(item,index) in stateList"
									:key="index" :type="item.type">{{item.text}}</el-tag>
							</template>
						</el-table-column>

						<el-table-column prop="ruleType" label="规则状态">
							<template slot-scope="scope">
								<el-tag v-if="item.id == scope.row.ruleState" v-for="(item,index) in stateList"
									:key="index" :type="item.type">{{formatType(2,scope.row)}}</el-tag>
							</template>
						</el-table-column>

						<el-table-column width="130" prop="creator" label="创建人">
							<template slot-scope="scope">
								<span
									v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
								<span v-else>{{scope.row.creator||''}}</span>
							</template>
						</el-table-column>

						<el-table-column prop="createTime" label="创建时间" sortable>
							<template slot-scope="scope">
								<span>
									{{formatDate(scope.row.createTime)}}
								</span>
							</template>
						</el-table-column>

						<el-table-column fixed="right" label="操作" width="160">
							<template slot-scope="scope">
								<el-button @click="openModal(1,scope.$index)" type="success" round size="small"
									icon="el-icon-edit">修改
								</el-button>
								<el-popconfirm title="确定删除该规则吗？删除后无法恢复，请谨慎操作!"
									@confirm="deleteServiceScoreRule(scope.row)">
									<el-button type="text" size="small" slot="reference" style="margin-left: 10px"
										icon="el-icon-delete">删除
									</el-button>
								</el-popconfirm>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>

				<el-tab-pane label="规则分组" name="fifth">
					<el-input v-model="quer.search" placeholder="请输入分组名称/内容"
						style="width: 20%;margin-right: 20px;"></el-input>
					<el-select v-model="searchState" placeholder="请选择分组状态" style="margin-right: 20px">
						<el-option label="" value=""></el-option>
						<el-option label="不启用" value="0"></el-option>
						<el-option label="启用" value="1"></el-option>
					</el-select>
					<el-button type="primary" icon="el-icon-search" @click="listServiceScoreRuleGroup">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="reset">重置</el-button>
					<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(2,0)">添加</el-button>

					<el-table :data="serviceScoreRuleGroupList" style="width: 100%">
						<el-table-column fixed prop="id" label="分组编号" width="80">
							<template slot-scope="scope">
								<span>{{scope.row.id}}</span>
							</template>
						</el-table-column>

						<el-table-column fixed prop="groupName" label="分组标题">
							<template slot-scope="scope">
								<span>{{scope.row.groupName}}</span>
							</template>
						</el-table-column>

						<el-table-column fixed prop="groupContent" label="分组内容">
							<template slot-scope="scope">
								<span>{{scope.row.groupContent}}</span>
							</template>
						</el-table-column>

						<el-table-column fixed prop="groupIntegralRate" label="分值比例" :render-header="renderHeader">
							<template slot-scope="scope">
								<span v-if="scope.row.groupIntegralRate">
									{{scope.row.groupIntegralRate*100}}%
								</span>
								<span v-else>
									暂未设置
								</span>
							</template>
						</el-table-column>

						<el-table-column fixed prop="ruleType" label="分组状态">
							<template slot-scope="scope">
								<el-tag v-if="item.id == scope.row.groupState" v-for="(item,index) in stateList"
									:key="index" :type="item.type">{{item.text}}</el-tag>
							</template>
						</el-table-column>

						<el-table-column width="130" prop="creator" label="创建人">
							<template slot-scope="scope">
								<span
									v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
								<span v-else>{{scope.row.creator||''}}</span>
							</template>
						</el-table-column>

						<el-table-column fixed prop="createTime" label="创建时间" sortable>
							<template slot-scope="scope">
								<span>
									{{formatDate(scope.row.createTime)}}
								</span>
							</template>
						</el-table-column>

						<el-table-column fixed="right" label="操作" width="160">
							<template slot-scope="scope">
								<el-button @click="openModal(3,scope.$index)" type="success" round size="small"
									icon="el-icon-edit">修改
								</el-button>
								<!-- 	<el-popconfirm title="确定删除该规则分组吗？删除后无法恢复，请谨慎操作!"
									@confirm="deleteServiceScoreRuleGroup(scope.$index)">
									<el-button type="text" size="small" slot="reference" style="margin-left: 10px"
										icon="el-icon-delete">删除
									</el-button>
								</el-popconfirm> -->
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>

				<el-tab-pane label="奖惩规则" name="sixth">
					<el-input v-model="quer.search" placeholder="请输入规则名称/内容或备注"
						style="width: 20%;margin-right: 20px;"></el-input>
					<el-button type="primary" icon="el-icon-search" @click="listAgentStoreRule">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="reset">重置</el-button>
					<el-button type="success" icon="el-icon-circle-plus-outline"
						@click="drawerRule= true">添加</el-button>

					<el-table :data="agentStoreRuleList" style="width: 100%">
						<el-table-column fixed prop="id" label="规则编号" width="80">
							<template slot-scope="scope">
								<div>{{scope.row.id}}</div>
							</template>
						</el-table-column>
						<el-table-column fixed prop="ruleTitle" label="规则标题">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">{{scope.row.ruleTitle}}</div>
								<el-input v-model="scope.row.ruleTitle" style="width: 100%;" type="textarea"
									v-if="scope.row.isEdit" placeholder="请输入规则标题"
									:autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</template>
						</el-table-column>
						<el-table-column fixed prop="ruleContent" label="规则内容">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">{{scope.row.ruleContent}}</div>
								<el-input v-model="scope.row.ruleContent" style="width: 100%;" type="textarea"
									v-if="scope.row.isEdit" placeholder="请输入规则内容"
									:autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</template>
						</el-table-column>
						<el-table-column fixed prop="ruleRemark" label="规则备注">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">{{scope.row.ruleRemark}}</div>
								<el-input v-model="scope.row.ruleRemark" style="width: 100%;" type="textarea"
									v-if="scope.row.isEdit" placeholder="请输入规则备注"
									:autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</template>
						</el-table-column>
						<el-table-column fixed prop="ruleType" label="规则类型">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">
									<el-tag v-if="item.id == scope.row.ruleType" v-for="(item,index) in ruleTypeList"
										:key="index" :type="item.type">{{formatType(0,scope.row)}}</el-tag>
								</div>

								<el-select v-model="scope.row.ruleType" placeholder="请选择" style="width: 100px;"
									v-if="scope.row.isEdit">
									<el-option v-for="(item,index) in ruleTypeList" :key="index" :label="item.text"
										:value="item.id"></el-option>
								</el-select>
							</template>
						</el-table-column>

						<el-table-column fixed prop="ruleType" label="规则分组">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">
									<el-tag v-if="item.id == scope.row.ruleGroup" v-for="(item,index) in ruleGroupList"
										:key="index" :type="item.type">{{formatType(1,scope.row)}}</el-tag>
								</div>

								<el-select v-model="scope.row.ruleGroup" placeholder="请选择" style="width: 100px;"
									v-if="scope.row.isEdit">
									<el-option v-for="(item,index) in ruleGroupList" :key="index" :label="item.text"
										:value="item.id"></el-option>
								</el-select>
							</template>
						</el-table-column>
						<el-table-column fixed prop="ruleAmount" label="奖惩金额">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">
									{{scope.row.ruleType==0?'-':'+'}}￥{{scope.row.ruleAmount}}
								</div>
								<div v-if="scope.row.isEdit">￥<el-input v-model="scope.row.ruleAmount"
										style="width: 80%;" placeholder="单位：元"
										onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
									</el-input>
								</div>
							</template>
						</el-table-column>
						<el-table-column fixed prop="ruleIntegral" label="奖惩积分">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">
									{{scope.row.ruleType==0?'-':'+'}}{{scope.row.ruleIntegral}}分
								</div>
								<div v-if="scope.row.isEdit"><el-input v-model="scope.row.ruleIntegral"
										style="width: 80%;" placeholder="单位：分"
										onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
									</el-input> 分
								</div>
							</template>
						</el-table-column>
						<el-table-column fixed prop="ruleIntegral" label="关联课程" :render-header="renderHeader">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">
									<div v-if="scope.row.courseId==item.id" v-for="(item,index) in courseList"
										:key="index">
										{{item.courseTitle}}
									</div>
									<div v-if="!scope.row.courseId">
										暂未设置
									</div>
								</div>

								<el-select v-model="scope.row.courseId" placeholder="请选择" style="width: 100px;"
									v-if="scope.row.isEdit">
									<el-option v-for="(item,index) in courseList" :key="index"
										:label="item.id+'：'+item.courseTitle" :value="item.id"></el-option>
								</el-select>
							</template>
						</el-table-column>
						<el-table-column fixed prop="ruleIntegral" label="关联考试" :render-header="renderHeader">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">
									<div v-if="scope.row.examId==item.id" v-for="(item,index) in examList" :key="index">
										{{item.examTitle}}
									</div>
									<div v-if="!scope.row.examId">
										暂未设置
									</div>
								</div>

								<el-select v-model="scope.row.examId" placeholder="请选择" style="width: 100px;"
									v-if="scope.row.isEdit">
									<el-option v-for="(item,index) in examList" :key="index"
										:label="item.id+'：'+item.examTitle" :value="item.id"></el-option>
								</el-select>
							</template>
						</el-table-column>

						<el-table-column fixed prop="ruleType" label="规则状态">
							<template slot-scope="scope">
								<div v-if="!scope.row.isEdit">
									<el-tag v-if="item.id == scope.row.ruleState" v-for="(item,index) in stateList"
										:key="index" :type="item.type">{{formatType(2,scope.row)}}</el-tag>
								</div>

								<el-select v-model="scope.row.ruleState" placeholder="请选择" style="width: 100px;"
									v-if="scope.row.isEdit">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.id"></el-option>
								</el-select>
							</template>
						</el-table-column>

						<el-table-column fixed prop="createTime" label="创建时间" sortable>
							<template slot-scope="scope">
								<div>
									{{formatDate(scope.row.createTime)}}
								</div>
							</template>
						</el-table-column>

						<el-table-column fixed="right" label="操作" width="160">
							<template slot-scope="scope">
								<el-button @click="scope.row.isEdit = true" type="success" round size="small"
									:disabled="scope.row.isEdit" v-if="!scope.row.isEdit" icon="el-icon-edit">修改
								</el-button>
								<el-button @click="updateAgentStoreRule(scope.row)" type="primary" round size="small"
									v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
								</el-button>
								<el-popconfirm title="确定删除该规则吗？删除后无法恢复，请谨慎操作!"
									@confirm="deleteAgentStoreRule(scope.$index)">
									<el-button type="text" size="small" slot="reference" style="margin-left: 10px"
										:disabled="!scope.row.isEdit" icon="el-icon-delete">删除
									</el-button>
								</el-popconfirm>
							</template>
						</el-table-column>
					</el-table>
				</el-tab-pane>

				<el-tab-pane label="加分审批" name="seven">
					<el-input v-model="quer.search" placeholder="请输入审批项目、提交人姓名等"
						style="width: 20%;margin-right: 20px;"></el-input>
					<el-select v-model="searchApprovalState" style="margin-right: 20px;">
						<el-option label="" value=""></el-option>
						<el-option v-for="(item,index) in approvalStateList" :key="index" :label="item.text"
							:value="item.id"></el-option>
					</el-select>
					<el-button type="primary" icon="el-icon-search" @click="pageServiceScoreRuleApproval">搜索</el-button>
					<el-button icon="el-icon-refresh" @click="reset">重置</el-button>
					<el-divider></el-divider>
					<el-table :data="approvalList" v-loading="loading" border
						:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
						@expand-change="expandSelect">
						<!-- 展开列-审批内容详情 -->
						<el-table-column type="expand">
							<template slot-scope="scope">
								<el-col :span="48" style="margin: 0 0 20px 0">
									<h2>审批内容详情</h2>
								</el-col>
								<el-col :span="48" style="margin: 0 0 20px 20px;float: left;display: flex;">
									审批图片：
									<div style="display: inline-block;margin-right: 20px;">
										<img :src="scope.row.approvalImg||blankImg"
											@click="openImg(scope.row.approvalImg)"
											style="width:140px;height: auto;display: inline-block;" />
									</div>
									<div v-for="(item,index) in [1,2,3,4,5,6,7,8]" :key="index"
										v-if="scope.row['approvalImg'+item]"
										style="display: inline-block;margin-right: 20px;">
										<img :src="scope.row['approvalImg'+item]||blankImg"
											@click="openImg(scope.row['approvalImg'+item])"
											style="width:140px;height: auto;display: inline-block;" />
									</div>
									<div style="display: inline-block;">
										审批视频：
										<video :src="scope.row.approvalVideo||blankImg" v-if="scope.row.approvalVideo"
											@click="openVideo(scope.row.approvalVideo)"
											style="width:140px;height: auto;display: inline-block;" />
										<span v-else>暂未上传</span>
									</div>
								</el-col>
							</template>
						</el-table-column>

						<el-table-column prop="id" label="审批编号" width="80">
							<template slot-scope="scope">
								<span>{{scope.row.id}}</span>
							</template>
						</el-table-column>
						<el-table-column prop="ruleTitle" label="规则标题">
							<template slot-scope="scope">
								<span>{{scope.row.ruleTitle}}</span>
							</template>
						</el-table-column>
						<el-table-column prop="ruleContent" label="规则内容">
							<template slot-scope="scope">
								<span>{{scope.row.ruleContent}}</span>
							</template>
						</el-table-column>

						<el-table-column prop="ruleIntegralPresets" label="增加分值" sortable>
							<template slot-scope="scope">
								<span>
									{{scope.row.approvalIntegral||scope.row.ruleIntegralPresets}}分
								</span>
							</template>
						</el-table-column>

						<el-table-column prop="approvalContent" label="提交内容">
							<template slot-scope="scope">
								<span>{{scope.row.approvalContent||'暂无'}}</span>
							</template>
						</el-table-column>

						<el-table-column prop="employeeName" label="提交人">
							<template slot-scope="scope">
								<div>
									{{scope.row.employeeName}}
								</div>
							</template>
						</el-table-column>

						<el-table-column prop="storeName" label="门店">
							<template slot-scope="scope">
								<div>
									{{scope.row.storeName|| '暂无'}}
								</div>
							</template>
						</el-table-column>

						<el-table-column prop="createTime" label="提交时间" sortable>
							<template slot-scope="scope">
								<div>
									{{scope.row.createTime||'-'}}
								</div>
							</template>
						</el-table-column>

						<el-table-column prop="approvalRemark" label="审批结果">
							<template slot-scope="scope">
								<span>{{scope.row.approvalRemark||'暂无'}}</span>
							</template>
						</el-table-column>
						<el-table-column prop="approvalorName" label="审批人">
							<template slot-scope="scope">
								<div>
									{{scope.row.approvalorName||'-'}}
								</div>
							</template>
						</el-table-column>

						<el-table-column prop="updateTime" label="审批时间" sortable>
							<template slot-scope="scope">
								<div>
									{{scope.row.updateTime||'-'}}
								</div>
							</template>
						</el-table-column>

						<el-table-column prop="approvalState" label="审批状态">
							<template slot-scope="scope">
								<span>
									<el-tag v-if="item.id == scope.row.approvalState"
										v-for="(item,index) in approvalStateList" :key="index"
										:type="item.type">{{item.text}}</el-tag>
								</span>
							</template>
						</el-table-column>

						<el-table-column fixed="right" label="操作" width="160">
							<template slot-scope="scope">
								<el-button @click="approvalModal =true;choiceItem=scope.row" type="success" round
									size="small" :disabled="scope.row.approvalState!=0" icon="el-icon-edit">审批
								</el-button>
							</template>
						</el-table-column>
					</el-table>

					<div class="pagination">
						<Page :total="pageInfo2.total" @on-change="onChange2" :show-total="true" :show-sizer="true"
							:page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange2"
							:page-size="pageInfo2.size" />
					</div>
				</el-tab-pane>
			</el-tabs>
		</div>


		<el-dialog title="权重配置：" :visible.sync="showOrderNeedsAlogSetting" width="50%">
			<el-form ref="form" :model="orderneedsDexForm" label-width="150px">
				<el-form-item label="距离权重">
					<el-input-number v-model="orderneedsDexForm.distanceDex" :precision="2" :step="0.1"
						:max="10"></el-input-number>
				</el-form-item>
				<el-form-item label="服务分权重">
					<el-input-number v-model="orderneedsDexForm.serviceScoreDex" :precision="2" :step="0.1"
						:max="10"></el-input-number>
				</el-form-item>
				<el-form-item label="等单时长权重">
					<el-input-number v-model="orderneedsDexForm.waitOrderDex" :precision="2" :step="0.1"
						:max="10"></el-input-number>
				</el-form-item>
				<el-button type="success" style="width: 50%" @click="updateorderneedsDexForm()">修改</el-button>
			</el-form>
		</el-dialog>


		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imgUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--视频预览-->
		<el-dialog :visible.sync="videoModal" width="40%" title="视频预览" :mask-closable="false">
			<video style="width: 100%;height: auto;margin: 0 auto;" :controls="true" :enable-progress-gesture="true"
				:initial-time="0" :show-center-play-btn="true" autoplay :src="videoUrl" custom-cache="false">
			</video>
		</el-dialog>

		<!--视频预览-->
		<el-dialog :visible.sync="approvalModal" width="40%" title="审批结果判定" :mask-closable="false">
			<el-select v-model="choiceItem.approvalState" style="margin-right: 20px;">
				<el-option v-for="(item,index) in approvalStateList" :key="index" :label="item.text" :value="item.id"
					:disabled='item.id==0'></el-option>
			</el-select>
			<el-input v-model="choiceItem.approvalRemark" style="width: 50%;" type="textarea"
				placeholder="请输入审批备注（不通过状态下必填）" :autosize="{ minRows: 4, maxRows: 10}">
			</el-input>
			<div style="margin: 20px 0;">
				<span>* 审批结束后不可再修改结果，加分立即生效</span>
			</div>
			<div>
				<el-button @click="updateServiceScoreRuleApproval(choiceItem)" type="success" round size="small"
					icon="el-icon-edit">确定结果
				</el-button>
			</div>
		</el-dialog>
	</div>

</template>

<script>
	import Vue from 'vue';
	import employeeSelect from "../../team/common/employeeSelect";
	import testSelect from "@/components/page/team/common/testSelect";

	export default {
		name: "agentStore",
		components: {
			employeeSelect,
			testSelect
		},
		data() {
			return {
				imgModal: false,
				videoModal: false,
				approvalModal: false,
				choiceItem: {},
				imgUrl: '',
				videoUrl: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				orderneedsDexForm: {
					distanceDex: '',
					serviceScoreDex: '',
					waitOrderDex: '',
					employeeId: '',
				},
				showOrderNeedsAlogSetting: false,
				roleId: localStorage.getItem('roleId'),
				dateTime: null,
				loadingExcel: false,
				searchEmployee: {
					realName: null,
				},
				options: [{
					value: '自动分发',
					label: '自动分发'
				}, {
					value: '积分更新',
					label: '积分更新'
				}, {
					value: '员工积分变更',
					label: '员工积分变更'
				}, {
					value: '权重配置',
					label: '权重配置'
				}],
				chooseMode: {
					missionList: [],
					employList: []
				},
				employeeDrawer: false,
				employeeDrawer1: false,
				ids: [],
				employees: [],
				loading: true,
				loading2: true,
				pageSizeOpts: [10, 20, 30],
				inintLoading: false,
				indexNum: 0,
				agentStoreSetting: {
					status: 0,
					indexNum: 0,
				},
				agentStoreSetting1: {
					status: 0,
					indexNum: 0,
					isFly: 0,
				},
				activeName: "first",
				searchState: '',
				searchApprovalState: '',
				searchNeedApproval: '',
				searchGroup: '',
				search: {
					status: 1,
					storeName: null,
					search: ''
				},
				agentStoreList: [],
				storeList: [],
				drawer: false,
				agentStore: {
					agentName: null,
					id: null,
					storeId: null,
					employeeId: null,
					status: null,
					creatDate: null,
					type: null,
					num: null,
					max: null,
					integral: null,
					remark: null,
					sequence: null,
				},
				baomuModel: {
					realName: null,
					No: null,
				},
				agentStoreShareList: [],
				logList: [],
				valueSelect: null,
				logModel: {
					title: '自动分发',
					current: 1,
					size: 10,
					startTime: null,
					endTime: null
				},
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				pageInfo2: {
					total: 10,
					size: 10,
					current: 1,
					pages: 1
				},

				detailType: 0,
				choiceItemIndex: 0,
				drawerRule: false,
				drawerRuleLog: false,
				drawerAgentStore: false,
				drawerServiceScoreRule: false,
				drawerServiceScoreRuleGroup: false,
				quer: {
					search: '',
					orderBy: '',
					current: 1,
					size: 10,
				},

				choiceItem: {},
				serviceScoreRule: {},
				serviceScoreRuleGroup: {},
				agentStoreRule: {},
				agentStoreRuleLog: {},
				serviceScoreRuleList: [],
				serviceScoreRuleGroupList: [],
				agentStoreRuleList: [],
				agentStoreRuleLogList: [],
				agentStoreLogList: [],
				storeLevelRecordList: [],
				ruleApprovalList: [],
				examList: [],
				courseList: [],
				approvalList: [],
				scoreDetail: null,
				scoreResult: '',
				ruleTypeList1: [{
					id: 0,
					text: "默认",
					type: "info"
				}, {
					id: 1,
					text: "加分项",
					type: "success"
				}, {
					id: 2,
					text: "补分项",
					type: "primary"
				}],
				ruleTypeList: [{
					id: 0,
					text: "惩罚",
					type: "danger"
				}, {
					id: 1,
					text: "奖励",
					type: "success"
				}],
				ruleGroupList: [{
					id: 0,
					text: "信息类",
					type: "error"
				}, {
					id: 1,
					text: "履约类",
					type: "success"
				}, {
					id: 2,
					text: "平台类",
					type: "success"
				}],
				stateList: [{
					id: 0,
					text: "不启用",
					type: "info"
				}, {
					id: 1,
					text: "启用",
					type: "success"
				}],
				approvalStateList: [{
					id: 0,
					text: "未审批",
					type: "warning"
				}, {
					id: 1,
					text: "不通过",
					type: "info"
				}, {
					id: 2,
					text: "通过",
					type: "success"
				}],
				typeStyleList: ['success', 'info', 'warning', 'primary']
			};
		},
		created() {
			this.etAgentStoreList()
			this.getStore()
			this.getAgentStoreShareList()
			this.getLos()
			this.getEmployeeList()
			this.getAgentStoreRuleRelated()
			this.getOrderNeedsAlgorithmSetting()
			this.pageServiceScoreRuleApproval()
		},
		methods: {
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.pageServiceScoreRuleApproval()
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.isEdit = false
					}
				} else {
					that.expands = [] // 默认不展开
					this.isEdit = false
				}
			},
			// 打开图片预览
			openImg(url) {
				this.imgUrl = url || this.blankImg
				this.imgModal = true
			},
			// 打开视频预览
			openVideo(url) {
				this.videoUrl = url || ''
				this.videoModal = true
			},
			updateorderneedsDexForm() {
				this.orderneedsDexForm.employeeId = localStorage.getItem("id");
				this.$postData("updateOrderNeedsAlgorithmSetting", this.orderneedsDexForm, {}).then(res => {
					if (res.status == 200) {
						this.$message.success("更新成功")
						this.showOrderNeedsAlogSetting = false;
					} else {
						this.$message.error("更新失败")
					}
				});
			},
			getOrderNeedsAlgorithmSetting() {
				this.$getData("getOrderNeedsAlgorithmSetting", {}).then(res => {
					if (res.status === 200) {
						this.orderneedsDexForm = res.data;
					}

				});
			},
			formatLongStr(str) {
				if (!str) {
					return "暂无"
				} else {
					let long = 40
					if (str.length > long) {
						str = str.substring(0, long) + "..."
					}
					return str
				}
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "关联课程") {
					tips = "学习完该课程后可撤销此惩罚"
				} else if (column.label == "关联考试") {
					tips = "通过该考试后可撤销此惩罚"
				} else if (column.label == "日志状态") {
					tips = "不启用：计算服务分时忽略该条奖惩"
				} else if (column.label == "分值预设") {
					tips = '优先级高于分值比例，设定后，分值比例栏目将失效'
				} else if (column.label == "分值比例") {
					tips = '按照比例计算分值（计算规则：总分-100分*规则分组比例*规则分值比例）'
				} else if (column.label == "校验sql") {
					tips = '由技术人员填写，对员工规则达成情况进行数据校验'
				} else if (column.label == "达标数量") {
					tips = '规则达标所需达到的值'
				} else if (column.label == "默认达标") {
					tips = '在未查询到员工相关数据时，是否默认达标（默认关闭）'
				} else if (column.label == "需要审批") {
					tips = '不启用：系统自动计算分值；启用：需由用户提交资料，并在【加分审批】栏目中进行审批'
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			getEmployeeList() {
				this.$postData("getEmployeeList", this.searchEmployee, {}).then(res => {
					if (res.status == 200) {
						this.employees = res.data
						this.ids = []
						if (this.employees.length > 0) {
							this.employees.forEach(v => {
								this.ids.push(v.employeeId)
							})
						}
						console.info(this.ids)
					}
				});
			},
			updateAgentEmployeeShareList() {
				this.$postData("updateAgentEmployeeShareList", this.ids, {}).then(res => {
					if (res.status == 200) {
						this.getEmployeeList()
						this.$message.success("更新成功")
					}
				});
			},
			updateAgentEmployeeShare(dom) {
				dom.operatorId = localStorage.getItem("id");
				this.$postData("updateAgentEmployeeShare", dom, {}).then(res => {
					if (res.status == 200) {
						this.getEmployeeList()
						this.$message.success("更新成功")
					} else {
						this.$message.error(res.data)
					}
				});
			},
			teamEmployee(list) {
				console.log(list)
				this.ids = list
				this.updateAgentEmployeeShareList()
				this.employeeDrawer = false


			},
			onChange2(index) {
				console.log(index)
				this.logModel.current = index;
				this.getLos()
			},
			onPageSizeChange2(size) {
				console.log(size)
				this.logModel.size = size;
				this.getLos()
			},
			getLos() {
				this.loading2 = true
				this.$postData("page_orderNeedsLog", this.logModel, {}).then(res => {
					if (res.status == 200) {
						this.logList = res.data.records;
						this.pageInfo2.current = res.data.current;
						this.pageInfo2.size = res.data.size;
						this.pageInfo2.total = res.data.total
					} else {
						this.$message.error("查询失败，" + res.msg);
					}
					this.loading2 = false
				})

			},
			exportLog() {
				if (this.dateTime) {
					this.logModel.startTime = this.dateTime[0];
					this.logModel.endTime = this.dateTime[1];
				} else {
					this.logModel.startTime = null;
					this.logModel.endTime = null;
				}
				this.loadingExcel = true;
				this.$postData("orderNeedsLogExport", this.logModel, {
					responseType: "arraybuffer"
				}).then(res => {
					this.loadingExcel = false;
					this.blobExport({
						tablename: "跟踪日志",
						res: res
					});
				})
			},
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			searchLog() {
				if (this.dateTime) {
					this.logModel.startTime = this.dateTime[0];
					this.logModel.endTime = this.dateTime[1];
				} else {
					this.logModel.startTime = null;
					this.logModel.endTime = null;
				}
				this.loading2 = true
				this.$postData("page_orderNeedsLog", this.logModel, {}).then(res => {
					if (res.status == 200) {
						this.logList = res.data.records;
						this.pageInfo2.current = res.data.current;
						this.pageInfo2.size = res.data.size;
						this.pageInfo2.total = res.data.total
					} else {
						this.$message.error("查询失败，" + res.msg);
					}
					this.loading2 = false
				})
			},
			selectLog(value) {
				Vue.set(this.logModel, "title", value);
			},
			saveAgentStoreSetting() {
				this.indexNum = null
				this.$postData("saveAgentStoreSetting", this.agentStoreSetting, null).then(res => {
					if (res.status == 200) {
						this.$message.success("更新成功")
						this.getAgentStoreSetting()
					}
				});
			},
			feiniaoTask() {
				let isFly = this.agentStoreSetting1.isFly;
				Vue.set(this.agentStoreSetting, "isFly", isFly);
				this.$postData("saveAgentStoreSetting", this.agentStoreSetting, null).then(res => {
					if (res.status == 200) {

						;
						this.$message.success("更新成功")
						this.getAgentStoreSetting()
					}
				});

			},
			agentStoreShareInint() {

				this.$confirm('（默认每晚11:55分自动更新）此操作将可能影响分发顺序, 是否继续?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					this.inintLoading = true
					this.$getData("agentStoreShareInint", null).then(res => {
						if (res.status == 200) {
							;
							this.inintLoading = false
							this.getAgentStoreShareList()
							this.$message.success("更新成功")

						}
					});
				}).catch(() => {
					return this.$message({
						type: 'info',
						message: '已取消操作'
					});
				});

			},
			getAgentStoreSetting() {
				this.$postData("getAgentStoreSetting", {}, null).then(res => {
					if (res.status == 200) {
						this.agentStoreSetting = res.data;
						this.agentStoreSetting1.isFly = res.data.isFly;
						this.indexNum = this.agentStoreSetting.indexNum % this.agentStoreShareList.length;
					}
				});
			},
			deleteRow(index, rows) {

				rows[index].status = 0
				this.onSubmit(rows[index])
			},
			getAgentStoreShareList() {
				this.$getData("agentStoreShareList", {}, ).then(res => {
					if (res.status == 200) {


						this.agentStoreShareList = res.data;

						this.getAgentStoreSetting()
					}
				});
			},
			onSubmit(dom) {
				if (dom.storeId == null || dom.employeeId == null) {
					return this.$message.error("请选择正确的门店或接收人")
				}
				dom.operatorId = localStorage.getItem("id");
				this.$postData("saveAgentStore", dom, {}).then(res => {
					if (res.status == 200) {
						if (res.data == 1) {
							this.drawer = false;
							this.$message.success("更新门店成功")
							this.etAgentStoreList()
						} else {
							this.etAgentStoreList()
							return this.$message.error("更新失败，请检查门店是否存在")
						}

					}
				});
			},
			getStore() {
				this.$postData("store_getByList", {}, {}).then(res => {
					if (res.status == 200) {

						;
						this.storeList = res.data;
					}
				});
			},
			etAgentStoreList() {
				this.$postData("getAgentStoreList", this.search, {}).then(res => {
					if (res.status == 200) {

						;
						this.agentStoreList = res.data;
					}
				});
			},
			handleSelect(item) {
				this.agentStore.employeeId = item.name;
			},
			querySearchAsync(queryString, cb) {
				this.restaurants = []
				this.baomuModel.realName = this.agentStore.agentName;
				this.baomuModel.selectType = 1;
				this.$postData("agent_list", this.baomuModel, {}).then(res => {
					if (res.status == 200) {
						this.list = []
						this.list = res.data;
						this.list.forEach((item, index, arr) => {
							var a = {}
							a.value = item.realName;
							a.name = item.id;
							this.restaurants.push(a);
						});

					} else {
						this.$message.error("查询失败，" + res.msg);
					}
				})
				var restaurants = this.restaurants;
				var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants;
				cb(restaurants);
			},
			createStateFilter(queryString) {
				return (state) => {
					return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
				};
			},
			submitFlyBrid(val) {
				this.chooseMode = val;
				if (this.chooseMode.missionList.length == null || this.chooseMode.missionList.length === 0) {
					this.$message.error("分配任务不能为空");
				}
				if (this.chooseMode.employList.length == null || this.chooseMode.employList.length === 0) {
					this.$message.error("分配人员不能为空");
				}
				if (this.chooseMode.missionList.length < this.chooseMode.employList.length) {
					this.$message.error("任务分配数小于人员数，个别人员无法轮询到分发机制");
				}

				let taskFlyBridDto = {
					missionList: this.chooseMode.missionList,
					employList: this.chooseMode.employList
				}
				this.$postData("getFlyBridOrder", taskFlyBridDto, {}).then(res => {
					if (res.status == 200) {
						this.$message.success("操作成功");
					} else {
						this.$message.error("分配失败，" + res.msg);
					}
				})
				this.employeeDrawer1 = false;

			},
			// 打开编辑栏目
			openModal(index, index1) {
				if (index == 0) {
					this.detailType = 0
					this.choiceItem = {}
					this.drawerServiceScoreRule = true
				} else if (index == 1) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.serviceScoreRuleList[index1]
					this.drawerServiceScoreRule = true
				} else if (index == 2) {
					this.detailType = 0
					this.choiceItem = {}
					this.drawerServiceScoreRuleGroup = true
				} else if (index == 3) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.serviceScoreRuleGroupList[index1]
					this.drawerServiceScoreRuleGroup = true
				}
			},
			// 获取索引
			getIndex(val, list) {
				let index = 0
				for (let i = 0; i < list.length; i++) {
					if (val == list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 打开详情
			openDetail(id) {
				this.choiceItemIndex = this.getIndex(id, this.agentStoreList)
				this.drawerAgentStore = true
				this.getStoreServiceScore()
				this.listAgentStoreLog()
				this.listAgentStoreRuleLog()
				this.listStoreLevelRecord()
			},
			// 时间格式化
			formatDate(value) {
				if (value == null) {
					return "-"
				}
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				return y + '.' + MM + '.' + d;
			},
			// 格式化标签显示
			formatType(value, val) {
				let result = "暂无"
				let type = 0
				let typeData = []
				if (value == 0) {
					type = val.ruleType
					typeData = this.ruleTypeList
				} else if (value == 1) {
					type = val.ruleGroup
					typeData = this.ruleGroupList
				} else if (value == 2) {
					type = val.ruleState
					typeData = this.stateList
				} else if (value == 3) {
					type = val.logState
					typeData = this.stateList
				} else if (value == 4) {
					type = val.defaultReach
					typeData = this.stateList
				}
				for (let item of typeData) {
					if (item.id == type) {
						result = item.text
						break
					}
				}
				return result
			},
			// 重置
			reset() {
				this.quer.search = ''
				this.quer.ruleState = null
				this.quer.groupState = null
				this.quer.ruleGroupId = null
				this.searchState = ''
				this.searchApprovalState = ''
				this.searchNeedApproval = ''
				this.searchGroup = null
				this.getAgentStoreRuleRelated()
				this.pageServiceScoreRuleApproval()
				this.$message.success('已重置！')
			},
			// 获取门店积分规则-相关数据
			getAgentStoreRuleRelated() {
				// 考试-课程
				this.$postData("getUnionExam", {
					state: 0
				}).then(res => {
					if (res.status == 200) {
						this.examList = res.data
					}
				})

				this.$postData("listCourse", {}).then(res => {
					if (res.status == 200) {
						this.courseList = res.data
					}
				})
				this.listServiceScoreRule()
				this.listServiceScoreRuleGroup()
				this.listAgentStoreRule()
			},
			formatGroupScore(item) {
				let str = item.groupName + '（' + Number(item.groupIntegral).toFixed(2)
				if (item.groupId < 5) {
					str += '/' + Number(item.groupIntegralRate * 100).toFixed(2)
				}
				return str + '）'
			},
			// 获取服务分明细
			getStoreServiceScore() {
				let id = this.agentStoreList[this.choiceItemIndex].id
				let storeId = this.agentStoreList[this.choiceItemIndex].storeId
				let score = this.agentStoreList[this.choiceItemIndex].integral
				$.ajax({
					type: "POST",
					url: 'https://api.xiaoyujia.com/task/getStoreServiceScore',
					data: JSON.stringify({
						agentStoreId: id,
						storeId: storeId
					}),
					dataType: "json",
					contentType: "application/json;charset=UTF-8",
					success: (res) => {
						if (res.code == 0) {
							this.scoreDetail = res.data
							this.scoreResult = '（总分：' + score + '）'
							this.listServiceScoreRuleApproval()
						} else {
							this.scoreDetail = null
							this.scoreResult = ''
						}
					},
				});
			},
			// 获取通过审批的加分记录
			listServiceScoreRuleApproval() {
				let storeId = this.agentStoreList[this.choiceItemIndex].storeId
				$.ajax({
					type: "POST",
					url: 'https://api.xiaoyujia.com/task/listServiceScoreRuleApproval',
					data: JSON.stringify({
						approvalState: 2,
						storeId: storeId,
						thisWeek: 1
					}),
					dataType: "json",
					contentType: "application/json;charset=UTF-8",
					success: (res) => {
						if (res.code == 0) {
							this.ruleApprovalList = res.data
							let score = 0
							this.ruleApprovalList.forEach(item => {
								score += item.ruleIntegralPresets
							})
							let scoreTotal = parseInt(this.scoreDetail.ruleIntegralResult) + score
							// this.scoreResult = '（总分：' + scoreTotal + '）'
						} else {
							this.ruleApprovalList = []
						}
					},
				});
			},
			listAgentStoreLog() {
				let id = this.agentStoreList[this.choiceItemIndex].id
				this.$postData("listAgentStoreLog", {
					agentStoreId: id
				}).then(res => {
					if (res.status == 200) {
						this.agentStoreLogList = res.data
					} else {
						// this.$message.error(res.msg)
						this.agentStoreLogList = []
					}
				})
			},
			// 获取门店星级记录
			listStoreLevelRecord() {
				let storeId = this.agentStoreList[this.choiceItemIndex].storeId
				$.ajax({
					type: "POST",
					url: 'https://api.xiaoyujia.com/task/listStoreLevelRecord',
					data: JSON.stringify({
						storeId: storeId
					}),
					dataType: "json",
					contentType: "application/json;charset=UTF-8",
					success: (res) => {
						if (res.code == 0) {
							this.storeLevelRecordList = res.data
						} else {
							this.storeLevelRecordList = []
						}
					},
				});
			},
			// 获取服务分规则列表
			listServiceScoreRule() {
				this.quer.ruleState = this.searchState
				this.quer.ruleGroupId = this.searchGroup
				this.quer.needApproval = this.searchNeedApproval
				this.quer.orderBy = 't.id ASC'
				this.$postData("listServiceScoreRule", this.quer, {}).then(res => {
					if (res.status == 200) {
						this.serviceScoreRuleList = res.data
					} else {
						this.$message.error(res.msg)
						this.serviceScoreRuleList = []
					}
				})
			},
			// 添加服务分规则
			insertServiceScoreRule() {
				let val = this.serviceScoreRule
				if (!val.ruleTitle) {
					this.$message.error('请填写规则标题！')
				} else if (!val.ruleContent) {
					this.$message.error('请填写规则内容！')
				} else if (!val.ruleGroupId) {
					this.$message.error('请填写规则分组！')
				} else {
					val.reachFlag = val.reachFlag || null
					this.$postData("insertServiceScoreRule", val).then(res => {
						if (res.status == 200) {
							this.$message.success('添加服务分规则成功！')
						} else {
							this.$message.error(res.msg)
						}
					})
				}
			},
			// 更新服务分规则
			updateServiceScoreRule(val) {
				let ruleIntegralPresets = val.ruleIntegralPresets || null
				let val1 = {
					"id": val.id,
					"ruleTitle": val.ruleTitle,
					"ruleContent": val.ruleContent,
					"ruleType": val.ruleType,
					"ruleGroupId": val.ruleGroupId,
					"ruleIntegralPresets": ruleIntegralPresets,
					"ruleIntegralRate": val.ruleIntegralRate,
					"reachFlag": val.reachFlag || null,
					"defaultReach": val.defaultReach,
					"ruleState": val.ruleState,
					"needApproval": val.needApproval,
				}
				this.$postData("updateServiceScoreRule", val1).then(res => {
					if (res.status == 200) {
						this.$message.success('更新服务分规则成功！')
					} else {
						this.$message.error(res.msg)
					}
				})
			},
			// 删除服务分规则
			deleteServiceScoreRule(val) {
				this.$postData("deleteServiceScoreRule", val).then(res => {
					if (res.status == 200) {
						this.$message.success('删除服务分规则成功！')
					} else {
						this.$message.error(res.msg)
					}
				})
			},
			// 获取服务分规则分组
			listServiceScoreRuleGroup() {
				this.quer.groupState = this.searchState
				this.$postData("listServiceScoreRuleGroup", this.quer, {}).then(res => {
					if (res.status == 200) {
						this.serviceScoreRuleGroupList = res.data
					} else {
						this.$message.error(res.msg)
						this.serviceScoreRuleGroupList = []
					}
				})
			},
			// 添加服务分规则分组
			insertServiceScoreRuleGroup() {
				let val = this.serviceScoreRuleGroup
				if (!val.groupName) {
					this.$message.error('请填写规则分组标题！')
				} else if (!val.groupContent) {
					this.$message.error('请填写规则分组内容！')
				} else if (!val.groupIntegralRate) {
					this.$message.error('请填写分值比例！')
				} else {
					this.$postData("insertServiceScoreRuleGroup", val).then(res => {
						if (res.status == 200) {
							this.$message.success('添加服务分规则分组成功！')
						} else {
							this.$message.error(res.msg)
						}
					})
				}
			},
			// 更新服务分规则分组
			updateServiceScoreRuleGroup(val) {
				this.$postData("updateServiceScoreRuleGroup", val).then(res => {
					if (res.status == 200) {
						this.$message.success('更新服务分规则分组成功！')
					} else {
						this.$message.error(res.msg)
					}
				})
			},
			// 搜索门店积分规则
			listAgentStoreRule() {
				this.$postData("listAgentStoreRule", this.quer, {}).then(res => {
					if (res.status == 200) {
						this.agentStoreRuleList = res.data
						// 追加编辑状态位
						for (let item of this.agentStoreRuleList) {
							this.$set(item, "isEdit", false)
						}
					} else {
						this.$message.error(res.msg)
						this.agentStoreRuleList = []
					}
				})
			},
			updateAgentStoreRule(val) {
				this.$postData("updateAgentStoreRule", val).then(res => {
					if (res.status == 200) {
						val.isEdit = false
						this.$message.success('更新积分规则成功！')
					} else {
						this.$message.error(res.msg)
					}
				})
			},
			deleteAgentStoreRule(index) {
				let val = this.agentStoreRuleList[index]
				this.$postData("deleteAgentStoreRule", val).then(res => {
					if (res.status == 200) {
						this.$delete(this.agentStoreRuleList, index)
						this.$message.success('删除积分规则成功！')
					} else {
						this.$message.error(res.msg)
					}
				})
			},
			insertAgentStoreRule() {
				let val = this.agentStoreRule
				if (val.ruleType == null) {
					this.$message.error('请填写规则类型！')
				} else if (val.ruleGroup == null) {
					this.$message.error('请填写规则分组！')
				} else if (val.ruleTitle == null) {
					this.$message.error('请填写规则标题！')
				} else if (val.ruleContent == null) {
					this.$message.error('请填写规则内容！')
				} else if (val.ruleAmount == null) {
					this.$message.error('请填写奖惩金额！')
				} else if (val.ruleIntegral == null) {
					this.$message.error('请填写奖惩积分！')
				} else {
					let no = localStorage.getItem("account")
					if (no == undefined) {
						no = "admin"
					}
					this.$set(val, "creater", no)
					this.$postData("insertAgentStoreRule", val).then(res => {
						if (res.status == 200) {
							this.agentStoreRuleList.push(val)
							this.agentStoreRule = {}
							this.drawerRule = false
							this.$message.success('添加积分规则成功！')
						} else {
							this.$message.error(res.msg)
						}
					})
				}
			},
			listAgentStoreRuleLog() {
				let id = this.agentStoreList[this.choiceItemIndex].id
				this.$postData("listAgentStoreRuleLog", {
					agentStoreId: id
				}).then(res => {
					if (res.status == 200) {
						this.agentStoreRuleLogList = res.data
						// 追加编辑状态位
						for (let item of this.agentStoreRuleLogList) {
							this.$set(item, "isEdit", false)
						}
					} else {
						// this.$message.error(res.msg)
						this.agentStoreRuleLogList = []
					}
				})
			},
			updateAgentStoreRuleLog(val) {
				this.$postData("updateAgentStoreRuleLog", val).then(res => {
					if (res.status == 200) {
						val.isEdit = false
						this.$message.success('更新奖惩日志成功！')
					} else {
						this.$message.error(res.msg)
					}
				})
			},
			deleteAgentStoreRuleLog(index) {
				let val = this.agentStoreRuleLogList[index]
				this.$postData("deleteAgentStoreRuleLog", val).then(res => {
					if (res.status == 200) {
						this.$delete(this.agentStoreRuleLogList, index)
						this.$message.success('删除奖惩日志成功！')
					} else {
						this.$message.error(res.msg)
					}
				})
			},
			insertAgentStoreRuleLog() {
				let val = this.agentStoreRuleLog
				let agentStoreId = this.agentStoreList[this.choiceItemIndex].id
				this.$set(val, "agentStoreId", agentStoreId)
				if (val.ruleId == null) {
					this.$message.error('请填写奖惩规则！')
				} else if (val.logTitle == null) {
					this.$message.error('请填写奖惩标题！')
				} else if (val.logContent == null) {
					this.$message.error('请填写奖惩内容（原因）！')
				} else {
					let no = localStorage.getItem("account")
					if (no == undefined) {
						no = "admin"
					}
					this.$set(val, "creater", no)
					this.$postData("insertAgentStoreRuleLog", val).then(res => {
						if (res.status == 200) {
							this.agentStoreRuleLogList.push(val)
							this.agentStoreRuleLog = {}
							this.drawerRuleLog = false
							this.listAgentStoreRuleLog()
							this.$message.success('添加奖惩成功！')
						} else {
							this.$message.error(res.msg)
						}
					})
				}
			},
			// 获取审批记录列表
			pageServiceScoreRuleApproval() {
				let quer = {
					search: this.quer.search,
					orderBy: 't.createTime DESC',
					approvalState: this.searchApprovalState,
					current: this.quer.current,
					size: this.quer.size
				}
				this.$postData("pageServiceScoreRuleApproval", quer).then(res => {
					if (res.status == 200) {
						this.approvalList = res.data.records
						this.pageInfo2.current = res.data.current
						this.pageInfo2.size = res.data.size
						this.pageInfo2.total = res.data.total
					} else {
						this.$message.error("查询失败，" + res.msg);
					}
					this.loading = false
				})
			},
			// 更新审批记录
			updateServiceScoreRuleApproval(val) {
				if (val.approvalState == 0) {
					this.$message.error('请选择审批结果！')
				} else if (val.approvalState == 1 && !val.approvalRemark) {
					this.$message.error('请补充不通过原因！')
				} else {
					let id = localStorage.getItem('id') || 12
					this.$set(val, 'approvalor', id)
					this.$postData("updateServiceScoreRuleApproval", val).then(res => {
						if (res.status == 200) {
							this.$message.success('审批成功！')
							// 通过尝试更新分数
							if (val.approvalState == 2) {
								this.updateServiceScore(val)
							}
							this.approvalModal = false
						} else {
							this.$message.error(res.msg)
						}
					})
				}
			},
			// 更新服务分
			updateServiceScore(val) {
				let agentStoreId = val.agentStoreId
				$.ajax({
					type: "POST",
					url: 'https://api.xiaoyujia.com/task/updateServiceScore',
					data: JSON.stringify({
						id: agentStoreId
					}),
					dataType: "json",
					contentType: "application/json;charset=UTF-8",
					success: (res) => {
						if (res.code == 0) {

						}
					},
				});
			},
			// 服务分明细下载
			agentStoreDownload() {
				this.$postData("listAgentStoreExcelDownload", {}, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('服务分明细记录下载成功!')
					this.blobExport({
						tablename: this.formatDate(new Date()) + "_服务分明细记录",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
		}

	}
</script>

<style scoped>
	.el-table .warning-row {
		background: oldlace;
	}

	.drawerForm {
		padding: 20px;
	}

	.sortClass {
		font-size: 20px;
		padding: 20px;
		color: #1677FF;
		font-weight: bold;
	}
</style>