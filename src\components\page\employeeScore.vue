<template>
  <div>
    <div class="handle-box">
      <el-form ref="form" :model="pageInfo">
        <el-row>
          <el-col :span="5">
            <el-form-item label="订单号">
              <el-input v-model="quer.billNo" placeholder="订单号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="员工工号">
              <el-input v-model="quer.employeeNo" placeholder="员工工号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="评分类别">
              <el-select v-model="dto.scoreType">
                <el-option label="全部"></el-option>
                <el-option label="中评" value="61"></el-option>
                <el-option label="差评" value="60"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="评价时间区间">
              <el-date-picker
                  v-model="date1"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-button type="primary" @click="query()" icon="el-icon-search"
                       style="margin-left: 20px;margin-top: 30px;">搜索
            </el-button>
          <el-button  icon="el-icon-refresh" @click="reset()" style="margin-left: 20px">重置</el-button>
            <el-button type="success"  :loading="loadingExcel" icon="el-icon-download" @click="download" style="margin-left: 20px">导出</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="30">

            <el-button type="success"  :loading="loadingExcel" icon="el-icon-download" @click="downloadscore6(10,11)" >低于10分名单导出</el-button>
            <el-button type="success"  :loading="loadingExcel" icon="el-icon-download" @click="downloadscore6(6,9)" >低于6分名单导出</el-button>
            <el-button type="success"  :loading="loadingExcel" icon="el-icon-download" @click="downloadscore6(3,5)" >低于3分名单导出</el-button>
            <el-button type="success"  :loading="loadingExcel" icon="el-icon-download" @click="downloadscore6(0,2)" >低于0分名单导出</el-button>
          </el-col>
        </el-row>

      </el-form>
    </div>
    <el-table :data="list" height="450px" v-loading="loading" border
              :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
      <el-table-column
          prop="billNo"
          width="200"
          label="订单号">
      </el-table-column>
      <el-table-column
          width="110"
          prop="employeeNo"
          label="员工工号"
      ></el-table-column>
      <el-table-column
          width="110"
          prop="scoreTypeName"
          label="评分类别">
      </el-table-column>
      <el-table-column
          width="160"
          prop="reamke"
          label="评分备注">
      </el-table-column>
      <el-table-column
          width="80"
          prop="oldScore"
          label="原积分">
      </el-table-column>
      <el-table-column
          width="80"
          prop="changeScore"
          label="扣减分数">
      </el-table-column>
      <el-table-column
          width="80"
          prop="score"
          label="积分余额">
      </el-table-column>
      <el-table-column
          prop="createTime"
          label="评价时间">
      </el-table-column>

    </el-table>
    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChange"
            :current="this.dto.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="10"/>
    </div>

  </div>
</template>

<script>
import Vue from 'vue';

export default {
  name: "employeeScore",

  data() {
    return {
      loadingExcel: false,
      list: null,
      loading: true,
      date1: null,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      quer: {
        billNo: null,
        employeeNo: null,
        scoreType: null,
        startTime: null,
        endTime: null,
        score:null,
        size: 10,
        current: 1,
        startScore:null,
        endScore:null,
      },
      dto: {
        id: null,
        billNo: null,
        employeeNo: null,
        scoreType: null,
        reamke: null,
        oldScore: null,
        changeScore: null,
        score: null,
        createTime: null,
      },
      rowData: null,
      journalInfoLoading: true,
      journalList: null,
      journalInfoSizeOpts: [10, 20, 30],
      journalInfo: {total: 10, size: 10, current: 1, pages: 1},
      journalInfoDrawer: false,

    }
  },
  created() {

    this.getData();
  },
  methods: {

    getData() {
      if (this.date1 !=null){
        this.quer.startTime = this.date1[0];
        this.quer.endTime = this.date1[1];
      } else {
        this.quer.startTime = null;
        this.quer.endTime = null;
      }
      this.$postData("selectEmployeeScore", this.quer).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    query() {
      this.loading = true;
      this.quer.current = 1;
      this.getData();
    },
    reset() {
      this.quer.billNo = null;
      this.quer.employeeNo = null;
      this.quer.scoreType = null;
      this.quer.startTime = null;
      this.quer.endTime = null;
      this.date1 = null;
      this.getData();
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.quer.size = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.quer.current = index;
      this.getData();
    },
    downloadscore6(startScore,endScore){
      this.quer.startScore = startScore;
      this.quer.endScore = endScore;
      this.download()
    },
    download(){

      this.loadingExcel = true;
      this.$postData("downloadEmployeeScore", this.quer, {
        responseType: "arraybuffer"
      }).then(res => {
        this.quer.score = null;
        this.quer.startScore = null;
        this.quer.endScore = null;
        this.loadingExcel = false;
        console.log(res)
        this.blobExport({
          tablename: "评分统计",
          res: res
        });
      })
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },

  },
}
</script>

<style scoped>
.handle-box {
  /*margin-bottom: 10px;*/
  font-weight: bold !important;
}

table {
  border-collapse: collapse; /*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
  border-left: #C8B9AE solid 1px;
  border-top: #C8B9AE solid 1px;
}

table td {
  border-right: #C8B9AE solid 1px;
  border-bottom: #C8B9AE solid 1px;
}

th {
  background: #eee;
  font-weight: normal;
}

tr {
  background: #fff;
}

tr:hover {
  background: #cc0;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

>>> .el-upload--text {
  width: 200px;
  height: 150px;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
