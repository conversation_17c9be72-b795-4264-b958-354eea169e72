<template>
  <div class="table">
    <div class="container">
      <div class="handle-box">
        <el-form ref="form" :model="pageInfo">
          <el-row>
            <el-col :span="5">
              <el-form-item label="订单编号">
                <el-input
                    clearable
                    v-model="dto.billNo"
                    placeholder="订单编号"
                    style="width:180px"
                    class="handle-input mr10"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="会员账户">
                <el-input
                    clearable
                    v-model="dto.account"
                    placeholder="会员账户"
                    style="width:150px"
                    class="handle-input mr10"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="发票号码">
                <el-input
                    clearable
                    v-model="dto.invoiceNo"
                    placeholder="发票号码"
                    style="width:120px"
                    class="handle-input mr10"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="下单时间">
                <el-date-picker
                    style="width: 170px"
                    v-model="dto.startTime"
                    type="datetime"
                    placeholder="开始时间"
                    format="yyyy-MM-dd HH:mm"
                    value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5" style="text-align: left">
              <el-form-item label="至">
                <el-date-picker
                    style="width: 170px"
                    v-model="dto.endTime"
                    type="datetime"
                    placeholder="结束时间"
                    format="yyyy-MM-dd HH:mm"
                    value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6" style="text-align:left">
              <el-form-item>
                <el-button type="success" style="width:30%"
                           @click="oneButtonList" :loading="loading1">批量充值
                </el-button>
                <el-button type="primary" style="width:30%" @click="query()">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-table :data="list" border class="table"
                v-loading="loading"
                ref="multipleTable"
                tooltip-effect="dark"
                @selection-change="handleSelectionChange">
        <el-table-column
            type="selection"
            width="55">
        </el-table-column>
        <el-table-column
            width="100"
            label="操作">
          <template slot-scope="scope">
            <div style="text-align: center">
              <div>
                <el-button size="mini" @click="goRecharge(scope.row)" type="primary">充值返单</el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
            prop="order.billNo"
            width="170"
            label="订单编号">
          <template slot-scope="scope">
            <div v-for="item in scope.row.orderList" style="text-align: center;font-size: 15px">
              <a :href="url+item.billNo"
                 target="_Blank">{{ item.billNo }}</a>
            </div>
          </template>
        </el-table-column>
        <el-table-column
            width="130"
            prop="order.productName"
            label="服务项目"
            :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <div v-for="item in scope.row.orderList" style="text-align: center;font-size: 15px">
              <span>{{ item.productName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
            width="120"
            prop="invoiceNo"
            label="发票号"
        ></el-table-column>
        <el-table-column
            prop="header"
            width="100"
            label="发票抬头"
            :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
            width="90"
            prop="amount"
            label="开票金额"
        ></el-table-column>
        <el-table-column
            prop="account"
            width="150"
            label="会员账号"
            :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
            width="150"
            prop="taxpayerIdentityNumber"
            label="税号"
            :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column
            prop="createTime"
            label="下单时间">
          <template slot-scope="scope">
            <div style="text-align: center;font-size: 15px">
              <span>{{ scope.row.createTime.replace("T", " ") }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="pageInfo.total"
              :current="this.dto.pageNum"
              @on-change="onChange"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onPageSizeChange"
              :page-size="10"/>
      </div>
    </div>
    <Modal v-model="modal" class="Modal" width="77%" title="充值"
           :mask-closable="false"
           @on-cancel="getData()">
      <div style="height: 450px">
        <div style="height: 450px;overflow-x: hidden">
          <el-table :data="rechargeList" border class="table"
                    ref="multipleTable"
                    tooltip-effect="dark">
            <el-table-column
                prop="order.billNo"
                width="170"
                label="订单编号">
              <template slot-scope="scope">
                <div v-for="item in scope.row.orderList" style="text-align: center;font-size: 15px">
                  <span>{{ item.billNo }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
                width="90"
                prop="amount"
                label="支付金额"
            ></el-table-column>
            <el-table-column
                prop="order.billNo"
                width="150"
                label="支付类型">
              <template slot-scope="scope">
                <Select style="width: 120px;
               top: auto;left: auto;z-index: 9999"
                        transfer
                        v-model="rechargeList[scope.$index].paymentChannel" label-in-value
                        @on-change="change($event,scope.$index)">
                  <Option :value="23">股份银行转账</Option>
                  <Option :value="24">服务银行转账</Option>
                  <Option :value="25">业绩卡转账</Option>
                  <Option :value="26">股份支付宝</Option>
                  <Option :value="27">服务财付通</Option>
                  <Option :value="31">小羽佳人收款端</Option>
                  <Option :value="3">银联</Option>
                  <Option :value="36">商户收款</Option>
                  <Option :value="37">电商银行转账</Option>
                </Select>
              </template>
            </el-table-column>
            <el-table-column
                prop="order.billNo"
                width="130"
                label="汇款类型">
              <template slot-scope="scope">
                <Select style="width: 100px;top: auto;
              left: auto;z-index: 9999"
                        transfer
                        v-model="rechargeList[scope.$index].sendType">
                  <Option :value="1">个人</Option>
                  <Option :value="2">企业</Option>
                </Select>
              </template>
            </el-table-column>
            <el-table-column
                prop="order.billNo"
                width="160"
                label="转账银行">
              <template slot-scope="scope">
                <Select style="width: 130px;top: auto;
              left: auto;z-index: 9999"
                        transfer
                        v-model="rechargeList[scope.$index].customerBank">
                  <Option value="2">股份公司建行</Option>
                  <Option value="4">服务公司建行</Option>
                  <Option value="21">兴业银行</Option>
                  <Option value="22">电子商务公司</Option>
                  <Option value="3">业绩卡建行</Option>
                  <Option value="165">电商银行转账</Option>
                </Select>
              </template>
            </el-table-column>
            <el-table-column
                prop="order.billNo"
                width="150"
                label="交易号">
              <template slot-scope="scope">
                <el-input
                    clearable
                    v-model="rechargeList[scope.$index].tradingCode"
                    placeholder="交易号"
                    style="width:120px"
                    class="handle-input mr10"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column
                prop="order.billNo"
                label="到账时间">
              <template slot-scope="scope">
                <el-date-picker
                    style="width: 190px"
                    v-model="rechargeList[scope.$index].payTime"
                    type="datetime"
                    placeholder="到账时间"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
              </template>
            </el-table-column>
          </el-table>
        </div>

      </div>
      <div slot="footer">
        <Button type="success" @click="sending" v-loading.fullscreen.lock="loading1">
          确定
        </Button>
        <Button @click="close">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>


export default {
  data() {
    return {
      modal: false,
      url: "https://yun.xiaoyujia.com/Account/TokenLogin?Token=" + localStorage.getItem('token') + "&returnUrl=/order/baseinfo?BillNo=",
      loading: true,
      loading1: false,
      list: null,
      tableData: [],
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      dto: {
        billNo: null,
        invoiceNo: null,
        header: null,
        account: null,
        startTime: null,
        endTime: null,
        pageSize: 10,
        pageNum: 1,
      },
      rechargeList: [],
    };
  },
  components: {},
  created() {
    this.getData();
  },

  computed: {},
  methods: {
    change(data, index) {
      console.log(data, index)
    },
    close() {
      this.modal = false
      this.rechargeList = [];
    },
    goRecharge(list) {
      this.modal = true
      this.rechargeList = [];
      this.rechargeList.push(list)
    },
    getData() {
      this.loading = true;
      this.$postData("memberBlackList_selectPage", this.dto, {}).then(res => {
        if (res.status === 200) {
          this.loading = false;
         ;
          this.list = res.data.list;
          this.pageInfo.current = res.data.pageNum;
          this.pageInfo.size = res.data.pageSize;
          this.pageInfo.total = res.data.total
        } else {
          this.loading = false;
        }
      });
    },

    changeValue(value) {
      if (value != null) {
        this.$getData("getProductName", {productCategoryId: value}).then(
            res => {
              this.options_children = res.data;
            }
        );
      }
    },
    query() {
      this.dto.pageNum = 1;
      this.loading = true;
      this.rechargeList = [];
      this.getData();
    },


    // 页码大小
    onPageSizeChange(size) {
      this.loading = true
      this.dto.pageSize = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      console.log(index)
      this.loading = true
      this.dto.pageNum = index;
      this.getData();
    },
    handleSelectionChange(list) {
      console.log(list)
      this.rechargeList = list;
    },
    /*
    * 一键充值
    * */
    oneButtonList() {
      if (this.rechargeList.length <= 0) {
        this.$message.error('请选择订单');
        return;
      }
      this.modal = true; //遮罩
    },
    sending() {
      let flag = false;
      this.rechargeList.forEach((item) => {
        if (item.tradingCode == null || item.tradingCode === '') {
          flag = true;
          return true
        }
        if (item.payTime == null || item.payTime === '') {
          flag = true;
          return true
        }
      })
      if (flag) {
        this.$message.error('信息填写完整');
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '充值中......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.$postData("memberBlackList_recharge",
          {
            models: this.rechargeList,
            employeeId: localStorage.getItem("id")
          }).then(res => {

        if (res.status === 200) {
          loading.close();
          this.modal=false
          this.$alert( res.data, '提示', {
            confirmButtonText: '确定',
          });
          this.rechargeList=[];
          this.getData();
        } else {
          loading.close();
          this.modal=false
          this.$alert(res.msg, '提示', {
            confirmButtonText: '确定',
          });
          this.rechargeList=[];
          this.getData();
        }
      })
    }
  }
};
</script>

<style scoped>
.handle-box {
  margin-bottom: 20px;
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}

.del-dialog-cnt {
  font-size: 16px;
  text-align: center;
}

.table {
  width: 100%;
  font-size: 13px;
}

.red {
  color: #ff0000;
}

.mr10 {
  margin-right: 10px;
}
</style>
