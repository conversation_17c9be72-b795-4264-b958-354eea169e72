<template>
    <div   v-loading="loading">
        <el-row :gutter="20">
            <el-col :span="24">
                <el-row :gutter="20" class="mgb20">
                    <el-col :span="5">
                        <el-card shadow="hover" :body-style="{padding: '0px'}">
                            <div class="grid-content grid-con-1">
                                <i class="el-icon-s-order grid-con-icon"></i>
                                <div class="grid-cont-right">
                                    <div class="grid-title">今日订单</div>
                                    <div class="grid-text">
                                        数量：
                                        <span class="grid-num">{{dto.orderTodayNum}}</span>
                                    </div>
                                    <div class="grid-text">
                                        金额：
                                        <span class="grid-num">{{dto.orderTodayPrice}}</span>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="5">
                        <el-card shadow="hover" :body-style="{padding: '0px'}">
                            <div class="grid-content grid-con-2">
                                <i class="el-icon-notebook-2 grid-con-icon"></i>
                                <div class="grid-cont-right">
                                    <div class="grid-title">今日服务订单</div>
                                    <div class="grid-text">
                                        数量：
                                        <span class="grid-num">{{dto.orderDispatchNum}}</span>
                                    </div>
                                    <div class="grid-text">
                                        金额：
                                        <span class="grid-num">{{dto.orderDispatchPrice}}</span>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="5">
                        <el-card shadow="hover" :body-style="{padding: '0px'}">
                            <div class="grid-content grid-con-1">
                                <i class="el-icon-document-copy grid-con-icon"></i>
                                <div class="grid-cont-right">
                                    <div class="grid-title">本月订单</div>
                                    <div class="grid-text">
                                        数量：
                                        <span class="grid-num">{{dto.orderMonthNum}}</span>
                                    </div>
                                    <div class="grid-text">
                                        金额：
                                        <span class="grid-num">{{dto.orderMonthPrice}}</span>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="5">
                        <el-card shadow="hover" :body-style="{padding: '0px'}">
                            <div class="grid-content grid-con-2">
                                <i class="el-icon-receiving grid-con-icon"></i>
                                <div class="grid-cont-right">
                                    <div class="grid-title">总订单</div>
                                    <div class="grid-text">
                                        数量：
                                        <span class="grid-num">{{dto.totalOrderNum}}</span>
                                    </div>
                                    <div class="grid-text">
                                        金额：
                                        <span class="grid-num">{{dto.totalOrderPrice}}</span>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="4">
                        <el-card shadow="hover" :body-style="{padding: '0px'}">
                            <div class="grid-content grid-con-1">
                                <i class="el-icon-user grid-con-icon"></i>
                                <div class="grid-cont-right">
                                    <div class="grid-title">总用户数</div>
                                    <div class="grid-text">
                                        数量：
                                        <span class="grid-num">{{dto.serviceStaffNum}}</span>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
                <el-row>
                    <p class="home-tip">渠道统计：{{dto.channelName}} 数据每30分钟更新</p>
                </el-row>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="12">
                <el-card shadow="hover">
                    <chart ref="chart1" :options="chart1" :auto-resize="true"></chart>
                </el-card>
            </el-col>
            <el-col :span="12">
                <el-card shadow="hover">
                    <chart ref="chart2" :options="chart2" :auto-resize="true"></chart>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>
export default {
    name: "dashboard",
    data() {
        return {
            loading:false,
            chart1: {},
            chart2: {},
            dto: {
                channelName: "",
                orderTodayNum: "***",
                orderTodayPrice: "***",
                orderDispatchNum: "***",
                orderDispatchPrice: "***",
                orderMonthNum: "***",
                orderMonthPrice: "***",
                totalOrderNum: "***",
                totalOrderPrice: "***",
                serviceStaffNum: "***"
            }
        };
    },
    created() {
        this.loadDate();
    },
    methods: {
        loadDate() {
            // const loading = this.$loading({
            //     lock: true,
            //     text: "数据处理中，请稍后！",
            //     spinner: "el-icon-loading",
            //     background: "rgba(0, 0, 0, 0.7)"
            // });
            this.loading=true
            this.$getData("getHomeInfo", {})
                .then(res => {
                    this.dto = res.data

                    if(res.data.chart1){
                        this.chart1 = JSON.parse(res.data.chart1);
                    }
                    if(res.data.chart2){
                        this.chart2 = JSON.parse(res.data.chart2);
                    }
                    // loading.close();
                    this.loading=false
                })
        }
    }
};
</script>


<style scoped>
.el-row {
    margin-bottom: 20px;
}

.grid-content {
    display: flex;
    align-items: center;
    height: 100px;
}

.grid-cont-right {
    flex: 1;
    text-align: center;
    font-size: 14px;
    color: #999;
}

.grid-title {
    color: #222;
    font-size: 16px;
}

.grid-text {
    font-size: 16px;
}

.grid-num {
    font-size: 20px;
    font-weight: bold;
}

.grid-con-icon {
    font-size: 50px;
    width: 100px;
    height: 100px;
    text-align: center;
    line-height: 100px;
    color: #fff;
}

.grid-con-1 .grid-con-icon {
    background: rgb(45, 140, 240);
}

.grid-con-1 .grid-num {
    color: rgb(45, 140, 240);
}

.grid-con-2 .grid-con-icon {
    background: rgb(100, 213, 114);
}

.grid-con-2 .grid-num {
    color: rgb(45, 140, 240);
}

.grid-con-3 .grid-con-icon {
    background: rgb(242, 94, 67);
}

.grid-con-3 .grid-num {
    color: rgb(242, 94, 67);
}

.user-info {
    display: flex;
    align-items: center;
    padding-bottom: 20px;
    border-bottom: 2px solid #ccc;
    margin-bottom: 20px;
}

.user-avator {
    width: 120px;
    height: 120px;
    border-radius: 50%;
}

.user-info-cont {
    padding-left: 50px;
    flex: 1;
    font-size: 14px;
    color: #999;
}

.user-info-cont div:first-child {
    font-size: 30px;
    color: #222;
}

.user-info-list {
    font-size: 14px;
    color: #999;
    line-height: 25px;
}

.user-info-list span {
    margin-left: 70px;
}

.mgb20 {
    margin-bottom: 20px;
}

.todo-item {
    font-size: 14px;
}

.todo-item-del {
    text-decoration: line-through;
    color: #999;
}

.schart {
    width: 100%;
    height: 300px;
}

.home-tip {
    text-align: right;
    color: #999;
    font-size: 14px;
}
</style>
