<template>
    <div>
        <div class="amap-wrapper" :style="'width:' +width+';height: '+height" v-if="lng!==null">
            <el-amap class="amap-box" :zoom="zoom" :center="center">
                <el-amap-marker></el-amap-marker>
                <!--            <el-amap-marker v-for="(marker, index) in markers"  :position="[marker.lng,marker.lat]"-->
                <!--                            :vid="index" :key="index" >-->
                <!--            </el-amap-marker>-->
            </el-amap>
        </div>
    </div>

</template>
<script>

    export default {
        props: {
            width: {
                type: String,
                default: '1440px'
            },
            // 初始化地图显示层级
            zoom: {
                type: Number,
                default: 16
            },
            height: {
                type: String,
                default: "600px"
            },
            lng: {
                type: Number,
                default: null
            },
            lat: {
                type: Number,
                default: null
            },
            markers: {
                type: Array,
                default: () => []
            },
        },
        name: "addrMap",
        watch: {
            option() {
                this.$nextTick(() => {
                    console.log(this.markers)
                })
            }
        },
        data() {
            // const self = this;
            return {
                center: [],//地图中心点坐标
            }
        },
        created() {
            this.center.push(this.lng);
            this.center.push(this.lat);
        },
        methods: {
            reloadMap(data) {
                this.$nextTick(() => {
                    this.center = [];
                    this.center.push(data.lng);
                    this.center.push(data.lat);
                })
            }
        }

    }
</script>

<style scoped>

</style>
