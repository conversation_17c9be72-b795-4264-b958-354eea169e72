<template>
    <div>
        <el-transfer
                filterable
                :filter-method="filterMethod"
                filter-placeholder="请输入站点名称"
                v-model="value"
                :titles="['可关联站点', '已关联站点']"
                :data="data">
        </el-transfer>
        <div style="margin-right: 100px;float: right;margin-top: 60px">
            <Button type="primary" @click="save('form')">确定</Button>
            <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
        </div>
    </div>

</template>

<script>
    export default {
        props:['model'],
        name: "siteModal",
        data() {
            return {
                data:[],
                value: [],
                filterMethod(query, item) {
                    return item.label.indexOf(query) > -1;
                },
                dom:this.model,
                isAngent:false,
                agentmodel:{

                    realName:"realName"
                }
            }

        },
        created() {
            this.inint();
            if (this.dom.roleId==82){
                this.isAngent=true

            }
        },
        methods: {
            save(){
                console.log(this.dom.id)
                console.log(this.value)
                this.$postUrl("up_Sites", this.dom.id,this.value, {}).then(res => {
                    if (res.status == 200) {

                        this.$message.success("关联成功，" + res.msg);
                        this.chooseThisModel("siteModal")

                    } else {
                        this.$message.error("关联失败，" + res.msg);
                    }
                })
            },
            /*
           * 关闭当前窗口
           * */
            inint(){
                this.agentmodel.noEmployeeId=this.dom.id,
                this.$postData("site_list", this.agentmodel, {}).then(res => {
                    if (res.status == 200) {

                        res.data.forEach((city, index) => {

                            this.data.push({
                                label: city.name,
                                key: city.id,
                            });

                        });

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                this.agentmodel=this.dom
                this.$postData("site_list", this.agentmodel, {}).then(res => {
                    if (res.status == 200) {
                        res.data.forEach((city, index) => {
                            this.data.push({
                                label: city.name,
                                key: city.id,
                            });
                            this.value.push(city.id);
                        });

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })


            },
            chooseThisModel(name) {
                this.dom=null;
                this.$emit('init-choose', name);
            },

        }
    }
</script>

<style scoped>

</style>
