<template>
  <div>
    <el-upload
        class="upload-demo"
        drag
        action="https://api.xiaoyujia.com/system/imageUpload"
        multiple
        :on-success="getSuccess">
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div class="el-upload__tip" slot="tip">若文件过大可先使用图片压缩，待压缩后上传压缩后的文件 <a href="https://tinypng.com/">压缩地址</a></div>
    </el-upload>
    <el-input
        type="textarea"
        :rows="2"
        placeholder="返回结果"
        v-model="textarea">
    </el-input>
  </div>
</template>

<script>
export default {
  name: "publicImageUpload",
  data() {
    return {
      textarea:'',
    }
  }, created() {
  }, methods: {
    getSuccess(res,file){
      this.textarea = res.data;
    console.log(res.data)
    }
  }
}
</script>

<style scoped>

</style>
