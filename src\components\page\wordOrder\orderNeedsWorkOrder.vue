<template>
	<div class="container">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">工单列表</el-menu-item>
			<el-menu-item index="1">其他</el-menu-item>
		</el-menu>

		<!-- 搜索栏目 -->
		<div v-if="choiceIndexNow==0">
			<div class="handle-box" style="margin: 20px 20px 20px 0px;">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="需求标题、经纪人名、门店等"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="线索状态" style="margin-right: 20px">
								<el-select v-model="quer.flowStatus" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in flowStatusList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<!-- <el-col :span="10">
							<el-form-item label="线索状态">
								<el-checkbox-group v-model="quer.flowStatusList" @change="query()">
									<el-checkbox-button label="0" name="0" border>电联</el-checkbox-button>
									<el-checkbox-button label="1" name="1" border>需求</el-checkbox-button>
									<el-checkbox-button label="3" name="3" border>预算</el-checkbox-button>
									<el-checkbox-button label="2" name="2" border>匹配</el-checkbox-button>
									<el-checkbox-button label="4" name="4" border>面试</el-checkbox-button>
									<el-checkbox-button label="5" name="5" border>签约</el-checkbox-button>
									<el-checkbox-button label="6" name="6" border>结算</el-checkbox-button>
								</el-checkbox-group>
							</el-form-item>
						</el-col> -->
						<el-col :span="9">
							<el-form-item label="筛选时间">
								<div style="width: 180px;display: block;">
									<el-date-picker v-model="searchTime" type="datetimerange" format="yyyy-MM-dd"
										:picker-options="pickerOptions1" range-separator="至" start-placeholder="开始日期"
										end-placeholder="结束日期" align="right" value-format="yyyy-MM-dd HH:mm:ss">
									</el-date-picker>
								</div>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="info" @click="excelDownload()">导出
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">

				<el-table-column prop="id" width="60" label="id">
				</el-table-column>

				<el-table-column width="130" prop="orderNeedsId" label="线索id">
				</el-table-column>

				<el-table-column width="130" prop="agentName" label="所属经纪人">
				</el-table-column>

				<el-table-column width="130" prop="storeName" label="所属门店">
				</el-table-column>

				<el-table-column width="130" prop="productName" label="需求标题">
				</el-table-column>

				<el-table-column width="150" prop="workContent" label="需求描述">
					<template slot-scope="scope">
						<span>{{scope.row.workContent||'暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="salary" label="薪资待遇">
					<template slot-scope="scope">
						<span>{{scope.row.salary?'￥'+scope.row.salary:'面议'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="finalTime" label="最后发布时间" sortable>
				</el-table-column>

				<el-table-column width="130" prop="publishNum" label="发布次数" sortable>
				</el-table-column>

				<el-table-column width="130" prop="viewNum" label="线索点击数" sortable>
				</el-table-column>

				<el-table-column width="130" prop="flowStatusName" label="需求状态">
				</el-table-column>

				<el-table-column width="130" prop="workOrderRemark" label="工单备注">
					<template slot-scope="scope">
						<span>{{scope.row.workOrderRemark||'暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="200" label="操作">
					<template slot-scope="scope">
						<el-button @click="openModal(1,scope.$index)" type="warning" size="small">明细
						</el-button>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<div v-if="choiceIndexNow==1">
			<el-button type="success" size="small">敬请期待</el-button>
		</div>

		<div class="pagination" v-if="choiceIndexNow==0">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--详细信息-->
		<el-drawer size="60%" :with-header="false" :visible.sync="orderNeedsWorkOrderModal" direction="rtl">
			<div class="handle-box" style="margin: 20px 20px;" v-if="choiceItem">
				<el-row>
					<h2 class="detail-title">线索工单详情</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="线索id：">
								<span>{{choiceItem.id}}</span>
							</el-form-item>

							<el-form-item label="线索id：">
								<span>{{choiceItem.orderNeedsId}}</span>
							</el-form-item>

							<el-form-item label="所属经纪人：">
								<span>{{choiceItem.agentName}}</span>
							</el-form-item>

							<el-form-item label="所属门店：">
								<span>{{choiceItem.storeName}}</span>
							</el-form-item>

							<el-form-item label="需求标题：">
								<span>{{choiceItem.productName}}</span>
							</el-form-item>

							<el-form-item label="需求描述：">
								<span>{{choiceItem.workContent||'暂无'}}</span>
							</el-form-item>

							<el-form-item label="薪资待遇：">
								<span>{{choiceItem.salary?'￥'+choiceItem.salary:'面议'}}</span>
							</el-form-item>
						</el-form>
					</el-col>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="最后发布时间：">
								<span>{{choiceItem.finalTime||'暂无'}}</span>
							</el-form-item>

							<el-form-item label="发布次数：">
								<span>{{choiceItem.publishNum}}</span>
							</el-form-item>

							<el-form-item label="线索点击数：">
								<span>{{choiceItem.viewNum}}</span>
							</el-form-item>

							<el-form-item label="工单备注：">
								<el-input v-model="choiceItem.workOrderRemark" type="textarea" class="handle-input mr10"
									placeholder="请输入直播设备名称" :autosize="{ minRows: 4, maxRows: 10}">>
								</el-input>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="orderNeedsWorkOrderModal=false" type="primary" size="small">关闭
					</el-button>
					<el-button @click="updateOrderNeedsWorkOrder(choiceItem)" type="success" size="small"
						v-if="detailType==1">保存备注
					</el-button>
				</div>

				<h2 class="detail-title">线索工单日志</h2>
				<el-form ref="form">
					<el-row style="margin: 20prx 40px;width: 100%;">
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="searchText1" placeholder="请输入群昵称、设备名称等"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="9">
							<el-form-item label="筛选时间">
								<div style="width: 180px;display: block;">
									<el-date-picker v-model="searchTime1" type="datetimerange" format="yyyy-MM-dd"
										:picker-options="pickerOptions1" range-separator="至" start-placeholder="开始日期"
										end-placeholder="结束日期" align="right" value-format="yyyy-MM-dd HH:mm:ss">
									</el-date-picker>
								</div>
							</el-form-item>
						</el-col>
						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="listOrderNeedsWorkOrderLog(choiceItem.id)"
								icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh"
								@click="searchTime1=[];searchText1='';listOrderNeedsWorkOrderLog(choiceItem.id)">重置
							</el-button>
							<!-- <el-button type="danger" @click="recordDownload()">导出</el-button> -->
						</el-col>
					</el-row>
				</el-form>

				<el-table :data="logList" v-loading="loading" border
					:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
					@expand-change="expandSelect">

					<el-table-column width="120" prop="workOrderId" label="线索工单id">
						<template slot-scope="scope">
							<span>{{scope.row.workOrderId||'暂无'}}</span>
						</template>
					</el-table-column>

					<el-table-column width="120" prop="groupName" label="群昵称">
						<template slot-scope="scope">
							<span>{{scope.row.groupName||'暂无'}}</span>
						</template>
					</el-table-column>

					<el-table-column width="120" prop="deviceId" label="设备id">
						<template slot-scope="scope">
							<span>{{scope.row.deviceId||'暂无'}}</span>
						</template>
					</el-table-column>

					<el-table-column width="120" prop="deviceName" label="设备名称">
						<template slot-scope="scope">
							<span>{{scope.row.deviceName||'暂无'}}</span>
						</template>
					</el-table-column>

					<el-table-column width="120" prop="account" label="私域账户">
						<template slot-scope="scope">
							<span>{{scope.row.account||'暂无'}}</span>
						</template>
					</el-table-column>

					<el-table-column width="120" prop="createTime" label="创建时间">
					</el-table-column>
				</el-table>
			</div>
		</el-drawer>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--视频预览-->
		<el-dialog :visible.sync="videoModal" width="40%" title="视频预览" :mask-closable="false">
			<video style="width: 100%;height: auto;margin: 0 auto;" :controls="true" :enable-progress-gesture="true"
				:initial-time="0" :show-center-play-btn="true" autoplay :src="videoUrl" custom-cache="false">
			</video>
		</el-dialog>
	</div>

</template>

<script>
	import Vue from 'vue';

	export default {
		name: "orderNeedsWorkOrder",

		data() {
			return {
				// 可设置
				// 是否超级管理员（可执行删除等敏感操作）
				isAdmin: false,

				url: '',
				imageUrl: '',
				videoUrl: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				// 文件上传地址
				fileUploadUrl: 'https://api.xiaoyujia.com/system/uploadFile',
				fileUploadData: {
					route: 'orderNeedsWorkOrder'
				},
				uploadFileType: 0,

				// baseUrl: 'http://localhost:8063/',
				baseUrl: 'https://biapi.xiaoyujia.com/',
				uploadUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
				deleteTips: "确定删除选中直播设备吗？该操作无法恢复，请谨慎操作!",
				imgModal: false,
				videoModal: false,
				orderNeedsWorkOrderModal: false,
				detailType: 0,
				loading: true,
				pageInfo: {
					total: 10,
					size: 20,
					current: 1,
					pages: 1
				},
				pageSizeOpts: [20, 50, 100],
				list: [],
				courseList: [],
				keyList: [],
				employeeList: [],
				choiceIndex: '0',
				choiceIndexNow: '0',
				choiceItemIndex: 0,
				choiceItem: null,
				choiceRecordId: 0,
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				signInTips: '',
				searchText: '',
				searchText1: '',
				searchState: null,
				searchRequired: null,
				quer: {
					search: "",
					flowStatus: null,
					// flowStatusList: [0, 1],
					startTimeSearch: null,
					endTimeSearch: null,
					orderBy: "t.id DESC",
					current: 1,
					size: 20
				},
				switchStateList: [{
						id: 0,
						typeName: "关闭",
						type: "info"
					},
					{
						id: 1,
						typeName: "开启",
						type: "success"
					}
				],
				stateList: [{
					value: 0,
					text: "关机停用",
					type: "info"
				}, {
					value: 1,
					text: "待机中",
					type: "primary"
				}, {
					value: 2,
					text: "直播中",
					type: "success"
				}],
				flowStatusList: [{
					value: 0,
					text: "电联",
				}, {
					value: 1,
					text: "需求",
				}, {
					value: 2,
					text: "预算",
				}, {
					value: 3,
					text: "匹配",
				}, {
					value: 4,
					text: "面试",
				}, {
					value: 5,
					text: "签约",
				}, {
					value: 6,
					text: "结算",
				}],
				typeStyleList: ["success", "info", "warning", "primary"],
				logList: [],
				searchTime: [],
				searchTime1: [],
				pickerOptions: {
					shortcuts: [{
						text: '昨天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '今天',
						onClick(picker) {
							picker.$emit('pick', new Date());
						}
					}, {
						text: '明天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周前',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周后',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}]
				},
				pickerOptions1: {
					shortcuts: [{
						text: '今天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '昨天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}]
				}
			}
		},
		created() {
			if (this.$route.query.isAdmin) {
				this.isAdmin = true
			}
			this.getData()
		},
		methods: {
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceItem = null
				this.choiceIndex = key
				this.expands = []
				this.reset()
			},
			getData() {
				this.choiceIndexNow = 2
				if (this.choiceIndex == 0) {
					// 处理一下时间筛选字段
					if (this.searchTime && this.searchTime.length >= 2) {
						this.quer.startTimeSearch = this.searchTime[0]
						this.quer.endTimeSearch = this.searchTime[1]
					} else {
						this.quer.startTimeSearch = null
						this.quer.endTimeSearch = null
					}
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'orderNeedsWorkOrder/pageOrderNeedsWorkOrder',
						data: JSON.stringify(this.quer),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							this.loading = false
							if (res.status == 200) {
								this.list = res.data.records
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
								this.choiceIndexNow = this.choiceIndex
							} else {
								this.list = []
								this.$message.error('未查询到相关线索工单!')
								this.choiceIndexNow = this.choiceIndex
							}
						},
					})
				} else if (this.choiceIndex == 1) {
					this.choiceIndexNow = this.choiceIndex
				}
			},
			listOrderNeedsWorkOrderLog(id) {
				let quer = {
					search: this.searchText1,
					startTime: null,
					endTime: null,
					workOrderId: id,
					orderBy: 't.createTime DESC'
				}
				// 处理一下时间筛选字段
				if (this.searchTime1 && this.searchTime1.length >= 2) {
					quer.startTime = this.searchTime1[0]
					quer.endTime = this.searchTime1[1]
				} else {
					quer.startTime = null
					quer.endTime = null
				}
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'orderNeedsWorkOrder/listOrderNeedsWorkOrderLog',
					data: JSON.stringify(quer),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.status == 200) {
							this.logList = res.data
						} else {
							this.logList = []
						}
					},
				})
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.quer.deviceState = this.searchState
				this.getData()
			},
			// 重置
			reset() {
				this.loading = true
				this.quer.current = 1
				this.quer.search = ""
				this.searchTime = []


				this.searchText = ''
				this.searchState = null
				this.getData()
			},
			// 获取分组
			listOrderNeedsWorkOrderGroup() {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'orderNeedsWorkOrder/listOrderNeedsWorkOrderGroup',
					data: JSON.stringify({
						groupState: 1
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.status == 200) {
							this.groupList = res.data
						} else {
							this.groupList = []
						}
					},
				})
			},
			// 获取员工列表
			listEmployee() {
				if (!this.searchText) {
					return
				}
				this.$postData("listEmployeeDto", {
					search: this.searchText,
					employeeType: 20,
					state: 1
				}).then(res => {
					if (res.status == 200) {
						this.employeeList = res.data
					} else {
						this.$message.error("查询失败，" + res.msg)
					}
				})
			},
			// 选中员工
			selectEmployee(id) {
				let index = this.getIndex(id, this.employeeList)
				let employee = this.employeeList[index]
				this.choiceItem.employeeNo = employee.no || null
				this.choiceItem.employeeName = employee.realName || ''
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.choiceRecordId = row.id
					}
				} else {
					that.expands = [] // 默认不展开
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true
				this.quer.current = index
				this.getData()
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "设备状态") {
					tips = "设备当前使用状态"
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			// 将字符串转化为数组
			strToArray(val) {
				let arrayString = []
				let array = []
				arrayString = val.split(',')
				arrayString.forEach(it => {
					let value = parseInt(it)
					if (value != null && value != 0) {
						array.push(value)
					}
				})
				return array
			},
			formatDuration(duration, defaultStr) {
				if (!defaultStr) {
					defaultStr = '暂未录入'
				}
				let result = ""
				let sencond = 0
				let min = 0
				let hour = 0
				let day = 0
				// 小于一分钟
				if (duration <= 60) {
					sencond = Math.floor(parseFloat(duration / 1.0))
					if (sencond == 0) {
						result = defaultStr
					} else {
						result = sencond + "秒"
					}
				}

				// 小于二小时
				if (duration > 60 && duration <= 3600) {
					min = Math.floor(parseFloat(duration / 60.0))
					sencond = Math.floor(duration - (min * 60)).toFixed(0)
					if (min == 0) {
						result = defaultStr
					} else {
						result = min + "分钟" + sencond + "秒"
					}
				}
				// 大于二小时 小于一天
				if (duration > 3600 && duration <= 86400) {
					hour = Math.floor(parseFloat(duration / 3600))
					min = parseFloat((duration - (hour * 3600)) / 60).toFixed(0)
					if (min == 0) {
						result = hour + "小时"
					} else {
						result = hour + "小时" + min + "分钟"
					}
				}
				// 大于一天
				else if (duration > 86400) {
					day = Math.floor(parseFloat(duration / 86400))
					hour = parseFloat((duration - (day * 86400)) / 3600).toFixed(0)
					if (hour == 0) {
						result = day + "天"
					} else {
						result = day + "天" + hour + "小时"
					}
				}
				return result
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 直播设备信息下载
			excelDownload() {
				this.$postData("orderNeedsWorkOrderDownload", this.quer, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('线索工单记录下载成功!')
					this.blobExport({
						tablename: "线索工单记录",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.$set(this.choiceItem, "deviceImg", res.data)
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadId = index
				let tips = ''
				if (this.uploadImgType == 0) {
					tips = "上传设备图片，以方便识别"
				}
				this.$message.info(tips)
			},
			// 打开图片预览
			openImg(url) {
				this.imageUrl = url || this.blankImg
				this.imgModal = true
			},
			// 打开视频预览
			openVideo(url) {
				if (url != null && url != '') {
					this.videoUrl = url
				}
				this.videoModal = true
			},
			// 将字符串转化为数组
			strToArray(val) {
				let arrayString = []
				let array = []
				arrayString = val.split(',')
				arrayString.forEach(it => {
					let value = parseInt(it)
					if (value != null && value != 0) {
						array.push(value)
					}
				})
				return array
			},
			openModal(index, index1) {
				if (index == 0) {
					this.detailType = 0
					this.choiceItem = {}
					this.orderNeedsWorkOrderModal = true
				} else if (index == 1) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.orderNeedsWorkOrderModal = true
					this.listOrderNeedsWorkOrderLog(this.choiceItem.id)
				} else if (index == 3) {
					window.open(
						'https://vod.console.aliyun.com/?spm=5176.12672711.categories-n-products.dvod.8f791fa37JVfKI#/media/video/list'
					)
				}
			},
			//打开员工信息详情
			rowClick(id) {

			},
			// 添加直播设备
			insertOrderNeedsWorkOrder() {
				let orderNeedsWorkOrder = this.choiceItem
				if (!orderNeedsWorkOrder.deviceId) {
					this.$message.error('请填写直播设备标识！')
				} else if (!orderNeedsWorkOrder.deviceName) {
					this.$message.error('请填写直播设备名称！')
				} else {
					this.$set(orderNeedsWorkOrder, "creator", localStorage.getItem('account') || 'admin')
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'orderNeedsWorkOrder/insertOrderNeedsWorkOrder',
						data: JSON.stringify(orderNeedsWorkOrder),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.status == 200) {
								this.$message.success('直播设备添加成功!')
								this.orderNeedsWorkOrderModal = false
								this.list.push(res.data)
							} else {
								this.$message.error('直播设备添加失败！' + res.msg)
							}
						},
					})
				}
			},
			// 更新
			updateOrderNeedsWorkOrder(val) {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'orderNeedsWorkOrder/updateOrderNeedsWorkOrder',
					data: JSON.stringify(val),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.status == 200) {
							this.$message.success('线索工单备注成功!')
							this.list[this.choiceItemIndex] = this.choiceItem
						} else {
							this.$message.error('线索工单备注失败！' + res.msg)
						}
					},
				})
			},
			beforeFileUpload() {
				this.loading = true
			},
			// 章节素材文件上传成功
			fileUploadSuccess(res, file) {
				this.loading = false
				if (this.uploadFileType == 0) {
					this.choiceItem.videoUrl = res.data
				}
				this.$message.success('直播设备文件上传成功！')
			},
			// 章节素材文件上传失败
			fileUploadError(err) {
				this.loading = false
				this.$message.error('直播设备文件上传失败！请重试！' + err.msg)
			},
			// 课程章节素材文件上传
			fileUpload(value) {
				this.uploadFileType = value
				this.fileUploadUrl = fileUploadUrl
			},
			beforeImgUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!');
				}
				return isLt2M;
			}
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>