<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row >
          <el-col :span="6">
            <el-form-item label="会员账号">
              <el-input clearable @change="query()" v-model="dto.name"
                  placeholder="会员账号" style="width:220px"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="月份">
              <el-date-picker
                  @change="query()"
                  v-model="dto.month"
                  type="month"
                  format="yyyy-MM"
                  value-format="yyyy/MM"
                  placeholder="选择月">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="已开/未开">
              <el-select v-model="dto.billingState" clearable filterable placeholder="请选择" @change="invoice()">
                <el-option v-for="item in options"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="满意度">
              <el-select v-model="value2" clearable filterable placeholder="请选择" @change="aaa()">
                <el-option v-for="item in options2"
                           :key="item.value2" :label="item.label" :value="item.value2">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
          <el-button v-if="roleId!=='2'" type="info" :loading="loadingExcel"
                     @click="download" icon="el-icon-download">导出
          </el-button>
          <el-button v-if="roleId!=='99'" type="primary" :loading="loadingExcel2"
                     @click="download2" icon="el-icon-download">财务导出
          </el-button>
          <el-button type="warning" :loading="bindLoading1"
                     @click="pushSms" icon="el-icon-position">一键推送
          </el-button>
      </el-form>
      <el-table :data="list" class="table" ref="multipleTable"
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}"
                @selection-change="handleSelectionChange" v-loading="loading">
        <el-table-column type="selection" width="50">
        </el-table-column>
        <el-table-column prop="siteProject.name" width="240" label="会员账号">
        </el-table-column>
        <el-table-column width="145" prop="billNo" label="子订单号">
        </el-table-column>
        <el-table-column width="145" prop="siteProject.no" label="总订单号">
        </el-table-column>
        <el-table-column width="75" prop="order.realTotalAmount" label="合同金额">
        </el-table-column>
        <el-table-column width="110" prop="siteInvoice.sppersonTel" label="手机号">
        </el-table-column>
        <el-table-column width="100" prop="state" label="已开/未开">
          <template slot-scope="scope">
            <el-tag :type="scope.row.state === '发票已开' ? 'primary' : 'success'"
                    disable-transitions>
              {{ scope.row.state }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column width="100" prop="orderInvoice.invoiceNo" label="发票号">
        </el-table-column>
        <el-table-column width="75" prop="satisfaction" label="满意度">
          <template slot-scope="scope">
            <el-tag effect="dark" :type="scope.row.satisfaction===0 ? 'danger' : ''" disable-transitions>
              <span v-if=" scope.row.satisfaction===null">未评价</span>
              <span v-if=" scope.row.satisfaction===0">不满意</span>
              <span v-if=" scope.row.satisfaction===1">一般</span>
              <span v-if=" scope.row.satisfaction===2">满意</span>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column width="180" prop="evaluate" label="评价">
        </el-table-column>
        <el-table-column prop="customerSuggestionsId" label="建议">
        </el-table-column>
        <el-table-column fixed="right" width="65" label="操作">
          <template slot-scope="scope">
                  <el-button size="small " icon="el-icon-thumb"
                      @click="edit(scope.row.projectId)" type="text">查看
                  </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="pageInfo.total"
              @on-change="onChange"
              :current="this.dto.pageNum"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onPageSizeChange"
              :page-size="10"/>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";

export default {
  data() {
    return {
      roleId:localStorage.getItem("roleId"),
      options: [{
        value: '0',
        label: '未发短信'
      }, {
        value: '1',
        label: '已发短信'
      }, {
        value: '2',
        label: '客户已确认'
      }, {
        value: '3',
        label: '发票已开'
      }, {
        value: '4',
        label: '未开发票'
      },],
      options2: [{
        value2: '0',
        label: '不满意'
      }, {
        value2: '1',
        label: '一般'
      }, {
        value2: '2',
        label: '满意'
      },],
      value2: '',
      loading: true,
      pageSizeOpts: [10,50,100],
      list: null,
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      loadingExcel: false,
      loadingExcel2: false,
      sppersonTel: [],
      bindLoading1: false,
      dto: {
        name: null,
        push: null,
        state: null,
        month: null,
        pageSize: 10,
        pageNum: 1,
        satisfaction: null,
        billingState: null,
      },
    };
  },
  created() {
    this.dto.month = moment(new Date()).format('YYYY/MM')
    this.getData();
  },
  computed: {},
  methods: {
    edit(id) {
      let url = window.location.href.split("/")[0];
      window.open(url + "/siteDetails?id=" + id + "&tabName=first")
    },
    aaa() {
      if (this.value2 === '0') {
        this.dto.satisfaction = 0;
        this.getData();
      } else if (this.value2 === '1') {
        this.dto.satisfaction = 1;
        this.getData();
      } else if (this.value2 === '2') {
        this.dto.satisfaction = 2;
        this.getData();
      } else {
        this.getData();
      }
    },
    invoice() {
      this.loading = true;
      this.dto.pageNum = 1;
      this.getData();
    },
    download2(){
      this.loadingExcel2 = true;
      this.$postData("siteOrder_download2", this.dto, {responseType: "arraybuffer"}).then(res => {
        this.loadingExcel2 = false;
        this.blobExport({tablename: "开票信息", res: res});
      })
    },
    download() {
      this.loadingExcel = true;
      this.$postData("siteOrder_download", this.dto, {
        responseType: "arraybuffer"
      }).then(res => {
        this.loadingExcel = false;
        this.blobExport({
          tablename: "开票信息导出",
          res: res
        });
      })
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
    query() {
      this.loading = true;
      this.dto.pageNum = 1;
      this.getData();
    },
    getData() {
      let _this = this;
      this.$postData("siteOrder_selectOrderList", this.dto).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.list.forEach(function (e) {
            if (e.orderInvoice !== null) {
              e.state = "发票已开"
            } else if (e.state) {
              e.state = "客户已确认"
            } else if (e.push) {
              e.state = "已发短信"
            } else {
              e.state = "未发短信"
            }
            if (e.customerSuggestionsId !== null) {
              _this.$getData("selectByIds", {ids: e.customerSuggestionsId}).then(res => {
                if (res.status === 200) {
                  let aa = [];
                  res.data.forEach(function (e) {
                    aa.push(e.proposalName);
                  })
                  e.customerSuggestionsId = aa.toString();
                }
              })
            }
          })
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total;
          this.dto.push = null;
          this.dto.state = null;
          this.dto.satisfaction = null;
        }
      });
    },
    handleSelectionChange(val) {
      var newList = []
      val.forEach(item => {
          let siteOrders={
            projectId:item.projectId,
            billNo:item.billNo,
          }
          newList.push(siteOrders)
      })
      this.sppersonTel = newList;
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dto.pageSize = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dto.pageNum = index;
      this.getData();
    },
    pushSms() {
      if (this.sppersonTel.length < 1) {
        this.$message.error("请选择要发送短信的项目");
      } else {
        this.$postData("siteOrder_pushSms",  this.sppersonTel).then(res => {
          this.bindLoading1 = false;
          if (res.status === 200) {
            if(null===res.data){
              this.$message({message: '推送成功', type: 'success'});
            }else {
              this.$notify({
                title: '以下会员推送短信失败',
                message: res.data,
                duration: 0
              });
            }
          } else {
            this.$message.error(res.msg);
          }
        })
      }
    },
  },
};
</script>

<style scoped>
.table {
  width: 100%;
  font-size: 13px;
}
</style>
