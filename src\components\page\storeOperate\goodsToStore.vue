<template>
  <div>
    <div class="handle-box">
      <el-form ref="form" :model="pageInfo" label-width="80px">
        <el-row>
          <el-col :span="5">
            <div v-show="this.roleId == 66?false:true">
            <el-form-item label="选择门店">
              <el-select ref="storeNameSel"  filterable v-model="quer.storeId" placeholder="请选择">
                <el-option
                    v-for="(item, index) in storeList"
                    :key="index"
                    :label="item.text"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            </div>
          </el-col>
          <el-col :span="5">
            <el-form-item label="商品名称">
              <el-input v-model="quer.goodsName" placeholder="商品名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-button v-show="chukuButton"  type="primary" @click="drawer1 = true,getStoreOut()" v-if="this.quer.storeId !=null"
                       style="margin-left: 40px;float: right">商品出库
            </el-button>
            <el-button v-show="rukuButton"  type="primary" v-if="this.quer.storeId !=null"
                       style="margin-left: 60px;float: right" @click="drawer2 = true,getStoreIn()">商品入库
            </el-button>
            <el-button v-show="exportButton"  type="success"
                       style="margin-left: 60px;float: right" :loading="loadingExcel" @click="getExport2()">所有门店明细导出
            </el-button>
            <el-button type="primary" @click="query()" icon="el-icon-search"
                       style="margin-left: 20px;float: right">搜索
            </el-button>

          </el-col>
        </el-row>

      </el-form>
    </div>
    <el-table :data="list" height="500px" v-loading="loading"
              :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
      <el-table-column
          prop="goodsName"
          width="500"
          label="商品信息">
        <template slot-scope="scope">
          <span>
            {{ scope.row.goodsName }}
            <span v-if="scope.row.attribute6 !=null && scope.row.attribute6 !=''">
              ({{ scope.row.attribute6 }})</span>
          </span>
          <br>
          <span style="color: orange">
           售价:￥{{ scope.row.sellPrice }}
          </span>
        </template>
      </el-table-column>
<!--      <el-table-column-->
<!--          width="110"-->
<!--          prop="allStock"-->
<!--          label="历史总库存"-->
<!--      ></el-table-column>-->
      <el-table-column
          width="110"
          prop="stock"
          label="可销售库存">
      </el-table-column>
      <el-table-column
          width="160"
          prop="occupyStock"
          label="领用但未核销数量">
        <template slot-scope="scope">
          <span v-if="scope.row.occupyStock !== 0" style="color: red">
            {{ scope.row.occupyStock }}
          </span>
          <span v-else-if="scope.row.occupyStock == 0">
            {{ scope.row.occupyStock }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
          label="操作">
        <template slot-scope="scope">
          <el-button @click="drawer = true,getStockInfo(scope.row.goodsId)" style="float: right">出入库明细</el-button>
        </template>

      </el-table-column>

    </el-table>
    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChange"
            :current="this.dto.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="10"/>
    </div>


    <el-drawer
        title="出入库明细"
        :visible.sync="drawer">
      <el-container>
        <el-header>
          <el-radio-group v-model="radio1" style="top: 200px" @change="changeState">
            <!--            <el-radio-button :label="0">所有</el-radio-button>-->
            <el-radio-button :label="2">出库</el-radio-button>
            <el-radio-button :label="1">入库</el-radio-button>
          </el-radio-group>
        </el-header>
        <el-main>
          <el-card v-for="(item , i) in stockList" class="box-card" :key="i">
            <div>操作物品：{{ item.goodsName }}</div>
            <div>操作时间：{{ item.creatTime }}</div>
            <div v-if="radio1 === 2">
              出库状态：
              <span v-if="item.state === 1">待出库</span>
              <span v-if="item.state === 2">已出库</span>
              <span v-if="item.state === 3">待核销</span>
              <span v-if="item.state === 4">已核销</span>
            </div>
            <div v-if="item.sellType ===0">销售类别：购买核销</div>
            <div v-if="item.sellType ===1">销售类别：领用核销</div>
            <div>操作人：{{ item.realName }}</div>
            <div>数量：{{ item.number }}</div>
            <div v-if="item.remark !=null">备注：{{item.remark}}</div>
          </el-card>
        </el-main>
      </el-container>
    </el-drawer>


    <el-drawer
        title="商品出库"
        :visible.sync="drawer1"
        size="70%">

      <el-form ref="form" :model="formChuKu" label-width="80px">
        <el-form-item label="出库门店">
          <el-col :span="9">
            <el-input v-model="this.storeName" :disabled="true"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="门店id">
          <el-col :span="9">
            <el-input v-model="this.quer.storeId" :disabled="true"></el-input>
          </el-col>
        </el-form-item>
        <el-alert title="只有出库数量大于0才表示领用" type="warning" :closable="false"> </el-alert>
        <el-alert title="购买核销(银豹核销)时，必须把银豹订单填写在备注里，并且核销类型选择购买核销。否则会同步失败！" type="warning" :closable="false"> </el-alert>
        <el-alert title="如果不选核销类型，那么默认会归类为领用核销" type="warning" :closable="false"> </el-alert>
        <el-form-item label="出库商品" ></el-form-item>
        <el-table
            :data="chukuList"
            style="width: 100%">
          <el-table-column
              prop="goodsName"
              label="商品"
              width="180">
          </el-table-column>
          <el-table-column
              label="数量"
              width="180">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.paramNumber" :min="0" :max="scope.row.stock" ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column
              label="当前剩余库存"
              prop="stock"
              width="60">
          </el-table-column>
          <el-table-column
              label="订单号(选填)"
              prop="billNo"
              width="180">
            <template slot-scope="scope">
              <el-input v-model="scope.row.billNo"></el-input>
            </template>
          </el-table-column>
          <el-table-column
              label="备注"
              prop="remark"
              width="180">
            <template slot-scope="scope">
              <el-input v-model="scope.row.remark"></el-input>
            </template>
          </el-table-column>
          <el-table-column
              label="核销类型"
              width="180">
            <template slot-scope="scope">
              <el-select v-model="scope.row.selltype">
                <el-option key="1" label="领用核销" value="1"> </el-option>
                <el-option key="0" label="购买核销" value="0"> </el-option>
              </el-select>
            </template>
          </el-table-column>

        </el-table>
<!--        <el-form-item>-->
<!--          <div v-for="(item,i) in list">-->
<!--            <el-row>-->
<!--              <el-col :span="9">-->
<!--                <span v-model="item.goodsId">{{item.goodsName}}</span>-->
<!--              </el-col>-->
<!--              <el-col :span="4">-->
<!--                <el-input v-model="item.value" placeholder="数量"></el-input>-->
<!--              </el-col>-->
<!--            </el-row>-->
<!--            <el-divider></el-divider>-->
<!--          </div>-->
<!--        </el-form-item>-->
      </el-form>
      <el-button type="primary" @click="submitChuKu()"
                 style="margin-left: 20px; margin-top: 20px;">提交</el-button>
    </el-drawer>

    <el-drawer
        title="商品入库"
        :visible.sync="drawer2"
        size="40%">
      <el-form ref="form" :model="formRuKu" label-width="80px">
       <el-form-item label="商品分类">
         <el-select v-model="formRuKu.selectType" @change="selectTyped">
           <el-option
               v-for="item in collectType"
               :key="item.id"
               :label="item.name"
               :value="item.uid">
           </el-option>
         </el-select>
       </el-form-item>
        <el-table
            :data="collectTypeDetail"
            style="width: 100%">
          <el-table-column
              prop="name"
              label="商品名称"
              width="180">
          </el-table-column>
          <el-table-column
              prop="attribute6"
              label="商品规格"
              width="180">
          </el-table-column>
          <el-table-column
              label="入库数量"
              width="180">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.paramNumber" :min="0" :max="scope.row.stock" ></el-input-number>
            </template>
          </el-table-column>
        </el-table>

      </el-form>
      <el-button type="primary" @click="submitRuKu()"
                 style="margin-left: 20px; margin-top: 20px;">提交</el-button>
    </el-drawer>

  </div>
</template>

<script>
import Vue from 'vue';

export default {
  name: "goodsToStore",

  data() {
    return {
      list: null,
      loading: false,
      storeList: [],
      stockList: [],
      chukuList:[], //出库集合
      ruKuList:[], //入库集合
      collectType:[],//物料的部门分类
      collectTypeDetail:[],//物料分类下的全部物品
      drawer: false, //出入库明细
      drawer1: false, //出库
      drawer2: false, //入库
      storeName:null,
      radio1: 1,
      pageSizeOpts: [20, 40, 60],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      quer: {
        goodsName: null,
        storeId: null,
        size: 20,
        current: 1,
        goodsId: null,
        state: null,
      },
      dto: [],
      formChuKu:{},
      chukuButton:false,
      rukuButton:false,
      exportButton:false,
      loadingExcel:false,
      formRuKu:{
        selectType:null,
      },
      roleId:localStorage.getItem("roleId"),
      // rowData: null,
      // journalInfoLoading: true,
      // journalList: null,
      // journalInfoSizeOpts: [20, 40, 60],
      // journalInfo: {total: 10, size: 10, current: 1, pages: 1},
      // journalInfoDrawer: false,

    }
  },
  created() {

    // this.getData();
    this.getStoreInfo();
    this.getRoleShow();

  },
  methods: {

    getExport2(){
    this.loadingExcel = true;
    this.$postData("exportCollectStore", this.quer, {
      responseType: "arraybuffer"
    }).then(res => {
      this.loadingExcel = false;
      this.blobExport({
        tablename: "物料统计",
        res: res
      });
    })
  },
  blobExport({tablename, res}) {
    const aLink = document.createElement("a");
    let blob = new Blob([res], {type: "application/vnd.ms-excel"});
    aLink.href = URL.createObjectURL(blob);
    aLink.download = tablename + ".xlsx";
    document.body.appendChild(aLink);
    aLink.click();
    document.body.removeChild(aLink);
  },
    getRoleShow(){
      let storeId = localStorage.getItem("storeId");
      console.info(this.roleId)
      if (this.roleId == 9 || this.roleId == 1) {
        this.chukuButton = true;
        this.rukuButton = true;
        this.exportButton = true;
      } else{
        this.quer.storeId = storeId;
        this.getData();
      }
    },
    getData() {
      this.$postData("selectGoodsToStore", this.quer).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    query() {
      this.storeName = this.$refs.storeNameSel.selectedLabel
      this.loading = true;
      this.quer.current = 1;
      console.info(this.storeName)
      this.getData();
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.quer.size = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.quer.current = index;
      this.getData();
    },

    getStoreInfo() {
      this.$getData("getAllStore", {}, {}).then(res => {
        if (res.status === 200) {
          this.storeList = res.data;
        }
      });
    },

    getStockInfo(goodsId) {
      if (goodsId !== null && goodsId !== undefined) {
        this.quer.goodsId = goodsId;
      }
      this.quer.state = this.radio1;
      this.$postData("getStockInfo", this.quer, {}).then(res => {
        if (res.status == 200) {
          this.stockList = res.data;
        }
      });
    },
    changeState(val) {
      this.quer.state = val;
      this.getStockInfo(null);
    },
    getStoreOut(){
      this.quer.size = 500;
      this.getChuKuData();

    },
    getChuKuData() {
      this.quer.goodsId = null;
      this.$postData("selectGoodsToStore", this.quer).then(res => {
        if (res.status === 200) {
          this.chukuList = res.data.records;
        }
      })
    },

    submitChuKu(){

      let p = [];
      for (let i=0;i <this.chukuList.length; i++){
        if (this.chukuList[i].paramNumber > 0){
          p.push(this.chukuList[i])
        }
      }
      let param = {
        "inOutStockParam":p,
        "employeeId":localStorage.getItem("id"),
      }
      this.$postData("commitStockByChuKu", param).then(res => {
        if (res.status === 200){
          this.$message({
            message: '申请成功',
            type: 'success'
          });
        } else {
          this.$message.error(res.data);
        }
      })
      this.drawer1 = false;
      this.getData();
    },
    getStoreIn(){
      this.$getData("selectAllCollect", null).then(res => {
        if (res.code === 0) {
          this.collectType = res.data;
        }
      })


    },

    submitRuKu(){
      let param = {
        "inStockParam":this.collectTypeDetail,
        "employeeId":localStorage.getItem("id"),
        "storeId": this.quer.storeId,
      }

      console.info(JSON.stringify(this.collectTypeDetail))
      this.$postData("commitStockByRuKu", param).then(res => {
        if (res.status === 200){
          this.$message({
            message: '入库成功',
            type: 'success'
          });
        } else {
          this.$message.error(res.data);
        }
      })
      this.drawer2 = false;
      this.getData();
    },
    selectTyped(val){
      let name ={
        "categoryUid":val,
      }
      this.$getData("selectAllCollectType", name).then(res => {
          this.collectTypeDetail = res.data;
        this.collectTypeDetail.forEach(e =>{
          e.paramNumber = 0;
        })
      })
    }


  },
}
</script>

<style scoped>
.handle-box {
  /*margin-bottom: 10px;*/
  font-weight: bold !important;
}

table {
  border-collapse: collapse; /*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
  border-left: #C8B9AE solid 1px;
  border-top: #C8B9AE solid 1px;
}

table td {
  border-right: #C8B9AE solid 1px;
  border-bottom: #C8B9AE solid 1px;
}

th {
  background: #eee;
  font-weight: normal;
}

tr {
  background: #fff;
}

tr:hover {
  background: #cc0;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

>>> .el-upload--text {
  width: 200px;
  height: 150px;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
