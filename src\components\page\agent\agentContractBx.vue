<template>
  <div class="table">
    <div class="container">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-date-picker
              :unlink-panels="true"
              v-model="model.time"
              type="datetimerange"
              start-placeholder="操作日期开始日期"
              end-placeholder="操作日期结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00']">
          </el-date-picker>

        </el-col>
        <el-col :span="4">
          <el-input v-model="model.employeeName" placeholder="保姆姓名"></el-input>
        </el-col>
        <el-col :span="4">
          <el-input v-model="model.employeeCarId" placeholder="身份证"></el-input>
        </el-col>
        <el-col :span="2">
          <el-button type="primary" @click="query()">搜索</el-button>
        </el-col>
        <el-col :span="2">
          <el-button type="primary" @click="inputInsure()">上险/下险</el-button>
        </el-col>
        <el-col :span="2">
          <el-button type="success"  :loading="loadingExcel" icon="el-icon-download" @click="download" >导出</el-button>
        </el-col>
<!--        <el-col :span="2">-->
<!--          <el-button type="warning" icon="el-icon-upload2" @click="isModal1=true">批量导入-->
<!--          </el-button>-->
<!--        </el-col>-->

      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-date-picker
              :unlink-panels="true"
              v-model="contractTime"
              type="datetimerange"
              start-placeholder="合同结束时间区间"
              end-placeholder="合同结束时间区间"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00']">
          </el-date-picker>
        </el-col>
        <el-col :span="4">
          <el-input v-model="model.id" placeholder="合同id"></el-input>
        </el-col>
        <el-col :span="4">
          <el-switch
              v-model="model.quchong"
              active-text="去重"
              inactive-text="不去重"></el-switch>
        </el-col>
      </el-row>
      <div style="height: 10px"/>
      <el-radio-group v-model="model.status" @change="query">
        <el-radio-button :label='null'>上户</el-radio-button>
        <el-radio-button label=99>下户</el-radio-button>
      </el-radio-group>


      <div class="handle-box">
        <el-table
            :data="list"
            border
            style="width: 100%"
            v-loading="loading"
            @selection-change="handleSelectionChange">
          <el-table-column
              type="selection"
              width="55">
          </el-table-column>
          <el-table-column
              prop="insuranceNumber"
              label="保险号"
              width="80">
          </el-table-column>
          <el-table-column
              prop="inputSuranceTime"
              label="填写日期"
              width="150">

          </el-table-column>
          <el-table-column
              prop="agentName"
              label="经纪人"
              width="100">
          </el-table-column>

          <el-table-column
              prop="semployeeName"
              label="保姆名称"
              width="100">
            <template slot-scope="scope">
              <span
                  v-if="model.status!=99">{{ scope.row.semployeeName == null ? scope.row.employeeName : scope.row.semployeeName }}</span>
              <span v-if="model.status==99">{{ scope.row.status == 99 ? scope.row.semployeeName == null ? scope.row.employeeName : scope.row.semployeeName : scope.row.oldEmployeeName }}</span>
            </template>
          </el-table-column>

          <el-table-column
              label="保姆身份证"
              prop="employeeCarId"
              width="180">
            <template slot-scope="scope">
              <span
                  v-if="model.status!=99">{{ scope.row.semployeeCarId == null ? scope.row.employeeCarId : scope.row.semployeeCarId }}</span>
              <span
                  v-if="model.status==99">{{ scope.row.status == 99 ? scope.row.semployeeCarId == null ? scope.row.employeeCarId : scope.row.semployeeCarId : scope.row.oldEmployeeCarId }}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="类型"
              prop="employeeCarId"
              width="100">
            <template slot-scope="scope">
              {{ scope.row.status == 99 ? '作废' : scope.row.num == null ? '合同' : '更换' }}
            </template>
          </el-table-column>
          <el-table-column
              label="合同状态">
            <template slot-scope="scope">
              <span v-if="scope.row.status == 0">暂存</span>
              <span v-if="scope.row.status == 3">生效中</span>
              <span v-if="scope.row.status == 1">补签单</span>
              <span v-if="scope.row.status == 2">已完成</span>
              <span v-if="scope.row.status == 99">作废单</span>
              <span v-if="scope.row.status == 100">暂存下户</span>
            </template>
          </el-table-column>
          <el-table-column
              prop="no"
              label="合同编号">
            <template slot-scope="scope">
              <a :href = url(scope.row.id)>{{scope.row.no}}</a>
            </template>
          </el-table-column>
          <el-table-column
              prop="inPay"
              label="合同费用"
              width="120">
          </el-table-column>
          <el-table-column
              prop="createDate"
              label="操作日期"
              width="140">
            <template slot-scope="scope">
              {{ scope.row.status == 99 ? scope.row.updateDate : scope.row.screateDate == null ? scope.row.createDate : scope.row.screateDate }}
            </template>
          </el-table-column>
          <el-table-column
              prop="serviceStarDate"
              label="合同开始日期"
              width="140">
          </el-table-column>
          <el-table-column
              prop="serviceEndDate"
              label="合同结束日期"
              width="140">
          </el-table-column>
          <el-table-column
              prop="zuizaoContractTime"
              label="最早合同开始时间"
              width="140">
          </el-table-column>
          <el-table-column
              prop="zuiwanContractTime"
              label="最晚合同结束时间"
              width="140">
          </el-table-column>
        </el-table>
        <div class="pagination">
          <Page :total="pageInfo.total"
                @on-change="onChange"
                :show-total="true"
                :show-sizer="true"
                :page-size-opts="pageSizeOpts"
                @on-page-size-change="onPageSizeChange"
                :page-size="pageInfo.size"/>
        </div>

      </div>
    </div>

    <el-dialog
        title="请输入保险号"
        :visible.sync="centerDialogVisible"
        width="30%"
        center>
      <el-input v-model="insuranceNumber" placeholder="请输入保险号"></el-input>
      <span slot="footer" class="dialog-footer">
    <el-button @click="closeOpen('close')" >取 消</el-button>
    <el-button type="primary" @click="closeOpen('confirm')">确 定</el-button>
    </span>
    </el-dialog>



    <Modal v-model="isModal1" class="Modal" :width="screenWidth" title="批量导入"
           :mask-closable="false"
           @on-cancel="getData()">
      <div class="addBody">
        <agent-contract-bx-info-excel v-if="isModal1" @init-choose="initChooseProject"
                             @close-modal="closeCurrModal"></agent-contract-bx-info-excel>
      </div>
      <div slot="footer">
      </div>
    </Modal>
  </div>
</template>


<script>
import agentContractBxInfoExcel from '@/components/page/agent/agentContractBxInfoExcel.vue'
export default {
  name: "agentContractBx",
  data() {
    return {
      list: [],
      pageSizeOpts: [10, 20, 30, 50, 100],
      centerDialogVisible: false,
      multipleSelection: [],
      loading:true,
      isModal1:false,
      loadingExcel:false,
      screenWidth: "35%",
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      insuranceNumber:null,
      contractTime:[],
      model: {
        quchong:false,
        time: [],
        id:null,
        employeeName: null,
        employeeCarId: null,
        startDate: null,
        endDate: null,
        contractStartDate:null,
        contractEndDate:null,
        current: 1,
        size: 10,
        status: null,
      },

    }
  },
  components: {
    "agentContractBxInfoExcel": agentContractBxInfoExcel,
  },
  created() {

    this.initTime();

  },
  methods: {
    closeCurrModal(data) {
      this.isModal1 = false;
    },
    initChooseProject(data) {
      this.closeCurrModal();
      this.query();
    },
    url(id) {
      return "https://agent.xiaoyujia.com/upbaomu/contractInfo/"+id;
    },
    download(){

      this.loadingExcel = true;
      this.$postData("downloadContractBx", this.model, {
        responseType: "arraybuffer"
      }).then(res => {
        this.blobExport({
          tablename: "保险通知",
          res: res
        });
      })
      this.loadingExcel = false;
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
    closeOpen(val){

      if (val === 'close'){
       this.insuranceNumber = null;
       this.centerDialogVisible = false;
      } else {
        if (this.insuranceNumber == null || this.insuranceNumber == ''){
         return  this.$message.error("不能为空");
        } else {
          this.centerDialogVisible = false;
          this.updateInsure();
        }

      }
    },
    updateInsure(){

      let param = [];
      let data = JSON.parse(this.multipleSelection);
      for (let i=0; i<data.length; i++){
        let list = {};
        list.id = data[i].id;
        list.insuranceNumber = this.insuranceNumber;
        list.contractId = data[i].contractId;
        param.push(list);
      }
      console.log(param)
      this.$postData("updateContractBxPage", param, {}).then(res => {
        if (res.status == 200) {
          this.$message.success("更新成功");
          this.query();
        } else {
          this.$message.error("更新失败，" + res.msg);
        }
      })
    },
    inputInsure(){
      if ( this.multipleSelection.length <= 0){
        return this.$message({
          message: '请选择人员',
          type: 'warning'
        });
      } else {
        this.centerDialogVisible = true;
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = JSON.stringify(val);
    },
    initTime() {
      // let y = new Date().getFullYear(),
      //     m = new Date().getMonth() + 1,
      //     d = new Date().getDate();
      // this.model.time[1] =
      //     y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d) + " 23:59:59";
      // this.model.time[0] =
      //     y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d) + " 00:00:00";
      this.query()
    },
    query() {
      this.model.current = 1
      console.log(this.model.time)
      if (this.model.time != null) {
        this.model.startDate = this.model.time[0]
        this.model.endDate = this.model.time[1]
      } else {
        this.model.startDate = null;
        this.model.endDate = null;
      }
      if (this.contractTime != null) {
        this.model.contractStartDate = this.contractTime[0]
        this.model.contractEndDate = this.contractTime[1]
      } else {
        this.model.contractStartDate = null;
        this.model.contractEndDate = null;
      }
      if (this.model.quchong === true && this.model.startDate == null){
       return  this.$message.error("数据量庞大，请先选择操作日期");
      }
      this.getData();
    },
    getData() {
      this.loading = true;
      this.$postData("contractBxPage", this.model, {}).then(res => {
        if (res.status == 200) {

          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
          this.loading = false;
        } else {
          this.$message.error("查询失败，" + res.msg);
          this.loading = false;
        }
      })

    },
    // 页码大小
    onPageSizeChange(size) {
      console.log(size)
      this.model.size = size;
      this.getData();
    },
    // 跳转页码

    onChange(index) {
      console.log(index)
      this.model.current = index;
      this.getData();
    },
  }

}
</script>

<style scoped>

</style>
