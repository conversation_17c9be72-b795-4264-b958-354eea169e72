<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="9">
            <el-form-item label="结算时间">
              <el-date-picker v-model="days" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item  label="所属门店" >
              <el-select  filterable v-model="form.storeId" clearable placeholder="请选择所属门店">
                <el-option v-for="item in storeOptions"
                           :key="item.id" :label="item.storeName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item  label="订单类型">
              <el-select style="width:200px" v-model="form.orderType" clearable placeholder="请选择订单类型">
                <el-option v-for="item in options"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="9">
            <el-form-item label="订单编号">
              <el-input
                  clearable
                  v-model="form.billNo"
                  placeholder="请输入订单编号"
                  style="width:350px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
            <el-button type="info"
                       style="margin-bottom: 15px"
                       @click="exportAllContractStoreOrder"
                       icon="el-icon-download">导出
            </el-button>
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 20px">
          <el-col :span="4">
            <div style="font-size: 15px;font-weight: bold;color: red">累计订单金额：￥{{sumAmount}}</div>
          </el-col>
          <el-col :span="4">
            <div style="font-size: 15px;font-weight: bold;color: red">累计三嫂订单金额：￥{{sanSaoSumAmount}}</div>
          </el-col>
          <el-col :span="4">
            <div style="font-size: 15px;font-weight: bold;color: red">累计Abc订单金额：￥{{abcSumAmount}}</div>
          </el-col>
          <el-col :span="4">
            <div style="font-size: 15px;font-weight: bold;color: red">累计保险扣费：￥{{insuranceSumAmount}}</div>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="收入详情">
          <el-table :data="logList" v-loading="loading" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column prop="storeName" label="门店名称" width="180"></el-table-column>
            <el-table-column prop="paySettlementTime" label="结算时间" width="180"></el-table-column>
            <el-table-column prop="orderType" label="订单类型" width="80">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.orderType===1" type="success">标准单</el-tag>
                <el-tag v-if="scope.row.orderType===2" type="primary">三嫂单</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="billNo" label="订单编号" width="160"></el-table-column>
            <el-table-column prop="sourceBillNo" label="集团单号" width="140"></el-table-column>
            <el-table-column prop="productName" label="服务项目" width="120"></el-table-column>
            <el-table-column prop="memberName" label="客户名称" width="80"></el-table-column>
            <el-table-column prop="phone" label="客户手机号" width="120"></el-table-column>
            <el-table-column prop="memberAdress" label="服务地址" width="180"></el-table-column>
            <el-table-column prop="paidInAmount" label="服务人员" width="180">
              <template slot-scope="scope">
                <span>{{scope.row.serviceName?scope.row.serviceName+'（'+scope.row.serviceNo+'）':'无'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="clueDeduction" label="开发人" width="180">
              <template slot-scope="scope">
                <span>{{scope.row.realName?scope.row.realName+'（'+scope.row.employeeNo+'）':'无'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="clueDeduction" label="经纪人" width="180">
              <template slot-scope="scope">
                <span>{{scope.row.orderType==2&&scope.row.agentName?scope.row.agentName+'（'+scope.row.agentNo+'）':'无'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="订单金额" width="80"></el-table-column>
            <el-table-column prop="contractNo" label="合同编号" width="120"></el-table-column>
            <el-table-column prop="servicePay" label="合同薪资" width="80"></el-table-column>
            <el-table-column prop="monthDiff" label="服务月数" width="80"></el-table-column>
            <el-table-column prop="proportion" label="收费比例" width="80"></el-table-column>
            <el-table-column prop="insuranceAmount" label="保险费用" width="80"></el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-link :href="hrefUrl" target="_blank" id="elLink"/>


  </div>
</template>

<script>
import moment from "moment";
import log from "echarts/src/scale/Log";
export default {
  name: "register",
  data() {
    return {
      sumAmount: 0.00,
      sanSaoSumAmount: 0.00,
      abcSumAmount: 0.00,
      insuranceSumAmount: 0.00,
      logList: [],
      loading: true,
      roleId: localStorage.getItem('roleId'),
      hrefUrl: "",
      blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
      dialog: false,
      storeId: null,
      dialogVisible: false,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      days: [],
      form: {
        orderType: '',
        startTime: '',
        endTime: '',
        storeName: '',
        roleId: localStorage.getItem('roleId'),
        billNo: '',
        current: 1,
        size: 10
      },
      options: [{
        value:  '1',
        label: '标准单'
      },{
        value:  '2',
        label: '三嫂单'
      }],
      storeOptions: [],
      dzPickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },

  created() {
    this.days.push(moment(new Date()).startOf("month").format("YYYY-MM-DD"))
    this.days.push(moment(new Date()).endOf("month").format("YYYY-MM-DD"))
    this.getData();
    this.getAllContractStoreAmountTotal();
    this.getAllFranchiseStore();
  },
  methods: {
    getAllFranchiseStore(){
      this.$getData("getAllFranchiseStore", {type:5}, {}).then(res => {
        if (res.code == 0) {
          this.storeOptions = res.data
        } else {
          this.$message.error("查询筛选门店失败!");
        }
      })
    },
    exportAllContractStoreOrder(){
      this.form.startTime = '';
      this.form.endTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD") + ' 00:00:00';
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")+ ' 23:59:59'
      }
      this.hrefUrl = "https://biapi.xiaoyujia.com/storeWithdrawal/exportAllContractStoreOrder?startTime="+this.form.startTime+
          "&endTime="+this.form.endTime
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    // 页码大小
    onPageSizeChange(size) {
      this.form.size = size;
      this.getData();
    },
    onChange(index) {
      this.form.current = index;
      this.getData();
    },
    getAllContractStoreAmountTotal(){
      this.form.startTime = '';
      this.form.endTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      this.$getData("getAllContractStoreAmountTotal", this.form, {}).then(res => {
        if (res.code == 0) {
          this.sanSaoSumAmount = res.data.sanSaoSumAmount;
          this.insuranceSumAmount = res.data.insuranceSumAmount;
          this.sumAmount = res.data.sumAmount;
          this.abcSumAmount = res.data.abcSumAmount;
        } else {
          this.$message.error("查询统计失败，" + res.msg);
        }
      })
    },
    getData() {
      this.loading = true;
      this.form.startTime = '';
      this.form.endTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD")  + ' 00:00:00';
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD") + ' 23:59:59'
      }
      this.$getData("getAllContractStoreOrder", this.form, {}).then(res => {
        this.loading = false
        if (res.code == 0) {
          this.logList = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    }
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
