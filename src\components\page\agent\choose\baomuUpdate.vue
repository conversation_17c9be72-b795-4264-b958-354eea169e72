<template style="background-color: #000">

	<el-tabs type="card" :stretch="true">
		<el-tab-pane label="基本信息">
			<Row>
				<Col span="24">
				<el-form ref="dom" :model="dom" :rules="ruleValidate">
					<Row>
						<Col span="6" style="padding: 50px">
						<div style="padding-left: 30%;padding-bottom: 10px">
							<h3>员工头像</h3>
						</div>
						<el-dialog :visible.sync="dialogImg">
							<img width="100%" :src="dom.headPortrait" alt="">
						</el-dialog>
						<el-image @click="dialogImg=true" style="width: 100%; height: 200px;min-width: 150px;"
							:src="dom.headPortrait">
						</el-image>
						<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
							list-type="picture" :on-success="handleAvatarSuccess">
							<el-button v-if="showHandleByRoleId()" size="small" type="primary">更改头像</el-button>
						</el-upload>

						</Col>
						<Col span="8">
						<el-form-item prop="no">
							<div class="label-name">编号:</div>
							<el-input :disabled="true" placeholder="请输入编号" v-model="dom.no" style="width: 70%;"
								:readonly="writeNo">
							</el-input>
						</el-form-item>

						<el-form-item prop="realName">
							<div class="label-name">名称:</div>
							<el-input placeholder="请输入名称" v-model="dom.realName" style="width: 70%;">
							</el-input>
						</el-form-item>

						<el-form-item prop="address">
							<div class="label-name">地址:</div>
							<el-input placeholder="请输入地址" v-model="dom.address" style="width: 70%;">
							</el-input>
						</el-form-item>

						<el-form-item prop="phone">
							<div style="display: flex;">
								<div class="label-name">手机:</div>
								<div style="display: flex;" v-if="!isPhoneGot">
									<span v-if="dom.phone">{{dom.phone.slice(0, 3)}}****{{dom.phone.slice(-4)}}</span>
								</div>
								<el-input placeholder="请输入联系方式" v-model="dom.phone" style="width: 70%;" v-else>
								</el-input>
								<el-button @click="isPhoneGot?isPhoneGot=false:getEmployeePhoneById()" size="small"
									type="primary" style="margin-left: 10px">{{isPhoneGot?'隐藏手机':'查看手机'}}</el-button>
							</div>
						</el-form-item>
						<el-form-item prop="workRemark">
							<div class="label-name">备注信息:</div>
							<el-input placeholder="仅保姆经纪人可见" v-model="dom.workRemark" style="width: 70%; " rows="2"
								type="textarea" :disabled="roleId=='42'">
							</el-input>
						</el-form-item>
						<el-form-item prop="phone" label="介绍人工号:">
							<el-input placeholder="" v-model="dom.introducer" style="width: 60%" :disabled="true">
							</el-input>
						</el-form-item>
						</Col>
						<Col span="8">
						<!--                                <el-form-item   prop="baomuWorkType">-->
						<!--                                    <el-input placeholder="请输入工作方式" v-model="dom.baomuWorkType">-->
						<!--                                        <template slot="prepend">工作方式:</template>-->
						<!--                                    </el-input>-->
						<!--                                </el-form-item >-->
						<el-form-item prop="sites">
							<div class="label-name">所属站点:</div>
							<el-select v-model="dom.siteId" filterable clearable placeholder="请选择"
								@change="changeSite()" style="width: 70%;">
								<el-option v-for="item in allSites" :key="item.value" :label="item.label"
									:value="item.value">
								</el-option>
							</el-select>
						</el-form-item>

						<el-form-item prop="shimingState">
							<div class="label-name">认证状态:</div>
							<el-select v-model="dom.shimingState" clearable placeholder="请选择" style="width: 70%;">
								<el-option v-for="item in shimingStateoptions" :key="item.value" :label="item.label"
									:value="item.value">
								</el-option>
							</el-select>
						</el-form-item>

						<el-form-item prop="state">
							<div class="label-name">员工状态:</div>
							<el-select v-model="dom.state" clearable placeholder="请选择" style="width: 70%;">
								<el-option v-for="item in states" :key="item.value" :label="item.label"
									:value="item.value">
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item prop="baomuWorkType">
							<div class="label-name">工作方式:</div>
							<el-select v-model="baomuWorkType" multiple placeholder="请选择" style="width: 70%;">
								<el-option v-for="item in workTypeOptions" :key="item.value" :label="item.label"
									:value="item.value">
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item>
							<div class="label-name">城市区域:</div>
							<city-choose @init-choose="initChooseProject" :getcity=setCity />
						</el-form-item>

						<el-form-item prop="EntryTime">
							<div class="label-name">入职时间:</div>

							<el-date-picker style="width: 70%;" v-model="dom.entryTime" type="date" disabled
								value-format="yyyy-MM-dd HH:mm:ss" placeholder="入职时间">
							</el-date-picker>

						</el-form-item>
						<el-form-item prop="address">
							<div class="label-name">排序积分:</div>
							<el-input placeholder="请输入积分" type="number" v-model="sortDom.sort" style="width: 70%;">
							</el-input>
						</el-form-item>

						<!--:model="dom"-->
						</Col>
						<Col span="6">
						</Col>
						<Col span="16">
						<div v-html="dom.workRemark" style="background: #ddd;border: 1px solid #ddd;padding: 20px">
						</div>
						</Col>


					</Row>


					<div style="margin-right: 100px;float: right">
						<Button v-if="showHandleByRoleId()" type="primary" @click="save('dom')" :loading="saveOk">
							确定
						</Button>
						<Button v-if="showHandleByRoleId()" @click="chooseThisModel" style="margin-left: 15px">取消
						</Button>
					</div>
				</el-form>
				</Col>


			</Row>
		</el-tab-pane>
		<el-tab-pane label="详细信息" style="padding: 20px">
			<el-form ref="baomuinfo" :model="baomuinfo" :rules="ruleValidate2">
				<el-collapse v-model="activeNames">
					<el-collapse-item title="- 基本信息 -" name="1">
						<Row>
							<Col span="12">
							<el-form-item prop="sex">
								<div class="label-name"><span class="must">*</span>性别:</div>
								<el-select v-model="baomuinfo.sex" clearable placeholder="请选择" style="width: 70%;">
									<el-option v-for="item in sexoptions" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>

							<el-form-item prop="birthTime">
								<div class="label-name"><span class="must">*</span>生日:</div>
								<el-date-picker @change="getSx" style="width: 70%;" v-model="baomuinfo.birthTime"
									type="date" value-format="yyyy-MM-dd HH:mm:ss" placeholder="生日">
								</el-date-picker>
							</el-form-item>

							<el-form-item prop="hometown">
								<div class="label-name"><span class="must">*</span>籍贯:</div>
								<el-input placeholder="请输入籍贯" v-model="baomuinfo.hometown" style="width: 70%;">
								</el-input>
							</el-form-item>
							<el-form-item prop="nation">
								<div class="label-name">民族:</div>
								<el-input placeholder="请输入民族" v-model="baomuinfo.nation" style="width: 70%;">
								</el-input>
							</el-form-item>
							<el-form-item prop="nation">
								<div class="label-name"><span class="must">*</span>身份证:</div>
								<el-input placeholder="请输入身份证" v-model="baomuinfo.idcard" required style="width: 70%;">
								</el-input>
							</el-form-item>

							<el-form-item prop="education">
								<div class="label-name">工作意向:</div>
								<!--                                        {{remarkIds}}-->
								<el-select v-model="remarkIds" multiple placeholder="请选择" style="width: 70%;">
									<el-option-group v-for="group in remarks" :key="group.label" :label="group.label">
										<el-option v-for="item in group.options" :key="item.value" :label="item.label"
											:value="item.value">
										</el-option>
									</el-option-group>
								</el-select>
							</el-form-item>


							</Col>
							<Col span="12">
							<el-form-item prop="education">
								<div class="label-name"><span class="must">*</span>学历:</div>
								<el-select v-model="baomuinfo.education" clearable placeholder="请选择"
									style="width: 70%;">
									<el-option v-for="item in eduoptions" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>

							<el-form-item prop="married">
								<div class="label-name"><span class="must">*</span>婚否:</div>
								<el-select v-model="baomuinfo.married" clearable placeholder="请选择" style="width: 70%;">
									<el-option v-for="item in maroptions" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>

							<el-form-item prop="married">
								<div class="label-name">招聘来源:</div>
								<el-select v-model="baomuinfo2.source" clearable placeholder="请选择" style="width: 70%;">
									<el-option v-for="item in sources" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>
							<el-form-item prop="languages">
								<div class="label-name"><span class="must">*</span>语言:</div>
								<el-select v-model="languages" multiple placeholder="请选择" style="width: 70%;">
									<el-option v-for="item in languagess" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>
							<!--                                    <el-form-item   prop="language" >-->
							<!--                                        <div class="label-name">语言:</div>-->
							<!--                                        <el-select v-model="baomuinfo.language" clearable placeholder="请选择" style="width: 70%;">-->
							<!--                                            <el-option-->
							<!--                                                    v-for="item in lanoptions"-->
							<!--                                                    :key="item.value"-->
							<!--                                                    :label="item.label"-->
							<!--                                                    :value="item.value">-->
							<!--                                            </el-option>-->
							<!--                                        </el-select>-->
							<!--                                    </el-form-item >-->
							<el-form-item prop="birthTime">
								<div class="label-name">身份证过期:</div>
								<el-date-picker style="width: 70%;" v-model="baomuinfo2.idCardTime" type="date"
									value-format="yyyy-MM-dd HH:mm:ss">
								</el-date-picker>
							</el-form-item>
							<el-form-item prop="otherSkills">
								<div class="label-name">其他技能:</div>
								<el-select v-model="otherSkills" multiple placeholder="请选择" style="width: 70%;">
									<el-option v-for="item in otherSkillss" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>

							</Col>
						</Row>

					</el-collapse-item>
					<el-collapse-item title="- 工作信息 -" name="2">

						<Row>
							<Col span="12">
							<el-form-item prop="serverContent">
								<div class="label-name"><span class="must">*</span>服务内容:</div>
								<el-select v-model="serverContent" multiple placeholder="请选择" style="width: 70%;">
									<el-option v-for="item in serverContents" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>
							<el-form-item prop="salary">
								<div class="label-name"><span class="must">*</span>工作年限:</div>
								<el-input placeholder="请输入待遇要求" v-model="baomuinfo.workYear" type="number"
									style="width: 70%;">
									<template slot="append">年</template>
								</el-input>
							</el-form-item>
							<!--                                    <el-form-item   prop="experience" >-->
							<!--                                        <div class="label-name">工作经验:</div>-->
							<!--                                        <el-input placeholder="请输入工作经验" v-model="baomuinfo.experience" style="width: 70%;">-->
							<!--                                        </el-input>-->
							<!--                                    </el-form-item >-->

							</Col>
							<Col span="12">
							<el-form-item prop="baomuWorkType">
								<div class="label-name"><span class="must">*</span>工作类型:</div>
								<el-select v-model="workType" multiple placeholder="请选择" style="width: 70%;">
									<el-option v-for="item in workTypes" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>
							<el-form-item prop="salary">
								<div class="label-name">最低薪资:</div>
								<el-input placeholder="请输入待遇要求" v-model="baomuinfo.minSalary" style="width: 70%;">
								</el-input>
							</el-form-item>
							<el-form-item prop="salary">
								<div class="label-name">最高薪资:</div>
								<el-input placeholder="请输入待遇要求" v-model="baomuinfo.maxSalary" style="width: 70%;">
								</el-input>
							</el-form-item>
							<!--                                    <el-form-item   prop="training" >-->
							<!--                                        <div class="label-name">培训情况:</div>-->
							<!--                                        <el-input placeholder="请输入培训情况" v-model="baomuinfo.training" style="width: 70%;">-->
							<!--                                        </el-input>-->
							<!--                                    </el-form-item >-->
							<!--                                    <el-form-item   prop="banks" >-->
							<!--                                        <div class="label-name">开户银行:</div>-->
							<!--                                        <el-input placeholder="请输入开户银行" v-model="baomuinfo.banks" style="width: 70%;">-->
							<!--                                        </el-input>-->
							<!--                                    </el-form-item >-->
							<!--                                    <el-form-item   prop="openAccount" >-->
							<!--                                        <div class="label-name">开户支行:</div>-->
							<!--                                        <el-input placeholder="请输入开户支行" v-model="baomuinfo.openAccount" style="width: 70%;">-->
							<!--                                        </el-input>-->
							<!--                                    </el-form-item >-->
							<!--                                    <el-form-item   prop="bankCards" >-->
							<!--                                        <div class="label-name">银行卡号:</div>-->
							<!--                                        <el-input placeholder="请输入银行卡号" v-model="baomuinfo.bankCards" style="width: 70%;">-->
							<!--                                        </el-input>-->
							<!--                                    </el-form-item >-->
							</Col>
						</Row>
					</el-collapse-item>
					<el-collapse-item title="- 个人信息 -" name="3">

						<Row>
							<Col span="12">
							<!--                                    <el-form-item   prop="specialty">-->
							<!--                                        <div class="label-name">特长:</div>-->
							<!--                                        <el-input placeholder="请输入特长" v-model="baomuinfo.specialty" style="width: 70%;">-->
							<!--                                        </el-input>-->
							<!--                                    </el-form-item >-->
							<!--                                    <el-form-item   prop="ability" >-->
							<!--                                        <div class="label-name">个人能力:</div>-->
							<!--                                        <el-input placeholder="请输入个人能力" v-model="baomuinfo.ability" style="width: 70%;">-->
							<!--                                        </el-input>-->
							<!--                                    </el-form-item >-->
							<el-form-item prop="religion">
								<div class="label-name">宗教:</div>
								<el-select v-model="baomuinfo2.religion" clearable placeholder="请选择"
									style="width: 70%;">
									<el-option v-for="item in religions" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>
							<el-form-item prop="zodiac">
								<div class="label-name"><span class="must">*</span>属相:</div>
								<el-select v-model="baomuinfo2.zodiac" clearable placeholder="请选择" style="width: 70%;">
									<el-option v-for="item in zodiacs" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>


							<el-form-item prop="introduce">
								<div class="label-name"><span class="must">*</span>文字简介:</div>
								<el-input placeholder="请输入文字简介" v-model="baomuinfo2.introduce" style="width: 70%;">
								</el-input>
							</el-form-item>

							<!--                                    <el-form-item   prop="introduceVoice" >-->
							<!--                                        <div class="label-name">语音简介:</div>-->
							<!--                                        <el-input placeholder="请输入语音简介" v-model="baomuinfo2.introduceVoice" style="width: 70%;">-->
							<!--                                        </el-input>-->
							<!--                                    </el-form-item >-->


							</Col>
							<Col span="12">
							<el-form-item prop="family">
								<div class="label-name">家庭情况:</div>
								<el-input placeholder="请输入家庭情况" v-model="baomuinfo.family" style="width: 70%;">
								</el-input>
							</el-form-item>

							<el-form-item prop="urgent">
								<div class="label-name">紧急联系人:</div>
								<el-input placeholder="请输入紧急联系人" v-model="baomuinfo2.urgent" style="width: 70%;">
								</el-input>
							</el-form-item>
							<el-form-item prop="urgentPhone">
								<div class="label-name">紧急联系电话:</div>
								<el-input placeholder="请输入紧急联系电话" v-model="baomuinfo2.urgentPhone" style="width: 70%;">
								</el-input>
							</el-form-item>
							<el-form-item prop="urgentType">
								<div class="label-name">联系人关系:</div>
								<el-select v-model="baomuinfo2.urgentType" clearable placeholder="请选择"
									style="width: 70%;">
									<el-option v-for="item in urgentTypes" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>


							<el-form-item prop="baomuWorkType">
								<div class="label-name">健康概况:</div>
								<el-select v-model="health" multiple placeholder="请选择" style="width: 70%;">
									<el-option v-for="item in healths" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>

							<!--                                    <el-form-item   prop="introduceVideo" >-->
							<!--                                        <div class="label-name">视频简介:</div>-->
							<!--                                        <el-input placeholder="请输入视频简介" v-model="baomuinfo.introduceVideo" style="width: 70%;">-->
							<!--                                        </el-input>-->
							<!--                                    </el-form-item >-->


							</Col>
						</Row>
					</el-collapse-item>
				</el-collapse>


				<!--                        <Row >-->
				<!--                            <p style="border-top:2px solid #dcdfe6;padding-top: 5px;font-size: 15px"> 认证图片：</p>-->
				<!--                          <span  v-for="item in listImg">-->
				<!--                            <el-image-->
				<!--                                    style="width: 200px; height: 150px;border:1px solid #dcdfe6;padding: 10px;margin: 10px;"-->
				<!--                                    :src="item"-->
				<!--                                    :preview-src-list="listImg">-->
				<!--                            </el-image>-->
				<!--                          </span>-->
				<!--                        </Row>-->


				<div style="margin-right: 100px;float: right">
					<Button v-if="showHandleByRoleId()" type="primary" @click="setinfo('baomuinfo')" :loading="saveOk">
						确定
					</Button>
					<Button v-if="showHandleByRoleId()" @click="chooseThisModel" style="margin-left: 15px">取消</Button>
				</div>
			</el-form>
		</el-tab-pane>
		<!--                <el-tab-pane label="所属经纪人">-->
		<!--                    <center>-->
		<!--                       <el-autocomplete-->
		<!--                                v-model="dom.agentName"-->
		<!--                                :fetch-suggestions="querySearchAsync"-->
		<!--                                placeholder="请输入内容"-->
		<!--                                @select="handleSelect"-->
		<!--                                :trigger-on-focus="false"-->
		<!--                       >-->
		<!--                            <template slot="prepend">所属经纪人:</template>-->
		<!--                            <el-button slot="append" icon="el-icon-delete"  @click="upTo">解除绑定</el-button>-->
		<!--                        </el-autocomplete>-->
		<!--                        <el-button slot="append" icon="el-icon-delete" type="primary" @click="saveTo">更改绑定</el-button>-->


		<!--                    </center>-->
		<!--                </el-tab-pane>-->

	</el-tabs>
</template>

<style>
	{
		background: #409eff;
		color: #fff;
	}
</style>
<script>
	import cityChoose from '@/components/page/agent/minichoose/cityChoose.vue'

	export default {
		props: ['model'],
		data() {
			return {
				roleId: localStorage.getItem("roleId"),
				writeNo: true,
				saveOk: false,
				sortDom: null,
				health: [],
				activeNames: ['1', '2', '3'],
				allSites: [],
				baomuWorkType: [],
				workType: [],
				serverContent: [],
				urgentTypes: [{
					value: '夫妻',
					label: '夫妻'
				}, {
					value: '子女',
					label: '子女'
				}, {
					value: '亲戚',
					label: '亲戚'
				}, {
					value: '朋友',
					label: '朋友'
				}],
				serverContents: [{
					value: '做饭',
					label: '做饭'
				}, {
					value: '做卫生',
					label: '做卫生'
				}, {
					value: '带宝宝（带睡)',
					label: '带宝宝（带睡)'
				}, {
					value: '带宝宝（不带睡)',
					label: '带宝宝（不带睡)'
				}, {
					value: '看护老人',
					label: '看护老人'
				}, {
					value: '看护病人',
					label: '看护病人'
				}, {
					value: '照顾产妇',
					label: '照顾产妇'
				}, {
					value: '陪读师',
					label: '陪读师'
				}],
				zodiacs: [{
					value: '鼠',
					label: '鼠'
				}, {
					value: '牛',
					label: '牛'
				}, {
					value: '虎',
					label: '虎'
				}, {
					value: '兔',
					label: '兔'
				}, {
					value: '龙',
					label: '龙'
				}, {
					value: '蛇',
					label: '蛇'
				}, {
					value: '马',
					label: '马'
				}, {
					value: '羊',
					label: '羊'
				}, {
					value: '猴',
					label: '猴'
				}, {
					value: '鸡',
					label: '鸡'
				}, {
					value: '狗',
					label: '狗'
				}, {
					value: '猪',
					label: '猪'
				}],

				// workTypes: [{
				//     value:'住家',
				//     label: '住家'
				// }, {
				//     value: '不住家',
				//     label: '不住家'
				// }, {
				//     value:'单餐',
				//     label: '单餐'
				// } ],
				//住家、不住家、钟点单餐、育儿嫂、护工、月嫂
				workTypes: [{
					value: '住家',
					label: '住家'
				}, {
					value: '不住家',
					label: '不住家'
				}, {
					value: '钟点单餐',
					label: '钟点单餐'
				}, {
					value: '育儿嫂',
					label: '育儿嫂'
				}, {
					value: '护工',
					label: '护工'
				}, {
					value: '月嫂',
					label: '月嫂'
				}, {
					value: '陪读师',
					label: '陪读师'
				}],
				religions: [{
					value: '佛教',
					label: '佛教'
				}, {
					value: '基督教',
					label: '基督教'
				}, {
					value: '天主教',
					label: '天主教'
				}, {
					value: '道教',
					label: '道教'
				}],
				healths: [{
					value: '健康证',
					label: '健康证'
				}, {
					value: '体检表',
					label: '体检表'
				}],
				states: [{
					value: null,
					label: '全部'
				}, {
					value: 1,
					label: '上架'
				}, {
					value: 2,
					label: '下架'
				}, {
					value: 3,
					label: '离职'
				}, {
					value: 4,
					label: '黑名单'
				}, ],
				otherSkills: [],
				otherSkillss: [{
					value: '玻璃擦',
					label: '玻璃擦'
				}, {
					value: '收纳整理',
					label: '收纳整理'
				}, {
					value: '熨烫',
					label: '熨烫'
				}, {
					value: '电动车',
					label: '电动车'
				}, {
					value: '英语',
					label: '英语'
				}, {
					value: '电脑',
					label: '电脑'
				}, {
					value: '开车',
					label: '开车'
				}, {
					value: '骑电动车',
					label: '骑电动车'
				}, {
					value: '收纳',
					label: '收纳'
				}, ],
				workTypeOptions: [{
						value: '做饭',
						label: '做饭'
					}, {
						value: '打扫',
						label: '打扫'
					}, {
						value: '宝宝带睡',
						label: '宝宝带睡'
					}, {
						value: '宝宝不带睡',
						label: '宝宝不带睡'
					}, {
						value: '纯打扫',
						label: '纯打扫'
					}, {
						value: '照顾老人',
						label: '照顾老人'
					},
					{
						value: '照顾病人',
						label: '照顾病人'
					}, {
						value: '陪读师',
						label: '陪读师'
					}, {
						value: '月嫂',
						label: '月嫂'
					}
				],
				// workTypeOptions:[{
				//     value:'育儿嫂',
				//     label: '育儿嫂'
				// }, {
				//     value: '家务保姆',
				//     label: '家务保姆'
				// }, {
				//     value: '护工',
				//     label: '护工'
				// }, {
				//     value: '保姆',
				//     label: '保姆'
				// }, {
				//     value: '纯打扫',
				//     label: '纯打扫'
				// }, {
				//     value:'纯做饭',
				//     label: '纯做饭'
				// },],

				dialogImg: false,
				setCity: {
					cityId: this.model.cityId,
					areaId: this.model.areaId,
				},
				sexoptions: [{
					value: 1,
					label: '男'
				}, {
					value: 2,
					label: '女'
				}, {
					value: 3,
					label: '未知'
				}],
				eduoptions: [{
					value: 0,
					label: '无'
				}, {
					value: 2,
					label: '无需求'
				}, {
					value: 3,
					label: '小学'
				}, {
					value: 4,
					label: '中学'
				}, {
					value: 5,
					label: '高中'
				}, {
					value: 6,
					label: '大专'
				}, {
					value: 7,
					label: '本科及以上'
				}, {
					value: 8,
					label: '中专'
				}],
				maroptions: [{
					value: 1,
					label: '已婚未育'
				}, {
					value: 0,
					label: '未婚未育'
				}, {
					value: 2,
					label: '已婚已育'
				}, {
					value: 3,
					label: '未婚已孕'
				}, {
					value: 4,
					label: '离异'
				}, {
					value: 5,
					label: '其它'
				}],
				sources: [{
					value: '朋友介绍',
					label: '朋友介绍'
				}, {
					value: '经纪人介绍',
					label: '经纪人介绍'
				}, {
					value: '其它部门',
					label: '其它部门'
				}, {
					value: '公交广告',
					label: '公交广告'
				}, {
					value: '网络',
					label: '网络'
				}, {
					value: '招聘传单',
					label: '招聘传单'
				}, {
					value: '人才市场',
					label: '人才市场'
				}, {
					value: '招聘会',
					label: '招聘会'
				}],
				languages: null,
				languagess: [{
					value: '普通话',
					label: '普通话'
				}, {
					value: '闽南话',
					label: '闽南话'
				}, {
					value: '英语',
					label: '英语'
				}, {
					value: '粤语',
					label: '粤语'
				}, {
					value: '客家话',
					label: '客家话'
				}],
				lanoptions: [{
					value: 1,
					label: '普通话'
				}, {
					value: 2,
					label: '闽南话'
				}, {
					value: 0,
					label: '闽南语,普通话'
				}, {
					value: 3,
					label: '其他'
				}],

				shimingStateoptions: [{
					value: 0,
					label: '未通过'
				}, {
					value: 1,
					label: '已通过'
				}, ],
				list: [],
				baomuinfo: {},
				baomuinfo2: {},
				addTo: {
					employeeId: null,
					baomuId: null,
					isDev: 1
				},
				agentitem: {
					value: null,
					name: null
				},
				restaurants: [],
				dom: this.model,
				formItem: {
					No: null,
					RealName: null,
					Phone: null,
					Address: null,
				},
				baomuModel: {
					RealName: null,
					No: null,
				},
				listImg: [],
				ruleValidate2: {
					sex: [{
						required: true,
						message: '请输入',
						trigger: 'change'
					}],
					IDCard: [{
						required: true,
						message: '请输入',
						trigger: 'change'
					}],
					nation: [{
						required: true,
						message: '请输入',
						trigger: 'change'
					}],
					must: [{
						required: true,
						message: '请输入',
						trigger: 'change'
					}],

				},
				ruleValidate: {
					no: [{
						required: true,
						message: '请输入编号',
						trigger: 'change'
					}],
					realName: [{
						required: true,
						message: '请选择名称',
						trigger: 'change'
					}],
					phone: [{
						required: true,
						message: '请选择联系方式',
						trigger: 'change'
					}],
					address: [{
						required: true,
						message: '请输入地址',
						trigger: 'blur'
					}],

				},
				remarkIds: null,
				remarks: [{
					label: '保姆能力',
					options: []
				}, {
					label: '厨艺',
					options: []
				}, {
					label: '清理户型',
					options: []
				}, {
					label: '育儿能力',
					options: []
				}, {
					label: '照顾老人',
					options: []
				}],
				isPhoneGot: false
			}
		},
		components: {
			'cityChoose': cityChoose,
		},

		created: function() {
			// console.log(this.dom.no)
			if (this.dom.no == null || this.dom.no.substring(0, 3) == 'BMJ') {
				this.writeNo = false
			} else {
				this.writeNo = true
			}
			if (localStorage.getItem("roleId") !== '42') {
				this.writeNo = false
			}
			if (this.dom.baomuWorkType != null) {
				this.baomuWorkType = this.StringToArray(this.dom.baomuWorkType, ',')
			}
			var imgurl = this.dom.headimg
			// if (imgurl!=null){
			//     if (imgurl.substring(0,1)=='e'){
			//         this.dom.headPortrait= "http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/"+this.dom.headimg
			//     }else if (imgurl.substring(0,1)=='h'){
			//         this.dom.headPortrait= this.dom.headimg
			//     }
			// }
			this.addTo.baomuId = this.dom.id;
			this.getinfo();
			this.getallSites();
			this.getData();
			this.getEmployeeSort();
			this.getinfo2()

		},
		methods: {
			showHandleByRoleId() {
				if (this.roleId == null) {
					return false;
				}
				return this.roleId != 85;
			},
			getEmployeeSort() {
				this.$getUrl("get_employeeSort", this.dom.id, {}).then(res => {
					if (res.status == 200) {

						this.sortDom = res.data

					} else {
						this.$message.error("失败，" + res.msg);
					}
				})
			},
			getSx() {
				this.baomuinfo2.zodiac = this.getshengxiao(this.baomuinfo.birthTime.substring(0, 4))
			},
			getshengxiao(yyyy) {

				var arr = ['猴', '鸡', '狗', '猪', '鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊'];
				return /^\d{4}$/.test(yyyy) ? arr[yyyy % 12] : null
			},
			getData() {
				this.$postUrl("list_Certificate", this.dom.id, {}).then(res => {
					if (res.status == 200) {
						res.data.forEach((cer, index) => {
							this.listImg.push(
								cer.certificateImg
							);
						});


					} else {
						this.$message.error("失败，" + res.msg);
					}
				})
			},
			/* 函数功能：字符串按照指定字符串分割转换为数组
			   参数:
			   str :需转换的字符串
			   substr:分割字符串
			   返回值:
			   转换后的数组
			*/
			StringToArray(str, substr) {

				var arrTmp = new Array();
				if (substr == "") {
					arrTmp.push(str);
					return arrTmp;
				}
				var i = 0,
					j = 0,
					k = str.length;
				while (i < k) {
					j = str.indexOf(substr, i);
					if (j != -1) {
						if (str.substring(i, j) != "") {
							arrTmp.push(str.substring(i, j));
						}
						i = j + 1;
					} else {
						if (str.substring(i, k) != "") {
							arrTmp.push(str.substring(i, k));
						}
						i = k;
					}
				}
				return arrTmp;
			},
			changeSite() {

			},
			getallSites() {
				this.$postData("site_baomulist", {}, {}).then(res => {
					if (res.status == 200) {
						res.data.forEach((city, index) => {
							this.allSites.push({
								label: city.name,
								value: city.id,
							});
						});

					} else {
						this.$message.error("查询失败，" + res.msg);
					}
				})
			},
			handleAvatarSuccess(res, file) {
				this.dom.headPortrait = res.data;
				this.dom.headSource = "";

			},
			/*
			城市选择
			 */
			initChooseProject(city) {
				if (city != null) {
					this.dom.cityId = city.cityId
					this.dom.areaId = city.areaId
				}
			},
			getEmployeePhoneById() {
				this.$getUrl("getEmployeePhoneById", this.dom.id).then(res => {
					if (res.status == 200) {
						this.dom.phone = res.data
						this.isPhoneGot = true
					} else {
						this.$message.error(res.msg)
					}
				})
			},
			getinfo() {
				this.$postUrl("get_dict", 124, null, {}).then(res => {
					if (res.status == 200) {
						// console.log(res.data)
						res.data.forEach(v => {
							if (v.typeText == '保姆能力') {
								this.remarks[0].options.push({
									value: (v.id).toString(),
									label: v.text,
								})
							}
							if (v.typeText == '厨艺') {
								this.remarks[1].options.push({
									value: (v.id).toString(),
									label: v.text,
								})
							}
							if (v.typeText == '清理户型') {
								this.remarks[2].options.push({
									value: (v.id).toString(),
									label: v.text,
								})
							}
							if (v.typeText == '育儿能力') {
								this.remarks[3].options.push({
									value: (v.id).toString(),
									label: v.text,
								})
							}
							if (v.typeText == '照顾老人') {
								this.remarks[4].options.push({
									value: (v.id).toString(),
									label: v.text,
								})
							}
						})


					} else {
						this.$message.error("查询失败，" + res.msg);
					}
				})
				this.$postUrl("info_baomu", this.addTo.baomuId, {}).then(res => {
					if (res.status == 200) {
						this.baomuinfo = res.data;

					} else {
						this.$message.error("失败，" + res.msg);
					}
				})

			},
			getinfo2() {
				this.$postUrl("get_baomuinfo", this.addTo.baomuId, {}).then(res => {
					if (res.status == 200) {
						this.baomuinfo2 = res.data;
						if (this.baomuinfo2.workType != null) {
							this.workType = this.StringToArray(this.baomuinfo2.workType, ',')
						}
						if (this.baomuinfo2.languages != null) {
							this.languages = this.StringToArray(this.baomuinfo2.languages, ',')
						}
						if (this.baomuinfo2.serverContent != null) {
							this.serverContent = this.StringToArray(this.baomuinfo2.serverContent, ',')
						}
						if (this.baomuinfo2.health != null) {
							this.health = this.StringToArray(this.baomuinfo2.health, ',')
						}
						if (this.baomuinfo2.otherSkills != null) {
							this.otherSkills = this.StringToArray(this.baomuinfo2.otherSkills, ',')
						}
						if (this.baomuinfo2.remarks != null) {
							this.remarkIds = this.StringToArray(this.baomuinfo2.remarks, ',')
						}


					} else {
						this.$message.error("失败，" + res.msg);
					}
				})

			},
			setinfo(name) {
				this.baomuinfo.employeeId = this.addTo.baomuId;
				if (this.baomuinfo.idcard == null) {
					return this.$message.error("请输入身份证等信息");
				}
				this.baomuinfo2.workType = null,
					this.workType.forEach(v => {
						if (this.baomuinfo2.workType == null) {
							this.baomuinfo2.workType = v
						} else {
							this.baomuinfo2.workType = this.baomuinfo2.workType + "," + v
						}
					});
				this.baomuinfo2.remarks = null,
					this.remarkIds.forEach(v => {
						if (this.baomuinfo2.remarks == null) {
							this.baomuinfo2.remarks = v
						} else {
							this.baomuinfo2.remarks = this.baomuinfo2.remarks + "," + v
						}
					});
				this.baomuinfo2.serverContent = null,
					this.serverContent.forEach(v => {
						if (this.baomuinfo2.serverContent == null) {
							this.baomuinfo2.serverContent = v
						} else {
							this.baomuinfo2.serverContent = this.baomuinfo2.serverContent + "," + v
						}
					});
				this.baomuinfo2.health = null,
					this.health.forEach(v => {
						if (this.baomuinfo2.health == null) {
							this.baomuinfo2.health = v
						} else {
							this.baomuinfo2.health = this.baomuinfo2.health + "," + v
						}
					});
				this.baomuinfo2.otherSkills = null,
					this.otherSkills.forEach(v => {
						if (this.baomuinfo2.otherSkills == null) {
							this.baomuinfo2.otherSkills = v
						} else {
							this.baomuinfo2.otherSkills = this.baomuinfo2.otherSkills + "," + v
						}
					});
				this.baomuinfo2.languages = null,
					this.languages.forEach(v => {
						if (this.baomuinfo2.languages == null) {
							this.baomuinfo2.languages = v
						} else {
							this.baomuinfo2.languages = this.baomuinfo2.languages + "," + v
						}
					});

				this.$refs[name].validate(valid => {
					if (valid) {

						if (this.baomuinfo.idcard.length != 18) {
							return this.$message.error("身份证号长度错误");
						}
						this.saveOk = true
						this.$postData("add_baomu_info", this.baomuinfo, {}).then(res => {
							if (res.status == 200) {

								this.$message.success("更新成功，" + res.msg);
								this.getinfo();

								this.baomuinfo2.baomuId = this.addTo.baomuId;
								this.$postData("up_baomuinfo", this.baomuinfo2, {}).then(res => {
									if (res.status == 200) {
										this.$message.success("更新成功！")
										this.getinfo2()
										this.saveOk = false
										this.chooseThisModel();

									} else {
										this.$message.error("失败，" + res.msg);
									}
								})

							} else {
								this.$message.error("失败，" + res.msg);
							}
						})

						console.log("验证成功")
					} else {
						this.$message.error("请将必填内容填写完整");
						return false;
					}
				});


			},
			upTo() {
				this.$postData("up_employeeToBaomu", this.addTo, {}).then(res => {
					if (res.status == 200) {
						this.$message.success("解绑成功，" + res.msg);
						this.chooseThisModel();
					} else {
						this.$message.error("解绑失败，" + res.msg);
					}
				})
			},
			saveTo() {
				this.$postData("add_employeeToBaomu", this.addTo, {}).then(res => {
					if (res.status == 200) {
						this.$message.success("绑定成功，" + res.msg);
						this.chooseThisModel();
					} else {
						this.$message.error("绑定失败，" + res.msg);
					}
				})
			},
			save(name) {
				this.dom.baomuWorkType = null,
					this.baomuWorkType.forEach(v => {
						if (this.dom.baomuWorkType == null) {
							this.dom.baomuWorkType = v
						} else {
							this.dom.baomuWorkType = this.dom.baomuWorkType + "," + v
						}


					});
				this.$refs[name].validate(valid => {
					if (valid) {
						if (this.dom.phone.length != 11) {
							return this.$message.error("手机号长度错误");
						}
						this.$postData("save_employeeSort", this.sortDom, {}).then(res => {
							if (res.status == 200) {

							} else {
								this.$message.error("修改失败，" + res.msg);
							}
						})
						this.$postData("update_employee", this.dom, {}).then(res => {
							if (res.status == 200) {
								this.$Message.success('修改成功');
								this.chooseThisModel();
							} else {
								this.$message.error("修改失败，" + res.msg);
							}
						})
						console.log("验证成功")
					} else {
						return false;
					}
				});


			},
			/*
			 * 关闭当前窗口
			 * */
			chooseThisModel(name) {
				// this.dom=null;
				this.$emit('init-choose', "");
			},
			//------------
			handleSelect(item) {
				this.addTo.employeeId = item.name;
				this.addTo.baomuId = this.dom.id;

			},
			querySearchAsync(queryString, cb) {
				this.restaurants = []
				this.baomuModel.RealName = this.dom.agentName;
				this.$postData("agent_list", this.baomuModel, {}).then(res => {
					if (res.status == 200) {
						this.list = res.data;
						this.list.forEach((item, index, arr) => {
							var a = {}
							a.value = item.realName;
							a.name = item.id;
							this.restaurants.push(a);
						});
						this.list = []
					} else {
						this.$message.error("查询失败，" + res.msg);
					}
				})
				var restaurants = this.restaurants;
				var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants;
				cb(restaurants);
			},

			createStateFilter(queryString) {
				return (state) => {
					return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
				};
			},

		},

	}
</script>


<style scoped>
	.must {
		color: red;
	}

	.label-name {
		float: left;
		text-align: center;
		width: 20%;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 178px;
		height: 178px;
		display: block;
	}

	.startWord {
		background-color: #F5F7FA;
		color: #909399;
		vertical-align: middle;

		position: relative;
		border: 1px solid #DCDFE6;
		border-radius: 4px;
		padding: 0 20px;
		width: 1px;
		white-space: nowrap;
		float: left;
		padding-right: 50px;
	}

	.ivu-col-span-9 {
		padding-top: 30px;
	}

	.el-input-group {
		width: 80%;
	}

	.contentBox {
		overflow: hidden;
	}

	.addProject {
		width: 200px;
	}

	#operBtnDiv button {
		margin: 0px 0px 10px 5px;
	}

	/* 查询div*/
	#searchDiv {
		/*padding-top: 20px;*/
		padding-left: 20px;
		font-weight: bolder;
		/*padding-bottom: 85px;*/
	}

	/*分页按钮*/
	#bodyDiv {
		/*width: 1339px;*/
		background-color: #fff;

	}

	/*分页按钮*/
	#footPage {
		background-color: #fff;
		padding: 5px;
		padding-top: 15px;
	}

	#left_body_div,
	#right_body_div {
		float: left;
	}

	#left_body_div {
		width: 20%;
		/*border: 1px solid #000;*/
		overflow: auto;
		height: 800px;
	}

	#right_body_div {
		width: 80%;
	}

	.text_align {
		text-align: center;
	}
</style>