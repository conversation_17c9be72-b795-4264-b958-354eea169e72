<template>
	<div class="container">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">课程管理</el-menu-item>
			<el-menu-item index="1">分组管理</el-menu-item>
			<el-menu-item index="2">数据统计</el-menu-item>
			<el-menu-item index="3">学习进度</el-menu-item>
		</el-menu>


		<!-- 搜索栏目 -->
		<div v-if="choiceIndexNow==0">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="课程标题、关键词、简介和类型名等"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="课程分组" style="margin-right: 20px">
								<el-select v-model="searchGroup" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in groupList" :key="index" :label="item.groupName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="课程类型" style="margin-right: 20px">
								<el-select v-model="searchType" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="课程状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>
							<el-button icon="el-icon-refresh" @click="reset()">重置</el-button>
							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(0,0)">添加
							</el-button>
							<!-- 						<el-button type="success" icon="el-icon-edit" @click="openModal(2,0)">添加类型
						</el-button> -->
						</el-col>
					</el-row>

				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column prop="id" width="60" label="编号">
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="160" prop="courseTitle" label="课程标题">
					<template slot-scope="scope">
						<span v-if="!scope.row.isEdit">{{scope.row.courseTitle}}</span>

						<el-input v-model="scope.row.courseTitle" style="width: 100%;" type="textarea"
							v-if="scope.row.isEdit" placeholder="请输入课程标题"
							:autosize="{ minRows: 4, maxRows: 10}"></el-input>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="courseRemark" label="课程简介" :render-header="renderHeader">
					<template slot-scope="scope">
						<span class="column-text">{{scope.row.courseRemark|| '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="courseKeyWords" label="关键词" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="scope.row.courseKeyWords||scope.row.isEdit">
							<ul v-for="(item,index) in keyList" :key="index">
								<li class="tags-li" v-for="(item1,index1) in scope.row.keyListArray" :key="index1"
									v-if="item.text==item1">
									<span>
										{{ item.text }}
									</span>
									<span class="tags-li-icon" @click="closeKeyTags(choiceItem,index1)"><i
											class="el-icon-close" v-if="scope.row.isEdit"></i></span>
								</li>
							</ul>
							<el-button type="primary" @click="openModal(6,scope.$index)" plain icon="el-icon-plus"
								v-if="scope.row.isEdit">添加标签</el-button>
						</div>

						<div v-if="!scope.row.courseKeyWords&&!scope.row.isEdit">
							<span>暂未设置</span>
						</div>
					</template>
				</el-table-column>

				<el-table-column width="90" label="课程分组">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag :type="typeStyleList[scope.row.courseGroupId%4]">
								{{formatGroup(scope.row)}}
							</el-tag>
						</div>

						<el-select v-model="scope.row.courseGroupId" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit" filterable>
							<el-option v-for="(item,index) in groupList" :key="index" :label="item.groupName"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="typeName" label="课程类型">
					<template slot-scope="scope">
						<span v-if="!scope.row.isEdit">{{formatType(scope.row)}}</span>

						<el-select v-model="scope.row.courseTypeId" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit" filterable>
							<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column prop="campusName" width="120" label="课程校区" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-tag v-if="item.id == scope.row.campusId" v-for="(item,index) in campusList" :key="index"
							:type="typeStyleList[index%4]">{{item.campusName}}</el-tag>
						<span v-if="!scope.row.campusId">-</span>
					</template>
				</el-table-column>

				<el-table-column width="180" label="课程查看权限" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="scope.row.authList!='0'||scope.row.isEdit">
							<ul v-for="(item,index) in authList" :key="index">
								<li class="tags-li" v-for="(item1,index1) in scope.row.authListArray" :key="index1"
									v-if="item.id==parseInt(item1)">
									<span>
										{{ item.name }}
									</span>
									<span class="tags-li-icon" @click="closeAuthTags(scope.$index,index1)"><i
											class="el-icon-close" v-if="scope.row.isEdit"></i></span>
								</li>
							</ul>
							<el-button type="primary" @click="openModal(4,scope.$index)" plain icon="el-icon-plus"
								v-if="scope.row.isEdit">添加角色</el-button>
						</div>

						<div v-if="scope.row.authList==0&&!scope.row.isEdit">
							<span>暂未设置</span>
						</div>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="courseImg" label="课程封面" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
							:disabled="!scope.row.isEdit" list-type="picture" :on-success="imgUploadSuccess"
							:show-file-list="false">
							<img :src="scope.row.courseImg!=null?scope.row.courseImg:blankImg"
								style="width: 120px;height: 72px;"
								@click="scope.row.isEdit?openImgUpload(0,scope.$index):openImg(scope.row.courseImg)">
						</el-upload>
					</template>
				</el-table-column>

				<el-table-column prop="courseAmount" label="课程价格" width="120" sortable>
					<template slot-scope="scope">
						<span
							v-if="!scope.row.isEdit">{{scope.row.courseAmount?'￥'+scope.row.courseAmount:'暂未录入'}}</span>

						<el-input v-model="scope.row.courseAmount" style="width: 100%;" v-if="scope.row.isEdit"
							placeholder="请输入课程价格(也可使用佳币兑换)"
							onKeypress="return event.keyCode>=48&&event.keyCode<=57||event.keyCode==46"
							onpaste="return !clipboardData.getData('text').match(/D/)" ondragenter="return false">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column prop="courseDuration" label="课程时长" width="120" sortable>
					<template slot-scope="scope">
						<span>{{formatCourseDuration(scope.row.courseDuration,'')}}</span>
					</template>
				</el-table-column>

				<el-table-column prop="videoUrl" label="简介视频" width="140">
					<template slot-scope="scope">
						<el-button @click="openVideo(scope.row.videoUrl)" type="primary" size="small"
							:disabled="scope.row.videoUrl==null||scope.row.videoUrl==''" v-if="!scope.row.isEdit"
							icon="el-icon-zoom-in">预览
						</el-button>

						<el-input v-model="scope.row.videoUrl" style="width: 100%;" type="textarea"
							placeholder="请输入简介视频地址" v-if="scope.row.isEdit" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column width="125" prop="courseDetailUrl" label="详情页图片" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
							:disabled="!scope.row.isEdit" list-type="picture" :on-success="imgUploadSuccess"
							:show-file-list="false">
							<img :src="scope.row.courseDetailUrl!=null?scope.row.courseDetailUrl:blankImg"
								style="width: 100px;height: 100px;"
								@click="scope.row.isEdit?openImgUpload(1,scope.$index):openImg(scope.row.courseDetailUrl)">
						</el-upload>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="certId" label="证书编号">
					<template slot-scope="scope">
						<span v-if="!scope.row.isEdit">{{formatCert(scope.row)}}</span>

						<el-select v-model="scope.row.certId" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit" filterable>
							<el-option v-for="(item,index) in certList" :key="index" :label="item.certCodeFixed"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="examId" label="课程考试">
					<template slot-scope="scope">
						<span v-if="!scope.row.isEdit">{{formatExam(scope.row)}}</span>

						<el-select v-model="scope.row.examId" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in examList" :key="index" :label="item.examTitle"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column prop="courseScore" label="课程评分" width="120" :render-header="renderHeader">>
					<template slot-scope="scope">
						<span v-if="!scope.row.isEdit">{{scope.row.courseScore!=0?scope.row.courseScore:'暂无评分'}}</span>

						<div v-if="scope.row.isEdit">
							<el-input v-model="scope.row.courseScore" style="width: 80%;" placeholder="分(可输入小数)"
								onKeypress="return event.keyCode>=48&&event.keyCode<=57||event.keyCode==46"
								onpaste="return !clipboardData.getData('text').match(/D/)" ondragenter="return false">
							</el-input> 分
						</div>
					</template>
				</el-table-column>

				<el-table-column width="122" label="课程状态">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-if="scope.row.courseState == false" type="info">下架</el-tag>
							<el-tag v-if="scope.row.courseState == true" type="success">上架</el-tag>
						</div>

						<el-select v-model="scope.row.courseState" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in switchStateList" :key="index" :label="item.typeName"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="122" label="首页推荐" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-for="(item,index) in switchStateList" :key="index"
								v-if="scope.row.push == item.id" :type="item.type">{{item.typeName}}</el-tag>
						</div>

						<el-select v-model="scope.row.push" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in switchStateList" :key="index" :label="item.typeName"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="122" label="店长推荐" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-for="(item,index) in switchStateList" :key="index"
								v-if="scope.row.storePush == item.id" :type="item.type">{{item.typeName}}</el-tag>
						</div>

						<el-select v-model="scope.row.storePush" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in switchStateList" :key="index" :label="item.typeName"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="122" label="问卷调查" :render-header="renderHeader">
					<template slot-scope="scope">
						<div>
							<el-tag v-for="(item,index) in switchStateList" :key="index"
								v-if="scope.row.needSurvey == item.id" :type="item.type">{{item.typeName}}</el-tag>
						</div>
					</template>
				</el-table-column>


				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column width="120" prop="creater" label="创建人">
					<template slot-scope="scope">
						<span v-if="scope.row.createrName!=null">{{scope.row.createrName}}（{{scope.row.creater}}）</span>
						<span v-if="scope.row.createrName==null">{{checkStr(scope.row.creater)}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
					<template slot-scope="scope">
						<el-button @click="openModal(7,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
						<el-button @click="updateCourse(scope.row)" type="primary" size="small" v-if="scope.row.isEdit"
							icon="el-icon-circle-check">保存
						</el-button>
						<el-popconfirm title="确定删除该课程及其所有章节吗？删除后无法恢复，请谨慎操作!" @confirm="deleteCourse(scope.row)">
							<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
								icon="el-icon-delete" disabled>删除
							</el-button>
						</el-popconfirm>

					</template>
				</el-table-column>

			</el-table>
		</div>

		<!-- 课程分组 -->
		<div v-if="choiceIndexNow==1">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="分组标题、内容等"></el-input>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="分组状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(3,0)">添加
							</el-button>
						</el-col>
					</el-row>

				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column prop="id" width="80" label="编号" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="groupName" label="分组标题">
					<template slot-scope="scope">
						<span v-if="!scope.row.isEdit">{{checkStr(scope.row.groupName)}}</span>

						<el-input v-model="scope.row.groupName" style="width: 100%;" type="textarea"
							v-if="scope.row.isEdit" placeholder="请输入分组标题" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column width="180" prop="groupContent" label="分组内容">
					<template slot-scope="scope">
						<span class="column-text" v-if="!scope.row.isEdit">{{checkStr(scope.row.groupContent)}}</span>

						<el-input v-model="scope.row.groupContent" style="width: 100%;" type="textarea"
							v-if="scope.row.isEdit" placeholder="请输入分组内容" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column width="125" prop="groupImg" label="分组图片" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
							:disabled="!scope.row.isEdit" list-type="picture" :on-success="imgUploadSuccess"
							:show-file-list="false">
							<img :src="scope.row.groupImg!=null?scope.row.groupImg:blankImg"
								style="width: 100px;height: 100px;"
								@click="scope.row.isEdit?openImgUpload(6,scope.$index):openImg(scope.row.groupImg)">
						</el-upload>
					</template>
				</el-table-column>

				<el-table-column width="125" prop="groupIcon" label="分组图标" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
							:disabled="!scope.row.isEdit" list-type="picture" :on-success="imgUploadSuccess"
							:show-file-list="false">
							<img :src="scope.row.groupIcon!=null?scope.row.groupIcon:blankImg"
								style="width: 100px;height: 100px;"
								@click="scope.row.isEdit?openImgUpload(7,scope.$index):openImg(scope.row.groupIcon)">
						</el-upload>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="groupState" label="分组状态">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-for="(item,index) in stateList" :key="index"
								v-if="scope.row.groupState == item.id" :type="item.type">{{item.text}}
							</el-tag>
						</div>

						<el-select v-model="scope.row.groupState" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="180" label="分组查看权限" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="scope.row.authList!=0||scope.row.isEdit">
							<ul v-for="(item,index) in authList" :key="index">
								<li class="tags-li" v-for="(item1,index1) in scope.row.authListArray" :key="index1"
									v-if="item.id==parseInt(item1)">
									<span>
										{{ item.name }}
									</span>
									<span class="tags-li-icon" @click="closeAuthTags(scope.$index,index1)"><i
											class="el-icon-close" v-if="scope.row.isEdit"></i></span>
								</li>
							</ul>
							<el-button type="primary" @click="openModal(4,scope.$index)" plain icon="el-icon-plus"
								v-if="scope.row.isEdit">添加角色</el-button>
						</div>

						<div v-if="scope.row.authList==0&&!scope.row.isEdit">
							<span>暂未设置</span>
						</div>
					</template>
				</el-table-column>

				<el-table-column width="180" label="分组角色权限" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="scope.row.roleList!=0||scope.row.isEdit">
							<ul v-for="(item,index) in roleList" :key="index">
								<li class="tags-li" v-for="(item1,index1) in scope.row.roleListArray" :key="index1"
									v-if="item.id==parseInt(item1)">
									<span>
										{{ item.name }}
									</span>
									<span class="tags-li-icon" @click="closeRoleTags(scope.$index,index1)"><i
											class="el-icon-close" v-if="scope.row.isEdit"></i></span>
								</li>
							</ul>
							<el-button type="primary" @click="openModal(5,scope.$index)" plain icon="el-icon-plus"
								v-if="scope.row.isEdit">添加角色</el-button>
						</div>

						<div v-if="scope.row.roleList==0&&!scope.row.isEdit">
							<span>暂未设置</span>
						</div>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="creTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.creTime}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creater" label="创建人">
					<template slot-scope="scope">
						<span v-if="scope.row.createrName!=null">{{scope.row.createrName}}({{scope.row.creater}})</span>
						<span v-if="scope.row.createrName==null">{{checkStr(scope.row.creater)}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
					<template slot-scope="scope">
						<el-button @click="openEdit(scope.row)" type="success" size="small" :disabled="scope.row.isEdit"
							v-if="!scope.row.isEdit" icon="el-icon-edit">修改
						</el-button>
						<el-button @click="updateCourseGroup(scope.row)" type="primary" size="small"
							v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
						</el-button>
						<el-popconfirm title="确定删除该课程分组吗？分组内的课程将会移至默认分组，请谨慎操作!" @confirm="deleteCourseGroup(scope.row)">
							<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
								:disabled="!scope.row.isEdit" icon="el-icon-delete">删除
							</el-button>
						</el-popconfirm>

					</template>
				</el-table-column>

			</el-table>
		</div>

		<!-- 数据统计 -->
		<div v-if="choiceIndexNow==2">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="请输入课程标题"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="课程分组" style="margin-right: 20px">
								<el-select v-model="quer.courseTypeId" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in groupList" :key="index" :label="item.groupName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="7">
							<el-form-item label="筛选时间" style="margin-right: 20px">
								<div style="width: 180px;display: block;">
									<el-date-picker v-model="searchTime" type="datetimerange" format="yyyy-MM-dd"
										:picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期"
										end-placeholder="结束日期" align="right" value-format="yyyy-MM-dd HH:mm:ss">
									</el-date-picker>
								</div>
							</el-form-item>
						</el-col>
						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>
						</el-col>
					</el-row>

				</el-form>
			</div>

			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">


				<el-table-column prop="id" width="80" label="编号" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="courseTitle" label="课程标题">
					<template slot-scope="scope">
						<span>{{scope.row.courseTitle}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="pv" label="pv" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.pv||0}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="uv" label="uv" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.uv||0}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="percentage" label="点击率" sortable>
					<template slot-scope="scope">
						<span>{{parseFloat(scope.row.percentage||0.00).toFixed(2)}}%</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="addCount" label="报名次数" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.addCount|| 0}}次</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="shareCount" label="分享次数" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.shareCount|| 0}}次</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="commentCount" label="评价次数" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.commentCount|| 0}}次</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="studyDurationTotal" label="学习总时长" sortable>
					<template slot-scope="scope">
						<span>{{formatCourseDuration(scope.row.studyDurationTotal||0,'暂无')}}</span>
					</template>
				</el-table-column>
			</el-table>

			<h1 style="margin-top: 40px;">课程数据 - 统计</h1>
			<el-row>
				<el-col :span="12">
					<h2>课程查看（总计：{{logTotal}}）</h2>
					<g2char :setCharData="logList" :heightSize="300"></g2char>
				</el-col>
				<el-col :span="12">
					<h2>课程报名（总计：{{logTotal1}}）</h2>
					<g2char :setCharData="logList1" :heightSize="300"></g2char>
				</el-col>
			</el-row>
			<el-row style="margin-top: 40px;">
				<el-col :span="12">
					<h2>课程分享（总计：{{logTotal2}}）</h2>
					<g2char :setCharData="logList2" :heightSize="300"></g2char>
				</el-col>
				<el-col :span="12">
					<h2>课程评价（总计：{{logTotal3}}）</h2>
					<g2char :setCharData="logList3" :heightSize="300"></g2char>
				</el-col>
			</el-row>
		</div>

		<!-- 学习记录 -->
		<div v-if="choiceIndexNow==3">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="学员手机号" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="请输入学员手机号进行查询"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(10,0)">学员开课
							</el-button>
						</el-col>
					</el-row>

				</el-form>
			</div>

			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column width="140" prop="memberName" label="会员名">
					<template slot-scope="scope">
						<span>{{scope.row.memberName||'匿名用户'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="phone" label="手机号">
					<template slot-scope="scope">
						<span>{{scope.row.phone||'-'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="courseTitle" label="课程标题">
					<template slot-scope="scope">
						<span>{{scope.row.courseTitle}}</span>
					</template>
				</el-table-column>

				<el-table-column prop="courseAmount" label="课程价格" width="140" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.courseAmount?'￥'+scope.row.courseAmount:'0.00'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="studyDurationRate" label="学习进度">
					<template slot-scope="scope">
						<span>{{scope.row.studyDurationRate}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="studyDuration" label="学习时长" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.studyDuration}}小时</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="lastTime" label="最后学习时间">
					<template slot-scope="scope">
						<span>{{scope.row.lastTime}}</span>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<el-drawer size="90%" :with-header="false" :visible.sync="courseModal" direction="rtl">
			<el-menu :default-active="choiceIndexCourse" class="el-menu-demo" mode="horizontal"
				@select="handleSelectCourse" style="margin: 0 0 30px 0;">
				<el-menu-item index="0">基础信息</el-menu-item>
				<el-menu-item index="1" v-if="detailType==1">章节分组</el-menu-item>
				<el-menu-item index="2" v-if="detailType==1">章节详情</el-menu-item>
			</el-menu>

			<div style="margin: 0 40px;">
				<transition name="el-zoom-in-top">
					<div v-show="choiceIndexCourse == '0'">
						<el-row style="width:100%;height: auto;margin-bottom: 0px;">
							<h2 class="detail-title">课程信息</h2>
							<el-col :span="12">
								<el-form :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm"
									size="mini">
									<el-form-item label="* 课程标题：">
										<el-input v-model="course.courseTitle" type="textarea" class="handle-input mr10"
											placeholder="请输入课程标题">
										</el-input>
									</el-form-item>

									<el-form-item label="* 课程简介：">
										<el-input v-model="course.courseRemark" type="textarea"
											class="handle-input mr10" placeholder="请输入课程简介" disabled>
										</el-input>
										<el-button type="primary" @click="openContentText1=true" plain
											icon="el-icon-edit">编辑</el-button>
										<el-button @click="openView(course)" type="primary" size="small"
											icon="el-icon-zoom-in">预览
										</el-button>
										<el-tooltip class="item" effect="dark" content="支持图文形式（优先展示课程详情图，推荐用详情图代替）"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="简介视频：">
										<el-input v-model="course.videoUrl" type="textarea" class="handle-input mr10"
											placeholder="请输入课程简介视频地址" :autosize="{ minRows: 4, maxRows: 10}">
										</el-input>
									</el-form-item>

									<el-form-item label="关键词：" v-if="detailType==1">
										<div v-if="course.keyListArray.length">
											<ul v-for="(item,index) in keyList" :key="index">
												<li class="tags-li" v-for="(item1,index1) in course.keyListArray"
													:key="index1" v-if="item.text==item1">
													<span>
														{{ item.text }}
													</span>
													<span class="tags-li-icon" @click="closeKeyTags(index1)"><i
															class="el-icon-close"></i></span>
												</li>
											</ul>

										</div>
										<div v-if="!course.keyListArray.length">
											<span>暂未设置</span>
										</div>
										<el-button type="primary" @click="openModal(6)" plain
											icon="el-icon-plus">添加标签</el-button>
									</el-form-item>

									<el-form-item label="查看权限：" v-if="detailType==1">
										<template slot-scope="scope">
											<div v-if="course.authListArray.length">
												<ul v-for=" (item,index) in authList" :key="index">
													<li class="tags-li" v-for="(item1,index1) in course.authListArray"
														:key="index1" v-if="item.id==parseInt(item1)">
														<span>
															{{ item.name }}
														</span>
														<span class="tags-li-icon" @click="closeAuthTags(index1)"><i
																class="el-icon-close"></i></span>
													</li>
												</ul>

											</div>
											<div v-if="!course.authListArray.length">
												<span>暂未设置</span>
											</div>
											<el-button type="primary" @click="openModal(4)" plain
												icon="el-icon-plus">添加角色</el-button>
										</template>
									</el-form-item>

									<el-form-item label="首页推荐：">
										<el-select v-model="course.push" placeholder="请选择" style="width: 100px;">
											<el-option v-for="(item,index) in switchStateList" :key="index"
												:label="item.typeName" :value="item.id"></el-option>
										</el-select>
										<el-tooltip class="item" effect="dark" content="是否显示在课程首页推荐列表"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="店长推荐：">
										<el-select v-model="course.storePush" placeholder="请选择" style="width: 100px;">
											<el-option v-for="(item,index) in switchStateList" :key="index"
												:label="item.typeName" :value="item.id"></el-option>
										</el-select>
										<el-tooltip class="item" effect="dark" content="是否显示在店长主页推荐列表"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="课程封面：">
										<el-upload class="upload-demo"
											action="https://biapi.xiaoyujia.com/files/uploadFiles" list-type="picture"
											:on-success="imgUploadSuccess" :show-file-list="false">
											<img :src="course.courseImg!=null?course.courseImg:blankImg"
												style="width: 250px;height: 150px;" @click="openImgUpload(3,0)">
										</el-upload>
									</el-form-item>

								</el-form>
							</el-col>

							<el-col :span="12">
								<el-form :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm"
									size="mini">
									<el-form-item label="* 课程分组：">
										<el-select v-model="course.courseGroupId" placeholder="请选择"
											style="width: 100px;">
											<el-option v-for="(item,index) in groupList" :key="index"
												:label="item.groupName" :value="item.id"></el-option>
										</el-select>
										<el-button type="success" icon="el-icon-circle-plus-outline"
											@click="openModal(3,0)" style="margin-left: 20px">添加
										</el-button>
									</el-form-item>

									<el-form-item label="* 课程类型：">
										<el-select v-model="course.courseTypeId" placeholder="请选择" style="width: 100px;"
											filterable>
											<el-option v-for="(item,index) in typeList" :key="index"
												:label="item.typeName" :value="item.id"></el-option>
										</el-select>
										<el-button type="success" icon="el-icon-circle-plus-outline"
											@click="openModal(2,0)" style="margin-left: 20px">添加
										</el-button>
									</el-form-item>

									<el-form-item label="课程校区：">
										<el-select v-model="course.campusId" placeholder="请选择" style="width: 100px;"
											filterable clearable>
											<el-option v-for="(item,index) in campusList" :key="index"
												:label="item.campusName" :value="item.id"></el-option>
										</el-select>
										<el-button type="success" icon="el-icon-circle-plus-outline"
											@click="openCampus()" style="margin-left: 20px">添加
										</el-button>
									</el-form-item>

									<el-form-item label="课程证书：">
										<el-select v-model="course.certId" placeholder="请选择" style="width: 100px;"
											filterable clearable>
											<el-option v-for="(item,index) in certList" :key="index"
												:label="item.certCodeFixed" :value="item.id"></el-option>
										</el-select>
										<el-button type="success" icon="el-icon-circle-plus-outline" @click="openCert()"
											style="margin-left: 20px">添加
										</el-button>
									</el-form-item>

									<el-form-item label="课程考试：">
										<el-select v-model="course.examId" placeholder="请选择" style="width: 100px;"
											filterable clearable>
											<el-option v-for="(item,index) in examList" :key="index"
												:label="item.examTitle" :value="item.id"></el-option>
										</el-select>
										<el-button type="success" icon="el-icon-circle-plus-outline" @click="openExam()"
											style="margin-left: 20px">添加
										</el-button>
									</el-form-item>

									<el-form-item label="课程导师：">
										<el-select v-model="course.teacherId" placeholder="请选择" style="width: 100px;"
											filterable clearable>
											<el-option v-for="(item,index) in teacherList" :key="index"
												:label="item.id+'：'+item.realName" :value="item.id"></el-option>
										</el-select>
										<el-button type="success" icon="el-icon-circle-plus-outline"
											@click="openTeacher()" style="margin-left: 20px">添加
										</el-button>
									</el-form-item>

									<el-form-item label="* 课程价格：">
										<el-input v-model="course.courseAmount" class="handle-input mr10"
											placeholder="请输入课程价格(也可使用佳币兑换，汇率为RMB:佳币=1:100)"
											onKeypress="return event.keyCode>=48&&event.keyCode<=57||event.keyCode==46"
											onpaste="return !clipboardData.getData('text').match(/D/)"
											ondragenter="return false">
										</el-input> 元
										<el-tooltip class="item" effect="dark" content="课程使用佳币兑换时，将有一定汇率"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="* 分佣金额：">
										<el-input v-model="course.commissionAmount" class="handle-input mr10"
											placeholder="请输入课程分佣金额(可通过课程分享/海报进行分销，课程开发人获取佣金，并可在家姐联盟中进行提现)"
											onKeypress="return event.keyCode>=48&&event.keyCode<=57||event.keyCode==46"
											onpaste="return !clipboardData.getData('text').match(/D/)"
											ondragenter="return false">
										</el-input> 元
										<el-tooltip class="item" effect="dark"
											content="默认按云平台中设置的分佣来（50%），若在此进行了配置，则按这里自定义的值进行分佣" placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="课程评分：">
										<el-input v-model="course.courseScore" class="handle-input mr10"
											placeholder="分(可输入小数)"
											onKeypress="return event.keyCode>=48&&event.keyCode<=57||event.keyCode==46"
											onpaste="return !clipboardData.getData('text').match(/D/)"
											ondragenter="return false">
										</el-input> 分
										<el-tooltip class="item" effect="dark" content="无人评分时可由后台手动设置，否则将自动计算平均分并展示给用户"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="课程赠送：" v-if="detailType==1">
										<el-input v-model="course.giftNum" class="handle-input mr10"
											placeholder="请输入课程赠送(购买后可免费赠送给他人)"
											onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
										</el-input> 次
										<el-tooltip class="item" effect="dark" content="在购买课程后，可免费赠送课程给他人，在此设置赠送次数上限"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="课程时长：">
										<span>{{formatCourseDuration(course.courseDuration,'')}}</span>
										<el-tooltip class="item" effect="dark" content="无需填写，在保存课程章节时将自动计算总时长"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="问卷调查：">
										<el-select v-model="course.needSurvey" placeholder="请选择" style="width: 100px;">
											<el-option v-for="(item,index) in switchStateList" :key="index"
												:label="item.typeName" :value="item.id"></el-option>
										</el-select>
										<el-tooltip class="item" effect="dark" content="报名前是否强制要求用户填写问卷"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="课程状态：" v-if="detailType==1">
										<el-select v-model="course.courseState" placeholder="请选择" style="width: 100px;">
											<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
												:value="item.value"></el-option>
										</el-select>
									</el-form-item>

									<el-form-item label="课程详情图：">
										<div style="display: flex;">
											<el-upload class="upload-demo"
												action="https://biapi.xiaoyujia.com/files/uploadFiles"
												list-type="picture" :on-success="imgUploadSuccess"
												:show-file-list="false">
												<img :src="course.courseDetailUrl!=null?course.courseDetailUrl:blankImg"
													style="width: 250px;height: 250px;" @click="openImgUpload(4,0)">
											</el-upload>
											<el-tooltip class="item" effect="dark" content="推荐上传，未上传则根据课程简介中的图文内容进行展示"
												placement="top-start">
												<i class="el-icon-question" style="margin-left:10px;"></i>
											</el-tooltip>
										</div>
									</el-form-item>

								</el-form>
							</el-col>
						</el-row>

						<div style="margin: 0 400px 100px 400px;width: 100%;">
							<el-button @click="courseModal=false" type="primary" size="small">取消
							</el-button>
							<el-button @click="addCourse()" type="success" size="small" v-if="detailType==0">确定添加
							</el-button>
							<el-button @click="updateCourse(course)" type="success" size="small" v-if="detailType==1">保存
							</el-button>
						</div>
					</div>
				</transition>

				<!-- 展开列-课程章节表格 -->
				<transition name="el-zoom-in-top">
					<div v-show="choiceIndexCourse == '1'">
						<h2 class="detail-title">课程章节分组</h2>
						<el-col :span="24" style="margin: 20px 0 40px 0;">
							<el-button @click="isEdit=true" type="success" size="small" :disabled="isEdit"
								v-if="!isEdit" icon="el-icon-edit">修改
							</el-button>
							<el-button @click="updateCourseChapterList(courseChapterList)" type="primary" size="small"
								v-if="isEdit" icon="el-icon-circle-check">保存
							</el-button>

							<el-button @click="addChapter()" type="success" size="small"
								icon="el-icon-circle-plus-outline">
								添加
							</el-button>

							<el-button @click="openView(course)" type="primary" size="small" icon="el-icon-zoom-in">预览
							</el-button>
						</el-col>

						<el-table :data="courseChapterList" v-loading="loading" style="width: 1800px;margin-top: 20px;"
							border stripe :header-cell-style="{background:'#f8f8f9',fontWeight:400,color:'#000000'}">

							<el-table-column width="180" prop="courseNum" label="分组序号" :render-header="renderHeader">
								<template slot-scope="scope">
									<span v-if="!isEdit" class="column-text">{{scope.row.chapterNum}}</span>

									<el-input v-if="isEdit" v-model="scope.row.chapterNum" style="width: 90%;"
										placeholder="请输入分组序号"
										onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
									</el-input>
								</template>
							</el-table-column>

							<el-table-column width="180" prop="chapterTitle" label="分组标题">
								<template slot-scope="scope">
									<span v-if="!isEdit">{{scope.row.chapterTitle}}</span>

									<el-input v-model="scope.row.chapterTitle" style="width: 100%;" type="textarea"
										v-if="isEdit" placeholder="请输入分组标题" :autosize="{ minRows: 4, maxRows: 10}">
									</el-input>
								</template>
							</el-table-column>

							<el-table-column width="200" prop="chapterContent" label="分组简介">
								<template slot-scope="scope">
									<span v-if="!isEdit" class="column-text">{{scope.row.chapterContent}}</span>

									<el-input v-model="scope.row.chapterContent" style="width: 100%;" type="textarea"
										v-if="isEdit" placeholder='请输入分组简介' :autosize="{ minRows: 4, maxRows: 10}">
									</el-input>
									</el-input>
								</template>
							</el-table-column>

							<el-table-column width="240" label="分组章节内容" :render-header="renderHeader">
								<template slot-scope="scope">
									<div v-if="isEdit||scope.row.courseContentIdListArray.length">
										<ul v-for="(item,index) in courseContentList" :key="index">
											<li class="tags-li"
												v-for="(item1,index1) in scope.row.courseContentIdListArray"
												:key="index1" v-if="item.id==parseInt(item1)">
												<span>
													{{ index1+1 }}、{{ item.title }}
												</span>
												<span class="tags-li-icon"
													@click="closeContentTags(scope.$index,index1)"><i
														class="el-icon-close" v-if="isEdit"></i></span>
											</li>
										</ul>
										<el-button type="primary" @click="openModal(9,scope.$index)" plain
											icon="el-icon-plus" v-if="isEdit">添加章节</el-button>
									</div>

									<div v-if="!scope.row.courseContentIdListArray.length">
										<span>暂未设置</span>
									</div>
								</template>
							</el-table-column>

							<el-table-column width="180" prop="chapterState" label="分组状态">
								<template slot-scope="scope">
									<div v-if="!isEdit">
										<el-tag v-for="(item,index) in switchStateList" :key="index"
											v-if="scope.row.chapterState == item.id"
											:type="item.type">{{item.typeName}}</el-tag>
									</div>

									<el-select v-model="scope.row.chapterState" placeholder="请选择" style="width: 100px;"
										v-if="isEdit">
										<el-option v-for="(item,index) in switchStateList" :key="index"
											:label="item.typeName" :value="item.id"></el-option>
									</el-select>
								</template>
							</el-table-column>
						</el-table>

					</div>
				</transition>

				<!-- 展开列-课程章节表格 -->
				<transition name="el-zoom-in-top">
					<div v-show="choiceIndexCourse == '2'">
						<h2 class="detail-title">课程章节详情</h2>
						<el-col :span="24" style="margin: 20px 0 40px 0;">
							<el-button @click="isEdit=true" type="success" size="small" :disabled="isEdit"
								v-if="!isEdit" icon="el-icon-edit">修改
							</el-button>
							<el-button @click="updateCourseContent(courseContentList)" type="primary" size="small"
								v-if="isEdit" icon="el-icon-circle-check">保存
							</el-button>

							<el-button @click="openModal(1,choiceItemIndex)" type="success" size="small"
								icon="el-icon-circle-plus-outline">
								添加
							</el-button>

							<el-button @click="excelDownload()" type="warning" size="small" :disabled="!isEdit"
								style="margin-left: 10px" icon="el-icon-download">模板</el-button>

							<el-upload :action="excelUploadUrl" list-type="picture" :on-success="excelUploadSuccess"
								:on-error="excelUploadError" :show-file-list="false" class="upload-demo inline-block">
								<el-button @click="excelImport(choiceItemIndex)" type="warning" size="small"
									:disabled="!isEdit" style="margin-left: 10px" icon="el-icon-upload2">导入</el-button>
							</el-upload>

							<el-popconfirm :title="deleteTips" @confirm="deleteCourseContent()">
								<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
									:disabled="!isEdit" icon="el-icon-delete">删除
								</el-button>
							</el-popconfirm>
						</el-col>

						<el-table :data="courseContentList" v-loading="loading" style="width: 1800px;margin-top: 20px;"
							border stripe :header-cell-style="{background:'#f8f8f9',fontWeight:400,color:'#000000'}">
							<el-table-column type="selection" width="55">
							</el-table-column>

							<el-table-column prop="courseNum" width="80" label="序号" sortable>
								<template slot-scope="scope">
									<span @click="rowClick(scope.row.courseNum)"
										v-if="!isEdit">{{scope.row.courseNum}}</span>

									<el-input v-model="scope.row.courseNum" style="width: 100%;" v-if="isEdit"
										placeholder="请输入章节序号"
										onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
									</el-input>
								</template>
							</el-table-column>

							<el-table-column width="180" prop="title" label="章节标题">
								<template slot-scope="scope">
									<span v-if="!isEdit">{{scope.row.title}}</span>

									<el-input v-model="scope.row.title" style="width: 100%;" type="textarea"
										v-if="isEdit" placeholder="请输入章节标题" :autosize="{ minRows: 4, maxRows: 10}">
									</el-input>
								</template>
							</el-table-column>

							<el-table-column width="180" prop="courseRemark" label="章节简介">
								<template slot-scope="scope">
									<span v-if="!isEdit">{{checkStr(scope.row.courseRemark)}}</span>

									<el-input v-model="scope.row.courseRemark" style="width: 100%;" type="textarea"
										v-if="isEdit" placeholder="请输入章节简介" :autosize="{ minRows: 4, maxRows: 10}">
									</el-input>
								</template>
							</el-table-column>

							<el-table-column min-width="200" prop="courseContent" label="章节内容">
								<template slot-scope="scope">
									<span v-if="!isEdit" class="column-text">{{scope.row.courseContent}}</span>

									<el-input v-model="scope.row.courseContent" style="width: 100%;" type="textarea"
										v-if="isEdit" placeholder='请输入章节内容（可使用分隔符“|”进行换行）'
										:autosize="{ minRows: 4, maxRows: 10}">
									</el-input>
									</el-input>
									<el-button type="primary" @click="toOpenContentText(scope.$index)" plain
										icon="el-icon-edit">编辑</el-button>
								</template>
							</el-table-column>


							<el-table-column min-width="120" prop="contentState" label="章节状态">
								<template slot-scope="scope">
									<div v-if="!isEdit">
										<el-tag v-for="(item,index) in switchStateList" :key="index"
											v-if="scope.row.contentState == item.id"
											:type="item.type">{{item.typeName}}</el-tag>
									</div>

									<el-select v-model="scope.row.contentState" placeholder="请选择" style="width: 100px;"
										v-if="isEdit">
										<el-option v-for="(item,index) in switchStateList" :key="index"
											:label="item.typeName" :value="item.id"></el-option>
									</el-select>
								</template>
							</el-table-column>

							<el-table-column prop="compulsory" label="是否必修" width="120" :render-header="renderHeader">
								<template slot-scope="scope">
									<div v-if="!isEdit">
										<el-tag v-for="(item,index) in switchStateList" :key="index"
											v-if="scope.row.compulsory == item.id"
											:type="item.type">{{item.typeName}}</el-tag>
									</div>

									<el-select v-model="scope.row.compulsory" placeholder="请选择" style="width: 100px;"
										v-if="isEdit">
										<el-option v-for="(item,index) in switchStateList" :key="index"
											:label="item.typeName" :value="item.id"></el-option>
									</el-select>
								</template>
							</el-table-column>

							<el-table-column width="125" prop="courseImgUrl" label="章节封面">
								<template slot-scope="scope">
									<el-upload class="upload-demo"
										action="https://biapi.xiaoyujia.com/files/uploadFiles" :disabled="!isEdit"
										list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
										<img :src="scope.row.courseImgUrl!=null?scope.row.courseImgUrl:blankImg"
											style="width: 100px;height: 100px;"
											@click="isEdit?openImgUpload(2,scope.$index):openImg(scope.row.courseImgUrl)">
									</el-upload>
								</template>
							</el-table-column>

							<el-table-column prop="videoUrl" label="章节视频" width="180">
								<template slot-scope="scope">

									<el-button @click="openVideo(scope.row.videoUrl)" type="primary" size="small"
										:disabled="scope.row.videoUrl==null||scope.row.videoUrl==''" v-if="!isEdit"
										icon="el-icon-zoom-in">预览
									</el-button>

									<el-input v-model="scope.row.videoUrl" style="width: 100%;" type="textarea"
										placeholder="请输入章节视频地址" v-if="isEdit" :autosize="{ minRows: 4, maxRows: 10}">
									</el-input>

									<el-upload :action="fileUploadUrl" list-type="picture"
										:before-upload="beforeFileUpload" :on-success="fileUploadSuccess"
										:on-error="fileUploadError" :show-file-list="false"
										class="upload-demo inline-block">
										<el-button @click="fileUpload(scope.$index,0)" type="warning" size="small"
											style="margin-left: 10px" icon="el-icon-upload2">上传</el-button>
									</el-upload>
									<el-button @click="openModal(8,0)" type="info" size="small"
										style="margin-left: 10px" icon="el-icon-upload2">云点播</el-button>
									<el-tooltip class="item" effect="dark"
										content="推荐上传到阿里云点播控制台，再将链接复制到这，以获得更流畅的视频播放体验" placement="top-start">
										<i class="el-icon-question" style="margin-left:10px;"></i>
									</el-tooltip>
								</template>
							</el-table-column>

							<el-table-column prop="audioUrl" label="章节音频" width="180">
								<template slot-scope="scope">
									<el-button @click="openFile(scope.row.audioUrl)" type="primary" size="small"
										:disabled="scope.row.audioUrl==null||scope.row.audioUrl==''" v-if="!isEdit"
										icon="el-icon-zoom-in">预览
									</el-button>

									<el-input v-model="scope.row.audioUrl" style="width: 100%;" type="textarea"
										placeholder="请输入章节音频地址(支持.mp3等)" v-if="isEdit"
										:autosize="{ minRows: 4, maxRows: 10}">
									</el-input>

									<el-upload :action="fileUploadUrl" list-type="picture"
										:before-upload="beforeFileUpload" :on-success="fileUploadSuccess"
										:on-error="fileUploadError" :show-file-list="false"
										class="upload-demo inline-block">
										<el-button @click="fileUpload(scope.$index,1)" type="warning" size="small"
											style="margin-left: 10px" icon="el-icon-upload2">上传</el-button>
									</el-upload>
								</template>
							</el-table-column>

							<el-table-column prop="fileUrl" label="章节文档" width="180">
								<template slot-scope="scope">
									<el-button @click="openFile(scope.row.fileUrl)" type="primary" size="small"
										:disabled="scope.row.fileUrl==null||scope.row.fileUrl==''" v-if="!isEdit"
										icon="el-icon-zoom-in">预览
									</el-button>

									<el-input v-model="scope.row.fileUrl" style="width: 100%;" type="textarea"
										placeholder="请输入章节文档地址(支持.pdf/.ppt/.doc/.xls等)" v-if="isEdit"
										:autosize="{ minRows: 4, maxRows: 10}">
									</el-input>

									<el-upload :action="fileUploadUrl" list-type="picture"
										:before-upload="beforeFileUpload" :on-success="fileUploadSuccess"
										:on-error="fileUploadError" :show-file-list="false"
										class="upload-demo inline-block">
										<el-button @click="fileUpload(scope.$index,2)" type="warning" size="small"
											style="margin-left: 10px" icon="el-icon-upload2">上传</el-button>
									</el-upload>
								</template>
							</el-table-column>

							<el-table-column width="140" prop="createTime" label="创建时间" sortable>
								<template slot-scope="scope">
									<span>{{scope.row.createTime}}</span>
								</template>
							</el-table-column>

							<el-table-column fixed="right" label="章节时长" width="100">
								<template slot-scope="scope">
									<span>{{formatCourseDuration(scope.row.courseDuration)}}</span>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</transition>
			</div>
		</el-drawer>

		<!-- 添加课程分组 -->
		<el-dialog :visible.sync="courseGroupModal" width="60%" title="添加课程分组" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<h2 class="detail-title">课程分组</h2>

					<el-col :span="12">
						<el-form :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 分组名称：">
								<el-input v-model="courseGroup.groupName" class="handle-input mr10"
									placeholder="请输入分组名称（建议4个字）">
								</el-input>
							</el-form-item>

							<el-form-item label="分组图片：">
								<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
									list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="courseGroup.groupImg!=null?courseGroup.groupImg:blankImg"
										style="width: 250px;height: 250px;" @click="openImgUpload(8,0)">
								</el-upload>
							</el-form-item>
						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 分组内容：">
								<el-input v-model="courseGroup.groupContent" class="handle-input mr10"
									placeholder="请输入分组内容">
								</el-input>
							</el-form-item>

							<el-form-item label="分组图标：">
								<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
									list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="courseGroup.groupIcon!=null?courseGroup.groupIcon:blankImg"
										style="width: 250px;height: 250px;" @click="openImgUpload(9,0)">
								</el-upload>
							</el-form-item>

						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 240px;width: 100%;margin-top: 20px">
					<el-button @click="courseGroupModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addCourseGroup()()" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

		<!-- 添加课程类型 -->
		<el-dialog :visible.sync="courseTypeModal" width="40%" title="添加课程类型" :mask-closable="false">
			<div style="height: 340px;">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<h2 class="detail-title">课程类型</h2>
					<el-col :span="24">
						<el-form :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 类型名称：">
								<el-input v-model="courseType.typeName" class="handle-input mr10"
									placeholder="请输入类型名称（建议4个字）">
								</el-input>
							</el-form-item>

							<el-form-item label="* 类型编号：">
								<el-input v-model="courseType.typeCode" class="handle-input mr10"
									placeholder="请输入类型编号（按分组id：0=流量变现、1=私域变现、2=门店运营、3=美好生活，如流量变现的第一个课程类型可命名为C001）"
									type="textarea" :autosize="{ minRows: 4, maxRows: 10}">>
								</el-input>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 240px;width: 100%;margin-top: 20px">
					<el-button @click="courseTypeModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addCourseType()" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

		<!-- 添加课程章节 -->
		<el-dialog :visible.sync="courseContentModal" width="60%" title="添加课程章节" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<h2 class="detail-title">课程章节信息（{{courseName}}）</h2>
					<el-col :span="12">
						<el-form :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 章节序号：">
								<el-input v-model="courseContent.courseNum" class="handle-input mr10"
									placeholder="请输入章节序号"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input>
							</el-form-item>

							<el-form-item label="* 章节标题：">
								<el-input v-model="courseContent.title" type="textarea" class="handle-input mr10"
									placeholder="请输入章节标题">
								</el-input>
							</el-form-item>

							<el-form-item label="章节简介：">
								<el-input v-model="courseContent.courseRemark" type="textarea" class="handle-input mr10"
									placeholder="请输入章节简介">
								</el-input>
							</el-form-item>

							<el-form-item label="章节封面：">
								<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
									list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="courseContent.courseImgUrl!=null?courseContent.courseImgUrl:blankImg"
										style="width: 250px;height: 250px;" @click="openImgUpload(5,0)">
								</el-upload>
							</el-form-item>

						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="是否必修：">
								<el-select v-model="courseContent.compulsory" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in switchStateList" :key="index"
										:label="item.typeName" :value="item.id"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="章节时长：">
								<el-input v-model="courseContent.courseDuration" class="handle-input mr10"
									placeholder="时长/秒(保存后将自动转换格式)"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 秒
							</el-form-item>

							<el-form-item label="* 章节内容：">
								<el-input v-model="courseContent.courseContent" type="textarea"
									class="handle-input mr10" placeholder='请输入章节内容（可使用分隔符“|”进行换行）'
									:autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="章节视频：">
								<el-input v-model="courseContent.videoUrl" type="textarea" class="handle-input mr10"
									placeholder="请输入章节视频地址（支持.mp4/.avi等）" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="章节音频：">
								<el-input v-model="courseContent.audioUrl" type="textarea" class="handle-input mr10"
									placeholder="请输入章节音频地址（支持.mp3/.m4a等）" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="章节文档：">
								<el-input v-model="courseContent.fileUrl" type="textarea" class="handle-input mr10"
									placeholder="请输入章节文档地址（支持.pdf/.ppt/.doc/.xls等）"
									:autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="courseContentModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addCourseContent()" type="success" size="small">确定添加
					</el-button>
				</div>

			</div>
		</el-dialog>


		<!-- 添加课程标签 -->
		<el-dialog :visible.sync="keyListModal" width="60%" title="添加课程关键词标签" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;" v-if="choiceIndex==0&&course">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-checkbox-group v-model="course.keyListArray">
						<el-checkbox-button v-for="(val,index) in keyList" :key="index" :label="val.text">{{ val.text }}
						</el-checkbox-button>
					</el-checkbox-group>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="keyListModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addKeyList()" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

		<!-- 添加查看权限 -->
		<el-dialog :visible.sync="authIdListModal" width="60%" title="添加查看权限" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;" v-if="course">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-checkbox-group v-model="course.authListArray" value-key="id">
						<el-checkbox-button v-for="(val,index) in authList" :key="index" :label="val.id">{{ val.name }}
						</el-checkbox-button>
					</el-checkbox-group>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="authIdListModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addAuthIdList()" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

		<!-- 添加分组查看权限 -->
		<el-dialog :visible.sync="groupRoleListModal" width="60%" title="添加分组查看权限" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;" v-if="choiceIndex==1">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-checkbox-group v-model="list[choiceItemIndex].roleListArray" value-key="id">
						<el-checkbox-button v-for="(val,index) in roleList" :key="index" :label="val.id">{{ val.name }}
						</el-checkbox-button>
					</el-checkbox-group>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="groupRoleListModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addGroupRoleList()" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

		<el-dialog :visible.sync="courseContentIdListModal" width="60%" title="添加课程章节分组内容" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;"
				v-if="courseChapterList[choiceItemContentIndex]">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-checkbox-group v-model="courseChapterList[choiceItemContentIndex].courseContentIdListArray"
						value-key="id">
						<el-checkbox-button v-for="(val,index) in courseContentList" :key="index"
							:label="val.id">{{index+1}}：{{ val.title }}
						</el-checkbox-button>
					</el-checkbox-group>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="courseContentIdListModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="courseContentIdListModal=false" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

		<el-dialog title="章节内容详情" :visible.sync="openContentText" width="80%">
			<div style="display: flex">
				<el-button @click="toUpdateContent1" type="success" icon="Check">确定
				</el-button>
				<el-button @click="openContentText=false">取消
				</el-button>
			</div>
			<Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig"
				mode="default" />
			<Editor style="height: 500px; overflow-y: hidden;" v-model="courseContentText" :defaultConfig="editorConfig"
				mode="default" @onCreated="onCreated" />
		</el-dialog>

		<el-dialog title="课程简介编辑" :visible.sync="openContentText1" width="80%">
			<div style="display: flex;margin-bottom: 20px;">
				<el-button @click="openContentText1=false" type="success" icon="Check">确定
				</el-button>
				<el-button @click="openContentText1=false">取消
				</el-button>
				<el-button @click="openView(course)" type="primary" size="small" icon="el-icon-zoom-in">预览
				</el-button>
			</div>
			<Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig"
				mode="default" />
			<Editor style="height: 500px; overflow-y: hidden;" v-model="course.courseRemark"
				:defaultConfig="editorConfig" mode="default" @onCreated="onCreated" />
		</el-dialog>

		<!-- 添加课程标签 -->
		<el-dialog :visible.sync="studyAddModal" width="60%" title="学员开课" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;">
				<el-form ref="newCourseStudyRef" :model="newCourseStudy" :rules="rules">
					<el-row>
						<el-col :span="6">
							<el-form-item label="学员手机号" style="margin-right: 20px" prop="phone">
								<el-input v-model="newCourseStudy.phone" placeholder="请输入学员手机号"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="课程" style="margin-right: 20px" prop="courseId">
								<el-select v-model="newCourseStudy.courseId" filterable>
									<el-option v-for="(item,index) in courseList" :key="index"
										:label="item.id+':'+item.courseTitle+'-￥'+item.courseAmount"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="studyAddModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addNewCourseStudy()" type="success" size="small">确定开通
					</el-button>
				</div>
			</div>
		</el-dialog>

		<!-- 站点预览 -->
		<el-drawer size="25%" :with-header="false" :visible.sync="viewModal" direction="rtl" :show-close="false">
			<iframe id="iframeId" :src="viewPageUrl" frameborder="0" style="width: 400px;height: 800px;margin: 0 auto;"
				scrolling="auto">
			</iframe>
		</el-drawer>

		<!--视频预览-->
		<el-dialog :visible.sync="videoModal" width="40%" title="视频预览" :mask-closable="false">
			<video style="width: 100%;height: auto;margin: 0 auto;" :controls="true" :enable-progress-gesture="true"
				:initial-time="0" :show-center-play-btn="true" autoplay :src="videoUrl" custom-cache="false">
			</video>
		</el-dialog>

		<!--素材预览-->
		<el-dialog :visible.sync="fileModal" width="60%" title="素材预览" :mask-closable="false" append-to-body center>
			<pdf v-for="item in numPages" :key="item" :src="fileUrl" :page="item" ref="pdf" v-if="openFileType==0">
			</pdf>
			<iframe :src="fileUrl" width="100%" height="500px" frameborder="0" v-if="openFileType==1"></iframe>
			<video style="width: 100%;height: auto;margin: 0 auto;" :controls="true" :enable-progress-gesture="true"
				:initial-time="0" :show-center-play-btn="true" autoplay :src="fileUrl" custom-cache="false"
				v-if="openFileType==2" />
		</el-dialog>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

	</div>
</template>

<script>
	import Vue from 'vue';
	import pdf from 'vue-pdf'
	import g2char from "@/components/page/agent/char/g2char";
	import {
		Editor,
		Toolbar
	} from '@wangeditor/editor-for-vue'
	export default {
		name: "courseList",
		components: {
			"g2char": g2char,
			Editor,
			Toolbar
		},
		data() {
			return {
				url: '',
				imageUrl: '',
				videoUrl: '',
				fileUrl: '',
				openFileType: false,
				numPages: null,
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				excelUploadId: 1,
				excelUploadUrl: "",
				fileUploadId: 1,
				fileUploadUrl: "",
				viewPageUrl: "",
				// 测试时使用
				// excelUploadUrlOrgin: "http://localhost:8063/courseContent/excelImport",
				excelUploadUrlOrgin: "https://biapi.xiaoyujia.com/courseContent/excelImport",

				// 测试时使用
				// fileUploadUrlOrgin: "http://localhost:8063/courseContent/fileUpload",
				fileUploadUrlOrgin: "https://biapi.xiaoyujia.com/courseContent/fileUpload",

				deleteTips: "确定删除选中章节吗？该操作无法恢复，请谨慎操作!",
				isEdit: false,
				showEdit: true,
				imgModal: false,
				videoModal: false,
				viewModal: false,
				fileModal: false,
				courseModal: false,
				courseGroupModal: false,
				courseTypeModal: false,
				courseContentModal: false,
				keyListModal: false,
				authIdListModal: false,
				courseContentIdListModal: false,
				groupRoleListModal: false,
				studyAddModal: false,
				openContentText: false,
				openContentText1: false,
				courseContentText: '',
				newCourseStudy: {
					courseId: null,
					memberId: null,
					phone: null
				},
				detailType: 0,

				editor: null,
				toolbarConfig: {},
				editorConfig: {
					placeholder: "请输入内容...",
					readOnly: false,
					MENU_CONF: {
						//上传参考https://www.wangeditor.com/v5/menu-config.html#%E5%9F%BA%E6%9C%AC%E9%85%8D%E7%BD%AE
						uploadImage: {
							server: `https://api.xiaoyujia.com/system/imageUpload`,
							// 超时时间，默认为 10 秒
							timeout: 30 * 1000, // 5s
							fieldName: "file",
							// 将 meta 拼接到 url 参数中，默认 false
							metaWithUrl: false, // join params to url
							// 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
							meta: {
								watermark: 1,
							},
							// 自定义增加 http  header
							// headers: {
							//   Accept: "text/x-json",
							//   otherKey: "xxx"
							// },
							// 跨域是否传递 cookie ，默认为 false
							withCredentials: true,
							// 自定义增加 http  header
							// headers: {
							//   Accept: "text/x-json",
							//   otherKey: "xxx"
							// },
							maxFileSize: 10 * 1024 * 1024, // 10M
							base64LimitSize: 5 * 1024, // insert base64 format, if file's size less than 5kb
							// 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
							allowedFileTypes: ["image/*"],
							// 最多可上传几个文件，默认为 100
							maxNumberOfFiles: 30,
							onBeforeUpload(file) {
								console.log("onBeforeUpload", file);
								return file; // will upload this file
								// return false // prevent upload
							},
							onProgress(progress) {
								console.log("onProgress", progress);
							},
							onSuccess(file, res) {
								console.log("onSuccess", file, res);
							},
							onFailed(file, res) {
								alert(res.message);
								console.log("onFailed", file, res);
							},
							onError(file, err, res) {
								alert(err.message);
								console.error("onError", file, err, res);
							},
							// 自定义插入图片
							customInsert(res, insertFn) {
								insertFn(res.data, '消息图片', '消息图片')
							},
						},
						uploadVideo: {
							server: "https://api.xiaoyujia.com/system/uploadFile",
							fieldName: "file",
							// 单个文件的最大体积限制，默认为 10M
							maxFileSize: 100 * 1024 * 1024, // 100M
							// 最多可上传几个文件，默认为 5
							maxNumberOfFiles: 5,
							// 选择文件时的类型限制，默认为 ['video/*'] 。如不想限制，则设置为 []
							allowedFileTypes: ["video/*"],
							// 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
							meta: {

							},
							// 将 meta 拼接到 url 参数中，默认 false
							metaWithUrl: false,
							// 自定义增加 http  header
							// headers: {
							//   Accept: "text/x-json",
							//   otherKey: "xxx"
							// },
							// 跨域是否传递 cookie ，默认为 false
							withCredentials: true,
							// 超时时间，默认为 30 秒
							timeout: 30 * 1000, // 15 秒
							// 上传进度的回调函数
							onProgress(progress) {
								console.log("progress", progress);
							},
							// 单个文件上传成功之后
							onSuccess(file, res) {
								console.log(`${file.name} 上传成功`, res);
							},
							// 单个文件上传失败
							onFailed(file, res) {
								console.log(`${file.name} 上传失败`, res);
							},
							// 上传错误，或者触发 timeout 超时
							onError(file, err, res) {
								console.log(`${file.name} 上传出错`, err, res);
							},
							// 自定义插入视频
							customInsert(res, insertFn) {
								insertFn(res.data, '消息视频', '消息视频')
							},
						}
					}
				},

				loading: true,
				pageSizeOpts: [10, 20, 50, 100, 200],
				list: [],
				logList: [],
				logList1: [],
				logList2: [],
				logList3: [],
				logTotal: 0,
				logTotal1: 0,
				logTotal2: 0,
				logTotal3: 0,
				keyList: [],
				roleList: [],
				courseList: [],
				authList: [{
					id: 1,
					name: '普通会员'
				}, {
					id: 2,
					name: '行政员工'
				}, {
					id: 3,
					name: '服务员工'
				}, {
					id: 4,
					name: '保姆月嫂'
				}, {
					id: 5,
					name: '共创店'
				}, {
					id: 6,
					name: '合伙人'
				}, {
					id: 7,
					name: '门店店长'
				}, {
					id: 8,
					name: '陪跑店店长'
				}],
				course: {},
				courseGroup: {},
				courseType: {},
				courseContent: {},
				chapter: {},
				courseContentList: [],
				courseChapterList: [],
				courseId: 1,
				courseName: '',
				courseNum: 1,
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				choiceIndex: '0',
				choiceIndexNow: '0',
				choiceItemIndex: 0,
				choiceItemContentIndex: 0,
				choiceIndexCourse: '0',
				choiceItem: {},
				uploadFileType: 0,

				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				searchType: null,
				searchGroup: null,
				searchState: null,
				quer: {
					search: "",
					typeIdList: null,
					groupIdList: null,
					courseState: null,
					courseTypeId: null,
					isEnabled: null,
					orderBy: "CreateTime ASC",
					current: 1,
					size: 10
				},
				switchStateList: [{
						id: 0,
						typeName: "关闭",
						type: "info"
					},
					{
						id: 1,
						typeName: "开启",
						type: "success"
					}
				],
				typeStyleList: ['success', 'info', 'warning', 'primary'],
				stateList: [{
					id: 0,
					value: false,
					text: "下架",
					type: "info"
				}, {
					id: 1,
					value: true,
					text: "上架",
					type: "success"
				}],
				groupList: [],
				typeList: [],
				certList: [],
				examList: [],
				teacherList: [],
				campusList: [],
				rules: {
					name: [{
						required: true,
						message: '请输入姓名',
						trigger: 'blur'
					}, ],

					date1: [{
						type: 'date',
						required: true,
						message: '请选择日期',
						trigger: 'change'
					}],
					position: [{
						required: true,
						message: '请输入职位',
						trigger: 'blur'
					}],
					introduce: [{
						required: true,
						message: '内容不能为空',
						trigger: 'blur'
					}],
					workDeeds: [{
						required: true,
						message: '内容不能为空',
						trigger: 'blur'
					}],
					phone: [{
						required: true,
						message: "请填写手机号",
						trigger: "change"
					}, {
						pattern: /^((0\d{2,3}-\d{7,8})|(1[345789]\d{9}))$/,
						message: "请填写正确的手机号"
					}],
					courseId: [{
						required: true,
						message: '请选择要开通的课程',
						trigger: 'blur'
					}],
				},
				searchTime: [],
				pickerOptions: {
					shortcuts: [{
						text: '今天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '昨天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}],
				},
			}
		},
		created() {
			if (this.$route.query.choiceIndex) {
				this.choiceIndex = this.$route.query.choiceIndex
			}
			this.getData()
		},
		methods: {
			onCreated(editor) {
				this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
			},
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceIndex = key
				this.expands = []
				this.showEdit = true
				this.reset()
			},
			// 切换栏目
			handleSelectCourse(key, keyPath) {
				this.choiceIndexCourse = key
				if (this.choiceIndexCourse == '1') {
					this.courseChapterList = []
					this.courseContentList = this.course.courseContent
					this.listCourseChapter(this.course.id)
				} else if (this.choiceIndexCourse == '2') {
					this.courseContentList = this.course.courseContent
				}
			},
			async getData() {
				this.choiceIndexNow = 3
				if (this.choiceIndex == 0) {
					await this.getDict()
					// 获取课程列表
					this.$postData("getCoursePage", this.quer).then(res => {
						this.loading = false
						if (res.status == 200) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
							// 追加编辑状态位
							for (let item of this.list) {
								this.$set(item, "isEdit", false)
								// 追加课程标签
								let courseKeyWords = item.courseKeyWords || ''
								this.$set(item, "keyListArray", courseKeyWords.split(','))

								// 追加课程查看权限
								this.$set(item, "authListArray", this.strToArray(item.authList))
							}
							this.choiceIndexNow = this.choiceIndex
						} else {
							this.list = []
							this.choiceIndexNow = this.choiceIndex
							this.$message.error('未查询到相关课程记录!')
						}
					})
					this.getCourseGroup()
					this.getCourseType()
					this.getCertList()
					this.getExamList()
					this.listCourseTeacher()
					this.listCampus()
				} else if (this.choiceIndex == 1) {
					await this.getAllRole()
					this.$postData("getCourseGroupPage", this.quer).then(res => {
						this.loading = false
						if (res.status == 200) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
							// 追加编辑状态位
							for (let item of this.list) {
								this.$set(item, "isEdit", false)
								this.$set(item, "roleListArray", this.strToArray(item.roleList))
								this.$set(item, "authListArray", this.strToArray(item.authList))
							}
							this.choiceIndexNow = this.choiceIndex
						} else {
							this.list = []
							this.choiceIndexNow = this.choiceIndex
							this.$message.error('未查询到相关课程分组记录!')
						}
					})
				} else if (this.choiceIndex == 2) {
					// 处理一下时间筛选字段
					if (this.searchTime && this.searchTime.length >= 2) {
						this.quer.startTime = this.searchTime[0]
						this.quer.endTime = this.searchTime[1]
					} else {
						this.quer.startTime = null
						this.quer.endTime = null
					}
					this.listCourseLogVo(1)
					this.listCourseLogVo(2)
					this.listCourseLogVo(3)
					this.listCourseLogVo(4)
					this.$postData("pageCourseVo", this.quer).then(res => {
						this.loading = false
						if (res.status == 200) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
							this.choiceIndexNow = this.choiceIndex
						} else {
							this.list = []
							this.choiceIndexNow = this.choiceIndex
							this.$message.error('未查询到相关课程统计信息记录!')
						}
					})
				} else if (this.choiceIndex == 3) {
					this.listCourse()
					if (!this.quer.search) {
						this.list = []
						this.loading = false
						return
					}
					$.ajax({
						type: "GET",
						url: 'https://api.xiaoyujia.com/course/getStudyDuration/' + this.quer.search,
						success: res => {
							this.loading = false
							if (res.code == 0) {
								this.list = res.data
								this.choiceIndexNow = this.choiceIndex
							} else {
								this.list = []
								this.$message.error('未查询到会员学习记录!')
							}
						},
					})
				}
			},
			listCourseLogVo(type) {
				this.$postData("listCourseLogVo", {
					logType: type,
					startTime: this.quer.startTime,
					endTime: this.quer.endTime
				}).then(res => {
					this.loading = false
					if (res.status == 200) {
						let sourceList = [{
								id: 0,
								response: "未知",
								num: 0,
							},
							{
								id: 1,
								response: "H5",
								num: 0,
							},
							{
								id: 2,
								response: "APP",
								num: 0,
							},
							{
								id: 3,
								response: "微信小程序",
								num: 0,
							},
							{
								id: 4,
								response: "抖音小程序",
								num: 0,
							},
							{
								id: 5,
								response: "支付宝小程序",
								num: 0,
							}
						]

						let result = []
						let total = 0
						for (let item of sourceList) {
							for (let item1 of res.data) {
								if (item1.logSource == item.id) {
									item.num = item1.num
									total += item1.num
								}
							}
							result.push(item)
						}

						if (type == 1) {
							this.logList = result
							this.logTotal = total
						} else if (type == 2) {
							this.logList1 = result
							this.logTotal1 = total
						} else if (type == 3) {
							this.logList2 = result
							this.logTotal2 = total
						} else if (type == 4) {
							this.logList3 = result
							this.logTotal3 = total
						}
					}
				})
			},
			getDict() {
				this.$postUrl("get_dict", 250, null, {}).then(res => {
					if (res.status == 200) {
						this.keyList = res.data
					} else {
						this.keyList = []
					}
				})
			},
			getCourseDetail(id) {
				$.ajax({
					type: "GET",
					url: 'https://biapi.xiaoyujia.com/course/getCourseById/' + id,
					success: res => {
						if (res.status == 200) {
							this.course = res.data
							this.$set(this.course, "isEdit", false)
							let courseKeyWords = this.course.courseKeyWords || ''
							this.$set(this.course, "keyListArray", courseKeyWords.split(','))
							this.$set(this.course, "authListArray", this.strToArray(this.course.authList))
						} else {
							this.$message.error('未查询到课程详细信息!')
						}
					},
				})
			},
			listCourseChapter(id) {
				$.ajax({
					type: "GET",
					url: "https://biapi.xiaoyujia.com/courseContent/listCourseChapter/" + id,
					success: res => {
						if (res.status == 200) {
							this.courseChapterList = res.data
							for (let item of this.courseChapterList) {
								let idList = item.courseContentIdList || ''
								this.$set(item, "courseContentIdListArray", this.strToArray(idList))
							}
						}
					},
				})
			},
			// 获取课程列表
			listCourse() {
				if (this.courseList.length) {
					return
				}
				this.$postData("listCourse", {}).then(res => {
					if (res.status == 200) {
						this.courseList = res.data
					}
				})
			},
			// 获取所有角色
			getAllRole() {
				this.$getData("getAllRole").then(res => {
					if (res.status == 200) {
						this.roleList = res.data
					} else {
						this.roleList = []
						this.$message.error('未查询到角色相关记录!')
					}
				})
			},
			// 获取课程分组列表
			getCourseGroup() {
				this.$getData("getCourseGroup").then(res => {
					if (res.status == 200) {
						this.groupList = res.data
					} else {
						this.list = []
						this.$message.error('未查询到课程类型相关记录!')
					}
				})
			},
			// 获取课程类型列表
			getCourseType() {
				this.$getData("getCourseType").then(res => {
					if (res.status == 200) {
						this.typeList = res.data
						// this.formatLevel()
					} else {
						this.list = []
						this.$message.error('未查询到课程类型相关记录!')
					}
				})
			},
			// 获取证书列表
			getCertList() {
				this.$getData("getAllUnionCertificates").then(res => {
					if (res.status == 200) {
						this.certList = res.data
					} else {
						this.certList = []
						// this.$message.error('未查询到证书相关记录!')
					}
				})
			},
			// 获取考试列表
			getExamList() {
				this.$postData("getUnionExam", {}).then(res => {
					if (res.status == 200) {
						this.examList = res.data
					} else {
						this.examList = []
						// this.$message.error('未查询到证书相关记录!')
					}
				})
			},
			// 获取导师列表
			listCourseTeacher() {
				$.ajax({
					type: "POST",
					url: 'https://api.xiaoyujia.com/course/listCourseTeacher',
					data: JSON.stringify({
						state: 1
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.teacherList = res.data
						}
					},
				})
			},
			// 获取校区列表
			listCampus() {
				$.ajax({
					type: "POST",
					url: 'https://api.xiaoyujia.com/course/listCampus',
					data: JSON.stringify({
						campusState: 1,
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.campusList = res.data
						}
					},
				})
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				let data = []
				let i = 0
				let length = this.typeList.length
				let isFindType = false
				// 格式化搜索项
				// for (i = 0; i < length; i++) {
				// 	if (this.searchType == i + "") {
				// 		data = []
				// 		data.push(Number(i))
				// 		this.quer.typeIdList = data
				// 		isFindType = true
				// 	} else {
				// 		if (this.searchType == "" && this.searchType !== 0) {
				// 			this.quer.typeIdList = null
				// 		}
				// 	}
				// }

				if (!this.searchType) {
					this.quer.typeIdList = null
				} else {
					data = []
					data.push(Number(this.searchType))
					this.quer.typeIdList = data
				}

				if (!this.searchGroup) {
					this.quer.groupIdList = null
				} else {
					data = []
					data.push(Number(this.searchGroup))
					this.quer.groupIdList = data
				}

				if (this.searchState != null) {
					if (this.choiceIndex == 0) {
						this.quer.courseState = this.searchState
					} else if (this.choiceIndex == 1) {
						this.quer.groupState = this.searchState
					}
				}

				// 遍历到最后没有找到对应的课程类型，则随便赋一个值
				// if (!isFindType && this.searchType !== "" && this.searchType !== null) {
				// 	this.quer.typeIdList = [999]
				// }
				this.getData()
			},
			// 重置
			reset() {
				this.loading = true

				this.quer.search = ""
				this.quer.typeIdList = null
				this.quer.groupIdList = null
				this.quer.courseState = null
				this.quer.isEnabled = null
				this.quer.startTime = null
				this.quer.endTime = null
				this.quer.courseTypeId = null
				this.searchTime = []
				this.quer.orderBy = "CreateTime ASC"
				this.quer.current = 1

				this.searchType = null
				this.searchGroup = null
				this.searchState = null
				this.isEdit = false


				if (this.choiceIndex == 0) {
					this.quer.orderBy = "CreateTime ASC"
				} else if (this.choiceIndex == 1) {
					this.quer.orderBy = "cg.creTime ASC"
				}
				this.getData()
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
						this.choiceItemIndex = this.getIndex1(row.id, this.list)
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 多选
			handleSelectionChange(val) {
				this.multipleSelection = val
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "课程封面") {
					tips = "推荐尺寸：1500*900（5:3）"
				} else if (column.label == "详情页图片") {
					tips = "推荐尺寸：1500*高度（长图高度自适应）"
				} else if (column.label == "首页推荐") {
					tips = "是否显示在课程首页推荐列表"
				} else if (column.label == "店长推荐") {
					tips = "是否显示在店长主页推荐列表"
				} else if (column.label == "课程评分") {
					tips = "后台自动计算或手动输入，将展示给用户"
				} else if (column.label == "分组图片") {
					tips = "推荐尺寸：750*750（1:1）"
				} else if (column.label == "分组图标") {
					tips = "推荐尺寸：200*200（1:1）"
				} else if (column.label == "课程查看权限") {
					tips = "设置哪类人员可以查看该课程，未设置则所有人可见"
				} else if (column.label == "分组查看权限") {
					tips = "设置哪类人员可以查看该分组，未设置则所有人可见"
				} else if (column.label == "分组角色权限") {
					tips = "设置哪些员工角色可以查看该分组，未设置则所有人可见"
				} else if (column.label == "关键词") {
					tips = "支持搜索，前端优先显示前三个标签（课程首页推荐位只显示必修课）"
				} else if (column.label == "课程类型") {
					tips = "带有“家姐课堂”课程类型的课程，将会显示在对应小程序中"
				} else if (column.label == "是否必修") {
					tips = "计算考试课程进度时，只以必修章节为准（默认都为必修章节）"
				} else if (column.label == "问卷调查") {
					tips = "报名前是否强制用户填写问卷"
				} else if (column.label == "课程简介") {
					tips = "支持图文形式，推荐直接制作并上传课程详情图替代此项"
				} else if (column.label == "分组章节内容") {
					tips = "可包含多个章节内容，将在课程章节展示时进行聚合"
				} else if (column.label == "分组序号") {
					tips = "在展示时根据序号升序排列"
				}

				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			// 字符串校验及格式化
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			// 格式化课程时间
			formatCourseDuration(courseDuration, defaultStr) {
				if (!defaultStr) {
					defaultStr = '暂未录入'
				}
				let result = ""
				let sencond = 0
				let min = 0
				let hour = 0
				let day = 0
				// 小于一分钟
				if (courseDuration <= 60) {
					sencond = Math.floor(parseFloat(courseDuration / 1.0))
					if (sencond == 0) {
						result = defaultStr
					} else {
						result = sencond + "秒"
					}
				}

				// 小于二小时
				if (courseDuration > 60 && courseDuration <= 3600) {
					min = Math.floor(parseFloat(courseDuration / 60.0))
					sencond = Math.floor(courseDuration - (min * 60)).toFixed(0)
					if (min == 0) {
						result = defaultStr
					} else {
						result = min + "分钟" + sencond + "秒"
					}
				}
				// 大于二小时 小于一天
				if (courseDuration > 3600 && courseDuration <= 86400) {
					hour = Math.floor(parseFloat(courseDuration / 3600))
					min = parseFloat((courseDuration - (hour * 3600)) / 60).toFixed(0)
					if (min == 0) {
						result = hour + "小时"
					} else {
						result = hour + "小时" + min + "分钟"
					}
				}
				// 大于一天
				else if (courseDuration > 86400) {
					day = Math.floor(parseFloat(courseDuration / 86400))
					hour = parseFloat((courseDuration - (day * 86400)) / 3600).toFixed(0)
					if (hour == 0) {
						result = day + "天"
					} else {
						result = day + "天" + hour + "小时"
					}
				}
				return result
			},
			// 格式化工作类型
			formatWorkType(value) {
				let data = ["保姆", "月嫂", "育婴师", "护工", "其他"]
				switch (value) {
					case 10:
						return data[0]
						break
					case 20:
						return data[1]
						break
					case 30:
						return data[2]
						break
					case 40:
						return data[3]
						break
					case 50:
						return data[4]
						break
				}
			},
			// 格式化等级
			formatLevel() {
				// let data = ["不通过", "未认证", "普通", "金牌", "翡翠", "钻石"]
				for (let item of this.list) {
					for (let item1 of this.groupList) {
						if (item.level == item1.value) {
							item.levle = item1.text
						}
					}
				}
			},
			// 格式化课程类型
			formatType(val) {
				let result = "暂无"
				let id = val.courseTypeId
				for (let item of this.typeList) {
					if (id == item.id) {
						result = item.typeName
						break
					}
				}
				return result
			},
			// 格式化分组
			formatGroup(val) {
				let result = "暂无"
				let id = val.courseGroupId
				for (let item of this.groupList) {
					if (id == item.id) {
						result = item.groupName
						break
					}
				}
				return result
			},
			// 格式化证书显示
			formatCert(val) {
				let result = "暂未录入"
				let id = val.certId
				for (let item of this.certList) {
					if (id == item.id) {
						result = item.certCodeFixed
						break
					}
				}
				return result
			},
			// 格式化考试显示
			formatExam(val) {
				let result = "暂未录入"
				let id = val.examId
				for (let item of this.examList) {
					if (id == item.id) {
						result = item.examTitle
						break
					}
				}
				return result
			},
			addNewCourseStudy() {
				this.$refs.newCourseStudyRef.validate(valid => {
					if (valid) {
						$.ajax({
							type: "POST",
							url: 'https://api.xiaoyujia.com/course/addCourseStudyFree',
							data: JSON.stringify(this.newCourseStudy),
							dataType: "json",
							contentType: "application/json",
							success: res => {
								if (res.code == 0) {
									// this.newCourseStudy.phone = ''
									this.$message.success('课程开通成功！')
									this.getData()
								} else {
									this.$message.error(res.msg)
								}
							},
						})
					}
				});
			},
			// 课程章节Excel导入
			excelImport(index) {
				let id = this.list[index].id
				let excelUploadUrl = this.excelUploadUrlOrgin
				excelUploadUrl += "/" + id
				this.excelUploadUrl = excelUploadUrl
				this.excelUploadId = index
			},
			beforeFileUpload() {
				this.loading = true
			},
			// 课程章节素材文件上传
			fileUpload(index, value) {
				this.choiceItemContentIndex = index
				this.uploadFileType = value
				let id = this.list[this.getIndex(this.expands[0])].courseContent[index].id
				let fileUploadUrl = this.fileUploadUrlOrgin
				fileUploadUrl += "/" + id + "/" + value
				this.fileUploadUrl = fileUploadUrl
				this.fileUploadId = index
			},
			getIndex1(id, list) {
				return list.findIndex(item => item.id === id)
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 课程章节Excel模板下载
			excelDownload() {
				this.$postData("courseContentExcelDownload", null, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('课程章节Excel模板下载成功!')
					this.blobExport({
						tablename: "课程章节Excel模板",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// Excel表格上传成功
			excelUploadSuccess(res, file) {
				this.$message.success('章节导入（' + res.data.length + '条）成功！点击保存按钮即可录入哦！')
				// 将解析到的结果加入到章节列表中
				for (let item of res.data) {
					this.list[this.excelUploadId].courseContent.push(item)
				}
			},
			// Excel表格上传失败
			excelUploadError(err) {
				this.$message.error('章节内容解析失败！请使用正确的Excel模板进行上传！')
			},
			// 章节素材文件上传成功
			fileUploadSuccess(res, file) {
				this.loading = false
				if (this.uploadFileType == 1) {
					this.list[this.getIndex(this.expands[0])].courseContent[this.choiceItemContentIndex].audioUrl = res
						.data
				} else if (this.uploadFileType == 2) {
					this.list[this.getIndex(this.expands[0])].courseContent[this.choiceItemContentIndex].fileUrl = res
						.data
				}
				this.$message.success('章节素材文件上传成功！')
			},
			// 章节素材文件上传失败
			fileUploadError(err) {
				this.loading = false
				this.$message.error('章节素材文件上传失败！请重试！' + err.msg)
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.list[this.uploadId].courseImg = res.data
				} else if (this.uploadImgType == 1) {
					this.list[this.uploadId].courseDetailUrl = res.data
				} else if (this.uploadImgType == 2) {
					this.list[this.getIndex(this.expands[0])].courseContent[this.uploadId].courseImgUrl = res.data
				} else if (this.uploadImgType == 3) {
					this.$set(this.course, "courseImg", res.data)
				} else if (this.uploadImgType == 4) {
					this.$set(this.course, "courseDetailUrl", res.data)
				} else if (this.uploadImgType == 5) {
					this.$set(this.courseContent, "courseImgUrl", res.data)
				} else if (this.uploadImgType == 6) {
					this.list[this.uploadId].groupImg = res.data
				} else if (this.uploadImgType == 7) {
					this.list[this.uploadId].groupIcon = res.data
				} else if (this.uploadImgType == 8) {
					this.$set(this.courseGroup, "groupImg", res.data)
				} else if (this.uploadImgType == 9) {
					this.$set(this.courseGroup, "groupIcon", res.data)
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadId = index
			},
			// 打开图片预览
			openImg(url) {
				if (url != null && url != '') {
					this.imageUrl = url
				} else {
					this.imageUrl = this.blankImg
				}
				this.imgModal = true
			},
			// 打开视频预览
			openVideo(url) {
				if (url != null && url != '') {
					this.videoUrl = url
				}
				this.videoModal = true
			},
			// 打开文档预览
			openFile(url) {
				if (url) {
					if (url.includes('.pdf')) {
						this.openFileType = 0
						this.fileUrl = url
						this.getNumPages()
					} else if (url.includes('.mp3') || url.includes('.m4a')) {
						this.openFileType = 2
						this.fileUrl = url
					} else {
						this.openFileType = 1
						this.fileUrl = 'https://view.officeapps.live.com/op/view.aspx?src=' + url
					}
				}
				this.fileModal = true
			},
			// 打开预览
			openView(val) {
				let id = val.id
				this.viewPageUrl = "https://jiajie.xiaoyujia.com/pages-mine/studyCenter/course-detail?id=" + id
				this.viewModal = true
			},
			// 将字符串转化为数组
			strToArray(val) {
				let arrayString = []
				let array = []
				arrayString = val.split(',')
				arrayString.forEach(it => {
					let value = parseInt(it)
					if (value != null && value != 0) {
						array.push(value)
					}
				})
				return array
			},
			getNumPages() {
				let loadingTask = pdf.createLoadingTask(this.fileUrl)
				loadingTask.promise.then(pdf => {
					this.numPages = pdf.numPages
				}).catch(err => {
					console.error('pdf 加载失败', err);
				})
			},
			openModal(index, index1) {
				if (index == 0) {
					this.detailType = 0
					this.choiceIndexCourse = '0'
					this.course = {}
					this.courseModal = true
				} else if (index == 1) {
					this.courseContent = {}
					this.courseContentModal = true
					this.courseId = this.list[index1].id
					this.courseName = this.list[index1].courseTitle
					let length = this.list[index1].courseContent.length
					if (length == 0) {
						this.courseNum = 1
					} else {
						this.courseNum = this.list[index1].courseContent[length - 1].courseNum + 1
					}
					this.isEdit = true
					this.$set(this.courseContent, "courseNum", this.courseNum)
				} else if (index == 2) {
					this.courseType = {}
					this.courseTypeModal = true
				} else if (index == 3) {
					this.courseGroup = {}
					this.courseGroupModal = true
				} else if (index == 4) {
					// this.choiceItemIndex = index1
					this.authIdListModal = true
				} else if (index == 5) {
					this.choiceItemIndex = index1
					this.groupRoleListModal = true
				} else if (index == 6) {
					// this.choiceItemIndex = index1
					this.keyListModal = true
				} else if (index == 7) {
					this.getCourseDetail(this.list[index1].id)
					setTimeout(() => {
						this.detailType = 1
						this.choiceIndexCourse = '0'
						this.choiceItemIndex = index1
						this.courseModal = true
					}, 500)
				} else if (index == 8) {
					window.open(
						'https://vod.console.aliyun.com/?spm=5176.12672711.categories-n-products.dvod.8f791fa37JVfKI#/media/video/list'
					)
				} else if (index == 9) {
					// this.chapter = this.courseChapterList[index1]
					this.choiceItemContentIndex = index1
					this.courseContentIdListModal = true
				} else if (index == 10) {
					this.newCourseStudy = {}
					this.studyAddModal = true
				}
			},
			// 打开证书管理
			openCert() {
				this.courseModal = false
				window.open('/certificate', '_blank')
			},
			openExam() {
				this.courseModal = false
				window.open('/exam', '_blank')
			},
			openTeacher() {
				this.courseModal = false
				window.open('/courseTeacher', '_blank')
			},
			openCampus() {
				this.courseModal = false
				window.open('/campus', '_blank')
			},
			//打开员工信息详情
			rowClick(id) {
				// this.baomuId = baomuId
				// this.courseModal = true
				// this.loading = true
				// this.getBaomuDetail(baomuId)
			},
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			// 添加课程
			addCourse() {
				let course = this.course
				// 数据校验
				if (course.courseTitle == null) {
					this.$message.error('请填写课程标题！')
				} else if (course.courseRemark == null) {
					this.$message.error('请填写课程简介！')
				}
				// else if (course.courseImg == null) {
				// 	this.$message.error('请上传课程封面！')
				// } 
				else if (course.courseTypeId == null) {
					this.$message.error('请选择课程类型！')
				} else if (course.courseAmount == null) {
					this.$message.error('请填写课程价格！')
				}
				// else if (course.courseDuration == null) {
				// 	this.$message.error('请填写课程时长！')
				// }
				// else if (course.videoUrl == null) {
				// 	this.$message.error('请上传课程简介视频！')
				// } 
				// else if (course.courseDetailUrl == null) {
				// 	this.$message.error('请上传课程详情图片！')
				// } 
				else {
					let no = localStorage.getItem("account")
					if (no == undefined) {
						no = "admin"
					}
					this.$set(course, "creater", no)
					this.$set(course, "courseState", 1)
					this.$postData("addCourse", course).then(res => {
						if (res.status == 200) {
							this.$message.success('课程添加成功!')
							this.courseModal = false
							this.course = {}
							this.getData()
						} else {
							this.$message.error('课程添加失败！' + res.msg)
						}
					})
				}
			},
			// 添加课程分组
			addCourseGroup() {
				let courseGroup = this.courseGroup
				// 数据校验
				if (!courseGroup.groupName) {
					this.$message.error('请填写课程分组名称！')
				} else if (!courseGroup.groupContent) {
					this.$message.error('请填写课程分组内容！')
				} else {
					let no = localStorage.getItem("account")
					if (no == undefined) {
						no = "admin"
					}
					this.$set(courseGroup, "creater", no)
					this.$postData("addCourseGroup", courseGroup).then(res => {
						if (res.status == 200) {
							this.$message.success('课程分组添加成功!')
							this.courseGroupModal = false
							this.getCourseType()
						} else {
							this.$message.error('课程分组添加失败！' + res.msg)
						}
					})
				}
			},
			// 添加课程类型
			addCourseType() {
				let courseType = this.courseType
				// 数据校验
				if (courseType.typeName == null) {
					this.$message.error('请填写课程类型名称！')
				} else if (courseType.typeCode == null) {
					this.$message.error('请填写课程类型编号！')
				} else {
					this.$postData("addCourseType", courseType).then(res => {
						if (res.status == 200) {
							this.$message.success('课程类型添加成功!')
							this.courseTypeModal = false
							this.getCourseType()
						} else {
							this.$message.error('课程类型添加失败！' + res.msg)
						}
					})
				}
			},
			// 添加课程章节
			addCourseContent() {
				let courseContent = this.courseContent
				// 数据校验
				if (courseContent.courseNum == null) {
					this.$message.error('请填写章节序号！')
				} else if (courseContent.title == null) {
					this.$message.error('请填写章节标题！')
				}
				// else if (courseContent.courseRemark == null) {
				// 	this.$message.error('请填写章节简介！')
				// }
				// else if (courseContent.courseImgUrl == null) {
				// 	this.$message.error('请上传章节封面！')
				// } 
				// else if (courseContent.courseDuration == null) {
				// 	this.$message.error('请填写章节时长！')
				// }
				else if (courseContent.courseContent == null) {
					this.$message.error('请填写章节内容！')
				}
				// else if (courseContent.videoUrl == null) {
				// 	this.$message.error('请上传课程简介视频！')
				// } 
				else {
					this.$set(courseContent, "courseId", this.courseId)
					this.$postData("addCourseContent", courseContent).then(res => {
						if (res.status == 200) {
							this.$message.success('课程章节添加成功!')
							this.courseContentModal = false
							this.getData()
						} else {
							this.$message.error('课程章节添加失败！' + res.msg)
						}
					})
				}
			},
			// 添加课程标签
			addKeyList() {
				this.keyListModal = false
			},
			// 添加课程权限列表
			addAuthIdList() {
				this.authIdListModal = false
			},
			// 添加分组角色列表
			addGroupRoleList() {
				this.groupRoleListModal = false
			},
			// 删除课程标签
			closeKeyTags(index, index1) {
				this.course.keyListArray.splice(index1, 1)
			},
			// 删除课程权限标签
			closeAuthTags(index, index1) {
				this.course.authListArray.splice(index1, 1)
			},
			// 删除角色标签
			closeRoleTags(index, index1) {
				this.list[index].roleListArray.splice(index1, 1)
			},
			closeContentTags(index, index1) {
				this.courseChapterList[index].courseContentIdListArray.splice(index1, 1)
			},
			// 删除课程
			deleteCourse(val) {
				this.$postData("deleteCourse", val).then(res => {
					if (res.status == 200) {
						this.$message.success('课程删除成功!')
						val.isEdit = false
						this.getData()
					} else {
						this.$message.error('课程删除失败！' + res.msg)
					}
				})
			},
			// 删除课程分组
			deleteCourseGroup(val) {
				this.$postData("deleteCourseGroup", val).then(res => {
					if (res.status == 200) {
						this.$message.success('课程分组删除成功!')
						val.isEdit = false
						this.getData()
					} else {
						this.$message.error('课程分组删除失败！' + res.msg)
					}
				})
			},
			// 删除课程章节
			deleteCourseContent() {
				let val = this.multipleSelection
				let length = val.length
				if (length == 0) {
					return this.$message.error('还未选择任何章节!')
				}
				this.deleteTips = "确定删除选中的" + length + "个章节吗？该操作无法恢复，请谨慎操作!"

				this.$postData("deleteCourseContent", val).then(res => {
					if (res.status == 200) {
						this.$message.success('课程章节删除成功!')
						this.isEdit = false
						this.multipleSelection = []
						this.getData()
					} else {
						this.$message.error('课程章节删除失败！' + res.msg)
					}
				})
			},
			// 更改课程状态
			updateCourse(val) {
				// 格式化课程标签
				if (val.keyListArray.length != 0) {
					val.courseKeyWords = val.keyListArray.join(',')
					if (val.courseKeyWords.length == 0) {
						val.courseKeyWords = ''
					}
				}

				// 格式化权限角色列表
				val.authList = val.authListArray.join(',')
				if (val.authList.length == 0) {
					val.authList = '0'
				}

				if (val.courseRemark == '<p><br></p>') {
					val.courseRemark = ''
				}
				this.$postData("updateCourse", val).then(res => {
					if (res.status == 200) {
						this.$message.success('课程更新成功!')
						this.list[this.choiceItemIndex] = this.course
						val.isEdit = false
					} else {
						this.$message.error('课程更新失败！' + res.msg)
					}
				})
			},
			updateCourseGroup(val) {
				// 格式化角色列表
				val.roleList = val.roleListArray.join(',')
				if (val.roleList.length == 0) {
					val.roleList = '0'
				}

				val.authList = val.authListArray.join(',')
				if (val.authList.length == 0) {
					val.authList = '0'
				}

				this.$postData("updateCourseGroup", val).then(res => {
					if (res.status == 200) {
						this.$message.success('课程分组更新成功!')
						val.isEdit = false
					} else {
						this.$message.error('课程分组更新失败！' + res.msg)
					}
				})
			},
			updateCourseContent(val) {
				if (!val.length) {
					this.isEdit = false
					return this.$message.warning('暂无可更新内容!')
				}
				this.loading = true
				if ((val.length || 0) > 10) {
					this.$message.warning('章节较多，正在保存中...请耐心等待哦！')
				}
				this.$postData("updateCourseContent", val).then(res => {
					if (res.status == 200) {
						this.$message.success('课程章节更新成功!')
						this.isEdit = false
					} else {
						this.$message.error('课程章节更新失败！' + res.msg)
					}
					this.loading = false
				})
			},
			updateCourseChapterList() {
				this.courseChapterList.forEach(val => {
					if (val.courseContentIdListArray.length != 0) {
						val.courseContentIdList = val.courseContentIdListArray.join(',')
						if (val.courseContentIdList.length == 0) {
							val.courseContentIdList = ''
						}
					}
				})

				$.ajax({
					type: "POST",
					url: "https://biapi.xiaoyujia.com/courseContent/updateCourseChapterList",
					data: JSON.stringify(this.courseChapterList),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.status == 200) {
							this.$message.success('章节分组更新成功!')
							this.listCourseChapter(this.course.id)
							this.isEdit = false
						}
					},
				})
			},
			addChapter() {
				let data = {
					id: null,
					courseId: this.course.id,
					chapterTitle: '',
					chapterContent: '',
					courseContentIdList: '',
					courseContentIdListArray: '',
					chapterNum: ''
				}
				this.isEdit = true
				this.courseChapterList.push(data)
			},
			toOpenContentText(index) {
				this.choiceItemContentIndex = index
				let list = this.list[this.choiceItemIndex].courseContent
				this.courseContentText = list[index].courseContent || ''
				this.openContentText = true
			},
			toUpdateContent1() {
				if (this.courseContentText == '<p><br></p>') {
					this.courseContentText = ''
				}
				this.$set(this.list[this.choiceItemIndex].courseContent[this.choiceItemContentIndex],
					'courseContent', this.courseContentText || '')
				this.openContentText = false
				// data.courseContent = this.courseContentText || ''
				// this.$postData("updateCourseContent", data).then(res => {
				// 	if (res.status == 200) {
				// 		this.$message.success('课程章节更新成功!')
				// 		this.openContentText = false
				// 	} else {
				// 		this.$message.error('课程章节更新失败！' + res.msg)
				// 	}
				// 	this.loading = false
				// })
			},

			beforeImgUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!');
				}
				return isLt2M;
			}
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}

	.column-text {
		white-space: pre-wrap;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 4;
	}
</style>

<style src="@wangeditor/editor/dist/css/style.css"></style>