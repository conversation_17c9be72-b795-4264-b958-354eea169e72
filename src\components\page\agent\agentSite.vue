<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">

                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="地址">
                                <el-input
                                        v-model="model.street"
                                        placeholder="地址"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="名称">
                                <el-input
                                        v-model="model.name"
                                        placeholder="名称"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="经纪人">
                                <el-input
                                        v-model="model.realName"
                                        placeholder="经纪人"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8" style="float: right">

                            <el-form-item>
                                <el-button type="success"  round @click="query()">搜索</el-button>
                                <el-button type="info"  round @click="re()">重置</el-button>
                                <el-button type="success" round  @click="addModal=true">添加</el-button>
                                <el-button type="success" round  @click="ups()">批量绑定</el-button>
                            </el-form-item>

                        </el-col>
                    </el-row>

                </el-form>
            </div>
            <el-table :data="list" :header-cell-style="{background:'#ddd'}" @sort-change="sortChange" border class="table" ref="multipleTable" @cell-dblclick="edit" @selection-change="handleSelectionChange">
                <el-table-column
                        type="selection"
                        width="35">
                </el-table-column>
                <el-table-column
                        fixed="left"
                        prop="name"
                        label="名称"
                        sortable="custom"
                        min-width="180"
                ></el-table-column>
                <el-table-column
                        prop="cityName"
                        label="城市"
                        min-width="150"
                        sortable="custom"
                ></el-table-column>
                <el-table-column
                        prop="areaName"
                        label="区域"
                        min-width="150"
                        sortable="custom"
                ></el-table-column>
                <el-table-column
                        prop="street"
                        label="地址"
                        sortable="custom"
                        min-width="180"

                ></el-table-column>
                <el-table-column
                        prop="realName"
                        label="所属经纪人"
                        min-width="180"
                        sortable="custom"
                ></el-table-column>
                <el-table-column
                        prop="status"
                        label="状态"
                        sortable="custom"
                        width="120"
                >
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.status==1" type="info">正常</el-tag>
                        <el-tag v-else-if="scope.row.status==2" type="success">主站点</el-tag>
                    </template>
                </el-table-column>
<!--                <el-table-column-->
<!--                        prop="workRemark"-->
<!--                        label="工作记录"-->
<!--                        width="150"-->
<!--                >-->
<!--                </el-table-column>-->
<!--                <el-table-column-->
<!--                        prop="createTime"-->
<!--                        label="创建时间"-->
<!--                        sortable="custom"-->
<!--                        width="180"-->
<!--                ></el-table-column>-->

                <el-table-column
                        label="操作"
                        fixed="right"
                        min-width="260">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="setMain(scope.row)" type="primary" v-if="scope.row.realName!=null && scope.row.status!=2">设为主站点</el-button>
                        <el-button size="mini" @click="delMain(scope.row)" type="info" v-if="scope.row.realName!=null && scope.row.status==2">取消主站点</el-button>
                        <el-button size="mini" @click="edit(scope.row)" type="primary">编辑</el-button>
                        <el-button size="mini" @click="del(scope.row)" type="info">删除</el-button>
                   </template>

                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>

        <Modal v-model="saveModal" class="Modal" :width="screenWidth"  title="站点详情" :mask-closable="false" >
            <div class="addBody">

                <siteUpdate v-if="saveModal" @init-choose="initChooseProject"
                                @close-modal="closeCurrModal" :model="show"></siteUpdate>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="addModal" class="Modal" :width="screenWidth"   title="添加站点" :mask-closable="false" >
            <div class="addBody" >
                <siteAdd v-if="addModal" @init-choose="initChooseProject"
                          @close-modal="closeCurrModal" ></siteAdd>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="addsModal" class="Modal" :width="screenWidth"   title="批量绑定" :mask-closable="false" >
            <center>
                <el-autocomplete
                        v-model="ename"
                        :fetch-suggestions="querySearchAsync"
                        placeholder="请输入内容"
                        @select="handleSelect"
                        :trigger-on-focus="false">
                    <template slot="prepend">所属经纪人:</template>
                    <el-button slot="append" icon="el-icon-delete"  @click="upTo">解除绑定</el-button>
                </el-autocomplete>
                <el-button slot="append" icon="el-icon-delete" type="primary" @click="saveTo">更改绑定</el-button>
            </center>
        </Modal>

    </div>
</template>

<script>
    import siteAdd from '@/components/page/agent/choose/siteAdd.vue'
    import siteUpdate from '@/components/page/agent/choose/siteUpdate.vue'
    import {formatDate} from '@/components/common/utils.js'
    export default {
        data() {
            return {
                addsModal:false,
                eid:null,
                ename:null,
                ids:[],
                baomuModel:{
                    RealName:null,
                    No:null,
                },
                restaurants:[],
                tableData: [],
                pageSizeOpts:[10,20,30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                screenWidth: '80%',//新增对话框 宽度
                saveModal: false,
                addModal:false,
                show:{},
                model: {
                    realName:null,
                    name: null,
                    street:null,
                    current: 1,
                    size: 10

                },
                update:{
                    id:null,
                    realName:null,

                },
                list:null
            };
        },
        components: {
            'siteAdd': siteAdd,
            'siteUpdate': siteUpdate,
        },
        created() {
            this.getData();
        },
        computed: {
        },
        methods: {
            sortChange: function(column, prop, order) {

                if (column == null) {
                    this.model.orderBy = "";
                } else {
                    if (column.order == "ascending") {
                        this.model.orderBy = column.prop + " ASC";
                    }
                    if (column.order == "descending") {
                        this.model.orderBy = column.prop + " DESC";
                    }
                }
                this.getData();
            },

            getData() {
                this.$postData("site_page", this.model, {}).then(res => {
                    if (res.status == 200) {

                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            query() {
                this.getData();
            },
            re(){
                this.model.realName=null;
                this.model.name=null;
                this.model.street=null;
                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.model.size = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                console.log(index)
                this.model.current = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
                this.addModal=false;
                this.updateModal = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },
            edit(id){
                this.show=id;

                this.saveModal=true;
            },
            //------------
            ups(){

                if (this.multipleSelection[0]==null){
                    return this.$message.error("请勾选操作项。");
                }
                this.addsModal=true;
                this.ids=[];
                this.multipleSelection.forEach((item, index, arr) => {
                    this.ids.push(item.id);
                });


            },
            handleSelectionChange(val) {
                this.multipleSelection = val;

            },
            //------------
            handleSelect(item) {
                this.eid=item.name;
                //this.addTo.baomuId=this.dom.id;

            },
            querySearchAsync(queryString, cb) {
                this.restaurants=[]
                this.baomuModel.RealName=this.ename;
                this.$postData("agent_list", this.baomuModel, {}).then(res => {
                    if (res.status == 200) {

                        res.data.forEach((item, index, arr) => {
                            var a={}
                            a.value=item.realName;
                            a.name=item.id;
                            this.restaurants.push(a);
                        });

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                var restaurants = this.restaurants;
                var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants;
                cb(restaurants);
            },
            createStateFilter(queryString) {
                return (state) => {
                    return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                };
            },
            delMain(dom){
                dom.status=1
                dom.siteId=dom.id
                console.log(dom)
                this.$postData("upMain_site", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.$Message.success('修改成功');
                        this.getData();
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                        this.getData();
                    }
                })
            },
            setMain(dom){
                if(dom.employeeId==null){
                    return this.$message.success("请先绑定一位经纪人" );
                }
                this.$postData("main_site", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.success("成功，" + res.msg);
                        this.getData();
                        this.addsModal=false;
                    } else {
                        this.$message.error("失败，" + res.msg);
                    }
                })
            },
            upTo(){
                console.log(this.ids)
                this.$postData("up_employeeSites", this.ids, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.success("解绑成功，" + res.msg);
                        this.getData();
                        this.addsModal=false;
                    } else {
                        this.$message.error("解绑失败，" + res.msg);
                    }
                })
            },
            saveTo(){
                this.$postUrl("add_employeeSites",this.eid, this.ids, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.success("绑定成功，" + res.msg);
                        this.getData();
                        this.addsModal=false;
                    } else {
                        this.$message.error("绑定失败，" + res.msg);
                    }
                })

            },
            del(dom){
                this.$postUrl("del_site",dom.id, null, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.success("删除成功，" + res.msg);
                        this.getData();
                    } else {
                        this.$message.error("删除失败，" + res.msg);
                    }
                })

            }
        }
    };
</script>

<style scoped>

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
