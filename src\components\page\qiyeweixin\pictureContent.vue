<template>
  <div style=" border: 1px solid #eee;width: 100%">
    <el-container>
      <el-container style="background: white">
        <el-main>
          <h1>图片</h1>
          <p>上传图片得到图片URL，该URL永久有效</p>
          <p>返回的图片URL，仅能用于图文消息正文中的图片展示，或者给客户发送欢迎语等；若用于非企业微信环境下的页面，图片将被屏蔽。</p>
          <p> 每个企业每天最多可上传100张图片。</p>
          <el-button type="primary" size="small" @click="dialogVisible = true">添加图片</el-button>
          <el-divider></el-divider>
          <pictureSelection :content="content"></pictureSelection>
        </el-main>
        <el-dialog title="图片上传" :visible.sync="dialogVisible" width="50%" center>
          <el-form label-width="80px">
            <el-form-item label="选择图片">
              <el-upload action="''" ref="upload" :limit="1" :http-request="upload" list-type="picture-card"
                         :auto-upload="false">
                <i slot="default" class="el-icon-plus"></i>
              </el-upload>
            </el-form-item>
            <el-form-item label="图片标签">
              <div>
                <el-radio-group v-model="dom.dataLabelId" v-for="(tag,index) in dynamicTags" :key="index"
                                @change="radioChange(tag.name)">
                  <el-radio-button :label="tag.id">{{ tag.name }}</el-radio-button>
                </el-radio-group>
              </div>
            </el-form-item>
            <el-form-item label="图片名称">
              <el-input v-model="dom.name" placeholder="请输入内容"></el-input>
            </el-form-item>
            <el-form-item label="文案">
              <el-input v-model="dom.content" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
          </span>
        </el-dialog>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import axios from "axios";
//引入组件
import pictureSelection from "./pictureSelection";

export default {
  name: "pictureContent",
  inject: ['reload'],
  // 注册组件
  components: {
    pictureSelection,
  },
  data() {
    return {
      //标签
      dynamicTags: [],
      inputVisible: false,
      //图片上传
      dialogVisible: false,
      inputValue: '',
      closable: false,
      content: true,
      quick: null,
      roleId: null,
      imgList: [],
      checkList: [],
      loading: false,
      pageSizeOpts: [9, 24, 36],
      pageInfo: {total: 9, size: 9, current: 1, pages: 1},
      dom: {
        type: 1,
        img: null,
        qiyeImg: null,
        operatorId: null,
        dataLabelId: null,
        dataLabelName: null,
        name: null,
        size: 9,
        current: 1,
        content:null,
        isTop:1

      }
    }
  },
  created() {
    this.dom.operatorId = localStorage.getItem("id")
    this.roleId = localStorage.getItem("roleId")
    this.selectByType()
    this.selectByCondition()
  },
  methods: {
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dom.current = index;
      this.selectByCondition();
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dom.size = size;
      this.selectByCondition();
    },
    reset() {
      this.dom.img = null;
      this.dom.qiyeImg = null;
      this.dom.operatorId = null;
      this.dom.dataLabelId = null;
      this.dom.dataLabelName = null;
      this.dom.name = null;
      this.quick = null;
      this.dialogVisible=false;
      this.selectByCondition()
    },
    //删除图片
    determine() {
      this.delectImg = !this.delectImg
      this.$postData("delectQiyeData", this.checkList).then(res => {
        if (res.status === 200) {
          this.reset()
          this.$message.success("删除图片成功");
        }
      })
    },
    handleClose(tag) {
      this.delectQiyeDataLabe(tag.id)
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },
    delectQiyeDataLabe(id) {
      this.$getData("delectQiyeDataLabe", {id: id}).then(res => {
        if (res.status === 200) {
          if (res.data === 1) {
            this.reset()
            this.$message({
              message: '删除成功',
              type: 'success'
            });
          }
        }
      })
    },
    radioChange(name) {
      this.dom.dataLabelName = name
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    selectByType() {
      this.$getData("selectByType", {type: this.dom.type}).then(res => {
        if (res.status === 200) {
          this.dynamicTags = res.data
        }
      })
    },
    addPoliteness(index, id) {
      this.quick = index
      this.dom.dataLabelId = id
      this.selectByCondition()
    },
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (null !== inputValue && '' !== inputValue) {
        this.dynamicTags.push(inputValue);
        //添加标签
        let qiyeDataLabe = {
          name: inputValue,
          type: 1,
        }
        this.$postData("addQiyeDataLabe", qiyeDataLabe).then(res => {
          if (res.status === 200) {
            this.qiyeLabelGroup = res.data
            this.selectByType()
          }
        })
      }
      this.inputVisible = false;
      this.inputValue = '';
    },
    //手动上传文件列表
    submitForm() {
      if (null == this.dom.dataLabelId) {
        return this.$message.error('请选择图片标签');
      }
      if (null == this.dom.name) {
        return this.$message.error('请输入图片名称');
      }
      this.$refs.upload.submit();
    },
    addQiyeData() {
      this.$postData("addQiyeData", this.dom).then(res => {
        if (res.status === 200) {
          this.$message.success("上传图片成功");
          this.reload();
        }
      })
    },
    selectByCondition() {
      this.$postData("selectByCondition", this.dom).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.imgList = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    //覆盖默认的上传行为，可以自定义上传
    async upload(param) {
      var formData = new FormData();
      formData.append("flies", param.file);
      await axios.post(`/qiyeCustomer/ploadPicturesu`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then((res) => {
        if (res.status === 200) {
          this.dom.img = res.data
        }
      })
      await axios.post(`/qiyeCustomer/qiyeUploadPictures`, formData, {
        headers: {'Content-Type': 'multipart/form-data'}
      }).then((res) => {
        if (res.status === 200) {
          this.dom.qiyeImg = res.data
        }
      })
      this.addQiyeData()
    },
  },


}
</script>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}


</style>
