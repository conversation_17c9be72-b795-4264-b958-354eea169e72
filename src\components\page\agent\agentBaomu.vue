<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">

                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="最小年龄">
                                <el-input
                                        v-model="model.minAge"
                                        placeholder="最小年龄"
                                        style="width:200px"
                                        type="number"
                                        :min="0"
                                        :max="100"
                                        :maxlength="3"
                                        @blur="checkNumber(model.minAge, 1)"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="编号">
                                <el-input
                                        v-model="model.no"
                                        placeholder="编号"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <div v-show="searchMore">
                            <el-col :span="8">
                                <el-form-item prop="state" label="员工状态">

                                    <el-select v-model="model.state" clearable placeholder="请选择">
                                        <el-option
                                                v-for="item in states"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="最大年龄">
                                    <el-input
                                            v-model="model.maxAge"
                                            placeholder="最大年龄"
                                            style="width:200px"
                                            type="number"
                                            :min="0"
                                            :max="100"
                                            :maxlength="3"
                                            @blur="checkNumber(model.maxAge, 2)"
                                            class="handle-input mr10"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="姓名">
                                    <el-input
                                            v-model="model.realName"
                                            placeholder="姓名"
                                            style="width:200px"
                                            class="handle-input mr10"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                          <el-col :span="8">
                            <el-form-item label="性别">
                              <el-select v-model="model.sex">
                                <el-option label="全部"></el-option>
                                <el-option value="1" label="男"></el-option>
                                <el-option value="2" label="女"></el-option>
                              </el-select>
                            </el-form-item>
                          </el-col>
                            <el-col :span="8">
                                <el-form-item label="认证状态">
                                    <el-select v-model="model.shimingState" clearable placeholder="请选择">
                                        <el-option
                                                v-for="item in shimingStateoptions"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                          <el-col :span="8">
                            <el-form-item label="安全认证状态">
                              <el-select v-model="model.securityAuth" clearable placeholder="请选择">
                                <el-option
                                    v-for="item in shimingStateoptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                              </el-select>
                            </el-form-item>
                          </el-col>
                            <el-col :span="8">
                                <el-form-item label="服务内容">
                                    <el-select v-model="model.serverContent" clearable placeholder="请选择"
                                               style="width: 200px">
                                        <el-option
                                                v-for="item in serverContents"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="工龄">
                                    <el-input
                                            v-model="model.workYear"
                                            placeholder="工龄"
                                            style="width:200px"
                                            type="number"
                                            class="handle-input mr10"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="招聘来源">
                                    <el-select v-model="model.source" clearable placeholder="请选择" style="width: 200px">
                                        <el-option
                                                v-for="item in sources"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="健康情况">
                                    <el-select v-model="model.health" clearable placeholder="请选择">
                                        <el-option
                                                v-for="item in healths"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="户籍">
                                    <el-input
                                            v-model="model.hometown"
                                            placeholder="户籍"
                                            style="width:200px"
                                            class="handle-input mr10"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="工作类型">
                                    <el-select v-model="model.workType" clearable placeholder="请选择"
                                               style="width: 200px">
                                        <el-option
                                                v-for="item in workTypes"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="工作方式">
                                    <el-select v-model="model.baomuWorkType" clearable placeholder="请选择">
                                        <el-option
                                                v-for="item in workTypeOptions"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="8">
                                <el-form-item label="站点">
                                    <el-input
                                            v-model="model.sites"
                                            placeholder="站点"
                                            style="width:200px"
                                            class="handle-input mr10"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <!--<el-col :span="8">-->
                            <!--    <el-form-item prop="EntryTime" label="入职日期">-->
                            <!--        <el-date-picker-->
                            <!--                style="width: 170px"-->
                            <!--                v-model="model.showTime"-->
                            <!--                type="date"-->
                            <!--                value-format="yyyy-MM-dd HH:mm:ss"-->
                            <!--                placeholder="入职日期">-->
                            <!--        </el-date-picker>-->
                            <!--    </el-form-item>-->
                            <!--</el-col>-->
                            <el-col :span="8">
                                <el-form-item label="手机号码">
                                    <el-input
                                            v-model="model.phone"
                                            placeholder="手机"
                                            style="width:200px"

                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="现住地址">
                                    <el-input
                                            v-model="model.address"
                                            placeholder="现住地址"
                                            style="width:200px"
                                            class="handle-input mr10"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="学历情况">
                                    <el-select v-model="model.education" clearable placeholder="请选择"
                                               style="width: 175px">
                                        <el-option
                                                v-for="item in eduoptions"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="其他技能">
                                    <el-input
                                            v-model="model.otherSkills"
                                            placeholder="其他技能"
                                            style="width:200px"
                                            class="handle-input mr10"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="time" label="入职时间">
                                    <el-date-picker
                                            style="width:200px"
                                            :default-value="new Date()"
                                            :unlink-panels="true"
                                            v-model="model.time"
                                            type="datetimerange"
                                            start-placeholder="开始日期"
                                            end-placeholder="结束日期"
                                            value-format="yyyy-MM-dd HH:mm:ss"
                                            :default-time="['00:00:00']">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="所属经纪人">
                                    <el-input
                                            v-model="model.agentName"
                                            placeholder="所属经纪人"
                                            style="width:160px"
                                            class="handle-input mr10"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        </div>
                        <el-col :span="8">

                            <el-form-item>
                                <el-button :class="searchMore? 'el-icon-caret-top':'el-icon-caret-bottom'"
                                           @click="searchMore=!searchMore" v-show="radio1=='新版'">
                                    {{searchMore?'收起更多':'展开更多'}}
                                </el-button>
                                <el-button type="success" round @click="query()" icon="el-icon-search">搜索</el-button>
                                <el-button type="info" round @click="re()" icon="el-icon-refresh-right">重置</el-button>
                                <!--                                <el-button type="success" round  @click="addModal=true" icon="el-icon-plus">添加</el-button>-->
                                <!--                                <el-button type="success" round  @click="ups()">批量绑定</el-button>-->
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div style="background: #ddd;padding: 12px" id="top">当前数目：<b>{{pageInfo.total}}</b>
                <el-radio-group v-model="radio1" style="padding-left: 30px" @change="searchMore=!searchMore"
                                v-show="true">
                    <el-radio-button label="原版"></el-radio-button>
                    <el-radio-button label="新版"></el-radio-button>
                </el-radio-group>
                <el-divider direction="vertical"></el-divider>
                <el-button type="info" @click="showJzyDetail=true">认证记录</el-button>
                <el-button type="info" @click="toAgentBaomuData">保姆分析</el-button>

                <el-radio-group v-model="model.baomuLevel" @change="model.current=1,getData()"
                                style="padding-left: 30px">
                    <el-radio-button :label="null">全部</el-radio-button>
                    <el-radio-button :label="1">百优保姆</el-radio-button>
                    <el-radio-button :label="2">推荐保姆</el-radio-button>
                </el-radio-group>

            </div>
            <el-table :data="list"
                      v-show="radio1=='新版'"
                      @cell-mouse-enter="mouseEnter"
                      :header-cell-style="{background:'#ddd'}"
                      @sort-change="sortChange"
                      border
                      stripe
                      class="table" ref="multipleTable"
                      @selection-change="handleSelectionChange">
                <!--@cell-dblclick="edit"-->
                <el-table-column
                        type="selection"
                        width="35">
                </el-table-column>
                <el-table-column
                        prop="no"
                        label="头像"
                        width="150"

                >
                    <template slot-scope="scope">
                        <img :src="scope.row.headPortrait" width="120px">
                    </template>
                </el-table-column>

                <el-table-column
                        prop="no"
                        label="基本信息"
                        width="180"
                >
                    <template slot-scope="scope">
                        <div style="line-height: 22px">
                            <el-tag v-if="scope.row.baomuLevel==1" type="Danger" @click="openby(scope.row,0)">百优保姆
                            </el-tag>
                            <el-tag v-else-if="scope.row.baomuLevel==0 " type="info" @click="openby(scope.row,2)">普通保姆
                            </el-tag>
                            <el-tag v-else-if="scope.row.baomuLevel==2" type="warning" @click="openby(scope.row,1)">
                                推荐保姆
                            </el-tag>

                            <el-tag v-if="scope.row.state==1" type="success">上架</el-tag>
                            <el-tag v-else-if="scope.row.state==2" type="warning">下架</el-tag>
                            <el-tag v-else-if="scope.row.state==3" type="warning">离职</el-tag>
                            <el-tag v-else-if="scope.row.state==4" type="danger">黑名单</el-tag>
                            <br>
                            编号:<b>{{scope.row.no}}</b><br>
                            名称:<b>
                            <el-link type="primary" @click="toBaomuInfo(scope.row.id)">{{scope.row.realName}}</el-link>
                        </b><br>
                            手机: <span v-if="scope.row.phone">{{scope.row.phone.slice(0, 3)}}****{{scope.row.phone.slice(-4)}}</span><br>
                            年龄:{{scope.row.age}}<br>
                            经纪人:{{scope.row.agentName}}<br>
                            经验:{{scope.row.workYear}}年<br>
                        </div>

                    </template>
                </el-table-column>
                <el-table-column
                        prop="no"
                        label="详细信息"
                        min-width="480"
                >
                    <template slot-scope="scope">
                        <div style="line-height: 22px">
                            现住地:{{scope.row.address}}<br>
                            户籍:{{scope.row.hometown}}<br>
                            入职时间:<b>{{scope.row.entryTime}}</b><br>
                            <div v-show="scope.row.baomuWorkType!=null">工作方式:{{scope.row.baomuWorkType}}<br></div>
                            <div v-show="scope.row.workType!=null">工作类型:{{scope.row.workType}}</div>
                            <br>
                            <div v-show="scope.row.health!=null">健康状态:{{scope.row.health}}</div>
                            <br>
                            <div v-show="scope.row.serverContent!=null">服务类型:{{scope.row.serverContent}}</div>
                            <br>
                            <div v-show="scope.row.source!=null">招聘来源:{{scope.row.source}}</div>
                            <br>
                            <div v-show="scope.row.otherSkills!=null">其他技能:{{scope.row.otherSkills}}</div>
                            <br>
                        </div>

                    </template>
                </el-table-column>

                <el-table-column
                        :label="'操作   --    ('+mouseEnterName+')'"
                        fixed="right"
                        width="290">
                    <template slot-scope="scope">

                        <el-button v-if="showHandleByRoleId()" @click="editbaomuImg(scope.row)" type="primary" round>
                            认证
                        </el-button>
                        <el-button v-if="showHandleByRoleId()" @click="editbaomurate(scope.row)" type="primary" round>
                            评价
                        </el-button>
                        <el-button v-if="showHandleByRoleId()" @click="editbaomuExp(scope.row)" type="primary" round>
                            经验
                        </el-button>
                        <el-button size="mini" @click="editbaomuLog(scope.row)" type="primary" round>日志</el-button>
                        <div><br></div>

                        <el-button v-if="showHandleByRoleId()" @click="editbaomuSkill(scope.row)" type="primary" round>
                            技能
                        </el-button>

                        <el-button v-if="showHandleByRoleId()" @click="edit(scope.row)" type="primary" round>编辑
                        </el-button>
                        <el-button v-if="showHandleByRoleId()" @click="editbaomuhb(scope.row)" type="primary" round>海报
                        </el-button>
<!--                        <el-button v-if="showHandleByRoleId() && scope.row.state==1" @click="save(scope.row,2)"-->
<!--                                   type="primary" round>下架-->
<!--                        </el-button>-->
<!--                        <el-button v-if="showHandleByRoleId() && (scope.row.state==2||scope.row.state==3)"-->
<!--                                   @click="save(scope.row,1)" type="info"-->
<!--                                   round>上架-->
<!--                        </el-button>-->
                        <div><br></div>
                        <el-button v-if="showHandleByRoleId()" @click="editRate(scope.row)" type="primary" round>等级
                        </el-button>
<!--                        <el-button v-if="showHandleByRoleId() && scope.row.state!==4" @click="save(scope.row,4)"-->
<!--                                   type="info" round>拉黑-->
<!--                        </el-button>-->
                        <el-button v-if="showHandleByRoleId() && (scope.row.agentName==''||scope.row.agentName==null)"
                                   @click="addEmployeeToBaomu(scope.row.id)" type="primary" round>绑定
                        </el-button>
                    </template>

                </el-table-column>
            </el-table>
            <el-table :data="list"

                      @cell-mouse-enter="mouseEnter"
                      :header-cell-style="{background:'#ddd'}"
                      @sort-change="sortChange"
                      border
                      stripe
                      v-show="radio1=='原版'"
                      class="table" ref="multipleTable"
                      @selection-change="handleSelectionChange"
                      :row-class-name="tableRowClassName">
                <!--                <el-table-column-->
                <!--                        type="selection"-->
                <!--                        width="35">-->
                <!--                </el-table-column>-->
                <el-table-column
                        prop="no"
                        label="编号"
                        width="120"
                >

                </el-table-column>

                <el-table-column

                        prop="realName"
                        label="名称"
                        width="80"
                        sortable="custom"
                >
                    <template slot-scope="scope">
                        <el-link type="primary" @click="toBaomuInfo(scope.row.id)">{{scope.row.realName}}
                        </el-link>
                    </template>
                </el-table-column>

                <el-table-column
                        prop="shimingState"
                        label="状态"
                        sortable="custom"
                        width="80">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.shimingState==1" type="success">已认证</el-tag>
                        <el-tag v-else-if="scope.row.shimingState==0" type="warning" @click="open(scope.row)">未认证
                        </el-tag>
                    </template>
                </el-table-column>
              <el-table-column
                  prop="securityAuth"
                  label="安全认证"
                  sortable="custom"
                  width="80">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.securityAuth==1" type="success">已认证</el-tag>
                  <el-tag v-else-if="scope.row.securityAuth==0" type="warning">未认证
                  </el-tag>
                </template>
              </el-table-column>
                <el-table-column
                        prop="baomuLevel"
                        label="等级"
                        sortable="custom"
                        width="80">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.baomuLevel==1" type="Danger" @click="openby(scope.row,0)">百优保姆</el-tag>
                        <el-tag v-else-if="scope.row.baomuLevel==0" type="info" @click="openby(scope.row,2)">普通保姆
                        </el-tag>
                        <el-tag v-else-if="scope.row.baomuLevel==null" type="info" @click="openby(scope.row,2)">普通保姆
                        </el-tag>
                        <el-tag v-else-if="scope.row.baomuLevel==2" type="warning" @click="openby(scope.row,1)">推荐保姆
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="agentName"
                        label="所属经纪人"
                        width="120"
                        show-overflow-tooltip
                        sortable="custom"
                ></el-table-column>
                <el-table-column
                        prop="phone"
                        sortable="custom"
                        label="联系方式"
                        width="120"
                >
                  <template slot-scope="scope" v-if="scope.row.phone">
                    {{scope.row.phone.slice(0, 3)}}****{{scope.row.phone.slice(-4)}}
                  </template>
                </el-table-column>
                <el-table-column
                        prop="age"
                        sortable="custom"
                        label="年龄/年"
                        width="100"
                ></el-table-column>
                <el-table-column
                        prop="address"
                        sortable="custom"
                        label="现住地"
                        width="100"
                ></el-table-column>
                <el-table-column
                        prop="workYear"
                        sortable="custom"
                        label="工作年限/年"
                        width="120"
                ></el-table-column>
                <el-table-column
                        prop="entryTime"
                        label="入职时间"
                        width="180"
                        sortable="custom">
                </el-table-column>
                <el-table-column
                        prop="createDate"
                        label="注册时间"
                        width="180"
                        sortable="custom">
                </el-table-column>

                <el-table-column
                        prop="hometown"
                        label="户籍"
                        width="100"
                        show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                        prop="baomuWorkType"
                        label="工作方式"
                        width="150"
                        show-overflow-tooltip
                ></el-table-column>

                <el-table-column
                        prop="workType"
                        label="工作类型"
                        width="150"
                ></el-table-column>
                <el-table-column
                        prop="health"
                        label="健康状态"
                        width="150"
                ></el-table-column>
                <el-table-column
                        prop="serverContent"
                        label="服务类型"
                        width="150"
                        show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                        prop="source"
                        label="招聘来源"
                        width="120"
                        sortable="custom"
                ></el-table-column>
                <el-table-column
                        prop="otherSkills"
                        label="其他技能"
                        width="120"
                        sortable="custom"
                ></el-table-column>

                <el-table-column
                        prop="sites"
                        label="所属站点"
                        width="150"
                ></el-table-column>
                <el-table-column
                        prop="education"
                        label="学历"
                        width="150"
                >
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.education" placeholder="请选择" style="width: 70%;">
                            <el-option label="未知" :value="null"></el-option>
                            <el-option
                                    v-for="item in eduoptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>

                <el-table-column
                        prop="state"
                        label="状态"
                        width="120"
                        sortable="custom">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.state==1" type="success">上架</el-tag>
                        <el-tag v-else-if="scope.row.state==2" type="warning">下架</el-tag>
                        <el-tag v-else-if="scope.row.state==3" type="warning">离职</el-tag>
                        <el-tag v-else-if="scope.row.state==4" type="danger">黑名单</el-tag>
                    </template>
                </el-table-column>
                <el-table-column type="expand" label="操作" fixed="left">
                    <template slot-scope="scope">
                        <el-form label-position="left" inline class="demo-table-expand">
                            <el-button v-if="showHandleByRoleId()" size="mini" @click="editRate(scope.row)"
                                       type="primary">等级
                            </el-button>
                            <el-button v-if="showHandleByRoleId()" size="mini" @click="editbaomuImg(scope.row)"
                                       type="primary">认证
                            </el-button>
                            <!--                        <el-button size="mini" @click="editbaomurate(scope.row)" type="primary">评价</el-button>-->
                            <el-button v-if="showHandleByRoleId()" size="mini" @click="editbaomuExp(scope.row)"
                                       type="primary">经验
                            </el-button>
                            <el-button v-if="showHandleByRoleId()" size="mini" @click="editbaomuSkill(scope.row)"
                                       type="primary">技能
                            </el-button>
                            <el-button v-if="showHandleByRoleId()" size="mini" @click="editbaomuScore(scope.row)"
                                       type="primary">评分
                            </el-button>

                            <el-button size="mini" @click="editbaomuLog(scope.row)" type="primary">日志</el-button>
                            <el-button v-if="showHandleByRoleId()" size="mini" @click="edit(scope.row)" type="primary">
                                编辑
                            </el-button>
                            <el-button v-if="showHandleByRoleId()" size="mini" @click="editbaomuhb(scope.row)"
                                       type="primary">海报
                            </el-button>
<!--                            <el-button v-if="showHandleByRoleId() && scope.row.state==1" size="mini"-->
<!--                                       @click="save(scope.row,2)" type="primary">-->
<!--                                下架-->
<!--                            </el-button>-->
<!--                            <el-button v-if="showHandleByRoleId() && (scope.row.state == 2||scope.row.state == 3) "-->
<!--                                       size="mini"-->
<!--                                       @click="save(scope.row,1)" type="info">上架-->
<!--                            </el-button>-->
<!--                            <el-button v-if="showHandleByRoleId() && scope.row.state!==4" @click="save(scope.row,4)"-->
<!--                                       type="info">拉黑-->
<!--                            </el-button>-->
                            <el-button v-if="showHandleByRoleId() && scope.row.agentName==''||scope.row.agentName==null"
                                       @click="addEmployeeToBaomu(scope.row.id)" type="primary">绑定
                            </el-button>
                        </el-form>
                    </template>
                </el-table-column>
                <!--                <el-table-column-->
                <!--                        :label="'操作   &#45;&#45;    ('+mouseEnterName+')'"-->
                <!--                        fixed="right"-->
                <!--                        min-width="420">-->
                <!--                    <template slot-scope="scope">-->
                <!--                        <el-button size="mini" @click="editbaomuImg(scope.row)" type="primary">认证</el-button>-->
                <!--&lt;!&ndash;                        <el-button size="mini" @click="editbaomurate(scope.row)" type="primary">评价</el-button>&ndash;&gt;-->
                <!--                        <el-button size="mini" @click="editbaomuExp(scope.row)" type="primary">经验</el-button>-->
                <!--                        <el-button size="mini" @click="editbaomuSkill(scope.row)" type="primary">技能</el-button>-->
                <!--                        <el-button size="mini" @click="edit(scope.row)" type="primary">编辑</el-button>-->
                <!--                        <el-button size="mini" @click="editbaomuhb(scope.row)" type="primary">海报</el-button>-->
                <!--                        <el-button v-if="scope.row.state==1" size="mini" @click="save(scope.row,2)" type="primary">下架</el-button>-->
                <!--                        <el-button v-if="scope.row.state==2||scope.row.state==3" size="mini" @click="save(scope.row,1)" type="info">上架</el-button>-->

                <!--                    </template>-->

                <!--                </el-table-column>-->
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>
        <!--        <Modal v-model="saveModal" class="Modal" :width="screenWidth"  title="添加" :mask-closable="false" >-->
        <!--            <div class="addBody">-->

        <!--                <agentAdd v-if="saveModal" @init-choose="initChooseProject"-->
        <!--                                @close-modal="closeCurrModal"></agentAdd>-->
        <!--            </div>-->
        <!--            <div slot="footer">-->
        <!--            </div>-->
        <!--        </Modal>-->

        <Modal v-model="baomuExpModal" class="Modal" :width="screenWidth" title="工作经验" :mask-closable="false">
            <div class="addBody">
                <baomuExp v-if="baomuExpModal" @init-choose="initChooseProject"
                          @close-modal="closeCurrModal" :model="show"></baomuExp>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="baomuImgModal" class="Modal" :width="screenWidth" title="认证图片" :mask-closable="false">
            <div class="addBody" style="min-height: 300px">
                <baomuImg v-if="baomuImgModal" @init-choose="initChooseProject"
                          @close-modal="closeCurrModal" :model="show"></baomuImg>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="baomuSkillModal" class="Modal" :width="screenWidth" title="保姆技能" :mask-closable="false">
            <div class="addBody">
                <baomuSkill v-if="baomuSkillModal" @init-choose="initChooseProject"
                            @close-modal="closeCurrModal" :model="show"></baomuSkill>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="baomuLevelModal" class="Modal" width="1200px" title="保姆等级" :mask-closable="false">
            <div class="addBody">
                <BaomuLevel v-if="baomuLevelModal" @init-choose="initChooseProject"
                            @close-modal="closeCurrModal" :model="show"></BaomuLevel>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="saveModal" class="Modal" :width="screenWidth" title="保姆详情" :mask-closable="false">
            <div class="addBody">
                <baomuUpdate v-if="saveModal" @init-choose="initChooseProject"
                             @close-modal="closeCurrModal" :model="show"></baomuUpdate>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="addModal" class="Modal" :width="screenWidth" title="添加保姆" :mask-closable="false">
            <div class="addBody">
                <baomuAdd v-if="addModal" @init-choose="initChooseProject"
                          @close-modal="closeCurrModal"></baomuAdd>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="baomuhbModal" class="Modal" width="360px" title="保姆海报" :mask-closable="false"
               style="min-width: 360px">
            <div class="addBody">
                <baomuhb v-if="baomuhbModal" @init-choose="initChooseProject"
                         @close-modal="closeCurrModal" :model="show"></baomuhb>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="baomurateModal" class="Modal" :width="screenWidth" title="保姆评价" :mask-closable="false"
               style="min-width: 360px">
            <div class="addBody">
                <baomurate v-if="baomurateModal" @init-choose="initChooseProject"
                           @close-modal="closeCurrModal" :model="show"></baomurate>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="showBaomuWorkLog" class="Modal" :width="screenWidth" title="保姆日志" :mask-closable="false">
            <div class="addBody">
                <baomuWorkLog v-if="showBaomuWorkLog" @init-choose="initChooseProject"
                              @close-modal="closeCurrModal" :employeeId="show.id"></baomuWorkLog>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="showJzyDetail" class="Modal" :width="screenWidth" title="认证日志" :mask-closable="false">
            <div class="addBody">
                <jzyDetail v-if="showJzyDetail" @init-choose="initChooseProject"
                           @close-modal="closeCurrModal" :employeeId="show.id"></jzyDetail>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <!--<Modal v-model="addsModal" class="Modal" :width="screenWidth" title="批量绑定" :mask-closable="false">-->
        <!--    <center>-->
        <!--        <el-autocomplete-->
        <!--                v-model="ename"-->
        <!--                :fetch-suggestions="querySearchAsync"-->
        <!--                placeholder="请输入内容"-->
        <!--                @select="handleSelect"-->
        <!--                :trigger-on-focus="false">-->
        <!--            <template slot="prepend">所属经纪人:</template>-->
        <!--            <el-button slot="append" icon="el-icon-delete" @click="upTo">解除绑定</el-button>-->
        <!--        </el-autocomplete>-->
        <!--        <el-button slot="append" icon="el-icon-delete" type="primary" @click="saveTo">更改绑定</el-button>-->
        <!--    </center>-->
        <!--</Modal>-->
        <el-dialog
                title="保姆评分"
                :visible.sync="showBaomuScoreModel"
                @close='closeScoreDialog'
                width="50%">
            <el-tabs v-model="activeName" @tab-click="handleClickTab" type="card">
                <el-tab-pane label="保姆自评" name="first">
                    <el-form :model="nannyMyselfScore">
                        <el-form-item label="保洁:" label-width="80px">
                            <el-rate v-model="nannyMyselfScore.cleanScore" show-score :max="10" :disabled="nannySelfDis"
                                     @change="addTotalScore(nannyMyselfScore.cleanScore, 1)"
                                     :colors="colors"
                                     score-template="{value}分"></el-rate>
                        </el-form-item>
                        <el-form-item label="护幼:" label-width="80px">
                            <el-rate v-model="nannyMyselfScore.careChildScore" show-score :max="10"
                                     :disabled="nannySelfDis"
                                     @change="addTotalScore(nannyMyselfScore.careChildScore, 1)"
                                     :colors="colors"
                                     score-template="{value}分"></el-rate>
                        </el-form-item>
                        <el-form-item label="陪护:" label-width="80px">
                            <el-rate v-model="nannyMyselfScore.escortScore" show-score :max="10"
                                     :disabled="nannySelfDis"
                                     @change="addTotalScore(nannyMyselfScore.escortScore, 1)"
                                     :colors="colors"
                                     score-template="{value}分"></el-rate>
                        </el-form-item>
                        <el-form-item label="宠物:" label-width="80px">
                            <el-rate v-model="nannyMyselfScore.petsScore" show-score :max="10" :disabled="nannySelfDis"
                                     @change="addTotalScore(nannyMyselfScore.petsScore, 1)"
                                     :colors="colors"
                                     score-template="{value}分"></el-rate>
                        </el-form-item>
                        <el-form-item label="收纳:" label-width="80px">
                            <el-rate v-model="nannyMyselfScore.arrangeScore" show-score :max="10"
                                     :disabled="nannySelfDis"
                                     @change="addTotalScore(nannyMyselfScore.arrangeScore, 1)"
                                     :colors="colors"
                                     score-template="{value}分"></el-rate>
                        </el-form-item>
                        <el-form-item label="厨艺:" label-width="80px">
                            <el-rate v-model="nannyMyselfScore.cookScore" show-score :max="10" :disabled="nannySelfDis"
                                     @change="addTotalScore(nannyMyselfScore.cookScore, 1)"
                                     :colors="colors"
                                     score-template="{value}分"></el-rate>
                        </el-form-item>
                        <el-form-item label="合计:" label-width="80px">
                            <span v-if="nannyMyselfScore.totalScore == null">
                            {{ Number(nannyMyselfScore.cleanScore + nannyMyselfScore.careChildScore + nannyMyselfScore.escortScore
                            + nannyMyselfScore.petsScore + nannyMyselfScore.arrangeScore + nannyMyselfScore.cookScore) }}分
                            </span>
                            <span v-else>{{ nannyMyselfScore.totalScore }}分</span>
                        </el-form-item>
                    </el-form>
                    <el-button v-if="!showSelfReset" type="primary" @click="addScore(1)" style="float: right">保存
                    </el-button>
                    <el-button v-if="showSelfReset" type="info" @click="resetSelfScore()"
                               style="float: right;margin-right: 5px;">重置
                    </el-button>
                </el-tab-pane>
                <el-tab-pane label="顾问评分" name="second">
                    <el-form :model="agentScore">
                        <el-form-item label="保洁:" label-width="80px">
                            <el-rate v-model="agentScore.cleanScore" show-score :max="10" :disabled="agentDis"
                                     @change="addTotalScore(agentScore.cleanScore, 2)"
                                     :colors="colors"
                                     score-template="{value}分"></el-rate>
                        </el-form-item>
                        <el-form-item label="护幼:" label-width="80px">
                            <el-rate v-model="agentScore.careChildScore" show-score :max="10" :disabled="agentDis"
                                     @change="addTotalScore(agentScore.careChildScore, 2)"
                                     :colors="colors"
                                     score-template="{value}分"></el-rate>
                        </el-form-item>
                        <el-form-item label="陪护:" label-width="80px">
                            <el-rate v-model="agentScore.escortScore" show-score :max="10" :disabled="agentDis"
                                     @change="addTotalScore(agentScore.escortScore, 2)"
                                     :colors="colors"
                                     score-template="{value}分"></el-rate>
                        </el-form-item>
                        <el-form-item label="宠物:" label-width="80px">
                            <el-rate v-model="agentScore.petsScore" show-score :max="10" :disabled="agentDis"
                                     @change="addTotalScore(agentScore.petsScore, 2)"
                                     :colors="colors"
                                     score-template="{value}分"></el-rate>
                        </el-form-item>
                        <el-form-item label="收纳:" label-width="80px">
                            <el-rate v-model="agentScore.arrangeScore" show-score :max="10" :disabled="agentDis"
                                     @change="addTotalScore(agentScore.arrangeScore, 2)"
                                     :colors="colors"
                                     score-template="{value}分"></el-rate>
                        </el-form-item>
                        <el-form-item label="厨艺:" label-width="80px">
                            <el-rate v-model="agentScore.cookScore" show-score :max="10" :disabled="agentDis"
                                     @change="addTotalScore(agentScore.cookScore, 2)"
                                     :colors="colors"
                                     score-template="{value}分"></el-rate>
                        </el-form-item>
                        <el-form-item label="合计:" label-width="80px">
                            <!--v-if="agentScore.totalScore == null"-->
                            <!--<span>-->
                            <!--{{ Number(agentScore.cleanScore + agentScore.careChildScore + agentScore.escortScore-->
                            <!--+ agentScore.petsScore + agentScore.arrangeScore + agentScore.cookScore) }}分-->
                            <!--</span>-->
                            <span>{{ agentScore.totalScore }}分</span>
                        </el-form-item>
                    </el-form>
                    <el-button v-if="!showAgentReset" type="primary" @click="addScore(2)" style="float: right;">保存
                    </el-button>
                    <el-button v-if="showAgentReset" type="info" @click="resetAgentScore()"
                               style="float: right;margin-right: 5px;">重置
                    </el-button>
                </el-tab-pane>
            </el-tabs>

        </el-dialog>
        <el-drawer
                size="80%"
                :with-header="false"
                :visible.sync="showBaomuInfo"
                direction="rtl"
                :before-close="InfohandleClose">
            <baomuInfo :baomuid="baomuId" v-if="showBaomuInfo"></baomuInfo>
        </el-drawer>
    </div>
</template>

<script>
    import baomuAdd from '@/components/page/agent/choose/baomuAdd.vue'
    import baomuUpdate from '@/components/page/agent/choose/baomuUpdate.vue'
    import baomuExp from '@/components/page/agent/choose/baomuExp.vue'
    import baomuSkill from '@/components/page/agent/choose/baomuSkill.vue'
    import baomuImg from '@/components/page/agent/choose/baomuImg.vue'
    import baomuhb from '@/components/page/agent/choose/baomuhb.vue'
    import baomurate from '@/components/page/agent/choose/baomurate.vue'
    import baomuWorkLog from "@/components/page/agent/choose/baomuWorkLog";
    import BaomuLevel from "@/components/page/agent/choose/BaomuLevel";
    import jzyDetail from "@/components/page/agent/choose/jzyDetail";
    import baomuInfo from "./info/baomuInfo";
    import {dateFormat} from 'vux'
    import {formatDate} from '@/components/common/utils.js'

    export default {
        data() {
            return {
                activeName: 'first',
                nannyId: null,
                nannySelfDis: false,
                agentDis: false,
                showSelfReset: false,
                showAgentReset: false,
                nannyMyselfScore: {
                    nannyId: null,
                    // 保洁评分
                    cleanScore: 0,
                    // 护幼评分
                    careChildScore: 0,
                    // 陪护评分
                    escortScore: 0,
                    // 宠物评分
                    petsScore: 0,
                    // 收纳评分
                    arrangeScore: 0,
                    // 厨艺评分
                    cookScore: 0,
                    // 总分
                    totalScore: 0
                },
                agentScore: {
                    agentId: null,
                    nannyId: null,
                    // 保洁评分
                    cleanScore: 0,
                    // 护幼评分
                    careChildScore: 0,
                    // 陪护评分
                    escortScore: 0,
                    // 宠物评分
                    petsScore: 0,
                    // 收纳评分
                    arrangeScore: 0,
                    // 厨艺评分
                    cookScore: 0,
                    // 总分
                    totalScore: 0
                },
                colors: ['#99A9BF', '#F7BA2A', '#FF9900'],
                baomuId: null,
                showBaomuInfo: false,
                index: null,
                showBaomuWorkLog: false,
                showBaomuScoreModel: false,
                showJzyDetail: false,
                searchMore: true,
                radio1: '原版',
                eduoptions: [{
                    value: 0,
                    label: '无'
                }, {
                    value: 2,
                    label: '无需求'
                }, {
                    value: 3,
                    label: '小学'
                }, {
                    value: 4,
                    label: '中学'
                }, {
                    value: 5,
                    label: '高中'
                }, {
                    value: 6,
                    label: '大专'
                }, {
                    value: 7,
                    label: '本科及以上'
                }, {
                    value: 8,
                    label: '中专'
                }],
                sources: [{
                    value: null,
                    label: '全部'
                }, {
                    value: '朋友介绍',
                    label: '朋友介绍'
                }, {
                    value: '经纪人介绍',
                    label: '经纪人介绍'
                }, {
                    value: '其它部门',
                    label: '其它部门'
                }, {
                    value: '公交广告',
                    label: '公交广告'
                }, {
                    value: '网络',
                    label: '网络'
                }, {
                    value: '招聘传单',
                    label: '招聘传单'
                }, {
                    value: '人才市场',
                    label: '人才市场'
                }, {
                    value: '招聘会',
                    label: '招聘会'
                }],
                workTypeOptions: [{
                    value: null,
                    label: '全部'
                }, {
                    value: '育儿嫂',
                    label: '育儿嫂'
                }, {
                    value: '家务保姆',
                    label: '家务保姆'
                }, {
                    value: '护工',
                    label: '护工'
                }, {
                    value: '保姆',
                    label: '保姆'
                }, {
                    value: '纯打扫',
                    label: '纯打扫'
                }, {
                    value: '纯做饭',
                    label: '纯做饭'
                },{
                  value: '陪读师',
                  label: '陪读师'
                }],
                workTypes: [{
                    value: '住家',
                    label: '住家'
                }, {
                    value: '不住家',
                    label: '不住家'
                }, {
                    value: '钟点单餐',
                    label: '钟点单餐'
                }, {
                    value: '育儿嫂',
                    label: '育儿嫂'
                }, {
                    value: '护工',
                    label: '护工'
                }, {
                    value: '月嫂',
                    label: '月嫂'
                }, {
                    value: '单餐',
                    label: '单餐'
                }],
                // workTypes: [{
                //     value:null,
                //     label: '全部'
                // },{
                //     value:'住家',
                //     label: '住家'
                // }, {
                //     value: '不住家',
                //     label: '不住家'
                // }, {
                //     value:'单餐',
                //     label: '单餐'
                // } ],
                serverContents: [{
                    value: null,
                    label: '全部'
                }, {
                    value: '做饭',
                    label: '做饭'
                }, {
                    value: '做卫生',
                    label: '做卫生'
                }, {
                    value: '带宝宝（带睡)',
                    label: '带宝宝（带睡)'
                }, {
                    value: '带宝宝（不带睡)',
                    label: '带宝宝（不带睡)'
                }, {
                    value: '看护老人',
                    label: '看护老人'
                }, {
                    value: '看护病人',
                    label: '看护病人'
                }, {
                    value: '照顾产妇',
                    label: '照顾产妇'
                }, {
                  value: '陪读师',
                  label: '陪读师'
                }],
                states: [{
                    value: null,
                    label: '全部'
                }, {
                    value: 1,
                    label: '上架'
                }, {
                    value: 2,
                    label: '下架'
                }, {
                    value: 3,
                    label: '离职'
                },],
                shimingStateoptions: [{
                    value: null,
                    label: '全部'
                }, {
                    value: 2,
                    label: '未认证'
                }, {
                    value: 1,
                    label: '已认证'
                },],
                healths: [{
                    value: null,
                    label: '全部'
                }, {
                    value: '健康证',
                    label: '健康证'
                }, {
                    value: '体检表',
                    label: '体检表'
                },],
                tableData: [],
                pageSizeOpts: [10, 20, 30, 50, 100, 500, 1000],
                multipleSelection: [],
                eid: null,
                ename: null,
                ids: [],
                baomuModel: {
                    RealName: null,
                    No: null,
                },
                restaurants: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                screenWidth: '80%',//新增对话框 宽度
                baomuImgModal: false,
                baomuExpModal: false,
                baomuLevelModal: false,
                baomuSkillModal: false,
                addsModal: false,
                saveModal: false,
                addModal: false,
                baomuhbModal: false,
                baomurateModal: false,
                show: {},
                model: {
                    education: null,
                    otherSkills: null,
                    agentName: null,
                    baomuLevel: null,
                    minAge: null,
                    maxAge: null,
                    storeId: localStorage.getItem("storeId"),
                    address: null,
                    phone: null,
                    showTime: null,
                    startTime: null,
                    endTime: null,
                    source: null,
                    time: null,
                    hometown: null,
                    age: null,
                    workYear: null,
                    health: null,
                    serverContent: null,
                    workType: null,
                    BaomuWorkType: null,
                    state: null,
                    sites: null,
                    orderBy: null,
                    ShimingState: null,
                    securityAuth: null,
                    id: null,
                    No: null,
                    realName: "",
                    current: 1,
                    size: 10

                },
                update: {
                    id: null,
                    realName: null,

                },
                list: null,
                mouseEnterName: "无",
                account: localStorage.getItem("account"),
                roleId: null,
            };
        },
        components: {
            "BaomuLevel": BaomuLevel,
            "baomuWorkLog": baomuWorkLog,
            'baomuAdd': baomuAdd,
            'baomuUpdate': baomuUpdate,
            'baomuExp': baomuExp,
            'baomuImg': baomuImg,
            'baomuSkill': baomuSkill,
            'baomuhb': baomuhb,
            'baomurate': baomurate,
            'jzyDetail': jzyDetail,
            'baomuInfo': baomuInfo,

        },
        created() {
            this.roleId = localStorage.getItem("roleId");
            this.model.orderBy = 'createDate DESC';
            this.getData();
        },
        computed: {},
        methods: {
            checkNumber(num, type) {
                if (type === 1) {
                    if (num != null && num !== '') {
                        const n = Number(num);
                        if (n < 0) {
                            return this.model.minAge = 0;
                        } else if (n > 100) {
                            return this.model.minAge = 100;
                        }
                    }
                } else {
                    if (num != null && num !== '') {
                        const n = Number(num);
                        if (n < 0) {
                            return this.model.maxAge = 0;
                        } else if (n > 100) {
                            return this.model.maxAge = 100;
                        }
                    }
                }
            },
            handleClickTab() {
            },
            showHandleByRoleId() {
                if (this.roleId == null) {
                    return false;
                }
                return this.roleId != 85;
            },
            resetSelfScore() {
                this.$confirm('是否确认重置,重新评分?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.nannyMyselfScore = {
                        nannyId: null,
                        // 保洁评分
                        cleanScore: 0,
                        // 护幼评分
                        careChildScore: 0,
                        // 陪护评分
                        escortScore: 0,
                        // 宠物评分
                        petsScore: 0,
                        // 收纳评分
                        arrangeScore: 0,
                        // 厨艺评分
                        cookScore: 0,
                        // 总分
                        totalScore: 0
                    };
                    this.nannySelfDis = false;
                    this.showSelfReset = false;
                });
            },
            resetAgentScore() {
                this.$confirm('是否确认重置,重新评分?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.agentScore = {
                        agentId: null,
                        nannyId: null,
                        // 保洁评分
                        cleanScore: 0,
                        // 护幼评分
                        careChildScore: 0,
                        // 陪护评分
                        escortScore: 0,
                        // 宠物评分
                        petsScore: 0,
                        // 收纳评分
                        arrangeScore: 0,
                        // 厨艺评分
                        cookScore: 0,
                        // 总分
                        totalScore: 0
                    };
                    this.agentDis = false;
                    this.showAgentReset = false;
                });
            },
            addTotalScore(score, type) {
                if (type === 1) {
                    const t = this.nannyMyselfScore.totalScore;
                    this.nannyMyselfScore.totalScore = Number(t + score);
                } else if (type === 2) {
                    // console.info(score, type);
                    const t = this.agentScore.totalScore;
                    this.agentScore.totalScore = Number(t + score);
                }
            },
            closeScoreDialog() {
                this.nannyMyselfScore = {
                    nannyId: null,
                    // 保洁评分
                    cleanScore: 0,
                    // 护幼评分
                    careChildScore: 0,
                    // 陪护评分
                    escortScore: 0,
                    // 宠物评分
                    petsScore: 0,
                    // 收纳评分
                    arrangeScore: 0,
                    // 厨艺评分
                    cookScore: 0,
                    // 总分
                    totalScore: 0
                };
                this.agentScore = {
                    agentId: null,
                    nannyId: null,
                    // 保洁评分
                    cleanScore: 0,
                    // 护幼评分
                    careChildScore: 0,
                    // 陪护评分
                    escortScore: 0,
                    // 宠物评分
                    petsScore: 0,
                    // 收纳评分
                    arrangeScore: 0,
                    // 厨艺评分
                    cookScore: 0,
                    // 总分
                    totalScore: 0
                };
            },
            addScore(type) {
                if (this.nannyId == null) {
                    this.$message.error("保存失败，请重试");
                    this.showBaomuScoreModel = false;
                    return;
                }
                let param = {};
                if (type === 1) {
                    param = this.nannyMyselfScore;
                } else if (type === 2) {
                    param = this.agentScore;
                    param.agentId = localStorage.getItem("id");
                    if (param.agentId == null) {
                        this.$message.error("保存失败，请重新登录！");
                        return;
                    }
                }
                param.totalScore = Number(param.cleanScore + param.careChildScore + param.escortScore
                    + param.petsScore + param.arrangeScore + param.cookScore);
                if (param.totalScore < 1) {
                    this.$message.error("请至少选择一项评分！");
                    return;
                }
                param.nannyId = this.nannyId;
                param.type = type;
                // console.info('p', param);
                this.$postData("addOrEditNannyScore", param, {}).then(res => {
                    if (res.status === 200) {
                        this.$message.success("保存成功");
                        this.getData();
                    } else {
                        this.$message.error("保存失败，" + res.msg);
                    }
                })
            },
            InfohandleClose() {
                this.baomuId = null;
                this.showBaomuInfo = false;
            },
            toBaomuInfo(id) {
                // console.log(id);
                this.baomuId = id;
                this.showBaomuInfo = true;
                // this.$router.push({path:'/baomuInfo',query:{"id":id }})
            },
            toAgentBaomuData() {
                this.$router.push({path: '/agentBaomuData'})
            },
            addEmployeeToBaomu(baomuId) {
                this.$confirm('是否确认绑定?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let dom = {
                        employeeId: localStorage.getItem("id"),
                        baomuId: baomuId
                    };
                    this.$postData("add_employeeToBaomu", dom, {}).then(res => {
                        if (res.status == 200) {
                            this.$message.success("绑定成功，" + res.msg);
                            this.getData();
                        } else {
                            this.$message.error("绑定失败，" + res.msg);
                        }
                    })
                });
            },
            tableRowClassName({row, rowIndex}) {
                // console.log(row);
                if (row.baomuLevel === 0) {
                    return 'by-row';
                }
                if (rowIndex === 1) {
                    return 'warning-row';
                } else if (rowIndex === 3) {
                    return 'success-row';
                }
                return '';
            },
            toTop(i) {
                window.scrollTo(0, 0);
            },
            mouseEnter(row, column, cell, event) {
                this.mouseEnterName = row.realName + "-" + row.no;
            },
            save(dom, state) {

                if (state == 1 && dom.idcard == null) {
                    return this.$Message.success('请先填写保姆身份证号码');
                } else {
                    dom.state = state;
                    this.$postData("update_employee", dom, {}).then(res => {
                        if (res.status == 200) {
                            this.$Message.success('修改成功');
                        } else {
                            this.$message.error("修改失败，" + res.msg);
                        }
                    })
                }


            },
            sortChange: function (column, prop, order) {

                if (column == null) {
                    this.model.orderBy = "";
                } else {
                    if (column.order == "ascending") {
                        this.model.orderBy = column.prop + " ASC";
                    }
                    if (column.order == "descending") {
                        this.model.orderBy = column.prop + " DESC";
                    }
                }
                this.getData();
            },
            open(item) {
                this.$alert('是否确认认证', '认证确认', {
                    confirmButtonText: '确定',
                    callback: action => {
                        if (action != 'cancel') {
                            item.shimingState = 1;
                            // console.log(item);
                            this.$postData("update_employee", item, {}).then(res => {
                                if (res.status == 200) {
                                    this.$Message.success('认证成功');
                                    this.chooseThisModel();
                                } else {
                                    this.$message.error("认证失败，" + res.msg);
                                }
                            })
                        }
                    }
                });
            },
            saveBaomuWorkLog(baomuWorkLog) {
                this.$postData("saveBaomuWorkLog", baomuWorkLog, {}).then(res => {
                    if (res.status == 200) {

                    } else {
                        this.$message.error("日志保存失败，" + res.msg);
                    }
                })
            },
            openby(item, baomuLevel) {
                // if (baomuLevel != 2) {
                //     if (this.account !== '820' && this.account !== '789') {
                //         return this.$message.error("你无权操作")
                //     }
                // }

                const roleId = localStorage.getItem('roleId');
                if (roleId == null) {
                    return this.$message.error("你无权操作")
                }
                if ((roleId !== '1' && roleId !== '9')) {
                    return this.$message.error("你无权操作")
                }
                this.$confirm('是否确认更改保姆等级', '认证确认', {
                    distinguishCancelAndClose: true,
                    confirmButtonText: '确定',
                    cancelButtonText: baomuLevel == 2 ? '取消' : '驳回',
                    type: 'warning',
                    callback: action => {
                        let baomuWorkLog = {
                            employeeId: item.id,
                            workContent: baomuLevel == 0 ? '变更为（普通保姆）' : baomuLevel == 1 ? '变更为（百优保姆）' : '变更为（推荐保姆）',
                            remark: null,
                            status: 1,
                            billNo: null,
                            contractNo: null,
                            startTime: dateFormat(new Date(), 'YYYY-MM-DD HH:mm:ss'),
                            endTime: null,
                            creatTime: dateFormat(new Date(), 'YYYY-MM-DD HH:mm:ss'),
                            crePerson: localStorage.getItem("realName"),
                            type: 0,
                            title: '保姆级别变更'
                        }
                        if (action === 'confirm') {

                            let baomu = {
                                baomuId: item.id,
                                baomuLevel: baomuLevel
                            };
                            this.$postData("updateBaomuLevelByBaomuId", baomu, {}).then(res => {
                                if (res.status == 200) {
                                    item.baomuLevel = baomu.baomuLevel;
                                    this.$Message.success('更改成功');
                                    this.saveBaomuWorkLog(baomuWorkLog);
                                    this.chooseThisModel();
                                } else {
                                    this.$message.error("更改失败，" + res.msg);
                                }
                            })
                        }
                        if (action === 'cancel') {
                            // console.log(item);
                            let baomu = {
                                baomuId: item.id,
                                baomuLevel: 0
                            };
                            this.$postData("updateBaomuLevelByBaomuId", baomu, {}).then(res => {
                                if (res.status == 200) {
                                    item.baomuLevel = baomu.baomuLevel;
                                    this.$Message.success('更改成功');
                                    baomuWorkLog.workContent = "推荐驳回为（普通保姆）";
                                    this.saveBaomuWorkLog(baomuWorkLog);
                                    this.chooseThisModel();
                                } else {
                                    this.$message.error("更改失败，" + res.msg);
                                }
                            })
                        }
                    }
                });
            },
            ups() {
                if (this.multipleSelection[0] == null) {
                    return this.$message.error("请勾选操作项。");
                }
                this.addsModal = true;
                this.ids = [];
                this.multipleSelection.forEach((item, index, arr) => {

                    this.ids.push(item.id);
                });
                // console.log(this.ids)
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            getData() {
                // if (localStorage.getItem("account").substring(0,4)=='MJGJ'){
                //     if (this.model.No==null){
                //         this.model.No='MJGJ'
                //     }else {
                //         this.model.No='MJGJ'+ this.model.No
                //     }
                //
                // }
                if (this.roleId != null && this.roleId === '85') {
                    let now = new Date();
                    this.model.startTime = this.dateFormat(now.setDate(now.getDate() - 7));
                    this.model.endTime = this.dateFormat(new Date());
                }
                this.$postData("Baomu_page", this.model, {}).then(res => {
                    if (res.status == 200) {
                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            dateFormat: function (time) {
                var date = new Date(time);
                var year = date.getFullYear();
                /* 在日期格式中，月份是从0开始的，因此要加0
                 * 使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05
                 * */
                var month = date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1;
                var day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
                var hours = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
                var minutes = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
                var seconds = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
                // 拼接
                return year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds;
            },
            getNextDay(d) {
                d = new Date(d);
                d = +d + 1000 * 60 * 60 * 24;
                d = new Date(d);
                //return d;
                //格式化
                return d.getFullYear() + "-" + (d.getMonth() + 1) + "-" + d.getDate() + " 00:00:00";

            },
            query() {
                if (this.model.time != null) {
                    this.model.startTime = this.model.time[0];
                    this.model.endTime = this.model.time[1];
                }
                if (this.model.showTime != null) {
                    this.model.startTime = this.model.showTime;
                    this.model.endTime = this.getNextDay(this.model.showTime)
                }
                if (this.model.time == null && this.model.showTime == null) {
                    this.model.startTime = null;
                    this.model.endTime = null;
                }
                this.model.current = 1;
                this.getData();
            },
            re() {
                this.model.agentName = null;
                this.model.hometown = null;
                this.model.age = null;
                this.model.maxAge = null;
                this.model.minAge = null;
                this.model.workYear = null;
                this.model.health = null;
                this.model.serverContent = null;
                this.model.workType = null;
                this.model.baomuWorkType = null;
                this.model.source = null;
                this.model.no = '';
                this.model.realName = '';
                this.model.sites = '';
                this.model.state = null;
                this.model.shimingState = null;
                this.model.securityAuth = null;
                this.model.time = null;
                this.model.phone = null;
                this.model.address = null;

                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                // console.log(size);
                this.model.size = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                this.toTop(50);
                // console.log(index);
                this.model.current = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
                this.addModal = false;
                this.showBaomuWorkLog = false;
            },
            initChooseProject(data) {
                this.getData();
                this.closeCurrModal();

            },
            editRate(id) {
                // console.log(id)
                this.show = id;
                // console.log(this.model);
                this.baomuLevelModal = true;
            },
            edit(id) {
                this.show = id;
                // console.log(this.model);
                this.saveModal = true;
            },
            editbaomuExp(id) {
                this.show = id;
                // console.log(this.model);
                this.baomuExpModal = true;
            },
            editbaomuImg(id) {
                this.show = id;
                // console.log(this.model);
                this.baomuImgModal = true;
            },
            editbaomurate(id) {
                this.show = id;
                // console.log(this.model);
                this.baomurateModal = true;
            },
            editbaomuSkill(id) {
                this.show = id;
                // console.log(this.model);
                this.baomuSkillModal = true;
            },
            editbaomuScore(row) {
                this.nannyId = row.id;
                this.nannySelfDis = false;
                this.showSelfReset = false;
                this.agentDis = false;
                this.showAgentReset = false;
                this.$getData("getNannyScore", {employeeId: this.nannyId}).then(res => {
                    if (res.status === 200) {
                        res.data.forEach(v => {
                            if (v.type === 1) {
                                this.nannyMyselfScore = v;
                                this.nannySelfDis = true;
                                this.showSelfReset = true;
                            } else if (v.type === 2) {
                                this.agentScore = v;
                                this.agentDis = true;
                                this.showAgentReset = true;
                            }
                        });
                    } else {
                    }
                    this.showBaomuScoreModel = true;
                });
                // console.log(this.model);
            },
            editbaomuLog(id) {
                this.show = id;
                // console.log(this.model);
                this.showBaomuWorkLog = true;
            },
            editbaomuhb(id) {
                this.show = id;
                this.baomuhbModal = true;
            },
            //------------
            handleSelect(item) {
                this.eid = item.name;
                //this.addTo.baomuId=this.dom.id;

            },
            querySearchAsync(queryString, cb) {
                this.restaurants = [];
                this.baomuModel.RealName = this.ename;
                this.$postData("agent_list", this.baomuModel, {}).then(res => {
                    if (res.status == 200) {

                        res.data.forEach((item, index, arr) => {
                            var a = {};
                            a.value = item.realName;
                            a.name = item.id;
                            this.restaurants.push(a);
                        });

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                });
                var restaurants = this.restaurants;
                var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants;
                cb(restaurants);
            },
            createStateFilter(queryString) {
                return (state) => {
                    return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                };
            },
            upTo() {
                // console.log(this.ids);
                this.$postData("up_employeeToBaomus", this.ids, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.success("解绑成功，" + res.msg);
                        this.getData();
                        this.addsModal = false;
                    } else {
                        this.$message.error("解绑失败，" + res.msg);
                    }
                })
            },
            saveTo() {
                this.$postUrl("add_employeeToBaomus", this.eid, this.ids, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.success("绑定成功，" + res.msg);
                        this.getData();
                        this.addsModal = false;
                    } else {
                        this.$message.error("绑定失败，" + res.msg);
                    }
                })

            }
        }
    };
</script>

<style scoped>
    .by-row {
        background: #f0bd35;

    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }

</style>
