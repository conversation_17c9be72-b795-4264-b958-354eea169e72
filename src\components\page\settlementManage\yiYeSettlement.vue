<template>
    <div>
        <div>
            <el-form :inline="true" class="demo-form-inline">
                <div style="margin: 20px 10px 10px 10px">
                    <el-form-item label="归属门店">
                        <el-select ref="storeNameSel" filterable v-model="dom.storeId" placeholder="请选择门店"
                                   @change="changeStoreData">
                            <el-option
                                    v-for="(item,index) in storeList"
                                    :key="index"
                                    :label="item.storeName"
                                    :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="归属部门">
                        <el-select ref="deptNameSel" filterable v-model="dom.deptId" placeholder="请先选择门店">
                            <el-option
                                    v-for="(item,index) in deptList"
                                    :key="index"
                                    :label="item.name"
                                    :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="服务员工">
                        <el-input v-model="dom.serviceNo" placeholder="请精准输入服务员工工号" clearable
                                  @keyup.enter.native="onQuery"></el-input>
                    </el-form-item>
                    <el-form-item label="结算人员">
                        <el-input v-model="dom.paySettlementPersonNo" placeholder="请精准输入结算人员工号" clearable
                                  @keyup.enter.native="onQuery"></el-input>
                    </el-form-item>
                    <el-form-item label="产品类别">
                        <el-select ref="productCategorySel" filterable v-model="dom.productCategoryId"
                                   placeholder="请选择产品类别" @change="changeProductCategoryData">
                            <el-option
                                    v-for="(item,index) in productCategoryList"
                                    :key="index"
                                    :label="item.name"
                                    :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="服务项目">
                        <el-select ref="productSel" filterable v-model="dom.productId" placeholder="请先选择产品类别">
                            <el-option
                                    v-for="(item,index) in productList"
                                    :key="index"
                                    :label="item.name"
                                    :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="结算状态">
                        <el-select ref="settlementStateSel" filterable v-model="dom.paySettlement"
                                   placeholder="请选择结算状态" @change="changeSettlementState">
                            <el-option
                                    v-for="(item,index) in settlementStateList"
                                    :key="index"
                                    :label="item.text"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="结算时间">
                        <el-date-picker
                                style="width: 280px"
                                :clearable="true"
                                v-model="dom.settlementTime"
                                type="daterange"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                :default-time="['00:00:00', '23:59:59']"
                                range-separator="~"
                                @change="changeSettlementTime"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="服务时间">
                        <el-date-picker
                                style="width: 280px"
                                :clearable="true"
                                v-model="dom.serviceTime"
                                type="daterange"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                :default-time="['00:00:00', '23:59:59']"
                                range-separator="~"
                                @change="changeServiceTime"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                    <!--<el-form-item label="支付时间">-->
                    <!--    <el-date-picker-->
                    <!--            style="width: 280px"-->
                    <!--            :clearable="true"-->
                    <!--            v-model="dom.payTime"-->
                    <!--            type="daterange"-->
                    <!--            format="yyyy-MM-dd"-->
                    <!--            value-format="yyyy-MM-dd HH:mm:ss"-->
                    <!--            :default-time="['00:00:00', '23:59:59']"-->
                    <!--            range-separator="~"-->
                    <!--            @change="changePayTime"-->
                    <!--            start-placeholder="开始日期"-->
                    <!--            end-placeholder="结束日期">-->
                    <!--    </el-date-picker>-->
                    <!--</el-form-item>-->
                </div>
                <div style="display: flex;justify-content: space-between;">
                    <div>
                        <el-form-item>
                            <el-button icon="el-icon-upload2" type="success" @click="importHandler('ysb')"
                                       v-loading="loading"
                                       :disabled="loading">
                                导入医社保
                            </el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button icon="el-icon-upload2" type="success" @click="importHandler('gjj')"
                                       v-loading="loading"
                                       :disabled="loading">
                                导入公积金
                            </el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button icon="el-icon-upload2" type="success" @click="importHandler('gzx')"
                                       v-loading="loading"
                                       :disabled="loading">
                                导入公众险
                            </el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button icon="el-icon-upload2" type="success" @click="importHandler('other')"
                                       v-loading="loading"
                                       :disabled="loading">
                                导入其他奖金
                            </el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button icon="el-icon-upload2" type="success" @click="importHandler('gs')"
                                       v-loading="loading"
                                       :disabled="loading">
                                导入专项附加
                            </el-button>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item>
                            <el-button icon="el-icon-search" type="primary" @click="onQuery" v-loading="loading"
                                       :disabled="loading">
                                查询
                            </el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button icon="el-icon-refresh" @click="reset" v-loading="loading" :disabled="loading">
                                重置
                            </el-button>
                        </el-form-item>
                        <!--<el-form-item v-if="roleId != null && roleId === '1'">-->
                        <!--    <el-button icon="el-icon-download" type="success" @click="exportExcel" v-loading="loading"-->
                        <!--               :disabled="loading">-->
                        <!--        导出-->
                        <!--    </el-button>-->
                        <!--</el-form-item>-->
                        <el-form-item>
                            <el-button icon="el-icon-money" type="primary" @click="isOpenDrawer = true"
                                       v-loading="loading"
                                       :disabled="loading">
                                分析
                            </el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button icon="el-icon-download" type="success" v-loading="loading" v-if="!showExcelBtn"
                                       :disabled="loading">
                                导出
                            </el-button>
                            <div class="exportCls" v-if="showExcelBtn" @click="handleShowExcelBtn">
                                <download-excel class="blueBtn"
                                                :data="tempList"
                                                :fields="jsonFields"
                                                worksheet="sheet1"
                                                name="一野结算数据"
                                >
                                    <i class="el-icon-download el-icon--right"></i>&nbsp;导出
                                </download-excel>
                            </div>
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </div>
        <div>
            <div>
                <el-row>
                    <center>
                        <el-col :span="12">
                            <el-card shadow="hover">
                                订单金额: <span class="allAmount">￥{{orderAmount}}</span>
                                <span style="color: #6f7180">(均价: ￥<span style="color: #f00f14">{{(Number(orderAmount) / pageInfo.total).toFixed(2)}}</span>)</span>
                            </el-card>
                        </el-col>
                        <el-col :span="12">
                            <el-card shadow="hover">
                                薪酬收入: <span class="allAmount">￥{{orderWage}}</span>
                                <span style="color: #6f7180">(均价: ￥<span style="color: #f00f14">{{(Number(orderWage) / pageInfo.total).toFixed(2)}}</span>)</span>
                            </el-card>
                        </el-col>
                    </center>
                </el-row>
            </div>
            <el-table :data="list" :header-cell-style="{background:'#d2d3d5'}" border class="table" ref="multipleTable"
                      v-loading="loading">

                <el-table-column
                        label="操作"
                        align="center"
                        min-width="50">
                    <template slot-scope="scope" v-if="scope.row.paySettlement !== 2">
                        <el-button size="mini" @click="settlementBillNo(scope.row.billNo)" type="primary">结算</el-button>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="billNo"
                        header-align="center"
                        align="center"
                        label="总订单号"
                ></el-table-column>
                <el-table-column
                        prop="storeName"
                        align="center"
                        label="所属门店"
                ></el-table-column>
                <el-table-column
                        prop="memberAccount"
                        align="center"
                        label="会员账号"
                >
                    <template slot-scope="scope">
                        <!--<el-link type="primary">{{scope.row.memberAccount}}</el-link>-->
                        <span>{{scope.row.memberAccount}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        prop="productName"
                        label="服务产品"
                >
                </el-table-column>
                <el-table-column
                        align="center"
                        prop="serviceTime"
                        label="服务时间"
                >
                </el-table-column>
                <el-table-column
                        align="center"
                        prop="servicePersonNo"
                        label="服务员工"
                >
                    <template slot-scope="scope">
                        <!--<el-link type="primary">{{scope.row.servicePersonName }}[{{scope.row.servicePersonNo}}]</el-link>-->
                        <span>{{scope.row.servicePersonName }}[{{scope.row.servicePersonNo}}]</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="orderAmount"
                        label="订单金额"
                        align="right"
                        width="100"
                >
                    <template slot-scope="scope">
                        <!--<el-link type="primary">{{scope.row.paySettlementPersonName }}[{{scope.row.paySettlementPersonNo}}]</el-link>-->
                        <span><span style="color: red;">￥</span>{{ scope.row.orderAmount }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="paySettlementPersonNo"
                        label="结算人员"
                        align="center"
                >
                    <template slot-scope="scope">
                        <!--<el-link type="primary">{{scope.row.paySettlementPersonName }}[{{scope.row.paySettlementPersonNo}}]</el-link>-->
                        <span>{{scope.row.paySettlementPersonName }}[{{scope.row.paySettlementPersonNo}}]</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="paySettlementTime"
                        align="center"
                        label="结算时间">
                    <template slot-scope="scope">
                        <span v-if="scope.row.paySettlementTime != null">
                            {{scope.row.paySettlementTime}}
                        </span>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination
                    style="margin-top: 10px; "
                    @size-change="onPageSizeChange"
                    @current-change="onChange"
                    :current-page="pageInfo.current"
                    :page-sizes="pageSizeOpts"
                    :page-size="pageInfo.size"
                    layout="->, total, prev, pager, next, sizes"
                    :total="pageInfo.total">
            </el-pagination>
        </div>
        <div>
            <el-drawer
                    size="40%"
                    title=""
                    :with-header="false"
                    :visible.sync="isOpenDrawer"
                    :direction="'rtl'"
                    :before-close="handleCloseDrawer">
                <div style="margin-top: 30px;">
                    <div style="display: flex;justify-content: space-between;width: 90%;">
                        <div style="text-align: left;font-size: 16px;color: red; margin-left: 2vw;font-weight: 600;">
                            本页面关联外部页面的条件查询
                        </div>
                        <div>
                            <!--<el-button icon="el-icon-download" type="success" @click="exportSumExcel"-->
                            <!--           v-loading="loading"-->
                            <!--           :disabled="loading" size="mini">-->
                            <!--    导出-->
                            <!--</el-button>-->
                            <el-button icon="el-icon-download" type="success" v-if="!showExcelBtn"
                                       v-loading="loading"
                                       :disabled="loading" size="mini">
                                导出
                            </el-button>
                            <div class="exportCls" v-if="showExcelBtn" @click="handleShowExcelBtn">
                                <download-excel class="blueBtn"
                                                :data="tempSumList"
                                                :fields="jsonFields1"
                                                worksheet="sheet1"
                                                name="一野员工薪酬结算"
                                >
                                    <i class="el-icon-download el-icon--right"></i>&nbsp;导出
                                </download-excel>
                            </div>
                        </div>
                    </div>
                    <div style="margin: 0 2vw; margin-top: 20px;width: 85%;">
                        <el-table :data="sumList" :header-cell-style="{background:'#d2d3d5'}" border class="table"
                                  ref="multipleTable"
                                  v-loading="loading">
                            <el-table-column
                                    align="center"
                                    prop="servicePersonNo"
                                    label="服务员工"
                            >
                                <template slot-scope="scope">
                                    <!--<el-link type="primary">{{scope.row.servicePersonName }}[{{scope.row.servicePersonNo}}]</el-link>-->
                                    <span>{{scope.row.serviceName }}[{{scope.row.serviceNo}}]</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    prop="orderAmount"
                                    label="薪酬"
                                    align="center"
                            >
                                <template slot-scope="scope">
                                    <!--<el-link type="primary">{{scope.row.paySettlementPersonName }}[{{scope.row.paySettlementPersonNo}}]</el-link>-->
                                    <span><span style="color: red;">￥</span>{{ scope.row.realWage }}</span>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div style="margin-top: 10px;">
                            <el-pagination
                                    @size-change="onSumPageSizeChange"
                                    @current-change="onSumChange"
                                    :current-page="sumPageInfo.current"
                                    :page-sizes="sumPageSizeOpts"
                                    :page-size="sumPageInfo.size"
                                    layout="->, total, prev, pager, next, sizes"
                                    :total="sumPageInfo.total">
                            </el-pagination>
                        </div>
                        <br/>
                        <br/>
                    </div>

                </div>
            </el-drawer>
        </div>
        <div>
            <Modal v-model="isModal" class="Modal" :width="screenWidth" :title="'导入' + title"
                   :mask-closable="false">
                <div class="addBody">
                    <currency-import-excel
                            v-if="isModal"
                            :excelImportUrl="excelImportActionUrl"
                            @init-choose="initChooseProject"
                            @close-modal="closeCurrYsbModal">
                    </currency-import-excel>
                </div>
                <div slot="footer"></div>
            </Modal>
        </div>
    </div>
</template>

<script>
    import {dateFormat} from 'vux'
    import reqs from "../../../config/axios";
    import currencyImportExcel from '@/components/page/settlementManage/importExcel/currencyImportExcel.vue'

    export default {
        name: "yiYeSettlement",
        components: {
            "currencyImportExcel": currencyImportExcel
        },
        data() {
            return {
                showExcelBtn: true,
                isOpenDrawer: false,
                title: '',
                // modal 宽度
                screenWidth: "460px",
                isModal: false,
                // 医社保组件显隐
                isYsbModal: false,
                // 公积金组件显隐
                isGjjModal: false,
                // 公众险组件显隐
                isGzxModal: false,
                // 其他奖金组件显隐
                isOtherModal: false,
                // 专项附加组件显隐
                isGsModal: false,
                // 全局接口请求控制 加载动画
                loading: false,
                // 表格数据
                list: [],
                tempList: [],
                // 订单金额
                orderAmount: 0,
                // 薪酬
                orderWage: 0,
                // 分析数据 汇总数据
                sumList: [],
                tempSumList: [],
                // 接口请求条件
                dom: {
                    storeId: null,
                    deptId: null,
                    serviceNo: null,
                    paySettlementPersonNo: null,
                    productCategoryId: null,
                    productId: null,
                    paySettlement: 2,

                    settlementTime: null,
                    paySettlementStartTime: null,
                    paySettlementEndTime: null,

                    serviceTime: null,
                    serviceStartTime: null,
                    serviceEndTime: null,

                    payTime: null,
                    payStartTime: null,
                    payEndTime: null,

                    eId: Number(localStorage.getItem('id')),

                    size: 15,
                    current: 1,

                    sumSize: 20,
                    sumCurrent: 1
                },
                roleId: localStorage.getItem('roleId'),
                // 门店数据集合
                storeList: [],
                // 产品分类数据集合
                productCategoryList: [],
                // productCategoryList: [
                //     {"text": "11", "value": 0},
                //     {"text": "22", "value": 1},
                //     {"text": "33", "value": 2}
                // ],
                // 部门数据集合
                deptList: [],
                // 产品数据集合
                productList: [],
                // 结算状态数据集合
                settlementStateList: [
                    {"text": "全部", "value": null},
                    {"text": "已结算", "value": 2},
                    {"text": "未结算", "value": 1}
                ],
                // 分页选择项
                pageSizeOpts: [10, 15, 20, 30, 50, 100, 200, 300, 500, 1000, 2000],
                // 分页初始化项
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                // 分析、汇总 分页选择项
                sumPageSizeOpts: [10, 15, 20, 30, 50, 100, 200, 300, 500, 1000, 2000],
                // 分析、汇总 分页初始化项
                sumPageInfo: {total: 10, size: 10, current: 1, pages: 1},
                // 接口请求前缀地址
                requestPrefixUrl: null,
                // 导入excel地址前缀
                excelImportPrefixUrl: null,
                // 导入医社保接口
                excelImportYsbUrl: null,
                // 导入公积金接口
                excelImportGjjUrl: null,
                // 导入公众险接口
                excelImportGzxUrl: null,
                // 导入其他奖金接口
                excelImportOtherUrl: null,
                // 导入专项附加接口
                excelImportGsUrl: null,
                // 通用
                excelImportActionUrl: null,
                jsonFields: {
                    "总订单号": "billNo",
                    "门店名称": "storeName",
                    "会员账号": "memberAccount",
                    "产品名称": "productName",
                    "服务时间": "serviceTime",
                    "订单金额": "orderAmount",
                    "服务员工工号": "servicePersonNo",
                    "服务员工姓名": "servicePersonName",
                    "结算时间": "paySettlementTime",
                    "结算人姓名": "paySettlementPersonName",
                    "结算人工号": "paySettlementPersonNo",
                },
                jsonFields1: {
                    "服务员工工号": "serviceNo",
                    "服务员工姓名": "serviceName",
                    "薪酬": "realWage"
                },
            }
        },
        async created() {
        },
        async mounted() {
            await this.initInterface();
            await this.initServiceTime();
            await this.getStoreData();
            await this.getProductCategoryData();
            this.onQuery();
        },
        methods: {
            handleShowExcelBtn() {
                this.showExcelBtn = false;
                this.loading = true;
                setTimeout(() => {
                    this.showExcelBtn = true;
                    this.loading = false;
                }, 3000);
            },
            settlementBillNo(billNo) {
                const param = {
                    'billNo': billNo,
                    'employeeId': this.dom.eId
                };
                this.loading = true;
                this.showExcelBtn = false;
                const url = this.requestPrefixUrl + '/admins/updateOrderSettlementInfo';
                this.$postDataSelfUrl(url, JSON.stringify(param), {}).then(res => {
                    if (res.code === 0) {
                        this.onQuery();
                        return this.$message.success('结算成功');
                    }
                    this.$message.error(res.msg);
                }).finally(() => {
                    this.loading = false;
                    this.showExcelBtn = true;
                });
            },
            selectImportTypeUrl(type) {
                this.excelImportActionUrl = null;
                this.title = null;
                if (type == null) {
                    return this.$message.error('类型错误');
                }
                switch (type) {
                    case 'ysb':
                        this.title = '医社保';
                        this.excelImportActionUrl = this.excelImportYsbUrl;
                        break;
                    case 'gjj':
                        this.title = '公积金';
                        this.excelImportActionUrl = this.excelImportGjjUrl;
                        break;
                    case 'gzx':
                        this.title = '公众险';
                        this.excelImportActionUrl = this.excelImportGzxUrl;
                        break;
                    case 'other':
                        this.title = '其他奖金';
                        this.excelImportActionUrl = this.excelImportOtherUrl;
                        break;
                    case 'gs':
                        this.title = '专项附加';
                        this.excelImportActionUrl = this.excelImportGsUrl;
                        break;
                    default:
                        this.$message.error('类型错误');
                        break;
                }
            },
            importHandler(type) {
                this.selectImportTypeUrl(type);
                if (this.excelImportActionUrl == null) {
                    return this.$message.error('类型错误');
                }
                this.loading = true;
                this.showExcelBtn = false;
                const checkUrl = this.requestPrefixUrl + '/admins/checkThisMonthIsExistImportData/' + type;
                const deleteUrl = this.requestPrefixUrl + '/admins/deleteThisMonthData/' + type;
                this.$getDataSelfUrl(checkUrl, null).then(res => {
                    if (res.code === 0) {
                        if (res.data == null) {
                            this.isModal = true;
                            this.loading = false;
                        } else {
                            this.$confirm(res.data + ', 是否删除?', '提示', {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }).then(() => {
                                this.$getDataSelfUrl(deleteUrl, null).then(res => {
                                    if (res.code === 0) {
                                        this.$message.error(res.data);
                                        this.isModal = true;
                                    } else {
                                        this.$message.error(res.msg);
                                    }
                                }).finally(() => {
                                    this.loading = false;
                                    this.showExcelBtn = true;
                                });
                            }).catch(() => {
                                // this.$message.info('已取消');
                                this.loading = false;
                                this.showExcelBtn = true;
                            });
                        }
                    } else {
                        this.$message.error(res.msg);
                        this.loading = false;
                        this.showExcelBtn = true;
                    }
                });
            },
            initInterface() {
                // [生产环境]: 'http://apitest.xiaoyujia.com:9999'
                // [本地环境]: 'http://localhost:9999'
                // [203服务器测试环境]: 'https://api.xiaoyujia.com'
                // 在 axios 中的 otherUrl 变量
                this.requestPrefixUrl = reqs.otherUrl;
                // 导入excel地址前缀
                this.excelImportPrefixUrl = this.requestPrefixUrl + '/admins/batchImportYiYe';
                // 导入医社保接口
                this.excelImportYsbUrl = this.excelImportPrefixUrl + 'Ysb';
                // 导入公积金接口
                this.excelImportGjjUrl = this.excelImportPrefixUrl + 'Gjj';
                // 导入公众险接口
                this.excelImportGzxUrl = this.excelImportPrefixUrl + 'Gzx';
                // 导入其他奖金接口
                this.excelImportOtherUrl = this.excelImportPrefixUrl + 'Other';
                // 导入专项附加接口
                this.excelImportGsUrl = this.excelImportPrefixUrl + 'Gs';
            },
            initServiceTime() {
                const date = new Date();
                date.setDate(date.getDate() + 1);
                this.dom.serviceStartTime = dateFormat(new Date(), 'YYYY-MM-DD') + ' 00:00:00';
                this.dom.serviceEndTime = dateFormat(date, 'YYYY-MM-DD') + ' 23:59:59';
                this.dom.serviceTime = [this.dom.serviceStartTime, this.dom.serviceEndTime];
            },
            /**
             * 获取门店数据
             */
            getStoreData() {
                this.loading = true;
                this.showExcelBtn = false;
                let self = this;
                $.ajax({
                    // type可以为post也可以为get
                    type: "GET",
                    url: self.requestPrefixUrl + "/system/getSelfStoreList",
                    contentType: "application/json",
                    // 这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                    data: {},
                    // 这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                    dataType: "json",
                    success: function (data) {
                        if (data.code === 0) {
                            const param = [{"storeName": "全部", "id": null}];
                            self.storeList = self.arrayMerge(param, data.data);
                        } else {
                            self.$message.error("门店数据查询失败，" + data.msg);
                        }
                        self.loading = false;
                        self.showExcelBtn = true;
                    },
                    error: function (data) {
                        self.loading = false;
                        self.showExcelBtn = true;
                        self.$message.error("门店数据查询失败，" + data.responseJSON.msg);
                        //alert("出现了错误！");
                    }
                });
            },
            /**
             * 门店条件变更联动部门
             */
            async changeStoreData(value) {
                if (value == null || value === '') {
                    return;
                }
                this.dom.deptId = null;
                this.deptList = [];
                await this.getDeptData(value);
            },
            /**
             * 获取部门数据
             */
            getDeptData(value) {
                this.loading = true;
                this.showExcelBtn = false;
                let self = this;
                $.ajax({
                    // type可以为post也可以为get
                    type: "GET",
                    url: self.requestPrefixUrl + "/system/getDeptByStoreId/" + value,
                    contentType: "application/json",
                    // 这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                    data: {},
                    // 这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                    dataType: "json",
                    success: function (data) {
                        if (data.code === 0) {
                            const param = [{"name": "全部", "id": null}];
                            self.deptList = self.arrayMerge(param, data.data);
                        } else {
                            self.$message.error("部门数据查询失败，" + data.msg);
                        }
                        self.loading = false;
                        self.showExcelBtn = true;
                    },
                    error: function (data) {
                        self.loading = false;
                        self.showExcelBtn = true;
                        self.$message.error("部门数据查询失败，" + data.responseJSON.msg);
                        //alert("出现了错误！");
                    }
                });
            },
            /**
             * 获取产品分类数据
             */
            getProductCategoryData() {
                // const list = this.productCategoryList;
                // const param = [{"text": "全部", "value": null}];
                // this.productCategoryList = this.arrayMerge(param, list);
                this.loading = true;
                this.showExcelBtn = false;
                let self = this;
                $.ajax({
                    // type可以为post也可以为get
                    type: "GET",
                    url: self.requestPrefixUrl + "/product/getAllProductCategory",
                    contentType: "application/json",
                    // 这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                    data: {},
                    // 这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                    dataType: "json",
                    success: function (data) {
                        // console.info('data', data);
                        if (data.code === 0) {
                            const param = [{"name": "全部", "id": null}];
                            self.productCategoryList = self.arrayMerge(param, data.data);
                        } else {
                            self.$message.error("产品分类数据查询失败，" + data.msg);
                        }
                        self.loading = false;
                        self.showExcelBtn = true;
                    },
                    error: function (data) {
                        self.loading = false;
                        self.showExcelBtn = true;
                        self.$message.error("产品分类数据查询失败，" + data.responseJSON.msg);
                        //alert("出现了错误！");
                    }
                });
            },
            /**
             * 产品分类条件变更联动产品数据
             */
            changeProductCategoryData(value) {
                if (value == null || value === '') {
                    return;
                }
                this.dom.productId = null;
                this.productList = [];
                this.getProductData(value);
            },
            /**
             * 获取产品数据
             */
            getProductData(value) {
                this.loading = true;
                let self = this;
                $.ajax({
                    // type可以为post也可以为get
                    type: "GET",
                    url: self.requestPrefixUrl + "/product/getProductByCategoryId/" + value,
                    contentType: "application/json",
                    // 这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                    data: {},
                    // 这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                    dataType: "json",
                    success: function (data) {
                        if (data.code === 0) {
                            const param = [{"name": "全部", "id": null}];
                            self.productList = self.arrayMerge(param, data.data);
                        } else {
                            self.$message.error("产品数据查询失败，" + data.msg);
                        }
                        self.loading = false;
                    },
                    error: function (data) {
                        self.loading = false;
                        self.$message.error("产品数据查询失败，" + data.responseJSON.msg);
                        //alert("出现了错误！");
                    }
                });
            },
            /**
             * 初始化查询
             */
            onQuery() {
                this.pageInfo.current = 1;
                this.dom.current = 1;
                this.getData();
                this.getYiYeSettlementCount();
                this.getSumData();
            },
            getYiYeSettlementCount() {
                const url = this.requestPrefixUrl + '/admins/getYiYeSettlementCount';
                this.$postDataSelfUrl(url, JSON.stringify(this.dom), {}).then(res => {
                    if (res.code === 0) {
                        const data = res.data;
                        this.orderAmount = data.orderAmount;
                        this.orderWage = data.orderWage;
                    } else {
                        this.$message.info('统计失败,' + res.msg);
                    }
                });
            },
            /**
             * 查询一野结算数据
             */
            getData() {
                this.loading = true;
                this.showExcelBtn = false;
                this.tempList = [];
                let self = this;
                $.ajax({
                    // type可以为post也可以为get
                    type: "POST",
                    url: self.requestPrefixUrl + "/admins/getYiYeSettlementPage",
                    contentType: "application/json",
                    // 这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                    data: JSON.stringify(self.dom),
                    // 这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                    dataType: "json",
                    success: function (data) {
                        if (data.code === 0) {
                            const list = JSON.parse(JSON.stringify(data.data.records));
                            for (let listKey in list) {
                                let temp = list[listKey];
                                temp.billNo = "`" + temp.billNo;
                                self.tempList.push(temp);
                            }
                            self.list = data.data.records;
                            self.pageInfo.current = data.data.current;
                            self.pageInfo.size = data.data.size;
                            self.pageInfo.total = data.data.total;
                        } else {
                            self.$message.error("查询失败，" + data.msg);
                        }
                        self.showExcelBtn = true;
                        self.loading = false;
                    },
                    error: function (data) {
                        self.loading = false;
                        self.showExcelBtn = true;
                        self.$message.error("查询失败，" + data.responseJSON.msg);
                        //alert("出现了错误！");
                    }
                });
            },
            /**
             * 查询一野结算数据
             */
            getSumData() {
                this.loading = true;
                this.showExcelBtn = false;
                this.tempSumList = [];
                let self = this;
                $.ajax({
                    // type可以为post也可以为get
                    type: "POST",
                    url: self.requestPrefixUrl + "/admins/getYiYeSettlementSumPage",
                    contentType: "application/json",
                    // 这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                    data: JSON.stringify(self.dom),
                    // 这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                    dataType: "json",
                    success: function (data) {
                        if (data.code === 0) {
                            const list = JSON.parse(JSON.stringify(data.data.records));
                            for (let listKey in list) {
                                let temp = list[listKey];
                                temp.servicePersonNo = "`" + temp.servicePersonNo;
                                self.tempSumList.push(temp);
                            }
                            self.sumList = data.data.records;
                            self.sumPageInfo.current = data.data.current;
                            self.sumPageInfo.size = data.data.size;
                            self.sumPageInfo.total = data.data.total;
                        } else {
                            self.$message.error("查询失败，" + data.msg);
                        }
                        self.loading = false;
                        self.showExcelBtn = true;
                    },
                    error: function (data) {
                        self.loading = false;
                        self.showExcelBtn = true;
                        self.$message.error("查询失败，" + data.responseJSON.msg);
                        //alert("出现了错误！");
                    }
                });
            },
            /**
             * 重置搜索条件，并初始化请求
             */
            async reset() {
                this.dom.storeId = null;
                this.dom.deptId = null;
                this.dom.serviceNo = null;
                this.dom.paySettlementPersonNo = null;
                this.dom.productCategoryId = null;
                this.dom.productId = null;
                this.dom.paySettlement = 1;
                this.dom.settlementTime = null;
                this.dom.paySettlementStartTime = null;
                this.dom.paySettlementEndTime = null;
                this.dom.serviceTime = null;
                this.dom.serviceStartTime = null;
                this.dom.serviceEndTime = null;
                this.dom.payTime = null;
                this.dom.payStartTime = null;
                this.dom.payEndTime = null;
                await this.initServiceTime();
                await this.onQuery();
            },
            /**
             * 重载分页数, pageSize 改变时会触发
             */
            onPageSizeChange(size) {
                this.dom.current = 1;
                this.pageInfo.current = 1;
                this.dom.size = size;
                this.getData();
            },
            /**
             * 切换其他页, currentPage 改变时会触发
             */
            onChange(index) {
                // console.log(index)
                this.dom.current = index;
                this.getData();
            },
            /**
             * 重载分页数, pageSize 改变时会触发
             */
            onSumPageSizeChange(size) {
                this.dom.sumCurrent = 1;
                this.sumPageInfo.current = 1;
                this.dom.sumSize = size;
                this.getSumData();
            },
            /**
             * 切换其他页, currentPage 改变时会触发
             */
            onSumChange(index) {
                // console.log(index)
                this.dom.sumCurrent = index;
                this.getSumData();
            },
            changeSettlementState(value) {
                const st = this.dom.settlementTime;
                if ((value == null || value === 1)
                    && st != null && st.length > 0) {
                    this.$message.info('结算状态切换至未结算或全部时，自动清空结算时间');
                    this.dom.settlementTime = null;
                    this.dom.paySettlementStartTime = null;
                    this.dom.paySettlementEndTime = null;
                }
            },
            /**
             * 时间搜索条件赋值
             */
            changeSettlementTime(v, s) {
                const ss = this.dom.paySettlement;
                if (ss == null || ss === 1) {
                    this.dom.settlementTime = null;
                    this.dom.paySettlementStartTime = null;
                    this.dom.paySettlementEndTime = null;
                    return this.$message.info('结算状态为未结算或全部时，不允许选择结算时间');
                }
                if (v == null) {
                    this.dom.paySettlementStartTime = null;
                    this.dom.paySettlementEndTime = null;
                    return;
                }
                this.dom.paySettlementStartTime = v[0];
                this.dom.paySettlementEndTime = v[1];
            },
            changeServiceTime(v, s) {
                if (v == null) {
                    this.dom.serviceStartTime = null;
                    this.dom.serviceEndTime = null;
                    return;
                }
                this.dom.serviceStartTime = v[0];
                this.dom.serviceEndTime = v[1];
            },
            changePayTime(v, s) {
                if (v == null) {
                    this.dom.payStartTime = null;
                    this.dom.payEndTime = null;
                    return;
                }
                this.dom.payStartTime = v[0];
                this.dom.payEndTime = v[1];
            },
            /**
             * 关闭当前界面弹出的窗口
             */
            closeCurrModal() {
                this.isModal = false;
            },
            /**
             * 初始化窗口
             */
            initChooseProject() {
                this.closeCurrModal();
                // this.onQuery();
            },
            handleCloseDrawer(done) {
                done();
                // this.$confirm('确认关闭？')
                //     .then(_ => {
                //         done();
                //     })
                //     .catch(_ => {});
            },
            exportExcel() {
                this.loading = true;
                this.showExcelBtn = false;
                const url = this.requestPrefixUrl + '/admins/exportYiYeSettlement';
                this.$postDataSelfUrl(url, JSON.stringify(this.dom), {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tablename: "一野结算数据",
                        res: res
                    });
                });
            },
            exportSumExcel() {
                this.loading = true;
                this.showExcelBtn = false;
                const url = this.requestPrefixUrl + '/admins/exportYiYeSettlementSum';
                this.$postDataSelfUrl(url, JSON.stringify(this.dom), {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tablename: "一野员工薪酬结算",
                        res: res
                    });
                });
            },
            blobExport({tablename, res}) {
                this.$message.success('正在导出中,可能需要1~2分钟，请稍等...');
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
                setTimeout(() => {
                    this.loading = false;
                    this.showExcelBtn = true;
                }, 3000);
            },
            /**
             * 合并有序数组
             */
            arrayMerge(arr, arr1) {
                if (!Array.isArray(arr) || !Array.isArray(arr1)) {
                    return [];
                }
                const data = arr.concat(arr1);
                data.sort((a, b) => a - b);
                return data;
            }
        }
    }
</script>

<style scoped>

    .allAmount {
        font-weight: bold;
        /*padding-left: 10px;*/
        color: red;
        font-size: 24px;
    }

    .exportCls {
        padding: 0 15px;
        border: 1px solid #67C23A;
        border-radius: 3px;
        background: #67C23A;
        color: #fff;
        cursor: pointer;
        font-size: 12px;
    }

</style>
