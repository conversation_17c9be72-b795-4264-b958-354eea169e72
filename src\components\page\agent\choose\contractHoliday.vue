<template>
    <div>
        <el-collapse v-model="activeNames" >

            <el-collapse-item title="新增假日规则" name="1">
                <template slot="title">
                    <el-button type="primary" >添加假日规则</el-button>

                </template>
                <el-form ref="form" :model="contractHoliday" :rules="ruleValidate">
                    <el-form-item   prop="weekDay">
                        <div class="label-name">周休息天数:</div>
                        <el-input placeholder="请输入周休息天数" v-model="contractHoliday.weekDay" style="width: 70%">
                        </el-input>
                    </el-form-item >

                    <el-form-item   prop="mouthDay">
                        <div class="label-name">月工资天数:</div>
                        <el-input placeholder="请输入月工资天数" v-model="contractHoliday.mouthDay" style="width: 70%">
                        </el-input>
                    </el-form-item >
                    <el-form-item   prop="holiday">
                        <div class="label-name">节假日休息天数:</div>
                        <el-input placeholder="请输入节假日休息天数" v-model="contractHoliday.holiday" style="width: 70%">
                        </el-input>
                    </el-form-item >
                    <span class="eg">/* 注意：如果按国家规定安排，请输入-1</span>
                    <el-form-item   prop="nationalDay">
                        <div class="label-name">国庆休息天数:</div>
                        <el-input placeholder="请输入国庆休息天数" v-model="contractHoliday.nationalDay" style="width: 70%">
                        </el-input>
                    </el-form-item >
                    <span class="eg">/* 注意：如果按国家规定安排，请输入-1</span>
                    <el-form-item   prop="springDay">
                        <div class="label-name">春节休息天数:</div>
                        <el-input placeholder="请输入春节休息天数" v-model="contractHoliday.springDay" style="width: 70%">
                        </el-input>
                    </el-form-item >



                    <div style="margin-right: 100px;float: right">
                        <Button type="primary" @click="save('form')">确定</Button>
                    </div>
                </el-form>
            </el-collapse-item>
        </el-collapse>
        <el-table
                :data="list"
                style="width: 100%"
                :row-class-name="tableRow">
            <el-table-column
                    prop="weekDay"
                    label="周休息天数"
                   >
            </el-table-column>
            <el-table-column
                    prop="mouthDay"
                    label="月工资"
                   >
            </el-table-column>
            <el-table-column
                    prop="holiday"
                    label="节假日休息天数">
            </el-table-column>
            <el-table-column
                    prop="nationalDay"
                    label="国庆休息天数">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.nationalDay==-1" type="success">依法定</el-tag>
                    <el-tag v-else-if="scope.row.nationalDay!=-1" type="warning">{{scope.row.nationalDay}}</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="springDay"
                    label="春节休息天数">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.springDay==-1" type="success">依法定</el-tag>
                    <el-tag v-else-if="scope.row.springDay!=-1" type="warning">{{scope.row.springDay}}</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    label="操作"
                    fixed="right"
                    min-width="100">
                <template slot-scope="scope">
                    <el-button size="mini" type="success"  v-if="dom.holidayId==scope.row.id">当前绑定</el-button>
                    <el-button size="mini" type="primary"  v-if="dom.holidayId!=scope.row.id" @click="changeHolidayId(scope.row.id)">更换绑定</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>

</template>

<script>
    export default {
        name: "contractHoliday",
        props:['model'],
        data() {
            return {
                contractHoliday:{
                    weekDay:null,
                    mouthDay:null,
                    holiday:null,
                    nationalDay:null,
                    springDay:null,
                },
                activeNames:[],
                list:[],
                dom:this.model,
                ruleValidate: {
                    weekDay: [
                        {required: true, message: '请输入', trigger: 'change'}
                    ],
                    mouthDay: [
                        {required: true, message: '请输入', trigger: 'change'}
                    ],
                    holiday: [
                        {required: true, message: '请输入', trigger: 'change'}
                    ],
                    nationalDay: [
                        {required: true, message: '请输入', trigger: 'blur'}
                    ],
                    springDay: [
                        {required: true, message: '请输入', trigger: 'blur'}
                    ],
                },
            }
        },
        created() {
            this.getList()
            console.log(this.model)
        },
        methods: {
            save(name) {
                this.$refs[name].validate(valid => {
                    if (valid) {
                        this.$postData("addorup_contractHoliday", this.contractHoliday, {}).then(res => {
                            if (res.status == 200) {
                                this.$Message.success('新增成功');
                                this.activeNames = []
                                this.getList()
                            } else {
                                this.$message.error("新增失败，" + res.msg);
                            }
                        })
                    }
                })
            },
            changeHolidayId(id){
                this.dom.holidayId=id
                this.$postData("update_contract", this.dom, {}).then(res => {
                    if (res.status == 200) {
                        this.$Message.success('修改成功');
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })
            },
            getList(){
                this.$postData("list_contractHoliday", {}, {}).then(res => {
                    if (res.status == 200) {
                        this.list=res.data

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            tableRow({row, rowIndex}){
                if (this.dom.holidayId==row.id) {
                    return 'success-row';
                }
                return '';
            }

        }
    }
</script>

<style >
    .eg{
        margin-left: 15%;
        color: #909399;
    }
    .label-name{
        float: left;
        text-align: center;
        width: 15%;
    }
    .el-table .success-row {
        background: #f0f9eb;
    }
</style>
