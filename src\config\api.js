export default {

		noTest: "/account/noTest", //登录
		account: "/account/login", //登录
	userInfo: "/profile/userInfo", //获取用户信息
	menuTree: "/profile/menuTree",
	get_dictText: "/dictionary/getDictTextByType/", //字典
	get_dict: "/dictionary/getDictByType/", //字典
	updateDict: "/dictionary/update", //字典
	coupon_selectPage: "coupon/selectPage",

	// 运营报表
	operate_userStatistics: "/operate/userStatistics",
	operate_ordersStatistics: "/operate/ordersStatistics",
	operate_cancelOrders: "/operate/cancelOrders",
	operate_performanceStatistics: "/operate/performanceStatistics",
	operate_qualityReport: "/operate/qualityReport",

	//充值统计
	payment_getList: "/payment/getList",
	payment_getTotal: "/payment/getTotal",
	payment_export: "/payment/exportList", //导出

	// 售卡业绩
	category_queryList: "/category/queryList", //获取产品分类
	getProduct: "product/getProduct", //所有上架产品
	product_getByProductCategoryId: "/product/getByProductCategoryId", //获取产品分类
	product_performanceStatistics: "/product/performanceStatistics",
	product_excel: "/product/exportList",


	// 渠道运营
	channel_channelStatistics: "/channel/channelStatistics",
	channel_channelUser: "/channel/channelUser",
	channel_channelPerformance: "/channel/channelPerformance",
	channel_channelSetting: "/channel/channelSetting",

	channel_selectList: "/promotion/selectList",
	channel_insert: "/promotion/insert",

	channel_update: "/promotion/update",

	goUrl: "https://yibanapi.xiaoyujia.com/move/getByQRCode",

	//goUrl:"http://localhost:9888/move/getByQRCode",

	// 蜂拥运营
	flocking_flockingStatistics: "/flocking/flockingStatistics",
	flocking_flockingUser: "/flocking/flockingUser",
	flocking_flockingWelfare: "/flocking/flockingWelfare",

	// 管家帖运营
	sticker_stickerStatistics: "/sticker/stickerStatistics",
	sticker_stickerUser: "/sticker/stickerUser",
	sticker_stickerPerformance: "/sticker/stickerPerformance",
	sticker_stickerSetting: "/sticker/stickerSetting",

	// 用户运营
	users_userProfile: "/users/userProfile",
	users_smsPush: "/users/smsPush",
	users_convertsAnalysis: "/users/convertsAnalysis",

	// 活动运营
	activity_activitySetting: "/activity/activitySetting",
	activity_relatedPerformance: "/activity/relatedPerformance",
	activity_dataAnalysis: "/activity/dataAnalysis",

	// 通用
	getProductCategory: "/common/getProductCategory", //获取产品分类
	getProductName: "/common/getProductName", //获取产品名称

	getMonthLash: "/common/getMonthLash", //获取产品名称
	getCityArea: "/common/getCityArea", //获取服务城市和地区
	getCityId: "/common/getCityId", //获取服务城市和地区
	getChannel: "/common/getChannel", // 获取渠道
	getOrderState: "common/getOrderStateEnum", // 获取订单状态
	getOrderLabel: "common/getOrderLabel",
	getHomeInfo: "profile/getHomeInfo",
	getChannelByIsDelete: "/common/getChannelByIsDelete", // 获取渠道

	// 管理员
	admin_operate_ordersStatistics: "/admin/ordersStatistics",
	admin_operate_performanceStatistics: "/admin/performanceStatistics",
	admin_sticker_stickerUser: "/admin/stickerUser",
	admin_sticker_stickerStatistics: "/admin/stickerStatistics",

	// 项目运营
	project_getByList: "/project/getByProject",
	project_getByProjectNo: "/project/getByProjectNo",
	project_saveProject: "/project/saveProject",
	project_selectList: "/project/selectList",
	project_getEmployees: "/project/getEmployees",
	project_update: "/project/projectUpdate",
	project_export: "/project/exportList", //导出
	projectDictionary_save: "/dictionary/save",
	budget_getByProjectId: "/budget/getByProjectId",
	budget_delete: "/budget/delete",
	budget_save: "/budget/save",
	dictionary_getList: "/dictionary/getList",
	budget_export: "/budget/exportList", //导出
	project_getById: "/project/getById",

	store_getByList: "/store/getByList",
	selectLngLatByPrimaryKey: "/store/selectLngLatByPrimaryKey",

	//工单
	dispatch_list: "/dispatch/selectList",

	dispatch_sending: "/dispatch/sendingSms",

	depart_list: "/depart/getList",

	category_getByRoleId: "/category/getByRoleId",

	tsOrderList: "tsOrder/getList",
	tsOrderDelete: "tsOrder/delete",
	tsTrainOrderList: "tsOrder/getByTrainList",
	userTrackList: "userTrack/selectList",
	userTrack_updateOne: "userTrack/updateOne",
	tsOrder_getGdNo: "/tsOrder/getGdNo",
	tsExportList: "tsOrder/exportList",

	trainExportList: "tsOrder/trainExportList",

	tsSave: "tsOrder/save",

	tsUpdate: "tsOrder/update",

	tsAppeal_update: "appeal/update",

	appeal_getByTsId: "appeal/getByTsId",

	tsGetById: "tsOrder/getById",

	getByTrainId: "tsOrder/getByTrainId",

	userTrack_getById: "userTrack/getById",

	userTrack_exportList: "userTrack/exportList",

	orderExtSave: "orderExt/save",

	employeeGetByNo: "employee/getByNo",

	ts_push: "tsOrder/push",

	storeDetails_insert: "storeDetails/insert",
	storeDetails_update: "storeDetails/update",

	fundDetails_insert: "fundDetails/insert",
	fundDetails_update: "fundDetails/update",
	employeeDeductPointsRanking: "fundDetails/employeeDeductPointsRanking",

	getByService: "employee/getByService",
	getByAdministration: "employee/getByAdministration",
	getByTsId: "emOrder/getByTsId",
	emOrder_update: "emOrder/update",

	emOrder_save: "emOrder/save",

	emOrder_delete: "emOrder/delete",

	activityYi_getList: "activityYi/selectList",

	activityYi_exportList: "activityYi/exportList",

	activityYi_getById: "activityYi/getById",

	activityYi_update: "activityYi/update",


	updatePassword: "employee/updatePassword",


	dispatch_selectSettlement: "/dispatch/selectSettlement",
	//一键派单
	dispatch_button_list: "http://inside.xiaoyujia.com/api/Order/OneKyeAuto",
	//一键结算
	dispatch_click_settlement: "http://inside.xiaoyujia.com/api/Order/UpdateSettlement",

	// //一键派单
	// dispatch_button_list:"http://apib.xiaoyujia.com/api/Order/OneKyeAuto",
	// //一键结算
	// dispatch_click_settlement:"http://apib.xiaoyujia.com/api/Order/UpdateSettlement",


	//成员管理
	projectMember_getByList: "/member/getByProjectMember",
	projectMember_saveProject: "/member/saveProjectMember",
	is_ProjectMember: "/member/isProjectMember", //判断员工是否已在该项目
	projectMember_export: "/member/exportList", //导出
	projectMember_update: "/member/update",
	projectMember_getById: "/member/getById",
	selectMemberLabel: "/member/selectMemberLabel",
	memberUpdateById: "/member/memberUpdateById",

	//收入支出
	projectIncome_getByList: "/income/getByProjectIncome",
	projectIncome_saveProject: "/income/saveProjectIncome",
	projectIncome_export: "/income/exportList", //导出
	income_uploadTemplate: "/income/uploadTemplate", //模版下载
	projectIncome_delete: "/income/delete",

	income_detail_getList: "/income/detail/getList",
	income_detail_save: "/income/detail/save",
	income_detail_delete: "/income/detail/delete",

	income_detail_export: "/income/detail/export", //导出


	//分润奖罚
	projectAward_getByList: "/award/getByProjectAward",
	projectAward_saveProject: "/award/saveProjectAward",
	projectAward_export: "/award/exportList", //导出
	projectAward_delete: "/award/delete",
	projectRule_save: "/rule/save",
	projectRule_getList: "/rule/getList",
	projectRule_delete: "/rule/delete",

	// 员工
	employee_getByList: "/employee/getByEmployeeList",
	getEmployeeSelectDto: "/employee/getEmployeeSelectDto",
	getEmployeeSelectDtoByName: "/employee/getEmployeeSelectDtoByName",
	listEmployeeDto: "employee/listEmployeeDto",

	employee_getByRole: "/employee/getByRole",


	//试吃
	product_getByList: "/product/getByProductList", //试吃商品列表
	product_saveProduct: "/product/saveProduct", //试吃商品新增
	product_updateProduct: "/product/updateProduct", //试吃商品编辑
	product_getById: "/product/getById", //试吃详情
	product_getByIsWinList: "/product/getByIsWinList", //获取未中奖商品及上架商品
	//商品详情
	detail_getByList: "/detail/getByList", //试吃商品列表
	detail_save: "/detail/save", //试吃商品新增
	detail_update: "/detail/update", //试吃商品编辑
	detail_getById: "/detail/getById", //试吃商品详情

	//中奖名单
	apply_getByList: "/apply/getByList", //试吃商品列表
	apply_getByApplyList: "/apply/getByApplyList", //试吃商品列表
	applyCoupon_save: "/applyCoupon/save", //添加中选名单
	couponGroup_getByList: "/coupon/getByList", //优惠券批次列表
	applyCoupon_binding: "/applyCoupon/binding", //绑定优惠券
	apply_export: "/apply/exportList", //中奖名单导出

	//推送管理
	push_getList: "/push/getList",
	push_getById: "/push/getById",
	push_update: "/push/update",
	push_delete: "/push/delete",
	push_saveTempleList: "/push/saveTempleList",
	push_export: "/push/exportList", //导出


	//经纪人运营
	getBaomuLevel: "/employee/getBaomuLevel", //保姆等级
	agent_page: "/employee/agentPage", //经纪人管理分页列表
	agent_list: "/employee/agentlist", //经纪人管理列表
	baomu_list: "/employee/Baomulist", //经纪人管理列表
	baomu_list_mini: "/employee/baomulistMini", //经纪人管理列表
	member_list: "/employee/MemberNameList/", //经纪人管理列表
	agent_weixinimg: "/certificate/weixinimg", //更新微信二维码

	agentBaomu_page: "/employee/agentBaomuPage", //保姆管理列表
	agentBaomu_list: "/employee/agentBaomulist", //保姆管理列表
	getBaomuNum: "/employee/getBaomuNum", //保姆管理列表
	pageBaomuRegister: "/employee/pageBaomuRegister", //保姆注册列表
	pageBaomuExcelDownload: "/employee/pageBaomuExcelDownload", //注册保姆列表excel下载
	pageStoreBaomuRegister: "/employee/pageStoreBaomuRegister", //门店保姆注册列表
	pageStoreBaomuExcelDownload: "/employee/pageStoreBaomuExcelDownload", //门店注册保姆列表excel下载

	Baomu_page: "/employee/BaomuPage", //保姆管理列表
	update_employee: "/employee/updateEmployee", //更新经纪人
	add_agent: "/employee/addAgent", //新增经纪人
	add_baomu: "/employee/addBaomu", //新增保姆
	info_baomu: "/employee/getBaomuInfo/", //保姆详情
	add_baomu_info: "/employee/upBaomuInfo", //更新或增加保姆详情
	updateBaomuLevelByBaomuId: "/baomuInfo/updateBaomuLevelByBaomuId", //更新或增加保姆详情
	add_employeeToBaomu: "/employeeToBaomu/addEmployeeToBaomu", //新增经纪人保姆绑定关系
	add_employeeToBaomus: "/employeeToBaomu/addEmployeeToBaomus/", //新增经纪人保姆绑定关系
	up_employeeToBaomu: "/employeeToBaomu/upEmployeeToBaomu", //更新经纪人保姆绑定关系
	up_employeeToBaomus: "/employeeToBaomu/upEmployeeToBaomus", //更新经纪人保姆绑定关系
	up_Baomus: "/employeeToBaomu/upListEmployeeToBaomu/", //新增经纪人保姆绑定关系
	get_baomubyno: "/employee/getbaomubyNo",
	getNannyByName: "/employee/getNannyByName",
	saveBaomuLevel: "/employee/saveBaomuLevel",
	get_memberbyBindTel: "/member/getmemberbyBindTel/",

	get_baomuinfo: "/baomuInfo/getBaomuInfo/", //保姆信息补充
	up_baomuinfo: "/baomuInfo/upBaomuInfo", //保姆信息补充


	list_Certificate: "/certificate/listCertificate/", //更获取图片信息
	add_Certificate: "/certificate/addCertificate", //更获取图片信息
	del_Certificate: "/certificate/delCertificate/", //更获取图片信息
	up_Certificate: "/certificate/upCertificate", //更获取图片信息

	//评价
	baomuRate_list: "/baomuRate/listBaomuRate", //合同管理列表
	save_baomuRate: "/baomuRate/saveBaomuRate", //合同管理列表
	del_baomuRate: "/baomuRate/delBaomuRate/", //合同管理列表
	//保姆经验
	baomuExp_list: "baomuWorkExperience/listBaomuWorkExperience/",
	update_baomuExp: "baomuWorkExperience/upBaomuWorkExperience",
	del_baomuExp: "baomuWorkExperience/delBaomuWorkExperience/",
	add_baomuExp: "baomuWorkExperience/insertBaomuWorkExperience",
	//保姆技能
	baomuSkill_list: "baomuSkill/baomuSkillList/",
	update_baomuSkill: "baomuSkill/upBaomuSkill",
	del_baomuSkill: "baomuSkill/delBaomuSkill/",
	add_baomuExpbaomuSkill: "baomuSkill/addBaomuSkill",

	site_page: "/site/sitePage", //站点管理列表
	cityArea_list: "/site/listCityArea", //城市区域列表
	add_employeeSite: "/employeeSite/addEmployeeSite", //新增经纪人保姆绑定关系
	add_employeeSites: "/employeeSite/addEmployeeSites/", //新增经纪人保姆绑定关系
	up_employeeSite: "/employeeSite/upEmployeeSite", //更新经纪人保姆绑定关系
	up_employeeSites: "/employeeSite/upEmployeeSites", //更新经纪人保姆绑定关系
	site_list: "/site/siteList", //站点管理列表
	site_baomulist: "/site/siteBaomuList", //站点管理列表
	update_site: "/site/updateSite", //更新
	upMain_site: "/employeeSite/updateMainSite", //更新
	add_site: "/site/addSite", //新增经
	del_site: "/site/deleteSite/", //删除
	up_Sites: "/employeeSite/upListEmployeeSite/", //新增经纪人保姆绑定关系
	main_site: "/employeeSite/upMainSite", //

	updateRemark: "/order/updateRemark", //修改订单备注
	getAgentABC: "/order/getAgentABC", //修改订单备注
	getAgentCC: "/order/getAgentCC", //修改订单备注
	getAgentCCExcel: "/order/getAgentCCExcel", //修改订单备注
	getAgentABCSum: "/order/getAgentABCSum", //修改订单备注
	getAgentCCSum: "/order/getAgentCCSum", //修改订单备注
	updateServiceRemark: "/order/updateServiceRemark", //修改订单备注
	updateServiceDate: "/order/updateServiceDate", //修改订单备注
	contractToOrder: "/order/contractToOrder", //合同信息转入订单中
	getOrderWaiterList: "/order/getOrderWaiterList", //合同信息转入订单中
	getOrderWagesList: "/order/getOrderWagesList", //合同信息转入订单中
	changeRealTotalAmount: "/order/changeRealTotalAmount", //订单改价
	splitOrder: "/order/splitOrder", //订单拆单
	orderOperationLogList: "/order/orderOperationLogList", //订单日志
	order_page: "/order/agentOrderPage", //订单管理列表
	agentOrderPageOne: "/order/agentOrderPageOne", //订单管理列表
	order_list: "/order/agentOrderList", //订单管理列表
	get_order: "/order/getOrder/", //订单
	update_order: "/order/updateOrder", //更新经纪人
	add_order: "/order/addSite", //更新经纪人
	getListBylngAndLat: "/order/getListBylngAndLat", //更新经纪人

	orderNeeds_page: "/orderNeeds/orderNeedsPage", //订单管理列表
	orderNeeds_count: "/orderNeeds/getCount/", //订单管理列表
	page_orderNeedsLog: "/orderNeeds/orderNeedsLogPage",
	orderNeeds_save: "/orderNeeds/save", //订单管理列表
	orderNeedsGet: "/orderNeeds/get/", //订单管理列表
	orderNeedsAlogList: "/orderNeeds/orderNeedsAlogList", //算法列表

	selectOrderInterRefundIPage: "orderInterRefund/selectOrderInterRefundIPage",
	getOrderResidueMoney: "orderInterRefund/getOrderResidueMoney",
	addOrderInterRefund: "orderInterRefund/addOrderInterRefund",
	selectOrderInterRefundByBillNo: "orderInterRefund/selectOrderInterRefundByBillNo",
	orderInterRefundExcel: "orderInterRefund/orderInterRefundExcel",

	selectByRemarks_baomuinfo: "/baomuInfo/selectByRemarks", //新增保姆信息
	getNumByRealName: "/orderNeeds/getNumByRealName", //

	communicate_page: "/communicate/communicatePage", //咨询管理列表
	update_communicate: "/communicate/updatecommunicate", //更新咨询
	add_communicate: "/communicate/addcommunicate", //更新咨询

	getContractByOrderId: "/contract/getContractByOrderId", //获取合同
	getContractById: "/contract/getContractById", //获取合同
	contract_page: "/contract/contractPage", //合同管理列表
	contractBxPage: "/contract/contractBxPage", //合同管理列表
	get_contract_no: "/contract/getContractNo", //合同管理列表
	update_contract: "/contract/updateContract", //更新合同
	up_contract: "/contract/upContract", //更新合同
	removeOrderId: "/contract/removeOrderId", //更新合同
	add_contract: "/contract/addContract", //新增合同
	download_contract: "/contract/download/", //下载合同
	download_yuesao_contract: "/contract/download/yuesao/", //下载月嫂合同
	reMemberSign: "/contract/reMemberSign/", //
	reEmployeeSign: "/contract/reEmployeeSign/", //

	add_contractSigin: "/contractSigin/addContractSigin", //新增合同签名
	get_contractSigin: "/contractSigin/getContractSigin", //客户合同签名
	up_contractSigin: "/contractSigin/upContractSigin", //客户合同签名
	del_contractSigin: "/contractSigin/delContractSigin/", //客户合同签名

	list_contractHoliday: "/contractHoliday/ListContractHoliday", //假期规则
	addorup_contractHoliday: "/contractHoliday/addOrUpContractHoliday", //假期规则
	del_contractHoliday: "/contractHoliday/delContractHoliday/", //假期规则

	contractEnclosureList: "contractEnclosure/contractEnclosureList",
	contractEnclosureSave: "contractEnclosure/contractEnclosureSave",
	contractEnclosureDel: "contractEnclosure/contractEnclosureDel",


	settlementRule_page: "/settlementRule/settlementRulePage", //结算管理列表
	update_settlementRule: "/settlementRule/updatesettlementRule", //更新结算
	add_settlementRule: "/settlementRule/addsettlementRule", //新增结算
	do_settlementRulePage: "/settlementRule/dosettlementRulePage", //计算结算
	settlementPage: "/settlement/settlementPage", //计算结算
	settlementData: "/settlement/settlementData", //计算结算


	get_employeeSort: "/employeeSort/get/",
	save_employeeSort: "/employeeSort/save",

	list_contractSupply: "contractSupply/getList/",
	save_contractSupply: "contractSupply/save",

	//保姆日志
	getBaomuWorkLog: "baomuWorkLog/getBaomuWorkLog",
	saveBaomuWorkLog: "baomuWorkLog/save",
	delBaomuWorkLog: "baomuWorkLog/del",

	//客户日志
	getMemberLog: "memberLog/getMemberLog",
	saveMemberLog: "memberLog/save",
	delMemberLog: "memberLog/del",
	getServiceByMemberId: "memberLog/getServiceByMemberId",

	//jizhengyun
	jizhengyunList: "jizhengyun/list",
	jzyAll: "jizhengyun/jzyAll",
	getJzyNum: "jizhengyun/getJzyNum",
	getJzyPage: "jizhengyun/getJzyPage",
	getJzySum: "jizhengyun/getJzySum",
	getJzyExportList: "jizhengyun/getJzyExportList",

	//客户信息
	selectAgentMembers: "member/selectAgentMembers",
	memberPage: "member/memberPage",
	memberById: "member/memberById",
	insertMember: "member/insertMember",
	pageMemberIntegral: "member/pageMemberIntegral",
	pageMemberIntegralDownload: "member/pageMemberIntegralDownload",

	//图片上传地址
	imgUrl: "https://biapi.xiaoyujia.com/files/uploadFiles", //正式
	testUrl: "http://localhost:8069/files/uploadFiles", //测试


	//违纪工单
	violation_getList: "violation/getList",
	getByOrderNo: "order/getByOrderNo",
	violation_insert: "violation/insert",
	violation_update: "violation/update",
	violation_getById: "violation/getById",
	violation_getByAppeal: "violation/getByAppeal",
	violation_updateAppeal: "violation/updateAppeal",
	violation_exportList: "violation/exportList",

	//表彰工单
	commend_getList: "commend/getList",
	commend_insert: "commend/insert",
	commend_update: "commend/update",
	commend_getById: "commend/getById",
	//需求工单
	demandWorkOrderPage: "demandWorkOrder/demandWorkOrderPage",
	updateDemandWorkOrder: "demandWorkOrder/updateDemandWorkOrder",
	getOrdrtAmountByBillNo: "demandWorkOrder/getOrdrtAmountByBillNo",
	channelClue: "demandWorkOrder/channelClue",
	projectClue: "demandWorkOrder/projectClue",

	//表单
	formList: "form/formList",
	saveForm: "form/saveForm",
	formResponse: "form/formResponse",
	getFormByTaskTypeId: "form/getFormByTaskTypeId/",
	getAddFormByTaskTypeId: "form/getAddFormByTaskTypeId/",
	getResponseByTaskId: "form/getResponseByTaskId/",

	//任务信息表
	saveTaskType: "taskType/saveTaskType",
	taskTypeList: "taskType/taskTypeList",
	getNumByStatus: "taskType/getNumByStatus",
	getNumByResponse: "taskType/getNumByResponse",


	//任务
	saveTask: "task/saveTask",
	saveTaskShareDto: "task/saveTaskShareDto",
	taskPage: "task/taskPage",
	getTaskDataDto: "task/getTaskDataDto",
	taskList: "task/taskList",
	taskCheckPage: "taskCheck/taskCheckPage",

	//海报中心
	posterActivityList: "poster/posterActivityList",
	posterList: "poster/posterList",
	posterParamList: "poster/posterParamList",
	insertPoster: "poster/insertPoster",
	savePoster: "poster/savePoster",
	delPoster: "poster/delPoster",
	savePosterLog: "poster/savePosterLog",
	savePosterActivity: "poster/savePosterActivity",
	getPoter: "poster/getPoter",
	posterLogPage: "poster/posterLogPage",
	posterLogNum: "poster/posterLogNum",

	//招聘
	initRecruit: "recruit/initRecruit",
	saveRecruit: "recruit/saveRecruit",
	recruitPage: "recruit/recruitPage",
	recruitmentRanking: "recruit/recruitmentRanking",
	recruitDownload: "recruit/download",
	recruitDownload2: "recruit/download2",
	recruitmentRankingAndChannel: "recruit/recruitmentRankingAndChannel",
	insertTrialStaffByRecruitDto: "trialStaff/insertTrialStaffByRecruitDto",
	recruitmentRankingType: "recruit/recruitmentRankingType",
	recruitDownload3: "recruit/download3",

	//徒弟申请管理
	getApprenticeData: "apprentice/getApprenticeData",
	updateApprentice: "apprentice/updateApprentice",

	//合伙人
	franchiseRegisterPage: "franchiseRegister/franchiseRegisterPage",
	getFranchiseMsg: "franchiseAudit/getFranchiseMsg",
	updateFranchiseState: "franchiseAudit/updateFranchiseState",
	getAllUnionRunData: "franchiseAudit/getAllUnionRunData",
	updateFranchiseRecruit: "franchiseRegister/updateFranchiseRecruit",
	downloadFranchiseRegister: "franchiseRegister/download",
	getFranchiseChannel: "franchiseChannel/getFranchiseChannel",
	getFranchiseRegisterHandler: "franchiseRegisterHandler/getFranchiseRegisterHandler",
	addFranchiseRegisterHandler: "franchiseRegisterHandler/addFranchiseRegisterHandler",
	//客户标签
	memberTagDtoLists: "memberTag/memberTagDtoLists/",
	saveMemberTagBind: "memberTag/saveMemberTagBind",

	//结算
	getWithdrawalData: "storeWithdrawal/getWithdrawalData",
	updateWithdrawalData: "storeWithdrawal/updateWithdrawalData",
	getWithdrawalOrder: "storeWithdrawal/getWithdrawalOrder",
	exportWithdrawalOrder: "storeWithdrawal/exportWithdrawalOrder",
	getRecruitReturnData: "storeWithdrawal/getRecruitReturnData",
	getDeclareWithdrawalData: "storeWithdrawal/getDeclareWithdrawalData",
	getRecruitReturnDetails: "storeWithdrawal/getRecruitReturnDetails",
	getDeclareWithdrawalDetails: "storeWithdrawal/getDeclareWithdrawalDetails",
	updateDeclareWithdrawalData: "storeWithdrawal/updateDeclareWithdrawalData",
	updateRecruitReturnData: "storeWithdrawal/updateRecruitReturnData",
	getStoreBalance: "storeWithdrawal/getStoreBalance",
	getStoreRegionData: "smartPartner/getStoreRegionData",
	getStoreRegionDetails: "smartPartner/getStoreRegionDetails",
	getSessionChatLog: "smartPartner/getSessionChatLog",
	getRegionData: "smartPartner/getRegionData",
	saveStoreRegion: "smartPartner/saveStoreRegion",
	updateStoreRegion: "smartPartner/updateStoreRegion",
	getAllStoreRegion: "smartPartner/getAllStoreRegion",
	getStoreByRegionId: "smartPartner/getStoreByRegionId",
	synchronousStoreRegionHead: "smartPartner/synchronousStoreRegionHead",
	storeRegionTransfer: "smartPartner/storeRegionTransfer",
	addRegionStore: "smartPartner/addRegionStore",

	getUnionCashJournalList: "unionCashJournal/getUnionCashJournalList",
	getUnionSettlementDetails: "unionCashJournal/getUnionSettlementDetails",
	updateCashState: "unionCashJournal/updateCashState",
	exportUnionSettlementDetails: "unionCashJournal/exportUnionSettlementDetails",
	getStoreSettlementData: "aiRun/getStoreSettlementData",
	exportAllStoreSettlementData: "aiRun/exportAllStoreSettlementData",

	//问答库
	getUnionQaType: "unionQa/getUnionQaType",
	saveUnionQAType: "unionQa/saveUnionQAType",
	updateUnionQAType: "unionQa/updateUnionQAType",
	deleteUnionQAType: "unionQa/deleteUnionQAType",
	getUnionQaList: "unionQa/getUnionQaList",
	getUnionQaAllType: "unionQa/getUnionQaAllType",
	saveUnionQA: "unionQa/saveUnionQA",
	updateUnionQA: "unionQa/updateUnionQA",
	deleteUnionQA: "unionQa/deleteUnionQA",
	getUnionQaReply: "unionQa/getUnionQaReply",
	saveUnionQAReply: "unionQa/saveUnionQAReply",
	deleteUnionQAReply: "unionQa/deleteUnionQAReply",
	updateUnionQAReply: "unionQa/updateUnionQAReply",


	//工作台
	getRoleList: "workbench/getRoleList",
	saveWorkbench: "workbench/saveWorkbench",
	getWorkbench: "workbench/getWorkbench",
	updateWorkbench: "workbench/updateWorkbench",

	//成长任务
	saveUnionPlan: "czTask/saveUnionPlan",
	getUnionPlan: "czTask/getUnionPlan",
	updateUnionPlan: "czTask/updateUnionPlan",
	getAllUnionPlan: "czTask/getAllUnionPlan",
	getAllUnionFlow: "czTask/getAllUnionFlow",
	saveUnionTask: "czTask/saveUnionTask",
	getUnionTask: "czTask/getUnionTask",
	getUnionFlow: "czTask/getUnionFlow",
	deleteUnionFlow: "czTask/deleteUnionFlow",
	updateUnionFlow: "czTask/updateUnionFlow",
	saveUnionFlow: "czTask/saveUnionFlow",
	updateUnionTask: "czTask/updateUnionTask",
	saveUnionForm: "czTask/saveUnionForm",
	getUnionForm: "czTask/getUnionForm",
	updateUnionForm: "czTask/updateUnionForm",
	deleteUnionPlan: "czTask/deleteUnionPlan",
	deleteUnionTask: "czTask/deleteUnionTask",
	deleteUnionForm: "czTask/deleteUnionForm",
	saveFormTask: "czTask/saveFormTask",
	getFormTask: "czTask/getFormTask",
	updateFormTask: "czTask/updateFormTask",
	deleteFormTask: "czTask/deleteFormTask",
	getAllUnionForm: "czTask/getAllUnionForm",
	saveTaskForm: "czTask/saveTaskForm",
	getTaskForm: "czTask/getTaskForm",
	deleteTaskForm: "czTask/deleteTaskForm",
	updateTaskForm: "czTask/updateTaskForm",
	getFlowTask: "czTask/getFlowTask",
	getAllUnionTask: "czTask/getAllUnionTask",
	saveFlowTask: "czTask/saveFlowTask",
	updateFlowTask: "czTask/updateFlowTask",
	deleteFlowTask: "czTask/deleteFlowTask",
	saveImgFlow: "czTask/saveImgFlow",
	getImgFlow: "czTask/getImgFlow",
	updateImgFlow: "czTask/updateImgFlow",
	deleteImgFlow: "czTask/deleteImgFlow",
	selectAllStore: "swarmRobots/selectAllStore",


	//工资发放

	salaryDetails_salaryDetails: "salaryDetails/selectPage",
	salaryDetails_export: "salaryDetails/exportSalary",
	salarySetUp_goPush: "salarySetUp/goPush",
	salarySetUp_getById: "salarySetUp/getById",
	salarySetUp_update: "salarySetUp/update",
	salarySetUp_getDepartmentList: "salarySetUp/getDepartmentList",
	salarySetUp_push: "salarySetUp/push",

	//领用工单
	collectRecordList_selectPage: "collectRecordList/selectPage",
	collectRecordList_selectCategoryAll: "collectRecordList/selectCategoryAll",
	collectRecordList_export: "collectRecordList/export",

	//结算
	agentSettlementByBillNo: "/agentBonus/agentSettlementByBillNo/", //保姆信息补充
	agentBonusList: "/agentBonus/agentBonusList/", //保姆信息补充
	getEmployeeBaomuBindDto: "/agentBonus/getEmployeeBaomuBindDto/", //保姆信息补充

	queryProductCategoryPages: "https://yibanapi.xiaoyujia.com/leopard/queryProductCategoryPages",
	queryProductPages: "https://yibanapi.xiaoyujia.com/leopard/queryProductPages",
	queryProductPages_categoryId: "https://yibanapi.xiaoyujia.com/leopard/queryProductPages/categoryId",

	//财务充值流水
	memberBlackList_selectPage: "memberBlackList/selectPage",
	memberBlackList_recharge: "memberBlackList/recharge",
	baomuEmployeeList: "baomuEmployee/baomuEmployeeList",


	saveAgentStore: "AgentStore/saveAgentStore",
	getAgentStoreList: "AgentStore/getAgentStoreList",
	listAgentStoreExcelDownload: "AgentStore/listAgentStoreExcelDownload",
	updateAgentEmployeeShareList: "agentEmployeeShare/updateAgentEmployeeShareList",
	getEmployeeList: "agentEmployeeShare/getEmployeeList",
	updateAgentEmployeeShare: "agentEmployeeShare/updateAgentEmployeeShare",
	listAgentStoreLog: "AgentStore/listAgentStoreLog",
	agentStoreShareInint: "agentStoreShare/init",
	agentStoreShareList: "agentStoreShare/agentStoreShareList",
	getOrderNeedsAlgorithmSetting: "orderNeeds/getOrderNeedsAlgorithmSetting",
	updateOrderNeedsAlgorithmSetting: "orderNeeds/updateOrderNeedsAlgorithmSetting",

	getAgentStoreSetting: "agentStoreSetting/getAgentStoreSetting",
	saveAgentStoreSetting: "agentStoreSetting/saveAgentStoreSetting",

	listAgentStoreRule: "agentStoreRule/listAgentStoreRule",
	insertAgentStoreRule: "agentStoreRule/insertAgentStoreRule",
	updateAgentStoreRule: "agentStoreRule/updateAgentStoreRule",
	deleteAgentStoreRule: "agentStoreRule/deleteAgentStoreRule",

	listAgentStoreRuleLog: "agentStoreRuleLog/listAgentStoreRuleLog",
	insertAgentStoreRuleLog: "agentStoreRuleLog/insertAgentStoreRuleLog",
	updateAgentStoreRuleLog: "agentStoreRuleLog/updateAgentStoreRuleLog",
	deleteAgentStoreRuleLog: "agentStoreRuleLog/deleteAgentStoreRuleLog",

	listServiceScoreRule: "serviceScore/listServiceScoreRule",
	insertServiceScoreRule: "serviceScore/insertServiceScoreRule",
	updateServiceScoreRule: "serviceScore/updateServiceScoreRule",
	deleteServiceScoreRule: "serviceScore/deleteServiceScoreRule",
	listServiceScoreRuleGroup: "serviceScoreRuleGroup/listServiceScoreRuleGroup",
	insertServiceScoreRuleGroup: "serviceScoreRuleGroup/insertServiceScoreRuleGroup",
	updateServiceScoreRuleGroup: "serviceScoreRuleGroup/updateServiceScoreRuleGroup",
	pageServiceScoreRuleApproval: "serviceScore/pageServiceScoreRuleApproval",
	updateServiceScoreRuleApproval: "serviceScore/updateServiceScoreRuleApproval",


	getLocByBillNo: "mg/getLocByBillNo/",
	backOrder: "/order/backOrder", //合同信息转入订单中

	/*
	 * 驻场项目
	 * */
	siteProject_selectPage: "siteProject/selectPage",
	siteProject_insert: "siteProject/insert/billNo",
	siteProject_getByNo: "siteProject/getByNo",
	siteProject_getByEmployee: "siteProject/getByEmployee",
	siteProject_delete: "siteProject/delete",
	siteProject_getById: "siteProject/getById/",
	siteProject_update: "siteProject/update",
	siteProject_selectList: "siteProject/selectList/",
	siteProject_download: "siteProject/download",
	siteProject_selectSiteEmployee: "siteProject/selectSiteEmployee",
	siteProject_updateBySiteEmployeeId: "siteProject/updateBySiteEmployeeId",
	synchronizations: "siteProject/synchronizations",
	fastExpiringContract: "siteProject/fastExpiringContract",
	fastExpiringContractExport: "siteProject/fastExpiringContractExport",

	/*
	 * 合同订单
	 * */
	siteOrder_getByProjectId: "siteOrder/getByProjectId/",
	siteOrder_getByBind: "siteOrder/getByBind/",
	siteOrder_getByBindAll: "siteOrder/getByBindAll",
	siteOrder_update: "siteOrder/update",
	siteOrder_delete: "siteOrder/delete/",
	siteOrder_selectOrderList: "siteOrder/selectOrderList",
	siteOrder_selectOrderArrears: "siteOrder/selectOrderArrears",
	siteOrder_totalOrderDebt: "siteOrder/totalOrderDebt",
	siteOrder_openInvoice: "siteOrder/openInvoice",
	siteOrder_pushSms: "siteOrder/pushSms",
	/*
	 * 合同人员
	 * */
	siteEmployee_getByProjectId: "siteEmployee/getByProjectId/",
	siteEmployee_update: "siteEmployee/update",
	siteEmployee_delete: "siteEmployee/delete/",
	orderWaiterDelete: "/siteEmployee/orderWaiterDelete",
	siteEmployee_getByBind: "siteEmployee/getByBind/",
	siteEmployee_getEmployee: "siteEmployee/getEmployee/",
	siteEmployee_insertEmployee: "siteEmployee/insertEmployee",
	employee_getByGroup: "employee/getByGroup",
	siteEmployee_quit: "siteEmployee/quit",
	siteEmployee_getByBindAll: "siteEmployee/getByBindAll",
	siteEmployee_insertFormal: "siteEmployee/insertFormal",

	siteWagesMatter_batchMatter: "siteWagesMatter/batchMatter",
	siteWagesMatter_getByMonth: "siteWagesMatter/getByMonth",
	siteWagesMatter_delete: "siteWagesMatter/delete/",

	siteWagesDetails_selectPage: "siteWagesDetails/selectPage",
	selectSiteWagesDetail: "siteWagesDetails/selectSiteWagesDetail",
	siteWagesDetails_calculationWagesMonth: "siteWagesDetails/calculationWagesMonth/",
	siteWagesDetails_calculationWagesMonthBatch: "siteWagesDetails/calculationWagesMonthBatch",
	calculationWagesMonthOneKey: "siteWagesDetails/calculationWagesMonthOneKey",
	siteWagesDetails_update: "siteWagesDetails/update",
	siteWagesDetails_download: "siteWagesDetails/download",
	siteWagesDetails_insertWagesMonth: "siteWagesDetails/insertWagesMonthTwos",
	siteWagesDetails_insertWagesMonthByProjectId: "siteWagesDetails/insertWagesMonthTwo",
	siteInvoice_getByProjectId: "siteInvoice/getByProjectId/",
	siteInvoice_getBindInvoice: "siteInvoice/getBindInvoice",
	siteInvoice_update: "siteInvoice/update",
	siteInvoice_insert: "siteInvoice/insert",
	/*
	 * 操作日志
	 * */
	siteLog_getByProjectId: "siteLog/getByProjectId/",
	/*
	 * 项目评价
	 * */
	siteOrder_download: "siteOrder/download",
	siteOrder_download2: "siteOrder/download2",
	exportOrderArrears: "siteOrder/exportOrderArrears",


	seletCouponDto: "couponDto/seletCouponDto",
	promotionChannelStatistics: "order/promotionChannelStatistics",
	exportPromotionChannelStatistics: "order/exportPromotionChannelStatistics",

	couponChannelStatistics: "order/couponChannelStatistics",
	exportCouponChannelStatistics: "order/exportCouponChannelStatistics",

	dataCouponDto: "couponDto/dataCouponDto",
	exportCouponDto: "couponDto/exportCouponDto",
	getByBillNo: "userTrack/getByBillNo",

	saveCmsMaterial: "cmsMaterial/saveCmsMaterial",
	cmsMaterialPage: "cmsMaterial/cmsMaterialPage",
	groupNameList: "cmsMaterial/groupNameList",

	saveCmsInfo: "cmsInfo/saveCmsInfo",
	getCmsInfo: "cmsInfo/getCmsInfo",
	cmsInfoPage: "cmsInfo/cmsInfoPage",
	getTypeNameList: "cmsInfo/getTypeNameList",

	saveCmsMenu: "cmsMenu/saveCmsMenu",
	menuTreeData: "biMenu/getAll",
	updateMenu: "biMenu/updateMenu",
	deleteMenu: "biMenu/deleteMenu",
	addMenu: "biMenu/addMenu",
	getAllRoleList: "biMenu/getAllRole",
	getRolePermission: "biMenu/getRolePermission",
	updateRoleMenu: "biMenu/updateRoleMenu",


	saveTeam: "team/saveTeam",
	saveTeamSaveDto: "team/saveTeamSaveDto",
	getTeamSaveDto: "team/getTeamSaveDto",
	teamList: "team/teamList",
	teamPage: "team/teamPage",

	teamParamList: "teamParam/teamParamList",
	getSumParamsToteamId: "teamDate/getSumParamsToteamId",
	getSumParamsToEmployeeId: "teamDate/getSumParamsToEmployeeId",
	getTeamDataByDay: "teamDate/getTeamDataByDay",
	getTeamEmployeeById: "teamDate/getTeamEmployeeById",


	sqlText: "parameterInfo/sqlText",
	parameterInfoList: "parameterInfo/parameterInfoList",
	saveParameterInfo: "parameterInfo/saveParameterInfo",
	getParameterInfo: "parameterInfo/getParameterInfo",
	setAllParameterData: "parameterInfo/setAllParameterData",
	parameterInfoDtoShow: "parameterInfo/parameterInfoDtoShow",
	getParamsDataChar: "parameterInfo/getParamsDataChar",
	setParameterData: "parameterInfo/setParameterData",

	initAgent31: "task/initAgent31",

	//服务签收单
	selectServiceAcceptanceFormByOrderNo: "/serviceAcceptanceForm/selectServiceAcceptanceFormByOrderNo",
	selectServiceAcceptanceFormByOrderNoHTTPS: "https://biapi.xiaoyujia.com/serviceAcceptanceForm/selectServiceAcceptanceFormByOrderNo",
	selectServiceReceipt: "/serviceAcceptanceForm/selectServiceReceipt",
	selectServiceEmployee: "/serviceAcceptanceForm/selectServiceEmployee",

	selectServiceCancelOrder: "/serviceAcceptanceForm/cancelOrder",
	forceCustomerRefund: "/serviceAcceptanceForm/forceCustomerRefund",
	wholeRefund: "/serviceAcceptanceForm/wholeRefund",
	staffRefund: "/serviceAcceptanceForm/staffRefund",
	selectCount: "/serviceAcceptanceForm/selectCount",
	//服务签收单--日志
	getserviceAcceptancFormLogDESC: "/serviceAcceptancFormLog/getserviceAcceptancFormLogDESC",
	//建议
	selectByIds: "/customerSuggestions/selectByIds",
	//检测异常
	selectByAbnormal: "serviceAcceptanceForm/selectByAbnormal",
	setOrderNoToken: "/serviceAcceptanceForm/setOrderNoToken",
	getServiceAcceptanceForm: "serviceAcceptanceForm/getServiceAcceptanceForm",


	saveMarketing: "marketing/saveMarketing",
	getMarketingList: "marketing/getMarketingList",
	saveMarketingDto: "marketing/saveMarketingDto",
	getMarketingDtoById: "marketing/getMarketingDtoById",
	getMarketingByEndTime: "marketing/getMarketingByEndTime",


	//远程在线企业微信部门
	getParty: "qiyeWeixin/getParty",
	getPagingParty: "qiyeWeixin/getPagingParty",
	batchDeleteUser: "qiyeWeixin/batchDeleteUser",
	delDepartMent: "qiyeWeixin/delDepartMent",
	selectQiyeChannelGroupBy: "qiyeChannelGroupBy/selectQiyeChannelGroupBy",
	//远程在线企业微信部门下的人员
	getPartyUser: "qiyeWeixin/getPartyUser",
	getPartyUser2: "qiyeWeixinUser/getPartyUser",
	selectQiyeWeixinUserIPage: "qiyeWeixinUser/selectQiyeWeixinUserIPage",
	getQiyeStaffLabel: "qiyeStaffLabel/getQiyeStaffLabel",
	//企微标签组
	getQiyeLabelGroup: "qiyeLabelGroup/getQiyeLabelGroup",
	//企微标签
	getQiyeLabel: "qiyeLabel/getQiyeLabel",
	//添加渠道
	insertQiyeState: "qiyeState/insertQiyeState",
	updateQiyeState: "qiyeState/updateQiyeState",
	delQiyeState: "qiyeState/delQiyeState",
	pic_media_id: "qiyeState/pic_media_id",
	selectQiyeChannelIPage: "qiyeChannel/selectQiyeChannelIPage",
	getQiyeCustomerAdds: "qiyeCustomerAdd/getQiyeCustomerAdds",
	//企微活码分组
	selectQiyeChannelGroupByIPage: "qiyeChannelGroupBy/selectQiyeChannelGroupByIPage",
	addGroup: "qiyeChannelGroupBy/addGroup",
	updateQiyeChannelGroupBy: "qiyeChannelGroupBy/updateQiyeChannelGroupBy",
	mobileQrCod: "qiyeChannelGroupBy/mobileQrCode",
	//上传图片
	ploadPicturesu: "qiyeCustomer/ploadPicturesu",
	getQiyeChannel: "qiyeChannel/getQiyeChannel",
	getQiyeState: "qiyeChannel/getQiyeState",
	addQiyeData: "qiyeData/addQiyeData",
	selectByCondition: "qiyeData/selectByCondition",
	toppingQiyeData: "qiyeData/toppingQiyeData",
	delectQiyeData: "qiyeData/delectQiyeData",
	addQiyeDataLabe: "qiyeDataLabe/addQiyeDataLabe",
	selectByType: "qiyeDataLabe/selectByType",
	delectQiyeDataLabe: "qiyeDataLabe/delectQiyeDataLabe",
	//图文
	addQiyeGraphics: "qiyeGraphics/addQiyeGraphics",
	selectQiyeGraphics: "qiyeGraphics/selectQiyeGraphics",
	delectQiyeGraphics: "qiyeGraphics/delectQiyeGraphics",
	//企微小程序资源
	addQiyeApplet: "qiyeApplet/addQiyeApplet",
	qiyeAppletByCondition: "qiyeApplet/qiyeAppletByCondition",
	delectQiyeApplet: "qiyeApplet/delectQiyeApplet",
	//日志
	totalCustomer: "qiyeJournal/totalCustomer",
	timeCustomer: "qiyeJournal/timeCustomer",
	staffAddCustomer: "qiyeJournal/staffAddCustomer",
	downloadQiyeJournal: "qiyeJournal/download",
	getQywxCustomerInfoGroupByTimeAndType: "qiyeJournal/getQywxCustomerInfoGroupByTimeAndType",
	// todayCustomer:"qiyeJournal/todayCustomer",
	hourStatistics: "qiyeJournal/hourStatistics",
	drainHourStatistics: "qiyeJournal/drainHourStatistics",
	dayStatistics: "qiyeJournal/dayStatistics",
	drainDayStatistics: "qiyeJournal/drainDayStatistics",
	//申请更改资料
	addQiyeApply: "qiyeApply/addQiyeApply",
	selectQiyeApplyIPage: "qiyeApply/selectQiyeApplyIPage",
	//渠道码
	addChannelCode: "channelCode/addChannelCode",
	getChannelCode: "channelCode/getChannelCode",
	getChannelCodeById: "channelCode/getChannelCodeById",
	customerByTime: "channelCodeLog/customerByTime",
	customerByChannelCodeId: "channelCodeLog/customerByChannelCodeId",


	//订单结算数据
	initData: "orderData/initData",
	getOrderData: "orderData/getOrderData",
	getProductList: "orderData/getProductList",

	addSwarmRobots: "swarmRobots/addSwarmRobots",
	getSwarmRobots: "swarmRobots/getSwarmRobots",
	selectSwarmRobots: "swarmRobots/selectSwarmRobots",
	addTimedTask: "timedTask/addTimedTask",
	messagePush: "timedTask/messagePush",
	messagePushLog: "timedTask/messagePushLog",
	timedTaskLog: "timedTask/timedTaskLog",
	assignTasksLog: "timedTask/assignTasksLog",
	updateTimedTaskState: "timedTask/updateState",
	saveOrEdit: "timedTask/saveOrEdit",
	specifyPause: "timedTask/specifyPause",
	specifyRun: "timedTask/specifyRun",
	updateRobotMsg: "timedTask/updateRobotMsg",
	deleteSpecify: "timedTask/deleteSpecify",
	addTimedTaskLog: "timedTaskLog/addTimedTaskLog",
	selectTimedTaskLog: "timedTaskLog/selectTimedTaskLog",
	getTimedTaskParameter: "timedTaskParameter/getTimedTaskParameter",
	//
	memberRefundPage: "memberRefund/memberRefundPage",
	setOperatorRemark: "memberRefund/setOperatorRemark",
	vMemberRefund: "memberRefund/vMemberRefund",
	pastMemberRefund: "memberRefund/pastMemberRefund",
	wxMemberRefundToTransfers: "memberRefund/wxMemberRefundToTransfers",
	getMemberRefundLog: "memberRefund/getMemberRefundLog",
	memberRefund_export: "memberRefund/exportData",

	getEmployeeStoreDto: "employeeStore/getEmployeeStoreDto",
	//飞鸟手动派单
	getFlyBridOrder: "task/getFlyBridTask",
	//差评任务任务列表
	getBadMissionList: "task/getBadMissionList",
	//体验管理数据
	getExperienceInfo: "coupon/selectExperienceManagementPage",
	//更新发放人
	updateExperienceInfo: "coupon/updateExperienceManagement",
	//导出报表
	downloadExperienceInfo: "coupon/downloadExperienceManagement",

	// 新增或更新保姆评分
	addOrEditNannyScore: "employeeGrade/addOrEditNannyScore",
	// 获取保姆评分
	getNannyScore: "employeeGrade/getNannyScore",

	//蜂窝营销活动获取当前活动
	getActiveByYXList: "activity/selectList",
	//蜂窝营销活动创建
	createActivityByYX: "activity/createActivity",

	//蜂窝营销活动对应id数据详情
	initFriendlyData: "activity/initFriendlyData",

	//优秀员工查询
	selectExcellentEmployee: "excellentEmployee/selectPage",
	//优秀员工更新
	updateExcellentEmployee: "excellentEmployee/updateCondition",
	//优秀员工新增
	createExcellentEmployee: "excellentEmployee/insertCondition",

	//员工技能认证记录查询
	getBaomuAuthPage: "baomuAuth/getBaomuAuthPage",
	//更新员工技能认证记录
	updateBaomuAuth: "baomuAuth/updateBaomuAuth",
	//获取保姆信息详情
	getBaomuDetail: "baomuAuth/getBaomuDetail/",
	//蜂窝营销数据统计
	yingxiaoCenus: "activity/yingxiaoCenus",
	//员工参与拉取统计
	yingxiaoEmoloyeeCenus: "activity/employeeInvit",
	//活动推送
	activityPush: "activity/activityPush",
	//评分统计查询
	selectEmployeeScore: "tsOrder/selectEmployeeScore",
	//excel导出
	downloadEmployeeScore: "tsOrder/employeeScoreByDownload",
	// 更新小区名
	updateMemberInfoAndOrderAddrInfo: "/member/updateMemberInfoAndOrderAddrInfo",
	//门店导出
	selectStoreByAll: "employee/getAllStore",

	//云仓系统相关api
	//搜索门店对应库存
	selectGoodsToStore: "collectGoodsToStore/selectGoodsToStore",
	//出入库明细
	getStockInfo: "collectGoodsToStore/getStockInfo",
	//出库提交
	commitStockByChuKu: "collectGoodsToStore/commitStockByChuKu",
	//入库提交
	commitStockByRuKu: "collectGoodsToStore/commitStockByRuKu",
	//获取全部商品(物料)
	getAllGoods: "collectGoodsToStore/getAllGoods",

	//获取分类
	selectAllCollect: "https://yibanapi.xiaoyujia.com/collectCategory/selectAll",
	//获取分类下的物品
	selectAllCollectType: "https://yibanapi.xiaoyujia.com/collectGoods/getByName",
	// 根据员工权限获取所辖门店
	getStoreDataByEid: "settlement/getBAOverviewStoreData",
	//获取所有门店接口-最新
	getAllStore: "store/getAllStore",
	//获取全部物料次数
	getAllStoreInventory: "collectGoodsToStore/getAllStoreInventory",
	//该门店所有物料出入库明细
	getStoreInventoryInfo: "collectGoodsToStore/getStoreInventoryInfo",
	// 获取经营指标数据
	getIndicDataPage: "settlement/getIndicDataPage",
	// 导出经营指标数据
	getIndicDataExport: "settlement/getIndicDataExport",

	getCommunityHeadPage: "communityHead/getComHeadList",
	// 审核
	updateCommHead: "communityHead/updateCommHead",
	// 删除
	delCommHead: "communityHead/delComm",
	// 查看附件
	viewCommAttach: "communityHead/viewCommAttach",
	// 编辑
	updateComm: "communityHead/updateComm",

	getCommEmployee: "communityHead/getCommEmployee",
	getComHeadListExport: "communityHead/getComHeadListExport",
	// 门店店长 经济人 社区管家列表
	getAgentAndShopownerAndCommManager: "/employee/getAgentAndShopownerAndCommManager",
	//门店物料导出
	exportCollectStore: "collectGoodsToStore/exportCollectStore",
	//超时未接单查询
	selectDelayOrder: "orderNeeds/selectDelayOrder",
	//工单报表
	workOrderReport: "workOrder/selectWorkOrderReport",
	//工单报表导出
	downloadWorkOrderReport: "workOrder/downloadWorkOrderReport",
	// 门店盈亏
	getStorePalPageData: "/storeMonthlyCost/getStorePalPageData",
	// 门店盈亏报表导出
	exportStorePal: "/storeMonthlyCost/exportStorePal",
	//保险号更新
	updateContractBxPage: "/contract/updateContractBxPage",
	//保险导出
	downloadContractBx: "/contract/downloadContractBx",
	//生成抖音分享码
	getDouYinCode: "/marketing/getDouYinCode",
	//流程日志
	orderNeedTrackPage: "/orderNeedTrack/orderNeedTrackPage",

	// 线索跟踪
	orderNeedsTrackPage: "/orderNeeds/orderNeedsTrackPage",
	getContractByNeedsId: "/orderNeeds/getContractByNeedsId",
	getFollowUp: "/orderNeeds/getFollowUp",
	getAllNeedsChannel: "/orderNeeds/getAllNeedsChannel",
	getNeedsAllTg: "/orderNeeds/getNeedsAllTg",
	// 线索跟踪导出
	orderNeedsTrackExport: "/orderNeeds/orderNeedsTrackExport",
	// 线索备注
	getOrderNeedsLogById: "/orderNeeds/getOrderNeedsLogById",
	//复购统计-开发员工列表
	getDevEmployeePage: "/OrderStatisticsController/getDevEmployeePage",
	//复购统计-开发员工列表导出
	getDevEmployeeExcel: "/OrderStatisticsController/getDevEmployeeExcel",
	//复购统计-复购列表
	getRepurchaseEmployeePage: "/OrderStatisticsController/getRepurchaseEmployeePage",
	//复购统计-复购列表导出
	getRepurchaseEmployeeExcel: "/OrderStatisticsController/getRepurchaseEmployeeExcel",
	//规范员工
	getStandardEmployee: "/OrderStatisticsController/getStandardEmployee",
	//规范员工导出
	getStandardEmployeeExcel: "/OrderStatisticsController/getStandardEmployeeExcel",
	exportSettlementData: "settlement/exportSettlementData",

	getABCSKOrderProductCategoryId: "product/getABCSKOrderProductCategoryId",
	exportAgentABC: "order/exportAgentABC",
	listContractHoliday: "contractHoliday/ListContractHoliday",
	exportOrderNeeds: "/orderNeeds/exportOrderNeeds", // 线索导出
	exportAgentSettlement: "/settlement/exportAgentSettlement", // 结算数据导出
	pvUploadVideoList: "https://api.xiaoyujia.com/employee/getUploadVideoList",
	pvVideoCateIdList: "https://api.xiaoyujia.com/employee/getVodCateIdList",
	pvVideoShow: "https://api.xiaoyujia.com/employee/getVideoInfo",
	setVideoState: "https://api.xiaoyujia.com/employee/setVideoState",
	getAllRole: "role/getAllRole",

	// 课程管理相关接口（家姐课堂）
	listCourse: "course/listCourse",
	getCoursePage: "course/getCoursePage",
	pageCourseVo: "course/pageCourseVo",
	listCourseLogVo: "course/listCourseLogVo",
	addCourse: "course/addCourse",
	updateCourse: "course/updateCourse",
	deleteCourse: "course/deleteCourse",

	getCourseContentPage: "courseContent/getCourseContentPage",
	addCourseContent: "courseContent/addCourseContent",
	updateCourseContent: "courseContent/updateCourseContent",
	deleteCourseContent: "courseContent/deleteCourseContent",
	courseContentExcelDownload: "courseContent/courseContentExcelDownload",
	orderNeedsWorkOrderDownload: "orderNeedsWorkOrder/orderNeedsWorkOrderDownload",

	getCourseType: "courseType/getCourseType",
	addCourseType: "courseType/addCourseType",
	updateCourseType: "courseType/updateCourseType",
	deleteCourseType: "courseType/deleteCourseType",

	getCourseGroup: "courseGroup/getCourseGroup",
	getCourseGroupPage: "courseGroup/getCourseGroupPage",
	addCourseGroup: "courseGroup/addCourseGroup",
	updateCourseGroup: "courseGroup/updateCourseGroup",
	deleteCourseGroup: "courseGroup/deleteCourseGroup",

	// 问卷调查
	pageSurvey: "survey/pageSurvey",
	insertSurvey: "survey/insertSurvey",
	updateSurvey: "survey/updateSurvey",
	deleteSurvey: "survey/deleteSurvey",

	pageSurveyQuestion: "surveyQuestion/pageSurveyQuestion",
	listSurveyQuestion: "surveyQuestion/listSurveyQuestion",
	insertSurveyQuestion: "surveyQuestion/insertSurveyQuestion",
	updateSurveyQuestion: "surveyQuestion/updateSurveyQuestion",

	listSurveyRecord: "surveyRecord/listSurveyRecord",
	listSurveyAnswerRecord: "surveyRecord/listSurveyAnswerRecord/",
	listSurveyRecordExcelDownload: "surveyRecord/listSurveyRecordExcelDownload",

	// 证书管理相关接口
	getAllUnionCertificates: "certificates/getAllUnionCertificates",
	getUnionCertificates: "certificates/getUnionCertificates",
	addUnionCertificates: "certificates/addUnionCertificates",
	updateUnionCertificates: "certificates/updateUnionCertificates",
	deleteUnionCertificates: "certificates/deleteUnionCertificates",

	getAllUnionCertRecord: "certificatesRecord/getAllUnionCertRecord",
	getUnionCertRecord: "certificatesRecord/getUnionCertRecord",
	addUnionCertRecord: "certificatesRecord/addUnionCertRecord",
	updateUnionCertRecord: "certificatesRecord/updateUnionCertRecord",
	deleteUnionCertRecord: "certificatesRecord/deleteUnionCertRecord",

	// 考试相关接口
	// 试题
	getUnionQuestionPage: "unionQuestion/getUnionQuestionPage",
	getExamQuestion: "unionQuestion/getExamQuestion",
	unionQuestionExcelDownload: "unionQuestion/unionQuestionExcelDownload",
	excelImport: "unionQuestion/excelImport",

	addUnionQuestion: "unionQuestion/addUnionQuestion",
	updateUnionQuestion: "unionQuestion/updateUnionQuestion",
	deleteUnionQuestion: "unionQuestion/deleteUnionQuestion",

	// 选项
	updateQuestionChoice: "unionQuestionChoice/updateQuestionChoice",
	deleteQuestionChoice: "unionQuestionChoice/deleteQuestionChoice",

	// 答案
	updateAnswerContent: "unionAnswerContent/updateAnswerContent",
	addAnswerContent: "unionAnswerContent/addAnswerContent",

	// 题库
	getUnionQuestionLib: "unionQuestionLib/getUnionQuestionLib",
	getQuestionLibPage: "unionQuestionLib/getQuestionLibPage",
	addUnionQuestionLib: "unionQuestionLib/addUnionQuestionLib",
	updateUnionQuestionLib: "unionQuestionLib/updateUnionQuestionLib",
	deleteUnionQuestionLib: "unionQuestionLib/deleteUnionQuestionLib",

	// 题库配置
	listUnionQuestionLibRule: "unionQuestionLibRule/listUnionQuestionLibRule",
	getUnionQuestionLibVo: "unionQuestionLibRule/getUnionQuestionLibVo",
	updateUnionQuestionLibVo: "unionQuestionLibRule/updateUnionQuestionLibVo",
	updateUnionQuestionLibRuleList: "unionQuestionLibRule/updateUnionQuestionLibRuleList",
	deleteUnionQuestionLibRule: "unionQuestionLibRule/deleteUnionQuestionLibRule",

	// 考试
	addUnionExam: "unionExam/addUnionExam",
	getUnionExam: "unionExam/getUnionExam",
	getUnionExamPage: "unionExam/getUnionExamPage",
	selectUnionExamById: "unionExam/selectUnionExamById/",
	updateUnionExam: "unionExam/updateUnionExam",
	deleteUnionExam: "unionExam/deleteUnionExam",

	// 考试记录
	unionExamRecord: "unionExamRecord/getUnionExamRecord",
	startUnionExamRecord: "unionExamRecord/startUnionExamRecord",
	endUnionExamRecord: "unionExamRecord/endUnionExamRecord",
	pageUnionExamRecord: "unionExamRecord/pageUnionExamRecord",
	pageUnionExamRecordVo: "unionExamRecord/pageUnionExamRecordVo",
	examRecordExcelDownload: "unionExamRecord/examRecordExcelDownload",

	// 考试答案记录
	answerQuestion: "unionAnswerRecord/answerQuestion",

	// 考试/题库类型
	getExamType: "unionExamType/getExamType",
	// 获取人寿保险保单人员
	chinaLifeContractBxPage: "chinaLife/chinaLifeContractBxPage",
	doInsure: "chinaLife/doInsureList",
	downloadChinaLife: "chinaLife/downloadChinaLife",
	pdfUrl: "https://api.xiaoyujia.com/openapi/viewPdfUrl",
	ActivityUnionList: "ActivityUnion/selectList",
	updateActivityUnion: "ActivityUnion/updateActivityUnion",
	insertActivityUnion: "ActivityUnion/insertActivityUnion",
	getQiyeFileList: "qiyeFiles/getQiyeFileList",
	updateQiyeFile: "qiyeFiles/updateQiyeFile",
	getQiyeFilesByChildrenId: "qiyeFiles/getQiyeFilesByChildrenId",
	addCatalog: "qiyeFiles/addCatalog",
	deleteCatalog: "qiyeFiles/deleteCatalog",
	addChildrenFile: "qiyeFiles/addChildrenFile",
	earnestMoneyStoreList: "StoreEarnestMoneyController/getStoreList",
	earnestMoneyPageList: "StoreEarnestMoneyController/selectList",
	addEarnestMoney: "StoreEarnestMoneyController/addEarnestMoney",
	earnestMoneyExportExcel: "StoreEarnestMoneyController/exportExcel",
	getEarnestMoneyAll: "StoreEarnestMoneyController/getEarnestMoneyAll",
	getDouyinMemberOrderList: "DouYinMemberToOrder/selectList",
	getDouyinMemberOrderExport: "DouYinMemberToOrder/export",

	getTimeoutReturnOrder: "/timeoutReturnOrder/getTimeoutReturnOrder",
	pushMsgNotice: "/timeoutReturnOrder/pushMsgNotice",
	batchPushMsgNotice: "/timeoutReturnOrder/batchPushMsgNotice",

	// 消息通知相关
	pageUnionInform: 'unionInform/pageUnionInform',
	insertUnionInform: 'unionInform/insertUnionInform',
	updateUnionInform: 'unionInform/updateUnionInform',
	deleteUnionInform: 'unionInform/deleteUnionInform',
	listUnionInformContent: 'unionInformContent/listUnionInformContent/',
	updateUnionInformContentList: 'unionInformContent/updateUnionInformContentList',
	deleteUnionInformContent: 'unionInformContent/deleteUnionInformContent',

	listAllUnionInformType: 'unionInformType/listAllUnionInformType',
	listAllUnionInformGroup: 'unionInformType/listAllUnionInformGroup',
	contractManagePage: 'insuranceManage/queryPage',
	getFranchiseStoreList: 'store/getFranchiseStoreList',
	getStoreAccountAuditing: 'store/getStoreAccountAuditing',
	updateStoreAccountAuditing: 'store/updateStoreAccountAuditing',
	getStoreDayLog: 'store/getStoreDayLog',

	// 成就相关接口
	pageAchievement: 'achievement/pageAchievement',
	getAchievement: 'achievement/getAchievement/',
	insertAchievement: 'achievement/insertAchievement',
	updateAchievement: 'achievement/updateAchievement',
	deleteAchievement: 'achievement/deleteAchievement',

	pageAchievementType: 'achievementType/pageAchievementType',
	listAchievementType: 'achievementType/listAchievementType',
	insertAchievementType: 'achievementType/insertAchievementType',
	updateAchievementType: 'achievementType/updateAchievementType',
	deleteAchievementType: 'achievementType/deleteAchievementType',

	pageAchievementRecord: 'achievementRecord/pageAchievementRecord',

	// 合伙人数据统计
	pageOrderNeedsPartner: 'orderNeeds/pageOrderNeedsPartner',
	pageBaomuPartner: 'employee/pageBaomuPartner',
	getEmployeePhoneById: 'employee/getEmployeePhoneById/',
	//抖音退款
	refundDouyinByNo: 'https://api.xiaoyujia.com/order/refundDouyinByNo',
	getCcbStoreWithdrawalCallBack: "storeWithdrawal/getCcbStoreWithdrawalCallBack",
	queryccbcallBack: 'storeWithdrawal/scheduledByCcbStoreWithdrawal',
	getAllFranchiseStore: 'storeWithdrawal/getAllFranchiseStore',
	sendVerificationCode: 'account/sendVerificationCode',
	sendVerificationCodeLogin: 'account/sendVerificationCodeLogin',
	orderNeedsLogExport: 'orderNeeds/orderNeedsLogExport',
	editRemarkDouyinByFirst: 'DouYinMemberToOrder/editRemark',
	writeoffDouyinNo: 'DouYinMemberToOrder/writeoffDouyinNo',
	operAnalysisList: 'settlement/operAnalysisList',
	operAnalysisMainList: 'settlement/operAnalysisMainList',
	operAnalysisSum: 'settlement/operAnalysisSum',
	operAnalysisMainExport: 'settlement/operAnalysisMainExport',
	parameterTeamList: 'settlement/parameterTeamList',
	parameterTeamSum: 'settlement/parameterTeamSum',
	parameterTeamExport: 'settlement/parameterTeamExport',


	getPartTimeJobWithdrawalData: 'partTimeJobBalance/getPartTimeJobWithdrawalData',
	getPartTimeJobWithdrawalLogData: 'partTimeJobBalance/getPartTimeJobWithdrawalLogData',
	auditingPartTimeJobWithdrawal: 'partTimeJobBalance/auditingPartTimeJobWithdrawal',
	queryRlwPayment: 'partTimeJobBalance/queryRlwPayment',
	getRlwQuestLog: 'partTimeJobBalance/getRlwQuestLog',

	listEmployeeSigninVo: 'employeeSignin/listEmployeeSigninVo',
	listEmployeeSigninVoExport: 'employeeSignin/listEmployeeSigninVoExport',
	getSumAll: 'DouYinMemberToOrder/getSumAll',
	getAllCategory: 'category/getAllCategory',
	getAllProductCategory: 'employeeServiceScript/getAllProductCategory',
	getProductScriptTypeData: 'employeeServiceScript/getProductScriptTypeData',
	saveProductScriptType: 'employeeServiceScript/saveProductScriptType',
	getServiceLanguage: 'employeeServiceScript/getServiceLanguage',
	saveServiceLanguage: 'employeeServiceScript/saveServiceLanguage',
	delServiceLanguage: 'employeeServiceScript/delServiceLanguage',
	editServiceLanguage: 'employeeServiceScript/editServiceLanguage',
	delProductScriptType: 'employeeServiceScript/delProductScriptType',
	getRlwPrechargeLog: 'partTimeJobBalance/getRlwPrechargeLog',
	addRlwPrechargeMoney: 'partTimeJobBalance/addRlwPrechargeMoney',

	getKuaiShouMemberOrderList: "KuaishouMemberToOrderController/selectList",
	getKuaiShouMemberOrderExport: "KuaishouMemberToOrderController/export",
	getKuaiShouSumAll: 'KuaishouMemberToOrderController/getSumAll',
	writeoffKuaiShouNo: 'KuaishouMemberToOrderController/writeoffKuaiShouNo',
	editRemarkKuaiShouTicket: 'KuaishouMemberToOrderController/editRemark',
	cancelKuaiShouTicket: 'https://api.xiaoyujia.com/openapi/kuaishouUseCancelTicket',
	bindKuaiShouTicket: 'https://api.xiaoyujia.com/openapi/kuaiShouLifeTicketWriteOff',

	getPoiList: 'DouYinPoiProductController/selectList',
	saveJson: 'DouYinPoiProductController/saveJson',
	poiSaveStatus: 'DouYinPoiProductController/poiSaveStatus',
	appletLiveDeviceExcelDownload: 'appletLiveDevice/appletLiveDeviceExcelDownload',
	updateDouYinMemberPay: 'DouYinMemberToOrder/updateState',

	//合同报险
	getReportInsuranceList: '/reportInsurance/queryPage',
	getReportInsuranceById: '/reportInsurance/getReportInsuranceById',
	updateReportInsurance: '/reportInsurance/updateReportInsurance',
	downloadReportInsurance: '/reportInsurance/download/',


	getStoreIncomeBill: '/aiRun/getStoreIncomeBill/',
	getWithdrawalInvoice: '/storeWithdrawal/getWithdrawalInvoice',
	updateWithdrawalInvoice: '/storeWithdrawal/updateWithdrawalInvoice',
	getAllContractStoreOrder: '/storeWithdrawal/getAllContractStoreOrder',
	getAllContractStoreAmountTotal: '/storeWithdrawal/getAllContractStoreAmountTotal',
	getStoreWallet: '/storeWithdrawal/getStoreWallet',
	getStoreBalanceDetails: '/storeWithdrawal/getStoreBalanceDetails',
	updateStoreWallet: '/storeWithdrawal/updateStoreWallet',

	getjgDouyinMemberOrderList: "jinGangDouYinMemberToOrder/selectList",
	getjgDouyinMemberOrderExport: "jinGangDouYinMemberToOrder/export",
	editRemarkjgDouyinByFirst: 'jinGangDouYinMemberToOrder/editRemark',
	writeoffjgDouyinNo: 'jinGangDouYinMemberToOrder/writeoffDouyinNo',
	getjgSumAll: 'jinGangDouYinMemberToOrder/getSumAll',
	updatejgDouYinMemberPay: 'jinGangDouYinMemberToOrder/updateState',
	refundJinGangDouyinByNo: 'https://api.xiaoyujia.com/order/refundJinGangDouyinByNo',

	getjgPoiList: 'jinGangPoiController/selectList',
	savejgJson: 'jinGangPoiController/saveJson',
	poijgSaveStatus: 'jinGangPoiController/poiSaveStatus',

	contractInsuranceData:"contractInsuranceController/getList",
	exportContractInsuranceData:"contractInsuranceController/export",
	viewTencentSignPdf:"https://api.xiaoyujia.com/openapi/tencentSign/viewSignPdf",

	// 商城产品
	mallProductList: "/mall/product/list",
	updateMallStatus: "/mall/product/updateMallStatus",
	mallProductDetail: "/mall/product/detail",
	updateMall: "/mall/product/updateMall",
	insertMall: "/mall/product/insertMall",

	mallCategoryList: "/mall/product/category/list",
	mallCategoryUpdate: "/mall/product/category/update",
	mallCategoryadd: "/mall/product/category/add",
	mallCategoryDelete: "/mall/product/category/delete",
	mallOrderList: "/mall/order/list",
	mallOrderDetail: "/mall/order/detail",
	updateLogisticsNo: "/mall/order/updateLogisticsNo",
	mallSkuList: "/mall/sku/list",
	mallSkuOutIn: "/mall/sku/mallSkuOutIn",
	skuLogList: "/mall/sku/skuLogList",
	insertSku: "/mall/sku/insertSku",
	getInsuranceReportList: "/contractInsuranceController/getInsuranceReportList",
	exportInsuranceReport: "/contractInsuranceController/exportInsuranceReport",
	siteEmployeeJZYQuery: "/jizhengyun/siteEmployeeJZYQuery",
}
