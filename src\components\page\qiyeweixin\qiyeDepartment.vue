<template>
  <div class="container">
    <div class="handle-box">
      <el-form :inline="true" :model="pageInfo" class="demo-form-inline">
        <el-form-item label="主部门" >
          <el-select v-model="dom.departmentName" value-key="id" filterable clearable placeholder="使用部门">
            <el-option
                v-for="item in partys"
                :key="item.id"
                :label="item.name"
                :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-refresh" @click="reset()">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="list" :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}"
              stripe v-loading="loading" class="table" border style="width: 100%">
      <el-table-column
          prop="id"
          label="部门Id"
          :show-overflow-tooltip="true"
          width="100">
      </el-table-column>
      <el-table-column
          prop="name"
          label="部门名称"
          :show-overflow-tooltip="true"
          width="100">
      </el-table-column>
      <el-table-column
          prop="parentid"
          label="父级Id"
          width="110">
      </el-table-column>
      <el-table-column fixed="right" label="操作">
        <template slot-scope="scope">
          <div style="display: flex">
            <el-popconfirm title="此操作会将该部门下的所有员工一并删除，确定删除吗？" @confirm="delDepartMent(scope.row.id)">
              <el-button type="danger" size="success" slot="reference" style="margin-left: 10px">删除部门
              </el-button>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChange"
            :current="this.dom.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="10"/>
    </div>
  </div>
</template>

<script>

export default {
  name: "qiyeStaff",
  inject: ['reload'],
  data() {
    return {
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      pageInfo2: {total: 10, size: 10, current: 1, pages: 1},
      pageSizeOpts: [10, 15, 20],
      loading: false,
      list: [],
      partys: [],
      department:false,
      position:false,
      dom: {
        name:null,
        departmentName:null,
        userid:null,
        size: 10,
        current: 1,
      },
    };
  },
  created() {
    this.getData()
    this.getParty()
  },
  methods: {
    delDepartMent(val){
      this.$getData("delDepartMent", {id:val}).then(res => {
        if (res.status === 200) {
          this.$message.success('删除成功!')
          this.getParty()
          this.reset()
        }else {
          this.$message.error(res.msg)
        }
      })
    },
    onSubmit(){
      this.dom.size=10
      this.dom.current=1
      this.getData();
    },
    //重置
    reset(){
      this.dom.name=null;
      this.dom.departmentName=null;
      this.dom.userid=null;
      this.getData();
    },
    getData() {
      this.dom.userid = null
      if(this.dom.departmentName){
        for (let i = 0; i <this.partys.length; i++) {
          if(this.partys[i].name===this.dom.departmentName){
            console.log(this.partys[i])
            this.dom.userid = this.partys[i].id
          }
        }
      }
      this.$postData("getPagingParty", this.dom).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dom.size = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dom.current = index;
      this.getData();
    },
    getParty() {
      this.$getData("getParty",).then(res => {
        if (res.status === 200) {
          this.partys = res.data
        }
      })
    },
  }
}
</script>

<style scoped>
.el-tag {
  margin-left: 10px;
}
table {
  border-collapse: collapse; /*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
  border-left: #C8B9AE solid 1px;
  border-top: #C8B9AE solid 1px;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
</style>