<template>
    <div>
        <Drawer
                title="设置"
                v-model="value3"
                width="40%"
                :mask-closable="false"
                @on-close="chooseThisModel"
                :styles="styles"
        >
            <Form :model="formItem" :label-width="80" label-position="left">
                        <div class="searchDiv">
                            <FormItem label="跳转链接" prop="billNo">
                                <Input placeholder="跳转链接"
                                       disabled
                                       v-model="formItem.url">
                                </Input>
                            </FormItem>
                            <FormItem label="推送标题" prop="productName">
                                <Input placeholder="推送标题"
                                       v-model="formItem.sendTitle">
                                </Input>
                                <span>不超过128个字节，超过会自动截断</span>
                            </FormItem>
                            <FormItem label="工资异常推送者" prop="productName">
                                <Input placeholder="工资异常推送者"
                                       v-model="formItem.recipient">
                                </Input>
                                <span>仅支持工号填写</span>
                            </FormItem>
                            <FormItem label="推送描述">
                                <Input v-model="formItem.description" type="textarea" :rows="3"
                                       placeholder="推送描述"/>
                                <span>不超过512个字节，超过会自动截断</span>
                            </FormItem>
                        </div>
            </Form>
            <div class="demo-drawer-footer">
                <Button style="margin-right: 8px" @click="chooseThisModel">取消</Button>
                <Button type="primary" @click="update">确定</Button>
            </div>
        </Drawer>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                current: "0",
                value3: true,
                styles: {
                    height: 'calc(100% - 55px)',
                    overflow: 'auto',
                    paddingBottom: '53px',
                    position: 'static'
                },
                periodList: [],
                levelList: [],
                typeList: [],
                formItem: {
                    id:null
                },
            }
        },
        created: function () {
            this.getById()
        },
        methods: {
            getById() {
                this.$getData("salarySetUp_getById", {
                    id: 1
                }).then(res => {
                    if (res.status === 200) {
                        this.formItem = res.data;
                    }
                })
            },
            update() {
                this.$postData("salarySetUp_update", this.formItem).then(res => {
                        if (res.status === 200) {
                            this.$message({
                                type: 'success',
                                message: '修改成功!'
                            });
                            this.chooseThisModel()
                        } else {
                            this.$message.error("保存失败，" + res.msg);
                        }
                    }
                )
            },
            chooseThisModel() {
                this.value3 = false;
                this.$emit('init-choose', "");
            },
        }
    }
</script>
<style>
    .demo-drawer-footer {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        border-top: 1px solid #e8e8e8;
        padding: 10px 16px;
        text-align: left;
        background: #fff;
    }

    .searchDiv {
        width: 60%;
        margin: 20px auto;
        font-weight: bold;
        font-size: 17px !important;
    }
</style>