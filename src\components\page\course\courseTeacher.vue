<template>
	<div class="container">
		<div class="handle-box" style="margin: 20px 20px;">
			<el-form ref="form" :model="pageInfo">
				<el-row>
					<el-col :span="3">
						<el-form-item label="搜索" style="margin-right: 20px">
							<el-input v-model="quer.search" placeholder="请输入姓名等"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="3">
						<el-form-item label="校区" style="margin-right: 20px">
							<el-select v-model="quer.campusId">
								<el-option label="" value=""></el-option>
								<el-option v-for="(item,index) in campusList" :key="index" :label="item.campusName"
									:value="item.id"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="3">
						<el-form-item label="权限" style="margin-right: 20px">
							<el-select v-model="quer.roleId">
								<el-option label="" value=""></el-option>
								<el-option v-for="(item,index) in roleIdList" :key="index" :label="item.name"
									:value="item.id"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="3">
						<el-form-item label="状态" style="margin-right: 20px">
							<el-select v-model="quer.state">
								<el-option label="" value=""></el-option>
								<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
									:value="item.value"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="3">
						<el-form-item label="展示" style="margin-right: 20px">
							<el-select v-model="quer.display">
								<el-option label="" value=""></el-option>
								<el-option v-for="(item,index) in displayList" :key="index" :label="item.text"
									:value="item.value"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-button type="primary" @click="query()" icon="el-icon-search"
							style="margin-left: 20px">搜索</el-button>
						<el-button icon="el-icon-refresh" @click="reset()" style="margin-left: 20px">重置</el-button>
						<el-button type="primary" icon="el-icon-edit" @click="openModal(0,0)"
							style="margin-left: 20px">新增</el-button>
					</el-col>
				</el-row>

			</el-form>
		</div>
		<el-table :data="list" v-loading="loading" border stripe
			:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
			<el-table-column prop="id" width="60" label="编号">
			</el-table-column>
			<el-table-column prop="realName" width="100" label="导师姓名">
			</el-table-column>
			<el-table-column prop="phone" width="100" label="联系电话">
			</el-table-column>
			<el-table-column prop="campusName" width="100" label="所在校区">
				<template slot-scope="scope">
					<el-tag v-if="item.id == scope.row.campusId" v-for="(item,index) in campusList" :key="index"
						:type="typeStyleList[index%4]">{{item.campusName}}</el-tag>
					<span v-if="!scope.row.campusId">-</span>
				</template>
			</el-table-column>
			<el-table-column width="140" prop="headPortrait" label="导师头像" :render-header="renderHeader">
				<template slot-scope="scope">
					<img :src="scope.row.headImg||blankImg" style="width: 100px;height: 100px;"
						@click="openImg(scope.row.headImg||blankImg)">
				</template>
			</el-table-column>
			<el-table-column prop="teacherIntroduction" width="100" label="导师介绍" :render-header="renderHeader">
				<template slot-scope="scope">
					<span>{{formatLongStr(scope.row.teacherIntroduction)}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="selfContent" width="100" label="自荐理由" :render-header="renderHeader">
				<template slot-scope="scope">
					<span>{{formatLongStr(scope.row.selfContent)}}</span>
				</template>
			</el-table-column>
			<el-table-column width="140" prop="teacherImg" label="介绍详情图" :render-header="renderHeader">
				<template slot-scope="scope">
					<img :src="scope.row.teacherImg||blankImg" style="width: 100px;height: 100px;"
						@click="openImg(scope.row.teacherImg||blankImg)">
				</template>
			</el-table-column>
			<el-table-column width="140" prop="teacherImg" label="微信二维码" :render-header="renderHeader">
				<template slot-scope="scope">
					<img :src="scope.row.qrImg||blankImg" style="width: 100px;height: 100px;"
						@click="openImg(scope.row.qrImg||blankImg)">
				</template>
			</el-table-column>

			</el-table-column>
			<el-table-column width="120" prop="teacherScore" label="导师积分" :render-header="renderHeader">
			</el-table-column>
			<el-table-column width="120" prop="teacherLevel" label="导师星级" :render-header="renderHeader">
			</el-table-column>
			<el-table-column width="100" label="导师权限" :render-header="renderHeader">>
				<template slot-scope="scope">
					<el-tag v-if="item.id == scope.row.roleId" v-for="(item,index) in roleIdList" :key="index"
						:type="typeStyleList[index%4]">{{item.name}}</el-tag>
				</template>
			</el-table-column>
			<el-table-column width="100" label="导师状态">
				<template slot-scope="scope">
					<el-tag v-if="item.value == scope.row.state" v-for="(item,index) in stateList" :key="index"
						:type="item.type">{{item.text}}</el-tag>
				</template>
			</el-table-column>
			<el-table-column width="100" label="是否展示" :render-header="renderHeader">
				<template slot-scope="scope">
					<el-tag v-if="item.value == scope.row.display" v-for="(item,index) in displayList" :key="index"
						:type="item.type">{{item.text}}</el-tag>
				</template>
			</el-table-column>
			<el-table-column width="100" label="是否自荐">
				<template slot-scope="scope">
					<el-tag v-if="scope.row.selfRecommendation==0" type="info">否</el-tag>
					<el-tag v-if="scope.row.selfRecommendation==1" type="success">是</el-tag>
				</template>
			</el-table-column>
			<el-table-column width="130" prop="creator" label="创建人">
				<template slot-scope="scope">
					<span v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
					<span v-else>{{scope.row.creator||''}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="createTime" label="创建时间" width="160" sortable>
			</el-table-column>
			<el-table-column fixed="right" label="操作" width="220">
				<template slot-scope="scope">
					<el-button @click="openModal(1,scope.$index)" type="primary" size="small">修改资料
					</el-button>
					<el-button @click="addCert(scope.$index)" type="success" size="small"
						:disabled="scope.row.haveCert==1">颁发证书
					</el-button>
				</template>
			</el-table-column>
		</el-table>

		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--编辑-->
		<el-drawer size="70%" :with-header="false" :visible.sync="editModal" direction="rtl">
			<div class="drawerForm" style="margin: 40px 40px;">
				<h2>课程导师{{editType==0?'创建':'编辑'}}</h2>
				<el-form :model="dto" :rules="rules" ref="ruleForm" class="demo-ruleForm" size="mini">
					<el-form-item label="员工检索">
						<el-input v-model="searchText" placeholder="输入员工姓名/工号进行检索" @input="listEmployee"
							@change="listEmployee" style="width: 280px"></el-input>
					</el-form-item>
					<el-form-item label="员工选择">
						<el-select v-model="dto.employeeId" placeholder="请选择" @change="selectEmployee"
							:disabled="editType==1">
							<el-option v-for="(item,index) in employeeList" :key="index"
								:label="item.realName+'-'+item.no" :value="item.id">
							</el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="姓名">
						<el-input v-model="dto.realName" placeholder="可在上方检索后再进行选择" style="width: 180px"></el-input>
					</el-form-item>
					<el-form-item label="联系电话">
						<el-input v-model="dto.phone" placeholder="请填写导师联系电话" maxlength="11" type="number"
							oninput="if(value.length>11)value=value.slice(0,11)" style="width: 180px"></el-input>
					</el-form-item>
					<el-form-item label="所在校区：">
						<el-select v-model="dto.campusId" placeholder="请选择" style="width: 100px;">
							<el-option v-for="(item,index) in campusList" :key="index" :label="item.campusName"
								:value="item.id"></el-option>
						</el-select>
						<el-button type="success" icon="el-icon-circle-plus-outline" @click="openCampus()"
							style="margin-left: 20px">添加
						</el-button>
					</el-form-item>
					<el-form-item label="头像">
						<el-upload class="avatar-uploader" action="https://biapi.xiaoyujia.com/files/uploadFiles"
							:show-file-list="false" :on-success="imgUploadSuccess">
							<div @click="openImgUpload(0)"><img v-if="dto.headImg" :src="dto.headImg" class="avatar">
								<i v-else class="el-icon-plus avatar-uploader-icon"></i>
							</div>
						</el-upload>
						<el-tooltip class="item" effect="dark" content="推荐尺寸：250*250（1:1）" placement="top-start">
							<i class="el-icon-question" style="margin-left:10px;"></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="导师介绍">
						<el-input type="textarea" :rows="3" placeholder="请输入导师介绍" v-model="dto.teacherIntroduction">
						</el-input>
						<el-tooltip class="item" effect="dark" content="导师一句话简介，,尽量30个字以下" placement="top-start">
							<i class="el-icon-question" style="margin-left:10px;"></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="自荐理由">
						<el-input type="textarea" :rows="3" placeholder="暂无" v-model="dto.selfContent" :disabled="true">
						</el-input>
						<el-tooltip class="item" effect="dark" content="若理由符合要求，则可通过审核变更为上架状态" placement="top-start">
							<i class="el-icon-question" style="margin-left:10px;"></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="介绍详情图">
						<el-upload class="avatar-uploader" action="https://biapi.xiaoyujia.com/files/uploadFiles"
							:show-file-list="false" :on-success="imgUploadSuccess">
							<div @click="openImgUpload(1)"><img v-if="dto.teacherImg" :src="dto.teacherImg"
									class="avatar">
								<i v-else class="el-icon-plus avatar-uploader-icon"></i>
							</div>
						</el-upload>
						<el-tooltip class="item" effect="dark" content="建议上传长图" placement="top-start">
							<i class="el-icon-question" style="margin-left:10px;"></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="微信二维码">
						<el-upload class="avatar-uploader" action="https://biapi.xiaoyujia.com/files/uploadFiles"
							:show-file-list="false" :on-success="imgUploadSuccess">
							<div @click="openImgUpload(2)"><img v-if="dto.qrImg" :src="dto.qrImg" class="avatar">
								<i v-else class="el-icon-plus avatar-uploader-icon"></i>
							</div>
						</el-upload>
						<el-tooltip class="item" effect="dark" content="上传有效的个人微信二维码" placement="top-start">
							<i class="el-icon-question" style="margin-left:10px;"></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="导师星级" v-if="editType==1">
						<el-input-number v-model="dto.teacherLevel" :min="1" :max="5" label="填写导师星级"></el-input-number>
						<el-tooltip class="item" effect="dark" content="评估导师能力，根据导师收到打赏和评价自动评估，也可手动变更"
							placement="top-start">
							<i class="el-icon-question" style="margin-left:10px;"></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="导师权限：">
						<el-select v-model="dto.roleId" placeholder="请选择" style="width: 100px;">
							<el-option v-for="(item,index) in roleIdList" :key="index" :label="item.name"
								:value="item.id"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="导师状态：">
						<el-select v-model="dto.state" placeholder="请选择" style="width: 100px;">
							<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
								:value="item.value"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="是否展示：">
						<el-select v-model="dto.display" placeholder="请选择" style="width: 100px;">
							<el-option v-for="(item,index) in displayList" :key="index" :label="item.text"
								:value="item.value"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button type="primary"
							@click="submitForm('ruleForm')">{{editType==0?'立即创建':'更新记录'}}</el-button>
						<el-button @click="reset" v-if="editType==0" type="danger">重置</el-button>
						<el-button @click="editModal=false;">取消</el-button>
					</el-form-item>
				</el-form>
			</div>
		</el-drawer>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>
	</div>

</template>

<script>
	import Vue from 'vue';
	export default {
		name: "courseTeacher",
		data() {
			return {
				imgUpload: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669101235017img-upload.png",
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				typeStyleList: ['success', 'info', 'warning', 'primary'],
				imageUrl: '',
				imgModal: false,
				editModal: false,
				editType: 0,
				choiceItemIndex: 0,
				uploadImgType: 0,
				list: null,
				loading: true,
				pageSizeOpts: [20, 30, 50],
				pageInfo: {
					total: 0,
					size: 20,
					current: 1,
					pages: 1
				},
				quer: {
					no: null,
					search: '',
					roleId: null,
					campusId: null,
					name: null,
					size: 20,
					current: 1,
					state: null,
					display: null,
					orderBy: 't.id ASC'
				},
				searchText: '',
				stateList: [{
					value: 0,
					text: "审核",
					type: "warning"
				}, {
					value: 1,
					text: "上架",
					type: "success"
				}, {
					value: 2,
					text: "下架",
					type: "info"
				}],
				displayList: [{
					value: 1,
					text: "展示",
					type: "success"
				}, {
					value: 0,
					text: "隐藏",
					type: "info"
				}],
				roleIdList: [{
					id: 1,
					name: '管理员'
				}],
				dto: {
					id: null,
					realName: null,
					memberId: null,
					employeeId: null,
					headImg: null,
					teacherIntroduction: null,
					teacherImg: null,
				},
				rowData: null,
				journalInfoLoading: true,
				journalList: null,
				journalInfoSizeOpts: [10, 20, 50],
				journalInfo: {
					total: 10,
					size: 10,
					current: 1,
					pages: 1
				},
				journalInfoDrawer: false,
				rules: {
					name: [{
						required: true,
						message: '请检索并选择',
						trigger: 'blur'
					}, ],
				},
				employeeList: [],
				imgList: [],
				campusList: []
			}
		},
		created() {
			this.getData()
		},
		methods: {
			getData() {
				$.ajax({
					type: "post",
					url: "https://api.xiaoyujia.com/course/pageCourseTeacher",
					data: JSON.stringify(this.quer),
					dataType: "json",
					headers: {
						"Content-Type": "application/json"
					},
					success: res => {
						this.loading = false;
						if (res.code === 0) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
						} else {
							this.list = []
							this.$message.error('暂无数据！')
						}
					},
				})
				this.listCampus()
			},
			getCourseTeacherById(id) {
				$.ajax({
					type: "GET",
					url: "https://api.xiaoyujia.com/course/getCourseTeacherById/" + id,
					success: res => {
						if (res.code === 0) {
							this.dto = res.data
						} else {
							this.$message.error(res.msg)
						}
					},
				})
			},
			query() {
				this.loading = true
				this.quer.current = 1
				this.getData()
			},
			reset() {
				this.list = []
				this.quer.search = null
				this.quer.realName = null
				this.quer.state = null
				this.quer.display = null
				this.quer.roleId = null
				this.quer.campusId = null
				this.quer.campusState = null
				this.quer.current = 1
				this.getData();
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size;
				this.getData();
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index;
				this.getData();
			},
			formatLongStr(str) {
				if (!str) {
					return "暂无"
				} else {
					let long = 40
					if (str.length > long) {
						str = str.substring(0, long) + "..."
					}
					return str
				}
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "导师头像") {
					tips = "推荐尺寸：250*250（1:1）"
				} else if (column.label == "导师介绍") {
					tips = "一句话介绍，尽量控制在30字以内"
				} else if (column.label == "自荐理由") {
					tips = "自荐并提交审核成为导师，初始状态为审核，若条件符合可变更为上架状态"
				} else if (column.label == "介绍详情图") {
					tips = "推荐尺寸：1500*高度（长图-高度自适应）"
				} else if (column.label == "导师积分") {
					tips = "通过用户评价获取的平均打分"
				} else if (column.label == "导师星级") {
					tips = "评估导师能力，根据导师收到打赏和评价自动评估，也可手动变更"
				} else if (column.label == "是否展示") {
					tips = "是否在小程序中展示给客户"
				} else if (column.label == "导师权限") {
					tips = "管理员可进行校区管理"
				} else if (column.label == "微信二维码") {
					tips = "可在导师详情页通过二维码，添加导师微信"
				}

				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			// 打开图片预览
			openImg(url) {
				this.imageUrl = url || this.blankImg
				this.imgModal = true
			},
			openCampus() {
				this.editModal = false
				this.$router.push({
					path: '/campus'
				})
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			openModal(index, index1) {
				if (index == 0) {
					this.editType = 0
					this.dto = {
						realName: null,
						memberId: null,
						employeeId: null,
						headImg: null,
						teacherIntroduction: null,
						teacherImg: null,
					}
				} else if (index == 1) {
					this.editType = 1
					this.choiceItemIndex = index1
					this.getCourseTeacherById(this.list[index1].id)
				}
				this.editModal = true
			},
			// 打开图片上传
			openImgUpload(value) {
				this.uploadImgType = value
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.$set(this.dto, "headImg", res.data)
					this.list[this.choiceItemIndex].headImg = res.data
				} else if (this.uploadImgType == 1) {
					this.$set(this.dto, "teacherImg", res.data)
					this.list[this.choiceItemIndex].teacherImg = res.data
				} else if (this.uploadImgType == 2) {
					this.$set(this.dto, "qrImg", res.data)
					this.list[this.choiceItemIndex].qrImg = res.data
				}
			},
			beforeAvatarUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传头像图片大小不能超过 2MB!');
				}
				return isLt2M;
			},
			submitForm(formName) {
				let no = localStorage.getItem('account') || 'admin'
				if (!this.dto.memberId) {
					this.$message.error('请注意，该员工暂时未绑定会员账号，可能导致积分奖励无法赠送，请联系员工登录小程序或请技术处理！')
					return
				}

				if (!this.dto.realName) {
					this.$message.error('请填写导师姓名！')
					return
				}

				if (!this.dto.phone) {
					this.$message.error('请填写导师联系电话！')
					return
				}
				this.$refs[formName].validate((valid) => {
					if (valid) {
						this.$set(this.dto, "creator", this.dto.id ? null : no)
						let url = this.dto.id ? 'updateCourseTeacher' : 'insertCourseTeacher'
						$.ajax({
							type: "post",
							url: "https://api.xiaoyujia.com/course/" + url,
							data: JSON.stringify(this.dto),
							dataType: "json",
							headers: {
								"Content-Type": "application/json"
							},
							success: res => {
								if (res.code === 0) {
									this.$message.success('操作成功！')
									this.$refs[formName].resetFields()
									if (this.editType == 0) {
										this.editModal = false
									}
									this.dto = res.data
									this.list[this.choiceItemIndex] = this.dto
								} else {
									this.$message.error(res.msg)
								}
							},
						})
					} else {
						console.log('error submit!!');
						return false;
					}
				});
			},
			addCert(index) {
				let val = this.list[index]
				$.ajax({
					type: "POST",
					url: 'https://api.xiaoyujia.com/acn/addUnionCertRecord',
					data: JSON.stringify({
						creater: localStorage.getItem("account") || 'admin',
						memberId: val.memberId,
						employeeId: val.employeeId || null,
						certId: 5
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.$message.success('证书颁发成功！')
							val.haveCert = 1
						} else {
							this.$message.error(res.msg)
						}
					},
				})
			},
			// 获取索引
			getIndex(val, list) {
				let index = 0
				for (let i = 0; i < list.length; i++) {
					if (val == list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 选中员工
			selectEmployee(id) {
				let index = this.getIndex(id, this.employeeList)
				let employee = this.employeeList[index]
				this.dto.employeeId = employee.id || null
				this.dto.memberId = employee.memberId || 0
				this.dto.realName = employee.realName || ''
				this.dto.headImg = employee.headPortrait || ''
				this.dto.phone = employee.phone || ''
			},
			deleteCourseTeacherImg(index) {
				let id = this.imgList[index].id
				if (id) {
					$.ajax({
						type: "GET",
						url: "https://api.xiaoyujia.com/content/deleteCourseTeacherImg/" + id,
						data: {},
						dataType: "json",
						success: res => {

						},
					})
				}
				this.$delete(this.imgList, index)
				this.$message.success('图片删除成功！')
			},
			listCampus() {
				$.ajax({
					type: "POST",
					url: 'https://api.xiaoyujia.com/course/listCampus',
					data: JSON.stringify({
						campusState: 1,
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.campusList = res.data
						}
					},
				})
			},
			listEmployee() {
				if (!this.searchText) {
					return
				}
				this.$postData("listEmployeeDto", {
					search: this.searchText,
					employeeType: 20,
					state: 1
				}).then(res => {
					if (res.status == 200) {
						this.employeeList = res.data
					} else {
						this.$message.error("查询失败，" + res.msg)
					}
				})
			}
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 100px;
		height: 100px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 100px;
		height: 100px;
		line-height: 100px;
		text-align: center;
	}

	.avatar {
		width: 100px;
		height: 100px;
		display: block;
	}
</style>