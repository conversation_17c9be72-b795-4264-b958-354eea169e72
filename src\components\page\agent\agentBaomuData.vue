<template>
	<div style="background:#fff;padding: 20px">
		<el-card shadow="hover" body-style="padding: 5px 20px ">
			<div class="card-title">本月保姆注册数量 （ 共 <span style="color: #f00f14">{{allNumByMM}}</span>，平均 <span
					style="color: #f00f14">{{(allNumByMM/baomuNumByMM.length).toFixed(2)}}</span>）</div>
			<g2column :setCharData="baomuNumByMM" :heightSize="300" v-if="showChar" :showLine="true"></g2column>
		</el-card>

		<br>
		<div class="block">
			<span class="demonstration">条件选择</span>&nbsp;

			<el-date-picker v-model="showDate" type="datetimerange" :picker-options="pickerOptions" range-separator="至"
				start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" align="right">
			</el-date-picker>&nbsp;

			<Select filterable style="width: 130px" v-model="employeeDto.storeId">
				<Option value="">请选择</Option>
				<Option v-for="(item,index) in storeList" :value="item.id" :key="index">{{ item.storeName}}</Option>
			</Select>

			<Select filterable style="width: 130px" v-model="employeeDto.state">
				<Option value="">请选择</Option>
				<Option v-for="(item,index) in statusList" :value="item.state" :key="index">{{ item.statusName}}
				</Option>
			</Select>

			<el-button type="primary" @click="searchDate()">区间分析</el-button>
		</div><br>

		<el-card shadow="hover" body-style="padding: 5px 20px ">
			<div class="card-title">保姆信息 - 统计</div>
			<el-row :gutter="20">
				<el-col :span="6">
					<g2char :setCharData="baomuNumBySource" :heightSize="200" v-if="showChar"></g2char>
				</el-col>
				<el-col :span="6">
					<!--                    <el-card shadow="hover" body-style="padding: 5px 20px " >-->
					<!--                        <div class="card-title">需求总数:-->
					<!--                            <span style="color: #f00f14">{{allNumByStatus}}</span></div>-->
					<!--                    </el-card>-->
					<el-card shadow="hover" body-style="padding: 5px 20px " v-for="(item,index) in baomuNumBySource"
						:key="index">
						<div class="card-title">{{item.response}}:
							<span style="color: #f00f14">{{item.num}}</span>
						</div>
					</el-card>

				</el-col>
				<el-col :span="6">
					<g2char :setCharData="baomuNumByWorkType" :heightSize="200" v-if="showChar"></g2char>
				</el-col>
				<el-col :span="6">

					<el-card shadow="hover" body-style="padding: 5px 20px " v-for="(item,index) in baomuNumByWorkType"
						:key="index">
						<div class="card-title">{{item.response}}:
							<span style="color: #f00f14">{{item.num}}</span>
						</div>
					</el-card>

				</el-col>
			</el-row>
			<el-divider></el-divider>
			<el-row :gutter="20">
				<el-col :span="6">

					<el-card shadow="hover" body-style="padding: 5px 20px " v-for="(item,index) in baomuNumByArea"
						:key="index">
						<div class="card-title">{{item.response}}:
							<span style="color: #f00f14">{{item.num}}</span>
						</div>
					</el-card>

				</el-col>
				<el-col :span="18">
					<g2char :setCharData="baomuNumByArea" :heightSize="400" v-if="showChar"></g2char>
				</el-col>


			</el-row>

			<el-row :gutter="20">
				<el-col :span="6">

					<el-card shadow="hover" body-style="padding: 5px 20px " v-for="(item,index) in baomuNumByChannel"
						:key="index">
						<div class="card-title">{{item.response}}:
							<span style="color: #f00f14">{{item.num}}</span>
						</div>
					</el-card>

				</el-col>
				<el-col :span="18">
					<g2char :setCharData="baomuNumByChannel" :heightSize="400" v-if="showChar"></g2char>
				</el-col>
			</el-row>
		</el-card>

		<div class="handle-box">
			<el-button type="primary" @click="output()">保姆列表导出</el-button>
			<el-table :data="baomuList" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :expand-row-keys="expands"
				@expand-change="expandSelect">

				<el-table-column prop="id" width="80" label="编号" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="no" label="工号">
					<template slot-scope="scope">
						<span>{{scope.row.no}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="realName" label="姓名">
					<template slot-scope="scope">
						<span>{{scope.row.realName}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="age" label="年龄" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.age}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="phone" label="手机号">
					<template slot-scope="scope">
						<span>{{scope.row.phone}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="address" label="城市">
					<template slot-scope="scope">
						<span>{{scope.row.address}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="createDate" label="注册时间">
					<template slot-scope="scope">
						<span>{{scope.row.createDate}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="registerWay" label="注册渠道">
					<template slot-scope="scope">
						<span>{{scope.row.registerWay}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="nowState" label="状态">
					<template slot-scope="scope">
						<span>{{scope.row.nowState}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="putTime" label="上架时间">
					<template slot-scope="scope">
						<span>{{scope.row.putTime|| '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="leaveTime" label="下架时间">
					<template slot-scope="scope">
						<span>{{scope.row.leaveTime || '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="storeName" label="门店">
					<template slot-scope="scope">
						<span>{{scope.row.storeName || '暂无'}}</span>
					</template>
				</el-table-column>


				<el-table-column width="140" prop="authEmployeeName" label="鉴定人姓名">
					<template slot-scope="scope">
						<span>{{scope.row.authEmployeeName || '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="authState" label="鉴定状态">
					<template slot-scope="scope">
						<span>{{scope.row.authState || '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="authWorkType" label="鉴定工种">
					<template slot-scope="scope">
						<span>{{scope.row.authWorkType || '暂无'}}</span>
					</template>
				</el-table-column>


				<el-table-column width="140" prop="authLevel" label="鉴定等级">
					<template slot-scope="scope">
						<span>{{scope.row.authLevel || '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="introducerName" label="开发人姓名">
					<template slot-scope="scope">
						<span>{{scope.row.introducerName || '暂无'}}</span>
					</template>
				</el-table-column>
			</el-table>

			<div class="pagination">
				<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
					:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
					:page-size="pageInfo.size" />
			</div>
		</div>

		<div class="handle-box">
			<el-button type="primary" @click="output1()">门店保姆统计导出</el-button>
			<el-table :data="storeBaomuList" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :expand-row-keys="expands"
				@expand-change="expandSelect">

				<el-table-column prop="storeId" width="140" label="门店id" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.storeId}}</span>
					</template>
				</el-table-column>

				<el-table-column width="180" prop="storeName" label="门店名称">
					<template slot-scope="scope">
						<span>{{scope.row.storeName}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="nowState" label="员工状态">
					<template slot-scope="scope">
						<span>{{!scope.row.nowState||scope.row.nowState=='未知'?'-':scope.row.nowState}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="peopleNum" label="人数" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.peopleNum || '0'}}</span>
					</template>
				</el-table-column>
			</el-table>

			<div class="pagination">
				<Page :total="pageInfo1.total" @on-change="onChange" :current="pageInfo1.current" :show-total="true"
					:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
					:page-size="pageInfo1.size" />
			</div>
		</div>
	</div>

</template>

<script>
	import g2char from "./char/g2char";
	import g2column from "./char/g2column";
	import baiduHotChar from "./char/g2column";
	import hotChar from "./char/hotChar";
	import addrMap from "./char/addrMap";
	export default {
		components: {
			"g2char": g2char,
			"g2column": g2column,
			"baiduHotChar": baiduHotChar,
			"hotChar": hotChar,
			"addrMap": addrMap,
		},
		name: "agentBaomuData",
		data() {
			return {
				showChar: false,
				pickerOptions: {
					shortcuts: [{
						text: '昨天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}]
				},
				showDate: '',
				allNumByStatus: 0,
				allNumByMM: 0,
				numByProduct: [],
				baomuNumByArea: [],
				baomuNumByWorkType: [],
				baomuNumBySource: [],
				baomuNumByChannel: [],
				baomuNumByMM: [],
				storeList: [],
				baomuList: [],
				storeBaomuList: [],
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				pageInfo1: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				statusList: [{
					state: null,
					statusName: '已注册'
				}, {
					state: 1,
					statusName: '已上架'
				}, {
					state: 2,
					statusName: '已下架'
				}],
				employeeDto: {
					storeId: null,
					startTime: null,
					endTime: null,
					state: null,
					current: 1,
					size: 10
				}
			}
		},
		created() {
			this.getNumByStatus()
			this.getStore()
			this.pageBaomuRegister()
			this.pageStoreBaomuRegister()
		},
		methods: {
			// 跳转页码
			onChange(index) {
				this.loading = true
				this.employeeDto.current = index
				this.pageBaomuRegister()
				this.pageStroeBaomuRegister()
			},
			pageBaomuRegister() {
				this.$postData("pageBaomuRegister", this.employeeDto).then(res => {
					if (res.status == 200) {
						this.baomuList = res.data.records
						this.pageInfo.current = res.data.current
						this.pageInfo.size = res.data.size
						this.pageInfo.total = res.data.total
					} else {
						this.$message.error("查询失败，" + res.msg)
					}
					this.loading = false
				});
			},
			pageStoreBaomuRegister() {
				this.$postData("pageStoreBaomuRegister", this.employeeDto).then(res => {
					if (res.status == 200) {
						this.storeBaomuList = res.data.records
						this.pageInfo1.current = res.data.current
						this.pageInfo1.size = res.data.size
						this.pageInfo1.total = res.data.total
					} else {
						this.$message.error("查询失败，" + res.msg)
					}
					this.loading = false
				});
			},
			getStore() {
				this.$postData("store_getByList", {}, {}).then(res => {
					if (res.status == 200) {
						this.storeList = res.data;
					}
				});
			},
			searchDate() {
				this.numByResponses = []
				if (this.showDate != null) {
					this.employeeDto.startTime = this.showDate[0]
					this.employeeDto.endTime = this.showDate[1]
				} else {
					this.employeeDto.startTime = null
					this.employeeDto.endTime = null
				}

				this.getNumByStatus()
				this.pageBaomuRegister()
				this.pageStoreBaomuRegister()
			},
			getNumByStatus() {
				this.showChar = false
				this.$postData("getBaomuNum", this.employeeDto, {}).then(res => {
					if (res.status == 200) {

						this.baomuNumByMM = res.data.baomuNumByMM
						this.baomuNumBySource = res.data.baomuNumBySource
						this.baomuNumByArea = res.data.baomuNumByArea
						this.baomuNumByChannel = res.data.baomuNumByChannel
						this.baomuNumByWorkType = res.data.baomuNumByWorkType

						this.allNumByMM = 0
						this.baomuNumByMM.forEach(v => {
							this.allNumByMM += v.num
							v.response = v.response + '日'
						})

						this.showChar = true

					} else {
						this.showChar = true
						this.$message.error("查询失败，" + res.msg);
					}
				})
			},
			// 数据导出
			output() {
				let name = '保姆注册数据'
				this.$postData("pageBaomuExcelDownload", this.employeeDto, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('保姆注册记录下载成功!')
					this.blobExport({
						tablename: name,
						res: res
					})
				})
			},
			// 数据导出
			output1() {
				let name = '门店保姆数据统计'
				this.$postData("pageStoreBaomuExcelDownload", this.employeeDto, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('门店保姆数据统计下载成功!')
					this.blobExport({
						tablename: name,
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a")
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx"
				document.body.appendChild(aLink)
				aLink.click()
				document.body.removeChild(aLink)
			},
		}

	}
</script>

<style scoped>

</style>