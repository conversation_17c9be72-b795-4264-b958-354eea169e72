<template>
    <div class="table">
        <!--<div class="crumbs">-->
        <!--<el-breadcrumb separator="/">-->
        <!--<el-breadcrumb-item>-->
        <!--<i class="el-icon-lx-cascades"></i> 项目管理-->
        <!--</el-breadcrumb-item>-->
        <!--</el-breadcrumb>-->
        <!--</div>-->
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">

                    <el-row>
                        <el-col :span="9">
                            <el-form-item label="项目编号">
                                <el-input
                                        v-model="projectQuery.projectNo"
                                        placeholder="项目编号"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="负责人">
                                <Select v-model="projectQuery.employeeName" filterable style="width: 200px">
                                    <Option v-for="item in employees" :value="item.realName">{{ item.realName}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6" style="text-align:right">
                            <el-form-item>
                                <el-button type="success" style="width:30%" @click="saveModal=true">新增项目</el-button>
                                <el-button type="success" style="width:30%" @click="dictionaryModal=true">新增预算/成本
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>

                        <el-col :span="9">
                            <el-form-item label="立项时间">
                                <el-date-picker
                                        v-model="projectQuery.createTime"
                                        type="daterange"
                                        align="right"
                                        unlink-panels
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        format="yyyy 年 MM 月 dd 日"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>


                        <el-col :span="8">
                            <el-form-item label="结算月份">
                                <el-date-picker
                                        v-model="projectQuery.settlementTime"
                                        type="month"
                                        placeholder="选择月"
                                        value-format="yyyy-MM-dd">

                                </el-date-picker>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6" style="text-align:right">
                            <el-form-item>
                                <el-button type="primary" style="width:30%" @click="query()">搜索</el-button>
                                <el-button type="success" style="width:30%" @click="exportList">导出Excel</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div style="text-align:right">{{theader}}</div>
            <el-table :data="data" border class="table" ref="multipleTable">
                <el-table-column
                        prop="projectNo"
                        label="项目编号"
                        width="90"
                ></el-table-column>
                <el-table-column
                        prop="name"
                        label="项目名称"
                        width="170"
                ></el-table-column>
                <el-table-column
                        prop="employee.realName"
                        label="负责人"
                        width="90"
                ></el-table-column>
                <el-table-column
                        prop="cycleStartTime"
                        label="项目周期"
                        width="190"
                ></el-table-column>
                <el-table-column
                        prop="capital"
                        label="项目资金"
                        width="90"
                ></el-table-column>
                <el-table-column
                        prop=""
                        label="项目预算"
                        width="90">
                    <template slot-scope="scope">
                        <span v-if="scope.row.budgetAmount+scope.row.expenditure<0" style="color: red">{{scope.row.budgetAmount}}</span>
                        <span v-else>{{scope.row.budgetAmount}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="income"
                        label="项目收益"
                        width="90"
                ></el-table-column>
                <el-table-column
                        prop="expenditure"
                        label="项目支出"
                        width="90"
                ></el-table-column>
                <el-table-column
                        prop="createTime"
                        label="立项时间"
                        width="100"
                ></el-table-column>
                <el-table-column
                        label="操作">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="getBudget(scope.row.id,scope.row.budgetAmount,scope.row.name)"
                                   type="primary">预算</el-button>
                        <el-button size="mini" @click="reward(scope.row.id)" type="success">奖惩</el-button>
                        <el-button size="mini" @click="edit(scope.row.id)" type="success">编辑</el-button>
                        <el-button size="mini" @click="update(scope.row.id)" type="danger">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="10"/>
            </div>
        </div>
        <!--项目添加-->
        <Modal v-model="saveModal" class="Modal" :width="screenWidth" title="添加" :mask-closable="false">
            <div class="addBody">

                <project-choose v-if="saveModal" @init-choose="initChooseProject"
                                @close-modal="closeCurrModal"></project-choose>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <!--字典添加-->
        <Modal v-model="dictionaryModal" class="Modal" :width="width" title="添加" :mask-closable="false">
            <div class="addBody">

                <project-dictionary-choose v-if="dictionaryModal" @init-choose="initChooseProject"
                                           @close-modal="closeCurrModal"></project-dictionary-choose>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <!--项目预算-->
        <Modal v-model="budgetModal" class="Modal" :width="budgetWidth" title="项目预算" :mask-closable="false"
               @on-cancel="onCancel">
            <div class="addBody">

                <project-budget-choose v-if="budgetModal" :budgetList="budgetList" @init-choose="initChooseProject"
                                       @close-modal="closeCurrModal"></project-budget-choose>
            </div>
            <div slot="footer">
            </div>
        </Modal>

        <!--编辑-->
        <Modal v-model="editModal" class="Modal" :width="width" title="项目编辑" :mask-closable="false">
            <div class="addBody">

                <project-update-choose v-if="editModal" :projectId="projectId" @init-choose="initChooseProject"
                                       @close-modal="closeCurrModal"></project-update-choose>
            </div>
            <div slot="footer">
            </div>
        </Modal>

        <!--奖惩规则-->
        <Modal v-model="rewardModal" class="Modal" :width="screenWidth"  :projectId="projectId"
               title="项目编辑"
               :mask-closable="false">
            <div class="addBody">

                <project-rule-choose v-if="rewardModal" :projectId="projectId" @init-choose="initChooseProject"
                                       @close-modal="closeCurrModal"></project-rule-choose>
            </div>
            <div slot="footer">
            </div>
        </Modal>
    </div>
</template>

<script>
    import projectChoose from '@/components/page/project/choose/projectChoose.vue'
    import projectUpdateChoose from '@/components/page/project/choose/projectUpdateChoose.vue'
    import projectDictionaryChoose from '@/components/page/project/choose/projectDictionaryChoose.vue'
    import projectBudgetChoose from '@/components/page/project/choose/projectBudgetChoose.vue'
    import projectRuleChoose from '@/components/page/project/choose/projectRuleChoose.vue'
    import {formatDate} from '@/components/common/utils.js'

    export default {
        data() {
            return {
                budgetList: null,
                employees: [],
                tableData: [],
                pageSizeOpts: [10, 20, 30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                theader: "",
                screenWidth: '45%',//新增对话框 宽度
                width: '45%',//新增对话框 宽度
                budgetWidth: '45%',//新增对话框 宽度
                saveModal: false,
                dictionaryModal: false,
                budgetModal: false,
                editModal: false,
                rewardModal:false,
                projectId:null,
                registerTime: [],
                lastLoginTime: [],
                projectQuery: {
                    projectNo: null,
                    employeeId: null,
                    name: null,
                    createTime: null,
                    createTime1: null,
                    cycleStartTime: null,
                    cycleEndTime: null,
                    settlementTime: null,
                    SettlementTime1: null,
                    pageSize: "",
                    pageIndex: "",
                    employeeName: null
                },
                projectUpdate: {
                    id: null,
                    state: 0
                }
            };
        },
        components: {
            'projectChoose': projectChoose,
            "projectDictionaryChoose": projectDictionaryChoose,
            "projectBudgetChoose": projectBudgetChoose,
            "projectUpdateChoose":projectUpdateChoose,
            "projectRuleChoose":projectRuleChoose
        },
        created() {
            this.getData();
            this.getByEmployees();
        },
        computed: {
            data() {
                return this.tableData.filter(d => {
                    d.cycleStartTime = d.cycleStartTime.substring(0, 10) + " - " + d.cycleEndTime.substring(0, 10);
                    d.createTime = d.createTime.substring(0, 10);
                    for (let i = 0; i < d.incomeExpends.length; i++) {
                        if (d.incomeExpends[i].type == 1) {
                            d.income += d.incomeExpends[i].money;
                        } else {
                            d.expenditure += d.incomeExpends[i].money;
                        }
                    }
                    for (let i = 0; i < d.awardPunishes.length; i++) {
                        if (d.awardPunishes[i].type == 1) {
                            d.income += d.awardPunishes[i].money;
                        } else {
                            d.expenditure += d.awardPunishes[i].money;
                        }
                    }
                    if(d.income!=null){
                        d.income= parseFloat(d.income).toFixed(2);
                    }
                    if(d.expenditure!=null){
                        d.expenditure= parseFloat(d.expenditure).toFixed(2);
                    }

                    return d;
                });
            }
        },
        methods: {
            getData() {
                this.$postData("project_getByList", this.projectQuery, {}).then(res => {
                    if (res.meta.state == 200) {

                        this.tableData = res.data.tbody.records;
                        this.pageInfo.current = res.data.tbody.current;
                        this.pageInfo.pages = res.data.tbody.pages;
                        this.pageInfo.size = res.data.tbody.size;
                        this.pageInfo.total = res.data.tbody.total
                        this.income(this.tableData);
                        this.projectQuery.createTime = null;
                        this.projectQuery.createTime1 = null;
                    } else {
                        this.$message.error("查询失败，" + res.meta.msg);
                    }
                })

            },
            getByEmployees() {
                this.$postData("project_getEmployees", {}, {}).then(res => {
                    if (res.status == 200) {

                        this.employees = res.data
                    } else {
                        this.$message.error("查询失败，" + res.meta.msg);
                    }
                })
            },
            query() {
                console.log(this.projectQuery);
                if (this.projectQuery.createTime != null) {
                    this.projectQuery.createTime1 = this.projectQuery.createTime[1];
                    this.projectQuery.createTime = this.projectQuery.createTime[0];

                }
                this.projectQuery.pageIndex=1;
                this.getData();
            },
            edit(id) {
                this.projectId = id;
                this.editModal = true;
            },
            exportList() {
                if (this.projectQuery.createTime != null) {
                    this.projectQuery.createTime1 = this.projectQuery.createTime[1];
                    this.projectQuery.createTime = this.projectQuery.createTime[0];
                }
                this.$postData("project_export", this.projectQuery, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tablename: "项目管理",
                        res: res
                    });
                });
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
            reward(id){
                this.projectId = id;
                this.rewardModal = true;
            },
            /*
            * 计算总支出收入
            * */
            income(data) {
                let income = 0;
                let expend = 0;
                if (data) {
                    for (let i = 0; i < data.length; i++) {
                        let res = data[i];
                        if (res.incomeExpends) {
                            for (let j = 0; j < res.incomeExpends.length; j++) {
                                let flag = res.incomeExpends[j];
                                if (flag.type == 1) {
                                    income += flag.money;
                                } else if (flag.type == 2) {
                                    expend += flag.money;
                                }
                            }
                        }
                    }
                    for (let i = 0; i < data.length; i++) {
                        let res = data[i];
                        if (res.awardPunishes) {
                            for (let j = 0; j < res.awardPunishes.length; j++) {
                                let flag = res.awardPunishes[j];
                                if (flag.type == 1) {
                                    income += flag.money;
                                } else if (flag.type == 2) {
                                    expend += flag.money;
                                }
                            }
                        }
                    }
                    income= parseFloat(income).toFixed(2);
                    expend= parseFloat(expend).toFixed(2);
                    this.theader = "项目总收益: " + income + " 项目总支出:" + expend;
                }

            },
            // 页码大小
            onPageSizeChange(size) {
                this.projectQuery.pageSize = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                console.log(index)
                this.projectQuery.pageIndex = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
                this.dictionaryModal = false;
                this.budgetModal = false
                this.editModal = false
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
                this.getByEmployees();
            },
            update(id) {
                this.$confirm('此操作将永久性删除, 是否继续?', '提示', {
                    confirmButtonText: '删除',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.projectUpdate.id = id
                    this.$postData("project_update", this.projectUpdate, {}).then(res => {
                        if (res.status == 200) {
                            this.$message.success("删除成功");
                            this.getData();
                        } else {
                            this.$message.error("删除失败" + res.msg);
                        }
                    })

                }).catch(() => {

                });
            },
            getBudget(id, amount, name) {
                let list = {
                    projectId: id,
                    amount: amount,
                    projectName: name
                }
                this.budgetList = list;
                this.budgetModal = true;
            },
            onCancel() {
                this.getData();
            },
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
