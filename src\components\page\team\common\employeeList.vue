<template>
    <div>
        <el-input v-model="realName" placeholder="请输入名称" @input="querySearch"  suffix-icon="el-icon-search"></el-input>
        <el-divider></el-divider>
        <div v-for="(item,index) in options" :key="index" class="employeeBox">
            <el-avatar size="small"> {{item.name}} </el-avatar>
            <span style="position: relative;
    top: -9px;
    left: 10px;">{{item.realName}}</span>
        </div>
    </div>
</template>

<script>
    export default {
        props:{
            selectList: {
                type: Array,
                default: ()=>[]
            },
        },
        name: "employeeList",
        data() {
            return {
                options:[],
                realName:null,
                loading:false,
                employeeList:[],

            };
        },
        created(){
            this.getEmployee()
        },
        methods: {
            querySearch(queryString) {
                var restaurants = this.employeeList;
                var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
                // 调用 callback 返回建议列表的数据
                // cb(results);
                this.options=results
            },
            createFilter(queryString) {
                return (restaurant) => {

                    return (restaurant.realName.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                };
            },
            chooseThisModel() {
                this.$emit('init-choose', this.selectList);
            },
            getEmployee(){
                this.loading=true
                this.$getData("getEmployeeSelectDto", {}).then(res => {
                    if (res.status == 200) {
                        this.employeeList =  res.data;
                        this.options =  this.employeeList;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                    this.loading=false
                })
            },
            handleChange(value, direction, movedKeys) {
                console.log(value);
                console.log( direction);
            }
        }
    }
</script>

<style scoped>
    .el-avatar{
        background: #2d8cf0;
        font-size: 12px;
    }
    .employeeBox{
        margin-left: 10px;
        padding: 5px;
    }
</style>
