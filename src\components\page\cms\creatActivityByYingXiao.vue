<template>
  <div>
    <el-alert
        title="提示：填写时请确认填写信息，若要关闭活动请在编辑时把时间调整到今日日期前即可,编辑时若没有传图片则按原图传;"
        type="warning"
        effect="dark">
    </el-alert>
    <el-alert
        title="普通助力：链接分享给好友并且并且好友点击即可。企微助力：需要扫码添加企微才算助力成功"
        type="warning"
        effect="dark">
    </el-alert>
    <divider></divider>
    <el-form  :model="ruleForm" ref="ruleForm" label-width="170px" class="demo-ruleForm" :inline="false">
      <el-form-item label="活动名称" required >
        <el-col :span="4">
          <el-input v-model="ruleForm.activity.activityName"></el-input>
        </el-col>

        <el-form-item label="活动时间" required>
          <el-col :span="9">
            <el-form-item prop="date1">
              <el-date-picker type="datetimerange" placeholder="选择日期" value-format="yyyy-MM-dd HH:mm:ss" v-model="date1" style="width: 100%;"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-form-item>
      </el-form-item>


        <el-form-item label="活动奖品名称"  required>
          <el-col :span="4">
            <el-input v-model="ruleForm.activityGift.winGradeName"></el-input>
          </el-col>

          <el-form-item label="优惠券批次号"  required>
            <el-col :span="6">
              <el-input v-model="ruleForm.activityGift.couponId" onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>
            </el-col>
          </el-form-item >
        </el-form-item>

      <el-form-item label="活动奖品库存"  required>
        <el-col :span="4">
          <el-input-number v-model="ruleForm.activityGift.max" :min="1" :max="999999" label="描述文字"></el-input-number>
<!--          <el-input v-model="ruleForm.activityGift.max" onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>-->
        </el-col>
        <el-form-item label="邀请助力次数"  required>
          <el-col :span="4">
            <el-input-number v-model="ruleForm.activity.count" :min="1" :max="999999" label="描述文字"></el-input-number>
<!--            <el-input v-model.number="ruleForm.activity.count" onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>-->
          </el-col>
        </el-form-item>

      </el-form-item>

            <el-form-item label="活动类别" required>
              <el-col :span="6">
                <el-switch v-model="ruleForm.activity.activityIsQiWeiType" active-text="企微活码助力" inactive-text="普通助力" :active-value="1" :inactive-value="0"/>
              </el-col>
            </el-form-item>
<!--    <el-form-item v-if="ruleForm.activity.activityIsQiWeiType == 1" required>-->
<!--      <label class="el-form-item__label" v-if="ruleForm.activity.activityIsQiWeiType == 1">选择活码：</label>-->
<!--      <el-col :span="8" >-->
        <el-form-item label="客户标签" required  v-if="ruleForm.activity.activityIsQiWeiType == 1">
          <ul>
            <li class="tags-li" v-for="(item,index) in qiyeState.label" :key="index">
              <span id="">
                {{ item }}
              </span>
              <span class="tags-li-icon" @click="closeTags(index)"><i class="el-icon-close"></i></span>
            </li>
          </ul>
          <el-button type="primary" @click="dialogVisibleTag = true" plain icon="el-icon-plus">添加标签</el-button>
        </el-form-item>
        <el-form-item label="部门" required  v-if="ruleForm.activity.activityIsQiWeiType == 1">
          <el-select v-model="dom.party" value-key="id" filterable clearable @change="getPartyUser" placeholder="使用部门">
            <el-option
                v-for="item in partys"
                :key="item.id"
                :label="item.name"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

      <el-form-item label="人员" v-if="type!==2 && ruleForm.activity.activityIsQiWeiType == 1" required>
        <el-select v-model="dom.user" filterable multiple placeholder="使用人员" @change="changeSele">
          <el-option v-for="item in users"
                     :key="item.id"
                     :label="item.name"
                     :value="item.userid">
          </el-option>
        </el-select>
      </el-form-item>

<!--      </el-col>-->


<!--    </el-form-item>-->


      <el-link type="primary" @click="showImgDetail">点我查看活动图片区域说明</el-link>
      <el-dialog :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="">
        <img width="100%" :src="dialogImageUrl1" alt="">
        <img width="100%" :src="dialogImageUrl2" alt="">
      </el-dialog>
<!--        <el-form-item label="活动背景图"  required>-->
<!--          <activity-pic-content ref="zlbg"></activity-pic-content>-->
<!--        </el-form-item>-->

        <el-form-item label="活动背景顶部图"  required>
          <activity-pic-content ref="zlbgtop"></activity-pic-content>
        </el-form-item>

<!--      <el-form-item label="活动背景底部图"  required>-->
<!--        <activity-pic-content ref="zlbgbotton"></activity-pic-content>-->
<!--      </el-form-item>-->

<!--        <el-form-item label="已邀助力展示图"  required>-->
<!--        <activity-pic-content ref="zlbgcon"></activity-pic-content>-->
<!--        </el-form-item>-->


<!--        <el-form-item label="背景中部图(助力按钮区域)"  required>-->
<!--          <activity-pic-content ref="zlbgcon2"></activity-pic-content>-->
<!--        </el-form-item>-->

        <el-form-item label="活动规则图"  required>
          <activity-pic-content ref="zlgz"></activity-pic-content>
        </el-form-item>

        <el-form-item label="为好友助力成功展示图"  required>
          <activity-pic-content ref="zlhysuccess"></activity-pic-content>
        </el-form-item>

        <el-form-item label="助力成功展示图"  required >
          <activity-pic-content ref="zlsuccess"></activity-pic-content>
        </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
        <el-button @click="resetForm('ruleForm')">重置</el-button>
      </el-form-item>



      <el-dialog title="选择标签" :visible.sync="dialogVisibleTag" width="550px" append-to-body>
        <div v-for="item in qiyeLabelGroup">
          <el-form-item :label="item.group_name">
            <el-checkbox-group v-model="qiyeState.label" value-key="id">
              <el-checkbox-button v-for="(val,index) in qiyeLabel" v-show="val.qiyeLabelGroupId===item.id"
                                  :key="index"
                                  :label="val.name"
                                  @change="change(val.id)">{{ val.name }}
              </el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
        </div>
        <div style="margin-top: 150px;">
          <el-row type="flex" justify="center">
            <el-col :span="3">
              <el-button @click="dialogVisibleTag = false" size="large">取 消</el-button>
            </el-col>
            <el-col :span="3"></el-col>
            <el-col :span="3">
              <el-button type="success" size="large" @click="dialogVisibleTag = false">确定</el-button>
            </el-col>
          </el-row>
        </div>
      </el-dialog>

    </el-form>
  </div>
</template>

<script>
import moment from "moment";

const dataMap = {
  props: {},
  Set: function (key, value) {
    this.props[key] = value
  },
};

import activityPicContent from "./activityPicContent.vue";
export default {
  name: "creatActivityByYingXiao",
  components:{
    activityPicContent
  },
  created() {
    if (this.$route.query.id !=null && this.$route.query.id !== '' && this.$route.query.id !== undefined){
      this.ruleForm.activity.id = this.$route.query.id;
      this.getData(this.$route.query.id);
    }
    this.getParty();
    this.getQiyeLabel();
    this.getQiyeLabelGroup();

  },
  data() {
    return {
      dialogImageUrl: '',
      dialogImageUrl1: '',
      dialogImageUrl2: '',
      date1:[],
      optionsQW:[],
      type: null,
      dialogVisible:false,
      dialogVisibleTag:false,
      //部门
      partys: [],
      //人员
      users: [],
      ruleForm: {
        activityTypeName:'zl',
        activityType:1,
        activity:{
          id:null,
          activityName:'',
          count:'',
          startTime:'',
          endTime:'',
          type:0,
          activityIsQiWeiType:0,
        },
        activityGift: {
          max:'',
          winGradeName:'',
          detail:'助力活动',
          couponId:'',
          type:0,
        },
        activityQiWeiInfo:{
          user:null,
          userId: null,
          label: null,
          labelId:null,

        },
        dataMap :{},
      },
      qiyeState: {
        label: [],
        labelId: [],
      },
      //标签组
      qiyeLabelGroup: {
        group_id: [],
        group_name: [],
      },
      //标签
      qiyeLabel: {
        id: [],
        name: [],
        qiyeLabelGroupId: [],
      },
      dom: {
        name: null,
        //单人、多人
        type: null,
        //使用该联系方式的部门id列表，只在type为2时有效
        party: null,
        //用该联系方式的用户userID列表，在type为1时为必填，且只能有一个
        user: null,
        //1-在小程序中联系，2-通过二维码联系
        userName: [],
        partyName: [],

      },
    };
  },

  methods: {
    // 删除单个标签
    closeTags(index) {
      this.qiyeState.label.splice(index, 1)[0];
      this.qiyeState.labelId.splice(index, 1)[0];
    },
    submitForm(formName) {

      if (this.date1 !=null && this.date1.length > 0){
        this.ruleForm.activity.startTime = this.format(new Date(this.date1[0]), "yyyy-MM-dd hh:mm:ss");
        this.ruleForm.activity.endTime = this.format(new Date(this.date1[1]), "yyyy-MM-dd hh:mm:ss");
      } else {
        this.$message.error("时间不能为空")
      }

      dataMap.Set("zl_hy_success",this.$refs.zlhysuccess.pic);
      dataMap.Set("zl_bg_top",this.$refs.zlbgtop.pic);
      dataMap.Set("zl_gz",this.$refs.zlgz.pic);
      dataMap.Set("zl_success",this.$refs.zlsuccess.pic);
      // dataMap.Set("zl_bg_bottom",this.$refs.zlbgbotton.pic);
      // dataMap.Set("zl_bg",this.$refs.zlbg.pic);
      // dataMap.Set("zl_bg_con",this.$refs.zlbgcon.pic);
      // dataMap.Set("zl_bg_con2",this.$refs.zlbgcon2.pic);

      this.ruleForm.dataMap = dataMap.props;

      let d = this.ruleForm;
      if (d.activity.activityName === '' || d.activity.count === '' ||
          d.activityGift.max === '' || d.activityGift.winGradeName === ''
          || d.activityGift.couponId === '' ){
        return this.$message.error("请将信息补充完整")
      }


      if (this.ruleForm.activity.activityIsQiWeiType === 1){
        if (this.qiyeState.label == null || this.qiyeState.label == ''
            || this.qiyeState.labelId == '' || this.qiyeState.labelId == ''){
          return this.$message.error("请选择标签")
        } else if (this.dom.user == null || this.dom.user == '' ||
            this.dom.userName == null || this.dom.userName == ''){
          return this.$message.error("请选择人员")
        }

        this.ruleForm.activityQiWeiInfo.label = this.qiyeState.label;
        this.ruleForm.activityQiWeiInfo.labelId = this.qiyeState.labelId;
        this.ruleForm.activityQiWeiInfo.user = this.dom.user;
        this.ruleForm.activityQiWeiInfo.userName = this.dom.userName;

        this.ruleForm.activityQiWeiInfo.label = this.qiyeState.label.join(",");
        this.ruleForm.activityQiWeiInfo.labelId = this.qiyeState.labelId.join(",");
        this.ruleForm.activityQiWeiInfo.userName = this.dom.userName.join(",");
        this.ruleForm.activityQiWeiInfo.userId = this.dom.user.join(",");
      }




      console.info(JSON.stringify(this.ruleForm))
      this.$postData("createActivityByYX", this.ruleForm).then(res => {

        if (res.status === 200) {
          this.$message.success("创建成功");
          this.$router.push("/activityPageByYingXiao");
        } else {
          this.$message.error(res.data);
        }
      })

    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    getData(val){
      this.$getData("initFriendlyData", {"id":val} ).then(res => {

        if (res.status === 200) {
          this.ruleForm.activity = res.data.activity;
          this.ruleForm.activityGift = res.data.activityGift;
          this.$refs.zlhysuccess.createImg(res.data.dataMap.zl_hy_success);
          this.$refs.zlbgtop.createImg(res.data.dataMap.zl_bg_top);
          this.$refs.zlgz.createImg(res.data.dataMap.zl_gz);
          this.$refs.zlsuccess.createImg(res.data.dataMap.zl_success);
          // this.$refs.zlbgbotton.createImg(res.data.dataMap.zl_bg_bottom);
          // this.$refs.zlbgcon.createImg(res.data.dataMap.zl_bg_con);
          // this.$refs.zlbgcon2.createImg(res.data.dataMap.zl_bg_con2);
          this.date1 =[new Date(res.data.activity.startTime),new Date(res.data.activity.endTime)] ;
          // this.$refs.zlbg.createImg(res.data.dataMap.zl_bg);
          // this.dom.user = (res.data.activityQiWeiInfo.user || "").split(',');
          // this.dom.userName = (res.data.activityQiWeiInfo.userName|| "").split(',');
          this.qiyeState.labelId =( res.data.activityQiWeiInfo.labelId|| "").split(',');
          this.qiyeState.label = (res.data.activityQiWeiInfo.label|| "").split(',');

        } else {
          this.$message.error(res.data);
        }
      })
    },
    showImgDetail(){
      this.dialogImageUrl = 'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/qiye1636940139488%E8%AF%B4%E6%98%8E.png';
      this.dialogImageUrl1 = 'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/qiye1637119597484zl-20.png';
      this.dialogImageUrl2 = 'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/qiye1637198777178zl.png';
      this.dialogVisible = true;
      //
    },
    //标签
    change(id) {
      if (this.qiyeState.labelId.indexOf(id) >= 0) {
        this.qiyeState.labelId.splice(this.qiyeState.labelId.indexOf(id), 1);
      } else {
        this.qiyeState.labelId.push(id);
      }
    },
//标签
    getQiyeLabel() {
      this.$getData("getQiyeLabel",).then(res => {
        if (res.status === 200) {
          this.qiyeLabel = res.data
        }
      })
    },
    //标签组
    getQiyeLabelGroup() {
      this.$getData("getQiyeLabelGroup",).then(res => {
        if (res.status === 200) {
          this.qiyeLabelGroup = res.data
        }
      })
    },
    //标签数据初始化
    getQiyeChannelQiyeState(id) {
      this.$getData("getQiyeChannelQiyeState", {id: id}).then(res => {
        if (res.status === 200) {
          this.qiyeState = res.data
          this.dom = res.data
        }
      })
    },
    //人员
    getPartyUser(id) {
      this.partys.find((v) => {
        if (v.id === id) {
          this.dom.partyName = v.name
        }
      });
      this.$getData("getPartyUser", {id: id}).then(res => {
        if (res.status === 200) {
          this.users = res.data
        }
      })
    },
    changeSele(val) {
      this.dom.userName = [];
      val.forEach(e => {
        this.users.find((v) => {
          if (v.userid === e) {
            this.dom.userName.push(v.name);
          }
        });
      })
    },
    //部门
    getParty() {
      this.$getData("getParty",).then(res => {
        if (res.status === 200) {
          this.partys = res.data
        }
      })
    },

    format(DateTime, fmt) {
      var o = {
        "M+": DateTime.getMonth() + 1, //月份
        "d+": DateTime.getDate(), //日
        "h+": DateTime.getHours(), //小时
        "m+": DateTime.getMinutes(), //分
        "s+": DateTime.getSeconds(), //秒
        "q+": Math.floor((DateTime.getMonth() + 3) / 3), //季度
        "S": DateTime.getMilliseconds() //毫秒
      };
      if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (DateTime.getFullYear() + "").substr(4 - RegExp.$1.length));
      for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
      return fmt;
    },
  }
}
</script>

<style scoped>

</style>