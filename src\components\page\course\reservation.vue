<template>
	<div class="container">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">课程预约列表</el-menu-item>
		</el-menu>

		<!-- 搜索栏目 -->
		<div v-if="choiceIndexNow==0">
			<div class="handle-box" style="margin: 20px 20px;">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="预约标题、内容"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="课程预约类型" style="margin-right: 20px">
								<el-select v-model="searchType" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="课程预约状态" style="margin-right: 20px">
								<el-select v-model="searchState" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(0,0)">添加
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">

				<el-table-column prop="id" width="60" label="编号">
				</el-table-column>

				<el-table-column width="140" prop="reservationTitle" label="预约标题">
				</el-table-column>

				<el-table-column width="140" prop="reservationContent" label="课程预约内容">
				</el-table-column>

				<el-table-column width="140" prop="reservationAddress" label="授课地址">
				</el-table-column>
				<el-table-column width="122" prop="videoType" label="课程预约类型" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-tag v-if="item.id == scope.row.reservationType" v-for="(item,index) in typeList"
							:key="index" :type="typeStyleList[index%4].type">{{item.typeName}}</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="125" prop="reservationImg" label="课程预约封面" :render-header="renderHeader">
					<template slot-scope="scope">
						<img :src="scope.row.reservationImg||blankImg" style="width: 100px;height: 100px;"
							@click="openImg(scope.row.reservationImg||blankImg)">
					</template>
				</el-table-column>

				<el-table-column width="125" prop="recordNum" label="提交人数" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.recordNum}}/{{scope.row.collectLimit}}</span>
					</template>
				</el-table-column>

				<el-table-column width="122" label="关联课程" prop="courseId">
					<template slot-scope="scope">
						<span v-if="scope.row.courseId==item.id" v-for="(item,index) in courseList" :key="index">
							{{item.courseTitle}}
						</span>
						<span v-if="!scope.row.courseId">
							暂未设置
						</span>
					</template>
				</el-table-column>

				<el-table-column width="122" label="关联导师" prop="teacherId">
					<template slot-scope="scope">
						<span v-if="scope.row.teacherId==item.id" v-for="(item,index) in teacherList" :key="index">
							{{item.realName}}
						</span>
						<span v-if="!scope.row.teacherId">
							暂未设置
						</span>
					</template>
				</el-table-column>

				<el-table-column width="122" label="关联问卷" prop="surveyId">
					<template slot-scope="scope">
						<span v-if="scope.row.surveyId==item.id" v-for="(item,index) in surveyList" :key="index">
							{{item.surveyTitle}}
						</span>
						<span v-if="!scope.row.surveyId">
							暂未设置
						</span>
					</template>
				</el-table-column>

				<el-table-column width="102" label="状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.reservationState == 0" type="info">下架</el-tag>
						<el-tag v-if="scope.row.reservationState == 1" type="success">上架</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creator" label="创建人">
					<template slot-scope="scope">
						<span v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
						<span v-else>{{scope.row.creator||''}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="200" label="操作">
					<template slot-scope="scope">
						<el-button @click="openModal(1,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
						<el-button @click="openModal(5,scope.$index)" type="warning" size="small" icon="el-icon-s-order"
							style="margin-left: 10px">
							记录
						</el-button>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--添加课程预约-->
		<el-drawer size="60%" :with-header="false" :visible.sync="courseReservationModal" direction="rtl">
			<div v-if="choiceItem">
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">课程预约信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 预约标题：">
								<el-input v-model="choiceItem.reservationTitle" type="textarea"
									class="handle-input mr10" placeholder="请输入课程预约标题"
									:autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="* 预约内容：">
								<el-input v-model="choiceItem.reservationContent" type="textarea"
									class="handle-input mr10" placeholder="请输入预约内容" disabled>
								</el-input>
								<el-button type="primary" @click="toOpenContentText" plain
									icon="el-icon-edit">编辑</el-button>
								<el-tooltip class="item" effect="dark" content="支持图文形式" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="* 授课地址：">
								<el-input v-model="choiceItem.reservationAddress" type="textarea"
									class="handle-input mr10" placeholder="请输入课程授课地址"
									:autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="预约封面：">
								<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
									list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="choiceItem.reservationImg||blankImg" style="width: 150px;height: 150px;"
										@click="openImgUpload(0,0)">
								</el-upload>
							</el-form-item>
						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="人数限制：">
								<el-input v-model="choiceItem.collectLimit" type="number" class="handle-input mr10"
									placeholder="请输入人数限制">
								</el-input>
								<el-tooltip class="item" effect="dark" content="填写人数达到限制后预约入口关闭" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>
							<el-form-item label="关联课程：">
								<el-select v-model="choiceItem.courseId" placeholder="请选择" filterable>
									<el-option v-for="(item,index) in courseList" :key="index"
										:label="item.id+'：'+item.courseTitle" :value="item.id"></el-option>
								</el-select>
								<el-button type="success" icon="el-icon-circle-plus-outline" @click="openPage(0)"
									style="margin-left: 20px">添加
								</el-button>
								<el-tooltip class="item" effect="dark" content="在课程预约下方显示相关课程，并可在详情页通过点击卡片查看课程详情"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="关联导师：">
								<el-select v-model="choiceItem.teacherId" placeholder="请选择" filterable clearable>
									<el-option v-for="(item,index) in teacherList" :key="index"
										:label="item.id+'：'+item.realName" :value="item.id"></el-option>
								</el-select>
								<el-button type="success" icon="el-icon-circle-plus-outline" @click="openPage(1)"
									style="margin-left: 20px">添加
								</el-button>
								<el-tooltip class="item" effect="dark" content="在课程预约详情页，通过点击头像查看导师详情并添加其微信"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="关联问卷：">
								<el-select v-model="choiceItem.surveyId" placeholder="请选择" filterable>
									<el-option v-for="(item,index) in surveyList" :key="index"
										:label="item.id+'：'+item.surveyTitle" :value="item.id"></el-option>
								</el-select>
								<el-button type="success" icon="el-icon-circle-plus-outline" @click="openPage(2)"
									style="margin-left: 20px">添加
								</el-button>
								<el-tooltip class="item" effect="dark" content="在课程预约时，除了基本个人信息之外，需要填写的自定义问卷"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="预约类型：">
								<el-select v-model="choiceItem.reservationType" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="可根据类型进行分类" placement="top-start">
									<i class="el-icon-group" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item width="250" label="开始日期：">
								<el-date-picker v-model="choiceItem.startTime" type="datetime"
									value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00']"
									placeholder="请选择预约开始日期" :picker-options="pickerOptions">
								</el-date-picker>
							</el-form-item>

							<el-form-item width="250" label="结束日期：">
								<el-date-picker v-model="choiceItem.endTime" type="datetime"
									value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00']"
									placeholder="请选择预约结束日期" :picker-options="pickerOptions">
								</el-date-picker>
							</el-form-item>

							<el-form-item label="活动状态：">
								<el-select v-model="choiceItem.reservationState" placeholder="请选择"
									style="width: 100px;">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="预约入口：" v-if="detailType==1">
								<el-tag @click="openEntry(0)">入口二维码</el-tag>
								<el-tooltip class="item" effect="dark" content="可保存二维码发送给学员，或扫码进入小程序后点右上角分享给学员"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="courseReservationModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="insertCourseReservation()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateCourseReservation(choiceItem)" type="success" size="small"
						v-if="detailType==1">保存
					</el-button>
				</div>

			</div>
		</el-drawer>

		<!--问卷调查记录-->
		<el-drawer size="60%" :with-header="false" :visible.sync="recordModal" direction="rtl">
			<div class="handle-box" style="margin: 20px 20px;">
				<h2>预约填写记录{{signInTips}}</h2>
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="searchText" placeholder="请输入填写人、门店名称等"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="9">
							<el-form-item label="筛选时间">
								<div style="width: 180px;display: block;">
									<el-date-picker v-model="searchTime" type="datetimerange" format="yyyy-MM-dd"
										:picker-options="pickerOptions1" range-separator="至" start-placeholder="开始日期"
										end-placeholder="结束日期" align="right" value-format="yyyy-MM-dd HH:mm:ss">
									</el-date-picker>
								</div>
							</el-form-item>
						</el-col>
						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="listCourseReservationRecord()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh"
								@click="searchTime=[];searchText='';listCourseReservationRecord()">重置
							</el-button>

							<!-- 	<el-button type="danger" @click="recordDownload()">导出</el-button> -->
						</el-col>
					</el-row>
				</el-form>

				<el-table :data="recordList" v-loading="loading" border
					:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
					:expand-row-keys="expands" @expand-change="expandSelect">

					<el-table-column type="expand">
						<el-table :data="surveyAnswerRecordList" v-loading="loading"
							style="width: 1800px;margin-top: 20px;" border
							:header-cell-style="{background:'#f8f8f9',fontWeight:400,color:'#000000'}">
							<el-table-column prop="id" width="80" label="编号" sortable>
							</el-table-column>

							<el-table-column prop="questionTitle" width="120" label="问题">
							</el-table-column>

							<el-table-column prop="answerContent" width="120" label="回答">
								<template slot-scope="scope">
									<span v-if="scope.row.answerType!=4">{{scope.row.answerContent||'-'}}</span>
									<img :src="scope.row.answerContent||blankImg" style="width: 50px;height: 50px;"
										@click="openImg(scope.row.answerContent||blankImg)" v-else />
								</template>
							</el-table-column>
						</el-table>
					</el-table-column>

					<el-table-column prop="id" width="80" label="编号" sortable>
					</el-table-column>

					<el-table-column width="140" prop="name" label="姓名">
					</el-table-column>

					<el-table-column width="140" prop="phone" label="手机号">
					</el-table-column>

					<el-table-column width="100" prop="sex" label="性别">
					</el-table-column>
					<el-table-column width="140" prop="idCard" label="身份证号">
						<template slot-scope="scope">
							<span>{{scope.row.idCard||'-'}}</span>
						</template>
					</el-table-column>

					<el-table-column prop="age" label="年龄">
					</el-table-column>

					<el-table-column prop="married" label="婚姻状况">
						<template slot-scope="scope">
							<span>{{scope.row.married||'-'}}</span>
						</template>
					</el-table-column>
					<el-table-column prop="address" label="所在地">
						<template slot-scope="scope">
							<span>{{scope.row.address||'-'}}</span>
						</template>
					</el-table-column>
					<el-table-column prop="career" label="职业">
						<template slot-scope="scope">
							<span>{{scope.row.career||'-'}}</span>
						</template>
					</el-table-column>

					<el-table-column width="160" prop="createTime" label="填写时间" sortable>
					</el-table-column>
				</el-table>

				<div style="margin-top: 20px">
					<el-button type="danger" @click="recordModal=false">关闭</el-button>
				</div>
			</div>
		</el-drawer>

		<el-dialog title="预约内容详情" :visible.sync="openContentText" width="80%">
			<div style="display: flex">
				<el-button @click="toUpdateContent" type="success" icon="Check">确定
				</el-button>
				<el-button @click="openContentText=false">取消
				</el-button>
			</div>
			<Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig"
				mode="default" />
			<Editor style="height: 500px; overflow-y: hidden;" v-model="contentText" :defaultConfig="editorConfig"
				mode="default" @onCreated="onCreated" />
		</el-dialog>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--视频预览-->
		<el-dialog :visible.sync="videoModal" width="40%" title="视频预览" :mask-closable="false">
			<video style="width: 100%;height: auto;margin: 0 auto;" :controls="true" :enable-progress-gesture="true"
				:initial-time="0" :show-center-play-btn="true" autoplay :src="videoUrl" custom-cache="false">
			</video>
		</el-dialog>

		<!-- 入口二维码 -->
		<el-dialog :visible.sync="entryModal" width="30%" title="预约活动入口" :mask-closable="false">
			<img :src="entryUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>
	</div>

</template>

<script>
	import Vue from 'vue';
	import {
		Editor,
		Toolbar
	} from '@wangeditor/editor-for-vue'
	export default {
		name: "courseReservation",
		components: {
			Editor,
			Toolbar
		},
		data() {
			return {
				// 可设置
				// 是否超级管理员（可执行删除等敏感操作）
				isAdmin: false,

				url: '',
				imageUrl: '',
				videoUrl: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				// 文件上传地址
				fileUploadUrl: 'https://api.xiaoyujia.com/system/uploadFile',
				fileUploadData: {
					route: 'courseReservation'
				},
				uploadFileType: 0,

				editor: null,
				toolbarConfig: {},
				editorConfig: {
					placeholder: "请输入内容...",
					readOnly: false,
					MENU_CONF: {
						//上传参考https://www.wangeditor.com/v5/menu-config.html#%E5%9F%BA%E6%9C%AC%E9%85%8D%E7%BD%AE
						uploadImage: {
							server: `https://api.xiaoyujia.com/system/imageUpload`,
							// 超时时间，默认为 10 秒
							timeout: 30 * 1000, // 5s
							fieldName: "file",
							// 将 meta 拼接到 url 参数中，默认 false
							metaWithUrl: false, // join params to url
							// 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
							meta: {
								watermark: 1,
							},
							// 自定义增加 http  header
							// headers: {
							//   Accept: "text/x-json",
							//   otherKey: "xxx"
							// },
							// 跨域是否传递 cookie ，默认为 false
							withCredentials: true,
							// 自定义增加 http  header
							// headers: {
							//   Accept: "text/x-json",
							//   otherKey: "xxx"
							// },
							maxFileSize: 10 * 1024 * 1024, // 10M
							base64LimitSize: 5 * 1024, // insert base64 format, if file's size less than 5kb
							// 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
							allowedFileTypes: ["image/*"],
							// 最多可上传几个文件，默认为 100
							maxNumberOfFiles: 30,
							onBeforeUpload(file) {
								console.log("onBeforeUpload", file);
								return file; // will upload this file
								// return false // prevent upload
							},
							onProgress(progress) {
								console.log("onProgress", progress);
							},
							onSuccess(file, res) {
								console.log("onSuccess", file, res);
							},
							onFailed(file, res) {
								alert(res.message);
								console.log("onFailed", file, res);
							},
							onError(file, err, res) {
								alert(err.message);
								console.error("onError", file, err, res);
							},
							// 自定义插入图片
							customInsert(res, insertFn) {
								insertFn(res.data, '消息图片', '消息图片')
							},
						},
						uploadVideo: {
							server: "https://api.xiaoyujia.com/system/uploadFile",
							fieldName: "file",
							// 单个文件的最大体积限制，默认为 10M
							maxFileSize: 100 * 1024 * 1024, // 100M
							// 最多可上传几个文件，默认为 5
							maxNumberOfFiles: 5,
							// 选择文件时的类型限制，默认为 ['video/*'] 。如不想限制，则设置为 []
							allowedFileTypes: ["video/*"],
							// 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
							meta: {

							},
							// 将 meta 拼接到 url 参数中，默认 false
							metaWithUrl: false,
							// 自定义增加 http  header
							// headers: {
							//   Accept: "text/x-json",
							//   otherKey: "xxx"
							// },
							// 跨域是否传递 cookie ，默认为 false
							withCredentials: true,
							// 超时时间，默认为 30 秒
							timeout: 30 * 1000, // 15 秒
							// 上传进度的回调函数
							onProgress(progress) {
								console.log("progress", progress);
							},
							// 单个文件上传成功之后
							onSuccess(file, res) {
								console.log(`${file.name} 上传成功`, res);
							},
							// 单个文件上传失败
							onFailed(file, res) {
								console.log(`${file.name} 上传失败`, res);
							},
							// 上传错误，或者触发 timeout 超时
							onError(file, err, res) {
								console.log(`${file.name} 上传出错`, err, res);
							},
							// 自定义插入视频
							customInsert(res, insertFn) {
								insertFn(res.data, '消息视频', '消息视频')
							},
						}
					}
				},

				// baseUrl: 'http://**************:9999/',
				baseUrl: 'https://api.xiaoyujia.com/',
				uploadUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
				deleteTips: "确定删除选中课程预约吗？该操作无法恢复，请谨慎操作!",
				isEdit: false,
				showEdit: true,
				openContentText: false,
				contentText: '',
				entryUrl: false,
				entryModal: false,
				imgModal: false,
				videoModal: false,
				courseReservationModal: false,
				recordModal: false,
				keyListModal: false,
				authIdListModal: false,
				signInTips: '',
				detailType: 0,
				loading: true,
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				pageSizeOpts: [20, 50, 100],
				list: [],
				courseList: [],
				teacherList: [],
				liveRoomList: [],
				surveyList: [],
				groupList: [],
				recordList: [],
				surveyAnswerRecordList: [],
				choiceRecordId: 0,
				keyList: [],
				choiceIndex: '0',
				choiceIndexNow: '0',
				choiceItemIndex: 0,
				choiceItem: null,
				choiceRecordId: 0,
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				signInTips: '',
				searchText: '',
				searchType: null,
				searchState: null,
				searchRequired: null,
				quer: {
					search: "",
					groupIdList: null,
					videoState: null,
					typeState: null,
					orderBy: "t.id ASC",
					current: 1,
					size: 20
				},
				switchStateList: [{
						id: 0,
						typeName: "关闭",
						type: "info"
					},
					{
						id: 1,
						typeName: "开启",
						type: "success"
					}
				],
				typeList: [{
					id: 0,
					typeName: "默认类型"
				}],
				stateList: [{
					value: 0,
					text: "下架",
					type: "info"
				}, {
					value: 1,
					text: "上架",
					type: "success"
				}],
				typeStyleList: [{
						id: 0,
						type: "success"
					},
					{
						id: 1,
						type: "info"
					},
					{
						id: 2,
						type: "warning"
					}, {
						id: 3,
						type: "primary"
					}
				],
				authList: [{
					id: 1,
					name: '普通会员'
				}, {
					id: 2,
					name: '行政员工'
				}, {
					id: 3,
					name: '服务员工'
				}, {
					id: 4,
					name: '保姆月嫂'
				}, {
					id: 5,
					name: '共创店'
				}, {
					id: 6,
					name: '合伙人'
				}, {
					id: 7,
					name: '门店店长'
				}, {
					id: 8,
					name: '陪跑店店长'
				}],
				searchTime: [],
				pickerOptions: {
					shortcuts: [{
						text: '昨天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '今天',
						onClick(picker) {
							picker.$emit('pick', new Date());
						}
					}, {
						text: '明天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周前',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周后',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}]
				},
				pickerOptions1: {
					shortcuts: [{
						text: '今天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '昨天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}]
				}
			}
		},
		created() {
			if (this.$route.query.isAdmin) {
				this.isAdmin = true
			}
			this.getData()
		},
		methods: {
			onCreated(editor) {
				this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
			},
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceItem = null
				this.choiceIndex = key
				this.expands = []
				this.showEdit = true
				this.reset()
			},
			getData() {
				this.choiceIndexNow = 2
				if (this.choiceIndex == 0) {
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'course/pageCourseReservation',
						data: JSON.stringify(this.quer),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							this.loading = false
							if (res.code == 0) {
								this.list = res.data.records
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
								this.choiceIndexNow = this.choiceIndex

							} else {
								this.list = []
								this.$message.error('未查询到相关课程预约!')
								this.choiceIndexNow = this.choiceIndex
							}
						},
					})
					this.listCourse()
					this.listCourseTeacher()
					this.listSurvey()
				} else if (this.choiceIndex == 1) {
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'course/pageCourseReservationGroup',
						data: JSON.stringify(this.quer),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							this.loading = false
							if (res.code == 0) {
								this.list = res.data.records
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
								this.choiceIndexNow = this.choiceIndex
								for (let item of this.list) {
									this.$set(item, "authListArray", this.strToArray(item.authList))
								}
							} else {
								this.list = []
								this.$message.error('未查询到相关分组!')
								this.choiceIndexNow = this.choiceIndex
							}
						},
					})
				}
				this.getDict()
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.quer.reservationState = this.searchState
				this.quer.reservationType = this.searchType
				this.getData()
			},
			// 重置
			reset() {
				this.loading = true
				this.quer.search = ""
				this.quer.reservationState = null
				this.quer.reservationType = null

				this.quer.orderBy = "t.id ASC"
				this.quer.current = 1

				this.searchText = ''
				this.searchState = null
				this.searchType = null
				this.isEdit = false
				this.getData()
			},
			// 获取课程列表
			listCourse() {
				this.$postData("listCourse", {}).then(res => {
					if (res.status == 200) {
						this.courseList = res.data
					}
				})
			},
			// 获取导师列表
			listCourseTeacher() {
				$.ajax({
					type: "POST",
					url: 'https://api.xiaoyujia.com/course/listCourseTeacher',
					data: JSON.stringify({
						state: null
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.teacherList = res.data
						}
					},
				})
			},
			// 获取导师列表
			listSurvey() {
				$.ajax({
					type: "POST",
					url: 'https://biapi.xiaoyujia.com/survey/pageSurvey',
					data: JSON.stringify({
						current: 1,
						size: 9999
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.status == 200) {
							this.surveyList = res.data.records
						}
					},
				})
			},
			getDict() {
				this.$postUrl("get_dict", 250, null, {}).then(res => {
					if (res.status == 200) {
						this.keyList = res.data
					} else {
						this.keyList = []
					}
				})
			},
			listCourseReservationRecord() {
				let quer = {
					reservationId: this.list[this.choiceItemIndex].id,
					search: this.searchText,
					startTime: null,
					endTime: null,
				}
				// 处理一下时间筛选字段
				if (this.searchTime && this.searchTime.length >= 2) {
					quer.startTime = this.searchTime[0]
					quer.endTime = this.searchTime[1]
				} else {
					quer.startTime = null
					quer.endTime = null
				}
				$.ajax({
					type: "POST",
					url: 'https://api.xiaoyujia.com/course/listCourseReservationRecord',
					data: JSON.stringify(quer),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.recordList = res.data
							let total = this.list[this.choiceItemIndex].collectLimit
							let count = this.recordList.length
							let rate = (count / total * 100).toFixed(2) + '%'
							this.signInTips = '（实际报名/报名人数：' + count + '/' + total + '，报名进度：' + rate + '）'
						} else {
							this.recordList = []
							this.$message.error('未查询到相关预约记录!')
						}
					},
				})
			},
			// 获取问卷记录回答详情
			listSurveyAnswerRecord(id) {
				this.$getUrl("listSurveyAnswerRecord", this.choiceRecordId).then(res => {
					if (res.status == 200) {
						this.surveyAnswerRecordList = res.data
					} else {
						this.surveyAnswerRecordList = []
						this.$message.error('未查询到问卷回答!')
					}
					this.expands.push(id)
				})
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						this.showEdit = false
						this.isEdit = false
						this.choiceRecordId = row.surveyRecordId
						this.listSurveyAnswerRecord(row.id)
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "分组图片") {
					tips = "推荐尺寸：250*250"
				} else if (column.label == "分组图标") {
					tips = "推荐尺寸：250*250"
				} else if (column.label == "课程预约类型") {
					tips = "可根据类型进行分类"
				} else if (column.label == "关键词") {
					tips = "课程预约话题"
				} else if (column.label == "查看权限") {
					tips = "设置哪类人员可以查看该课程预约，未设置则所有人可见"
				} else if (column.label == "课程预约封面") {
					tips = "作为静态展示的素材"
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			formatDuration(duration, defaultStr) {
				if (!defaultStr) {
					defaultStr = '暂未录入'
				}
				let result = ""
				let sencond = 0
				let min = 0
				let hour = 0
				let day = 0
				// 小于一分钟
				if (duration <= 60) {
					sencond = Math.floor(parseFloat(duration / 1.0))
					if (sencond == 0) {
						result = defaultStr
					} else {
						result = sencond + "秒"
					}
				}

				// 小于二小时
				if (duration > 60 && duration <= 3600) {
					min = Math.floor(parseFloat(duration / 60.0))
					sencond = Math.floor(duration - (min * 60)).toFixed(0)
					if (min == 0) {
						result = defaultStr
					} else {
						result = min + "分钟" + sencond + "秒"
					}
				}
				// 大于二小时 小于一天
				if (duration > 3600 && duration <= 86400) {
					hour = Math.floor(parseFloat(duration / 3600))
					min = parseFloat((duration - (hour * 3600)) / 60).toFixed(0)
					if (min == 0) {
						result = hour + "小时"
					} else {
						result = hour + "小时" + min + "分钟"
					}
				}
				// 大于一天
				else if (duration > 86400) {
					day = Math.floor(parseFloat(duration / 86400))
					hour = parseFloat((duration - (day * 86400)) / 3600).toFixed(0)
					if (hour == 0) {
						result = day + "天"
					} else {
						result = day + "天" + hour + "小时"
					}
				}
				return result
			},
			formatTypeStyle(type) {
				return this.typeStyleList[type % 4].type
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 课程预约Excel模板下载
			excelDownload() {
				this.$postData("excelDownload", null, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('课程预约Excel模板下载成功!')
					this.blobExport({
						tablename: "课程预约Excel模板",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.$set(this.choiceItem, "reservationImg", res.data)
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadId = index
				let tips = ''
				if (this.uploadImgType == 0) {
					tips = "推荐尺寸：750*1000（3：4）"
				} else if (this.uploadImgType == 1) {
					tips = '推荐上传尺寸：250*250（1：1）'
				} else if (this.uploadImgType == 2) {
					tips = '推荐上传尺寸：250*250（1：1）'
				}
				this.$message.info(tips)
			},
			// 打开图片预览
			openImg(url) {
				this.imageUrl = url || this.blankImg
				this.imgModal = true
			},
			// 打开视频预览
			openVideo(url) {
				if (url != null && url != '') {
					this.videoUrl = url
				}
				this.videoModal = true
			},
			// 跳转页面
			openPage(value) {
				let path = '/courseList'
				if (value == 1) {
					path = '/courseTeacher'
				} else if (value == 2) {
					path = '/survey'
				}
				window.open(path, '_blank')
			},
			openModal(index, index1) {
				if (index == 0) {
					this.detailType = 0
					this.choiceItem = {}
					this.choiceItem.authListArray = []
					this.choiceItem.keyListArray = []
					this.courseReservationModal = true
				} else if (index == 1) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.courseReservationModal = true
				} else if (index == 5) {
					this.choiceItemIndex = index1
					this.listCourseReservationRecord()
					this.recordModal = true
				}
			},
			openEntry(value, index) {
				let id = this.choiceItem.id
				if (value == 0) {
					$.ajax({
						type: "POST",
						url: 'https://biapi.xiaoyujia.com/image/getWxShareImg',
						data: JSON.stringify({
							textList: [{
								"text": this.choiceItem.roomTitle,
								"fontSize": 50,
								"isCenter": true,
								"color": "0xfed472",
								"isBold": true,
								"x": 0,
								"y": 1020
							}],
							qrCodeStyle: {
								"width": 350,
								"height": 350,
								"x": 200,
								"y": 250
							},
							maxWidth: 600,
							img: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/QR_img/QR_examEntry.png',
							path: 'pages-other/reservation/index',
							scene: "id/" + id,
							source: "xyjCourse",
							type: 1
						}),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.status == 200) {
								this.entryUrl = res.data
								this.entryModal = true
							} else {
								this.$message.error(res.msg)
							}
						},
						error: res => {
							this.$message.error('当前暂无回放录播哦！')
						}
					})

				}
			},
			//打开员工信息详情
			rowClick(id) {

			},
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			// 添加课程权限列表
			addAuthIdList() {
				this.authIdListModal = false
			},
			// 删除课程预约权限标签
			closeAuthTags(index, index1) {
				this.choiceItem.authListArray.splice(index1, 1)
			},
			// 添加课程预约
			insertCourseReservation() {
				let courseReservation = this.choiceItem
				if (!courseReservation.reservationTitle) {
					this.$message.error('请填写课程预约标题！')
				} else if (!courseReservation.reservationContent) {
					this.$message.error('请填写课程预约内容！')
				} else if (!courseReservation.reservationAddress) {
					this.$message.error('请填写授课地址！')
				} else {
					let no = localStorage.getItem('account') || 'admin'
					this.$set(courseReservation, "creator", no)
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'course/insertCourseReservation',
						data: JSON.stringify(courseReservation),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.code == 0) {
								this.$message.success('课程预约添加成功!')
								this.courseReservationModal = false
								this.list.push(res.data)
							} else {
								this.$message.error('课程预约添加失败！' + res.msg)
							}
						},
					})
				}
			},
			// 更改课程预约
			updateCourseReservation(val) {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'course/updateCourseReservation',
					data: JSON.stringify(val),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.$message.success('课程预约更新成功!')
							this.list[this.choiceItemIndex] = this.choiceItem
						} else {
							this.$message.error('课程预约更新失败！' + res.msg)
						}
					},
				})
			},
			// 更改课程预约分组
			updateCourseReservationGroup(val) {
				// 格式化权限角色列表
				val.authList = val.authListArray.join(',')
				if (val.authList.length == 0) {
					val.authList = '0'
				}

				$.ajax({
					type: "POST",
					url: this.baseUrl + 'course/updateCourseReservationGroup',
					data: JSON.stringify(val),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.$message.success('课程预约分组更新成功!')
							this.list[this.choiceItemIndex] = this.choiceItem
						} else {
							this.$message.error('课程预约分组更新失败！' + res.msg)
						}
					},
				})
			},
			beforeFileUpload() {
				this.loading = true
			},
			// 章节素材文件上传成功
			fileUploadSuccess(res, file) {
				this.loading = false
				if (this.uploadFileType == 0) {
					this.choiceItem.videoUrl = res.data
				}
				this.$message.success('课程预约文件上传成功！')
			},
			// 章节素材文件上传失败
			fileUploadError(err) {
				this.loading = false
				this.$message.error('课程预约文件上传失败！请重试！' + err.msg)
			},
			// 课程章节素材文件上传
			fileUpload(value) {
				this.uploadFileType = value
				this.fileUploadUrl = fileUploadUrl
			},
			beforeImgUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!');
				}
				return isLt2M;
			},
			toOpenContentText(index) {
				this.contentText = this.choiceItem.reservationContent || ''
				this.openContentText = true
			},
			toUpdateContent() {
				if (this.contentText == '<p><br></p>') {
					this.contentText = ''
				}
				this.$set(this.list[this.choiceItemIndex],
					'reservationContent', this.contentText || '')
				this.$set(this.choiceItem,
					'reservationContent', this.contentText || '')
				this.openContentText = false
			},

		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>

<style src="@wangeditor/editor/dist/css/style.css"></style>