<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col span="24">
                    <div>
                        <div id="operBtnDiv">
                            <div id="searchDiv">
                                <Form ref="formItem" :model="formItem" :label-width="50" label-position="right">
                                    <Row>
                                        <Col v-if="isAdd" :span="6">
                                            <FormItem label="区间">
                                                <Input placeholder="区间" v-model="formItem.section">
                                                </Input>
                                            </FormItem>
                                        </Col>
                                        <div v-if="isAdd" style="float: left">
                                            <span>--</span>
                                            <Input placeholder="区间" v-model="formItem.section1" style="width: 100px">
                                            </Input>
                                        </div>
                                        <Col v-if="isAdd" :span="6">
                                            <FormItem label="百分百">
                                                <Input placeholder="百分百" v-model="formItem.proportion">
                                                </Input>
                                            </FormItem>
                                        </Col>

                                        <div v-if="isAdd" style="float: left">
                                            <span style="line-height: 3;">%</span>
                                        </div>

                                        <Col style="text-align:right" :span="addSpan">
                                            <el-button v-if="isAdd" @click="save">确定</el-button>
                                            <el-button v-if="isAdd" @click="cancel">取消</el-button>
                                            <el-button type="success" v-if="!isAdd" @click="addBudget">新增规则</el-button>
                                        </Col>
                                    </Row>


                                </Form>
                                <el-table :data="data" border class="table" ref="multipleTable">
                                    <el-table-column
                                            prop=""
                                            label="序号"
                                            width="55">
                                        <template slot-scope="scope">
                                            <span>{{scope.$index+1}}</span>

                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                            prop="section"
                                            label="区间"
                                            width="210"
                                    ></el-table-column>
                                    <el-table-column
                                            prop="proportion"
                                            label="百分比"
                                            width="150"
                                    ></el-table-column>
                                    <el-table-column
                                            label="操作"
                                            width="200">
                                        <template slot-scope="scope">
                                            <!--  <el-button size="mini" @click="update(scope.row.id)" type="primary">编辑
                                              </el-button>-->
                                            <el-button size="mini" @click="deleteRule(scope.row.id)" type="danger">删除
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <div class="pagination">
                                    <Page :total="pageInfo.total"
                                          @on-change="onChange"
                                          :show-total="true"
                                          :show-sizer="true"
                                          :page-size-opts="pageSizeOpts"
                                          @on-page-size-change="onPageSizeChange"
                                          :page-size="pageInfo.size"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
    </div>
</template>


<script>

    export default {
        props: ['projectId'],
        data() {
            return {
                amount: null,
                isAdd: false,
                addSpan: 24,
                data: null,
                pageSizeOpts: [5, 10, 15],
                pageInfo: {total: 0, size: 5, current: 1, pages: 1},
                formItem: {
                    projectId: this.projectId,
                    section: null,
                    section1: null,
                    proportion: null,
                    pageSize: 5,
                    pageNum: 1
                },
                list: null,

            }
        },
        components: {},
        created: function () {
            this.getData();
        },
        methods: {
            getData() {
                this.$postData("projectRule_getList", this.formItem, {}).then(res => {
                    if (res.status == 200) {
                        this.data = res.data.list;
                        for (let i = 0; i < this.data.length; i++) {
                            let item = this.data[i];
                            if (item.proportion) {
                                item.proportion = item.proportion * 100 + "%"
                            }
                        }
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("获取失败，" + res.msg);
                    }
                })
            },
            deleteRule(id, amount) {
                this.$confirm('此操作将永久性删除, 是否继续?', '提示', {
                    confirmButtonText: '删除',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$getData("projectRule_delete", {id: id}).then(res => {
                        if (res.status == 200) {
                            this.$message.success("删除成功");
                            this.getData();
                        } else {
                            this.$message.error("删除失败" + res.msg);
                        }
                    })
                }).catch(() => {

                });
            },
            save() {
                if (this.formItem.section != null && this.formItem.section1 != null && this.formItem.proportion != null) {
                    let list={
                        section:this.formItem.section,
                        section1:this.formItem.section1
                    }
                    this.$postData("projectRule_getList",list, {}).then(res => {
                        if (res.status == 200) {

                            if (res.data.list.length>0) {
                                this.$message.error("该区间已经存在，" + res.msg);
                            }else {
                                this.$postData("projectRule_save", this.formItem, {}).then(res => {
                                    if (res.status == 200) {
                                        this.$Message.success('添加成功');
                                        this.cancel();
                                        this.getData()
                                        this.formItem.proportion = null;
                                        this.formItem.section = null;
                                        this.formItem.section1 = null;
                                    } else {
                                        this.$message.error("添加失败，" + res.msg);
                                    }
                                })
                            }
                        }
                    })
                } else {
                    this.$Message.success('请补全新增信息');
                }


            },
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.formItem.pageSize = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                console.log(index)
                this.formItem.pageNum = index;
                this.getData();
            },
            addBudget() {
                this.isAdd = true
                this.addSpan = 6
            },
            cancel() {
                this.isAdd = false;
                this.addSpan = 24;
            },
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        /* padding-left: 20px;*/
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

