<template>
	<div class="container">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">考试管理</el-menu-item>
			<el-menu-item index="1">题库管理</el-menu-item>
			<el-menu-item index="2">试题管理</el-menu-item>
			<el-menu-item index="3">数据统计</el-menu-item>
		</el-menu>

		<!-- 考试管理 -->
		<div v-if="choiceIndexNow==0">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="考试标题、内容、备注、题库名称等"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="考试类型" style="margin-right: 20px">
								<el-select v-model="searchExamType">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in examTypeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="题库分类" style="margin-right: 20px">
								<el-select v-model="searchLib" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in libList" :key="index" :label="item.name"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="考试状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateExamList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(0,0)">添加
							</el-button>
						</el-col>
					</el-row>

				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<!-- 展开列-试题表格 -->
				<el-table-column type="expand" @expand-change="expandSelect">
					<template slot-scope="scope">

						<el-col :span="6" style="margin: 0 0 20px 0">
							<h2>考试详情</h2>
						</el-col>

						<el-col :span="10" style="margin: 0 320px 20px 0;">
							<el-button @click="isEdit=true" type="success" size="small" :disabled="isEdit"
								v-if="!isEdit" icon="el-icon-edit">修改
							</el-button>
							<el-button @click="updateUnionExam(scope.row)" type="primary" size="small" v-if="isEdit"
								icon="el-icon-circle-check">保存
							</el-button>
							<el-button @click="openRank(scope.row)" type="primary" size="small"
								icon="el-icon-trophy">排行榜
							</el-button>
						</el-col>

						<!-- 试题详细内容 -->
						<el-col :span="32" style="margin: 0 0 20px 0;float: left;">
							单选题数量：
							<div style="width:140px;height: 50px;display: inline-block;">
								<span @click="rowClick(scope.row.id)"
									v-if="!isEdit">{{scope.row.singleQuestionCount}}</span>

								<div v-if="isEdit">
									<el-input v-model="scope.row.singleQuestionCount" style="width: 80%;"
										placeholder="单选题数量"
										onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
									</el-input> 道
								</div>
							</div>

							多选题数量：
							<div style="width:140px;height: 50px;display: inline-block;">
								<span @click="rowClick(scope.row.id)"
									v-if="!isEdit">{{scope.row.multipleQuestionCount}}</span>

								<div v-if="isEdit">
									<el-input v-model="scope.row.multipleQuestionCount" style="width: 80%;"
										placeholder="多选题数量"
										onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
									</el-input> 道
								</div>
							</div>

							判断题数量：
							<div style="width:140px;height: 50px;display: inline-block;">
								<span @click="rowClick(scope.row.id)"
									v-if="!isEdit">{{scope.row.judgementQuestionCount}}</span>

								<div v-if="isEdit">
									<el-input v-model="scope.row.judgementQuestionCount" style="width: 80%;"
										placeholder="判断题数量"
										onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
									</el-input> 道
								</div>
							</div>

							填空题数量：
							<div style="width:140px;height: 50px;display: inline-block;">
								<span @click="rowClick(scope.row.id)"
									v-if="!isEdit">{{scope.row.fillQuestionCount}}</span>

								<div v-if="isEdit">
									<el-input v-model="scope.row.fillQuestionCount" style="width: 80%;"
										placeholder="填空题数量"
										onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
									</el-input> 道
								</div>
							</div>

							问答题数量：
							<div style="width:140px;height: 50px;display: inline-block;">
								<span @click="rowClick(scope.row.id)"
									v-if="!isEdit">{{scope.row.openQuestionCount}}</span>

								<div v-if="isEdit">
									<el-input v-model="scope.row.openQuestionCount" style="width: 80%;"
										placeholder="问答题数量"
										onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
									</el-input> 道
								</div>
							</div>

						</el-col>

					</template>
				</el-table-column>

				<el-table-column prop="id" width="80" label="编号" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)">{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="examTitle" label="考试标题">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="!scope.row.isEdit">{{checkStr(scope.row.examTitle)}}</span>

						<el-input v-model="scope.row.examTitle" style="width: 100%;" type="textarea"
							v-if="scope.row.isEdit" placeholder="请输入考试标题" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column width="180" prop="examContent" label="考试内容">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)" class="column-text"
							v-if="!scope.row.isEdit">{{checkStr(scope.row.examContent)}}</span>

						<el-input v-model="scope.row.examContent" style="width: 100%;" type="textarea"
							v-if="scope.row.isEdit" placeholder="请输入考试内容" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column width="180" prop="examRemark" label="考试说明">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)" class="column-text"
							v-if="!scope.row.isEdit">{{checkStr(scope.row.examRemark)}}</span>

						<el-input v-model="scope.row.examRemark" style="width: 100%;" type="textarea"
							v-if="scope.row.isEdit" placeholder="请输入考试说明" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column width="125" prop="examImg" label="考试封面图" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
							:disabled="!scope.row.isEdit" list-type="picture" :on-success="imgUploadSuccess"
							:show-file-list="false">
							<img :src="scope.row.examImg!=null?scope.row.examImg:blankImg"
								style="width: 100px;height: 100px;"
								@click="scope.row.isEdit?openImgUpload(1,scope.$index):openImg(scope.row.examImg)">
						</el-upload>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="examType" label="考试类型" sortable>
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-if="item.id == scope.row.examType" v-for="(item,index) in examTypeList"
								:key="index" :type="formatTypeStyle(0,scope.row)">{{formatType(0,scope.row)}}</el-tag>
						</div>

						<el-select v-model="scope.row.examType" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit" filterable>
							<el-option v-for="(item,index) in examTypeList" :key="index" :label="item.typeName"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="questionLibId" label="题库分类" sortable>
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<span v-if="!scope.row.isEdit">{{formatQuestionLib(scope.row.questionLibId)}}</span>
						</div>

						<el-select v-model="scope.row.questionLibId" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit" filterable>
							<el-option v-for="(item,index) in libList" :key="index" :label="item.name" :value="item.id">
							</el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="180" label="报名权限" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="scope.row.authList!='0'">
							<ul v-for="(item,index) in authList" :key="index">
								<li class="tags-li" v-for="(item1,index1) in scope.row.authListArray" :key="index1"
									v-if="item.id==parseInt(item1)">
									<span>
										{{ item.name }}
									</span>
								</li>
							</ul>
						</div>

						<div v-if="scope.row.authList==0">
							<span>暂未设置</span>
						</div>
					</template>
				</el-table-column>

				<el-table-column width="142" prop="examDuration" label="考试时长" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)" v-if="!scope.row.isEdit">{{scope.row.examDuration}}</span>

						<div v-if="scope.row.isEdit">
							<el-input v-model="scope.row.examDuration" style="width: 70%;" placeholder="单位：分钟"
								onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
							</el-input> 分钟
						</div>
					</template>
				</el-table-column>

				<el-table-column prop="examScore" label="考试满分" width="120" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)" v-if="!scope.row.isEdit">{{scope.row.examScore}}</span>

						<div v-if="scope.row.isEdit">
							<el-input v-model="scope.row.examScore" style="width: 80%;" placeholder="单位：分"
								onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
							</el-input> 分
						</div>
					</template>
				</el-table-column>

				<el-table-column prop="examPassScore" label="通过分数" width="120" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="!scope.row.isEdit">{{scope.row.examPassScore}}</span>

						<div v-if="scope.row.isEdit">
							<el-input v-model="scope.row.examPassScore" style="width: 80%;" placeholder="单位：分"
								onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
							</el-input> 分
						</div>
					</template>
				</el-table-column>


				<el-table-column width="122" label="考试状态">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-if="scope.row.state == 0" type="success">进行中</el-tag>
							<el-tag v-if="scope.row.state == 1" type="info">已结束</el-tag>
						</div>

						<el-select v-model="scope.row.state" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in stateExamList" :key="index" :label="item.text"
								:value="item.value"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="142" label="考试未通过角色" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-if="scope.row.examFailRoleId==item.id" v-for="(item,index) in roleList"
								:key="index">
								{{item.id}}：{{item.name}}
							</el-tag>
							<span v-if="!scope.row.examFailRoleId">暂未录入</span>
						</div>

						<el-select v-model="scope.row.examFailRoleId" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit" filterable>
							<el-option v-for="(item,index) in roleList" :key="index" :label="item.id+'：'+item.name"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="142" label="考试通过后角色" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-if="scope.row.examPassRoleId==item.id" v-for="(item,index) in roleList"
								:key="index">
								{{item.id}}：{{item.name}}
							</el-tag>
							<span v-if="!scope.row.examPassRoleId">暂未录入</span>
						</div>

						<el-select v-model="scope.row.examPassRoleId" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit" filterable>
							<el-option v-for="(item,index) in roleList" :key="index" :label="item.id+'：'+item.name"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="122" label="考试通过后证书">
					<template slot-scope="scope">
						<span v-if="!scope.row.isEdit">{{formatCert(scope.row)}}</span>

						<el-select v-model="scope.row.certId" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit" filterable>
							<el-option v-for="(item,index) in certList" :key="index" :label="item.certCodeFixed"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="130" label="考试通过后佳币" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="!scope.row.isEdit">{{scope.row.examPassJiaBi}}</span>

						<div v-if="scope.row.isEdit">
							<el-input v-model="scope.row.examPassJiaBi" style="width: 80%;" placeholder="单位：个，限加盟商员工"
								onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
							</el-input> 个
						</div>
					</template>
				</el-table-column>

				<el-table-column width="130" label="最大考试次数" :render-header="renderHeader">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="!scope.row.isEdit">{{scope.row.maxMakeUpCount}}</span>

						<div v-if="scope.row.isEdit">
							<el-input v-model="scope.row.maxMakeUpCount" style="width: 80%;" placeholder="单位：次（默认：3）"
								onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
							</el-input> 次
						</div>
					</template>
				</el-table-column>

				<el-table-column width="122" label="随机试题" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-for="(item,index) in switchStateList" :key="index"
								v-if="scope.row.isRandomQuestion == item.id" :type="item.type">{{item.typeName}}
							</el-tag>
						</div>

						<el-select v-model="scope.row.isRandomQuestion" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit" filterable>
							<el-option v-for="(item,index) in switchStateList" :key="index" :label="item.typeName"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="122" label="选项乱序" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-for="(item,index) in switchStateList" :key="index"
								v-if="scope.row.isShuffled == item.id" :type="item.type">{{item.typeName}}</el-tag>
						</div>

						<el-select v-model="scope.row.isShuffled" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in switchStateList" :key="index" :label="item.typeName"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="142" label="排行榜显示真名" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-for="(item,index) in switchStateList" :key="index"
								v-if="scope.row.showRealName == item.id" :type="item.type">{{item.typeName}}</el-tag>
						</div>

						<el-select v-model="scope.row.showRealName" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in switchStateList" :key="index" :label="item.typeName"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="250" prop="examStarted" label="开始日期" sortable>
					<template slot-scope="scope">
						<span v-if="!scope.row.isEdit" @click="rowClick(scope.row.id)">{{scope.row.examStarted}}</span>

						<el-date-picker v-model="scope.row.examStarted" type="datetime"
							value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00']" v-if="scope.row.isEdit"
							placeholder="请选择考试开始日期" :picker-options="pickerOptions">
						</el-date-picker>
					</template>
				</el-table-column>

				<el-table-column width="250" prop="examEnded" label="结束日期" sortable>
					<template slot-scope="scope">
						<span v-if="!scope.row.isEdit" @click="rowClick(scope.row.id)">{{scope.row.examEnded}}</span>

						<el-date-picker v-model="scope.row.examEnded" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
							:default-time="['00:00:00']" v-if="scope.row.isEdit" placeholder="请选择考试结束日期"
							:picker-options="pickerOptions">
						</el-date-picker>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="examCreTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)">{{scope.row.examCreTime}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="updater" label="更新人">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="scope.row.updaterName!=null">{{scope.row.updaterName}}({{scope.row.updater}})</span>
						<span @click="rowClick(scope.row.id)"
							v-if="scope.row.updaterName==null">{{checkStr(scope.row.updater)}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
					<template slot-scope="scope">
						<el-button @click="openDetail(scope.$index,0)" type="success" size="small"
							icon="el-icon-edit">修改
						</el-button>
						<!-- 						<el-button @click="openEdit(scope.row)" type="success" size="small" :disabled="scope.row.isEdit"
							v-if="!scope.row.isEdit" icon="el-icon-edit">修改
						</el-button> -->
						<el-button @click="updateUnionExam(scope.row)" type="primary" size="small"
							v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
						</el-button>
						<el-popconfirm title="确定删除该考试吗？删除后无法恢复，请谨慎操作!" @confirm="deleteUnionExam(scope.row)">
							<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
								:disabled="!scope.row.isEdit" icon="el-icon-delete">删除
							</el-button>
						</el-popconfirm>

					</template>
				</el-table-column>

			</el-table>

		</div>

		<!-- 题库管理 -->
		<div v-if="choiceIndexNow==1">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="题库标题、内容等"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="题库类型" style="margin-right: 20px">
								<el-select v-model="searchLibType">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in examTypeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="题库状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(1,0)">添加
							</el-button>
						</el-col>
					</el-row>

				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column prop="id" width="80" label="编号" sortable>
				</el-table-column>

				<el-table-column width="140" prop="name" label="题库标题">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="!scope.row.isEdit">{{checkStr(scope.row.name)}}</span>

						<el-input v-model="scope.row.name" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
							placeholder="请输入题库标题" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column width="180" prop="content" label="题库简介">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)" class="column-text"
							v-if="!scope.row.isEdit">{{checkStr(scope.row.content)}}</span>

						<el-input v-model="scope.row.content" style="width: 100%;" type="textarea"
							v-if="scope.row.isEdit" placeholder="请输入题库简介" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="state" label="题库状态">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-if="scope.row.state == 0" type="info">下架</el-tag>
							<el-tag v-if="scope.row.state == 1" type="success">上架</el-tag>
						</div>

						<el-select v-model="scope.row.state" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
								:value="item.value"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="type" label="题库类型" sortable>
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-if="item.id == scope.row.type" v-for="(item,index) in examTypeList" :key="index"
								:type="formatTypeStyle(1,scope.row)">{{formatType(2,scope.row)}}</el-tag>
						</div>

						<el-select v-model="scope.row.type" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in examTypeList" :key="index" :label="item.typeName"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="allowMock" label="开放模拟考" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-if="scope.row.allowMock == 0" type="info">关闭</el-tag>
							<el-tag v-if="scope.row.allowMock == 1" type="success">开启</el-tag>
						</div>

						<el-select v-model="scope.row.allowMock" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in switchStateList" :key="index" :label="item.typeName"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="libQuestionCount" label="题目数量">
				</el-table-column>

				<el-table-column width="140" prop="creTime" label="创建时间" sortable>
				</el-table-column>

				<el-table-column width="140" prop="updateTime" label="更新时间" sortable>
				</el-table-column>

				<el-table-column width="130" prop="creater" label="创建人">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="scope.row.createrName!=null">{{scope.row.createrName}}({{scope.row.creater}})</span>
						<span @click="rowClick(scope.row.id)"
							v-if="scope.row.createrName==null">{{checkStr(scope.row.creater)}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
					<template slot-scope="scope">
						<el-button @click="openEdit(scope.row)" type="success" size="small" :disabled="scope.row.isEdit"
							v-if="!scope.row.isEdit" icon="el-icon-edit">修改
						</el-button>
						<el-button @click="updateUnionQuestionLib(scope.row)" type="primary" size="small"
							v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
						</el-button>
						<el-popconfirm title="确定删除该题库吗？题库内的试题将会移至默认题库，请谨慎操作!"
							@confirm="deleteUnionQuestionLib(scope.row)">
							<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
								:disabled="!scope.row.isEdit" icon="el-icon-delete">删除
							</el-button>
						</el-popconfirm>

					</template>
				</el-table-column>

			</el-table>
		</div>

		<!-- 试题管理 -->
		<div v-if="choiceIndexNow==2">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="试题标题、备注、题库名称等"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="试题类型" style="margin-right: 20px">
								<el-select v-model="searchQuestionType">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in questionTypeList" :key="index"
										:label="item.typeName" :value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="题库分类" style="margin-right: 20px">
								<el-select v-model="searchLib">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in libList" :key="index" :label="item.name"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="试题状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
									题目数量 </el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button @click="excelDownload()" type="warning" size="small" :disabled="isEdit"
								style="margin-left: 10px" icon="el-icon-download">模板</el-button>

							<el-upload :action="excelUploadUrl" list-type="picture" :on-success="excelUploadSuccess"
								:on-error="excelUploadError" :show-file-list="false" class="upload-demo inline-block">
								<el-button @click="excelImport(0)" type="warning" size="small" :disabled="isEdit"
									style="margin-left: 10px" icon="el-icon-upload2">导入</el-button>
							</el-upload>
						</el-col>
					</el-row>

				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<!-- 展开列-试题表格 -->
				<el-table-column type="expand" @expand-change="expandSelect">
					<template slot-scope="scope">

						<el-col :span="6" style="margin: 0 0 20px 0;float: left;">
							<h2>试题详情</h2>
						</el-col>

						<el-col :span="8" style="margin: 0 320px 20px 0;float: right">
							<el-button @click="isEdit=true" type="success" size="small" :disabled="isEdit"
								v-if="!isEdit" icon="el-icon-edit">修改
							</el-button>
							<el-button
								@click="updateQuestionContent(scope.row.questionContent.choice,scope.row.answerContent)"
								type="primary" size="small" v-if="isEdit" icon="el-icon-circle-check">保存
							</el-button>
							<el-popconfirm :title="deleteTips" @confirm="deleteQuestionChoice()">
								<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
									:disabled="!isEdit" icon="el-icon-delete">删除
								</el-button>
							</el-popconfirm>
						</el-col>

						<!-- 试题详细内容 -->
						<el-col :span="32" style="margin: 0 0 20px 0;float: left;">
							正确答案：
							<div style="width:140px;height: 50px;display: inline-block;">
								<span v-if="!isEdit">{{checkStr(scope.row.answerContent.expect)}}</span>

								<el-input v-model="scope.row.answerContent.expect" style="width: 100%;" type="textarea"
									v-if="isEdit" placeholder="请输入试题正确答案" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</div>

							开放题答案：
							<div style="width:140px;height: 50px;display: inline-block;">
								<span v-if="!isEdit">{{checkStr(scope.row.answerContent.keyword)}}</span>

								<el-input v-model="scope.row.answerContent.keyword" style="width: 100%;" type="textarea"
									v-if="isEdit" placeholder="请输入开放题答案（包含关键词即可得分）"
									:autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</div>

							答案解析：
							<div style="width:140px;height: 50px;display: inline-block;">
								<span v-if="!isEdit">{{checkStr(scope.row.answerContent.analysis)}}</span>

								<el-input v-model="scope.row.answerContent.analysis" style="width: 100%;"
									type="textarea" v-if="isEdit" placeholder="请输入答案解析："
									:autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</div>


							解析链接：
							<div style="width:140px;height: 50px;display: inline-block;">
								<span v-if="!isEdit">{{checkStr(scope.row.answerContent.analysisUrl)}}</span>

								<el-input v-model="scope.row.answerContent.analysisUrl" style="width: 100%;"
									type="textarea" v-if="isEdit" placeholder="请输入解析链接"
									:autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</div>
						</el-col>

						<!-- 选项表格 -->
						<el-table :data="scope.row.questionContent.choice" v-loading="loading"
							style="width: 1280px;margin-top: 20px;" border stripe
							:header-cell-style="{background:'#f8f8f9',fontWeight:400,color:'#000000'}"
							@selection-change="handleSelectionChange"
							v-if="scope.row.questionType==0||scope.row.questionType==1||scope.row.questionType==2">
							<el-table-column type="selection" width="55" v-if="isEdit">
							</el-table-column>

							<el-table-column prop="flag" width="120" label="选项标识" sortable>
								<template slot-scope="scope">
									<span @click="rowClick(scope.row.id)" v-if="!isEdit">{{scope.row.flag}}</span>

									<el-input v-model="scope.row.flag" style="width: 100%;" v-if="isEdit"
										onkeyup="value=value.replace(/[/w]/g,'') "
										onbeforepaste="clipboarddata.setdata('text',clipboarddata.getdata('text').replace(/[^/d]/g,''))"
										placeholder="请输入选项索引">
									</el-input>
								</template>
							</el-table-column>

							<el-table-column prop="content" width="160" label="选项内容" sortable>
								<template slot-scope="scope">
									<span @click="rowClick(scope.row.id)" v-if="!isEdit">{{scope.row.content}}</span>

									<el-input v-model="scope.row.content" style="width: 100%;" type="textarea"
										v-if="isEdit" placeholder="请输入选项内容" :autosize="{ minRows: 4, maxRows: 10}">
									</el-input>
								</template>
							</el-table-column>

							<el-table-column width="125" prop="imgUrl" label="选项图片">
								<template slot-scope="scope">
									<el-upload class="upload-demo"
										action="https://biapi.xiaoyujia.com/files/uploadFiles" :disabled="!isEdit"
										list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
										<img :src="scope.row.imgUrl!=null?scope.row.imgUrl:blankImg"
											style="width: 100px;height: 100px;"
											@click="isEdit?openImgUpload(0,scope.$index):openImg(scope.row.imgUrl)">
									</el-upload>
								</template>
							</el-table-column>
						</el-table>


					</template>
				</el-table-column>

				<el-table-column prop="id" width="80" label="编号" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)">{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="questionTitle" label="试题标题">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="!scope.row.isEdit">{{checkStr(scope.row.questionTitle)}}</span>

						<el-input v-model="scope.row.questionTitle" style="width: 100%;" type="textarea"
							v-if="scope.row.isEdit" placeholder="请输入试题试题" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="questionRemark" label="试题备注">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="!scope.row.isEdit">{{checkStr(scope.row.questionRemark)}}</span>

						<el-input v-model="scope.row.questionRemark" style="width: 100%;" type="textarea"
							v-if="scope.row.isEdit" placeholder="请输入试题备注（将以括号的形式展示给考生）"
							:autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column width="125" prop="questionImg" label="试题图片" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
							:disabled="!scope.row.isEdit" list-type="picture" :on-success="imgUploadSuccess"
							:show-file-list="false">
							<img :src="scope.row.questionImg!=null?scope.row.questionImg:blankImg"
								style="width: 100px;height: 100px;"
								@click="scope.row.isEdit?openImgUpload(3,scope.$index):openImg(scope.row.questionImg)">
						</el-upload>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="questionVideo" label="试题视频" :render-header="renderHeader">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="!scope.row.isEdit">{{checkStr(scope.row.questionVideo)}}</span>

						<el-input v-model="scope.row.questionVideo" style="width: 100%;" type="textarea"
							v-if="scope.row.isEdit" placeholder="请输入试题视频链接" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="questionType" label="试题类型" sortable>
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-if="item.id == scope.row.questionType" v-for="(item,index) in questionTypeList"
								:key="index" :type="item.type">{{formatType(1,scope.row)}}</el-tag>
						</div>

						<el-select v-model="scope.row.questionType" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in questionTypeList" :key="index" :label="item.typeName"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="questionLibId" label="题库分类" sortable>
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<span v-if="!scope.row.isEdit">{{formatQuestionLib(scope.row.questionLibId)}}</span>
						</div>

						<el-select v-model="scope.row.questionLibId" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in libList" :key="index" :label="item.name" :value="item.id">
							</el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column prop="questionScore" label="试题分数" width="120" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="!scope.row.isEdit">{{scope.row.questionScore}}</span>

						<div v-if="scope.row.isEdit">
							<el-input v-model="scope.row.questionScore" style="width: 80%;" placeholder="单位：分"
								onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
							</el-input> 分
						</div>
					</template>
				</el-table-column>


				<el-table-column width="122" label="试题状态">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-if="scope.row.state == 0" type="info">下架</el-tag>
							<el-tag v-if="scope.row.state == 1" type="success">上架</el-tag>
						</div>

						<el-select v-model="scope.row.state" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
								:value="item.value"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="creTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)">{{scope.row.creTime}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="updateTime" label="更新时间" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)">{{scope.row.updateTime}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="updater" label="更新人">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="scope.row.updaterName!=null">{{scope.row.updaterName}}({{scope.row.updater}})</span>
						<span @click="rowClick(scope.row.id)"
							v-if="scope.row.updaterName==null">{{checkStr(scope.row.updater)}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
					<template slot-scope="scope">
						<el-button @click="openEdit(scope.row)" type="success" size="small" :disabled="scope.row.isEdit"
							v-if="!scope.row.isEdit" icon="el-icon-edit">修改
						</el-button>
						<el-button @click="updateUnionQuestion(scope.row)" type="primary" size="small"
							v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
						</el-button>
						<el-popconfirm title="确定删除该试题吗？删除后无法恢复，请谨慎操作!" @confirm="deleteUnionQuestion(scope.row)">
							<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
								:disabled="!scope.row.isEdit" icon="el-icon-delete">删除
							</el-button>
						</el-popconfirm>

					</template>
				</el-table-column>

			</el-table>
		</div>

		<!-- 数据统计 -->
		<div v-if="choiceIndexNow==3">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="请输入考试标题"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="7">
							<el-form-item label="筛选时间" style="margin-right: 20px">
								<div style="width: 180px;display: block;">
									<el-date-picker v-model="searchTime" type="datetimerange" format="yyyy-MM-dd"
										:picker-options="pickerOptions1" range-separator="至" start-placeholder="开始日期"
										end-placeholder="结束日期" align="right" value-format="yyyy-MM-dd HH:mm:ss">
									</el-date-picker>
								</div>
							</el-form-item>
						</el-col>
						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>
						</el-col>
					</el-row>

				</el-form>
			</div>

			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column prop="id" width="80" label="编号" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)">{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="examTitle" label="考试标题">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)">{{scope.row.examTitle}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="examMemberCount" label="考试人数" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)">{{scope.row.examMemberCount||0}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="passedCount" label="通过人数" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)">{{scope.row.passedCount||0}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="makeupMemberCount" label="补考人数" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)">{{scope.row.makeupMemberCount||0}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="makeupCount" label="补考次数" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)">{{scope.row.makeupCount||0}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="passedPercentage" label="通过率" sortable>
					<template slot-scope="scope">
						<span
							@click="rowClick(scope.row.id)">{{parseFloat(scope.row.passedPercentage||0.00).toFixed(2)}}%</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="makeupPercentage" label="补考率" sortable>
					<template slot-scope="scope">
						<span
							@click="rowClick(scope.row.id)">{{parseFloat(scope.row.makeupPercentage||0.00).toFixed(2)}}%</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="makeupPercentage" label="考试明细">
					<template slot-scope="scope">
						<el-button @click="openRecord(scope.$index)" type="warning" size="small"
							style="margin-left: 10px">考试明细</el-button>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<el-dialog :visible.sync="examModal" width="60%" title="添加考试内容" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<h2 class="detail-title">考试内容信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 考试标题：">
								<el-input v-model="exam.examTitle" type="textarea" class="handle-input mr10"
									placeholder="请输入考试标题">
								</el-input>
							</el-form-item>

							<el-form-item label="* 考试内容：">
								<el-input v-model="exam.examContent" type="textarea" class="handle-input mr10"
									placeholder="请输入考试内容">
								</el-input>
							</el-form-item>

							<el-form-item label="考试说明：">
								<el-input v-model="exam.examRemark" type="textarea" class="handle-input mr10"
									placeholder="请输入考试说明">
								</el-input>
							</el-form-item>

							<el-form-item label="考试题库：">
								<el-select v-model="exam.questionLibId" placeholder="请选择" style="width: 100px;"
									filterable>
									<el-option v-for="(item,index) in libList" :key="index" :label="item.name"
										:value="item.id">
									</el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="随机试题：">
								<el-select v-model="exam.isRandomQuestion" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in switchStateList" :key="index"
										:label="item.typeName" :value="item.id"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="选项乱序：">
								<el-select v-model="exam.isShuffled" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in switchStateList" :key="index"
										:label="item.typeName" :value="item.id"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="真名显示：">
								<el-select v-model="exam.showRealName" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in switchStateList" :key="index"
										:label="item.typeName" :value="item.id"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="考试封面：">
								<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
									list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="exam.examImg!=null?exam.examImg:blankImg"
										style="width: 250px;height: 250px;" @click="openImgUpload(2,0)">
								</el-upload>
							</el-form-item>

						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="考试类型：">
								<el-select v-model="exam.examType" placeholder="请选择" style="width: 100px;" filterable>
									<el-option v-for="(item,index) in examTypeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="* 考试时长：">
								<el-input v-model="exam.examDuration" class="handle-input mr10"
									placeholder="时长/分钟（默认：30）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 分钟
							</el-form-item>

							<el-form-item label="考试满分：">
								<el-input v-model="exam.examScore" class="handle-input mr10" placeholder="单位：分（默认：100）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 分
							</el-form-item>

							<el-form-item label="通过分数：">
								<el-input v-model="exam.examPassScore" class="handle-input mr10"
									placeholder="单位：分（默认：60）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 分
							</el-form-item>


							<el-form-item label="通过后角色：">
								<el-select v-model="exam.examPassRoleId" placeholder="请选择" style="width: 100px;"
									filterable>
									<el-option v-for="(item,index) in roleList" :key="index"
										:label="item.id+'：'+item.name" :value="item.id"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="未通过角色：">
								<el-select v-model="exam.examFailRoleId" placeholder="请选择" style="width: 100px;"
									filterable>
									<el-option v-for="(item,index) in roleList" :key="index"
										:label="item.id+'：'+item.name" :value="item.id"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="通过后证书：">
								<el-select v-model="exam.certId" placeholder="请选择" style="width: 100px;" filterable>
									<el-option v-for="(item,index) in certList" :key="index" :label="item.certCodeFixed"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="通过后佳币：">
								<el-input v-model="exam.examPassJiaBi" class="handle-input mr10"
									placeholder="单位：个（默认：0）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 个
							</el-form-item>

							<el-form-item label="补考次数：">
								<el-input v-model="exam.maxMakeUpCount" class="handle-input mr10"
									placeholder="单位：次（默认：3）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 次
							</el-form-item>

							<el-form-item label="应考人数：">
								<el-input v-model="exam.staffCount" class="handle-input mr10" placeholder="单位：人"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 人
							</el-form-item>

							<el-form-item label="单选题数量：">
								<el-input v-model="exam.singleQuestionCount" class="handle-input mr10"
									placeholder="单位：道（默认：0）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 道
							</el-form-item>
							<el-form-item label="多选题数量：">
								<el-input v-model="exam.multipleQuestionCount" class="handle-input mr10"
									placeholder="单位：道（默认：0）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 道
							</el-form-item>
							<el-form-item label="判断题数量：">
								<el-input v-model="exam.judgementQuestionCount" class="handle-input mr10"
									placeholder="单位：道（默认：0）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 道
							</el-form-item>
							<el-form-item label="填空题数量：">
								<el-input v-model="exam.fillQuestionCount" class="handle-input mr10"
									placeholder="单位：道（默认：0）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 道
							</el-form-item>
							<el-form-item label="问答题数量：">
								<el-input v-model="exam.openQuestionCount" class="handle-input mr10"
									placeholder="单位：道（默认：0）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 道
							</el-form-item>


						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="courseContentModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addUnionExam()" type="success" size="small">确定添加
					</el-button>
				</div>

			</div>
		</el-dialog>

		<el-dialog :visible.sync="questionLibModal" width="60%" title="添加题库内容" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<h2 class="detail-title">题库信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 题库标题：">
								<el-input v-model="questionLib.name" type="textarea" class="handle-input mr10"
									placeholder="请输入题库标题">
								</el-input>
							</el-form-item>

							<el-form-item label="* 题库内容：">
								<el-input v-model="questionLib.content" type="textarea" class="handle-input mr10"
									placeholder="请输入题库内容简介">
								</el-input>
							</el-form-item>

						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="题库类型：">
								<el-select v-model="questionLib.type" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in examTypeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>

						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="courseContentModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addUnionQuestionLib()" type="success" size="small">确定添加
					</el-button>
				</div>

			</div>
		</el-dialog>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!-- 考试排行 -->
		<el-dialog :visible.sync="rankModal" width="27%" title="考试排行榜" :mask-closable="false">
			<iframe id="iframeId" :src="rankUrl" frameborder="0" style="width: 400px;height: 800px;" scrolling="auto">
			</iframe>
		</el-dialog>

		<el-dialog :visible.sync="entryModal" width="30%" title="考试报名入口" :mask-closable="false">
			<iframe id="iframeId" :src="entryUrl" frameborder="0" style="width: 400px;height: 800px;" scrolling="auto">
			</iframe>
		</el-dialog>

		<!-- 添加考试报名权限 -->
		<el-dialog :visible.sync="authIdListModal" width="60%" title="添加报名权限" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;" v-if="examDetail">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-checkbox-group v-model="examDetail.authListArray" value-key="id">
						<el-checkbox-button v-for="(val,index) in authList" :key="index" :label="val.id">{{ val.name }}
						</el-checkbox-button>
					</el-checkbox-group>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="authIdListModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addAuthIdList()" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

		<!-- 添加分组查看权限 -->
		<el-dialog :visible.sync="libModal" width="60%" title="添加抽题题库" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;" v-if="examDetail&&libList.length!=0">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-checkbox-group v-model="examDetail.questionLibIdArray" value-key="id">
						<el-checkbox-button v-for="(val,index) in libList" :key="index"
							:label="val.id">{{ val.name }}({{val.libQuestionCount}}题)
						</el-checkbox-button>
					</el-checkbox-group>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="libModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addSelectLib()" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

		<!-- 考试详情设置 -->
		<el-drawer size="70%" :with-header="false" :visible.sync="drawerExam" direction="rtl">
			<el-menu :default-active="choiceIndexExam" class="el-menu-demo" mode="horizontal" @select="handleSelectExam"
				style="margin: 0 0 30px 0;">
				<el-menu-item index="0">考试内容</el-menu-item>
				<el-menu-item index="1">基础设置</el-menu-item>
				<el-menu-item index="2">试题设置</el-menu-item>
			</el-menu>

			<div v-if="examDetail" style="margin: 0 40px;">
				<!-- 考试内容 -->
				<transition name="el-zoom-in-top">
					<div v-show="choiceIndexExam == '0'">
						<el-row style="width:100%;height: auto;margin-bottom: 0px;" v-if="choiceIndexExam == '0'">
							<h2 class="detail-title">考试内容</h2>
							<el-col :span="12">
								<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
									<el-form-item label="* 考试标题：">
										<el-input v-model="examDetail.examTitle" type="textarea"
											class="handle-input mr10" placeholder="请输入考试标题">
										</el-input>
									</el-form-item>

									<el-form-item label="* 考试内容：">
										<el-input v-model="examDetail.examContent" type="textarea"
											class="handle-input mr10" placeholder="请输入考试内容"
											:autosize="{ minRows: 4, maxRows: 10}">
										</el-input>
									</el-form-item>

									<el-form-item label="考试说明：">
										<el-input v-model="examDetail.examRemark" type="textarea"
											class="handle-input mr10" placeholder="请输入考试说明"
											:autosize="{ minRows: 4, maxRows: 10}">
										</el-input>
									</el-form-item>

									<el-form-item label="考试封面：">
										<el-upload class="upload-demo"
											action="https://biapi.xiaoyujia.com/files/uploadFiles" list-type="picture"
											:on-success="imgUploadSuccess" :show-file-list="false">
											<img :src="examDetail.examImg!=null?examDetail.examImg:blankImg"
												style="width: 250px;height: 250px;" @click="openImgUpload(4,0)">
										</el-upload>
									</el-form-item>

								</el-form>
							</el-col>

							<el-col :span="12">
								<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
									<el-form-item label="考试类型：">
										<el-select v-model="examDetail.examType" placeholder="请选择"
											style="width: 100px;">
											<el-option v-for="(item,index) in examTypeList" :key="index"
												:label="item.typeName" :value="item.id"></el-option>
										</el-select>
									</el-form-item>

									<el-form-item label="考试状态：">
										<el-select v-model="examDetail.state" placeholder="请选择" style="width: 100px;">
											<el-option v-for="(item,index) in stateExamList" :key="index"
												:label="item.text" :value="item.value"></el-option>
										</el-select>
									</el-form-item>

									<el-form-item label="* 考试时长：">
										<el-input v-model="examDetail.examDuration" class="handle-input mr10"
											placeholder="时长/分钟（默认：30）"
											onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
										</el-input> 分钟
									</el-form-item>

									<el-form-item label="考试满分：">
										<el-input v-model="examDetail.examScore" class="handle-input mr10"
											placeholder="单位：分（默认：100）"
											onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
										</el-input> 分
									</el-form-item>

									<el-form-item label="通过分数：">
										<el-input v-model="examDetail.examPassScore" class="handle-input mr10"
											placeholder="单位：分（默认：60）"
											onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
										</el-input> 分
									</el-form-item>

									<el-form-item label="开始日期：">
										<el-date-picker v-model="examDetail.examStarted" type="datetime"
											value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00']"
											placeholder="请选择考试开始日期" :picker-options="pickerOptions">
										</el-date-picker>
									</el-form-item>

									<el-form-item label="结束日期：">
										<el-date-picker v-model="examDetail.examEnded" type="datetime"
											value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00']"
											placeholder="请选择考试结束日期" :picker-options="pickerOptions">
										</el-date-picker>
									</el-form-item>

									<el-form-item label="报名入口：">
										<el-tag @click="openEntry(0)">入口二维码</el-tag>
										<el-tag @click="openEntry(1)" style="margin-left: 10px;">入口H5链接</el-tag>
									</el-form-item>
								</el-form>
							</el-col>
						</el-row>

						<div style="padding: 0 40%;">
							<el-button type="primary" @click="updateUnionExam(examDetail)">保存修改</el-button>
							<el-button @click="drawerExam=false">取消</el-button>
						</div>
					</div>
				</transition>

				<!-- 基础设置 -->
				<transition name="el-zoom-in-top">
					<div v-show="choiceIndexExam == '1'">
						<el-row style="width:100%;height: auto;margin-bottom: 0px;" v-if="choiceIndexExam == '1'">
							<h2 class="detail-title">基础设置</h2>
							<el-col :span="12">
								<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
									<el-form-item label="随机试题：">
										<el-select v-model="examDetail.isRandomQuestion" placeholder="请选择"
											style="width: 100px;">
											<el-option v-for="(item,index) in switchStateList" :key="index"
												:label="item.typeName" :value="item.id"></el-option>
										</el-select>
										<el-tooltip class="item" effect="dark"
											content="开启：按照考试详情中设置的各类型题目数量，随机生成试卷。关闭：无视题目数量设置，生成的试卷内包含对应题库内所有试题（仅适用于单题库抽题）"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="选项乱序：">
										<el-select v-model="examDetail.isShuffled" placeholder="请选择"
											style="width: 100px;">
											<el-option v-for="(item,index) in switchStateList" :key="index"
												:label="item.typeName" :value="item.id"></el-option>
										</el-select>
										<el-tooltip class="item" effect="dark" content="试题选项打乱" placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="真名显示：">
										<el-select v-model="examDetail.showRealName" placeholder="请选择"
											style="width: 100px;">
											<el-option v-for="(item,index) in switchStateList" :key="index"
												:label="item.typeName" :value="item.id"></el-option>
										</el-select>
										<el-tooltip class="item" effect="dark" content="开启：排行榜显示员工真名。关闭：排行榜只显示会员名。默认开启。"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="补考次数：">
										<el-input v-model="examDetail.maxMakeUpCount" class="handle-input mr10"
											placeholder="单位：次（默认：3）"
											onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
										</el-input> 次
										<el-tooltip class="item" effect="dark" content="超过该次数将无法进行补考。"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="应考人数：">
										<el-input v-model="examDetail.staffCount" class="handle-input mr10"
											placeholder="单位：人"
											onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
										</el-input> 人
									</el-form-item>

								</el-form>
							</el-col>

							<el-col :span="12">
								<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
									<el-form-item label="通过后角色：">
										<el-select v-model="examDetail.examPassRoleId" placeholder="请选择"
											style="width: 100px;" filterable>
											<el-option v-for="(item,index) in roleList" :key="index"
												:label="item.id+'：'+item.name" :value="item.id"></el-option>
										</el-select>
										<el-tooltip class="item" effect="dark" content="用于通过后提升员工权限。"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="未通过角色：">
										<el-select v-model="exam.examFailRoleId" placeholder="请选择" style="width: 100px;"
											filterable>
											<el-option v-for="(item,index) in roleList" :key="index"
												:label="item.id+'：'+item.name" :value="item.id"></el-option>
										</el-select>
										<el-tooltip class="item" effect="dark" content="用于未通过时降低员工权限。"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="通过后证书：">
										<el-select v-model="examDetail.certId" placeholder="请选择" style="width: 100px;">
											<el-option v-for="(item,index) in certList" :key="index"
												:label="item.certCodeFixed" :value="item.id"></el-option>
										</el-select>
									</el-form-item>

									<el-form-item label="通过后佳币：">
										<el-input v-model="examDetail.examPassJiaBi" class="handle-input mr10"
											placeholder="单位：个（默认：0）"
											onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
										</el-input> 个
									</el-form-item>

									<el-form-item label="报名权限：">
										<div>
											<ul v-for="(item,index) in authList" :key="index">
												<li class="tags-li" v-for="(item1,index1) in examDetail.authListArray"
													:key="index1" v-if="item.id==parseInt(item1)">
													<span>
														{{ item.name }}
													</span>
													<span class="tags-li-icon" @click="closeAuthTags(index1)"><i
															class="el-icon-close"></i></span>
												</li>
											</ul>
											<el-button type="primary" @click="openModal(2,0)" plain
												icon="el-icon-plus">添加角色</el-button>
										</div>

										<div v-if="examDetail.authList==0">
											<span>暂未设置</span>
										</div>
									</el-form-item>
								</el-form>
							</el-col>
						</el-row>

						<div style="padding: 0 40%;">
							<el-button type="primary" @click="updateUnionExam(examDetail)">保存修改</el-button>
							<el-button @click="drawerExam=false">取消</el-button>
						</div>
					</div>
				</transition>

				<!-- 试题设置 -->
				<transition name="el-zoom-in-top">
					<div v-show="choiceIndexExam == '2'">
						<el-row style="width:100%;height: auto;margin-bottom: 0px;" v-if="choiceIndexExam == '2'">
							<h2 class="detail-title">试题设置</h2>
							<el-col :span="24">
								<el-form ref="ruleForm" label-width="300px" class="demo-ruleForm" size="mini">
									<el-radio-group v-model="examDetail.selectQuestionType"
										@input="changeSelectQuestionType">
										<el-radio :label="0">单题库抽题</el-radio>
										<el-radio :label="1">从所有题库中随机抽题</el-radio>
										<el-radio :label="2">分题库随机抽题</el-radio>
									</el-radio-group>
								</el-form>
							</el-col>

							<div style="width: 100%;height: 60px;"></div>

							<el-col :span="6">
								<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
									<div v-if="examDetail.selectQuestionType==0">
										单题库抽题
									</div>
									<div v-if="examDetail.selectQuestionType==1||examDetail.selectQuestionType==2">
										<div v-for="(item,index) in questionTypeList" :key="index"
											style="margin: 20px 0;">
											<h3>{{item.typeName}}</h3>
											<span>共：{{item.count}}题</span>
											<br />
											<span>共：{{item.score}}分</span>
										</div>
										<div>
											<span>总计：{{allCount}}题</span>
											<br />
											<span>总分：{{allScore}}分</span>
										</div>
									</div>
								</el-form>
							</el-col>

							<el-col :span="18">
								<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini"
									v-if="examDetail.selectQuestionType==0">
									<!-- 单题库抽题 -->
									<el-form-item label="考试题库：">
										<el-select v-model="examDetail.questionLibId" placeholder="请选择"
											style="width: 100px;">
											<el-option v-for="(item,index) in libList" :key="index" :label="item.name"
												:value="item.id">
											</el-option>
										</el-select>
									</el-form-item>

									<el-form-item label="单选题数量：">
										<el-input v-model="examDetail.singleQuestionCount" class="handle-input mr10"
											placeholder="单位：道（默认：0）"
											onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
										</el-input> 道
									</el-form-item>
									<el-form-item label="多选题数量：">
										<el-input v-model="examDetail.multipleQuestionCount" class="handle-input mr10"
											placeholder="单位：道（默认：0）"
											onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
										</el-input> 道
									</el-form-item>
									<el-form-item label="判断题数量：">
										<el-input v-model="examDetail.judgementQuestionCount" class="handle-input mr10"
											placeholder="单位：道（默认：0）"
											onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
										</el-input> 道
									</el-form-item>
									<el-form-item label="填空题数量：">
										<el-input v-model="examDetail.fillQuestionCount" class="handle-input mr10"
											placeholder="单位：道（默认：0）"
											onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
										</el-input> 道
									</el-form-item>
									<el-form-item label="问答题数量：">
										<el-input v-model="examDetail.openQuestionCount" class="handle-input mr10"
											placeholder="单位：道（默认：0）"
											onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
										</el-input> 道
									</el-form-item>
								</el-form>

								<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini"
									v-if="examDetail.selectQuestionType!=0">
									<ul v-for="(item,index) in libList" :key="index">
										<li class="tags-li" v-for="(item1,index1) in examDetail.questionLibIdArray"
											:key="index1" v-if="item.id==item1">
											<span>
												{{ item.name }}
											</span>
											<span class="tags-li-icon" @click="closeSelectLib(index1)"><i
													class="el-icon-close"></i></span>
										</li>
									</ul>
									<el-button type="primary" @click="libModal = true" plain
										icon="el-icon-plus">添加题库</el-button>

									<h2>抽题规则</h2>
									<div v-for="(item,index1) in questionLibVoList" :key="index1">
										<h3>题库“{{item.name}}”抽题规则：</h3>
										<el-table :data="item.unionQuestionLibRuleList" style="width: 100%">
											<el-table-column fixed prop="billNo" label="题库题目数量">
												<template slot-scope="scope">
													<div>{{questionTypeList[scope.row.questionType].typeName}}
														<span>
															（{{scope.row.libQuestionCount}}题）
														</span>
													</div>
												</template>
											</el-table-column>

											<el-table-column fixed prop="questionCount" label="抽题数量">
												<template slot-scope="scope">
													<el-input-number v-model="scope.row.questionCount"
														:max="scope.row.libQuestionCount" :controls="false"
														@change="changeRule" placeholder="请输入抽题数量"></el-input-number>
													道
												</template>
											</el-table-column>

											<el-table-column fixed prop="questionScore" label="每题分数">
												<template slot-scope="scope">
													<el-input v-model="scope.row.questionScore" @change="changeRule"
														class="handle-input mr10" placeholder="请输入每道题分数"
														onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
													</el-input> 分
												</template>
											</el-table-column>
										</el-table>
									</div>


								</el-form>
							</el-col>
						</el-row>

						<div style="padding: 50px 40%;">
							<el-button type="primary" @click="updateLibRule(examDetail)">提交修改</el-button>
							<el-button @click="drawerExam=false">取消</el-button>
						</div>
					</div>
				</transition>
			</div>
		</el-drawer>

		<!-- 考试详情设置 -->
		<el-drawer size="70%" :with-header="false" :visible.sync="drawerExamRecord" direction="rtl">
			<div class="drawerForm" style="margin: 40px 40px;">
				<h2>考试记录明细</h2>
				<el-form ref="form">
					<el-table :data="examRecordList" style="width: 100%">
						<el-table-column fixed prop="id" label="排名" width="60">
							<template slot-scope="scope">
								<div>{{scope.$index+1}}</div>
							</template>
						</el-table-column>

						<el-table-column fixed prop="memberId" label="会员id" width="120">
							<template slot-scope="scope">
								<span>
									{{scope.row.memberId||'-'}}
								</span>
							</template>
						</el-table-column>

						<el-table-column fixed prop="employeeNo" label="工号" width="120">
							<template slot-scope="scope">
								<span>
									{{scope.row.employeeNo||'-'}}
								</span>
							</template>
						</el-table-column>

						<el-table-column fixed prop="employeeName" label="姓名" width="120">
							<template slot-scope="scope">
								<span>
									{{scope.row.employeeName||scope.row.memberName||'暂无用户名'}}
								</span>
							</template>
						</el-table-column>

						<el-table-column fixed prop="startedTime" label="开考时间" width="140" sortable>
							<template slot-scope="scope">
								<span>
									{{scope.row.startedTime||'-'}}
								</span>
							</template>
						</el-table-column>

						<el-table-column fixed prop="endedTime" label="交卷时间" width="140" sortable>
							<template slot-scope="scope">
								<span>
									{{scope.row.endedTime||'-'}}
								</span>
							</template>
						</el-table-column>

						<el-table-column fixed prop="recordDuration" label="考试用时" width="120" sortable v-if="isRole">
							<template slot-scope="scope">
								<span>
									{{formatTime(scope.row.recordDuration||0)}}
								</span>
							</template>
						</el-table-column>

						<el-table-column fixed prop="recordScore" label="考试分数" width="120" sortable>
							<template slot-scope="scope">
								<span>
									{{scope.row.recordScore||'-'}}
								</span>分
							</template>
						</el-table-column>

						<el-table-column fixed prop="recordPassed" label="考试结果" width="120">
							<template slot-scope="scope">
								<el-tag
									:type="scope.row.recordPassed=='通过'?'success':'danger'">{{scope.row.recordPassed}}</el-tag>
							</template>
						</el-table-column>
					</el-table>

					<el-form-item>
						<el-button @click="drawerExamRecord=false">关闭</el-button>
						<el-button type="primary" @click="recordDownload()">导出</el-button>
					</el-form-item>
				</el-form>
			</div>
		</el-drawer>

		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>
	</div>
</template>

<script>
	import Vue from 'vue';
	export default {
		name: "exam",
		data() {
			return {
				url: '',
				imageUrl: '',
				videoUrl: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				excelUploadId: 1,
				excelUploadUrl: "",
				isRole: (localStorage.getItem('roleId') || 0) == 1 ? true : false,

				// 测试时使用
				// excelUploadUrlOrgin: "http://localhost:8063/unionQuestion/excelImport",
				excelUploadUrlOrgin: "https://biapi.xiaoyujia.com/unionQuestion/excelImport",
				rankUrl: "https://jiajie.xiaoyujia.com/pages-mine/exam/exam-rank",
				entryUrl: "https://jiajie.xiaoyujia.com/pages-mine/exam/exam-entry",
				deleteTips: "确定删除选中试题选项吗？该操作无法恢复，请谨慎操作!",
				isEdit: false,
				showEdit: true,
				imgModal: false,
				rankModal: false,
				videoModal: false,
				examModal: false,
				libModal: false,
				questionLibModal: false,
				authIdListModal: false,
				entryModal: false,
				drawerExam: false,
				drawerExamRecord: false,

				list: [],
				libList: [],
				authList: [{
					id: 1,
					name: '普通会员'
				}, {
					id: 2,
					name: '行政员工'
				}, {
					id: 3,
					name: '服务员工'
				}, {
					id: 4,
					name: '保姆月嫂'
				}, {
					id: 5,
					name: '共创店'
				}, {
					id: 6,
					name: '合伙人'
				}, {
					id: 7,
					name: '门店店长'
				}, {
					id: 8,
					name: '陪跑店店长'
				}],
				questionLibVoList: [],
				examRecordList: [],

				examTitle: '',
				loading: true,
				pageSizeOpts: [10, 20, 50, 100],
				exam: {
					examImg: null,
				},
				examDetail: null,
				questionLib: {},
				quer: {
					search: null,
					state: null,
					questionLibId: null,
					orderBy: "u.examCreTime ASC",
					current: 1,
					size: 10,

					// 考试
					examTitle: null,
					examContent: null,
					examRemark: null,
					examType: null,
					isRandomQuestion: null,

					// 题库
					type: null,

					// 试题
					questionTitle: null,
					questionRemark: null,
					questionType: null,
					typeList: null,
				},
				recordQuer: {
					examId: 0,
					orderBy: 't.recordScore DESC',
					current: 1,
					size: 200
				},
				querLib: {
					id: null
				},
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				choiceIndex: '0',
				choiceIndexExam: '0',
				choiceIndexNow: '0',
				choiceItemIndex: 0,
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				searchExamType: null,
				searchQuestionType: null,
				searchLib: null,
				searchLibType: null,
				searchState: null,
				stateExamList: [{
					value: 0,
					text: "进行中",
					type: "success"
				}, {
					value: 1,
					text: "已结束",
					type: "info"
				}],
				stateList: [{
					value: 0,
					text: "下架",
					type: "info"
				}, {
					value: 1,
					text: "上架",
					type: "success"
				}],
				roleList: [],
				certList: [],
				switchStateList: [{
						id: 0,
						typeName: "关闭",
						type: "info"
					},
					{
						id: 1,
						typeName: "开启",
						type: "success"
					}
				],
				examTypeList: [],
				typeStyleList: [{
						id: 0,
						type: "success"
					},
					{
						id: 1,
						type: "info"
					},
					{
						id: 2,
						type: "warning"
					}, {
						id: 3,
						type: "primary"
					}
				],
				questionTypeList: [{
						id: 0,
						typeName: "单选题",
						type: "primary",
						count: 0,
						score: 0
					},
					{
						id: 1,
						typeName: "多选题",
						type: "success",
						count: 0,
						score: 0
					},
					{
						id: 2,
						typeName: "判断题",
						type: "info",
						count: 0,
						score: 0
					},
					{
						id: 3,
						typeName: "填空题",
						type: "warning",
						count: 0,
						score: 0
					},
					{
						id: 4,
						typeName: "问答题",
						type: "danger",
						count: 0,
						score: 0
					}
				],
				allCount: 0,
				allScore: 0,
				pickerOptions: {
					shortcuts: [{
						text: '昨天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '今天',
						onClick(picker) {
							picker.$emit('pick', new Date());
						}
					}, {
						text: '明天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周前',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周后',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}]
				},
				searchTime: [],
				pickerOptions1: {
					shortcuts: [{
						text: '今天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '昨天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}],
				},
			}
		},
		created() {
			this.getData()
		},
		methods: {
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceIndex = key
				this.expands = []
				this.showEdit = true
				this.reset()
			},
			// 切换栏目
			handleSelectExam(key, keyPath) {
				this.choiceIndexExam = key
			},
			getData() {
				this.choiceIndexNow = 4
				if (this.choiceIndex == 0) {
					this.$postData("getUnionExamPage", this.quer).then(res => {
						if (res.status == 200) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
							// 追加编辑状态位
							for (let item of this.list) {
								this.$set(item, "isEdit", false)

								// 追加报名权限
								this.$set(item, "authListArray", this.strToArray(item.authList))
							}


							this.choiceIndexNow = this.choiceIndex
						} else {
							this.list = []
							this.choiceIndexNow = this.choiceIndex
							this.$message.error('未查询到相关考试记录!')
						}
						this.loading = false
					})

					this.getAllRole()
					this.getCertList()
					this.getLib()
				} else if (this.choiceIndex == 1) {
					this.$postData("getQuestionLibPage", this.quer).then(res => {
						if (res.status == 200) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total

							// 追加编辑状态位
							for (let item of this.list) {
								this.$set(item, "isEdit", false)
							}
							this.choiceIndexNow = this.choiceIndex
						} else {
							this.list = []
							this.choiceIndexNow = this.choiceIndex
							this.$message.error('未查询到相关题库记录!')
						}
						this.loading = false
					})
				} else if (this.choiceIndex == 2) {
					this.$postData("getUnionQuestionPage", this.quer).then(res => {
						if (res.status == 200) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
							this.$message.success('查询成功，总计' + res.data.total + '条数据！')
							// 追加编辑状态位
							for (let item of this.list) {
								this.$set(item, "isEdit", false)
							}
							this.choiceIndexNow = this.choiceIndex
						} else {
							this.list = []
							this.choiceIndexNow = this.choiceIndex
							this.$message.error('未查询到相关试题记录!')
						}
						this.loading = false
					})
				} else if (this.choiceIndex == 3) {
					// 处理一下时间筛选字段
					if (this.searchTime && this.searchTime.length >= 2) {
						this.quer.startTime = this.searchTime[0]
						this.quer.endTime = this.searchTime[1]
					} else {
						this.quer.startTime = null
						this.quer.endTime = null
					}

					this.$postData("pageUnionExamRecordVo", this.quer).then(res => {
						if (res.status == 200) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
							this.$message.success('查询成功，总计' + res.data.total + '条数据！')
							this.choiceIndexNow = this.choiceIndex
						} else {
							this.list = []
							this.choiceIndexNow = this.choiceIndex
							this.$message.error('未查询到相关考试记录统计数据!')
						}
						this.loading = false
					})
				}
				this.getExamType()
				this.getLib()
			},
			// 获取考试/题库类型
			getExamType() {
				this.$getData("getExamType").then(res => {
					this.loading = false
					if (res.status == 200) {
						this.examTypeList = res.data
					} else {
						this.examTypeList = []
						this.$message.error('未查询到相关考试/题库类型!')
					}
				})
			},
			// 获取题库列表
			getLib() {
				this.$postData("getUnionQuestionLib", this.querLib).then(res => {
					this.loading = false
					if (res.status == 200) {
						this.libList = res.data
					} else {
						this.libList = []
						this.$message.error('未查询到相关题库记录!')
					}
				})
			},
			// 获取所有角色
			getAllRole() {
				this.$getData("getAllRole").then(res => {
					if (res.status == 200) {
						this.roleList = res.data
					} else {
						this.roleList = []
						this.$message.error('未查询到角色相关记录!')
					}
				})
			},
			// 获取证书列表
			getCertList() {
				this.$getData("getAllUnionCertificates").then(res => {
					if (res.status == 200) {
						this.certList = res.data
					} else {
						this.certList = []
						// this.$message.error('未查询到证书相关记录!')
					}
				})
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				let data = []
				let i = 0
				let length = this.questionTypeList.length
				let isFindType = false
				// 格式化搜索项
				for (i = 0; i < length; i++) {
					if (this.searchQuestionType == i + "") {
						data = []
						data.push(Number(i))
						this.quer.typeList = data
						isFindType = true
					} else {
						if (this.searchQuestionType == "" && this.searchQuestionType !== 0) {
							this.quer.typeList = null
						}
					}
				}

				this.quer.questionLibId = this.searchLib != "" ? this.searchLib : null
				this.quer.examType = this.searchExamType != "" ? this.searchExamType : null
				this.quer.type = this.searchLibType != "" ? this.searchLibType : null
				this.quer.state = this.searchState != null ? this.searchState : null

				// 遍历到最后没有找到对应的试题类型，则随便赋一个值
				if (!isFindType && this.searchQuestionType !== "" && this.searchQuestionType !== null) {
					this.quer.typeList = [999]
				}
				this.getData()
			},
			// 重置
			reset() {
				this.loading = true

				// 考试
				this.quer.examTitle = ""
				this.quer.examContent = ""
				this.quer.examRemark = ""
				this.quer.examType = null
				this.quer.isRandomQuestion = null

				// 题库
				this.quer.type = null

				// 试题
				this.quer.questionTitle = null
				this.quer.questionRemark = null
				this.quer.questionType = null
				this.quer.typeList = null

				// 考试记录
				this.quer.startTime = null
				this.quer.endTime = null

				// 通用
				this.quer.search = ""
				this.quer.state = ""
				this.quer.questionLibId = null
				this.quer.orderBy = "u.examCreTime ASC"
				this.quer.current = 1

				this.searchState = ""
				this.searchQuestionType = null
				this.searchExamType = null
				this.searchLibType = null
				this.searchLib = null

				this.isEdit = false

				if (this.choiceIndex == 0) {
					this.quer.orderBy = "u.examCreTime ASC"
				} else if (this.choiceIndex == 1) {
					this.quer.orderBy = "u.creTime ASC"
				} else if (this.choiceIndex == 2) {
					this.quer.orderBy = "u.creTime ASC"
				}
				this.getData()
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "考试封面图") {
					tips = "推荐尺寸：1500*1500（1:1）"
				} else if (column.label == "试题图片") {
					tips = "推荐尺寸：1500*1125（4:3）"
				} else if (column.label == "试题视频") {
					tips = "考试中将优先于图片展示"
				} else if (column.label == "随机试题") {
					tips = "开启：按照考试详情中设置的各类型题目数量，随机生成试卷。关闭：无视题目数量设置，生成的试卷内包含对应题库内所有试题。"
				} else if (column.label == "选项乱序") {
					tips = "试题选项打乱"
				} else if (column.label == "排行榜显示真名") {
					tips = "开启：排行榜显示员工真名。关闭：排行榜只显示会员名。默认开启。"
				} else if (column.label == "最大考试次数") {
					tips = "可进行补考的次数"
				} else if (column.label == "考试未通过角色") {
					tips = "用于未通过时降低员工权限"
				} else if (column.label == "考试通过后角色") {
					tips = "用于通过后提升员工权限"
				} else if (column.label == "报名权限") {
					tips = "允许该类用户报名考试，未设置则允许所有"
				} else if (column.label == "开放模拟考") {
					tips = "允许用户通过模拟考熟悉考题"
				}

				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			// 格式化试题类型
			formatType(value, val) {
				let result = "暂无"
				let type = 0
				let typeData = []
				if (value == 0) {
					type = val.examType
					typeData = this.examTypeList
				} else if (value == 1) {
					type = val.questionType
					typeData = this.questionTypeList
				} else if (value == 2) {
					type = val.type
					typeData = this.examTypeList
				}
				for (let item of typeData) {
					if (item.id == type) {
						result = item.typeName
						break
					}
				}
				return result
			},
			formatTypeStyle(value, val) {
				let type = 1
				if (value == 0) {
					type = val.examType
				} else if (value == 1) {
					type = val.type
				}
				return this.typeStyleList[type % 4].type
			},
			// 格式化题库名称
			formatQuestionLib(val) {
				let result = "暂无"
				let libId = val
				for (let item of this.libList) {
					if (libId == item.id) {
						result = item.name
						break
					}
				}
				return result
			},
			// 格式化证书显示
			formatCert(val) {
				let result = "暂未录入"
				let id = val.certId
				for (let item of this.certList) {
					if (id == item.id) {
						result = item.certCodeFixed
						break
					}
				}
				return result
			},
			formatTime(time) {
				let h = parseInt(time / (60 * 60))
				let m = parseInt((time - (h * 60 * 60)) / 60)
				let s = parseInt((time - (h * 60 * 60) - (m * 60)))
				h = h < 10 ? '0' + h : h
				m = m < 10 ? '0' + m : m
				s = s < 10 ? '0' + s : s
				let leaveTime = h + ':' + m + ':' + s
				return leaveTime
			},

			openModal(index, index1) {
				if (index == 0) {
					this.exam = {}
					this.examModal = true
					this.examTitle = this.list[index1].examTitle
				} else if (index == 1) {
					this.questionLib = {}
					this.questionLibModal = true
				} else if (index == 2) {
					this.authIdListModal = true
				}
			},
			// 打开排行
			openRank(val) {
				let id = val.id
				let title = val.examTitle
				this.rankUrl = "https://jiajie.xiaoyujia.com/pages-mine/exam/exam-rank?id=" + id + "&examTitle=" + title
				this.rankModal = true
			},
			// 打开入口
			openEntry(value, index) {
				let id = this.examDetail.id
				let url = 'https://jiajie.xiaoyujia.com/pages-mine/exam/exam-entry?id=' + id
				if (value == 0) {
					url += '&showShareImg=1'
					this.entryUrl = url
					this.entryModal = true
				} else {
					this.copyDatas(url)
				}
			},
			copyDatas(url) {
				let oInput = document.createElement('input');
				oInput.value = url;
				document.body.appendChild(oInput);
				oInput.select();
				document.execCommand("Copy");
				this.$message({
					message: '复制成功',
					type: 'success'
				});
				oInput.remove()
			},
			//打开详情
			rowClick(id) {

			},
			// 打开记录
			openRecord(index) {
				this.choiceItemIndex = index
				let examId = this.list[index].id
				this.pageUnionExamRecord(examId)
				this.drawerExamRecord = true
			},
			pageUnionExamRecord(examId) {
				this.recordQuer.examId = examId
				this.$postData("pageUnionExamRecord", this.recordQuer).then(res => {
					if (res.status == 200) {
						this.examRecordList = res.data.records
						this.$message.success('查询成功，总计' + res.data.total + '条数据！')
					} else {
						this.examRecordList = []
						this.$message.error('未查询到相关考试记录统计数据!')
					}
				})
			},
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			openDetail(index, type) {
				this.choiceItemIndex = index
				if (type == 0) {
					this.examDetail = null
					this.drawerExam = true
					let val = this.list[index]
					this.getExamDetail(val.id)
				}
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.list[this.getIndex(this.expands[0])].questionContent.choice[this.uploadId].imgUrl = res.data
				} else if (this.uploadImgType == 1) {
					this.list[this.uploadId].examImg = res.data
				} else if (this.uploadImgType == 2) {
					this.$set(this.exam, "examImg", res.data)
				} else if (this.uploadImgType == 3) {
					this.list[this.uploadId].questionImg = res.data
				} else if (this.uploadImgType == 4) {
					this.examDetail.examImg = res.data
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadId = index
			},
			// 打开图片预览
			openImg(url) {
				if (url != null && url != '') {
					this.imageUrl = url
				} else {
					this.imageUrl = this.blankImg
				}
				this.imgModal = true
			},
			// 将字符串转化为数组
			strToArray(val) {
				let arrayString = []
				let array = []
				arrayString = val.split(',')
				arrayString.forEach(it => {
					let value = parseInt(it)
					if (value != null && value != 0) {
						array.push(value)
					}
				})
				return array
			},
			getExamDetail(id) {
				this.$getUrl("selectUnionExamById", id, null).then(res => {
					if (res.status == 200) {
						this.examDetail = res.data
						// 追加报名权限
						this.$set(this.examDetail, "authListArray", this.strToArray(this.examDetail.authList))

						if (this.examDetail.selectQuestionType != 0) {
							this.getUnionQuestionLibVo(this.examDetail, 0)
							this.$set(this.examDetail, "questionLibIdArray", this.strToArray(this.examDetail
								.questionLibIdList))
						}

					} else {
						this.examDetail = null
						this.$message.error(res.msg)
					}
				})
			},
			// 添加考试
			addUnionExam() {
				let exam = this.exam
				// 数据校验
				if (exam.examTitle == null) {
					this.$message.error('请填写考试标题！')
				} else if (exam.examContent == null) {
					this.$message.error('请填写考试内容简介！')
				} else if (exam.examDuration == null) {
					this.$message.error('请填写考试时长！')
				} else {
					let no = localStorage.getItem("account")
					if (no == undefined) {
						no = "admin"
					}
					this.$set(exam, "updater", no)
					this.$postData("addUnionExam", exam).then(res => {
						if (res.status == 200) {
							this.$message.success('考试添加成功!')
							this.examModal = false
							this.getData()
						} else {
							this.$message.error('考试添加失败！' + res.msg)
						}
					})

				}
			},
			// 更新考试
			updateUnionExam(val) {
				// 格式化权限角色列表
				if (val.authListArray) {
					val.authList = val.authListArray.join(',')
					if (val.authList.length == 0) {
						val.authList = '0'
					}
				}

				this.$postData("updateUnionExam", val).then(res => {
					if (res.status == 200) {
						this.$message.success('考试更新成功!')
						val.isEdit = false
						this.isEdit = false
						if (this.drawerExam && this.examDetail) {
							this.list[this.choiceItemIndex] = this.examDetail
							let array = this.strToArray(this
								.examDetail.authList)
							this.$set(this.list[this.choiceItemIndex], "authListArray", array)
						}
					} else {
						this.$message.error('考试更新失败！' + res.msg)
					}
				})
			},
			// 删除考试
			deleteUnionExam(val) {
				this.$postData("deleteUnionExam", val).then(res => {
					if (res.status == 200) {
						this.$message.success('考试删除成功!')
						val.isEdit = false
						let index = this.getIndex(val.id)
						this.$delete(this.list, index)
						// this.getData()
					} else {
						this.$message.error('考试删除失败！' + res.msg)
					}
				})
			},

			// 更改试题
			updateUnionQuestion(val) {
				let no = localStorage.getItem("account")
				if (no == undefined) {
					no = "admin"
				}
				this.$set(val, "updater", no)
				this.$postData("updateUnionQuestion", val).then(res => {
					if (res.status == 200) {
						this.$message.success('试题更新成功!')
						val.isEdit = false
					} else {
						this.$message.error('试题更新失败！' + res.msg)
					}
				})
			},
			// 删除试题
			deleteUnionQuestion(val) {
				this.$postData("deleteUnionQuestion", val).then(res => {
					if (res.status == 200) {
						this.$message.success('试题删除成功!')
						val.isEdit = false
						let index = this.getIndex(val.id)
						this.$delete(this.list, index)
						// this.getData()
					} else {
						this.$message.error('试题删除失败！' + res.msg)
					}
				})
			},

			// 添加题库
			addUnionQuestionLib() {
				let questionLib = this.questionLib
				// 数据校验
				if (questionLib.name == null) {
					this.$message.error('请填写题库标题！')
				} else if (questionLib.content == null) {
					this.$message.error('请填写题库内容简介！')
				} else {
					let no = localStorage.getItem("account")
					if (no == undefined) {
						no = "admin"
					}
					this.$set(questionLib, "creater", no)
					this.$postData("addUnionQuestionLib", questionLib).then(res => {
						if (res.status == 200) {
							this.$message.success('题库添加成功!')
							this.questionLibModal = false
							this.getData()
						} else {
							this.$message.error('题库添加失败！' + res.msg)
						}
					})

				}
			},
			// 更新题库
			updateUnionQuestionLib(val) {
				this.$postData("updateUnionQuestionLib", val).then(res => {
					if (res.status == 200) {
						this.$message.success('更新题库成功!')
						val.isEdit = false
					} else {
						this.$message.error('更新题库失败！' + res.msg)
					}
				})
			},
			// 删除题库
			deleteUnionQuestionLib(val) {
				this.$postData("deleteUnionQuestionLib", val).then(res => {
					if (res.status == 200) {
						this.$message.success('删除题库成功!')
						val.isEdit = false
						let index = this.getIndex(val.id)
						this.$delete(this.list, index)
						// this.getData()
					} else {
						this.$message.error('删除题库失败！' + res.msg)
					}
				})
			},

			// 更新试题内容
			updateQuestionContent(val, val1) {
				this.$postData("updateQuestionChoice", val).then(res => {
					if (res.status == 200) {
						// this.$message.success('试题选项更新-成功!')

					} else {
						this.$message.error('试题选项更新-失败！' + res.msg)
					}
				})


				this.$postData("updateAnswerContent", val1).then(res => {
					if (res.status == 200) {
						this.$message.success('试题内容更新成功!')
						this.isEdit = false
					} else {
						this.$message.error('试题答案更新-失败！' + res.msg)
					}
				})
			},
			// 删除试题选项内容
			deleteQuestionChoice() {
				let val = this.multipleSelection
				let length = val.length
				this.deleteTips = "确定删除选中的" + length + "个选项吗？该操作无法恢复，请谨慎操作!"

				if (length == 0) {
					this.$message.error('还未选择任何试题选项!')
					return
				}
				this.$postData("deleteQuestionChoice", val).then(res => {
					if (res.status == 200) {
						this.$message.success('试题选项删除成功!')
						this.isEdit = false
						this.multipleSelection = []
						this.getData()
					} else {
						this.$message.error('试题选项删除失败！' + res.msg)
					}
				})
			},

			// 计算各题型数量
			countQuestionInfo() {
				let allCount = 0
				let allScore = 0
				this.questionTypeList.forEach(item => {
					item.count = 0
					item.score = 0
				})

				this.questionLibVoList.forEach(item => {
					let data = item.unionQuestionLibRuleList
					data.forEach(item1 => {
						let type = item1.questionType
						let count = parseInt(item1.questionCount)
						let score = parseFloat(count * item1.questionScore)
						this.questionTypeList[type].count += count
						this.questionTypeList[type].score += score
						allCount += count
						allScore += score
					})
				})
				this.allCount = allCount
				this.allScore = allScore
			},
			// 变更输入
			changeRule(e) {
				this.countQuestionInfo()
			},
			changeSelectQuestionType(e) {
				if (e != 0) {
					this.$set(this.examDetail, "questionLibIdArray", this.strToArray(this.examDetail.questionLibIdList))
					this.getUnionQuestionLibVo(this.examDetail, 0)
				}
			},
			// 添加课程权限列表
			addAuthIdList() {
				this.authIdListModal = false
			},
			addSelectLib() {
				this.libModal = false
				this.getUnionQuestionLibVo(this.examDetail, 1)
			},
			// 删除考试报名权限标签
			closeAuthTags(index) {
				this.examDetail.authListArray.splice(index, 1)
			},
			// 删除题库标签
			closeSelectLib(index) {
				this.examDetail.questionLibIdArray.splice(index, 1)
				this.getUnionQuestionLibVo(this.examDetail, 1)
			},
			// 获取题库规则
			getUnionQuestionLibVo(val, type) {
				if (type == 1) {
					let array = this.examDetail.questionLibIdArray
					if (array != []) {
						this.examDetail.questionLibIdList = array.join(',')
						if (this.examDetail.questionLibIdList == '') {
							this.examDetail.questionLibIdList = '0'
						}
					}
				}

				let data = {
					examId: val.id,
					selectQuestionType: val.selectQuestionType,
					questionLibIdList: val.questionLibIdList
				}
				this.$postData("getUnionQuestionLibVo", data).then(res => {
					if (res.status == 200) {
						this.questionLibVoList = res.data
						this.countQuestionInfo()
					} else {
						this.questionLibVoList = null
					}
				})
			},
			// 更新题库规则
			updateUnionQuestionLibVo(val) {
				this.$set(val[0], "examId", this.examDetail.id)
				this.$set(val[0], "questionLibIdList", this.examDetail.questionLibIdList)
				this.$set(val[0], "selectQuestionType", this.examDetail.selectQuestionType)
				this.$postData("updateUnionQuestionLibVo", val).then(res => {
					if (res.status == 200) {
						this.$message.success('更新试题设置成功!')
					} else {
						this.$message.error(res.msg)
					}
				})
			},
			// 更新题库配置
			updateLibRule(val) {
				let selectQuestionType = val.selectQuestionType
				if (selectQuestionType == 0) {
					this.updateUnionExam(val)
				} else {
					this.updateUnionQuestionLibVo(this.questionLibVoList)
				}
			},
			// 删除试题配置规则
			deleteUnionQuestionLibRule(val) {
				this.$postData("deleteUnionQuestionLibRule", val).then(res => {
					if (res.status == 200) {} else {
						this.$message.error('删除试题配置规则-失败！' + res.msg)
					}
				})
			},

			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 导入
			excelImport(index) {
				let id = 1
				let excelUploadUrl = this.excelUploadUrlOrgin
				let no = localStorage.getItem("account")
				if (no == undefined) {
					no = "admin"
				}

				excelUploadUrl += "/" + id + "/" + no
				this.excelUploadUrl = excelUploadUrl
				this.excelUploadId = index
				this.loading = true
			},
			// 联盟试题模板下载
			excelDownload() {
				this.$postData("unionQuestionExcelDownload", null, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('联盟试题Excel模板下载成功!')
					this.blobExport({
						tablename: "考试题库试题模板1.0",
						res: res
					})
				})
			},
			// 联盟试题模板下载
			recordDownload() {
				let name = this.list[this.choiceItemIndex].examTitle || ''
				this.$postData("examRecordExcelDownload", this.recordQuer, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('考试记录下载成功!')
					this.blobExport({
						tablename: name + "-成绩单",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// Excel表格上传成功
			excelUploadSuccess(res, file) {
				this.loading = false
				this.$message.success(res.msg)
				// 将解析到的结果加入到列表中
				for (let item of res.data) {
					// this.list[this.excelUploadId].courseContent.push(item)
				}
			},
			// Excel表格上传失败
			excelUploadError() {
				this.loading = false
				this.$message.error('试题内容解析失败！请使用正确的Excel模板进行上传！')
			},


			// 多选
			handleSelectionChange(val) {
				this.multipleSelection = val
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
		}
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}

	.column-text {
		white-space: pre-wrap;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 4;
	}
</style>