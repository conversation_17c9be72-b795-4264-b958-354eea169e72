<template>
	<div class="container">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">海报列表</el-menu-item>
			<el-menu-item index="1">海报类型</el-menu-item>
		</el-menu>

		<!-- 搜索栏目 -->
		<div v-if="choiceIndexNow==0">
			<div class="handle-box" style="margin: 20px 20px;">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="海报标题、内容"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="海报类型" style="margin-right: 20px">
								<el-select v-model="searchType" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="海报状态" style="margin-right: 20px">
								<el-select v-model="searchState" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(0,0)">添加
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">

				<el-table-column prop="id" width="60" label="编号">
				</el-table-column>

				<el-table-column width="140" prop="posterName" label="海报标题">
				</el-table-column>

				<el-table-column width="140" label="海报内容" prop="posterContent">
					<template slot-scope="scope">
						<span>
							{{scope.row.posterContent || '暂无'}}
						</span>
					</template>
				</el-table-column>

				<el-table-column width="125" prop="posterImg" label="海报图" :render-header="renderHeader">
					<template slot-scope="scope">
						<img :src="scope.row.posterImg||blankImg" style="width: 100px;height: 100px;"
							@click="openImg(scope.row.posterImg||blankImg)">
					</template>
				</el-table-column>

				<el-table-column width="122" label="关联课程" prop="courseId">
					<template slot-scope="scope">
						<span v-if="scope.row.courseId==item.id" v-for="(item,index) in courseList" :key="index">
							{{item.courseTitle}}
						</span>
						<span v-if="!scope.row.courseId">
							暂未设置
						</span>
					</template>
				</el-table-column>

				<el-table-column width="122" label="分享数" prop="shareNum" sortable>
					<template slot-scope="scope">
						<span>
							{{scope.row.shareNum||0}}次
						</span>
					</template>
				</el-table-column>

				<el-table-column width="102" label="状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.posterState == 0" type="info">下架</el-tag>
						<el-tag v-if="scope.row.posterState == 1" type="success">上架</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creator" label="创建人">
					<template slot-scope="scope">
						<span v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
						<span v-else>{{scope.row.creator||''}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="200" label="操作">
					<template slot-scope="scope">
						<el-button @click="openModal(1,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<div v-if="choiceIndexNow==1">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="类型标题、内容"></el-input>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="类型状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>
							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>
							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(2,0)">添加
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands">

				<el-table-column prop="id" width="60" label="编号">
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="typeName" label="类型标题">
				</el-table-column>

				<el-table-column width="140" label="海报内容" prop="typeContent">
					<template slot-scope="scope">
						<span>
							{{scope.row.typeContent || '暂无'}}
						</span>
					</template>
				</el-table-column>

				<el-table-column width="102" label="状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.typeState == 0" type="info">下架</el-tag>
						<el-tag v-if="scope.row.typeState == 1" type="success">上架</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creator" label="创建人">
					<template slot-scope="scope">
						<span v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
						<span v-else>{{scope.row.creator||''}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="200" label="操作">
					<template slot-scope="scope">
						<el-button @click="openModal(3,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--添加海报-->
		<el-drawer size="60%" :with-header="false" :visible.sync="coursePosterModal" direction="rtl">
			<div v-if="choiceItem">
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">海报信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 海报标题：">
								<el-input v-model="choiceItem.posterName" type="textarea" class="handle-input mr10"
									placeholder="请输入海报标题" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="海报内容：">
								<el-input v-model="choiceItem.posterContent" type="textarea" class="handle-input mr10"
									placeholder="请输入海报内容描述" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="* 海报类型：">
								<el-select v-model="choiceItem.posterTypeId" placeholder="请选择" filterable>
									<el-option v-for="(item,index) in typeList" :key="index"
										:label="item.id+'：'+item.typeName" :value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="可根据类型对海报进行分类" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="海报图片：">
								<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
									list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="choiceItem.posterImg||blankImg" style="width: 150px;height: 150px;"
										@click="openImgUpload(0,0)">
								</el-upload>
							</el-form-item>

							<el-form-item label="* 关联课程：">
								<el-select v-model="choiceItem.courseId" placeholder="请选择" filterable>
									<el-option v-for="(item,index) in courseList" :key="index"
										:label="item.id+'：'+item.courseTitle" :value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="在海报下方显示相关课程，并可在详情页通过点击卡片查看课程详情"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="海报链接：">
								<el-input v-model="choiceItem.path" type="textarea" class="handle-input mr10"
									placeholder="请输入海报链接" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>
						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="海报类型：">
								<el-select v-model="choiceItem.posterType" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="可根据类型进行分类" placement="top-start">
									<i class="el-icon-type" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="* 海报类型：">
								<el-select v-model="choiceItem.courseTypeId" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
								<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(2,0)"
									style="margin-left: 20px">添加
								</el-button>
							</el-form-item>

							<el-form-item label="海报状态：" v-if="detailType==1">
								<el-select v-model="choiceItem.posterState" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>

						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="coursePosterModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="updateCoursePoster()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateCoursePoster()" type="success" size="small" v-if="detailType==1">保存
					</el-button>
				</div>

			</div>
		</el-drawer>

		<!--添加海报类型-->
		<el-drawer size="60%" :with-header="false" :visible.sync="coursePosterTypeModal" direction="rtl">
			<div v-if="choiceItem">
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">海报类型信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 类型标题：">
								<el-input v-model="choiceItem.typeName" type="textarea" class="handle-input mr10"
									placeholder="请输入类型标题">
								</el-input>
							</el-form-item>

							<el-form-item label="* 类型内容：">
								<el-input v-model="choiceItem.typeContent" type="textarea" class="handle-input mr10"
									placeholder="请输入类型内容描述">
								</el-input>
							</el-form-item>

							<el-form-item label="类型状态：" v-if="detailType==1">
								<el-select v-model="choiceItem.typeState" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="coursePosterTypeModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="updateCoursePosterType()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateCoursePosterType()" type="success" size="small" v-if="detailType==1">保存
					</el-button>
				</div>

			</div>
		</el-drawer>

		<!-- 添加标签 -->
		<el-dialog :visible.sync="keyListModal" width="60%" title="添加海报关键词标签" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;" v-if="choiceIndex==0&&choiceItem">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-checkbox-type v-model="choiceItem.keyListArray">
						<el-checkbox-button v-for="(val,index) in keyList" :key="index" :label="val.text">{{ val.text }}
						</el-checkbox-button>
					</el-checkbox-type>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="keyListModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="keyListModal=false" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

		<!-- 添加查看权限 -->
		<el-dialog :visible.sync="authIdListModal" width="60%" title="添加查看权限" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;"
				v-if="(choiceIndex==0||choiceIndex==1)&&choiceItem">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-checkbox-type v-model="choiceItem.authListArray" value-key="id">
						<el-checkbox-button v-for="(val,index) in authList" :key="index" :label="val.id">{{ val.name }}
						</el-checkbox-button>
					</el-checkbox-type>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="authIdListModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="authIdListModal=false" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--视频预览-->
		<el-dialog :visible.sync="posterModal" width="40%" title="视频预览" :mask-closable="false">
			<poster style="width: 100%;height: auto;margin: 0 auto;" :controls="true" :enable-progress-gesture="true"
				:initial-time="0" :show-center-play-btn="true" autoplay :src="posterUrl" custom-cache="false">
			</poster>
		</el-dialog>

	</div>

</template>

<script>
	import Vue from 'vue';

	export default {
		name: "coursePoster",

		data() {
			return {
				// 可设置
				// 是否超级管理员（可执行删除等敏感操作）
				isAdmin: false,

				url: '',
				imageUrl: '',
				posterUrl: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				// 文件上传地址
				fileUploadUrl: 'https://api.xiaoyujia.com/system/uploadFile',
				fileUploadData: {
					route: 'coursePoster'
				},
				uploadFileType: 0,

				// baseUrl: 'http://localhost:8063/',
				baseUrl: 'https://biapi.xiaoyujia.com/',
				uploadUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
				deleteTips: "确定删除选中海报吗？该操作无法恢复，请谨慎操作!",
				isEdit: false,
				showEdit: true,
				imgModal: false,
				posterModal: false,
				coursePosterModal: false,
				coursePosterTypeModal: false,

				coursePosterRecordModal: false,
				keyListModal: false,
				authIdListModal: false,
				detailType: 0,
				loading: true,
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				pageSizeOpts: [20, 50, 100],
				list: [],
				courseList: [],
				teacherList: [],
				liveRoomList: [],
				typeList: [],
				coursePosterRecordList: [],
				coursePosterAnswerRecordList: [],
				keyList: [],
				choiceIndex: '0',
				choiceIndexNow: '0',
				choiceItemIndex: 0,
				choiceItem: null,
				choiceRecordId: 0,
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				signInTips: '',
				searchText: '',
				searchType: null,
				searchState: null,
				searchRequired: null,
				quer: {
					search: "",
					posterState: null,
					typeState: null,
					orderBy: "t.id ASC",
					current: 1,
					size: 20
				},
				switchStateList: [{
						id: 0,
						typeName: "关闭",
						type: "info"
					},
					{
						id: 1,
						typeName: "开启",
						type: "success"
					}
				],
				typeList: [{
					id: 0,
					typeName: "家姐课堂"
				}, {
					id: 1,
					typeName: "家姐联盟"
				}],
				stateList: [{
					value: 0,
					text: "下架",
					type: "info"
				}, {
					value: 1,
					text: "上架",
					type: "success"
				}],
				typeStyleList: [{
						id: 0,
						type: "success"
					},
					{
						id: 1,
						type: "info"
					},
					{
						id: 2,
						type: "warning"
					}, {
						id: 3,
						type: "primary"
					}
				],
				authList: [{
					id: 1,
					name: '普通会员'
				}, {
					id: 2,
					name: '行政员工'
				}, {
					id: 3,
					name: '服务员工'
				}, {
					id: 4,
					name: '保姆月嫂'
				}, {
					id: 5,
					name: '共创店'
				}, {
					id: 6,
					name: '合伙人'
				}, {
					id: 7,
					name: '门店店长'
				}, {
					id: 8,
					name: '陪跑店店长'
				}],
				searchTime: [],
				pickerOptions: {
					shortcuts: [{
						text: '昨天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '今天',
						onClick(picker) {
							picker.$emit('pick', new Date());
						}
					}, {
						text: '明天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周前',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周后',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}]
				},
				pickerOptions1: {
					shortcuts: [{
						text: '今天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '昨天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}]
				}
			}
		},
		created() {
			if (this.$route.query.isAdmin) {
				this.isAdmin = true
			}
			this.getData()
		},
		methods: {
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceItem = null
				this.choiceIndex = key
				this.expands = []
				this.showEdit = true
				this.reset()
			},
			getData() {
				this.choiceIndexNow = 2
				if (this.choiceIndex == 0) {
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'coursePoster/pageCoursePoster',
						data: JSON.stringify(this.quer),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							this.loading = false
							if (res.status == 200) {
								this.list = res.data.records
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
								this.choiceIndexNow = this.choiceIndex
							} else {
								this.list = []
								this.$message.error('未查询到相关海报!')
								this.choiceIndexNow = this.choiceIndex
							}
						},
					})
					this.listCoursePosterType()
					this.listCourse()
				} else if (this.choiceIndex == 1) {
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'coursePoster/pageCoursePosterType',
						data: JSON.stringify(this.quer),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							this.loading = false
							if (res.status == 200) {
								this.list = res.data.records
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
								this.choiceIndexNow = this.choiceIndex
							} else {
								this.list = []
								this.$message.error('未查询到相关类型!')
								this.choiceIndexNow = this.choiceIndex
							}
						},
					})
				}
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.quer.posterState = this.searchState
				this.quer.typeState = this.searchState
				this.quer.posterTypeId = this.searchType
				this.getData()
			},
			// 重置
			reset() {
				this.loading = true
				this.quer.search = ""
				this.quer.posterState = null
				this.quer.typeState = null
				this.quer.posterTypeId = null

				this.quer.orderBy = "t.id ASC"
				this.quer.current = 1

				this.searchText = ''
				this.searchState = null
				this.searchType = null
				this.isEdit = false
				this.getData()
			},
			// 获取类型
			listCoursePosterType() {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'coursePoster/pageCoursePosterType',
					data: JSON.stringify({
						typeState: 1,
						current: 1,
						size: 999
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.status == 200) {
							this.typeList = res.data.records
						} else {
							this.typeList = []
						}
					},
				})
			},
			// 获取课程列表
			listCourse() {
				this.$postData("listCourse", {}).then(res => {
					if (res.status == 200) {
						this.courseList = res.data
					}
				})
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
						this.choiceRecordId = row.id
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "海报图") {
					tips = "推荐尺寸：500*1000(1:2)，海报二维码位置推荐在右下角"
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			formatDuration(duration, defaultStr) {
				if (!defaultStr) {
					defaultStr = '暂未录入'
				}
				let result = ""
				let sencond = 0
				let min = 0
				let hour = 0
				let day = 0
				// 小于一分钟
				if (duration <= 60) {
					sencond = Math.floor(parseFloat(duration / 1.0))
					if (sencond == 0) {
						result = defaultStr
					} else {
						result = sencond + "秒"
					}
				}

				// 小于二小时
				if (duration > 60 && duration <= 3600) {
					min = Math.floor(parseFloat(duration / 60.0))
					sencond = Math.floor(duration - (min * 60)).toFixed(0)
					if (min == 0) {
						result = defaultStr
					} else {
						result = min + "分钟" + sencond + "秒"
					}
				}
				// 大于二小时 小于一天
				if (duration > 3600 && duration <= 86400) {
					hour = Math.floor(parseFloat(duration / 3600))
					min = parseFloat((duration - (hour * 3600)) / 60).toFixed(0)
					if (min == 0) {
						result = hour + "小时"
					} else {
						result = hour + "小时" + min + "分钟"
					}
				}
				// 大于一天
				else if (duration > 86400) {
					day = Math.floor(parseFloat(duration / 86400))
					hour = parseFloat((duration - (day * 86400)) / 3600).toFixed(0)
					if (hour == 0) {
						result = day + "天"
					} else {
						result = day + "天" + hour + "小时"
					}
				}
				return result
			},
			formatTypeStyle(type) {
				return this.typeStyleList[type % 4].type
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 海报Excel模板下载
			excelDownload() {
				this.$postData("excelDownload", null, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('海报Excel模板下载成功!')
					this.blobExport({
						tablename: "海报Excel模板",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.$set(this.choiceItem, "posterImg", res.data)
				} else if (this.uploadImgType == 1) {
					this.$set(this.choiceItem, "typeImg", res.data)
				} else if (this.uploadImgType == 2) {
					this.$set(this.choiceItem, "typeIcon", res.data)
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadId = index
				let tips = ''
				if (this.uploadImgType == 0) {
					tips = "推荐尺寸：500*1000（1：2）"
				} else if (this.uploadImgType == 1) {
					tips = '推荐上传尺寸：250*250（1：1）'
				} else if (this.uploadImgType == 2) {
					tips = '推荐上传尺寸：250*250（1：1）'
				}
				this.$message.info(tips)
			},
			// 打开图片预览
			openImg(url) {
				this.imageUrl = url || this.blankImg
				this.imgModal = true
			},
			// 打开视频预览
			openVideo(url) {
				if (url != null && url != '') {
					this.posterUrl = url
				}
				this.posterModal = true
			},
			openModal(index, index1) {
				if (index == 0) {
					this.detailType = 0
					this.choiceItem = {}
					this.coursePosterModal = true
				} else if (index == 1) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.coursePosterModal = true
				} else if (index == 2) {
					this.detailType = 0
					this.choiceItem = {}
					this.coursePosterTypeModal = true
				} else if (index == 3) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.coursePosterTypeModal = true
				} else if (index == 4) {
					window.open(
						'https://vod.console.aliyun.com/?spm=5176.12672711.categories-n-products.dvod.8f791fa37JVfKI#/media/poster/list'
					)
				}
			},
			//打开员工信息详情
			rowClick(id) {

			},
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			// 添加课程权限列表
			addAuthIdList() {
				this.authIdListModal = false
			},
			// 删除海报权限标签
			closeAuthTags(index, index1) {
				this.choiceItem.authListArray.splice(index1, 1)
			},
			// 添加海报
			updateCoursePoster() {
				let dom = this.choiceItem
				if (!dom.posterName) {
					this.$message.error('请填写海报标题！')
				} else if (!dom.posterTypeId) {
					this.$message.error('请选择海报类型！')
				} else if (!dom.courseId) {
					this.$message.error('请选择关联课程！')
				} else {
					if (!dom.creator) {
						this.$set(dom, "creator", localStorage.getItem('account') || 'admin')
					}
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'coursePoster/updateCoursePoster',
						data: JSON.stringify(dom),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.status == 200) {
								let tips = this.detailType == 0 ? '添加' : '更新'
								this.$message.success('海报' + tips + '成功!')
								this.coursePosterModal = false
								if (this.detailType == 0) {
									this.list.push(res.data)
								}
							} else {
								this.$message.error('海报' + tips + '失败！' + res.msg)
							}
						},
					})
				}
			},
			// 海报类型
			updateCoursePosterType() {
				let dom = this.choiceItem
				if (!dom.typeName) {
					this.$message.error('请填写类型标题！')
				} else {
					if (!dom.creator) {
						this.$set(dom, "creator", localStorage.getItem('account') || 'admin')
					}
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'coursePoster/updateCoursePosterType',
						data: JSON.stringify(dom),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.status == 200) {
								let tips = this.detailType == 0 ? '添加' : '更新'
								this.$message.success('海报类型' + tips + '成功!')
								this.coursePosterTypeModal = false
								this.list.push(res.data)
							} else {
								this.$message.error('海报类型' + tips + '失败！' + res.msg)
							}
						},
					})
				}
			},
			beforeFileUpload() {
				this.loading = true
			},
			// 章节素材文件上传成功
			fileUploadSuccess(res, file) {
				this.loading = false
				if (this.uploadFileType == 0) {
					this.choiceItem.posterUrl = res.data
				}
				this.$message.success('海报文件上传成功！')
			},
			// 章节素材文件上传失败
			fileUploadError(err) {
				this.loading = false
				this.$message.error('海报文件上传失败！请重试！' + err.msg)
			},
			// 课程章节素材文件上传
			fileUpload(value) {
				this.uploadFileType = value
				this.fileUploadUrl = fileUploadUrl
			},
			beforeImgUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!');
				}
				return isLt2M;
			}
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>