<template>
    <div class="table">
        <el-row :gutter="20">
            <el-col :span="24">
                <div class="container" style="padding:20px 30px 0 30px">

                    <h2 >
                        <i class="el-icon-s-shop"></i>
                        {{teamList[0].team.name}}
                        &nbsp;&nbsp;&nbsp;
                        <el-button type="primary" round  icon="el-icon-s-custom" @click="showEmployeeList=!showEmployeeList">{{showEmployeeList?'收起':'查看'}}人员</el-button>
                    </h2>
                    <br>

                </div>
                <div class="container" style="padding:20px 30px 20px 30px;margin-top: 20px" v-if="showEmployeeList">

                    <el-row :gutter="12">
                        <el-col :span="3" v-for="(item,index) in employeeList" :key="index">
                            <el-card shadow="hover">
                                <el-avatar  :src="item.headPortrait"  shape="square" :size="100"></el-avatar>
                                <div><b><i class="el-icon-s-custom"/> &nbsp; {{item.realName}} - {{item.no}}</b></div>
                                <div><i class="el-icon-phone-outline"/> &nbsp; {{item.phone}}</div>
                            </el-card>


                        </el-col>
                    </el-row>


                </div>

                <div class="mybox">
                    <el-row :gutter="20">
                        <el-col :span="8"  v-for="(item,index) in teamList" :key="index" v-if="showMore?index<2:true && showType==0?true:item.team.type==showType" >
                            <el-card shadow="hover">
                                <div slot="header" class="clearfix">
                                    <el-avatar :src="item.team.headImg" size="large" fit="cover" style="float: right"></el-avatar>
                                    <h3>昨日数据</h3>
                                    <h4>队长：{{item.team.leaderName}}</h4>
                                </div>
                                <el-row  v-show="radio1=='当日数据'">
                                    <el-col :span="8"  v-for="(dataItem,dataIndex) in item.teamShowDataList" :key="dataIndex">
                                        <div style="text-align: center;height: 80px;border: 1px solid #f5f6f7">
                                            <div style="font-size: 20px;color: #2d8cf0">
                                                {{dataItem.num}}
                                                <el-badge :value="(((dataItem.num-item.teamShowDataListDay[dataIndex].num)/dataItem.num)*100).toFixed(2)+'%'" class="item" :type="((dataItem.num-item.teamShowDataListDay[dataIndex].num)/dataItem.num)>0?'danger':'primary'"/>
                                            </div>
                                            <div style="font-size: 10px;color: #6f7180"> {{dataItem.paramName}}</div>
                                        </div>

                                    </el-col>
                                </el-row>
                                <el-row  v-show="radio1=='门店参数'">
                                    <el-col :span="8">
                                        <div style="text-align: center;height: 80px;border: 1px solid #f5f6f7">
                                            <div style="font-size: 20px;color: #2d8cf0"> {{item.team.areaNum}}</div>
                                            <div style="font-size: 10px;color: #6f7180"> 面积</div>
                                        </div>
                                    </el-col>
                                    <el-col :span="8">
                                        <div style="text-align: center;height: 80px;border: 1px solid #f5f6f7">
                                            <div style="font-size: 20px;color: #2d8cf0"> {{item.team.rentNum}}</div>
                                            <div style="font-size: 10px;color: #6f7180"> 租金</div>
                                        </div>
                                    </el-col>
                                    <el-col :span="8">
                                        <div style="text-align: center;height: 80px;border: 1px solid #f5f6f7">
                                            <div style="font-size: 20px;color: #2d8cf0"> {{item.team.otherCosts}}</div>
                                            <div style="font-size: 10px;color: #6f7180"> 其他成本</div>
                                        </div>
                                    </el-col>
                                </el-row>

                            </el-card>

                        </el-col>
                        <el-col :span="8"  v-for="(item,index) in teamList" :key="index" v-if="showMore?index<2:true && showType==0?true:item.team.type==showType" >
                            <el-card shadow="hover">
                                <div slot="header" class="clearfix">
                                    <el-avatar :src="item.team.headImg" size="large" fit="cover" style="float: right"></el-avatar>
                                    <h3>门店地图</h3>
                                    <h4>经纬度：{{item.team.lng}},{{item.team.lat}}</h4>
                                </div>

                                <el-row  >
                                    <el-col :span="24">
                                        <addr-map :lng="item.team.lng" :lat="item.team.lat" width="100%" style="height: 400px"></addr-map>
                                    </el-col>

                                </el-row>

                            </el-card>

                        </el-col>
                        <el-col :span="8"  v-for="(item,index) in teamList" :key="index" v-if="showMore?index<2:true && showType==0?true:item.team.type==showType" >
                            <el-card shadow="hover">
                                <div slot="header" class="clearfix">
                                    <el-avatar :src="item.team.headImg" size="large" fit="cover" style="float: right"></el-avatar>
                                    <h3>门店参数</h3>
                                    <h4>队长：{{item.team.leaderName}}</h4>
                                </div>

                                <el-row  >
                                    <el-col :span="8">
                                        <div style="text-align: center;height: 80px;border: 1px solid #f5f6f7">
                                            <div style="font-size: 20px;color: #2d8cf0"> {{item.team.areaNum}}</div>
                                            <div style="font-size: 10px;color: #6f7180"> 面积</div>
                                        </div>
                                    </el-col>
                                    <el-col :span="8">
                                        <div style="text-align: center;height: 80px;border: 1px solid #f5f6f7">
                                            <div style="font-size: 20px;color: #2d8cf0"> {{item.team.rentNum}}</div>
                                            <div style="font-size: 10px;color: #6f7180"> 租金</div>
                                        </div>
                                    </el-col>
                                    <el-col :span="8">
                                        <div style="text-align: center;height: 80px;border: 1px solid #f5f6f7">
                                            <div style="font-size: 20px;color: #2d8cf0"> {{item.team.otherCosts}}</div>
                                            <div style="font-size: 10px;color: #6f7180"> 其他成本</div>
                                        </div>
                                    </el-col>
                                </el-row>

                            </el-card>

                        </el-col>

                    </el-row>
                </div>
                <div class="container" style="padding:20px 30px 20px 30px;margin-top: 20px">
                    <h2><i class="el-icon-date"/>
                        <el-divider direction="vertical"></el-divider>日期数据 &nbsp;
                        <el-divider direction="vertical"></el-divider>
                            <el-date-picker
                                    @change="getTeamDataByDay"
                                    v-model="value2"
                                    type="datetimerange"
                                    :picker-options="pickerOptions"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    align="right">
                            </el-date-picker>
                    </h2>
                    <el-row :gutter="20">
                        <el-col :span="16">
                            <el-tabs v-model="activeParamName" style="padding-top: 10px"  @tab-click="changeChar">
                                <el-tab-pane :label="item.paramName" :name="item.paramKey"  v-for="(item,index) in paramList" :key="index"></el-tab-pane>
                            </el-tabs>
                            <g2column :setCharData="dataByDayList" :heightSize="350"  v-if="showColumn" :showLine="true"></g2column>
                        </el-col>
                        <el-col :span="8">
                            <g2char :setCharData="dataByDayList" :heightSize="350"  v-if="showColumn"></g2char>
                        </el-col>
                    </el-row>

                </div>
                <div class="container" style="padding:20px 30px 20px 30px;margin-top: 20px">
                    <h2>Top榜 &nbsp;
                        <el-radio-group v-model="charDom.passDay" @change="getSumParamsToteamId">
                            <el-radio-button :label="0">今天</el-radio-button>
                            <el-radio-button :label="1">昨天</el-radio-button>
                            <el-radio-button :label="7">七天</el-radio-button>
                            <el-radio-button :label="15">十五天</el-radio-button>
                            <el-radio-button :label="30">三十天</el-radio-button>
                        </el-radio-group>
                        <el-input-number v-model="charDom.passDay" @change="getSumParamsToteamId" :min="0" :max="1000" label="描述文字"></el-input-number>
                        <el-divider direction="vertical"></el-divider>

                    </h2>
                    <el-row :gutter="20">
                        <el-col :span="16">
                            <el-tabs v-model="activeParamName" style="padding-top: 10px"  @tab-click="changeChar">
                                <el-tab-pane :label="item.paramName" :name="item.paramKey"  v-for="(item,index) in paramList" :key="index"></el-tab-pane>
                            </el-tabs>
                            <div>
                                <el-table
                                        v-loading="getParamsDataCharLoading"
                                        element-loading-text="拼命加载中"
                                        element-loading-spinner="el-icon-loading"
                                        element-loading-background="rgba(0, 0, 0, 0.8)"
                                        ref="singleTable"
                                        :data="charList"
                                        highlight-current-row
                                        style="width: 100%">
                                    <el-table-column
                                            label="排名"
                                            type="index"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            property="name"
                                            label="名称"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            property="sumParams"
                                            label="参数值"
                                    >
                                    </el-table-column>


                                </el-table>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <g2char :setCharData="g2CharList" :heightSize="350"  v-if="!getParamsDataCharLoading"></g2char>
                        </el-col>
                    </el-row>

                </div>
            </el-col>

        </el-row>

    </div>

</template>

<script>
    import g2column from "../agent/char/g2column";
    import employeeList from "./common/employeeList";
    import g2char from "../agent/char/g2char";
    import addrMap from "../agent/char/addrMap";

    export default {
        name: "teamInfo",
        components:{
            "g2char":g2char,
            employeeList,
            'addrMap':addrMap,
            'g2column':g2column,
        },
        data() {
            return {
                showEmployeeList:true,
                employeeList:[],
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                showColumn:false,
                value2:null,
                dataByDayList:[],
                dataByDay:{
                    startTime:new Date(),
                    endTime:new Date(),
                    teamParamId:1,
                    teamId:this.$route.query.id,
                },
                id:this.$route.query.id,
                radio1:'当日数据',
                charType:"个人",
                charDom:{
                    teamId:this.$route.query.id,
                    teamParamId:null,
                    passDay:7,
                },
                topDay:'昨日',
                showType:0,
                showMore:false,
                getParamsDataCharLoading:false,
                teamList:[],
                paramList:[],
                charList:[],
                g2CharList:[],
                loading:false,
                activeName:null,
                activeParamName:null,
                types: [
                    {
                        id: 1,
                        name: "新零售"
                    },
                    {
                        id: 2,
                        name: "消费者"
                    },
                    {
                        id: 3,
                        name: "大客户"
                    },
                    {
                        id: 4,
                        name: "一线"
                    },
                ],
            }
        },
        created(){
            this.dataByDay.startTime = new Date();
            this.dataByDay.endTime = new Date();
            this.dataByDay.startTime.setTime(this.dataByDay.startTime.getTime() - 3600 * 1000 * 24 * 7);
            this.value2=[this.dataByDay.startTime,this.dataByDay.endTime]
            this.getTeamList()
            this.teamParamList()
            this.getTeamEmployeeById()
        },
        methods:{
            getTeamEmployeeById(){
                this.$getData("getTeamEmployeeById",{id:this.id} ).then(res => {
                    if (res.status == 200) {
                        this.employeeList=res.data
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getTeamDataByDay(){
                this.dataByDay.startTime =this.value2[0];
                this.dataByDay.endTime = this.value2[1];
                this.showColumn=false
                this.$postData("getTeamDataByDay",this.dataByDay,null ).then(res => {
                    if (res.status == 200) {
                        this.dataByDayList=res.data
                        this.showColumn=true
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            changeChar(ar){
                this.charDom.teamParamId=this.paramList[ar.index].id
                this.dataByDay.teamParamId=this.paramList[ar.index].id
                this.getSumParamsToteamId()
                this.getTeamDataByDay()
                location.href="#char"
            },
            getSumParamsToteamId(){
                if (this.charType=='个人'){
                    return this.getSumParamsToEmployeeId()
                }
                this.getParamsDataCharLoading=true

                this.$postData("getSumParamsToteamId",this.charDom,null).then(res => {
                    if (res.status == 200) {
                        this.charList=res.data
                        this.g2CharList=[]
                        if (this.charList.length>0){
                            this.charList.forEach(v=>{
                                let item={
                                    response:v.name,
                                    num:v.sumParams,
                                }
                                this.g2CharList.push(item)
                            })
                        }
                        console.log(this.g2CharList)
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                    this.getParamsDataCharLoading=false

                })
            },
            getSumParamsToEmployeeId(){
                this.getParamsDataCharLoading=true

                this.$postData("getSumParamsToEmployeeId",this.charDom,null).then(res => {
                    if (res.status == 200) {
                        this.charList=res.data
                        this.g2CharList=[]
                        if (this.charList.length>0){
                            this.charList.forEach(v=>{
                                let item={
                                    response:v.name,
                                    num:v.sumParams,
                                }
                                this.g2CharList.push(item)
                            })
                        }
                        console.log(this.g2CharList)
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                    this.getParamsDataCharLoading=false

                })
            },
            teamParamList(){
                this.$getData("teamParamList", {}).then(res => {
                    if (res.status == 200) {
                        this.paramList = res.data;
                        this.activeParamName=this.paramList[0].paramKey
                        this.charDom.teamParamId=this.paramList[0].id
                        this.dataByDay.teamParamId=this.paramList[0].id
                        this.getSumParamsToteamId()
                        this.getTeamDataByDay()
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            handleClick(tab, event) {
                this.showType=Number(tab.index)
            },
            getTeamList() {
                this.loading = true;
                console.log(this.id)
                this.$postData("teamList",{ teamId: this.id},null).then(res => {
                    this.loading = false;
                    if (res.status === 200) {
                        this.teamList=res.data
                        this.$message({
                            type: 'success',
                            message: '获取成功!'
                        });
                    }
                })
            },
        }

    }
</script>
<style>
    .el-card__header{
        color: white;
        background: #2d8cf0;
    }
     {
        background: #fff;
        color: #2d8cf0;;
    }
</style>

<style scoped>
    .el-divider--horizontal {
        display: block;
        height: 1px;
        width: 100%;
        margin: 12px 0;
    }
    .box-bottom{
        text-align: center;
    }
    .mybox{
        margin-top: 5px;
    }
</style>
