import Vue from 'vue';
import Router from 'vue-router';

Vue.use(Router);

export default new Router({
	mode: "history",
	routes: [{
			path: '/',
			redirect: '/home'
		},
		{
			path: '/',
			component: resolve => require(['../components/common/Home.vue'], resolve),
			meta: {
				title: '自述文件'
			},
			children: [{
					path: '/home',
					component: resolve => require(['../components/page/home.vue'], resolve),
					meta: {
						title: '主页'
					}
				},
				{
					path: '/placeOrder',
					component: resolve => require(['../components/page/order/placeOrder.vue'], resolve),
					meta: {
						title: '立即下单'
					}
				}, {
					path: '/toPlaceOrder',
					component: resolve => require(['../components/page/order/placeOrder.vue'], resolve),
					meta: {
						title: '立即下单'
					}
				},
				{
					path: '/userStatistics',
					component: resolve => require(['../components/page/userStatistics.vue'], resolve),
					meta: {
						title: '用户统计'
					}
				},
				{
					path: '/ordersStatistics',
					component: resolve => require(['../components/page/ordersStatistics.vue'], resolve),
					meta: {
						title: '订单统计'
					}
				},
				{
					path: '/cashList',
					component: resolve => require(['../components/page/cash/cashJournal.vue'], resolve),
					meta: {
						title: '充值统计'
					}
				},
				{
					path: '/productStatistics',
					component: resolve => require(['../components/page/product/productStatistics.vue'],
						resolve),
					meta: {
						title: '售卡业绩'
					}
				},
				{
					path: '/cancelOrders',
					component: resolve => require(['../components/page/cancelOrders.vue'], resolve),
					meta: {
						title: '取消订单'
					}
				},
				{
					path: '/performanceStatistics',
					component: resolve => require(['../components/page/performanceStatistics.vue'],
						resolve),
					meta: {
						title: '业绩统计'
					}
				},
				{
					path: '/qualityReport',
					component: resolve => require(['../components/page/qualityReport.vue'], resolve),
					meta: {
						title: '品质报告'
					}
				},
				{
					path: '/couponList',
					component: resolve => require(['../components/page/couponList.vue'], resolve),
					meta: {
						title: '优惠券管理'
					}
				},

				{
					path: '/channelStatistics',
					component: resolve => require(['../components/page/channelStatistics.vue'],
						resolve),
					meta: {
						title: '渠道统计'
					}
				}, {
					path: '/channelUser',
					component: resolve => require(['../components/page/channelUser.vue'], resolve),
					meta: {
						title: '渠道用户'
					}
				},
				{
					path: '/channelPerformance',
					component: resolve => require(['../components/page/channelPerformance.vue'],
						resolve),
					meta: {
						title: '渠道业绩'
					}
				},
				{
					path: '/channelPromotion',
					component: resolve => require(['../components/page/channel/channelPromotion.vue'],
						resolve),
					meta: {
						title: '渠道推广 '
					}
				},
				{
					path: '/promotionChannel',
					component: resolve => require(['../components/page/channel/promotionChannel.vue'],
						resolve),
					meta: {
						title: '促销渠道数据'
					}
				},
				// {
				//     path: '/channelSetting',
				//     component: resolve => require(['../components/page/channelSetting.vue'], resolve),
				//     meta: { title: '渠道设置' }
				// },

				{
					path: '/flockingStatistics',
					component: resolve => require(['../components/page/flockingStatistics.vue'],
						resolve),
					meta: {
						title: '蜂佣统计'
					}
				},
				{
					path: '/flockingUser',
					component: resolve => require(['../components/page/flockingUser.vue'], resolve),
					meta: {
						title: '蜂佣用户'
					}
				},
				{
					path: '/flockingWelfare',
					component: resolve => require(['../components/page/flockingWelfare.vue'], resolve),
					meta: {
						title: '蜂佣福利'
					}
				},

				{
					path: '/stickerStatistics',
					component: resolve => require(['../components/page/stickerStatistics.vue'],
						resolve),
					meta: {
						title: '管家贴统计'
					}
				},
				{
					path: '/stickerUser',
					component: resolve => require(['../components/page/stickerUser.vue'], resolve),
					meta: {
						title: '管家贴用户'
					}
				},
				{
					path: '/stickerPerformance',
					component: resolve => require(['../components/page/stickerPerformance.vue'],
						resolve),
					meta: {
						title: '管家贴业绩'
					}
				},
				// {
				//     path: '/stickerSetting',
				//     component: resolve => require(['../components/page/stickerSetting.vue'], resolve),
				//     meta: { title: '管家贴设置' }
				// },

				// {
				//     path: '/bi',
				//     component: resolve => require(['../components/page/Bi.vue'], resolve),
				//     meta: { title: 'BI系统' }
				// },
				// {
				//     path: '/dashboard',
				//     component: resolve => require(['../components/page/Dashboard.vue'], resolve),
				//     meta: { title: '系统首页' }
				// },
				// {
				//     path: '/icon',
				//     component: resolve => require(['../components/page/Icon.vue'], resolve),
				//     meta: { title: '自定义图标' }
				// },
				// {
				//     path: '/table',
				//     component: resolve => require(['../components/page/BaseTable.vue'], resolve),
				//     meta: { title: '基础表格' }
				// },
				// {
				//     path: '/tabs',
				//     component: resolve => require(['../components/page/Tabs.vue'], resolve),
				//     meta: { title: 'tab选项卡' }
				// },
				// {
				//     path: '/form',
				//     component: resolve => require(['../components/page/BaseForm.vue'], resolve),
				//     meta: { title: '基本表单' }
				// },
				// {
				//     // 富文本编辑器组件
				//     path: '/editor',
				//     component: resolve => require(['../components/page/VueEditor.vue'], resolve),
				//     meta: { title: '富文本编辑器' }
				// },
				// {
				//     // markdown组件
				//     path: '/markdown',
				//     component: resolve => require(['../components/page/Markdown.vue'], resolve),
				//     meta: { title: 'markdown编辑器' }
				// },
				// {
				//     // 图片上传组件
				//     path: '/upload',
				//     component: resolve => require(['../components/page/Upload.vue'], resolve),
				//     meta: { title: '文件上传' }
				// },
				// {
				//     // vue-schart组件
				//     path: '/charts',
				//     component: resolve => require(['../components/page/BaseCharts.vue'], resolve),
				//     meta: { title: 'schart图表' }
				// },
				// {
				//     // 拖拽列表组件
				//     path: '/drag',
				//     component: resolve => require(['../components/page/DragList.vue'], resolve),
				//     meta: { title: '拖拽列表' }
				// },
				// {
				//     // 拖拽Dialog组件
				//     path: '/dialog',
				//     component: resolve => require(['../components/page/DragDialog.vue'], resolve),
				//     meta: { title: '拖拽弹框' }
				// },
				// {
				//     // 国际化组件
				//     path: '/i18n',
				//     component: resolve => require(['../components/page/I18n.vue'], resolve),
				//     meta: { title: '国际化' }
				// },
				// {
				//     // 权限页面
				//     path: '/permission',
				//     component: resolve => require(['../components/page/Permission.vue'], resolve),
				//     meta: { title: '权限测试', permission: true }
				// },
				{
					path: '/adminOrdersStatistics',
					component: resolve => require(['../components/page/adminOrdersStatistics.vue'],
						resolve),
					meta: {
						title: '订单统计'
					}
				},
				{
					path: '/adminPerformanceStatistics',
					component: resolve => require(['../components/page/adminPerformanceStatistics.vue'],
						resolve),
					meta: {
						title: '业绩统计'
					}
				},
				{
					path: '/adminStickerStatistics',
					component: resolve => require(['../components/page/adminStickerStatistics.vue'],
						resolve),
					meta: {
						title: '管家帖统计'
					}
				},
				{
					path: '/adminStickerUser',
					component: resolve => require(['../components/page/adminStickerUser.vue'], resolve),
					meta: {
						title: '管家帖用户'
					}
				},

				{
					path: '/projectManage',
					component: resolve => require(['../components/page/project/projectMange.vue'],
						resolve),
					meta: {
						title: '项目管理'
					}
				},
				{
					path: '/memberManage',
					component: resolve => require(['../components/page/project/projectMember.vue'],
						resolve),
					meta: {
						title: '成员管理'
					}
				},
				{
					path: '/incomeExpend',
					component: resolve => require([
						'../components/page/project/projectIncomeExpend.vue'
					], resolve),
					meta: {
						title: '收入支出'
					}
				},
				{
					path: '/awardPunish',
					component: resolve => require(['../components/page/project/projectAwardPunish.vue'],
						resolve),
					meta: {
						title: '分润奖罚'
					}
				},

				{
					path: '/site',
					component: resolve => require(['../components/page/site/siteProject.vue'], resolve),
					meta: {
						title: '驻场'
					}
				},
				{
					path: '/sitePage',
					component: resolve => require(['../components/page/site/siteProject.vue'], resolve),
					meta: {
						title: '项目合同'
					}
				},
				{
					path: '/fastExpiringContract',
					component: resolve => require(['../components/page/site/fastExpiringContract.vue'],
						resolve),
					meta: {
						title: '当月过期合同'
					}
				},
				{
					path: '/siteEvaluate',
					component: resolve => require(['../components/page/site/siteEvaluate.vue'],
						resolve),
					meta: {
						title: '项目开票/评价'
					}
				}, {
					path: '/siteArrears',
					component: resolve => require(['../components/page/site/siteArrears.vue'], resolve),
					meta: {
						title: '合同欠款'
					}
				}, {
					path: '/siteDetails',
					component: resolve => require(['../components/page/site/siteProjectDetails.vue'],
						resolve),
					meta: {
						title: '项目详情'
					}
				},
				{
					path: '/siteOrderList',
					component: resolve => require(['../components/page/site/siteOrderList.vue'],
						resolve),
					meta: {
						title: '项目订单'
					}
				},
				{
					path: '/siteWagesList',
					component: resolve => require(['../components/page/site/siteWagesList.vue'],
						resolve),
					meta: {
						title: '员工工资'
					}
				},
				{
					path: '/siteEmployeeJzy',
					component: resolve => require(['../components/page/site/siteEmployeeJzy.vue'],
						resolve),
					meta: {
						title: '员工诉讼查询'
					}
				},
				{
					path: '/salary',
					component: resolve => require(['../components/page/salary/salary.vue'], resolve),
					meta: {
						title: '工资管理'
					}
				},
				{
					path: '/salaryList',
					component: resolve => require(['../components/page/salary/salary.vue'], resolve),
					meta: {
						title: '工资发放',
						num: "123"
					},
					query: {
						num: "123"
					}
				},
				{
					path: '/workOrder',
					component: resolve => require(['../components/page/try/tryProduct.vue'], resolve),
					meta: {
						title: '工单管理'
					}
				},
				{
					path: '/dispatchOrder',
					component: resolve => require(['../components/page/wordOrder/wordOrder.vue'],
						resolve),
					meta: {
						title: '派单工单',
						num: "123"
					},
					query: {
						num: "123"
					}
				},
				{
					path: '/settleOrder',
					component: resolve => require(['../components/page/wordOrder/settleOrder.vue'],
						resolve),
					meta: {
						title: '结算工单'
					}
				},
				{
					path: '/commend',
					component: resolve => require(['../components/page/wordOrder/commend.vue'],
						resolve),
					meta: {
						title: '表彰工单'
					}
				},
				{
					path: '/violation',
					component: resolve => require(['../components/page/wordOrder/violation.vue'],
						resolve),
					meta: {
						title: '违纪工单'
					}
				},
				{
					path: '/tsOrder',
					component: resolve => require(['../components/page/wordOrder/tsOrder.vue'],
						resolve),
					meta: {
						title: '投诉工单'
					}
				},
				{
					path: '/trainOrder',
					component: resolve => require(['../components/page/wordOrder/trainOrder.vue'],
						resolve),
					meta: {
						title: '回炉工单'
					}
				},
				{
					path: '/collect',
					component: resolve => require(['../components/page/wordOrder/collect.vue'],
						resolve),
					meta: {
						title: '领用工单'
					}
				},
				{
					path: '/collect',
					component: resolve => require(['../components/page/wordOrder/storeCollect.vue'],
						resolve),
					meta: {
						title: '门店领用工单'
					}
				}, {
					path: '/demandWorkOrder',
					component: resolve => require(['../components/page/wordOrder/demandWorkOrder.vue'],
						resolve),
					meta: {
						title: '需求工单'
					}
				}, {
					path: '/orderNeedsWorkOrder',
					component: resolve => require([
							'../components/page/wordOrder/orderNeedsWorkOrder.vue'
						],
						resolve),
					meta: {
						title: '线索工单'
					}
				},
				{
					path: '/recharge',
					component: resolve => require(['../components/page/wordOrder/Recharge.vue'],
						resolve),
					meta: {
						title: '充值流水'
					}
				},
				{
					path: '/activityYi',
					component: resolve => require(['../components/page/wordOrder/activityYi.vue'],
						resolve),
					meta: {
						title: '体验报告'
					}
				},
				{
					path: '/userTrack',
					component: resolve => require(['../components/page/userTrack/userTrack.vue'],
						resolve),
					meta: {
						title: '服务跟踪'
					}
				},

				{
					path: '/tryProduct',
					component: resolve => require(['../components/page/try/tryProduct.vue'], resolve),
					meta: {
						title: '商品管理'
					}
				},
				{
					path: '/tryProductDetails',
					component: resolve => require(['../components/page/try/tryProductDetails.vue'],
						resolve),
					meta: {
						title: '商品详情'
					}
				},
				{
					path: '/tryApply',
					component: resolve => require(['../components/page/try/tryProductApply.vue'],
						resolve),
					meta: {
						title: '中奖名单'
					}
				},
				{
					path: '/experienceManagement',
					component: resolve => require(['../components/page/try/experienceManagement.vue'],
						resolve),
					meta: {
						title: '体验管理'
					}
				},
				{
					path: '/wxTempleList',
					component: resolve => require(['../components/page/wxTemple/wxTemple.vue'],
						resolve),
					meta: {
						title: '推送管理'
					}
				},


				//经纪人管理
				{
					path: '/refund',
					component: resolve => require(['../components/page/agent/refund.vue'], resolve),
					meta: {
						title: '退款管理'
					}
				},
				{
					path: '/agentManager',
					component: resolve => require(['../components/page/agent/agent.vue'], resolve),
					meta: {
						title: '经纪人管理'
					}
				},
				{
					path: '/apprentice',
					component: resolve => require(['../components/page/agent/apprentice.vue'], resolve),
					meta: {
						title: '徒弟申请管理'
					}
				},
				{
					path: '/storeDayLog',
					component: resolve => require(['../components/page/agent/storeDayLog.vue'],
						resolve),
					meta: {
						title: '门店日报管理'
					}
				},
				{
					path: '/agentBaomu',
					component: resolve => require(['../components/page/agent/agentBaomu.vue'], resolve),
					meta: {
						title: '保姆管理'
					}
				},
				{
					path: '/baomuInfo',
					component: resolve => require(['../components/page/agent/info/baomuInfo.vue'],
						resolve),
					meta: {
						title: '保姆详情'
					}
				},
				{
					path: '/agentSite',
					component: resolve => require(['../components/page/agent/agentSite.vue'], resolve),
					meta: {
						title: '站点管理'
					}
				}, {
					path: '/agentOrderNeeds',
					component: resolve => require(['../components/page/agent/agentOrderNeeds.vue'],
						resolve),
					meta: {
						title: '需求管理'
					}
				}, {
					path: '/needInfo',
					component: resolve => require(['../components/page/agent/info/needInfo.vue'],
						resolve),
					meta: {
						title: '需求详情'
					}
				},
				{
					path: '/agentOrder',
					component: resolve => require(['../components/page/agent/agentOrder.vue'], resolve),
					meta: {
						title: '订单管理'
					}
				},
				{
					path: '/agentCommunicate',
					component: resolve => require(['../components/page/agent/agentCommunicate.vue'],
						resolve),
					meta: {
						title: '咨询管理'
					}
				},
				{
					path: '/agentContract',
					component: resolve => require(['../components/page/agent/agentContract.vue'],
						resolve),
					meta: {
						title: '合同管理'
					}
				},
				{
					path: '/chinaLifeInsure',
					component: resolve => require(['../components/page/agent/chinaLifeInsure.vue'],
						resolve),
					meta: {
						title: '合同上险'
					}
				},
				{
					path: '/contractInsurance',
					component: resolve => require(['../components/page/agent/contractInsurance.vue'],
						resolve),
					meta: {
						title: '平安保险(保姆月嫂)'
					}
				},
				{
					path: '/agentContractBx',
					component: resolve => require(['../components/page/agent/agentContractBx.vue'],
						resolve),
					meta: {
						title: '保险通知'
					}
				},
				{
					path: '/insuranceManage',
					component: resolve => require(['../components/page/agent/insuranceManage.vue'],
						resolve),
					meta: {
						title: '保险管理'
					}
				}, {
					path: '/reportInsurance',
					component: resolve => require(['../components/page/agent/reportInsurance.vue'],
						resolve),
					meta: {
						title: '报险管理'
					}
				},
				{
					path: '/agentSettlementRule',
					component: resolve => require(['../components/page/agent/agentSettlementRule.vue'],
						resolve),
					meta: {
						title: '结算管理'
					}
				}, {
					path: '/agentJzy',
					component: resolve => require(['../components/page/agent/agentJzy.vue'], resolve),
					meta: {
						title: '认证管理'
					}
				},
				{
					path: '/agentMember',
					component: resolve => require(['../components/page/agent/agentMember.vue'],
						resolve),
					meta: {
						title: '客户管理'
					}
				},
				{
					path: '/test',
					component: resolve => require(['../components/page/agent/test.vue'], resolve),
					meta: {
						title: '测试'
					}
				},
				{
					path: '/orderInfo',
					component: resolve => require(['../components/page/agent/info/order.vue'], resolve),
					meta: {
						title: '订单详情'
					}
				}, {
					path: '/memberInfo',
					component: resolve => require(['../components/page/agent/info/memberInfo.vue'],
						resolve),
					meta: {
						title: '客户详情'
					}
				},
				{
					path: '/agentMemberByPhone',
					component: resolve => require(['../components/page/agent/agentMemberByPhone.vue'],
						resolve),
					meta: {
						title: '客户详情'
					}
				},
				{
					path: '/flyBrid',
					component: resolve => require(['../components/page/agent/info/formInfo.vue'],
						resolve),
					meta: {
						title: '飞鸟任务'
					}
				}, {
					path: '/formInfo',
					component: resolve => require(['../components/page/agent/info/formInfo.vue'],
						resolve),
					meta: {
						title: '问题内容'
					}
				}, {
					path: '/taskPage',
					component: resolve => require(['../components/page/agent/taskPage.vue'], resolve),
					meta: {
						title: '飞鸟任务'
					}
				}, {
					path: '/taskCheckPage',
					component: resolve => require(['../components/page/agent/taskCheckPage.vue'],
						resolve),
					meta: {
						title: '签到记录'
					}
				}, {
					path: '/poster',
					component: resolve => require(['../components/page/agent/poster.vue'], resolve),
					meta: {
						title: '海报中心'
					}
				}, {
					path: '/posterLogPage',
					component: resolve => require(['../components/page/agent/posterLogPage.vue'],
						resolve),
					meta: {
						title: '推广日志'
					}
				}, {
					path: '/taskTypeInfo',
					component: resolve => require(['../components/page/agent/info/taskTypeInfo.vue'],
						resolve),
					meta: {
						title: '飞鸟分析'
					}
				}, {
					path: '/g2char',
					component: resolve => require(['../components/page/agent/char/g2char.vue'],
						resolve),
					meta: {
						title: '图标示例'
					}
				}, {
					path: '/g2column',
					component: resolve => require(['../components/page/agent/char/g2column.vue'],
						resolve),
					meta: {
						title: '图标示例'
					}
				}, {
					path: '/addrMap',
					component: resolve => require(['../components/page/agent/char/addrMap.vue'],
						resolve),
					meta: {
						title: '地图示例'
					}
				}, {
					path: '/baiduHotChar',
					component: resolve => require(['../components/page/agent/char/hotChar.vue'],
						resolve),
					meta: {
						title: '热力图示例'
					}
				}, {
					path: '/orderNeedData',
					component: resolve => require(['../components/page/agent/info/orderNeedData.vue'],
						resolve),
					meta: {
						title: '需求分析'
					}
				}, {
					path: '/agentBaomuData',
					component: resolve => require(['../components/page/agent/agentBaomuData.vue'],
						resolve),
					meta: {
						title: '保姆分析'
					}
				}, {
					path: '/agentStore',
					component: resolve => require(['../components/page/agent/info/agentStore.vue'],
						resolve),
					meta: {
						title: '需求分发规则'
					}
				},

				{
					path: '/couponDto',
					component: resolve => require(['../components/page/data/couponDto.vue'], resolve),
					meta: {
						title: '优惠券数据'
					}
				}, {
					path: '/cmsMaterial',
					component: resolve => require(['../components/page/cms/cmsMaterial.vue'], resolve),
					meta: {
						title: '素材管理'
					}
				}, {
					path: '/cmsEdit',
					component: resolve => require(['../components/page/cms/cmsEdit.vue'], resolve),
					meta: {
						title: '素材编辑'
					}
				}, {
					path: '/cmsMenu',
					component: resolve => require(['../components/page/cms/cmsMenu.vue'], resolve),
					meta: {
						title: '菜单管理'
					}
				}, {
					path: '/cmsRole',
					component: resolve => require(['../components/page/cms/cmsRole.vue'], resolve),
					meta: {
						title: '权限管理'
					}
				}, {
					path: '/MenuIcons',
					component: resolve => require(['../components/page/cms/MenuIcons.vue'], resolve),
					meta: {
						title: '图标'
					}
				}, {
					path: '/cmsPage',
					component: resolve => require(['../components/page/cms/cmsPage.vue'], resolve),
					meta: {
						title: '文章列表'
					}
				}, {
					path: '/employeeSelect',
					component: resolve => require(['../components/page/team/common/employeeSelect.vue'],
						resolve),
					meta: {
						title: '员工选择'
					}
				}, {
					path: '/teamEdit',
					component: resolve => require(['../components/page/team/teamEdit.vue'], resolve),
					meta: {
						title: '队伍编辑'
					}
				}, {
					path: '/teamPage',
					component: resolve => require(['../components/page/team/teamPage.vue'], resolve),
					meta: {
						title: '飞蚁队伍'
					}
				}, {
					path: '/teamInfo',
					component: resolve => require(['../components/page/team/teamInfo.vue'], resolve),
					meta: {
						title: '队伍详情'
					}
				}, {
					path: '/agentAbcOrder',
					component: resolve => require(['../components/page/agent/agentAbcOrder.vue'],
						resolve),
					meta: {
						title: '经纪人ABC单'
					}
				}, {
					path: '/parameterEdit',
					component: resolve => require(['../components/page/parameter/parameterEdit.vue'],
						resolve),
					meta: {
						title: '蜂窝编辑'
					}
				}, {
					path: '/parameterPage',
					component: resolve => require(['../components/page/parameter/parameterPage.vue'],
						resolve),
					meta: {
						title: '蜂窝数据'
					}
				}, {
					path: '/parameterTeam',
					component: resolve => require(['../components/page/parameter/parameterTeam.vue'],
						resolve),
					meta: {
						title: '蜂窝团队'
					}
				}, {
					path: '/orderByLngAndLat',
					component: resolve => require(['../components/page/parameter/orderByLngAndLat.vue'],
						resolve),
					meta: {
						title: '蜂窝订单'
					}
				}, {
					path: '/serviceReceiptOrderNo',
					component: resolve => require([
						'../components/page/serviceReceipt/serviceReceiptOrderNo.vue'
					], resolve),
					meta: {
						title: '签收单--订单号'
					}
				}, {
					path: '/serviceReceiptStaff',
					component: resolve => require([
						'../components/page/serviceReceipt/serviceReceiptStaff.vue'
					], resolve),
					meta: {
						title: '签收单--员工'
					}
				}, {
					path: '/praiseOrder',
					component: resolve => require(['../components/page/serviceReceipt/praiseOrder.vue'],
						resolve),
					meta: {
						title: '员工积分/好评排行榜'
					}
				},
				{
					path: '/earnestMoney',
					component: resolve => require([
						'../components/page/serviceReceipt/earnestMoney.vue'
					], resolve),
					meta: {
						title: '门店保证金交易'
					}
				},
				{
					path: '/timeoutReturnOrder',
					component: resolve => require([
						'../components/page/serviceReceipt/timeoutReturnOrder.vue'
					], resolve),
					meta: {
						title: '超期返单'
					}
				},
				{
					path: '/serviceLanguage',
					component: resolve => require([
						'../components/page/serviceReceipt/serviceLanguage.vue'
					], resolve),
					meta: {
						title: '服务话术'
					}
				},
				{
					path: '/douyinMemberPay',
					component: resolve => require([
						'../components/page/serviceReceipt/douyinMemberPay.vue'
					], resolve),
					meta: {
						title: '抖音先买后付流水'
					}
				},
				{
					path: '/jingangMemberPay',
					component: resolve => require([
						'../components/page/serviceReceipt/jingangMemberPay.vue'
					], resolve),
					meta: {
						title: '金刚先买后付流水'
					}
				},
				{
					path: '/kuaishouMemberPay',
					component: resolve => require([
						'../components/page/serviceReceipt/kuaishouMemberPay.vue'
					], resolve),
					meta: {
						title: '快手先买后付流水'
					}
				},
				{
					path: '/douyinPoiProduct',
					component: resolve => require([
						'../components/page/serviceReceipt/douyinPoiProduct.vue'
					], resolve),
					meta: {
						title: '抖音poi产品'
					}
				},
				{
					path: '/jinGangPoiProduct',
					component: resolve => require([
						'../components/page/serviceReceipt/jinGangPoiProduct.vue'
					], resolve),
					meta: {
						title: '金刚poi产品'
					}
				},
				{
					path: '/serviceReceiptData',
					component: resolve => require([
						'../components/page/serviceReceipt/serviceReceiptData.vue'
					], resolve),
					meta: {
						title: '签收单--数据报表'
					}
				}, {
					path: '/qiyeState',
					component: resolve => require(['../components/page/qiyeweixin/qiyeState.vue'],
						resolve),
					meta: {
						title: '渠道建立'
					}
				}, {
					path: '/channel',
					component: resolve => require(['../components/page/qiyeweixin/channel.vue'],
						resolve),
					meta: {
						title: '活码管理'
					}
				}, {
					path: '/channelCode',
					component: resolve => require(['../components/page/qiyeweixin/channelCode.vue'],
						resolve),
					meta: {
						title: '渠道码管理'
					}
				}, {
					path: '/channelCodeStatistics',
					component: resolve => require([
						'../components/page/qiyeweixin/channelCodeStatistics.vue'
					], resolve),
					meta: {
						title: '渠道码统计'
					}
				}, {
					path: '/establish',
					component: resolve => require(['../components/page/qiyeweixin/establish.vue'],
						resolve),
					meta: {
						title: '内容中心'
					}
				}, {
					path: '/swarmRobots',
					component: resolve => require(['../components/page/qiyeweixin/swarmRobots.vue'],
						resolve),
					meta: {
						title: '群机器人'
					}
				}, {
					path: '/pictureContent',
					component: resolve => require(['../components/page/qiyeweixin/pictureContent.vue'],
						resolve),
					meta: {
						title: '图片'
					}
				}, {
					path: '/graphicsContent',
					component: resolve => require(['../components/page/qiyeweixin/graphicsContent.vue'],
						resolve),
					meta: {
						title: '图文'
					}
				}, {
					path: '/pictureSelection',
					component: resolve => require([
						'../components/page/qiyeweixin/pictureSelection.vue'
					], resolve),
					meta: {
						title: '图片上传'
					}
				}, {
					path: '/statistics',
					component: resolve => require(['../components/page/qiyeweixin/statistics.vue'],
						resolve),
					meta: {
						title: '统计'
					}
				}, {
					path: '/qiyeStaff',
					component: resolve => require(['../components/page/qiyeweixin/qiyeStaff.vue'],
						resolve),
					meta: {
						title: '企微员工'
					}
				}, {
					path: '/qiyeDepartment',
					component: resolve => require(['../components/page/qiyeweixin/qiyeDepartment.vue'],
						resolve),
					meta: {
						title: '企微部门'
					}
				}, {
					path: '/qiyeCustomer',
					component: resolve => require(['../components/page/qiyeweixin/qiyeCustomer.vue'],
						resolve),
					meta: {
						title: '企微客户'
					}
				},

				{
					path: '/jmreport',
					component: resolve => require(['../components/page/jmreport/jmreport.vue'],
						resolve),
					meta: {
						title: '蜂窝报表'
					}
				},
				{
					path: '/creatActivity',
					component: resolve => require(['../components/page/cms/creatActivity.vue'],
						resolve),
					meta: {
						title: '创建活动'
					}
				},
				{
					path: '/activityPage',
					component: resolve => require(['../components/page/cms/activityPage.vue'], resolve),
					meta: {
						title: '蜂窝活动'
					}
				},
				{
					path: '/creatActivityByYingXiao',
					component: resolve => require([
						'../components/page/cms/creatActivityByYingXiao.vue'
					], resolve),
					meta: {
						title: '助力活动创建'
					}
				}, {
					path: '/createActivityByGuessWord',
					component: resolve => require([
						'../components/page/cms/createActivityByGuessWord.vue'
					], resolve),
					meta: {
						title: '猜灯谜创建'
					}
				}, {
					path: '/createActivityByJiuGongGe',
					component: resolve => require([
						'../components/page/cms/createActivityByJiuGongGe.vue'
					], resolve),
					meta: {
						title: '九宫格转盘'
					}
				}, {
					path: '/creatActivityByYingXiaoBoBing',
					component: resolve => require([
						'../components/page/cms/creatActivityByYingXiaoBoBing.vue'
					], resolve),
					meta: {
						title: '博饼活动创建'
					}
				},
				{
					path: '/activityPageByYingXiao',
					component: resolve => require(['../components/page/cms/activityPageByYingXiao.vue'],
						resolve),
					meta: {
						title: '蜂窝营销活动'
					}
				},
				{
					path: '/activitySetting',
					component: resolve => require(['../components/page/activity/activitySetting.vue'],
						resolve),
					meta: {
						title: '每周阿姨活动'
					}
				},
				{
					path: '/themeActivity',
					component: resolve => require(['../components/page/activity/themeActivity.vue'],
						resolve),
					meta: {
						title: '周日主题活动'
					}
				},
				{
					path: '/publicImageUpload',
					component: resolve => require(['../components/page/cms/publicImageUpload.vue'],
						resolve),
					meta: {
						title: '公共图片上传'
					}
				},
				{
					path: '/activityPicContent',
					component: resolve => require(['../components/page/cms/activityPicContent.vue'],
						resolve),
					meta: {
						title: '蜂窝营销活动组件'
					}
				},

				{
					path: '/orderData',
					component: resolve => require(['../components/page/parameter/orderData.vue'],
						resolve),
					meta: {
						title: '蜂窝订单'
					}
				},
				{
					path: '/memberRefundPage',
					component: resolve => require([
						'../components/page/memberRefund/memberRefundPage.vue'
					], resolve),
					meta: {
						title: '微信退款'
					}
				},
				{
					path: '/excellentEmployee',
					component: resolve => require(['../components/page/excellentEmployee.vue'],
						resolve),
					meta: {
						title: '优秀员工管理'
					}
				},
				{
					path: '/unionInform',
					component: resolve => require(['../components/page/inform/unionInform.vue'],
						resolve),
					meta: {
						title: '消息管理'
					}
				},
				{
					path: '/abilityAuth',
					component: resolve => require(['../components/page/abilityAuth.vue'], resolve),
					meta: {
						title: '技能认证'
					}
				},
				{
					path: '/yingxiaoCensus',
					component: resolve => require(['../components/page/yingxiaoCensus.vue'], resolve),
					meta: {
						title: '营销数据统计'
					}
				},
				{
					path: '/employeeScore',
					component: resolve => require(['../components/page/employeeScore.vue'], resolve),
					meta: {
						title: '评分统计'
					}
				},
				{
					path: '/goodsToStore',
					component: resolve => require(['../components/page/storeOperate/goodsToStore.vue'],
						resolve),
					meta: {
						title: '门店库存'
					}
				},
				{
					path: '/operAnalysis',
					component: resolve => require(['../components/page/storeOperate/operAnalysis.vue'],
						resolve),
					meta: {
						title: '经营分析'
					}
				},
				{
					path: '/operIndic',
					component: resolve => require(['../components/page/storeOperate/operIndic.vue'],
						resolve),
					meta: {
						title: '经营指标'
					}
				},
				{
					path: '/communityHeadReview',
					component: resolve => require([
						'../components/page/community/communityHeadReview.vue'
					], resolve),
					meta: {
						title: '团长审核'
					}
				},
				{
					path: '/storeInventoryCount',
					component: resolve => require([
						'../components/page/storeOperate/storeInventoryCount.vue'
					], resolve),
					meta: {
						title: '门店库存盘点'
					}
				},
				{
					path: '/delayOrder',
					component: resolve => require(['../components/page/order/delayOrder.vue'], resolve),
					meta: {
						title: '超时未接单'
					}
				}, {
					path: '/workOrderReport',
					component: resolve => require(['../components/page/wordOrder/workOrderReport.vue'],
						resolve),
					meta: {
						title: '工单报表'
					}
				},
				{
					path: '/workOrderReportMain',
					component: resolve => require(['../components/page/wordOrder/workOrderReportMain'],
						resolve),
					meta: {
						title: '工单报表主页'
					}
				},
				{
					path: '/storePal',
					component: resolve => require(['../components/page/storeOperate/storePal.vue'],
						resolve),
					meta: {
						title: '门店盈亏'
					}
				},
				{
					path: '/register',
					component: resolve => require(['../components/page/partner/register.vue'], resolve),
					meta: {
						title: '合伙人登记'
					}
				},
				{
					path: '/franchseAudit',
					component: resolve => require(['../components/page/partner/franchseAudit.vue'],
						resolve),
					meta: {
						title: '合伙人入驻审核'
					}
				},
				{
					path: '/partnerData',
					component: resolve => require(['../components/page/partner/partnerData.vue'],
						resolve),
					meta: {
						title: '数据统计'
					}
				},
				{
					path: '/recruitPage',
					component: resolve => require(['../components/page/recruit/recruitPage.vue'],
						resolve),
					meta: {
						title: '招聘记录'
					}
				},
				{
					path: '/recruitmentRanking',
					component: resolve => require(['../components/page/recruit/recruitmentRanking.vue'],
						resolve),
					meta: {
						title: '招聘排行榜'
					}
				},
				{
					path: '/orderNeedsTrack',
					component: resolve => require(['../components/page/agent/orderNeedsTrack.vue'],
						resolve),
					meta: {
						title: '线索跟踪'
					}
				},
				{
					path: '/repurchaseStatistics',
					component: resolve => require(['../components/page/repurchaseStatistics.vue'],
						resolve),
					meta: {
						title: '复购统计'
					}
				},
				{
					path: '/franchiseSettlement',
					component: resolve => require([
						'../components/page/settlementManage/franchiseSettlement.vue'
					], resolve),
					meta: {
						title: '门店收入结算'
					}
				}, {
					path: '/smartPartnerWorkOrder',
					component: resolve => require([
						'../components/page/smartPartner/smartPartnerWorkOrder.vue'
					], resolve),
					meta: {
						title: '区域工单'
					}
				}, {
					path: '/storeRegion',
					component: resolve => require([
						'../components/page/smartPartner/storeRegion.vue'
					], resolve),
					meta: {
						title: '区域管理'
					}
				},
				{
					path: '/recruitReturn',
					component: resolve => require([
						'../components/page/settlementManage/recruitReturn.vue'
					], resolve),
					meta: {
						title: '招工返还'
					}
				},
				{
					path: '/declareWithdrawal',
					component: resolve => require([
						'../components/page/settlementManage/declareWithdrawal.vue'
					], resolve),
					meta: {
						title: '一线垫付结算'
					}
				},
				{
					path: '/partTimeJobBalance',
					component: resolve => require([
						'../components/page/settlementManage/partTimeJobBalance.vue'
					], resolve),
					meta: {
						title: '一线员工结算'
					}
				},
				{
					path: '/storeAccountAuditing',
					component: resolve => require([
						'../components/page/settlementManage/storeAccountAuditing.vue'
					], resolve),
					meta: {
						title: '门店账户审核'
					}
				},
				{
					path: '/unionSettlement',
					component: resolve => require([
						'../components/page/settlementManage/unionSettlement.vue'
					], resolve),
					meta: {
						title: '合伙人结算（奖金）'
					}
				},
				{
					path: '/unionRunSettlement',
					component: resolve => require([
						'../components/page/settlementManage/unionRunSettlement.vue'
					], resolve),
					meta: {
						title: '合伙人陪跑结算'
					}
				},
				{
					path: '/aiRunSettlement',
					component: resolve => require([
						'../components/page/settlementManage/aiRunSettlement.vue'
					], resolve),
					meta: {
						title: '加盟商陪跑结算'
					}
				},
				{
					path: '/yiYeSettlement',
					component: resolve => require([
						'../components/page/settlementManage/yiYeSettlement.vue'
					], resolve),
					meta: {
						title: '一野结算'
					}
				},
				{
					path: '/erYeSettlement',
					component: resolve => require([
						'../components/page/settlementManage/erYeSettlement.vue'
					], resolve),
					meta: {
						title: '二野结算'
					}
				},
				{
					path: '/courseList',
					component: resolve => require(['../components/page/course/courseList.vue'],
						resolve),
					meta: {
						title: '课程列表'
					}
				},
				{
					path: '/coursePoster',
					component: resolve => require(['../components/page/course/coursePoster.vue'],
						resolve),
					meta: {
						title: '课程海报'
					}
				},
				{
					path: '/courseComment',
					component: resolve => require(['../components/page/course/courseComment.vue'],
						resolve),
					meta: {
						title: '课程评价'
					}
				},
				{
					path: '/survey',
					component: resolve => require(['../components/page/course/survey.vue'], resolve),
					meta: {
						title: '问卷调查'
					}
				},
				{
					path: '/certificate',
					component: resolve => require(['../components/page/course/certificate.vue'],
						resolve),
					meta: {
						title: '证书管理'
					}
				},
				{
					path: '/courseTeacher',
					component: resolve => require(['../components/page/course/courseTeacher.vue'],
						resolve),
					meta: {
						title: '课程导师'
					}
				},
				{
					path: '/shortVideo',
					component: resolve => require(['../components/page/course/shortVideo.vue'],
						resolve),
					meta: {
						title: '短视频管理'
					}
				},
				{
					path: '/campus',
					component: resolve => require(['../components/page/course/campus.vue'],
						resolve),
					meta: {
						title: '校区管理'
					}
				},
				{
					path: '/courseClockIn',
					component: resolve => require(['../components/page/course/courseClockIn.vue'],
						resolve),
					meta: {
						title: '课程打卡'
					}
				},
				{
					path: '/reservation',
					component: resolve => require(['../components/page/course/reservation.vue'],
						resolve),
					meta: {
						title: '课程预约'
					}
				},
				{
					path: '/iconLibrary',
					component: resolve => require(['../components/page/integral/iconLibrary.vue'],
						resolve),
					meta: {
						title: '图标库'
					}
				},
				{
					path: '/achievement',
					component: resolve => require(['../components/page/integral/achievement.vue'],
						resolve),
					meta: {
						title: '成就管理'
					}
				},
				{
					path: '/integralList',
					component: resolve => require(['../components/page/integral/integralList.vue'],
						resolve),
					meta: {
						title: '积分管理'
					}
				},
				{
					path: '/appletLiveList',
					component: resolve => require(['../components/page/appletLive/appletLiveList.vue'],
						resolve),
					meta: {
						title: '直播列表'
					}
				},
				{
					path: '/liveRoom',
					component: resolve => require(['../components/page/appletLive/liveRoom.vue'],
						resolve),
					meta: {
						title: '录播列表'
					}
				},
				{
					path: '/unmannedLive',
					component: resolve => require(['../components/page/appletLive/unmannedLive.vue'],
						resolve),
					meta: {
						title: '无人直播'
					}
				},
				{
					path: '/qaLibraryType',
					component: resolve => require(['../components/page/qaLibrary/qaLibraryType.vue'],
						resolve),
					meta: {
						title: '问答类型'
					}
				},
				{
					path: '/qaLibraryList',
					component: resolve => require(['../components/page/qaLibrary/qaLibraryList.vue'],
						resolve),
					meta: {
						title: '问答列表'
					}
				},
				{
					path: '/workbenchMenu',
					component: resolve => require(['../components/page/workbench/workbenchMenu.vue'],
						resolve),
					meta: {
						title: '菜单管理'
					}
				},
				{
					path: '/planManage',
					component: resolve => require(['../components/page/czTask/planManage.vue'],
						resolve),
					meta: {
						title: '计划管理'
					}
				},
				{
					path: '/stageManage',
					component: resolve => require(['../components/page/czTask/stageManage.vue'],
						resolve),
					meta: {
						title: '阶段管理'
					}
				},
				{
					path: '/taskManage',
					component: resolve => require(['../components/page/czTask/taskManage.vue'],
						resolve),
					meta: {
						title: '任务管理'
					}
				},
				{
					path: '/formManage',
					component: resolve => require(['../components/page/czTask/formManage.vue'],
						resolve),
					meta: {
						title: '表单管理'
					}
				},
				{
					path: '/exam',
					component: resolve => require(['../components/page/course/exam.vue'], resolve),
					meta: {
						title: '在线考试'
					}
				},
				{
					path: '/qiyeFilesChildren',
					component: resolve => require([
						'../components/page/qiyeweixin/QiyeFilesChildren.vue'
					], resolve),
					meta: {
						title: '文件列表'
					}
				},
				{
					path: '/franchiseList',
					component: resolve => require([
						'../components/page/finance/franchiseList.vue'
					], resolve),
					meta: {
						title: '门店收入清单'
					}
				},
				{
					path: '/contractStoreOrder',
					component: resolve => require([
						'../components/page/finance/contractStoreOrder.vue'
					], resolve),
					meta: {
						title: '承包店结算管理'
					}
				},
				{
					path: '/storeWallet',
					component: resolve => require([
						'../components/page/finance/storeWallet.vue'
					], resolve),
					meta: {
						title: '门店钱包'
					}
				},
				{
					path: '/storeBalanceDetails',
					component: resolve => require([
						'../components/page/finance/storeBalanceDetails.vue'
					], resolve),
					meta: {
						title: '门店钱包明细'
					}
				},
				{
					path: '/withdrawalInvoice',
					component: resolve => require([
						'../components/page/finance/withdrawalInvoice.vue'
					], resolve),
					meta: {
						title: '共创店发票管理'
					}
				},
				{
					path: '/mallProduct',
					component: resolve => require([
						'../components/page/mall/mallProduct.vue'
					], resolve),
					meta: {
						title: '商城产品'
					}
				},{
					path: '/mallCategory',
					component: resolve => require([
						'../components/page/mall/mallCategory.vue'
					], resolve),
					meta: {
						title: '商城产品分类'
					}
				},{
					path: '/mallOrder',
					component: resolve => require([
						'../components/page/mall/mallOrder.vue'
					], resolve),
					meta: {
						title: '商城订单'
					}
				},{
					path: '/mallSku',
					component: resolve => require([
						'../components/page/mall/mallSku.vue'
					], resolve),
					meta: {
						title: '出入库管理'
					}
				},

				//经纪人管理-end
				{
					path: '/404',
					component: resolve => require(['../components/page/404.vue'], resolve),
					meta: {
						title: '404'
					}
				},
				{
					path: '/403',
					component: resolve => require(['../components/page/403.vue'], resolve),
					meta: {
						title: '403'
					}
				}
			]
		},
		{
			path: '/display',
			component: resolve => require(['../components/page/serviceReceipt/display.vue'], resolve),
			meta: {
				title: '服务签收单'
			}
		},
		{
			path: '/login',
			component: resolve => require(['../components/page/Login.vue'], resolve)
		},
		{
			path: '*',
			redirect: '/404'
		},
	]
})
