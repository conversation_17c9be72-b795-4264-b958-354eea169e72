<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">
                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="月份">
                                <el-date-picker
                                        @change="query()"
                                        v-model="dto.month"
                                        type="month"
                                        format="yyyy-MM"
                                        value-format="yyyy-MM"
                                        placeholder="选择月">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="服务员工">
                                <el-input
                                        @input="query()"
                                        clearable
                                        v-model="dto.name"
                                        style="width: 180px"
                                        placeholder="服务员工">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row style="margin-top: 10px">
                        <el-col :span="24">
                            <el-form-item>
                                <el-button type="info"
                                           :loading="loadingExcel"
                                           @click="download"
                                           icon="el-icon-download">导出
                                </el-button>
                                <el-button type="success" icon="el-icon-news"
                                           :loading="wagesMonthBatchLoading"
                                           @click="insertWagesMonth()">创建工资
                                </el-button>
                                <el-button type="success" icon="el-icon-coin"
                                           :loading="calculationLoading"
                                           @click="calculationWagesMonthBatch()">批量计算
                                </el-button>
                                <el-button icon="el-icon-refresh" @click="getData">刷新
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-table :data="list" class="table" ref="multipleTable"  height="450"
                      :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000',textAlign: 'center'}"
                      :cell-style="{textAlign: 'center'}"
                      @selection-change="handleSelectionChange"
                      v-loading="loading">
                <el-table-column
                        type="selection"
                        width="55">
                </el-table-column>
                <el-table-column
                    fixed="left"
                        width="120"
                        prop="name"
                        label="服务员工">
                    <template slot-scope="scope">
                        <span>{{scope.row.employee.realName}}{{scope.row.employee.no}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        width="95"
                        prop="wagesMonth"
                        label="工资月份">
                </el-table-column>
                <el-table-column
                        width="80"
                        prop="basePay"
                        label="基本工资">
<!--                  <template slot-scope="scope">-->
<!--                    <el-input-->
<!--                        @blur="update(scope.row)"-->
<!--                        v-model="scope.row.basePay"-->
<!--                        style="width: 70px"-->
<!--                        placeholder="基本工资">-->
<!--                    </el-input>-->
<!--                  </template>-->
                </el-table-column>
                <el-table-column
                        width="80"
                        prop="appointmentAmount"
                        label="约定金额">
                    <template slot-scope="scope">
                        <el-input
                                @blur="update(scope.row)"
                                v-model="scope.row.appointmentAmount"
                                style="width: 70px"
                                placeholder="约定金额">
                        </el-input>
                    </template>
                </el-table-column>
<!--                <el-table-column-->
<!--                        width="80"-->
<!--                        prop="contractAmount"-->
<!--                        label="合同金额">-->
<!--                </el-table-column>-->
                <el-table-column
                        width="80"
                        prop="workOvertimeAmount"
                        label="加班工资">
                </el-table-column>
              <el-table-column
                  width="80"
                  prop="workAmount"
                  label="岗位工资">
              </el-table-column>
                <el-table-column
                        width="75"
                        prop="fullAmount"
                        label="满勤">
<!--                    <template slot-scope="scope">-->
<!--                        <el-input-->
<!--                                @blur="update(scope.row)"-->
<!--                                v-model="scope.row.fullAmount"-->
<!--                                style="width: 65px"-->
<!--                                placeholder="满勤">-->
<!--                        </el-input>-->
<!--                    </template>-->
                </el-table-column>
                <el-table-column
                        width="90"
                        prop="otherAmount"
                        label="其他奖励">
                  <template slot-scope="scope">
                  <el-input
                      @blur="update(scope.row)"
                      v-model="scope.row.otherAmount"
                      style="width: 65px"
                      placeholder="其他奖励">
                  </el-input>
                  </template>
                </el-table-column>
              <el-table-column
                  width="72"
                  prop="amountRemark"
                  label="奖励备注">
                <template slot-scope="scope">
                  <el-input
                      @blur="update(scope.row)"
                      v-model="scope.row.amountRemark"
                      style="width: 65px"
                      placeholder="其他奖励">
                  </el-input>
                </template>
              </el-table-column>
                <el-table-column
                        width="80"
                        prop="shouldAmount"
                        label="应发工资">
                </el-table-column>
                <el-table-column
                        width="80"
                        prop="deductionAmount"
                        label="扣款金额">
                  <template slot-scope="scope">
                    <el-input
                        @blur="update(scope.row)"
                        v-model="scope.row.deductionAmount"
                        style="width: 65px"
                        placeholder="扣款金额">
                    </el-input>
                  </template>
                </el-table-column>
              <el-table-column
                  width="72"
                  prop="deductionRemark"
                  label="扣款备注">
                <template slot-scope="scope">
                  <el-input
                      @blur="update(scope.row)"
                      v-model="scope.row.deductionRemark"
                      style="width: 65px"
                      placeholder="扣款备注">
                  </el-input>
                </template>
              </el-table-column>
                <el-table-column
                        width="90"
                        prop="socialAmount"
                        label="医社保">
                    <template slot-scope="scope">
                        <el-input
                                @blur="update(scope.row)"
                                :disabled="!scope.row.siteEmployee.social"
                                v-model="scope.row.socialAmount"
                                style="width: 80px"
                                placeholder="医社保">
                        </el-input>
                    </template>
                </el-table-column>
                <el-table-column
                        width="90"
                        prop="providentAmount"
                        label="公积金">
                    <template slot-scope="scope">
                        <el-input
                                @blur="update(scope.row)"
                                :disabled="!scope.row.siteEmployee.provident"
                                v-model="scope.row.providentAmount"
                                style="width: 80px"
                                placeholder="公积金">
                        </el-input>
                    </template>
                </el-table-column>
                <el-table-column
                        width="110"
                        prop="personalAmount"
                        label="个人所得税">
<!--                    <template slot-scope="scope">-->
<!--                        <el-input-->
<!--                                @blur="update(scope.row)"-->
<!--                                v-model="scope.row.personalAmount"-->
<!--                                style="width: 80px"-->
<!--                                placeholder="所得税">-->
<!--                        </el-input>-->
<!--                    </template>-->
                </el-table-column>
                <el-table-column
                        width="80"
                        prop="actualAmount"
                        label="实发工资">
                </el-table-column>
                <el-table-column
                        fixed="right"
                        width="100"
                        label="操作">
                    <template slot-scope="scope">
                        <el-row>
                            <el-col :span="24">
                                <div style="display: flex">
                                    <el-button
                                            size="small "
                                            type="text"
                                            @click="calculationWagesMonth(scope.row.id)"
                                            icon="el-icon-coin">
                                        计算
                                    </el-button>
                                </div>
                            </el-col>
                        </el-row>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :current="this.dto.pageNum"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="10"/>
            </div>
        </div>
    </div>

</template>

<script>
    import moment from 'moment'

    export default {
        props: ['projectId'],
        data() {
            return {
                detailIds: [],
                wagesMonthBatchLoading:false,
                calculationLoading: false,
                loading: true,
                loadingExcel: false,
                list: null,
                pageSizeOpts: [10, 20, 30],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                dto: {
                    fileName: "工资条",
                    month: null,
                    projectId: this.projectId,
                    name: null,
                    pageSize: 10,
                    pageNum: 1,
                },
            };
        },
        components: {},
        watch: {
            projectId: {
                handler(newValue, oldValue) {
                    this.projectId = newValue;
                    this.getData();
                }
            }
        },
        created() {
            this.dto.month = moment(new Date()).format('YYYY-MM');
            this.getData();
        },
        computed: {},
        methods: {
            insertWagesMonth() {
              let establish={
                month: this.dto.month,
                projectId: this.projectId,
              }
                this.wagesMonthBatchLoading=true;
                this.$postData("siteWagesDetails_insertWagesMonthByProjectId", establish).then(res => {
                    this.wagesMonthBatchLoading=false;
                    if (res.status === 200) {
                        this.getData();
                        this.$message({
                            type: 'success',
                            message: '操作成功!'
                        });
                    }
                })
            },
            download() {
                this.loadingExcel = true;
                this.$postData("siteWagesDetails_download", this.dto, {
                    responseType: "arraybuffer"
                }).then(res => {
                    let name='';
                    if( this.dto.month!=null){
                        name=this.dto.month
                    }
                    this.loadingExcel = false;
                    this.blobExport({
                        tablename:name+"月份工资信息",
                        res: res
                    });
                })
            },
            handleSelectionChange(list) {
                console.log(list);
                let ids = [];
                list.forEach(item => {
                    ids.push(item.id)
                });
                this.detailIds = ids;
            },
            calculationWagesMonthBatch() {
                if (this.detailIds.length <= 0) {
                    this.$message.error("请勾选人员");
                    return
                }
                this.calculationLoading = true;
                this.$postData("siteWagesDetails_calculationWagesMonthBatch", {detailIds: this.detailIds}).then(res => {
                    this.calculationLoading = false;
                    if (res.status === 200) {
                        this.detailIds = [];
                        this.getData();
                        this.$message({
                            type: 'success',
                            message: '操作成功!'
                        });
                    }
                })
            },
            update(item) {
                this.$postData("siteWagesDetails_update", item).then(res => {
                    if (res.status === 200) {
                        this.calculationWagesMonth(item.id);
                    }
                })
            },
            calculationWagesMonth(id) {
                this.$postUrl("siteWagesDetails_calculationWagesMonth", id).then(res => {
                    if (res.status === 200) {
                        this.getData();
                        this.$message({
                            type: 'success',
                            message: '操作成功!'
                        });
                    }
                });
            },
            getData() {
                this.loading = true;
                this.$postData("siteWagesDetails_selectPage", this.dto).then(res => {
                    this.loading = false;
                    if (res.status === 200) {

                        this.list = res.data.list;
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    }
                });
            },
            query() {
                this.loading = true;
                this.dto.pageNum = 1;

                this.getData();
            },

            // 页码大小
            onPageSizeChange(size) {
                this.loading = true;
                this.dto.pageSize = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                this.loading = true;
                this.dto.pageNum = index;
                this.getData();
            },
            exportExcel() {
                this.loadingExcel = true;
                this.$postData("tsExportList", this.dto, {
                    responseType: "arraybuffer"
                }).then(res => {
                   ;
                    this.loadingExcel = false;
                    this.blobExport({
                        tablename: "投诉工单",
                        res: res
                    });
                });
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
        }
    };
</script>

<style scoped>
    .handle-box {
        /*margin-bottom: 10px;*/
        font-weight: bold !important;
    }

    .el-form-item__label {
        font-weight: bold !important;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;

    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }

    .container {
        padding: 10px !important;
        background: #fff;
        border: none !important;
        border-radius: 5px;
    }

    .el-date-editor .el-range-separator {
        padding: 0 5px;
        line-height: 32px;
        width: 7%;
        color: #303133;
    }
</style>
