<template>
    <div class="table">
       {{text}}
    </div>

</template>

<script>
    export default {
        name: "agentMemberByPhone",
        data() {
            return {
                text:" 获取客户详情中，请稍等",
                model: {
                    name:null,
                    bindTel:this.$route.query.bindTel,
                    current: 1,
                    size: 10
                },
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                list:[]
            }
        },
        created(){
            if (this.model.bindTel!==null && this.model.bindTel!==undefined){
                this.getData()
            }else {
                this.text="客户信息有误，请重新获取或者联系后台工作人员"
            }

        },
        methods:{
            getData() {
                this.$postData("memberPage", this.model, {}).then(res => {
                    if (res.status == 200) {

                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                        if (this.list.length>0){
                            this.$router.push({path:'/memberInfo',query:{"id":this.list[0].id }})
                        }
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
        }
    }
</script>

<style scoped>

</style>
