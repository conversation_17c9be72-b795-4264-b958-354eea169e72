<template>
    <div>

        <el-upload
                :limit="1"
                ref="upload"
                class="upload-demo"
                action="https://biapi.xiaoyujia.com/files/uploadFiles"
                list-type="picture"
                :on-success="handleAvatarSuccess"><!--:show-file-list="false"-->
            <el-button size="mini" type="primary" icon="el-icon-upload">上传纸质合同</el-button>
        </el-upload>
        <br>
        <el-popconfirm
                title="确定将合同转为已完成状态吗？"
                @confirm="save(dom,2)"
                v-if="dom.status!==2"
        >
            <el-button size="mini"  type="success" icon="el-icon-check"   slot="reference">合同转为完成状态</el-button>

        </el-popconfirm>


        <el-row style="padding-top: 10px">
            <el-col :span="4" v-for="(o, index) in contractEnclosureList" :key="index" style="padding: 5px">
                <el-card :body-style="{ padding: '0px' }">
                    <el-image :src="o.enclosureUrl" class="image" :preview-src-list="srcList"></el-image>
                    <div style="padding: 14px;">
                        <div class="bottom clearfix">
                            <time class="time">{{ o.creatTime }}</time>
                            <el-popconfirm
                                    title="这是一段内容确定删除吗？"
                                    @onConfirm="contractEnclosureDel(o.id)"
                            >
                                <el-button type="danger" icon="el-icon-delete"  slot="reference" >删除</el-button>
                            </el-popconfirm>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>
      import {dateFormat} from 'vux'
    export default {
        name: "contractEnclosure",
        props:['model'],
        data() {
            return {
                srcList:[],
                dom:this.model,
                contractEnclosure:{
                    contractId:this.model.id,
                    creatId:localStorage.getItem("id"),
                    enclosureUrl:null,
                },
                contractEnclosureList:[],
            }
        },
        created(){
            this.getList();
        },
        methods:{
            handleAvatarSuccess(res, file){
                this.contractEnclosure.enclosureUrl=res.data;
                this.$postData("contractEnclosureSave", this.contractEnclosure, {}).then(res => {
                    if (res.status == 200) {
                        this.enclosureUrl=null
                        this.getList();
                        this.$refs.upload.clearFiles()

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getList(){
                this.$postData("contractEnclosureList", this.contractEnclosure, {}).then(res => {
                    if (res.status == 200) {
                        this.srcList=[]
                        this.contractEnclosureList=res.data
                        if (res.data.length>0){
                            res.data.forEach(v=>{
                                this.srcList.push(v.enclosureUrl)

                            })
                        }


                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            contractEnclosureDel(id){
                this.$getData("contractEnclosureDel",{"id":id}).then(res => {
                    if (res.status == 200) {
                        this.getList();

                    } else {
                        this.$message.error("删除失败，" + res.msg);
                    }
                })
            },
            save(dom,state) {
                //console.log(dom)
                if (dom.holidayId==null && dom.contractType==0){
                    return  this.$Message.error('请先选择放假规则');
                }
                dom.status=state;
                dom.memberSignDate = dateFormat(new Date(), 'YYYY-MM-DD HH:mm:ss')
                dom.employeeSignDate = dateFormat(new Date(), 'YYYY-MM-DD HH:mm:ss')
                this.$postData("update_contract", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.$Message.success('修改成功');
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })

            },
        }
    }
</script>

<style scoped>
    .time {
        padding-right: 50px;
        font-size: 13px;
        color: #999;
    }

    .bottom {
        margin-top: 13px;
        line-height: 12px;
    }

    .button {
        padding: 0;
        float: right;
    }

    .image {
        width: 100%;
        height: 200px;
        display: block;
    }

    .clearfix:before,
    .clearfix:after {
        display: table;
        content: "";
    }

    .clearfix:after {
        clear: both
    }
</style>
