<template>
  <div class="table">
    <div class="container">
      <div class="handle-box">
        <el-form ref="form" :model="pageInfo">
          <el-row>
            <el-col :span="5">
              <el-form-item label="名称">
                <el-input
                    clearable
                    v-model="dto.name"
                    placeholder="名称/总订单号"
                    style="width:190px"
                    class="handle-input mr10"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="状态">
                <Select filterable clearable style="width: 120px" v-model="dto.state"
                        @on-change="query()">
                  <Option value="">请选择</Option>
                  <Option value="1">正常</Option>
                  <Option value="2">过期</Option>
                  <!--<Option v-for="item in stateList" :value="item.id">{{ item.text}}</Option>-->
                </Select>
              </el-form-item>
            </el-col>
            <el-col :span="15" style="padding-left: 1rem">
              <el-form-item label="签订时间">
                <el-date-picker
                    v-model="dto.days"
                    type="daterange"
                    :picker-options="pickerOptions"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
                <el-button type="primary"
                           @click="query()"
                           icon="el-icon-search"
                           style="margin-left: 20px">搜索
                </el-button>
                <el-button
                    icon="el-icon-refresh"
                    @click="reset()"
                    style="margin-left: 20px">
                  重置
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row style="margin-top: 10px">
            <el-col :span="24">
              <el-form-item>
                <el-button type="success" icon="el-icon-plus" @click="modalUp=true">新增</el-button>
                <el-button type="warning" icon="el-icon-upload2" @click="isModal=true">导入
                </el-button>
                <el-button type="info"
                           :loading="loadingExcel"
                           @click="download"
                           icon="el-icon-download">导出
                </el-button>
                <el-button type="primary" icon="el-icon-refresh" @click="synchronizationOrder()">同步（员工/订单）</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-table :data="list" class="table" ref="multipleTable"
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}"
                @selection-change="handleSelectionChange"
                v-loading="loading">
        <el-table-column
            type="selection"
            width="55">
        </el-table-column>
        <el-table-column
            fixed="right"
            width="130"
            label="操作">
          <template slot-scope="scope">
            <el-row>
              <el-col :span="24">
                <div style="display: flex">
                  <el-button
                      size="small "
                      icon="el-icon-thumb"
                      @click="edit(scope.row.id)"
                      type="text">查看
                  </el-button>
                  <!--<el-button-->
                  <!--size="small "-->
                  <!--type="text"-->
                  <!--icon="el-icon-edit">-->
                  <!--修改-->
                  <!--</el-button>-->
                  <el-button
                      size="small "
                      type="text"
                      @click="deleteProject(scope.row.id)"
                      icon="el-icon-delete">
                    删除
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </template>
        </el-table-column>
        <el-table-column
            prop="name"
            width="200"
            label="姓名">
        </el-table-column>
        <el-table-column
            width="170"
            prop="no"
            label="总订单号">
        </el-table-column>
        <el-table-column
            width="100"
            prop="basePay"
            label="基本工资"
        ></el-table-column>
        <el-table-column
            width="100"
            prop="contractAmount"
            label="合同工资"
        ></el-table-column>
        <el-table-column
            width="170"
            prop="signTime"
            label="签订时间">
        </el-table-column>
        <el-table-column
            width="170"
            prop="expireTime"
            label="到期时间">
        </el-table-column>
        <el-table-column
            width="80"
            prop="state"
            label="状态">
          <template slot-scope="scope">
                        <span style="font-size: 15px" v-if=" scope.row.state===1">
                            正常
                        </span>
            <span style="font-size: 15px;color: red" v-if=" scope.row.state===2">
                            过期
                        </span>
          </template>
        </el-table-column>
        <el-table-column
            width="100"
            prop="employee.realName"
            label="经理"
        ></el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="pageInfo.total"
              @on-change="onChange"
              :current="this.dto.pageNum"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onPageSizeChange"
              :page-size="10"/>
      </div>
    </div>
    <Modal v-model="isModal" class="Modal" :width="screenWidth" title="批量导入"
           :mask-closable="false"
           @on-cancel="getData()">
      <div class="addBody">
        <site-project-excel v-if="isModal" @init-choose="initChooseProject"
                            @close-modal="closeCurrModal"></site-project-excel>
      </div>
      <div slot="footer">
      </div>
    </Modal>
    <site-project-insert v-if="modalUp"
                         @init-choose="initChooseProject">
    </site-project-insert>
    <el-drawer :visible.sync="synchro" direction="rtl" size="90%">
      <el-button size="mini" icon="el-icon-refresh" type="primary" @click="synchronizations()">一键同步</el-button>
    <el-table  :data="siteProjectList" style="width: 100%;">
      <el-table-column label="姓名" width="90">
        <template slot-scope="scope">
            <span class="name-wrapper">
              <el-tag size="medium">{{ scope.row.name }}</el-tag>
            </span>
        </template>
      </el-table-column>
      <el-table-column label="工号" width="80">
        <template slot-scope="scope">
            <span class="name-wrapper">
              <el-tag size="medium">{{ scope.row.no }}</el-tag>
            </span>
        </template>
      </el-table-column>
      <el-table-column label="公司" width="160">
        <template slot-scope="scope">
          <span style="margin-left: 10px">{{scope.row.company }}</span>
        </template>
      </el-table-column>
      <el-table-column label="平台" width="60">
        <template slot-scope="scope">
          <p>bi:</p>
          <p>云:</p>
        </template>
      </el-table-column>
      <el-table-column label="银行卡号" width="160">
        <template slot-scope="scope">
          <p  :class="scope.row.bankCard!==scope.row.bankCard2  ? 'blue' : 'orange' ">&nbsp;{{ scope.row.bankCard }}</p>
          <p  :class="scope.row.bankCard!==scope.row.bankCard2  ? 'blue' : 'orange' ">&nbsp;{{ scope.row.bankCard2 }}</p>
        </template>
      </el-table-column>
      <el-table-column label="身份证" width="160">
        <template slot-scope="scope">
          <p :class="scope.row.idCard!==scope.row.idCard2  ? 'blue' : 'orange' ">&nbsp;{{ scope.row.idCard }}</p>
          <p :class="scope.row.idCard!==scope.row.idCard2  ? 'blue' : 'orange' ">&nbsp;{{ scope.row.idCard2 }}</p>
        </template>
      </el-table-column>
      <el-table-column label="电话" width="110">
        <template slot-scope="scope">
          <p :class="scope.row.phone!==scope.row.phone2  ? 'blue' : 'orange' ">&nbsp;{{ scope.row.phone }}</p>
          <p :class="scope.row.phone!==scope.row.phone2  ? 'blue' : 'orange' ">&nbsp;{{ scope.row.phone2 }}</p>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button size="mini" icon="el-icon-refresh" type="primary" @click="synchronization(scope.row)">同步</el-button>
        </template>
      </el-table-column>
    </el-table>
      <div class="pagination">
        <Page :total="pageInfo2.total"
              @on-change="change"
              :current="this.dom.current"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="sizeOpts"
              @on-page-size-change="pageSizeChange"
              :page-size="10"/>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import siteProjectInsert from '@/components/page/site/siteProjectInsert.vue'
import siteProjectExcel from '@/components/page/site/siteProjectExcel.vue'

export default {
  data() {
    return {
      pickerOptions: {
        onPick: obj => {
          this.pickerMinDate = new Date(obj.minDate).getTime();
        },
        disabledDate: time => {
          if (this.pickerMinDate) {
            const day1 = 366 * 10 * 24 * 3600 * 1000;
            let maxTime = this.pickerMinDate + day1;
            let minTime = this.pickerMinDate - day1;
            return time.getTime() > maxTime || time.getTime() < minTime;
          }
        }
      },
      loading: true,
      loadingExcel: false,
      isModal: false,
      isModal1: false,
      modalUp: false,
      synchro: false,
      siteProjectList: null,
      list: null,
      screenWidth: "35%",
      sizeOpts: [10, 20, 30],
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      pageInfo2: {total: 10, size: 10, current: 1, pages: 1},
      dto: {
        name: null,
        days: [],
        startTime: null,
        endTime: null,
        state: 1,
        expire: null,
        pageSize: 10,
        pageNum: 1,
      },
      //分页
      dom: {
        current: 1,
        size: 10,
      },
    };
  },
  components: {
    "siteProjectInsert": siteProjectInsert,
    "siteProjectExcel": siteProjectExcel,
  },
  created() {
    this.getData();
    this.onLoad();
  },
  computed: {},
  methods: {
    synchronization(id) {
      this.$postData("siteProject_updateBySiteEmployeeId", id).then(res => {
        if (res.status === 200) {
          if(res.data===true){
            this.onLoad();
          }else {

          }
        } else {
        }
      })
    },
    synchronizationOrder(){
      this.synchro=true
    },
    synchronizations(){
      this.$getData("synchronizations").then(res => {
        if (res.status === 200) {
          if(res.data===true){
            this.onLoad();
          }else {
          }
        } else {
        }
      })
    },
    download() {
      this.loadingExcel = true;
      this.$postData("siteProject_download", this.dto, {
        responseType: "arraybuffer"
      }).then(res => {
        this.loadingExcel = false;
        this.blobExport({
          tablename: "项目合同",
          res: res
        });
      })
    },
    reset() {
      this.dto.name = null;
      this.dto.days = [];
      this.dto.startTime = null;
      this.dto.endTime = null;
      this.dto.state = null;
      this.getData();
    },
    handleSelectionChange(val) {
      console.log(val)
    },
    deleteProject(id) {
      this.$confirm('此操作将永久删除该合同, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$getData("siteProject_delete", {id: id}).then(res => {
          this.getData()
        });
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    onLoad() {
      this.$postData("siteProject_selectSiteEmployee", this.dom).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.siteProjectList = res.data.records;
          this.pageInfo2.current = res.data.current;
          this.pageInfo2.size = res.data.size;
          this.pageInfo2.total = res.data.total
        }
      })
    },
    getData() {
      this.dto.startTime = null;
      this.dto.endTime = null;
      if (this.dto.days != null && this.dto.days.length > 0) {
        this.dto.startTime = this.dto.days[0];
        this.dto.endTime = this.dto.days[1]
      }
      this.$postData("siteProject_selectPage", this.dto).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      });
    },
    edit(id) {
      // this.$router.push({path: '/siteDetails', query: {"id": id, tabName: "first"}})
      let url = window.location.href.split("/")[0];
      window.open(url + "/siteDetails?id=" + id + "&tabName=first")
    },
    /**
     * 关闭当前界面弹出的窗口
     * */
    closeCurrModal(data) {
      this.isModal = false;
      this.isModal1 = false;
      this.modalUp = false;
    },
    initChooseProject(data) {
      this.closeCurrModal();
      this.getData()
    },
    query() {
      this.loading = true;
      this.dto.pageNum = 1;
      this.getData();
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dto.pageSize = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dto.pageNum = index;
      this.getData();
    },
    // 跳转页码
    change(index) {
      this.loading = true;
      this.dom.current = index;
      this.onLoad();
    },
    pageSizeChange(size){
      this.loading = true;
      this.dom.size = size;
      this.getData();
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
  },
};
</script>

<style scoped>
.handle-box {
  /*margin-bottom: 10px;*/
  font-weight: bold !important;
}

.el-form-item__label {
  font-weight: bold !important;
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}

.del-dialog-cnt {
  font-size: 16px;
  text-align: center;
}

.table {
  width: 100%;
  font-size: 13px;

}

.red {
  color: #ff0000;
}

.mr10 {
  margin-right: 10px;
}

.el-date-editor .el-range-separator {
  padding: 0 5px;
  line-height: 32px;
  width: 7%;
  color: #303133;
}
table {
  border-collapse: collapse;/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
  border-left:#C8B9AE solid 1px;
  border-top:#C8B9AE solid 1px;
}
table td{
  border-right:#C8B9AE solid 1px;
  border-bottom:#C8B9AE solid 1px;
}

th {
  background: #eee;
  font-weight: normal;
}
tr {
  background: #fff;
}
tr:hover {
  background: #cc0;
}
.blue {
  color: red;
}
.orange {
  color:#000000 ;
}


</style>
