<template>
  <div class="table">
    <div class="container">
      <div class="handle-box">
        <el-form ref="form" :model="pageInfo">
          <el-row>
            <el-col :span="5">
              <el-form-item label="工号">
                <el-input
                    clearable
                    v-model="dto.no"
                    placeholder="姓名/工号"
                    style="width:180px"
                    class="handle-input mr10"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="商品名称">
                <el-input
                    clearable
                    v-model="dto.goodsName"
                    placeholder="商品名称"
                    style="width:180px"
                    class="handle-input mr10"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5" style="text-align: left">
              <el-form-item label="商品分类">
                  <Select filterable clearable style="width: 120px" v-model="dto.categoryUid">
                    <Option value="">请选择</Option>
                    <Option v-for="item in categoryList" :value="item.uid" :key="item.id">{{ item.name }}</Option>
                  </Select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="归属门店">
                <label>
                  <el-select filterable clearable style="width: 180px" v-model="dto.storeid"
                          >
                    <el-option
                        v-for="(item, index) in optionStore"
                        :key="index"
                        :label="item.storeName"
                        :value="item.id">
                    </el-option>
                  </el-select>
                </label>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="5">
              <el-form-item label="领取时间">
                <el-date-picker
                    v-model="dto.startTime"
                    type="datetime"
                    placeholder="开始时间"
                    format="yyyy-MM-dd HH:mm"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width:180px"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6" style="padding-left: 1rem">
              <el-form-item label="至">
                <el-date-picker
                    v-model="dto.endTime"
                    type="datetime"
                    placeholder="结束时间"
                    style="width:180px"
                    format="yyyy-MM-dd HH:mm"
                    value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="领用状态">
                <label>
                  <Select filterable clearable style="width: 120px" v-model="dto.state"
                          @on-change="query()">
                    <Option value="">请选择</Option>
                    <Option value="1">待出库</Option>
                    <Option value="2">已出库</Option>
                    <Option value="3">待核销</Option>
                    <Option value="4">已核销</Option>
                  </Select>
                </label>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" style="text-align: right">
              <el-form-item>
                <el-button type="primary" style="width:8%" @click="query()">搜索</el-button>
                <el-button type="success" :loading="loadingExcel" @click="exportExcel">导出Excel
                </el-button>
                <el-button type="success" @click="modal=true">同步商品
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-table :data="list" border class="table" ref="multipleTable"
                v-loading="loading">
        <!--<el-table-column-->
        <!--type="selection"-->
        <!--width="55">-->
        <!--</el-table-column>-->
<!--        <el-table-column-->
<!--            width="120"-->
<!--            label="操作"></el-table-column>-->
<!--          <template slot-scope="scope">-->
<!--            <div style="text-align: center">-->
<!--              <div>-->
<!--                <el-button size="mini" @click="goDetail(scope.row.id)" type="primary">编辑</el-button>-->
<!--              </div>-->
<!--            </div>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column
            prop="billNo"
            width="140"
            label="工号">
          <template slot-scope="scope">
            <span>{{ scope.row.employee.no + scope.row.employee.realName }}</span>
          </template>
        </el-table-column>
        <el-table-column
            width="100"
            prop="category.name"
            label="商品分类">
        </el-table-column>
        <el-table-column
            width="150"
            prop="goods.name"
            label="商品名称"
        ></el-table-column>
        <el-table-column
            width="90"
            prop="typeDesc"
            label="领用状态">
          <template slot-scope="scope">
                        <span style="font-size: 12px" v-if="scope.row.state===1">
                            待出库
                        </span>
            <span style="font-size: 12px" v-else-if="scope.row.state===2">
                            已出库
                        </span>
            <span style="font-size: 12px" v-else-if="scope.row.state===3">
                            待核销
                        </span>
            <span style="font-size: 12px" v-else-if="scope.row.state===4">
                            已核销
                        </span>
          </template>
        </el-table-column>
        <el-table-column
            width="80"
            label="数量"
            prop="number"
        ></el-table-column>
        <el-table-column
            width="90"
            prop="size"
            label="规格"
        ></el-table-column>
        <el-table-column
            width="100"
            prop="sellPrice"
            label="单价"
        ></el-table-column>
        <el-table-column
            width="100"
            prop="amount"
            label="金额"
        ></el-table-column>
        <el-table-column
            width="150"
            prop="billNo"
            label="订单号"
        ></el-table-column>
        <el-table-column
            width="150"
            prop="createTime"
            label="领用时间"
        ></el-table-column>
        <el-table-column
            width="250"
            label="归属门店">
          <template slot-scope="scope">
            <div v-for="(item ,i) in optionStore">
              <div v-if="scope.row.storeid == item.id">
                {{item.storeName}}
              </div>
            </div>
          </template>

        </el-table-column>
      </el-table>

      <div class="pagination">
        <Page :total="pageInfo.total"
              @on-change="onChange"
              :current="this.dto.pageNum"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onPageSizeChange"
              :page-size="10"/>
      </div>
    </div>
    <collect-choose v-if="modal"
                       @init-choose="initChooseProject">
    </collect-choose>

  </div>

</template>

<script>
import collectChoose from '@/components/page/wordOrder/choose/collectChoose.vue'

export default {
  data() {
    return {
      modal: false,
      loading: true,
      loadingExcel: false,
      screenWidth: '40%',
      categoryList: [],
      optionStore:[],
      list: [],
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      dto: {
        no: null,
        startTime: null,
        endTime: null,
        state: null,
        pageSize: 10,
        pageNum: 1,
        categoryUid: null,
        goodsName: null,
        storeid:null,
      },
    };
  },
  components: {
    "collectChoose": collectChoose,
  },
  created() {
    this.getData();
    this.getCategoryList();
    this.getStore();
  },
  computed: {},
  methods: {
    getCategoryList() {
      this.$getData("collectRecordList_selectCategoryAll").then(res => {
        if (res.status === 200) {
          this.categoryList = res.data;
        }
      })
    },
    getData(data) {
      console.log(data);
      this.$postData("collectRecordList_selectPage", this.dto).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.list;
          this.pageInfo.current = res.data.pageNum;
          this.pageInfo.size = res.data.pageSize;
          this.pageInfo.total = res.data.total
        }
      });
    },
    /**
     * 关闭当前界面弹出的窗口
     * */
    closeCurrModal() {
      this.modal = false;
    },
    initChooseProject() {
      this.closeCurrModal();
      this.getData()
    },
    query() {
      this.loading = true;
      this.dto.pageNum = 1;
      this.getData();
    },

    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dto.pageSize = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dto.pageNum = index;
      this.getData();
    },
    exportExcel() {
      this.loadingExcel = true;
      this.$postData("collectRecordList_export", this.dto, {
        responseType: "arraybuffer"
      }).then(res => {
        this.loadingExcel = false;
        this.blobExport({
          tablename: "领用工单",
          res: res
        });
      });
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
    getStore() {
      this.$postData("store_getByList", {}, {}).then(res => {
        if (res.status == 200) {

          this.optionStore = res.data;
        }
      });

    },
  }
};
</script>

<style scoped>
.handle-box {
  margin-bottom: 20px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}

.table {
  width: 100%;
  font-size: 13px;
}

.mr10 {
  margin-right: 10px;
}
</style>
