<template>
    <div>
        <el-card shadow="hover" body-style="padding: 5px 20px ">
            <el-steps :active="active" simple>
                <el-step title="需求线索分析" icon="el-icon-edit" @click.native="showPage=0"></el-step>
                <el-step title="需求转订单" icon="el-icon-edit" @click.native="active>0?showPage=1:''"></el-step>
                <el-step title="需求合同" icon="el-icon-edit"
                         @click.native="active==2?showPage=2:active==1?addContract():''"></el-step>
            </el-steps>
        </el-card>
        <el-container v-show="showPage==0">
            <el-container>
                <el-aside width="220px">
                    <el-card shadow="hover" body-style="padding: 5px">
                        <h2>推荐保姆：</h2>
                        <div v-for="groups,index in  baomus ">
                            <div v-for="item in groups" style="margin-bottom: 10px">
                                <el-card shadow="hover" body-style="padding: 5px">
                                    <el-row>
                                        <el-col :span="16">
                                            <el-image
                                                    style="width: 100px; height: 100px"
                                                    :src="item.headPortrait"
                                                    :fit="fit"></el-image>
                                        </el-col>
                                        <el-col :span="8">
                                            <div @click="toBaomuInfo(item.id)">
                                                <div>
                                                    <el-link type="primary">{{item.realName}}
                                                    </el-link>
                                                </div>
                                                <div> {{item.no}}</div>
                                                <div>{{(index/remarkIdNum*100).toFixed(0)}}%</div>
                                                <div>{{item.baomuWorkType}}</div>
                                            </div>
                                        </el-col>
                                    </el-row>


                                </el-card>
                            </div>
                        </div>
                    </el-card>
                </el-aside>
                <el-container>
                    <el-main>
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-card shadow="hover" body-style="padding: 5px" style="padding: 0 20px">
                                    <h2>客户需求：</h2>
                                    <el-form :model="need">
                                        <br>
                                        <el-form-item label="客户名称">
                                            <el-input placeholder="请输入客户名称" v-model="need.name" label="客户名称"
                                                      style="width: 200px"></el-input>
                                            <el-button type="primary" @click="save(need)">更新客户名称</el-button>
                                        </el-form-item>

                                    </el-form>

                                    <div v-if="!showEditAddr">
                                        <div style="padding: 10px">位置：<i class="el-icon-location-outline"></i>{{need.street}}
                                            <el-button type="primary" @click="showAddr=!showAddr">
                                                {{showAddr?'隐藏地图':'展示地图'}}
                                            </el-button>
                                        </div>
                                        <div v-if="showAddr">
                                            <addr-map :lng="need.lng" :lat="need.lat"></addr-map>
                                        </div>
                                    </div>
                                    <!--v-if="showEditAddr"-->
                                    <div>
                                        <br>
                                        <el-card shadow="always">
                                            <el-row>
                                                <el-form :model="need" ref="ruleForm" style="padding-left: 20px">
                                                    <el-col :span="8">
                                                        <el-form-item label="城市区域">
                                                            <city-choose @init-choose="initChooseProject" :model="need"
                                                                         :getcity=setCity></city-choose>
                                                        </el-form-item>
                                                    </el-col>
                                                    <el-col :span="8">
                                                        <el-form-item label="服务地址" prop="street">
                                                            <el-col :span="17">
                                                                <el-input placeholder="选择城市区域后可填" v-model="need.street"
                                                                          @blur="geoCode"
                                                                          :disabled=" need.cityId==null && need.areaId==null"></el-input>
                                                            </el-col>
                                                        </el-form-item>
                                                    </el-col>
                                                    <el-button type="primary" @click="need.lat==null?'':save(need)">
                                                        保存地址
                                                    </el-button>
                                                </el-form>
                                            </el-row>
                                        </el-card>
                                        <br>
                                    </div>

                                    <el-button type="primary" plain v-for="(item,index) in remarks" v-if="item!==''"
                                               :key="index">{{item}}
                                    </el-button>


                                    <el-card shadow="hover" body-style="padding: 5px"
                                             style="padding: 0 10px;margin: 10px">
                                        <div style="padding: 20px">
                                            <el-radio style="margin: 10px" v-model="need.needStatus" :label="item.id"
                                                      border v-for="(item,index) in dic" :key="index"
                                                      @change="changeLabel">{{item.text}}
                                            </el-radio>
                                        </div>
                                    </el-card>


                                    <el-divider></el-divider>
									<h2>薪资预算：</h2><br>
									<el-input
											v-model.number="changeDom.salary"
									        placeholder="备注客户的薪资预算（月薪）"
											type="textarea"
											onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
									></el-input>
									<br>
									
									<h2>工作内容：</h2><br>
									<el-input
									        v-model="changeDom.workContent"
									        placeholder="备注员工的工作内容"
									        type="textarea"
									></el-input>
									<br>
									
									<h2>工作要求：</h2><br>
									<el-input
									        v-model="changeDom.workRequire"
									        placeholder="备注客户对员工的工作要求"
									        type="textarea"
									></el-input>
									<br>
									
									<h2>工作地点：</h2><br>
									<el-input
									        v-model="changeDom.workSite"
									        placeholder="备注员工的大致工作地点（线索对外展示时使用）"
									        type="textarea"
									></el-input>
									<br>
										
									<h2>需求备注：</h2><br>
									<el-input
									        v-model="changeDom.agentRemark"
									        placeholder="备注其他需求信息"
									        type="textarea"
									></el-input>
									<br>
                                    <el-button size="mini" @click="save(changeDom)" type="primary">更改备注信息</el-button>
                                    <el-divider></el-divider>
									

                                    <el-card shadow="hover" body-style="padding: 5px">
                                        <h2>操作日志：</h2><br>
                                        <center>

                                            <div v-for="item in logList" v-if="item!==''">
                                                <el-card shadow="hover" body-style="padding: 10px"
                                                         style="text-align: left">
                                                    <b>{{item.operator}}</b> -> <span>{{item.title}}</span>
                                                    <br>
                                                    {{item.message}}<br>
                                                    {{item.createTime}}<br>
                                                </el-card>
                                                <br>
                                            </div>
                                        </center>
                                    </el-card>

                                </el-card>

                            </el-col>
                            <!--                            <el-col :span="24"  style="height: 250px;overflow: auto" >-->
                            <!--                               -->
                            <!--                            </el-col>-->
                        </el-row>
                        <br>
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-card shadow="hover" body-style="padding: 5px">
                                    <member-info v-if="memberId!==null" :mid="memberId">

                                    </member-info>
                                </el-card>
                            </el-col>
                        </el-row>

                    </el-main>
                    <el-footer>
                        <el-card body-style="padding: 5px">
                            <el-button size="mini" @click="need.status =0,save(need)" type="danger"
                                       v-show="need.status!= 0">取消需求
                            </el-button>
                            <el-button size="mini" @click="need.status =3,save(need)" type="success"
                                       v-show="need.status==1">已读
                            </el-button>
                            <el-button size="mini" @click="creadOrder(need)" type="danger"
                                       v-show="need.billNo==null&&need.status!= 0&&!showEditAddr &&need.name!==null">
                                生成订单
                            </el-button>
                        </el-card>
                    </el-footer>
                </el-container>
            </el-container>
        </el-container>
        <el-container v-show="showPage==1">
            <el-card shadow="hover" style="margin-top: 10px">
                <order :order-bill-no="need.billNo" v-if="need.billNo!==null"></order>
            </el-card>
        </el-container>
        <el-container v-show="showPage==2">
            <contract-info :contractId="need.contractId" :contract-id="need.contractId" v-if="need.contractId!==null"
                           style="width: 100%"></contract-info>
        </el-container>
        <el-container v-show="showPage==3">
            <el-card shadow="hover" body-style="padding: 15px" style="width: 100%;margin-top: 10px">
                <contract-add :orderId="need.orderId" v-if="need.orderId!==null" @init-choose="re()"></contract-add>
            </el-card>
        </el-container>


    </div>

</template>
<script>
    import memberInfo from "./memberInfo";
    import order from "./order";
    import contractInfo from "./contractInfo";
    import ContractAdd from "../choose/contractAdd";
    import addrMap from "../char/addrMap";
    import cityChoose from "../minichoose/cityChoose";

    export default {
        name: "needInfo",
        components: {
            ContractAdd,
            'memberInfo': memberInfo,
            'addrMap': addrMap,
            'order': order,
            'contractInfo': contractInfo,
            'cityChoose': cityChoose,
        },
        data() {
            return {
                showEditAddr: false,
                setCity: {
                    cityId: 1,
                    areaId: 2,
                },
                showAddr: false,
                needStatus: 410,
                contract: null,
                showPage: 0,
                memberId: null,
                getMemberModel: {
                    name: null,
                    bindTel: null,
                    current: 1,
                    size: 10
                },
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                memberList: [],
                changeDom: {
                    agentRemark: null,
					salary: null,
					workContent: null,
					workRequire: null,
					workSite: null
                },
                logModel: {
                    orderNeedsId: this.$route.query.id,
                    current: 1,
                    size: 100,
                },
                fit: 'contain',
                active: 0,
                id: this.$route.query.id,
                need: {
                    street: null,
                    lng: null,
                    lat: null,
                    arearId: null,
                    cityId: null,
                    needStatusName: null,
                    needStatus: null,
                    orderId: null,
                    contractId: null,
                    id: null,
                    billNo: null,
                    remarkId: null,
                },
                // afterLng: null,
                // afterLat: null,
                remarkIdNum: 0,
                baomus: [],
                remarks: [],
                logList: [],
                dic: [],
                pageInfo2: {total: 10, size: 10, current: 1, pages: 1},
            }
        },
        created() {
            this.getData();
            this.getLos();
            this.getDic();
        },
        methods: {
            initChooseProject(city) {
                this.need.cityId = city.cityId;
                this.need.areaId = city.areaId;
                this.need.arearId = city.areaId;
                this.need.cityName = city.cityName;
                this.need.areaName = city.areaName;
                if (this.need.cityName == null) {
                    this.need.cityName = '厦门市';
                    this.need.areaName = '湖里区';
                }

            },
            geoCode() {
                var geocoder = new AMap.Geocoder({
                    city: "", //城市设为北京，默认：“全国”
                });
                let city = this.need.cityName + this.need.areaName;
                var address = city == null ? '厦门市湖里区' : city + this.need.street;
                geocoder.getLocation(address, (status, result) => {
                    console.info(result);
                    if (status === 'complete' && result.geocodes.length) {
                        // console.info(result.geocodes[0]);
                        // this.afterLng = result.geocodes[0].location.lng;
                        // this.afterLat = result.geocodes[0].location.lat;
                        this.need.lng = result.geocodes[0].location.lng;
                        this.need.lat = result.geocodes[0].location.lat;
                        // console.info('经度lng', this.need.lng);
                        // console.info('纬度lat', this.need.lat);

                        // document.getElementById('lat').value = result.geocodes[0].location.lat;
                        // document.getElementById('lng').value = result.geocodes[0].location.lng;

                    } else {
                        this.$message.error("获取经纬度失败");
                    }
                });
            },
            changeLabel(label) {
                this.$confirm('此操作将更新线索级别, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    console.log(label);
                    this.dic.forEach(v => {
                        if (v.id == label) {
                            this.need.needStatusName = v.text
                        }
                    });
                    this.save(this.need)
                }).catch(() => {
                    this.need.needStatus = this.needStatus;
                    this.$message({
                        type: 'info',
                        message: '已取消更新'
                    });
                });
            },
            getDic() {
                this.$postUrl("get_dict", 160, null, {}).then(res => {
                    if (res.status == 200) {
                        this.dic = res.data


                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            re() {
                this.$router.go(0)
            },
            addContract() {
                this.$alert('当前订单暂无合同，请确定是否已生成合同？如生成，请前往绑定，或者点击生成合同', '提示', {
                    confirmButtonText: '生成合同',
                    callback: action => {
                        this.showPage = 3
                    }
                });
            },
            toBaomuInfo(id) {
                console.log(id)
                let routeData = this.$router.resolve({path: '/baomuInfo', query: {"id": id}})//path：跳转页面的相对路径，query：参数
                window.open(routeData.href, '_blank');
            },
            creadOrder(item) {
                console.log('item', item);
                // return;
                if (item.needStatus < 417) {
                    return this.$notify({
                        title: '警告',
                        message: '请选择需求分类至5类及以上后再生成订单。',
                        type: 'warning'
                    });
                }
                if (item.billNo != null) {
                    return this.$message.error("该需求已生成过订单！");
                }
                if (this.checkOrder) {
                    return this.$message.error("订单生成中！请稍等。。。");
                }
                if (localStorage.getItem("id") != null) {
                    item.memberId = null
                }
                this.$alert('提醒', '是否确认创建订单', {
                    confirmButtonText: '确定',
                    callback: action => {
                        if (action != 'cancel') {
                            let paramData = {
                                Name: item.name,
                                Phone: item.phone,
                                ProductId: item.productId,
                                CityId: item.cityId,
                                ArearId: item.arearId,
                                StartTime: item.startTime,
                                StartTimeSpan: item.startTimeSpan,
                                EndTimeSpan: item.endTimeSpan,
                                Tg: item.Tg,
                                AgentId: item.agentId,
                                MemberId: item.memberId,
                                Channel: 'jjr',
                                Remarks: item.Remarks,
                                Lat: item.lat,
                                Lng: item.lng,
                                Street: item.street,
                                AreaName: item.areaName,
                                CityName: item.cityName,
                                kd: item.channel,
                                StoreId: localStorage.getItem("storeId"),
                            }
                            let self = this;
                            this.checkOrder = true;
                            item.status = 2;
                            console.log('paramData', JSON.stringify(paramData));
                            $.ajax({
                                type: "post",//type可以为post也可以为get
                                url: "https://inside.xiaoyujia.com/api/Order/create",
                                data: JSON.stringify(paramData),//这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                                dataType: "json",//这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                                headers: {
                                    "Content-Type": "application/json",
                                },
                                success: function (data) {
                                    if (data.Meta.State == 200) {
                                        item.billNo = data.Data.BillNo;
                                        console.log(data.Data.BillNo);
                                        item.kdId = localStorage.getItem("id")
                                        self.$postData("orderNeeds_save", item, {}).then(res => {
                                            if (res.status == 200) {
                                                self.dom.current = 1;
                                                self.list = [];
                                                self.$message.error("需求生成订单成功");
                                                self.checkOrder = false
                                                self.re()
                                            } else {

                                            }
                                        })
                                    } else {
                                        self.$message.error("下单错误," + data.Meta.Msg);
                                    }

                                },
                                error: function (data) {
                                    alert("出现了错误！");
                                }
                            })
                        }
                    }
                });


            },
            getMemberId() {
                this.$postData("memberPage", this.getMemberModel, {}).then(res => {
                    if (res.status == 200) {

                        this.memberList = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                        if (this.memberList.length > 0) {
                            this.memberId = this.memberList[0].id
                        }
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            save(dom) {
                // if (this.afterLng != null) {
                //     dom.lng = this.afterLng;
                // }
                // if (this.afterLat != null) {
                //     dom.lat = this.afterLat;
                // }
                // console.info(this.afterLng, this.afterLat);
                // console.info('dom', dom);
                // return;
				// 对薪资的空值进行处理
				if(dom.salary == null || dom.salary == 0) {
					dom.salary = 0.00
				}
                dom.kdId = localStorage.getItem("id");
                this.$postData("orderNeeds_save", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.$Message.success('修改成功');
                        this.getData();
                        this.getLos();
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })
            },
            getLos() {
                this.$postData("page_orderNeedsLog", this.logModel, {}).then(res => {
                    if (res.status == 200) {
                        this.logList = res.data.records;
                        this.pageInfo2.current = res.data.current;
                        this.pageInfo2.size = res.data.size;
                        this.pageInfo2.total = res.data.total;
                        this.dialog = true;
                        this.loading2 = false
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getTjBaomu(remarkId) {
                if (remarkId == null) {
                    return this.$message.error("此需求无法推荐保姆");
                }
                let list = this.StringToArray(remarkId, ',')
                this.remarkIdNum = list.length
                this.$postData("selectByRemarks_baomuinfo", list, {}).then(res => {
                    if (res.status == 200) {
                        this.baomus = res.data;
                        // console.log(res.data.size())
                        this.show = true
                    } else {
                        return this.$message.error(res.msg);
                    }
                })
            },
            getData() {
                // this.$getUrl("orderNeedsGet", this.id, {}).then(res => {
                const param = {
                    id: this.id,
                    agentId: localStorage.getItem("id")
                };
                this.$postData("orderNeedsGet", JSON.stringify(param), {}).then(res => {
                    if (res.status == 200) {
                        // console.info('resdatas', res.data);
                        this.need = res.data;
                        if (this.need.street == null) {
                            this.showEditAddr = true;
                            this.need.arearId = 2;
                            this.need.cityId = 1;
                        } else {
                            this.showEditAddr = false;
                        }
                        this.needStatus = this.need.needStatus;
                        if (this.need.remark !== null) {
                            this.remarks = this.need.remark.split(';')
                        }
                        this.getTjBaomu(this.need.remarkId);
                        this.getMemberModel.bindTel = this.need.phone;
                        this.getMemberId();
                        if (this.need.billNo !== null) {
                            this.active = 1;
                            this.showPage = 1;
                        }
                        if (this.need.contractId !== null) {
                            this.active = 2;
                            this.showPage = 2;
                        }
                        this.changeDom = this.need;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            StringToArray(str, substr) {
                var arrTmp = [];
                if (substr == "") {
                    arrTmp.push(str);
                    return arrTmp;
                }
                var i = 0, j = 0, k = str.length;
                while (i < k) {
                    j = str.indexOf(substr, i);
                    if (j != -1) {
                        if (str.substring(i, j) != "") {
                            arrTmp.push(str.substring(i, j));
                        }
                        i = j + 1;
                    } else {
                        if (str.substring(i, k) != "") {
                            arrTmp.push(str.substring(i, k));
                        }
                        i = k;
                    }
                }
                return arrTmp;
            },
        }
    }
</script>

<style scoped>
    .el-steps--simple {
        background: #fff;
    }

    .el-header, .el-footer {
        position: sticky;
        bottom: 10px;
        color: #333;
        text-align: center;
        line-height: 60px;
    }

    .el-aside {
        height: 785px;
        padding: 10px;
        color: #474444;
        font-size: 10px;
        text-align: center;
    }

    .el-main {
        padding: 10px 0;
        color: #333;
        text-align: left;

    }

    body > .el-container {
        margin-bottom: 40px;
    }

    .el-container:nth-child(5) .el-aside,
    .el-container:nth-child(6) .el-aside {
        line-height: 260px;
    }

    .el-container:nth-child(7) .el-aside {
        line-height: 320px;
    }
</style>
