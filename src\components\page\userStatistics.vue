<template>
    <div class="table">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item>
                    <i class="el-icon-lx-cascades"></i> 用户统计
                </el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">
                    <el-row>
                        <el-col :span="10">
                            <el-form-item label="订单数">
                                <el-input
                                    v-model="dto.orderNum1"
                                    placeholder="订单数范围"
                                    style="width:150px"
                                    class="handle-input mr10"
                                ></el-input>-
                                <el-input
                                    v-model="dto.orderNum2"
                                    placeholder="订单数范围"
                                    style="width:150px"
                                    class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="10">
                            <el-form-item label="消费金额">
                                <el-input
                                    v-model="dto.consumptionTotal1"
                                    placeholder="消费范围"
                                    style="width:150px"
                                    class="handle-input mr10"
                                ></el-input>-
                                <el-input
                                    v-model="dto.consumptionTotal2"
                                    placeholder="消费范围"
                                    style="width:150px"
                                    class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4" style="text-align:right">
                            <el-button type="primary" @click="onSubmit">搜索</el-button>
                            <el-button type="success" @click="exportExcel">导出Excel</el-button>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="10">
                            <el-form-item label="注册时间">
                                <el-date-picker
                                    v-model="registerTime"
                                    type="daterange"
                                    align="right"
                                    unlink-panels
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    format="yyyy 年 MM 月 dd 日"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="10">
                            <el-form-item label="登录时间">
                                <el-date-picker
                                    v-model="lastLoginTime"
                                    type="daterange"
                                    align="right"
                                    unlink-panels
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    format="yyyy 年 MM 月 dd 日"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div style="text-align:right">{{theader}}</div>
            <el-table :data="data" border class="table" ref="multipleTable" @sort-change="sortChange">
                <el-table-column
                    prop="memberId"
                    label="会员ID"
                    width="100"
                    :show-overflow-tooltip="true"
                    sortable="custom"
                ></el-table-column>
                <el-table-column
                    prop="deviceCode"
                    label="管家贴码"
                    :show-overflow-tooltip="true"
                    sortable="custom"
                ></el-table-column>
                <el-table-column
                    prop="belongChannel"
                    label="所属渠道"
                    width="120"
                    sortable="custom"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                    prop="orderNum"
                    label="订单数"
                    width="100"
                    :show-overflow-tooltip="true"
                    sortable="custom"
                ></el-table-column>
                <el-table-column
                    prop="consumptionTotal"
                    label="消费金额"
                    width="110"
                    :show-overflow-tooltip="true"
                    sortable="custom"
                ></el-table-column>
                <el-table-column prop="consumptionString" label="消费项目" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column
                    prop="couponSum"
                    label="代金券金额"
                    width="120"
                    :show-overflow-tooltip="true"
                    sortable="custom"
                ></el-table-column>
                <el-table-column
                    prop="registerTime"
                    label="注册时间"
                    :show-overflow-tooltip="true"
                    sortable="custom"
                ></el-table-column>
                <el-table-column
                    prop="lastConsumeTime"
                    label="最后消费时间"
                    :show-overflow-tooltip="true"
                    sortable="custom"
                ></el-table-column>
                <el-table-column
                    prop="lastLoginTime"
                    label="最后登入时间"
                    :show-overflow-tooltip="true"
                    sortable="custom"
                ></el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    background
                    layout="total,sizes,prev, pager, next,jumper"
                    :total="pageInfo.total"
                    :page-size="pageInfo.size"
                    :current-page="pageInfo.current"
                    :page-sizes="[10,20,50,100]"
                    :page-count="pageInfo.pages"
                    @size-change="sizeChange"
                    @current-change="currentChange"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "basetable",
    data() {
        return {
            tableData: [],
            multipleSelection: [],

            pageInfo: { total: 10, size: 10, current: 1, pages: 1 },
            theader: "",
            registerTime: [],
            lastLoginTime: [],
            dto: {
                orderNum1: "",
                orderNum2: "",
                consumptionTotal1: "",
                consumptionTotal2: "",
                registerTime1: "",
                registerTime2: "",
                lastLoginTime1: "",
                lastLoginTime2: "",

                export: "",
                orderBy: "",
                pageSize: "",
                pageIndex: ""
            }
        };
    },
    created() {
        this.getData();
    },
    computed: {
        data() {
            return this.tableData.filter(d => {
                d.registerTime =
                    d.registerTime == null
                        ? null
                        : d.registerTime.replace("T", " ").substring(0, 19);
                d.lastConsumeTime =
                    d.lastConsumeTime == null
                        ? null
                        : d.lastConsumeTime.replace("T", " ").substring(0, 19);
                d.lastLoginTime =
                    d.lastLoginTime == null
                        ? null
                        : d.lastLoginTime.replace("T", " ").substring(0, 19);
                return d;
            });
        }
    },
    methods: {
        getData() {
            this.openFullScreen();
            this.$postData("operate_userStatistics", this.dto, {})
                .then(res => {
                    
                    if (res.meta.state == 200) {
                        this.tableData = res.data.tbody.records;
                        this.pageInfo = res.data.tbody;

                        let theader = "";
                        for (let i of res.data.theader) {
                            theader += i + " ";
                        }
                        this.theader = theader;
                        this.closeFullScreen();
                    } else {
                        this.closeFullScreen();
                        this.$message.error("查询失败，" + res.meta.msg);
                    }
                })
                .catch(error => {
                    this.closeFullScreen();
                    this.$message.error("查询失败，系统超时");
                });
        },
        onSubmit() {
            
            this.dto.registerTime1 = this.registerTime?this.registerTime[0]||"":"";;
            this.dto.registerTime2 = this.registerTime?this.registerTime[1]||"":"";;
            this.dto.lastLoginTime1 = this.lastLoginTime?this.lastLoginTime[0]||"":"";
            this.dto.lastLoginTime2 = this.lastLoginTime?this.lastLoginTime[1]||"":"";

            this.dto.export = "";
            // this.dto.orderBy = "";
            // this.dto.pageSize = "";
            this.dto.pageIndex = "1";

            this.getData();
        },
        exportExcel() {
            this.dto.export = "Excel";
            this.$postData("operate_userStatistics", this.dto, {
                responseType: "arraybuffer"
            }).then(res => {
                this.blobExport({
                    tablename: "用户统计",
                    res: res
                });
                this.dto.export = ""
            });
        },
        // 页码大小
        sizeChange(size) {
            
            this.dto.pageSize = size;
            this.dto.pageIndex = 1;
            this.getData();
        },
        // 跳转页码
        currentChange(index) {
            
            this.dto.pageIndex = index;
            this.getData();
        },
        blobExport({ tablename, res }) {
            const aLink = document.createElement("a");
            let blob = new Blob([res], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.download = tablename + ".xlsx";
            document.body.appendChild(aLink);
            aLink.click();
            document.body.removeChild(aLink);
        },
        openFullScreen() {
            const loading = this.$loading({
                lock: true,
                text: "Loading",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)"
            });
        },
        closeFullScreen() {
            const loading = this.$loading({
                lock: true,
                text: "Loading",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)"
            });
            loading.close();
        },
        sortChange: function(column, prop, order) {
            
            if (column == null) {
                this.dto.orderBy = "";
            } else {
                if (column.order == "ascending") {
                    this.dto.orderBy = column.prop + " ASC";
                }
                if (column.order == "descending") {
                    this.dto.orderBy = column.prop + " DESC";
                }
            }
            this.getData();
        }
    }
};
</script>

<style scoped>
.handle-box {
    margin-bottom: 20px;
}

.handle-select {
    width: 120px;
}

.handle-input {
    width: 300px;
    display: inline-block;
}
.del-dialog-cnt {
    font-size: 16px;
    text-align: center;
}
.table {
    width: 100%;
    font-size: 14px;
}
.red {
    color: #ff0000;
}
.mr10 {
    margin-right: 10px;
}
</style>
