<template>
  <div>
    <el-alert
        title="提示：填写时请确认填写信息，若要关闭活动请在编辑时把时间调整到今日日期前即可,编辑时若没有传图片则按原图传"
        type="warning"
        effect="dark">
    </el-alert>
    <divider></divider>
    <el-form  :model="ruleForm" ref="ruleForm" label-width="180px" class="demo-ruleForm" :inline="false" >

      <el-form-item label="活动名称" required >
        <el-col :span="8">
          <el-input v-model="ruleForm.activity.activityName" ></el-input>
        </el-col>
      </el-form-item>

      <el-form-item label="活动时间" required>
        <el-col :span="8">
          <el-form-item prop="date1">
            <el-date-picker type="datetimerange" placeholder="选择日期" value-format="yyyy-MM-dd HH:mm:ss" v-model="date1" style="width: 100%;"></el-date-picker>
          </el-form-item>
        </el-col>
      </el-form-item>
        <el-form-item label="每日博饼次数" required>
          <el-col :span="8">
            <el-input-number v-model="ruleForm.activity.count" :min="1" :max="999999" label="描述文字"></el-input-number>
          </el-col>
        </el-form-item>
        <div class="inf1">
          <el-form-item label="消费中奖率设置"></el-form-item>
          <el-form-item label="中奖状元需消费满可参与" required>
            <el-col :span="8">
              <el-input-number v-model="ruleForm.activityGradePays[0].orderPay" :min="1" :max="999999" label="描述文字"></el-input-number>
            </el-col>
          </el-form-item>
          <el-form-item label="中奖对堂需消费满可参与" required>
            <el-col :span="8">
              <el-input-number v-model="ruleForm.activityGradePays[1].orderPay" :min="1" :max="999999" label="描述文字"></el-input-number>
            </el-col>
          </el-form-item>
          <el-form-item label="中奖三红需消费满可参与" required>
            <el-col :span="8">
              <el-input-number v-model="ruleForm.activityGradePays[2].orderPay" :min="1" :max="999999" label="描述文字"></el-input-number>
            </el-col>
          </el-form-item>
          <el-form-item label="中奖四进需消费满可参与" required>
            <el-col :span="8">
              <el-input-number v-model="ruleForm.activityGradePays[3].orderPay" :min="1" :max="999999" label="描述文字"></el-input-number>
            </el-col>
          </el-form-item>
<!--          <el-form-item label="中奖二举需消费满可参与" required>-->
<!--            <el-col :span="8">-->
<!--              <el-input v-model.number="ruleForm.activityGradePays[4].orderPay" onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>-->
<!--            </el-col>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="中奖一秀需消费满可参与" required>-->
<!--            <el-col :span="8">-->
<!--              <el-input v-model.number="ruleForm.activityGradePays[5].orderPay" onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>-->
<!--            </el-col>-->
<!--          </el-form-item>-->
        </div>

      <div class="inf1">
        <el-form-item label="奖品对应设置(如果非实物请务必填写对应优惠券批次号)" label-width="380px" ></el-form-item>
        <el-form-item label="状元奖项名" required>
          <el-col :span="6">
            <el-input v-model ="ruleForm.activityGifts[0].winGradeName" ></el-input>
          </el-col>

          <el-form-item label="奖项数量" required>
            <el-col :span="3">
              <el-input-number v-model="ruleForm.activityGifts[0].max" :min="1" :max="999999" label="描述文字"></el-input-number>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;是否实物：</label>
            <el-col :span="2">
              <el-switch v-model="ruleForm.activityGifts[0].type" active-text="是" inactive-text="否" :active-value="1" :inactive-value="0"/>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;优惠券批次：</label>
            <el-col :span="4">
              <el-input v-model.number="ruleForm.activityGifts[0].couponId" onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>
            </el-col>
          </el-form-item>
        </el-form-item>

        <el-form-item label="对堂奖项名" required>
          <el-col :span="6">
            <el-input v-model="ruleForm.activityGifts[1].winGradeName" ></el-input>
          </el-col>
          <el-form-item label="奖项数量" required>
            <el-col :span="3">
              <el-input-number v-model="ruleForm.activityGifts[1].max" :min="1" :max="999999" label="描述文字"></el-input-number>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;是否实物：</label>
            <el-col :span="2">
              <el-switch v-model="ruleForm.activityGifts[1].type" active-text="是" inactive-text="否" :active-value="1" :inactive-value="0"/>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;优惠券批次：</label>
            <el-col :span="4">
              <el-input v-model.number="ruleForm.activityGifts[1].couponId" onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>
            </el-col>
          </el-form-item>
        </el-form-item>

        <el-form-item label="三红奖项名" required>
          <el-col :span="6">
            <el-input v-model="ruleForm.activityGifts[2].winGradeName" ></el-input>
          </el-col>
          <el-form-item label="奖项数量" required>
            <el-col :span="3">
              <el-input-number v-model="ruleForm.activityGifts[2].max" :min="1" :max="999999" label="描述文字"></el-input-number>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;是否实物：</label>
            <el-col :span="2">
              <el-switch v-model="ruleForm.activityGifts[2].type" active-text="是" inactive-text="否" :active-value="1" :inactive-value="0"/>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;优惠券批次：</label>
            <el-col :span="4">
              <el-input v-model.number="ruleForm.activityGifts[2].couponId" onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>
            </el-col>
          </el-form-item>
        </el-form-item>

        <el-form-item label="四进奖项名" required>
          <el-col :span="6">
            <el-input v-model="ruleForm.activityGifts[3].winGradeName" ></el-input>
          </el-col>
          <el-form-item label="奖项数量" required>
            <el-col :span="3">
              <el-input-number v-model="ruleForm.activityGifts[3].max" :min="1" :max="999999" label="描述文字"></el-input-number>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;是否实物：</label>
            <el-col :span="2">
              <el-switch v-model="ruleForm.activityGifts[3].type" active-text="是" inactive-text="否" :active-value="1" :inactive-value="0"/>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;优惠券批次：</label>
            <el-col :span="4">
              <el-input v-model.number="ruleForm.activityGifts[3].couponId" onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>
            </el-col>
          </el-form-item>
        </el-form-item>

        <el-form-item label="二举奖项名" required>
          <el-col :span="6">
            <el-input v-model="ruleForm.activityGifts[4].winGradeName" ></el-input>
          </el-col>
          <el-form-item label="奖项数量" required>
            <el-col :span="3">
              <el-input-number v-model="ruleForm.activityGifts[4].max" :min="1" :max="999999" label="描述文字"></el-input-number>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;是否实物：</label>
            <el-col :span="2">
              <el-switch v-model="ruleForm.activityGifts[4].type" active-text="是" inactive-text="否" :active-value="1" :inactive-value="0"/>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;优惠券批次：</label>
            <el-col :span="4">
              <el-input v-model.number="ruleForm.activityGifts[4].couponId" onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>
            </el-col>
          </el-form-item>
        </el-form-item>

        <el-form-item label="一秀奖项名" required>
          <el-col :span="6">
            <el-input v-model="ruleForm.activityGifts[5].winGradeName" ></el-input>
          </el-col>
          <el-form-item label="奖项数量" required>
            <el-col :span="3">
              <el-input-number v-model="ruleForm.activityGifts[5].max" :min="1" :max="999999" label="描述文字"></el-input-number>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;是否实物：</label>
            <el-col :span="2">
              <el-switch v-model="ruleForm.activityGifts[5].type" active-text="是" inactive-text="否" :active-value="1" :inactive-value="0"/>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;优惠券批次：</label>
            <el-col :span="4">
              <el-input v-model.number="ruleForm.activityGifts[5].couponId" onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>
            </el-col>
          </el-form-item>
        </el-form-item>

      </div>
        <el-form-item label="(若编辑时不更改图片则以上一次图片上传)" label-width="280px"></el-form-item>
        <el-form-item label="活动任务图"  required>
          <el-link type="primary" @click="showImgDetail(1)">点我查看图片示例</el-link>
          <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
          </el-dialog>
          <activity-pic-content ref="bbMissionPic"></activity-pic-content>
        </el-form-item>

        <el-form-item label="界面背景图"  required>
          <el-link type="primary" @click="showImgDetail(2)">点我查看图片示例</el-link>
          <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
          </el-dialog>
          <activity-pic-content ref="bbBag"></activity-pic-content>
        </el-form-item>

      <el-form-item label="界面底部图"  required>
        <el-link type="primary" @click="showImgDetail(3)">点我查看图片示例</el-link>
        <el-dialog :visible.sync="dialogVisible">
          <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
        <activity-pic-content ref="bbBg3"></activity-pic-content>
      </el-form-item>

        <el-form-item label="规则图" required>
          <el-link type="primary" @click="showImgDetail(4)">点我查看图片示例</el-link>
          <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
          </el-dialog>
        <activity-pic-content ref="bbGz"></activity-pic-content>
        </el-form-item>


      <el-form-item>
        <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
        <el-button @click="resetForm('ruleForm')">重置</el-button>
      </el-form-item>
    </el-form>
  </div>

</template>

<script>
import moment from "moment";

let giftData = {
  max:null,
  winGradeName:null,
  detail:null,
  couponId:null,
  type:0,
}
let giftwindData = {
  orderPay:null,
}
let giftArr = [];
let windGradeArr = [];
for (let i=0;i<6;i++){
  giftArr.push(JSON.parse(JSON.stringify(giftData)));
}
for (let i=0;i<4;i++){
  windGradeArr.push(JSON.parse(JSON.stringify(giftwindData)));
}

const dataMap = {
  props: {},
  Set: function (key, value) {
    this.props[key] = value
  },
};

import activityPicContent from "./activityPicContent.vue";
export default {
  name: "creatActivityByYingXiaoBoBing",
  components:{
    activityPicContent
  },
  created() {
    if (this.$route.query.id !=null && this.$route.query.id !== '' && this.$route.query.id !== undefined){
      this.ruleForm.activity.id = this.$route.query.id;
      this.getData(this.$route.query.id);
    }
  },
  data() {
    return {
      dialogImageUrl: '',
      date1:[],
      dialogVisible:false,
      ruleForm: {
        activityTypeName:'bobing',
        activityType:0,
        activity:{
          id:null,
          activityName:'',
          count:'',
          startTime:'',
          endTime:'',
          type:0,
        },
        activityGradePays:windGradeArr,
        activityGifts:giftArr,
        dataMap :{},
      },

    };
  },

  methods: {
    submitForm(formName) {
      if (this.date1 !=null && this.date1.length > 0){
        this.ruleForm.activity.startTime = this.date1[0];
        this.ruleForm.activity.endTime = this.date1[1];
      } else {
        return this.$message.error("时间不能为空")
      }
      dataMap.Set("bbMissionPic",this.$refs.bbMissionPic.pic);
      dataMap.Set("bbBag",this.$refs.bbBag.pic);
      dataMap.Set("bbBg3",this.$refs.bbBg3.pic);
      dataMap.Set("bbGz",this.$refs.bbGz.pic);

      this.ruleForm.dataMap = dataMap.props;

      let d = this.ruleForm;
      if (d.activity.activityName === '' || d.activity.count === ''){
        return this.$message.error("请将信息补充完整-活动名称(次数)")
      }
      //活动中奖概率调整
      for (let i=0; i<d.activityGradePays.length; i++){

        if (d.activityGradePays[i].orderPay == null ||
            d.activityGradePays[i].orderPay === '' || d.activityGradePays[i].orderPay === undefined){
          return this.$message.error("请将信息补充完整-消费中奖设置")
        }
      }
      //奖品内容校验
      for (let i=0; i<d.activityGifts.length; i++){
        if (d.activityGifts[i].winGradeName == null ||
            d.activityGifts[i].winGradeName === '' || d.activityGifts[i].max == null
            || (d.activityGifts[i].type === 0 && d.activityGifts[i].couponId == null)
        ){
          return this.$message.error("请将信息补充完整-奖品内容")
        }
      }

      console.info(JSON.stringify(this.ruleForm))

      this.$postData("createActivityByYX", this.ruleForm).then(res => {

        if (res.status === 200) {
          this.$message.success("创建成功");
          this.$router.push("/activityPageByYingXiao");
        } else {
          this.$message.error(res.data);
        }
      })

    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    getData(val){
      this.$getData("initFriendlyData", {"id":val} ).then(res => {

        if (res.status === 200) {
          this.ruleForm.activity = res.data.activity;
          this.ruleForm.activityGifts = res.data.activityGifts;
          this.ruleForm.activityGradePays = res.data.activityGradePays;
          this.date1 =[new Date(res.data.activity.startTime),new Date(res.data.activity.endTime)] ;
          this.$refs.bbMissionPic.createImg(res.data.dataMap.bbMissionPic);
          this.$refs.bbBag.createImg(res.data.dataMap.bbBag);
          this.$refs.bbBg3.createImg(res.data.dataMap.bbBg3);
          this.$refs.bbGz.createImg(res.data.dataMap.bbGz);



        } else {
          this.$message.error(res.data);
        }
      })
    },
    showImgDetail(type){
      if (type == 1){
        this.dialogImageUrl = 'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/null1600846888216showbg2.png';
      } else if (type == 2){
        this.dialogImageUrl = 'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/null1599718623703bg1.png';
      } else if (type == 3){
        this.dialogImageUrl = 'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/qiye1636966170775bg3.png';
      } else if (type == 4){
        this.dialogImageUrl = 'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/qiye1636966236457gz.png';
      }
      this.dialogVisible = true;
    },
  }
}
</script>

<style scoped>
  .inf1{
    border: 1px solid red;
  }
</style>
