<template style="background-color: #000">

<!--            <el-tabs type="card" :stretch="true" >-->
<!--                <el-tab-pane label="基本信息">-->
                <Row>
                <Col span="24">
                        <el-form ref="form" :model="dom" :rules="ruleValidate">
                            <Row>
                                <Col span="24">
                                    <el-collapse v-model="activeNames">
                                        <el-collapse-item title="合同人员信息" name="1">
                                            <template slot="title">
                                                <i class="header-icon el-icon-user"></i>  - 合同人员信息 -
                                            </template>
                                            <Col span="12">

                                                <el-form-item   prop="agentName">
                                                    <el-input placeholder="经理名" v-model="dom.agentName"  >
                                                        <template slot="prepend">经理名:</template>
                                                    </el-input>
                                                </el-form-item >

                                                <el-form-item   prop="memberName">
                                                    <el-input placeholder="请输入客户名" v-model="dom.memberName"  >
                                                        <template slot="prepend">客户名:</template>
                                                    </el-input>
                                                </el-form-item >
                                            </Col>
                                            <Col span="12">

                                                <el-form-item   prop="baomuName">
                                                    <el-input placeholder="请输入保姆名" v-model="dom.employeeName"  >
                                                        <template slot="prepend">保姆名:</template>
                                                    </el-input>
                                                </el-form-item >

                                                <el-form-item   prop="client">
                                                    <el-input placeholder="委托人" v-model="dom.client" >
                                                        <template slot="prepend">委托人:</template>
                                                    </el-input>
                                                </el-form-item >
                                            </Col>
                                        </el-collapse-item>
                                        <el-collapse-item title="" name="2">
                                            <template slot="title">
                                                <i class="el-icon-bank-card"></i>  - 合同信息 -
                                            </template>
                                            <Col span="12">

                                                <el-form-item   prop="servicePay">
                                                    <el-input placeholder="劳动报酬" v-model="dom.servicePay"  >
                                                        <template slot="prepend">劳动报酬:</template>
                                                        <template slot="append">元</template>
                                                    </el-input>
                                                </el-form-item >
                                                <el-form-item   prop="inPay">
                                                    <el-input placeholder="中介服务费（大写）" v-model="dom.inPay"  >
                                                        <template slot="prepend">中介服务费（大写）:</template>
                                                    </el-input>
                                                </el-form-item >
                                                <el-form-item   prop="serviceType">
                                                    <el-input placeholder="服务方式" v-model="dom.serviceType"  >
                                                        <template slot="prepend">服务方式:</template>
                                                    </el-input>
                                                </el-form-item >
                                                <el-form-item   prop="insure">
                                                    <el-input placeholder="保险" v-model="dom.insure"  >
                                                        <template slot="prepend">保险:</template>
                                                    </el-input>
                                                </el-form-item >
                                                <el-form-item   prop="insureRemark">
                                                    <el-input placeholder="保险（其他）" v-model="dom.insureRemark"  >
                                                        <template slot="prepend">保险（其他）:</template>
                                                    </el-input>
                                                </el-form-item >
                                                <el-form-item   prop="insureRemark">
                                                    <el-input placeholder="insureBy" v-model="dom.insureBy"  >
                                                        <template slot="prepend">保费承担人:</template>
                                                    </el-input>
                                                </el-form-item >


                                            </Col>
                                            <Col span="12">

                                                <el-form-item   prop="serviceContent">
                                                    <el-input placeholder="服务内容" v-model="dom.serviceContent"  >
                                                        <template slot="prepend">服务内容:</template>
                                                    </el-input>
                                                </el-form-item >
                                                <el-form-item   prop="serviceContentRemark">
                                                    <el-input placeholder="服务内容（其他）" v-model="dom.serviceContentRemark"  >
                                                        <template slot="prepend">服务内容（其他）:</template>
                                                    </el-input>
                                                </el-form-item >
                                                <el-form-item   prop="serviceNum">
                                                    <el-input placeholder="服务次数（/年）" v-model="dom.serviceNum"  >
                                                        <template slot="prepend">服务次数:</template>
                                                        <template slot="append">/年</template>
                                                    </el-input>
                                                </el-form-item >
                                                <el-form-item   prop="serviceMax">
                                                    <el-input placeholder="服务最大天数" v-model="dom.serviceMax"  >
                                                        <template slot="prepend">服务最大天数:</template>
                                                        <template slot="append">/天</template>
                                                    </el-input>
                                                </el-form-item >


                                                <el-form-item   prop="clientDate" label="服务结束时间:">
                                                    <el-date-picker
                                                            v-model="dom.serviceEndDate"
                                                            type="datetime"
                                                            value-format="yyyy-MM-dd HH:mm:ss"
                                                            placeholder="服务结束时间">
                                                    </el-date-picker>
                                                </el-form-item >

                                                <el-form-item   prop="clientDate" label="服务开始时间:">
                                                    <el-date-picker
                                                            v-model="dom.serviceStarDate"
                                                            type="datetime"
                                                            value-format="yyyy-MM-dd HH:mm:ss"
                                                            placeholder="服务开始时间">
                                                    </el-date-picker>
                                                </el-form-item >
                                            </Col>
                                        </el-collapse-item>
                                        <el-collapse-item title="效率 Efficiency" name="3">
                                            <template slot="title">
                                                <i class="el-icon-office-building"></i>  - 服务对象基本情况 -
                                            </template>
                                            <Col span="12">

                                                <el-form-item   prop="homeDetail">
                                                    <el-input placeholder="服务对象户型" v-model="dom.homeDetail"  >
                                                        <template slot="prepend">服务对象户型:</template>
                                                    </el-input>
                                                </el-form-item >
                                                <el-form-item   prop="homeArea">
                                                    <el-input placeholder="面积" v-model="dom.homeArea"  >
                                                        <template slot="prepend">面积:</template>
                                                        <template slot="append">平方</template>
                                                    </el-input>
                                                </el-form-item >

                                                <el-form-item   prop="homeHealth">
                                                    <el-input placeholder="健康情况" v-model="dom.homeHealth"  >
                                                        <template slot="prepend">健康情况:</template>
                                                    </el-input>
                                                </el-form-item >
                                                <el-form-item   prop="homeRemark">
                                                    <el-input placeholder="其他" v-model="dom.homeRemark" >
                                                        <template slot="prepend">其他:</template>
                                                    </el-input>
                                                </el-form-item >
                                            </Col>
                                            <Col span="12">

                                                <el-form-item   prop="homeAllPerson">
                                                    <el-input placeholder="总家庭成员" v-model="dom.homeAllPerson"  >
                                                        <template slot="prepend">总家庭成员:</template>
                                                        <template slot="append">人</template>
                                                    </el-input>
                                                </el-form-item >
                                                <el-form-item   prop="homePerson">
                                                    <el-input placeholder="家庭成员（成年）" v-model="dom.homePerson"  >
                                                        <template slot="prepend">家庭成员（成年）:</template>
                                                        <template slot="append">人</template>
                                                    </el-input>
                                                </el-form-item >

                                                <el-form-item   prop="homeChildren">
                                                    <el-input placeholder="家庭成员（孩童）" v-model="dom.homeChildren" >
                                                        <template slot="prepend">家庭成员（孩童）:</template>
                                                        <template slot="append">人</template>
                                                    </el-input>
                                                </el-form-item >
                                                <el-form-item   prop="homePets">
                                                    <el-input placeholder="宠物" v-model="dom.homePets" >
                                                        <template slot="prepend">宠物:</template>
                                                        <template slot="append">只</template>
                                                    </el-input>
                                                </el-form-item >

                                            </Col>
                                        </el-collapse-item>
                                        <el-collapse-item title="其他合同信息" name="4">
                                            <template slot="title">
                                                <i class="el-icon-guide"></i>  - 其他合同信息 -
                                            </template>

                                            <Col span="12">

                                                <el-form-item   prop="clientDate" label="甲方（客户）签署日期:">
                                                    <el-date-picker
                                                            v-model="dom.memberSignDate"
                                                            type="datetime"
                                                            value-format="yyyy-MM-dd HH:mm:ss"
                                                            placeholder="甲方（客户）签署日期">
                                                    </el-date-picker>
                                                </el-form-item >
                                                <el-form-item   prop="memberCardId">
                                                    <el-input placeholder="甲方（客户）身份证" v-model="dom.memberCardId"  >
                                                        <template slot="prepend">甲方（客户）身份证:</template>
                                                    </el-input>
                                                </el-form-item >


                                                <el-form-item   prop="memberPhone">
                                                    <el-input placeholder="甲方（客户）电话" v-model="dom.memberPhone"  >
                                                        <template slot="prepend">甲方（客户）身份证:</template>
                                                    </el-input>
                                                </el-form-item >
                                                <el-form-item   prop="memberAdress">
                                                    <el-input placeholder="甲方（客户）地址" v-model="dom.memberAdress"  >
                                                        <template slot="prepend">甲方（客户）地址:</template>
                                                    </el-input>
                                                </el-form-item >


                                            </Col>
                                            <Col span="12">
                                                <el-form-item   prop="employeeSignDate" label="乙方（保姆）签署日期:">
                                                    <el-date-picker
                                                            v-model="dom.employeeSignDate"
                                                            type="datetime"
                                                            value-format="yyyy-MM-dd HH:mm:ss"
                                                            placeholder="乙方（保姆）签署日期">
                                                    </el-date-picker>
                                                </el-form-item>
                                                <el-form-item   prop="employeeCarId">
                                                    <el-input placeholder="乙方（保姆）身份证" v-model="dom.employeeCarId"  >
                                                        <template slot="prepend">乙方（保姆）身份证:</template>
                                                    </el-input>
                                                </el-form-item >

                                                <el-form-item   prop="employeePhone">
                                                    <el-input placeholder="乙方（保姆）电话" v-model="dom.employeePhone"  >
                                                        <template slot="prepend">乙方（保姆）身份证:</template>
                                                    </el-input>
                                                </el-form-item >
                                                <el-form-item   prop="employeeAdress">
                                                    <el-input placeholder="乙方（保姆）地址" v-model="dom.employeeAdress"  >
                                                        <template slot="prepend">乙方（保姆）地址:</template>
                                                    </el-input>
                                                </el-form-item >


                                                <el-form-item   prop="clientDate" label="委托代理人签字日期:">
                                                    <el-date-picker
                                                            v-model="dom.clientDate"
                                                            type="datetime"
                                                            value-format="yyyy-MM-dd HH:mm:ss"
                                                            placeholder="委托代理人签字日期">
                                                    </el-date-picker>
                                                </el-form-item >


                                            </Col>
                                        </el-collapse-item>
                                    </el-collapse>
                                </Col>



                            </Row>

                                    <div style="margin-right: 100px;float: right">
                                        <Button type="primary" @click="save('dom')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </div>
                                </el-form>
                    </Col>


            </Row>

</template>


<script>

    export default {
        props:['model'],
        data() {
            return {
                activeNames: ['1','2'],
                getdom:this.model,
                dom:{
                    id:null,
                    memberid:null,
                    employeeid:null,
                    membername:null,
                    employeename:null,
                    membercardid:null,
                    employeecarid:null,
                    memberphone:null,
                    employeephone:null,
                    memberadress:null,
                    employeeadress:null,
                    agentid:null,
                    agentname:null,
                    createdate:null,
                    servicestardate:null,
                    serviceenddate:null,
                    servicetype:null,
                    servicepay:null,
                    servicecontent:null,
                    servicecontentremark:null,
                    homedetail:null,
                    homearea:null,
                    homeallperson:null,
                    homeperson:null,
                    homechildren:null,
                    homehealth:null,
                    homepets:null,
                    homeremark:null,
                    inpay:null,
                    servicenum:null,
                    servicemax:null,
                    insure:null,
                    insureremark:null,
                    insureby:null,
                    membersigndate:null,
                    employeesigndate:null,
                    updatedate:null,
                    status:null,
                    client:null,
                    clientdate:null,
                    orderid:null,
                },
                formItem: {
                    No: null,
                    RealName: null,
                    Phone: null,
                    Address:null,
                },

                ruleValidate: {
                    name: [
                        {required: true, message: '请输入编号', trigger: 'change'}
                    ],
                    street: [
                        {required: true, message: '请选择名称', trigger: 'change'}
                    ],
                    lng: [
                        {required: true, message: '请选择联系方式', trigger: 'change'}
                    ],
                    lat: [
                        {required: true, message: '请输入地址', trigger: 'blur'}
                    ],

                },
            }
        },
        components: {
        },
        created: function () {
            console.log(this.getdom)
        },
        methods: {
            save(name) {
                        this.$postData("add_contract", this.dom, {}).then(res => {
                            if (res.status == 200) {
                                this.$Message.success('增加成功');
                                this.chooseThisModel();
                            } else {
                                this.$message.error("增加失败，" + res.msg);
                            }
                        })


            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.dom=null;
                this.$emit('init-choose', "");
            },

        },

    }
</script>

<style>
    {
        background: #409eff;
        color: #fff;
    }
</style>
<style scoped>

    .ivu-col-span-9{
        padding-top: 30px;
    }
    .el-input-group{
        width: 80%;
    }
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

