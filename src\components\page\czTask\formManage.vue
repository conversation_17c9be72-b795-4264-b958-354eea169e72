<template>
	<div class="container" id="pdfDom">

		<!-- 搜索栏目 -->
		<div class="handle-box">
			<el-form ref="form" :model="pageInfo">
				<el-row>
					<el-col :span="6">
						<el-form-item label="表单标题" style="margin-right: 20px">
							<el-input v-model="quer.formTitle" placeholder="请输入表单标题"></el-input>
						</el-form-item>
					</el-col>
          <el-col :span="6">
            <el-form-item label="表单介绍" style="margin-right: 20px">
              <el-input v-model="quer.formIntroduce" placeholder="请输入表单介绍"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="映射表名" style="margin-right: 20px">
              <el-input v-model="quer.mappingTable" placeholder="请输入映射表名"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="映射字段" style="margin-right: 20px">
              <el-input v-model="quer.mappingField" placeholder="请输入映射字段"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="映射条件" style="margin-right: 20px">
              <el-input v-model="quer.mappingWhere" placeholder="请输入映射条件"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="映射参数" style="margin-right: 20px">
              <el-input v-model="quer.whereField" placeholder="请输入映射参数"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="表单类型" style="margin-right: 20px">
              <el-select v-model="quer.formType" placeholder="请选择表单类型" clearable>
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>


					<el-col :span="6" style="margin: 32px 0;">
						<el-button type="primary" @click="query()" icon="el-icon-search">搜索
						</el-button>

						<el-button style="margin-left: 10px" type="success" icon="el-icon-circle-plus-outline" @click="certModal = true">添加
						</el-button>
					</el-col>
				</el-row>

			</el-form>
		</div>




		<!-- 数据表格 -->
		<el-table :data="list" v-loading="loading" border stripe
			:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
			:expand-row-keys="expands" @expand-change="expandSelect">

			<el-table-column width="140" prop="formTitle" label="表单标题">
				<template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.formTitle }}</span>

          <el-input v-model="scope.row.formTitle" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
                    placeholder="请输入表单标题" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>


      <el-table-column width="140" prop="formIntroduce" label="表单介绍">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.formIntroduce }}</span>

          <el-input v-model="scope.row.formIntroduce" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
                    placeholder="请输入表单介绍" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>

      <el-table-column width="140" prop="formType" label="表单类型">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{scope.row.formType==1?'图片':scope.row.formType==0?'文本':'未知'}}</span>

          <el-select v-if="scope.row.isEdit" v-model="scope.row.formType" placeholder="请选择表单类型">
            <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>


      <el-table-column width="140" prop="mappingTable" label="映射表名">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.mappingTable }}</span>

          <el-input v-model="scope.row.mappingTable" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
                    placeholder="请输入映射表名" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>

      <el-table-column width="140" prop="mappingField" label="映射字段">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.mappingField ||'未知'}}</span>

          <el-input v-model="scope.row.mappingField" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
                    placeholder="请输入映射字段" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>

      <el-table-column width="140" prop="mappingWhere" label="映射条件">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.mappingWhere ||'未知'}}</span>

          <el-input v-model="scope.row.mappingWhere" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
                    placeholder="请输入映射条件" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>

      <el-table-column width="140" prop="whereField" label="映射参数">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.whereField ||'未知'}}</span>

          <el-input v-model="scope.row.whereField" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
                    placeholder="请输入映射参数" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>


			<el-table-column width="140" prop="createTime" label="创建时间">
				<template slot-scope="scope">
					<span>{{scope.row.createTime}}</span>
				</template>
			</el-table-column>

			<el-table-column width="130" prop="creater" label="创建人">
				<template slot-scope="scope">
					<span >{{scope.row.creater}}</span>
				</template>
			</el-table-column>

			<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
				<template slot-scope="scope">
					<el-button @click="openEdit(scope.row)" type="success" size="small" :disabled="scope.row.isEdit"
						v-if="!scope.row.isEdit" icon="el-icon-edit">修改
					</el-button>
					<el-button @click="updateUnionForm(scope.row)" type="primary" size="small"
						v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
					</el-button>
					<el-popconfirm title="确定删除吗？" @confirm="deleteUnionForm(scope.row)">
						<el-button type="danger" size="small" slot="reference" style="margin-left: 10px" icon="el-icon-delete">删除
						</el-button>
					</el-popconfirm>

				</template>
			</el-table-column>

		</el-table>


		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--添加成长表单-->
		<el-dialog :visible.sync="certModal" width="60%" title="添加成长表单" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">

							<el-form-item label="表单标题：" required>
								<el-input v-model="unionForm.formTitle" type="textarea" class="handle-input mr10"
									placeholder="请输入表单标题">
								</el-input>
							</el-form-item>

              <el-form-item label="表单介绍：">
                <el-input v-model="unionForm.formIntroduce" type="textarea" class="handle-input mr10"
                          placeholder="请输入表单介绍">
                </el-input>
              </el-form-item>

              <el-form-item label="映射表名：" required>
                <el-input v-model="unionForm.mappingTable" type="textarea" class="handle-input mr10"
                          placeholder="请输入映射表名">
                </el-input>
              </el-form-item>

              <el-form-item label="映射字段：" required>
                <el-input v-model="unionForm.mappingField" type="textarea" class="handle-input mr10"
                          placeholder="请输入映射字段">
                </el-input>
              </el-form-item>

              <el-form-item label="映射条件：" required>
                <el-input v-model="unionForm.mappingWhere" type="textarea" class="handle-input mr10"
                          placeholder="请输入映射条件">
                </el-input>
              </el-form-item>

              <el-form-item label="映射参数：" required>
                <el-input v-model="unionForm.whereField" type="textarea" class="handle-input mr10"
                          placeholder="请输入映射参数">
                </el-input>
              </el-form-item>

						</el-form>
					</el-col>

          <el-col :span="12">
            <el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">

              <el-form-item label="表单类型：" required>
                <el-select v-model="unionForm.formType" placeholder="请选择表单类型" clearable>
                  <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>

            </el-form>
          </el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="saveUnionForm()" type="success" size="small">确定添加
					</el-button>
				</div>

			</div>
		</el-dialog>

	</div>

</template>

<script>

  export default {
    name: "qaLibraryType",
    components: {
    },
		data() {
			return {
				isEdit: false,
				showEdit: true,
				certModal: false,
        options: [{
          value: 0,
          label: '文本'
        }, {
          value: 1,
          label: '图片'
        }],
        rewardOptions: [{
          value: 0,
          label: '佳币'
        },{
          value: 1,
          label: '实物'
        },{
          value: 2,
          label: '奖金'
        },],
				list: [],
				loading: true,
				pageSizeOpts: [5, 10, 20],
        unionForm: {
          creater: localStorage.getItem("id")
        },
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				expands: [],
				getRowKeys(row) {
					return row.id
				},
				quer: {
					"formTitle": "",
					"formIntroduce": "",
					"formType": null,
					"mappingField": "",
					"mappingWhere": "",
					"whereField": "",
					"mappingTable": "",
					"current": 1,
					"size": 10
				},
			}
		},
		created() {
			this.getData()
		},
		methods: {
			getData() {
				// 获取成长表单
				this.$getData("getUnionForm", this.quer).then(res => {
					this.loading = false
					if (res.code == 0) {
						this.list = res.data.records
						this.pageInfo.current = res.data.current
						this.pageInfo.size = res.data.size
						this.pageInfo.total = res.data.total
						// 追加编辑状态位
						for (let item of this.list) {
							this.$set(item, "isEdit", false)
						}
					} else {
						this.list = []
						this.$message.error('未查询到成长表单!')
					}
				})
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.getData()
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			// 添加成长表单
      saveUnionForm() {
				// 数据校验
				if (!this.unionForm.formTitle) {
					this.$message.error('请输入表单标题！')
				} else if (!this.unionForm.mappingTable) {
          this.$message.error('请输入映射表名！')
        } else if (!this.unionForm.mappingField) {
          this.$message.error('请输入映射字段！')
        } else if (!this.unionForm.mappingWhere) {
          this.$message.error('请输入映射条件！')
        } else if (!this.unionForm.whereField) {
          this.$message.error('请输入映射参数！')
        } else if (this.unionForm.formType==null) {
          this.$message.error('请选择表单类型！')
        } else {
					this.$postData("saveUnionForm", this.unionForm).then(res => {
						if (res.code == 0) {
							this.$message.success('添加成功!')
							this.certModal = false
              this.unionForm.formTitle = ''
              this.unionForm.formIntroduce = ''
              this.unionForm.mappingField = ''
              this.unionForm.mappingWhere = ''
              this.unionForm.whereField = ''
              this.unionForm.formType = null
              this.unionForm.mappingTable = ''
							this.getData()
						} else {
							this.$message.error('添加失败！' + res.msg)
						}
					})
				}
			},
			// 删除成长表单
      deleteUnionForm(val) {
				this.$postData("deleteUnionForm", val).then(res => {
					if (res.code == 0) {
						this.$message.success('删除成功!')
						this.getData()
					} else {
						this.$message.error('删除失败！' + res.msg)
					}
				})
			},
			// 更改成长表单
      updateUnionForm(val) {
        // 数据校验
        if (!val.formTitle) {
          this.$message.error('请输入表单标题！')
        } else if (!val.mappingTable) {
          this.$message.error('请输入映射表名！')
        } else if (!val.mappingField) {
          this.$message.error('请输入映射字段！')
        } else if (!val.mappingWhere) {
          this.$message.error('请输入映射条件！')
        } else if (!val.whereField) {
          this.$message.error('请输入映射参数！')
        } else if (val.formType==null) {
          this.$message.error('请选择表单类型！')
        } else {
          val.creater = localStorage.getItem("id")
          this.$postData("updateUnionForm", val).then(res => {
            if (res.code == 0) {
              this.$message.success('更新成功!')
              this.getData()
              val.isEdit = false
            } else {
              this.$message.error('更新失败！' + res.msg)
            }
          })
        }
			},
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>