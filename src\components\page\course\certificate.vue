<template>
	<div class="container" id="pdfDom">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">证书管理</el-menu-item>
			<el-menu-item index="1">颁发记录</el-menu-item>
		</el-menu>

		<!-- 搜索栏目 -->
		<div v-if="choiceIndexNow==0">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="证书标题、内容、固定编号等"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="证书类型" style="margin-right: 20px">
								<el-select v-model="searchType">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="证书状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(0,0)">添加
							</el-button>

							<!-- 						<el-button type="success" @click="imgToPdf(blankImg)">测试
						</el-button> -->
						</el-col>
					</el-row>

				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column prop="id" width="60" label="编号">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)">{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="certCodeFixed" label="证书固定编号">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="!scope.row.isEdit">{{scope.row.certCodeFixed}}</span>

						<el-input v-model="scope.row.certCodeFixed" style="width: 100%;" type="textarea"
							v-if="scope.row.isEdit" placeholder="请输入证书固定编号" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="certTitle" label="证书标题">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)" v-if="!scope.row.isEdit">{{scope.row.certTitle}}</span>

						<el-input v-model="scope.row.certTitle" style="width: 100%;" type="textarea"
							v-if="scope.row.isEdit" placeholder="请输入证书标题"
							:autosize="{ minRows: 4, maxRows: 10}"></el-input>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="certContent" label="证书内容">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="!scope.row.isEdit">{{checkStr(scope.row.certContent)}}</span>

						<el-input v-model="scope.row.certContent" style="width: 100%;" type="textarea"
							v-if="scope.row.isEdit" placeholder="请输入证书介绍内容" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="certType" label="证书类型">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-if="item.id == scope.row.certType" v-for="(item,index) in typeList" :key="index"
								:type="item.type">{{formatType(scope.row)}}</el-tag>
						</div>

						<el-select v-model="scope.row.certType" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
								:value="item.id"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="certImg" label="证书图-横板" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-upload class="upload-demo" :action="uploadUrl" :disabled="!scope.row.isEdit"
							list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
							<img :src="scope.row.certImg!=null?scope.row.certImg:blankImg"
								style="width: 120px;height: 84px;"
								@click="scope.row.isEdit?openImgUpload(0,scope.$index):openImg(scope.row.certImg)">
						</el-upload>
					</template>
				</el-table-column>

				<el-table-column width="125" prop="certPreview" label="证书图-竖板" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-upload class="upload-demo" :action="uploadUrl" :disabled="!scope.row.isEdit"
							list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
							<img :src="scope.row.certPreview!=null?scope.row.certPreview:blankImg"
								style="width: 64px;height: 90px;margin-left: 20px;"
								@click="scope.row.isEdit?openImgUpload(1,scope.$index):openImg(scope.row.certPreview)">
						</el-upload>
					</template>
				</el-table-column>

				<el-table-column width="122" label="证书状态">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-if="scope.row.state == 0" type="info">下架</el-tag>
							<el-tag v-if="scope.row.state == 1" type="success">上架</el-tag>
						</div>

						<el-select v-model="scope.row.state" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
								:value="item.value"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column prop="validity" label="有效期" width="120" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="!scope.row.isEdit">{{formatCertValidity(scope.row.validity)}}</span>

						<div v-if="scope.row.isEdit">
							<el-input v-model="scope.row.validity" style="width: 80%;" placeholder="单位：天"
								onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
							</el-input> 天
						</div>
					</template>
				</el-table-column>

				<el-table-column prop="certNum" label="颁发总数" width="120" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)" v-if="!scope.row.isEdit">{{scope.row.certNum}}</span>

						<div v-if="scope.row.isEdit">
							<el-input v-model="scope.row.certNum" style="width: 80%;" placeholder="单位：份"
								onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
							</el-input> 份
						</div>
					</template>
				</el-table-column>

				<el-table-column width="122" label="颁发通知">
					<template slot-scope="scope">
						<div v-if="!scope.row.isEdit">
							<el-tag v-if="scope.row.isNotify == 0" type="info">不发送</el-tag>
							<el-tag v-if="scope.row.isNotify == 1" type="success">发送</el-tag>
						</div>

						<el-select v-model="scope.row.isNotify" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in notifyList" :key="index" :label="item.text"
								:value="item.value"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="creTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)">{{scope.row.creTime}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creater" label="创建人">
					<template slot-scope="scope">
						<span @click="rowClick(scope.row.id)"
							v-if="scope.row.createrName!=null">{{scope.row.createrName}}({{scope.row.creater}})</span>
						<span @click="rowClick(scope.row.id)"
							v-if="scope.row.createrName==null">{{checkStr(scope.row.creater)}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
					<template slot-scope="scope">
						<el-button @click="openModal(1,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
						<!-- 			<el-button @click="updateUnionCertificates(scope.row)" type="primary" size="small"
							v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
						</el-button> -->
						<el-popconfirm title="确定删除该证书吗？删除后无法恢复，请谨慎操作!" @confirm="deleteUnionCertificates(scope.row)">
							<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
								:disabled="!scope.row.isEdit" icon="el-icon-delete">删除
							</el-button>
						</el-popconfirm>

					</template>
				</el-table-column>

			</el-table>
		</div>

		<div v-if="choiceIndexNow==1">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="证书标题、内容、固定编号等"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="证书类型" style="margin-right: 20px">
								<el-select v-model="searchType">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(2,0)">添加
							</el-button>
						</el-col>
					</el-row>

				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column prop="id" width="60" label="ID">
				</el-table-column>

				<el-table-column prop="certCode" width="160" label="证书编号">
					<template slot-scope="scope">
						<span>{{scope.row.certCode}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="certTitle" label="证书标题">
				</el-table-column>

				<el-table-column width="140" prop="certImg" label="证书图片">
					<template slot-scope="scope">
						<img :src="scope.row.certImg||blankImg" style="width: 64px;height: 90px;"
							@click="openImg(scope.row.certImg||blankImg)">
					</template>
				</el-table-column>

				<el-table-column width="140" prop="realName" label="接收人">
					<template slot-scope="scope">
						<span>{{scope.row.realName||'-'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="storeName" label="接收门店">
					<template slot-scope="scope">
						<span>{{scope.row.storeName||'-'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="campusName" label="接收校区">
					<template slot-scope="scope">
						<span>{{scope.row.campusName||'-'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="102" label="证书状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.state == 0" type="info">下架</el-tag>
						<el-tag v-if="scope.row.state == 1" type="success">上架</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creater" label="颁发人">
					<template slot-scope="scope">
						<span v-if="scope.row.createrName!=null">{{scope.row.createrName}}({{scope.row.creater}})</span>
						<span v-if="scope.row.createrName==null">{{checkStr(scope.row.creater)}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作">
					<template slot-scope="scope">
						<el-button @click="openModal(3,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
						<el-button @click="openView(scope.row)" type="primary" size="small" icon="el-icon-document">预览
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>


		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!-- 消息通知预览 -->
		<el-drawer size="25%" :with-header="false" :visible.sync="viewModal" direction="rtl">
			<iframe id="iframeId" :src="viewPageUrl" frameborder="0" style="width: 400px;height: 800px;margin: 0 auto;"
				scrolling="auto">
			</iframe>
		</el-drawer>

		<!--添加证书-->
		<el-drawer size="60%" :with-header="false" :visible.sync="certModal" direction="rtl">
			<div style="height: 900px;overflow: hidden;overflow-y: scroll;">
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">证书信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 固定编号：">
								<el-input v-model="cert.certCodeFixed" type="textarea" class="handle-input mr10"
									placeholder="请输入证书固定编号">
								</el-input>
							</el-form-item>

							<el-form-item label="* 证书标题：">
								<el-input v-model="cert.certTitle" type="textarea" class="handle-input mr10"
									placeholder="请输入证书标题">
								</el-input>
							</el-form-item>

							<el-form-item label="* 证书内容：">
								<el-input v-model="cert.certContent" type="textarea" class="handle-input mr10"
									placeholder="请输入证书内容描述">
								</el-input>
							</el-form-item>

							<el-form-item label="* 证书图片：">
								<el-upload class="upload-demo" :action="uploadUrl" list-type="picture"
									:on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="cert.certImg!=null?cert.certImg:blankImg"
										style="width: 350px;height: 245px;" @click="openImgUpload(2,0)">
								</el-upload>
							</el-form-item>

						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 证书类型：">
								<el-select v-model="cert.certType" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="有效期：">
								<el-input v-model="cert.validity" class="handle-input mr10" placeholder="单位：天（默认为180天）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 天
							</el-form-item>


							<el-form-item label="颁发总数：">
								<el-input v-model="cert.certNum" class="handle-input mr10"
									placeholder="单位：份（默认为10000份，达到上限不可增发）"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 份
							</el-form-item>

							<el-form-item label="颁发通知：">
								<el-select v-model="cert.isNotify" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in notifyList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="证书缩略图：">
								<el-upload class="upload-demo" :action="uploadUrl" list-type="picture"
									:on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="cert.certPreview!=null?cert.certPreview:blankImg"
										style="width: 224px;height: 315px;" @click="openImgUpload(3,0)">
								</el-upload>
							</el-form-item>

							<el-form-item label="证书状态：" v-if="detailType==1">
								<el-select v-model="cert.state" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="certModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addUnionCertificates()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateUnionCertificates(cert)" type="success" size="small"
						v-if="detailType==1">保存修改
					</el-button>
				</div>
			</div>
		</el-drawer>

		<!--证书颁发记录-->
		<el-drawer size="60%" :with-header="false" :visible.sync="certRecordModal" direction="rtl">
			<div>
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title"></h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="证书标题：" v-if="detailType==0">
								<el-select v-model="choiceItem.certId" placeholder="请选择">
									<el-option v-for="(item,index) in certList" :key="index"
										:label="item.id+'：'+item.certTitle" :value="item.id"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="员工检索：" v-if="detailType==0&&choiceItem.certId!=4">
								<el-input v-model="searchText" placeholder="输入员工姓名/工号进行检索" @input="listEmployee"
									@change="listEmployee" style="width: 280px"></el-input>
							</el-form-item>

							<el-form-item label="接收员工：" v-if="detailType==0&&choiceItem.certId!=4">
								<el-select v-model="choiceItem.employeeId" placeholder="请选择" @change="selectEmployee">
									<el-option v-for="(item,index) in employeeList" :key="index"
										:label="item.realName+'-'+item.no" :value="item.id">
									</el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="门店检索：" v-if="detailType==0&&choiceItem.certId==4">
								<el-input v-model="searchText" placeholder="输入门店名进行检索" @input="listStore"
									@change="listStore" style="width: 280px"></el-input>
							</el-form-item>

							<el-form-item label="接收门店：" v-if="detailType==0&&choiceItem.certId==4">
								<el-select v-model="choiceItem.storeId" placeholder="请选择">
									<el-option v-for="(item,index) in storeList" :key="index"
										:label="item.id+'：'+item.storeName" :value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="培训相关资质需要指定接收门店" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="接收校区：" v-if="detailType==0&&choiceItem.certId==4">
								<el-select v-model="choiceItem.campusId" placeholder="请选择">
									<el-option v-for="(item,index) in campusList" :key="index"
										:label="item.id+'：'+item.campusName" :value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="培训相关资质需要指定接收校区" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="证书编号：" v-if="detailType==1">
								<el-input v-model="choiceItem.certCode" type="textarea" class="handle-input mr10"
									placeholder="请输入证书编号">
								</el-input>
							</el-form-item>
						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="证书状态：" v-if="detailType==1">
								<el-select v-model="choiceItem.state" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="certRecordModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addUnionCertRecord()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateUnionCertRecord(choiceItem)" type="success" size="small"
						v-if="detailType==1">保存
					</el-button>
				</div>
			</div>
		</el-drawer>

	</div>

</template>

<script>
	import Vue from 'vue';

	export default {
		name: "certificate",

		data() {
			return {
				url: '',
				imageUrl: '',
				viewPageUrl: '',

				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				excelUploadId: 1,
				excelUploadUrl: "",

				// 测试时使用
				// uploadUrl: "http://localhost:8063/files/uploadFiles",
				// excelUploadUrlOrgin: "http://localhost:8063/courseContent/excelImport",

				baseUrl: "https://api.xiaoyujia.com/",
				uploadUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
				excelUploadUrlOrgin: "https://biapi.xiaoyujia.com/courseContent/excelImport",
				deleteTips: "确定删除选中证书吗？该操作无法恢复，请谨慎操作!",
				searchText: '',
				isEdit: false,
				showEdit: true,
				imgModal: false,
				viewModal: false,
				certModal: false,
				certTypeModal: false,
				certRecordModal: false,
				choiceIndex: '0',
				choiceIndexNow: '0',
				choiceItemIndex: 0,
				choiceItem: {},
				detailType: 0,
				list: [],
				storeList: [],
				campusList: [],
				certList: [],
				employeeList: [],
				loading: true,
				pageSizeOpts: [10, 20, 50],
				cert: {},
				certType: {},
				pageInfo: {
					total: 10,
					size: 10,
					current: 1,
					pages: 1
				},
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				searchType: null,
				searchState: null,
				quer: {
					"search": "",
					"typeList": null,
					"state": null,
					"orderBy": "id ASC",
					"current": 1,
					"size": 10
				},
				stateList: [{
					value: 0,
					text: "下架",
					type: "info"
				}, {
					value: 1,
					text: "上架",
					type: "success"
				}],
				notifyList: [{
					value: 0,
					text: "不发送",
					type: "info"
				}, {
					value: 1,
					text: "发送",
					type: "success"
				}],
				typeList: [{
					id: 0,
					typeName: "普通证书",
					type: "info"
				}, {
					id: 1,
					typeName: "结业证书",
					type: "danger"
				}, {
					id: 2,
					typeName: "联盟认证",
					type: "warning"
				}, {
					id: 3,
					typeName: "门店资质",
					type: "info"
				}, {
					id: 4,
					typeName: "讲师资质",
					type: "danger"
				}, {
					id: 5,
					typeName: "技能资质",
					type: "warning"
				}]

			}
		},
		created() {
			this.getData()
		},
		methods: {
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceIndex = key
				this.expands = []
				this.showEdit = true
				this.reset()
			},
			getData() {
				this.choiceIndexNow = 2
				if (this.choiceIndex == 0) {
					this.$postData("getUnionCertificates", this.quer).then(res => {
						this.loading = false
						if (res.status == 200) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
							for (let item of this.list) {
								this.$set(item, "isEdit", false)
							}
						} else {
							this.list = []
							this.$message.error('未查询到相关证书记录!')
						}
						this.choiceIndexNow = 0
					})
				} else if (this.choiceIndex == 1) {
					this.choiceIndexNow = 1
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'acn/getUnionCertRecord',
						data: JSON.stringify(this.quer),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.code == 0 && res.data.records) {
								this.list = res.data.records
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
							} else {
								this.list = []
								this.$message.error('未查询到相关颁发记录!')
							}
							this.choiceIndexNow = 1
						},
					})
					this.loading = false
					this.listStore()
					this.listCampus()
					this.listCert()
				}
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				let data = []
				let i = 0
				let length = this.typeList.length
				let isFindType = false
				// 格式化搜索项
				for (i = 0; i < length; i++) {
					if (this.searchType == i + "") {
						data = []
						data.push(Number(i))
						this.quer.typeList = data
						isFindType = true
					} else {
						if (this.searchType == "" && this.searchType !== 0) {
							this.quer.typeList = null
						}
					}
				}
				if (this.searchState != null) {
					this.quer.state = this.searchState
				}

				// 遍历到最后没有找到对应的证书类型，则随便赋一个值
				if (!isFindType && this.searchType !== "" && this.searchType !== null) {
					this.quer.typeList = [999]
				}
				if (this.choiceIndex == 1) {
					this.quer.certType = this.searchType
					this.quer.typeList = null
				} else {
					this.quer.certType = null
				}
				this.getData()
			},
			// 重置
			reset() {
				this.loading = true
				this.quer.search = ""
				this.quer.typeList = null
				this.quer.state = null
				this.quer.orderBy = "id ASC"
				this.quer.certType = null
				if (this.choiceIndex == 1) {
					this.quer.orderBy = "t.creTime ASC"
				}
				this.quer.current = 1
				this.searchType = null
				this.searchState = null
				this.isEdit = false
				this.getData()
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 多选
			handleSelectionChange(val) {
				this.multipleSelection = val
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "证书图片") {
					tips = "推荐尺寸：1000*700"
				} else if (column.label == "证书缩略图") {
					tips = "推荐尺寸：640*900"
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			// 字符串校验及格式化
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			// 格式化证书时间
			formatCertValidity(validity) {
				let result = validity != null && validity != 0 ? validity + "天" : "暂未录入"
				return result
			},
			// 格式化工作类型
			formatWorkType(value) {
				let data = ["保姆", "月嫂", "育婴师", "护工", "其他"]
				switch (value) {
					case 10:
						return data[0]
						break
					case 20:
						return data[1]
						break
					case 30:
						return data[2]
						break
					case 40:
						return data[3]
						break
					case 50:
						return data[4]
						break
				}
			},
			// 格式化证书类型
			formatType(val) {
				let result = "暂无"
				let id = val.certType
				for (let item of this.typeList) {
					if (id == item.id) {
						result = item.typeName
						break
					}
				}
				return result
			},
			// 证书Excel导入
			excelImport(index) {
				let id = this.list[index].id
				let excelUploadUrl = this.excelUploadUrlOrgin
				excelUploadUrl += "/" + id
				this.excelUploadUrl = excelUploadUrl
				this.excelUploadId = index
			},
			// 获取索引
			getIndex(val, list) {
				let index = 0
				for (let i = 0; i < list.length; i++) {
					if (val == list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 选中员工
			selectEmployee(id) {
				let index = this.getIndex(id, this.employeeList)
				let employee = this.employeeList[index]
				this.choiceItem.memberId = employee.memberId || null
				this.choiceItem.employeeId = employee.id || null
				this.searchText = employee.realName || ''
			},
			// 证书Excel模板下载
			excelDownload() {
				this.$postData("excelDownload", null, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('证书Excel模板下载成功!')
					this.blobExport({
						tablename: "证书Excel模板",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// Excel表格上传成功
			excelUploadSuccess(res, file) {
				this.$message.success('章节导入成功！点击保存按钮即可录入哦！')
				// 将解析到的结果加入到章节列表中
				for (let item of res.data) {
					this.list[this.excelUploadId].courseContent.push(item)
				}
			},
			// Excel表格上传失败
			excelUploadError() {
				this.$message.error('章节内容解析失败！请使用正确的Excel模板进行上传！')
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.list[this.uploadId].certImg = res.data
				} else if (this.uploadImgType == 1) {
					this.list[this.uploadId].certPreview = res.data
				} else if (this.uploadImgType == 2) {
					this.$set(this.cert, "certImg", res.data)
				} else if (this.uploadImgType == 3) {
					this.$set(this.cert, "certPreview", res.data)
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadId = index

				if (this.uploadImgType == 0 || this.uploadImgType == 2) {
					this.$message.info('推荐上传尺寸：1000*700')
				} else if (this.uploadImgType == 1 || this.uploadImgType == 3) {
					this.$message.info('推荐上传尺寸：640*900')
				}
			},
			// 打开图片预览
			openImg(url) {
				if (url != null && url != '') {
					this.imageUrl = url
				} else {
					this.imageUrl = this.blankImg
				}
				this.imgModal = true
			},
			// 打开视频预览
			openVideo(url) {
				if (url != null && url != '') {
					this.videoUrl = url
				}
				this.videoModal = true
			},
			// 打开预览
			openView(val) {
				let id = val.id
				this.viewPageUrl = "https://jiajie.xiaoyujia.com/pages-mine/certificate/cert-detail?id=" + id
				this.viewModal = true
			},
			openModal(index, index1) {
				if (index == 0) {
					this.detailType = 0
					this.cert = {}
					this.certModal = true
				} else if (index == 1) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.cert = this.list[index1]
					this.certModal = true
				} else if (index == 2) {
					this.detailType = 0
					this.choiceItem = {}
					this.certRecordModal = true
				} else if (index == 3) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.certRecordModal = true
				}
			},
			//打开员工信息详情
			rowClick(id) {
				// this.baomuId = baomuId
				// this.certModal = true
				// this.loading = true
				// this.getBaomuDetail(baomuId)
			},
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			listStore() {
				this.$postData("store_getByList", {
					storeName: this.searchText || '',
				}, {}).then(res => {
					if (res.status == 200) {
						this.storeList = res.data
					} else {
						this.storeList = []
					}
				});
			},
			listCampus() {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'course/listCampus',
					data: JSON.stringify({
						campusState: 1
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.campusList = res.data
						} else {
							this.campusList = []
						}
					},
				})
			},
			listCert() {
				this.$getData("getAllUnionCertificates").then(res => {
					if (res.status == 200) {
						this.certList = res.data
					}
				})
			},
			listEmployee() {
				if (!this.searchText) {
					return
				}
				this.$postData("listEmployeeDto", {
					search: this.searchText,
					employeeType: 20,
					state: 1
				}).then(res => {
					if (res.status == 200) {
						this.employeeList = res.data
					} else {
						this.employeeList = []
						this.$message.error("查询失败，" + res.msg)
					}
				})
			},
			// 颁发证书
			addUnionCertRecord() {
				this.$set(this.choiceItem, "creater", localStorage.getItem('account') || 'admin')
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'acn/addUnionCertRecord',
					data: JSON.stringify(this.choiceItem),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.$message.success('证书颁布成功!')
							this.certRecordModal = false
							this.list.push(res.data)
						} else {
							this.$message.error(res.msg)
						}
					},
				})
			},
			// 更新
			updateUnionCertRecord() {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'acn/updateUnionCertRecord',
					data: JSON.stringify(this.choiceItem),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.$message.success('证书记录更新成功!')
							this.certRecordModal = false
							this.list[this.choiceItemIndex] = res.data
						} else {
							this.$message.error(res.msg)
						}
					},
				})
			},
			// 添加证书
			addUnionCertificates() {
				let cert = this.cert
				// 数据校验
				if (cert.certCodeFixed == null) {
					this.$message.error('请填写证书固定编号！')
				} else if (cert.certTitle == null) {
					this.$message.error('请填写证书标题！')
				} else if (cert.certContent == null) {
					this.$message.error('请填写证书内容简介！')
				} else if (cert.certType == null) {
					this.$message.error('请选择证书类型！')
				} else if (cert.certImg == null) {
					this.$message.error('请上传证书图片！')
				} else {
					let no = localStorage.getItem("account") || 'admin'
					this.$set(cert, "creater", no)
					this.$set(cert, "state", 1)
					this.$postData("addUnionCertificates", cert).then(res => {
						if (res.status == 200) {
							this.$message.success('证书添加成功!')
							this.certModal = false
							this.getData()
						} else {
							this.$message.error('证书添加失败！' + res.msg)
						}
					})
				}
			},
			// 类型
			addcertType() {
				let certType = this.certType
				// 数据校验
				if (certType.typeName == null) {
					this.$message.error('请填写证书类型名称！')
				} else if (certType.typeCode == null) {
					this.$message.error('请填写证书类型编号！')
				} else {
					this.$postData("addUnionCertificates", certType).then(res => {
						if (res.status == 200) {
							this.$message.success('证书类型添加成功!')
							this.certTypeModal = false
						} else {
							this.$message.error('证书类型添加失败！' + res.msg)
						}
					})
				}
			},
			// 删除证书
			deleteUnionCertificates(val) {
				this.$postData("deleteUnionCertificates", val).then(res => {
					if (res.status == 200) {
						this.$message.success('证书删除成功!')
						val.isEdit = false
						this.getData()
					} else {
						this.$message.error('证书删除失败！' + res.msg)
					}
				})
			},
			// 更改证书
			updateUnionCertificates(val) {
				this.$postData("updateUnionCertificates", val).then(res => {
					if (res.status == 200) {
						this.$message.success('证书更新成功!')
						val.isEdit = false
					} else {
						this.$message.error('证书更新失败！' + res.msg)
					}
				})
			},
			beforeImgUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!');
				}
				return isLt2M;
			}
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>