<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">
                    <el-row>

                            <el-col :span="8">
                                <el-form-item label="认证操作人">
                                    <el-input
                                            v-model="model.crePerson"
                                            placeholder="认证操作人"
                                            style="width:200px"
                                            class="handle-input mr10"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        <el-col :span="8">
                            <el-form-item label="认证人">
                                <el-input
                                        v-model="model.realName"
                                        placeholder="认证人"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="认证门店">
                                <el-input
                                        v-model="model.storeName"
                                        placeholder="认证门店"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="16">
                            <el-form-item label="操作时间">
                                <el-date-picker
                                        v-model="value2"
                                        type="datetimerange"
                                        :picker-options="pickerOptions"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        align="right">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8" >

                            <el-form-item>
                                <el-button type="success"  round @click="search()">搜索</el-button>
                                <el-button type="primary"  round @click="getJzyExportList()">导出</el-button>
                            </el-form-item>

                        </el-col>
                    </el-row>
                </el-form>
                <div class="sum-class">扣费总额：<b>￥{{this.pageInfo.total*3}}</b></div>
                    <el-table :data="list" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable" >
                        <el-table-column
                                type="selection"
                                width="35">
                        </el-table-column>
                        <el-table-column
                                prop="storeName"
                                label="认证门店"
                                width="280"
                        >
                            <template slot-scope="scope">
                                {{scope.row.storeId==1?'平台':scope.row.storeName}}
                            </template>
                        </el-table-column>

<!--                        <el-table-column-->
<!--                                prop="outTradeNo"-->
<!--                                label="平台订单号"-->
<!--                                width="180"-->
<!--                        ></el-table-column>-->
                        <el-table-column
                                prop="tranTime"
                                label="认证时间"
                                width="180"
                        ></el-table-column>
<!--                        <el-table-column-->
<!--                                prop="type"-->
<!--                                label="认证类型"-->
<!--                                width="180"-->
<!--                        >-->
<!--                            <template slot-scope="scope">-->
<!--                                {{getRzName(scope.row)}}-->
<!--                            </template>-->

<!--                        </el-table-column>-->
                        <el-table-column
                                prop="crePerson"
                                label="认证操作人"
                                width="180"
                        ></el-table-column>
                        <el-table-column
                                prop="realName"
                                label="认证人"
                                width="180"
                        ></el-table-column>
                        <el-table-column
                                prop="tranAmt"
                                label="扣费金额"
                        >
                            <template slot-scope="scope">
<!--                                {{scope.row.result==0?'3.45':'0.35'}}-->
                               ￥3
                            </template>
                        </el-table-column>


<!--                        <el-table-column-->
<!--                                prop="resultMsg"-->
<!--                                label="认证结果"-->
<!--                        ></el-table-column>-->
                    </el-table>
                    <div class="pagination">
                        <Page :total="pageInfo.total"
                              @on-change="onChange"
                              :show-total="true"
                              :show-sizer="true"
                              :page-size-opts="pageSizeOpts"
                              @on-page-size-change="onPageSizeChange"
                              :page-size="pageInfo.size"/>
                    </div>

            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: "agentJzy",
        data() {
            return {
                sum:0,
                list:[],
                pageSizeOpts:[10,20,30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                storeId:localStorage.getItem("storeId"),
                model: {
                    storeId:null,
                    realName:null,
                    storeName:null,
                    crePerson:null,
                    current: 1,
                    size: 10,
                    startTime:null,
                    endTime:null,
                },
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                value2: null,
            }
        },
        created() {
            this.getData();
        },
        methods:{
            getRzName(v){
                if (v.type=='0021'){
                    return "照片人证比对"
                }
                if (v.type=='0626'){
                    return "不良人员名单"
                }
                if (v.type=='0627'){
                    return "法院信息详情"
                }
                if (v.type=='04216'){
                    return "特殊名单验证"
                }
            },
            search(){
                this.model.current=1,
                this.model.startTime=null
                this.model.endTime=null
                if (this.value2==null) {
                    this.model.startTime=null
                    this.model.endTime=null
                }else {
                    this.model.startTime=this.value2[0]
                    this.model.endTime=this.value2[1]
                }
                this.getData()
            },
            getData() {
                if (this.storeId=='1'){

                }else {
                    this.model.storeId=this.storeId
                }
                this.$postData("getJzyPage", this.model, {}).then(res => {
                    if (res.status == 200) {
                        this.getJzySum()

                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            }, getJzySum() {
                this.$postData("getJzySum", this.model, {}).then(res => {
                    if (res.status == 200) {
                        this.sum = res.data;

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getJzyExportList() {
                this.$postData("getJzyExportList", this.model, {
                    responseType: "arraybuffer"
                }).then(res => {

                        this.blobExport({
                            tablename: "认证记录",
                            res: res
                        });


                })
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.model.size = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                console.log(index)
                this.model.current = index;
                this.getData();
            },
        }
    }
</script>

<style scoped>
    .sum-class{
        padding: 10px;
        font-size: 14px;
        background: #ddd;
        width: 100%;
    }

</style>
