<template>
  <div style=" border: 1px solid #eee;width: 100%">
    <el-container>
      <el-container style="background: white">
        <el-main>
          <h1>图文</h1>
          <p>封面图片只能使用内容中心的图片素材</p>
          <el-button type="primary" size="small" @click="dialogVisible = true">添加图文</el-button>
          <el-divider></el-divider>
          <el-row :gutter="20" type="flex" justify="center">
            <el-col :span="10">
              <el-input placeholder="请输入标题名称" prefix-icon="el-icon-search" clearable
                        width="100" v-model="dom.title">
              </el-input>
            </el-col>
            <el-col :span="8">
              <el-button type="primary" @click="selectQiyeGraphics()">查询</el-button>
              <el-button type="primary" plain @click="reset()">重置</el-button>
              <el-button type="warning" v-if="!delectGraphics" icon="el-icon-delete"
                         @click="delectGraphics=!delectGraphics" plain>
                删除图文
              </el-button>
              <el-button v-if="delectGraphics" @click="delectGraphics=!delectGraphics" plain>取消删除</el-button>
              <el-button type="danger" v-if="delectGraphics" icon="el-icon-delete" @click="delectQiyeGraphics()" plain>
                确定删除
              </el-button>
            </el-col>
          </el-row>
          <el-row v-loading="loading">
            <el-col :span="6" v-for="(item,index) in list" :key="index" style="margin: 30px">
              <el-card :body-style="{ padding: '0px'}" shadow="hover">
                <el-image class="image" :src="item.picurl" fit="contain"></el-image>
                <div style="padding:  0 0 0 10px">
                  <el-checkbox-group v-model="checkList" v-if="delectGraphics" size="large">
                    <el-checkbox-button :label="item.id">
                      <span style="font-size: 20px;font-weight:bold">{{ item.title }}</span>
                    </el-checkbox-button>
                  </el-checkbox-group>
                  <span style="font-size:20px" v-if="!delectGraphics">{{ item.title }}</span>
                  <el-button type="primary" v-if="choiceGraphics" plain round class="button"
                             @click="determineGraphics(item)">选择小程序
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <div class="pagination">
            <Page :total="pageInfo.total" @on-change="onChange" :current="this.dom.current"
                  :show-total="true" :show-sizer="true" :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange" :page-size="9"/>
          </div>
        </el-main>
        <el-dialog title="图文上传" :visible.sync="dialogVisible" width="50%" center>
          <el-form label-width="90px">
            <el-form-item label="标题" required>
              <el-input v-model="dom.title" maxlength="64" style="width:245px">
              </el-input>
            </el-form-item>
            <el-form-item label="描述">
              <el-input v-model="dom.desc" maxlength="256" style="width:245px">
              </el-input>
            </el-form-item>
            <el-form-item label="图文链接" required>
              <el-input v-model="dom.url" style="width:245px">
              </el-input>
            </el-form-item>
            <el-form-item label="封面图片">
              <el-card class="box-card" shadow="always">
                <div class="text item" @click="selectPicture2 = true">
                  <i style=" font-size: 40px; " class="el-icon-plus" v-if="dom.picurl===null"></i>
                  <el-image class="image" :src=dom.picurl fit="contain" v-if="dom.picurl!==null"></el-image>
                </div>
              </el-card>
            </el-form-item>
            <P>注：支持JPG、PNG格式，大图1068*455，小图150*150。</P>
          </el-form>
          <span slot="footer" class="dialog-footer">
               <el-button type="primary" @click="addQiyeGraphics()">立即创建</el-button>
          </span>
        </el-dialog>
        <el-dialog title="选择图片" :visible.sync="selectPicture2" width="70%" append-to-body>
          <pictureSelection :choiceImg="choiceImg" @picture="picture2"></pictureSelection>
        </el-dialog>
      </el-container>
    </el-container>
  </div>
</template>
<script>
//引入组件
import pictureSelection from "./pictureSelection";

export default {
  name: "graphicsContent",
  props: ['choiceGraphics'],
  // 注册组件
  components: {
    pictureSelection,
  },
  data() {
    return {
      choiceImg: true,
      dialogVisible: false,
      selectPicture2: false,
      loading: false,
      delectGraphics: false,
      checkList: [],
      list: [],
      pageSizeOpts: [9, 18, 27],
      pageInfo: {total: 9, size: 9, current: 1, pages: 1},
      dom: {
        title: null,
        desc: null,
        url: null,
        picurl: null,
        operatorId: localStorage.getItem("id"),
        current: 1,
        size: 9,
      }
    }
  },
  created() {
    this.selectQiyeGraphics();
  },
  methods: {
    //选择图文
    determineGraphics(graphics) {
      this.$emit("graphicsContent", graphics)
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dom.current = index;
      this.selectQiyeGraphics();
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dom.size = size;
      this.selectQiyeGraphics();
    },
    //重置
    reset() {
      this.dom.title = null;
      this.dom.desc = null;
      this.dom.url = null;
      this.dom.picurl = null;
      this.dialogVisible = false;
      this.selectQiyeGraphics();
    },
    selectQiyeGraphics() {
      this.$postData("selectQiyeGraphics", this.dom).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    addQiyeGraphics() {
      if (this.dom.title === null && this.dom.title === "") {
        return this.$message.error("标题不能为空");
      }
      if (this.dom.url === null && this.dom.url === "") {
        return this.$message.error("图文链接不能为空");
      }
      this.$postData("addQiyeGraphics", this.dom).then(res => {
        if (res.data > 0) {
          this.reset();
          return this.$message.success("添加成功");
        } else {
          return this.$message.error("添加失败");
        }
      })
    },
    picture2(val) {
      this.dom.picurl = val;
      this.selectPicture2 = false;
    },
    //删除图文
    delectQiyeGraphics() {
      this.delectGraphics = !this.delectGraphics
      this.$postData("delectQiyeGraphics", this.checkList).then(res => {
        if (res.status === 200) {
          this.reset();
          if (res.data === 1) {
            this.$message.success("删除图文成功");
          } else {
            this.$message.error("删除图文失败");
          }
        }
      })
    },
  },
}
</script>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}

.text {
  font-size: 10px;
  text-align: center;
}

.item {
  padding: 10px 0;
}

.box-card {
  width: 180px;
}

</style>