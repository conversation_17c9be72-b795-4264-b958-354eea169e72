<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">
                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="订单编号">
                                <el-input
                                        clearable
                                        v-model="dto.billNo"
                                        placeholder="订单编号"
                                        style="width:180px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="5">
                            <el-form-item label="服务项目">
                                <el-input
                                        clearable
                                        v-model="dto.productName"
                                        placeholder="服务项目"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="违纪人员">
                                <el-input
                                        clearable
                                        v-model="dto.serviceNo"
                                        placeholder="工号/姓名"
                                        style="width:130px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" style="text-align: left">
                            <el-form-item label="投诉判定">
                                <Select filterable clearable style="width: 120px" v-model="dto.violation">
                                    <Option value="">请选择</Option>
                                    <Option value="1">有效违纪</Option>
                                    <Option value="2">无效违纪</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="违纪时间">
                                <el-date-picker
                                        v-model="dto.startTime"
                                        type="datetime"
                                        placeholder="开始时间"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        style="width:180px"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" style="padding-left: 1rem">
                            <el-form-item label="至">
                                <el-date-picker
                                        v-model="dto.endTime"
                                        type="datetime"
                                        placeholder="结束时间"
                                        style="width:180px"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="工单状态">
                                <Select filterable clearable style="width: 120px" v-model="dto.state"
                                        @on-change="query()">
                                    <Option value="">请选择</Option>
                                    <Option value="1">已接单</Option>
                                    <Option value="2">审核阶段</Option>
                                    <Option value="3">处理阶段</Option>
                                    <Option value="4">申诉中</Option>
                                    <Option value="5">申诉成功</Option>
                                    <Option value="6">申诉失败</Option>
                                    <Option value="7">已结案</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="处理人">
                                <el-input
                                        clearable
                                        v-model="dto.dealNo"
                                        placeholder="工号"
                                        style="width:130px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24" style="text-align: right">
                            <el-form-item>
                                <el-button type="primary" style="width:8%" @click="query()">搜索</el-button>
                                <el-button type="success" :loading="loadingExcel" @click="exportExcel">导出Excel
                                </el-button>
                                <el-button type="success" @click="edit">新增违纪
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-table :data="list" border class="table" ref="multipleTable"
                      v-loading="loading">
                <!--<el-table-column-->
                <!--type="selection"-->
                <!--width="55">-->
                <!--</el-table-column>-->
                <el-table-column
                        width="100"
                        label="操作">
                    <template slot-scope="scope">
                        <div style="text-align: center">
                            <div>
                                <el-button size="mini" @click="goDetail(scope.row.id)" type="primary">查看</el-button>
                            </div>
                            <div style="margin-top: 10px">
                                <el-button size="mini"
                                           v-if="scope.row.orderAppeal!=null&&scope.row.orderAppeal.appealReason!==''"
                                           @click="goAppeal(scope.row.id)"
                                           type="warning">申诉处理
                                </el-button>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="billNo"
                        width="140"
                        label="订单编号">
                    <template slot-scope="scope">
                        <a style="text-decoration:underline;"
                           :href="url+scope.row.billNo" target="_Blank">{{scope.row.billNo}}</a>
                    </template>
                </el-table-column>
                <el-table-column
                        width="100"
                        prop="productName"
                        label="服务项目">
                    <template slot-scope="scope">
                        <div @click="goDetail(scope.row.id)">{{scope.row.order.productName}}</div>
                    </template>
                </el-table-column>
                <el-table-column
                        width="80"
                        prop="typeDesc"
                        label="违纪类型"
                ></el-table-column>
                <el-table-column
                        width="80"
                        prop="typeDesc"
                        label="违纪判断">
                    <template slot-scope="scope">
                        <span style="font-size: 12px" v-if="scope.row.violation===1">
                            有效违纪
                        </span>
                        <span style="font-size: 12px" v-else-if="scope.row.violation===2">
                            无效违纪
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                        width="80"
                        label="工单状态"
                        prop="stateDesc"
                ></el-table-column>
                <el-table-column
                        width="110"
                        prop="employeeNo"
                        label="违纪人员">
                    <template slot-scope="scope">
                        <p style="font-size: 12px" v-for="item in scope.row.violationEms">
                            {{item.employeeNo}} ({{item.employeeName}})
                        </p>
                    </template>
                </el-table-column>
                <el-table-column
                        width="150"
                        prop="createTime"
                        label="违纪时间"
                ></el-table-column>
                <el-table-column
                        width="150"
                        prop="lastTime"
                        label="完结时间"
                ></el-table-column>
                <el-table-column
                        width="100"
                        prop="violationEms[0].levelDesc"
                        label="处罚等级"
                ></el-table-column>
                <el-table-column
                        width="100"
                        prop="violationEms[0].offenceFine"
                        label="违纪罚金"
                ></el-table-column>
                <el-table-column
                        width="100"
                        prop="violationEms[0].punishmentDesc"
                        label="停工处罚"
                ></el-table-column>
                <el-table-column
                        width="90"
                        prop="dealNo"
                        label="处理人"
                ></el-table-column>
            </el-table>

            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :current="this.dto.pageNum"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="10"/>
            </div>
        </div>
        <Modal v-model="tsModal" class="Modal" :width="screenWidth" title="新增"
               :mask-closable="false"
               @on-cancel="getData()">
            <div class="addBody">
                <violation-insert v-if="tsModal" @init-choose="initChooseProject"
                                  @close-modal="closeCurrModal"></violation-insert>
            </div>
            <div slot="footer">
            </div>
        </Modal>

        <violation-details v-if="modal"
                           @init-choose="initChooseProject"
                           :id="detailId">
        </violation-details>

        <violation-appeal v-if="appealModal"
                          @init-choose="initChooseProject"
                          :id="detailId">
        </violation-appeal>
    </div>

</template>

<script>
    import violationInsert from '@/components/page/wordOrder/choose/violationInsert.vue'
    import violationDetails from '@/components/page/wordOrder/choose/violationDetails.vue'
    import violationAppeal from '@/components/page/wordOrder/choose/violationAppeal.vue'

    export default {
        data() {
            return {
                modal: false,
                appealModal: false,
                loading: true,
                loadingExcel: false,
                tsId: null,
                screenWidth: '40%',
                screenWidth1: '50%',
                tsModal: false,
                detailId: null,
                appeal: false,
                list: null,
                url: "https://yun.xiaoyujia.com/Account/TokenLogin?Token=" + localStorage.getItem('token') + "&returnUrl=/order/baseinfo?BillNo=",
                pageSizeOpts: [10, 20, 30],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                dto: {
                    billNo: null,
                    dealNo: null,
                    startTime: null,
                    endTime: null,
                    state: null,
                    pageSize: 10,
                    pageNum: 1,
                    violation: null,
                    productName: null,
                    serviceNo: null
                },
            };
        },
        components: {
            "violationInsert": violationInsert,
            "violationDetails": violationDetails,
            "violationAppeal": violationAppeal
        },
        created() {
            this.getData();
        },
        computed: {},
        methods: {
            getData(data) {
                console.log(data);
                this.$postData("violation_getList", this.dto, {}).then(res => {
                    this.loading = false;
                    if (res.status === 200) {
                       ;
                        this.list = res.data.list;
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    }
                });
            },
            edit() {
                this.tsModal = true;
            },
            goDetail(id) {
                this.detailId = id;
                this.modal = true
            },
            goAppeal(id) {
                this.detailId = id;
                this.appealModal = true
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal(data) {
                this.tsModal = false;
                this.appealModal = false;
                this.modal = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },
            query() {
                this.loading = true;
                this.dto.pageNum = 1;
                this.getData();
            },

            // 页码大小
            onPageSizeChange(size) {
                this.loading = true;
                this.dto.pageSize = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                this.loading = true;
                this.dto.pageNum = index;
                this.getData();
            },
            exportExcel() {
                this.loadingExcel = true;
                this.$postData("violation_exportList", this.dto, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.loadingExcel = false;
                    this.blobExport({
                        tablename: "违纪工单",
                        res: res
                    });
                });
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .table {
        width: 100%;
        font-size: 13px;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
