<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="9">
            <el-form-item label="提交时间">
              <el-date-picker v-model="days" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6" >
            <el-form-item  label="所属门店" >
              <el-select  filterable v-model="form.storeId" clearable placeholder="请选择所属门店">
                <el-option v-for="item in storeOptions"
                           :key="item.id" :label="item.storeName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item  label="状态">
              <el-select style="width:160px" v-model="form.state" clearable placeholder="请选择状态">
                <el-option v-for="item in options"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="3" >
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="填报记录">
          <el-table :data="logList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column prop="createTime" label="提交时间" width="150"></el-table-column>
            <el-table-column prop="storeName" label="门店名称" width="170"></el-table-column>
            <el-table-column prop="accountName" label="开户名称" width="150"></el-table-column>
            <el-table-column prop="accountOpenBank" label="开户支行" width="150"></el-table-column>
            <el-table-column prop="bankCode" label="银行卡号" width="150"></el-table-column>
            <el-table-column prop="bankType" label="银行卡类型" width="100"></el-table-column>
            <el-table-column prop="accountOpenAddress" label="银行卡开户地址" width="150"></el-table-column>
            <el-table-column prop="depositAccountImg" label="基本存款账户信息图片" width="150">
              <template slot-scope="scope">
                <img v-image-preview :src="scope.row.depositAccountImg"  style="width: 100%" />
              </template>
            </el-table-column>
            <el-table-column prop="businessLicenseImg" label="营业执照" width="150">
              <template slot-scope="scope">
                <img v-image-preview :src="scope.row.businessLicenseImg"  style="width: 100%"/>
              </template>
            </el-table-column>
            <el-table-column prop="state" label="状态" width="100">
              <template slot-scope="scope">
                <span>{{scope.row.state==1?'待审核':scope.row.state==2?'已通过':'不通过'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="businessCode" label="社会统一信用代码" width="150"></el-table-column>
            <el-table-column prop="companyName" label="企业名称" width="150"></el-table-column>
            <el-table-column prop="legalRepresentative" label="法定代表人" width="100"></el-table-column>
            <el-table-column prop="companyType" label="企业类型" width="160"></el-table-column>
            <el-table-column prop="remark" label="备注" width="100"></el-table-column>
            <el-table-column prop="operatorPeopleName" label="审批人" width="100"></el-table-column>
            <el-table-column prop="operateTime" label="审批时间" width="150"></el-table-column>
            <el-table-column prop="updateTime" label="修改时间" width="150"></el-table-column>
            <el-table-column label="操作" fixed="right" width="100">
              <template slot-scope="scope">
                <el-button :disabled="scope.row.state===2||scope.row.state===3" @click.native.prevent="openModel(scope.row)" type="text" size="small">操作
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
        <el-dialog title="处理" :visible.sync="innerVisible" append-to-body center>
          <el-form>
            <el-form-item label="状态">
              <el-radio-group v-model="dom.state">
                <el-radio label="3">不通过</el-radio>
                <el-radio label="2">通过</el-radio>
              </el-radio-group>
            </el-form-item>
            <br>
            <el-form-item v-if="dom.state==='3'" label="未通过原由">
              <el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="dom.remark"> </el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="updateStoreAccountAuditing">确定</el-button>
          </div>
        </el-dialog>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import moment from "moment";

export default {
  name: "register",
  data() {
    return {
      logList: [],
      storeOptions: [],
      handler: [],
      days: [],
      updateItem: {},
      innerVisible: false,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      form: {
        state: null,
        startTime: null,
        endTime: null,
        storeId: null,
        current: 1,
        size: 10
      },
      dom: {
        state: null,
        remark: null,
      },
      options: [{
        value: '1',
        label: '待审核'
      }, {
        value: '2',
        label: '已通过'
      }, {
        value: '3',
        label: '不通过'
      }
      ],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },
  created() {
    this.getData();
    this.getAllFranchiseStore();
  },
  methods: {
    openModel(item){
      this.updateItem = item
      this.innerVisible=true
    },
    getAllFranchiseStore(){
      this.$getData("getAllFranchiseStore", {}, {}).then(res => {
        if (res.code == 0) {
          this.storeOptions = res.data
        } else {
          this.$message.error("查询筛选门店失败!");
        }
      })
    },
    updateStoreAccountAuditing() {
      this.updateItem.operatorPeople  = localStorage.getItem("id")
      this.updateItem.remark  =this.dom.remark
      this.updateItem.state  =this.dom.state
      this.innerVisible = false;
      this.$postData("updateStoreAccountAuditing", this.updateItem).then(res => {
        if (res.code === 0) {
          this.dom.remark = null
          this.dom.state = null
          this.$message({message: '编辑成功', type: 'success'});
        } else {
          this.$message({message: '编辑失败', type: 'warning'});
        }
        this.getData();
      })
    },
    // 页码大小
    onPageSizeChange(size) {
      this.form.size = size;
      this.getData();
    },
    onChange(index) {
      this.form.current = index;
      this.getData();
    },
    getData() {
      this.getAllFranchiseStore()
      this.form.startTime = null;
      this.form.endTime = null;
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      this.$getData("getStoreAccountAuditing", this.form, {}).then(res => {
        if (res.code == 0) {
          this.logList = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    }
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
