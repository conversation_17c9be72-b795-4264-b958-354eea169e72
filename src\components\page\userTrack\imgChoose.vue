<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">

            <div>
                <img :src="url" style="width: 100%"/>
            </div>
        </div>
    </div>
</template>
<script>
    export default {
        props: ['url'],
        data() {
            return {
             url:this.url
            }
        },
        components: {},
        created: function () {
        },
        methods: {


            /*
         * 关闭当前窗口
         * */
            chooseThisModel(dto) {
                this.$emit('init-choose', dto);
                //this.update(this[dto])
            },
        }
    }
</script>


<style scoped>


    .fromInput {
        float: left;
    }

    #bodyDiv {
        font-size: 15px !important;
        background-color: #fff;
        height: 500px;
    }
</style>

