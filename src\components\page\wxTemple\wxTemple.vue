<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">
                    <el-row>
                        <el-col :span="9">
                            <el-form-item label="标题">
                                <el-input
                                        v-model="temple.title"
                                        placeholder="标题"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="模版Id">
                                <el-input
                                        v-model="temple.templeId"
                                        placeholder="模版Id"
                                        style="width:300px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6" style="text-align:right">
                            <el-form-item>
                                <el-button type="success" style="width:30%"
                                           :loading="loading"
                                           @click="saveTempleList">更新模版</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>

                        <el-col :span="9">
                            <el-form-item label="头部">
                                <el-input
                                        v-model="temple.firstData"
                                        placeholder="头部"
                                        style="width:300px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>


                        <el-col :span="8">
                            <el-form-item label="尾部">
                                <el-input
                                        v-model="temple.remarkData"
                                        placeholder="尾部"
                                        style="width:300px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6" style="text-align:right">
                            <el-form-item>
                                <el-button type="primary" style="width:30%" @click="query()">搜索</el-button>
                                <el-button type="success" style="width:30%" @click="exportList">导出Excel</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-table :data="list" border class="table" ref="multipleTable">
                <el-table-column
                        prop="templeId"
                        label="模板Id"
                        width="400"
                ></el-table-column>
                <el-table-column
                        prop="title"
                        label="标题"
                        width="250"
                ></el-table-column>
                <el-table-column
                        prop="firstData"
                        label="头部"
                        width="150"
                ></el-table-column>
                <el-table-column
                        prop="keyNum"
                        label="数量"
                        width="120"
                ></el-table-column>
                <el-table-column
                        prop="remarkData"
                        label="尾部"
                        width="150"
                ></el-table-column>
                <el-table-column
                        label="操作">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="edit(scope.row.id)" type="success">编辑</el-button>
                        <el-button size="mini" @click="update(scope.row.id)" type="danger">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="10"/>
            </div>
        </div>


        <!--编辑-->
        <Modal v-model="editModal" class="Modal" :width="screenWidth" title="编辑" :mask-closable="false">
            <div class="addBody">

                <wx-temple-update v-if="editModal" :templeId="templeId" @init-choose="initChooseProject"
                                       @close-modal="closeCurrModal"></wx-temple-update>
            </div>
            <div slot="footer">
            </div>
        </Modal>

    </div>
</template>

<script>
    import wxTempleUpdate from '@/components/page/wxTemple/choose/wxTempleUpdate.vue'


    export default {
        data() {
            return {
                loading:false,
                templeId:null,
                list:null,
                tableData: [],
                pageSizeOpts: [10, 20, 30],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                screenWidth: '45%',//新增对话框 宽度
                editModal: false,
                temple: {
                    templeId: null,
                    title: null,
                    firstData: null,
                    remarkData: null,
                    pageSize: 10,
                    pageNum: 1,
                },

            };
        },
        components: {
            'wxTempleUpdate': wxTempleUpdate,
        },
        created() {
            this.getData();
        },
        computed: {
        },
        methods: {
            getData() {
                this.$postData("push_getList", this.temple, {}).then(res => {
                    if (res.status == 200) {

                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total;
                        this.list=res.data.list;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })

            },
            query() {
                this.getData();
            },
            edit(id) {
                this.editModal = true;
                this.templeId=id;
            },
            saveTempleList(){
                this.loading=true;
                this.$postData("push_saveTempleList",{}, {}).then(res => {
                    if (res.status == 200) {
                        this.loading=false;
                        this.$message.success("更新成功");
                        this.getData();
                    } else {
                        this.loading=false;
                        this.$message.error("更新失败，" + res.msg);
                    }
                })
            },
            exportList() {
                this.$postData("push_export", this.temple, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tablename: "模版列表",
                        res: res
                    });
                });
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },

            // 页码大小
            onPageSizeChange(size) {
                this.temple.pageSize = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                console.log(index)
                this.temple.pageNum = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.editModal = false
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData();
            },
            update(id) {
                this.$confirm('此操作将永久性删除, 是否继续?', '提示', {
                    confirmButtonText: '删除',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$getData("push_delete", {wxTempleManageId:id}).then(res => {
                        if (res.status == 200) {
                            this.$message.success("删除成功");
                            this.getData();
                        } else {
                            this.$message.error("删除失败" + res.msg);
                        }
                    })

                }).catch(() => {

                });
            },
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
