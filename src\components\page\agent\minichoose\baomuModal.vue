<template>
    <div>
        <el-input v-model="queryStr" placeholder="请输入保姆名称查询"
                  style="width: 200px; margin-right: 10px;margin-bottom: 10px;"></el-input>
        <el-button @click="queryNanny" plain type="primary">查询</el-button>
        <el-transfer
                v-loading="loading"
                filter-placeholder="请输入保姆名称"
                v-model="value"
                :titles="['可关联保姆', '已关联保姆']"
                :data="data">
        </el-transfer>
        <div style="margin-right: 5px;float: right;margin-top: 10px">
            <Button type="primary" @click="save('form')">确定</Button>
            <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
        </div>
    </div>

</template>

<script>
    export default {
        props: ['model'],
        name: "siteModal",
        data() {
            return {
                queryStr: null,
                loading: true,
                data: [],
                value: [],
                dom: this.model,
                isAngent: false,
                agentmodel: {
                    agentName: "agentName"
                }
            }
        },
        created() {
            this.inint();
            if (this.dom.roleId == 82) {
                this.isAngent = true
            }
        },
        methods: {
            save() {
                if (this.value.length > 1000) {
                    return this.$message.error("请关联 1000 个以下的保姆！");
                } else {
                    this.$postUrl("up_Baomus", this.dom.id, this.value, {}).then(res => {
                        if (res.status === 200) {
                            this.$message.success("关联成功，" + res.msg);
                            this.chooseThisModel("siteModal")
                        } else {
                            this.$message.error("关联失败，" + res.msg);
                        }
                    })
                }
            },
            async queryNanny() {
                if (this.queryStr == null || this.queryStr === '') {
                    return this.$message.error("请输入搜索内容再查询");
                }

                await this.$getData("getNannyByName", {query: this.queryStr}).then(res => {
                    if (res.status === 200) {
                        if (res.data == null || res.data.length < 1) {
                            return this.$message.error("查询失败，" + res.msg);
                        }
                        res.data.forEach((b, index) => {
                            const temp = this.data.find(item => item.key === b.id);
                            // 等于 undefined 证明不在穿梭框右边列中，则添加至数据集中
                            // 但是每次查询是没有移除前面的查询结果，因此数据集结果是累加的。
                            if (temp == undefined) {
                                const param = {
                                    label: b.realName + "/" + b.no,
                                    key: b.id,
                                };
                                this.data.push(param);
                            }
                        });
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                });
            },
            inint() {
                // this.$postData("baomu_list_mini", this.agentmodel, {}).then(res => {
                //     if (res.status == 200) {
                //         res.data.forEach((city, index) => {
                //             this.data.push({
                //                 label: city.realName + "/" + city.no,
                //                 key: city.id,
                //             });
                //             this.loading = false
                //         });
                //
                //     } else {
                //         this.$message.error("查询失败，" + res.msg);
                //     }
                // })
                this.agentmodel = this.dom;
                this.agentmodel.sites = null;
                this.agentmodel.status = 1;
                this.$postData("agentBaomu_list", {id: this.agentmodel.id}, {}).then(res => {
                    if (res.status == 200) {
                        res.data.forEach((c, index) => {
                            this.data.push({
                                label: c.realName + "/" + c.no,
                                key: c.id,
                            });
                            this.value.push(c.id);
                        });
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                }).finally(_ => {
                    this.loading = false;
                })
            },
            chooseThisModel(name) {
                this.dom = null;
                this.$emit('init-choose', name);
            },
        }
    }
</script>

<style scoped>

</style>
