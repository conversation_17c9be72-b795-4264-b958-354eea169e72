<template>
    <div>
        认证日期区间：
        <el-date-picker
                v-model="value2"
                type="datetimerange"
                :picker-options="pickerOptions"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                align="right">
        </el-date-picker>
        <el-button type="success"   @click="search()"  icon="el-icon-search">查询</el-button>
<!--        <el-button type="info"   @click="value2=null,search" icon="el-icon-refresh-right">重置查询</el-button>-->
        <br>
        <br>
        <div v-for="item in jzy" class="jzy-box">
            <span>{{getName(item.type)}}</span>
            <span style="margin-left: 20px;color: red">{{item.num}} </span>次
        </div>
        <div v-if="jzy==null||jzy.length==0">
            <h3>查无认证记录，请更换时间试试</h3>
        </div>

    </div>
</template>

<script>
    export default {
        name: "jzyDetail",
        data(){
            return{
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                value2: null,
                dom:{
                    crePerson:localStorage.getItem("realName"),
                    startTime:null,
                    endTime:null,
                },
                jzy:[],
            }
        },
        created(){
            this.getData()
        },
        methods:{
            search(){
                console.log(this.value2)
                this.dom.startTime=null
                this.dom.endTime=null
                if (this.value2==null) {
                    this.dom.startTime=null
                    this.dom.endTime=null
                }else {
                    this.dom.startTime=this.value2[0]
                    this.dom.endTime=this.value2[1]
                }
                this.getData()
            },
            getName(type){
                switch (type) {
                    case '0021':
                        return "照片人证比对";
                    case '0626':
                        return "不良人员名单";
                    case '0627':
                        return "法院信息详情查询";
                    case '04216':
                        return "特殊名单验证";


                }
            },
            getData() {

                this.$postData("getJzyNum", this.dom, {}).then(res => {
                    if (res.status == 200) {

                        this.jzy=res.data

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
        }
    }
</script>

<style scoped>
    .jzy-box{
        padding: 0.2rem;
        border: 1px solid #ddd;
        margin: 0.2rem;
        font-size: 24px;
    }
</style>
