<template>
  <div>
    <div ref="imageDom">
      <el-form v-loading="loading" ref="form" :model="dom" label-width="100px" style="background:#FFFFFF"
               label-position="right">
        <el-form-item label="类型" v-if="dom.type===1" required>
          <el-radio-group v-model="dom.type">
            <el-radio-button :label="1">单人</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="类型" v-if="dom.type===2" required>
          <el-radio-group v-model="dom.type">
            <el-radio-button :label="2">多人</el-radio-button>
          </el-radio-group>
          注：人数最多不超过100人
        </el-form-item>
        <!--        <el-form-item v-if="dom.type===2" label="种类" required>-->
        <!--          <el-radio-group v-model="type">-->
        <!--            <el-radio-button :label="1">按人员</el-radio-button>-->
        <!--            <el-radio-button :label="2">按部门</el-radio-button>-->
        <!--          </el-radio-group>-->
        <!--          -->
        <!--        </el-form-item>-->
        <el-form-item label="活码名称" required>
          <el-input v-model="dom.name" style="width:245px"></el-input>
        </el-form-item>
        <el-form-item label="客户标签" required>
          <ul>
            <li class="tags-li" v-for="(item,index) in qiyeState.label" :key="index">
              <span id="">
                {{ item }}
              </span>
              <span class="tags-li-icon" @click="closeTags(index)"><i class="el-icon-close"></i></span>
            </li>
          </ul>
          <el-button type="primary" @click="dialogVisible = true" plain icon="el-icon-plus">添加标签</el-button>
        </el-form-item>
        <el-form-item label="部门" required>
          <el-select v-model="dom.party" value-key="id" filterable clearable @change="getPartyUser" placeholder="使用部门">
            <el-option
                v-for="item in partys"
                :key="item.id"
                :label="item.name"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="人员" v-if="type!==2" required>
          <el-select v-model="dom.user" filterable multiple placeholder="使用人员" @change="changeSele">
            <el-option v-for="item in users"
                       :key="item.id"
                       :label="item.name"
                       :value="item.userid">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="回复类型" required>
          <el-checkbox-group v-model="qiyeState.type">
            <el-checkbox-button :label='"0"'>文本</el-checkbox-button>
            <el-checkbox-button :label='"1"'>图片</el-checkbox-button>
            <el-checkbox-button :label='"2"'>图文</el-checkbox-button>
            <el-checkbox-button :label='"3"'>小程序</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="文本" v-if="qiyeState.type.indexOf('0')!==-1" required>
          <el-input type="textarea" placeholder="请输入内容"
                    :autosize="{ minRows:3,}" v-model="qiyeState.content"
                    maxlength="2000" style="width:300px"
          ></el-input>
          <el-divider></el-divider>
        </el-form-item>
        <el-form-item label="图片" v-if="qiyeState.type.indexOf('1')!==-1" required>
          <el-card class="box-card" shadow="always">
            <div class="text item" @click="selectPicture = true">
              <i style=" font-size: 40px; " class="el-icon-plus" v-if="qiyeState.pic_url===null"></i>
              <el-image class="image" :src=qiyeState.pic_url fit="contain" v-if="qiyeState.pic_url!==null"></el-image>
            </div>
          </el-card>
          <el-divider></el-divider>
        </el-form-item>
        <div v-if="qiyeState.type.indexOf('2')!==-1">
          <el-form-item label="标题" required>
            <el-input v-model="qiyeState.title" maxlength="64" style="width:245px">
            </el-input>
          </el-form-item>
          <el-form-item label="封面图片">
            <el-card class="box-card" shadow="always">
              <div class="text item" @click="selectPicture2 = true">
                <i style=" font-size: 40px; " class="el-icon-plus" v-if="qiyeState.picurl===null"></i>
                <el-image class="image" :src=qiyeState.picurl fit="contain" v-if="qiyeState.picurl!==null"></el-image>
              </div>
            </el-card>
          </el-form-item>
          <el-form-item label="描述">
            <el-input v-model="qiyeState.desc" maxlength="256" style="width:245px">
            </el-input>
          </el-form-item>
          <el-form-item label="图文链接" required>
            <el-input v-model="qiyeState.url" style="width:245px">
            </el-input>
          </el-form-item>
          <el-divider></el-divider>
        </div>
        <el-form-item label="选择小程序" v-if="qiyeState.type.indexOf('3')!==-1" required>
          <el-card class="box-card" shadow="always">
            <div class="text item" @click="selectApplet = true">
              <i style=" font-size: 40px; " class="el-icon-plus" v-if="qiyeState.sparePicture===null"></i>
              <el-image class="image" :src=qiyeState.sparePicture fit="contain"
                        v-if="qiyeState.sparePicture!==null"></el-image>
            </div>
          </el-card>
        </el-form-item>
        <el-dialog title="选择标签" :visible.sync="dialogVisible" width="550px" append-to-body>
          <div v-for="item in qiyeLabelGroup">
            <el-form-item :label="item.group_name">
              <el-checkbox-group v-model="qiyeState.label" value-key="id">
                <el-checkbox-button v-for="(val,index) in qiyeLabel" v-show="val.qiyeLabelGroupId===item.id"
                                    :key="index"
                                    :label="val.name"
                                    @change="change(val.id)"
                >{{ val.name }}
                </el-checkbox-button>
              </el-checkbox-group>
            </el-form-item>
          </div>
          <div style="margin-top: 15px;">
            <el-row type="flex" justify="center">
              <el-col :span="3">
                <el-button @click="dialogVisible = false" size="large">取 消</el-button>
              </el-col>
              <el-col :span="3"></el-col>
              <el-col :span="3">
                <el-button type="success" size="large" @click="dialogVisible = false">确定</el-button>
              </el-col>
            </el-row>
          </div>
        </el-dialog>
        <el-dialog title="选择图片" :visible.sync="selectPicture" width="70%" append-to-body>
          <pictureSelection :choiceImg="choiceImg" @picture="picture"></pictureSelection>
        </el-dialog>
        <el-dialog title="选择图片" :visible.sync="selectPicture2" width="70%" append-to-body>
          <pictureSelection :choiceImg="choiceImg" @picture="picture2"></pictureSelection>
        </el-dialog>
        <el-dialog title="选择小程序" :visible.sync="selectApplet" width="70%" append-to-body>
          <applet @applet="applet"></applet>
        </el-dialog>
        <br>
        <el-form-item>
          <el-button type="primary" @click="addQiyeState">修改活码</el-button>
        </el-form-item>
        <br>
      </el-form>
    </div>
  </div>
</template>

<script>
//引入组件
import pictureSelection from "./pictureSelection";
import axios from 'axios'
import applet from "./applet";

export default {
  name: "updateState",
  props: ['qiyeChannelId'],
  // 注册组件
  components: {
    pictureSelection,
    applet,
  },
  data() {
    return {
      loading: false,
      //种类
      type: null,
      type1: [],
      dialogVisible: false,
      clickUpload: true,
      selectPicture: false,
      selectPicture2: false,
      selectApplet: false,
      imgList: [],
      //部门
      partys: [],
      //人员
      users: [],
      choiceImg: true,
      user2: [],
      dom: {
        name: null,
        //单人、多人
        type: null,
        //使用该联系方式的部门id列表，只在type为2时有效
        party: null,
        //用该联系方式的用户userID列表，在type为1时为必填，且只能有一个
        user: [],
        //1-在小程序中联系，2-通过二维码联系
        scene: 2,
        userName: [],
        partyName: null,
        creatorId: null,
      },
      pic_url: [],
      qiyeState: {
        label: [],
        labelId: [],
        type: [],
        content: null,
        pic_url: null,
        title: null,
        picurl: null,
        desc: null,
        url: null,
        name: null,
        miniprogramTitle: null,
        pic_media_id: null,
        appid: null,
        page: null,
        sparePicture: null,
      },
      //标签组
      qiyeLabelGroup: {
        group_id: [],
        group_name: [],
      },
      //标签
      qiyeLabel: {
        id: [],
        name: [],
        qiyeLabelGroupId: [],
      },
      dam: {
        type: 1,
        img: null,
        qiyeImg: null,
        operatorId: null,
        dataLabelId: null,
        dataLabelName: null,
        name: null,
      }
    };
  },
  watch: {
    qiyeChannelId(newValue, oldValue) {
      this.qiyeChannelId = newValue
      if (null !== this.qiyeChannelId && undefined !== this.qiyeChannelId) {
        this.getQiyeChannel(this.qiyeChannelId)
        this.getQiyeState(this.qiyeChannelId)
      }
    }
  },
  created() {
    this.dom.creatorId = localStorage.getItem("id")
    this.getParty()
    this.getQiyeLabelGroup()
    this.getQiyeLabel()
    this.getPartyUser2()
    if (null !== this.qiyeChannelId && undefined !== this.qiyeChannelId) {
      this.getQiyeChannel(this.qiyeChannelId)
      this.getQiyeState(this.qiyeChannelId)
    }
  },
  methods: {
    changeSele(val) {
      this.dom.userName = [],
          val.forEach(e => {
            this.user2.find((v) => {
              if (v.userid === e) {
                this.dom.userName.push(v.name);
              }
            });
          })
    },
    // 删除单个标签
    closeTags(index) {
      this.qiyeState.label.splice(index, 1)[0];
      this.qiyeState.labelId.splice(index, 1)[0];
    },
    reset() {
      this.dom.party = null
      this.dom.user = []
      this.type = null
      this.qiyeState.label = []
      this.qiyeState.labelId = []
      this.qiyeState.type = null
      this.qiyeState.content = null
      this.qiyeState.pic_url = null
      this.qiyeState.title = null
      this.qiyeState.picurl = null
      this.qiyeState.desc = null
      this.qiyeState.url = null
    },
    async addQiyeState() {
      this.loading = true;
      if (null == this.dom.type) {
        this.loading = false;
        return this.$message({message: '类型不能为空', type: 'warning'});
      } else if (null == this.qiyeState.label) {
        this.loading = false;
        return this.$message({message: '标签不能为空', type: 'warning'});
      } else if (null == this.dom.party) {
        this.loading = false;
        return this.$message({message: '部门不能为空', type: 'warning'});
      } else if (null == this.dom.name) {
        this.loading = false;
        return this.$message({message: '名称不能为空', type: 'warning'});
      } else if (this.dom.type === 1 && this.dom.user.length > 1) {
        this.loading = false;
        return this.$message({message: '类型与人数不一致', type: 'warning'});
      }
      this.qiyeState.label = this.qiyeState.label.join(",");
      this.qiyeState.labelId = this.qiyeState.labelId.join(",");
      this.dom.userName = this.dom.userName.join(",");
      if (this.qiyeState.type.indexOf('3') !== -1) {
        await this.picMediaId()
      } else {
        this.updateQiyeState()
      }
    },
    picMediaId() {
      this.$getData("pic_media_id", {sparePicture: this.qiyeState.sparePicture}).then(res => {
        if (res.status === 200) {
          this.qiyeState.pic_media_id = res.data
          this.updateQiyeState()
        }
      })
    },
    updateQiyeState() {
      this.qiyeState.type = this.qiyeState.type.join(",");
      let qiyeStateDto = {
        "qiyeState": this.qiyeState,
        "qiyeStates": this.dom,
      }
      this.$postData("updateQiyeState", qiyeStateDto).then(res => {
        if (res.status === 200) {
          this.loading = false;
          this.qiyeState.label = this.qiyeState.label.split(",");
          this.$message({message: '修改成功', type: 'success'});
          this.$emit("updateByvalue", false)
        }
      })
    },
    //标签组
    getQiyeLabelGroup() {
      this.$getData("getQiyeLabelGroup",).then(res => {
        if (res.status === 200) {
          this.qiyeLabelGroup = res.data
        }
      })
    },
    //标签
    getQiyeLabel() {
      this.$getData("getQiyeLabel",).then(res => {
        if (res.status === 200) {
          this.qiyeLabel = res.data
        }
      })
    },
    //部门
    getParty() {
      this.$getData("getParty",).then(res => {
        if (res.status === 200) {
          this.partys = res.data
        }
      })
    },
    //人员
    getPartyUser(id) {
      this.partys.find((v) => {
        if (v.id === id) {
          this.dom.partyName = v.name
        }
      });
      this.$getData("getPartyUser", {id: id}).then(res => {
        if (res.status === 200) {
          this.users = res.data
        }
      })
    },
    getPartyUser2() {
      this.$getData("getPartyUser2",).then(res => {
        if (res.status === 200) {
          this.user2 = res.data
        }
      })
    },
    change(id) {
      if (this.qiyeState.labelId.indexOf(id) >= 0) {
        this.qiyeState.labelId.splice(this.qiyeState.labelId.indexOf(id), 1);
      } else {
        this.qiyeState.labelId.push(id);
      }
    },
    handleRemove() {
      this.clickUpload = true
      this.qiyeState.picurl = null
      this.qiyeState.pic_url = null
    },
    upload(param) {
      var formData = new FormData();
      formData.append("flies", param.file);
      axios.post(`/qiyeCustomer/ploadPicturesu`, formData, {
        headers: {'Content-Type': 'multipart/form-data'}
      }).then((res) => {
        if (res.status === 200) {
          this.clickUpload = false
          this.qiyeState.picurl = res.data
        }
      })
    },
    upload2(param) {
      var formData = new FormData();
      formData.append("flies", param.file);
      axios.post(`/qiyeCustomer/qiyeUploadPictures`, formData, {
        headers: {'Content-Type': 'multipart/form-data'}
      }).then((res) => {
        if (res.status === 200) {
          this.qiyeState.pic_url = res.data
        }
      })
    },
    getQiyeChannel(id) {
      this.$getData("getQiyeChannel", {id: id}).then(res => {
        if (res.status === 200) {
          this.dom = res.data
          this.dom.user = (res.data.user || "").split(',')
          this.dom.userName = (res.data.userName || "").split(',')
          if (null != this.dom.party) {
            this.getPartyUser(this.dom.party)
          }
        }
      })
    },
    getQiyeState(id) {
      this.$getData("getQiyeState", {id: id}).then(res => {
        if (res.status === 200) {
          this.qiyeState = res.data
          this.qiyeState.type = this.qiyeState.type.split(",");
          this.qiyeState.label = (res.data.label || "").split(',')
          this.qiyeState.labelId = (res.data.labelId || "").split(',')
        }
      })
    },
    //选择图片
    picture(val) {
      this.qiyeState.pic_url = val
      this.selectPicture = false
    },
    picture2(val) {
      this.qiyeState.picurl = val
      this.selectPicture2 = false
    },
    applet(app) {
      this.qiyeState.miniprogramTitle = app.miniprogramTitle
      this.qiyeState.pic_media_id = app.pic_media_id
      this.qiyeState.appid = app.appid
      this.qiyeState.page = app.page
      this.qiyeState.sparePicture = app.sparePicture
      this.selectApplet = false
    },
    //文件上传限制
    uploadSectionFile(param) {
      let fileObj = param.file;
      const isLt2M = fileObj.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
        return;
      }
      if (fileObj.type === "image/jpg") {
        this.pic_url = new File([fileObj], new Date().getTime() + ".jpg", {
          type: "image/jpg"
        });
      } else if (fileObj.type === "image/png") {
        this.pic_url = new File([fileObj], new Date().getTime() + ".png", {
          type: "image/png"
        });
      } else if (fileObj.type === "image/jpeg") {
        this.pic_url = new File([fileObj], new Date().getTime() + ".jpeg", {
          type: "image/jpeg"
        });
      } else {
        this.$message.error("只能上传jpg/png文件");
        return;
      }
    },
  }
}
</script>

<style scoped>
.text {
  font-size: 10px;
  text-align: center;
}

.item {
  padding: 10px 0;
}

.box-card {
  width: 180px;
}

.button {

  float: right;
}

.image {
  width: 100%;
  height: 100px;
  display: block;
}
</style>