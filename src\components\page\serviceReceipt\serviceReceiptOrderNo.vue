<template>
  <div class="container">
    <div class="handle-box">
      <el-form ref="form" :model="pageInfo">
        <el-row>
          <el-col :span="5">
            <el-form-item label="订单号">
              <el-input
                  clearable
                  v-model="dto.orderNo"
                  placeholder="订单号"
                  style="width:170px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="服务产品">
              <el-input
                  clearable
                  v-model="dto.serviceProject"
                  placeholder="服务产品"
                  style="width:180px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="9" class="block">
            <el-form-item label="订单日期">
              <el-date-picker
                  v-model="dto.days"
                  type="daterange"
                  format="yyyy 年 MM 月 dd 日"
                  value-format="yyyy-MM-dd"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">

          </el-col>
        </el-row>
        <el-row>
          <el-col :span="3" class="block">
            <el-form-item label="评分">
              <Select filterable clearable style="width:80px" v-model="dto.evaluateGrade"
                      @on-change="query()">
                <Option value="">请选择</Option>
                <Option value=0>不满意</Option>
                <Option value="1">一般</Option>
                <Option value="2">满意</Option>
              </Select>
            </el-form-item>
          </el-col>
          <el-col :span="3" class="block">
            <el-form-item label="异常" class="block">
              <Select filterable clearable style="width: 80px" v-model="dto.abnormal"
                      @on-change="query()">
                <Option value="">请选择</Option>
                <Option value="0">无异常</Option>
                <Option value="1">异常</Option>
              </Select>
            </el-form-item>
          </el-col>
          <el-col :span="4" class="block">
            <el-form-item label="状态">
              <Select filterable clearable style="width: 120px" v-model="dto.state"
                      @on-change="query()">
                <Option value="">请选择</Option>
                <Option value="1">订单完成</Option>
                <Option value="2">客户退款</Option>
                <Option value="3">员工退款未到账</Option>
                <Option value="4">员工退款已到账</Option>
              </Select>
            </el-form-item>
          </el-col>
          <el-col :span="4" class="block">
            <el-form-item label="异常内容" class="block">
              <el-input
                  clearable
                  v-model="dto.exceptionContent"
                  placeholder="异常内容"
                  style="width:100px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-button type="primary" @click="query()" icon="el-icon-search" style="margin-left: 20px">搜索
          </el-button>
          <el-button icon="el-icon-refresh" @click="reset()" style="margin-left: 20px">
            重置
          </el-button>
          <el-button icon="el-icon-refresh" type="success" plain @click="anomalyDetection()" style="margin-left: 20px">
            异常检测
          </el-button>
        </el-row>
        <el-row>
        <div style="float: right">
          <el-input placeholder="请输入订单号" style="width:300px" clearable v-model="orderNo">
            <el-button  style="background: #909399;color: #f8f8f8" slot="append" @click="QRcode(orderNo)" icon="el-icon-search">二维码搜索</el-button>
          </el-input><br/>
          <span style="color: #A4A4A4">注：专门为获取客户签收、评价的二维码或链接而设置的搜索</span>
        </div>

        </el-row>
      </el-form>
    </div>

    <el-drawer :visible.sync="journalInfoDrawer" size="90%">
      <el-table :data="journalList" class="table" ref="multipleTable" v-loading="journalInfoLoading"
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
        <el-table-column width="150" prop="orderNo" label="订单号"></el-table-column>
        <el-table-column width="150" prop="operationTime" label="操作时间"></el-table-column>
        <el-table-column width="185" prop="operationPeople" label="操作人"></el-table-column>
        <el-table-column width="60" prop="amountIncurred" label="金额"></el-table-column>
        <el-table-column width="120" prop="operationType" label="操作类型"></el-table-column>
        <el-table-column prop="operationContent" label="操作内容"></el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="journalInfo.total"
              @on-change="next"
              :current="this.dao.current"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="journalInfoSizeOpts"
              @on-page-size-change="onJournalIn"
              :page-size="10"/>
      </div>
    </el-drawer>
    <el-table :data="list" class="table" v-loading="loading"
              :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
      <el-table-column
          prop="orderNo"
          width="150"
          label="订单号">
      </el-table-column>
      <el-table-column
          width="70"
          prop="serviceProject"
          label="服务产品">
      </el-table-column>
      <el-table-column
          width="90"
          prop="serviceData"
          label="日期"
      ></el-table-column>
      <el-table-column
          width="80"
          prop="serviceTimeStart"
          label="开始时间"
      ></el-table-column>
      <el-table-column
          width="80"
          prop="serviceTimeEnd"
          label="结束时间">
      </el-table-column>
      <el-table-column
          width="122"
          prop="state"
          label="状态">
        <template slot-scope="scope">
          <el-tag :type="scope.row.state === '1' ? 'primary' : 'success'" disable-transitions>
            <span v-if=" scope.row.state==='1'">
                            订单完成
                        </span>
            <span v-if=" scope.row.state==='2'">
                            客户退款
                        </span>
            <span v-if=" scope.row.state==='3'">
                            员工退款未到账
                        </span>
            <span v-if=" scope.row.state==='4'">
                            员工退款已到账
                        </span>
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
          width="80"
          prop="evaluateGrade"
          label="评分">
        <template slot-scope="scope">
          <el-tag effect="dark" :type="scope.row.evaluateGrade===0 ? 'danger' : ''"
                  disable-transitions>
             <span v-if=" scope.row.evaluateGrade == null">
                            未评价
                        </span>
                        <span v-if=" scope.row.evaluateGrade===0">
                            不满意
                        </span>
            <span v-if=" scope.row.evaluateGrade===1">
                            一般
                        </span>
            <span v-if=" scope.row.evaluateGrade===2">
                            满意
                        </span>
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="abnormal"
          label="异常"
          width="75">
        <template slot-scope="scope">
          <el-tag :type="scope.row.abnormal === 1 ? 'danger' : ''"
                  disable-transitions>
            <span v-if=" scope.row.abnormal===0">
                            无异常
                        </span>
            <span v-if=" scope.row.abnormal===1">
                            异常
                        </span>
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column

          prop="exceptionContent"
          label="异常内容">
      </el-table-column>
      <el-table-column
          width="280"
          fixed="right"
          label="操作">
        <template slot-scope="scope">
          <div style="display: flex">
            <el-button
                size="small "
                icon="el-icon-thumb"
                @click="edit(scope.row.orderNo)"
                type="text">查看
            </el-button>
            <el-button
                v-if="scope.row.state!=='4'&&scope.row.evaluateGrade===0&&scope.row.state!=='3'&&scope.row.abnormal===0"
                size="small "
                type="text"
                @click="cancelOrder(scope.row.orderNo,scope.row.state)"
                icon="el-icon-delete">
              取消订单
            </el-button>
            <el-button
                v-if="scope.row.evaluateGrade!==0&&scope.row.abnormal===0&&scope.row.state==='1'"
                size="small "
                type="text"
                @click="mandatoryRefund(scope.row.orderNo,scope.row.state)"
                icon="el-icon-delete">
              强制退款
            </el-button>
            <el-button
                size="small "
                type="text"
                @click="onJournal(scope.row.orderNo)"
                icon="el-icon-tickets">
              日志
            </el-button>
            <el-button
                size="small "
                type="text"
                @click="QRcode(scope.row.orderNo)"
                icon="el-icon-s-grid">
              二维码
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :visible.sync="centerDialogVisible" width="35%" center>
      <p><span style="color: #FF0000">客户退款：</span> 当客户评价不满意且账户无异常状态时可退款，退款金额不包括优惠券金额。金额退到客户本身的账号余额上。点击后立刻退款。</p>
      <p><span style="color: #FF0000">员工退款：</span>公司补贴员工的服务费。金额退到员工账号余额上，点击后预计3小时内生效</p>
      <p><span style="color: #FF0000">全部退款：</span>包括客户退款跟员工退款</p>
      <p style="color: #A4A4A4">注：订单异常不可退款</p>
      <span slot="footer">
          <el-button v-if="state==='1'" @click="customerRefund(operationId)">客户退款</el-button>
          <el-button v-if="state==='2'" type="success" @click="employeeRefund(operationId)">员工退款</el-button>
          <el-button v-if="state==='1'" type="primary" @click="wholeRefund(operationId)">全部退款</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="refund" width="35%" center>
      <p><span style="color: #FF0000">强制退款：</span>当客户评价完毕，才可以执行。目前只能退客户余额。</p>
      <p style="color: #A4A4A4">注：订单异常不可退款</p>
      <p style="color: #A4A4A4">注：订单不满意只能走取消订单退款</p>
      <span slot="footer">
          <el-button v-if="state==='1'" type="primary" @click="forceCustomerRefund(operationId)">强制客户退款</el-button>
      </span>
    </el-dialog>
    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChange"
            :current="this.dto.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="10"/>
    </div>
    <el-drawer :visible.sync="drawer" size="90%">
      <display :order="order"></display>
    </el-drawer>
    <el-dialog title="客户评价链接" :visible.sync="dialogVisible" width="300px">
      <div id="qrcode" style="text-align:center;"></div>
      <br/>
      <div style="text-align:center;">
        <el-button class="tag-read" type="primary" :data-clipboard-text="url" @click="copyUrl">复制链接</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>
//引入组件
import display from "./display";
import QRCode from 'qrcodejs2';
import Clipboard from 'clipboard';

export default {
  name: "serviceReceiptOrderNo",
  // 注册组件
  components: {
    display,
  },
  data() {
    return {
      orderNo: null,
      url: null,
      dialogVisible: false,
      qrCode: false,
      imrUrl: "",
      operationId: localStorage.getItem("id"),
      //客户退款，员工退服务费
      centerDialogVisible: false,
      refund: false,
      order: null,
      state: null,
      list: null,
      loading: true,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      drawer: false,
      dto: {
        evaluateGrade: null,
        orderNo: null,
        abnormal: '',
        serviceProject: null,
        days: [],
        endTime: null,
        state: null,
        exceptionContent: null,
        size: 10,
        current: 1,
      },
      journalInfoLoading: true,
      journalList: null,
      journalInfoSizeOpts: [10, 20, 30],
      journalInfo: {total: 10, size: 10, current: 1, pages: 1},
      journalInfoDrawer: false,
      dao: {
        orderNo: null,
        size: 10,
        current: 1,
      },
    }
  },
  created() {
    this.getData();
    this.getserviceAcceptancFormLogDESC();
  },
  methods: {
    isQRcode() {
      this.qrCode = true
    },
    //查看订单
    edit(orderNo) {
      this.drawer = true
      this.order = orderNo
    },
    //强制退款
    mandatoryRefund(orderNo, state) {
      this.refund = true
      this.order = orderNo
      this.state = state
    },
    //取消订单
    cancelOrder(orderNo, state) {
      this.centerDialogVisible = true
      this.order = orderNo
      this.state = state
    },
    //全部退款
    wholeRefund(operationId) {
      this.centerDialogVisible = false
      this.$getData("wholeRefund", {orderNo: this.order, operationId: operationId}).then(res => {
        if (res.status === 200) {
          if (res.data.msg === "订单取消成功") {
            this.$alert(res.data.msg + "，客户已退款" + "<br/>" + "员工补贴费用将在3小时内发回", {
              dangerouslyUseHTMLString: true
            });
          } else {
            this.$alert("全部退款失败," + res.data.msg, {
              dangerouslyUseHTMLString: true
            });
          }
          this.getData();
        }
      })
    },
    //客户退款
    customerRefund(operationId) {
      this.centerDialogVisible = false
      this.$getData("selectServiceCancelOrder", {orderNo: this.order, operationId: operationId}).then(res => {
        if (res.status === 200) {
          if ("订单取消成功" === res.data) {
            this.$alert("客户退款成功." + res.data, {
              dangerouslyUseHTMLString: true
            });
            this.getData();
          } else {
            this.$alert("客户退款失败," + res.data, {
              dangerouslyUseHTMLString: true
            });
            this.getData();
          }
        }
      })
    },
    //复制链接
    copyUrl() {
      var clipboard = new Clipboard('.tag-read')
      clipboard.on('success', e => {
        this.$message({
          message: '复制成功',
          type: 'success'
        });
        // 释放内存
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        this.$message.error('该浏览器不支持自动复制');
        // 不支持复制
        // 释放内存
        clipboard.destroy()
      })

    },
    forceCustomerRefund(operationId) {
      this.refund = false
      this.$getData("forceCustomerRefund", {orderNo: this.order, operationId: operationId}).then(res => {
        if (res.status === 200) {
          if ("订单取消成功" === res.data) {
            this.$alert("客户退款成功." + res.data, {
              dangerouslyUseHTMLString: true
            });
            this.getData();
          } else {
            this.$alert("客户退款失败," + res.data, {
              dangerouslyUseHTMLString: true
            });
            this.getData();
          }
        }
      })
    },
    //员工退款
    employeeRefund(operationId) {
      this.centerDialogVisible = false
      this.$getData("staffRefund", {orderNo: this.order, operationId: operationId}).then(res => {
        if (res.status === 200) {
          this.$alert(res.data.msg + "<br/>" + "员工补贴费用将在3小时内发回", {
            dangerouslyUseHTMLString: true
          });
          this.getData();
        }
      })
    },
    //日志
    getserviceAcceptancFormLogDESC() {
      this.journalInfoLoading = false;
      this.$postData("getserviceAcceptancFormLogDESC", this.dao).then(res => {
        if (res.status === 200) {
          this.journalList = res.data.records;
          this.journalInfo.current = res.data.current;
          this.journalInfo.size = res.data.size;
          this.journalInfo.total = res.data.total
        }
      })
    },
    getData() {
      this.dto.startTime = null;
      this.dto.endTime = null;
      if (this.dto.days != null && this.dto.days.length > 0) {
        this.dto.startTime = this.dto.days[0];
        this.dto.endTime = this.dto.days[1];
      }
      this.$postData("selectServiceReceipt", this.dto).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    query() {
      this.loading = true;
      this.dto.current = 1;
      this.getData();
    },
    reset() {
      this.dto.orderNo = null;
      this.dto.serviceProject = null;
      this.dto.days = [];
      this.getData();
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dto.size = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dto.current = index;
      this.getData();
    },
    next(index) {
      this.journalInfoLoading = true;
      this.dao.current = index;
      this.getserviceAcceptancFormLogDESC();
    },
    onJournalIn(size) {
      this.journalInfoLoading = true;
      this.dao.size = size;
      this.getserviceAcceptancFormLogDESC();
    },
    //个人日志
    onJournal(orderNo) {
      this.journalInfoDrawer = true
      this.dao.orderNo = orderNo;
      this.getserviceAcceptancFormLogDESC();
    },
    //二维码
    QRcode(orderNo) {
      if (null !== orderNo) {
      this.$getData("getByOrderNo", {orderNo: orderNo}).then(res => {
        if (res.status === 200) {
          if(res.data!==null){
            this.setOrderNoToken(orderNo)
          }else {
            this.$message({
              message: '订单号输入错误',
              type: 'warning'
            });
          }
        }
      })
      }else {
        this.$message({
          message: '请输入订单号',
          type: 'warning'
        });
      }
    },
    setOrderNoToken(orderNo) {
      this.$getData("setOrderNoToken", {orderNo: orderNo}, {}).then(res => {
        if (res.status === 200) {
          let token = res.data;
          if (token === null) {
            this.$message("该订单未完成");
          } else if (token === "") {
            this.$message("该订单是集团总订单");
          } else {
            this.dialogVisible = true;
            this.url = "http://task.xiaoyujia.com/serviceTicket/serviceTicket?token=" + token//获取当前地址栏的地址
            this.$nextTick(function () {
              document.getElementById("qrcode").innerHTML = "";
              let qrcode = new QRCode("qrcode", {
                text: this.url,
              });
            });
          }
        } else {
          this.$message(res.msg);
        }
      })
    },
    //异常检测
    anomalyDetection() {
      this.$getData("selectByAbnormal", {operationId: this.operationId}).then(res => {
        if (res.status === 200) {
          if (res.data) {
            this.getData();
            this.$message({
              message: '异常检测成功，已修改',
              type: 'success'
            });
          } else {
            this.$message('异常检测成功，没有订单需要修改');
          }
        }
      })
    },
  },
}
</script>

<style scoped>
.handle-box {
  /*margin-bottom: 10px;*/
  font-weight: bold !important;
}

table {
  border-collapse: collapse; /*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
  border-left: #C8B9AE solid 1px;
  border-top: #C8B9AE solid 1px;
}

table td {
  border-right: #C8B9AE solid 1px;
  border-bottom: #C8B9AE solid 1px;
}

th {
  background: #eee;
  font-weight: normal;
}

tr {
  background: #fff;
}

tr:hover {
  background: #cc0;
}
</style>
