<template>
  <div class="container">
    <div class="handle-box">
      <el-form ref="form" :model="pageInfo">
        <!--        <el-row>-->
        <!--          <el-col :span="4">-->
        <!--            <el-form-item label="活动名">-->
        <!--              <el-select v-model="valueSelect" placeholder="请选择" @change="getData()">-->
        <!--                <el-option-->
        <!--                    v-for="item in list"-->
        <!--                    :key="item.Id"-->
        <!--                    :label="item.ActivityName"-->
        <!--                    :value="item.Id">-->
        <!--                </el-option>-->
        <!--              </el-select>-->
        <!--            </el-form-item>-->
        <!--          </el-col>-->
        <!--        </el-row>-->
        <el-row>
          <el-col :span="7">
            <el-form-item label="最近时间">
              <el-button type="primary" @click="createInfo('today')">今天</el-button>
              <el-button type="primary" @click="createInfo('7day')">7日</el-button>
              <el-button type="primary" @click="createInfo('30day')">30日</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-date-picker
                v-model="date1"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="['00:00:00', '23:59:59']"
                @change="getData">
            </el-date-picker>


          </el-col>

        </el-row>

      </el-form>
    </div>
    <el-table :data="list" height="500px" v-loading="loading" border width="1000px"
              :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
      <el-table-column
          prop="ActivityName"
          width="250"
          label="活动名称">
      </el-table-column>
      <el-table-column
          prop="ActivityType"
          width="150"
          label="活动类型">
        <template slot-scope="scope">
          <el-tag  v-if="scope.row.ActivityType == 'zl'">
            助力
          </el-tag>
          <el-tag  v-if="scope.row.ActivityType == 'bobing'">
            博饼
          </el-tag>
        </template>

      </el-table-column>
      <el-table-column
          width="200"
          prop="shareCount"
          label="参与人数">
        <template slot-scope="scope">
          <div v-if="scope.row.shareCount == null">0</div>
          <div v-if="scope.row.shareCount != null">{{scope.row.shareCount}}</div>
        </template>
      </el-table-column>
      <el-table-column
          width="200"
          prop="accaptCount"
          label="裂变人数">
        <template slot-scope="scope">
          <div v-if="scope.row.accaptCount == null">0</div>
          <div v-if="scope.row.accaptCount != null">{{scope.row.accaptCount}}</div>
        </template>
      </el-table-column>
      <el-table-column
          width="200"
          prop="winCount"
          label="中奖人数">
        <template slot-scope="scope">
          <div v-if="scope.row.winCount == null">0</div>
          <div v-if="scope.row.winCount != null">{{scope.row.winCount}}</div>
        </template>
      </el-table-column>
      <el-table-column
          width="300"
          label="员工参与详情">
        <template slot-scope="scope">
          <el-button  @click="employeeInfo(scope.row.Id)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChange"
            :current="this.dto.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="10"/>
    </div>



    <el-dialog :visible.sync="editModal" width="30%" title="员工参与详情" :mask-closable="false">
      <el-table :data="editModalList" height="500px" v-loading="loading" border
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
        <el-table-column
            prop="shareCount"
            width="150"
            label="员工">
          <template slot-scope="scope">
            <div>{{scope.row.realName}}({{scope.row.no}})</div>
          </template>

        </el-table-column>
        <el-table-column
            prop="shareCount"
            width="150"
            label="拉取人数">
        </el-table-column>
      </el-table>
    </el-dialog>



  </div>

</template>

<script>
import Vue from 'vue';

export default {
  name: "excellentEmployee",

  data() {
    return {
      valueSelect:null,
      editModal:false,
      date1: [],
      editModalList:[],
      list: null,
      loading: true,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      quer: {
        no: null,
        name: null,
      },
      dto: {
        startTime:null,
        endTime:null,
        id:null,
        size: 10,
        current: 1,
      },
      rowData: null,
      journalInfoLoading: true,
      journalList: null,
      journalInfoSizeOpts: [10, 20, 30],
      journalInfo: {total: 10, size: 10, current: 1, pages: 1},
      journalInfoDrawer: false,

    }
  },
  created() {
    this.date1[0] = new Date(new Date().toLocaleDateString());
    this.date1[1] = new Date();

    this.getData();
  },
  methods: {

    getData() {
      this.dto.startTime = this.format(this.date1[0],"yyyy-MM-dd hh:mm:ss");
      this.dto.endTime = this.format(this.date1[1],"yyyy-MM-dd hh:mm:ss");
      console.info(JSON.stringify(this.dto))
      this.$postData("yingxiaoCenus", this.dto).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    query() {
      this.loading = true;
      this.dto.current = 1;
      this.getData();
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dto.size = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dto.current = index;
      this.getData();
    },
    createInfo(val) {
      const start = new Date(new Date().toLocaleDateString());
      if (val == 'today'){
        this.date1 =[new Date(this.format(new Date(start.getTime() - 3600 * 1000 * 24), "yyyy-MM-dd hh:mm:ss")),new Date(this.date1[1])] ;
      } else if (val == '7day'){
        this.date1 =[new Date(this.format(new Date(start.getTime() - 3600 * 1000 * 24 * 7), "yyyy-MM-dd hh:mm:ss")),new Date(this.date1[1])] ;
      } else if (val == '30day'){
        this.date1 =[new Date(this.format(new Date(start.getTime() - 3600 * 1000 * 24 * 30), "yyyy-MM-dd hh:mm:ss")),new Date(this.date1[1])] ;
      }
      console.info(this.date1[0])
      this.getData();
    },
    format(DateTime, fmt) {
      var o = {
        "M+": DateTime.getMonth() + 1, //月份
        "d+": DateTime.getDate(), //日
        "h+": DateTime.getHours(), //小时
        "m+": DateTime.getMinutes(), //分
        "s+": DateTime.getSeconds(), //秒
        "q+": Math.floor((DateTime.getMonth() + 3) / 3), //季度
        "S": DateTime.getMilliseconds() //毫秒
      };
      if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (DateTime.getFullYear() + "").substr(4 - RegExp.$1.length));
      for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
      return fmt;
    },
    employeeInfo(val){
      let dom ={
        "activityId" :val,
        "startTime":this.format(this.date1[0],"yyyy-MM-dd hh:mm:ss"),
        "endTime":this.format(this.date1[1],"yyyy-MM-dd hh:mm:ss"),
      }
      this.$postData("yingxiaoEmoloyeeCenus", dom).then(res => {
        if (res.status === 200) {
          this.editModalList = res.data;
        }
      })
      this.editModal = true;
    }
  },
}
</script>

<style scoped>
.handle-box {
  /*margin-bottom: 10px;*/
  font-weight: bold !important;
}

table {
  border-collapse: collapse; /*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
  border-left: #C8B9AE solid 1px;
  border-top: #C8B9AE solid 1px;
}

table td {
  border-right: #C8B9AE solid 1px;
  border-bottom: #C8B9AE solid 1px;
}

th {
  background: #eee;
  font-weight: normal;
}

tr {
  background: #fff;
}

tr:hover {
  background: #cc0;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

>>> .el-upload--text {
  width: 200px;
  height: 150px;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
