<template>
  <div>
    <el-upload
        :action="imgUrl"
        list-type="picture-card"
        :on-preview="handlePictureCardPreview"
        :limit="1"
        :on-remove="onRemove"
        :on-success="onSuccess"
        @returnData>
      <i class="el-icon-plus"></i>
    </el-upload>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <div v-show="dialogVisible1">
     原图: <img  width="15%" :src="pic">
    </div>


  </div>
</template>

<script>

export default {
  name: "activityPicContent",
  data(){
    return{
      imgUrl:'https://biapi.xiaoyujia.com/qiyeCustomer/ploadPicturesu33',
      // imgUrl:'http://localhost:8063/qiyeCustomer/ploadPicturesu33',
      dialogImageUrl: '',
      dialogVisible: false,
      dialogVisible1: false,
      pic:'',
    }
  },
  methods:{
    //文件删除时执行的的钩子
    onRemove(file,fileList){
      this.pic = ''
    },
    //文件上传成功的钩子函数
    onSuccess(response,file,fileList){
      this.dialogVisible1 = false;
      this.pic=response.data
      console.log(response.data)
    },
    //on-preview 点击时展示图层
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;

    },
    createImg(value){
      this.pic = value;
      this.dialogVisible1 = true;
    }
  },
}
</script>

<style scoped>

</style>