<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">

                    <el-row>
                        <!--                        <el-col :span="8">-->
                        <!--                            <el-form-item label="订单服务">-->
                        <!--                                <el-input-->
                        <!--                                        v-model="model.ProductName"-->
                        <!--                                        placeholder="订单服务"-->
                        <!--                                        style="width:190px"-->
                        <!--                                        class="handle-input mr10"-->
                        <!--                                ></el-input>-->
                        <!--                            </el-form-item>-->
                        <!--                        </el-col>-->
                      <el-col :span="8">
                        <el-form-item label="线索号">
                          <el-input
                              oninput="this.value = this.value.replace(/[^0-9]/g, '');"
                              v-model="model.id"
                              placeholder="线索号"
                              style="width:200px"
                              class="handle-input mr10"
                          ></el-input>
                        </el-form-item>
                      </el-col>
                        <el-col :span="8">
                            <el-form-item label="经纪人">
                                <el-input
                                        v-model="model.realName"
                                        placeholder="经纪人"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="开单人">
                                <el-input
                                        v-model="model.channel"
                                        placeholder="开单人"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="手机号">
                                <el-input
                                        v-model="model.phone"
                                        placeholder="手机号"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <!--<el-col :span="8">-->
                        <!--    <el-form-item prop="isPayoff" label="需求状态">-->

                        <!--        <el-select v-model="model.status" clearable placeholder="请选择">-->
                        <!--            <el-option-->
                        <!--                    v-for="item in isPayoffoptions"-->
                        <!--                    :key="item.value"-->
                        <!--                    :label="item.label"-->
                        <!--                    :value="item.value">-->
                        <!--            </el-option>-->
                        <!--        </el-select>-->
                        <!--    </el-form-item>-->

                        <!--</el-col>-->

                        <el-col :span="8">
                            <el-form-item prop="time" label="时间段">

                                <el-date-picker
                                        style="width:250px"
                                        :default-value="new Date()"
                                        :unlink-panels="true"
                                        v-model="model.time"
                                        type="datetimerange"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        :default-time="['00:00:00']">
                                </el-date-picker>
                            </el-form-item>

                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="订单状态">
                                <el-radio-group v-model="model.split" @change="model.current=1,query()">
                                    <el-radio-button :label="null" border>全部</el-radio-button>
                                    <el-radio-button :label="0" border>未拆单</el-radio-button>
                                    <el-radio-button :label="1" border>已拆单</el-radio-button>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item label="归属门店">
                                <Select filterable style="width: 190px" v-model="model.storeId" placeholder="归属门店">
                                    <Option value="">请选择门店</Option>
                                    <Option value="1">平台</Option>
                                    <Option value="-1">小羽佳.佳管家(人才店)</Option>
                                    <Option v-for="(item,index) in storeList" :value="item.id" :key="index">{{
                                        item.storeName}}
                                    </Option>
                                </Select>
                            </el-form-item>
                        </el-col>

                      <el-col :span="8">
                      <el-form-item label="线索类型">
                        <el-select v-model="model.needsType" placeholder="请选择线索类型" clearable>
                          <el-option
                              v-for="item in needsOptions"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value">
                          </el-option>
                        </el-select>
                      </el-form-item>
                      </el-col>

                      <el-col :span="8">
                        <el-form-item label="订单编号">
                          <el-input
                              v-model="model.billNo"
                              placeholder="订单编号"
                              style="width:200px"
                              class="handle-input mr10"
                          ></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="24">
                        <el-form-item label="需求分类">
                          <el-checkbox-group v-model="model.needStatusList">
                            <el-checkbox :label="item.id" :name="String(item.id)" border
                                         v-for="(item,index) in dic" :key="index">{{item.text}}
                            </el-checkbox>
                          </el-checkbox-group>
                        </el-form-item>
                      </el-col>
                      <el-col :span="10">
                        <el-form-item label="需求状态">
                          <el-checkbox-group v-model="model.statusList">
                            <el-checkbox label="0" name="0" border>已取消</el-checkbox>
                            <el-checkbox label="1" name="1" border>未处理</el-checkbox>
                            <el-checkbox label="2" name="2" border>已开单</el-checkbox>
                            <el-checkbox label="3" name="3" border>已读</el-checkbox>
                            <el-checkbox label="4" name="4" border>待支付</el-checkbox>
                          </el-checkbox-group>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item label="需求性质">
                          <el-radio-group v-model="model.isKf" @change="model.current=1,query()">
                            <el-radio-button :label="null" border>全部</el-radio-button>
                            <el-radio-button :label="0" border>系统单</el-radio-button>
                            <el-radio-button :label="1" border>开发单</el-radio-button>
                          </el-radio-group>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="来源渠道">
                          <el-input
                              clearable
                              v-model="model.tg"
                              placeholder="来源渠道"
                              style="width:200px"
                              class="handle-input mr10"
                          ></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="服务区域">
                          <el-cascader  :options="serviceOptions" v-model="serviceArray" filterable @change="changeCityArea"></el-cascader>
                        </el-form-item>
                      </el-col>

                      <el-col :span="8">
                        <el-form-item label="线索来源渠道">
                          <el-select   filterable v-model="model.needsChannel" placeholder="请选择线索来源渠道" clearable>
                            <el-option
                                v-for="(item,index) in needsChannelList" :key="index" :label="item.text" :value="item.value">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>

                      <el-col :span="8">
                        <el-form-item label="线索标签">
                          <el-select   filterable v-model="model.validOrNot" placeholder="请选择线索标签" clearable>
                            <el-option
                                v-for="(item,index) in needsTagList" :key="index" :label="item.text" :value="item.value">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>


                      <el-col :span="8">
                        <el-form-item label="所属门店类型">
                          <el-select v-model="model.storeType" placeholder="请选择所属门店类型" clearable>
                            <el-option
                                v-for="item in storeTypeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>


                        <el-col :span="8">

                            <el-form-item>
                                <el-button type="success" round @click="query()">搜索</el-button>
                                <el-button type="info" round @click="re()">重置</el-button>
                                <el-button type="primary" round @click="toOrderNeedData()">分析</el-button>
                                <el-button type="default" round @click="exportExcel()">导出</el-button>

                                <el-button type="success" round v-if="showAgentStore" @click="toAgentStore()">配置自动分发
                                </el-button>
                            </el-form-item>

                        </el-col>
                    </el-row>

                </el-form>
            </div>
            <el-table :data="list" :header-cell-style="{background:'#ddd'}" @sort-change="sortChange" border
                      class="table" ref="multipleTable" @cell-dblclick="edit" v-loading="loading">

                <el-table-column
                        type="selection"
                        width="35">
                </el-table-column>
                <el-table-column
                        fixed="left"
                        prop="id"
                        label="需求编号"
                        width="80"
                >
                    <template slot-scope="scope">
                        <el-link type="primary" @click="toNeedInfo(scope.row.id)">{{scope.row.id}}</el-link>
                    </template>
                </el-table-column>
                <el-table-column
                        fixed="left"
                        prop="name"
                        label="客户名称"
                        width="80"
                ></el-table-column>
                <el-table-column
                        fixed="left"
                        prop="realName"
                        label="经纪人"
                        width="170"
                >
                    <template slot-scope="scope">
                        {{scope.row.realName}}
                        （ {{scope.row.agentNo}} ）
                    </template>
                </el-table-column>
                <el-table-column
                        prop="needStatusName"
                        label="需求分类"
                        width="150"
                ></el-table-column>
                <el-table-column
                        prop="status"
                        label="需求状态"
                        width="80"
                >
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.status=='0'" type="danger">已取消</el-tag>
                        <el-tag v-if="scope.row.status=='1'" type="info">未处理</el-tag>
                        <el-tag v-else-if="scope.row.status=='2'" type="warning">已开单</el-tag>
                        <el-tag v-else-if="scope.row.status=='3'" type="success">已读</el-tag>
                        <el-tag v-else-if="scope.row.status=='4'" type="info">待支付</el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="productName"
                        label="产品名称"
                        width="120"
                        sortable="custom"
                ></el-table-column>
                <el-table-column
                        prop="phone"
                        label="客户号码"
                        width="120"
                        sortable="custom"
                >
                    <template slot-scope="scope">
                        <el-link type="primary" @click="toMember(scope.row.phone)">{{scope.row.phone}}</el-link>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="channel"
                        label="开单工号"
                        width="120"
                >
                    <template slot-scope="scope">
                        {{scope.row.channel}}
                        <el-tag v-if="scope.row.channel==scope.row.agentNo" type="success">开发单</el-tag>
                        <el-tag v-if="scope.row.channel!==scope.row.agentNo" type="info">系统单</el-tag>

                    </template>
                </el-table-column>


                <el-table-column
                        prop="billNo"
                        label="订单编号"
                        width="180"
                ></el-table-column>
              <el-table-column
                  prop="storeName"
                  label="所属门店"
                  width="180"
              ></el-table-column>
              <el-table-column
                  prop="storeType"
                  label="门店类型"
                  width="180"
              > <template slot-scope="scope">
                <el-tag v-if="scope.row.storeType==0" type="info">平台</el-tag>
                <el-tag v-if="scope.row.storeType==1" type="success">直营店</el-tag>
                <el-tag v-if="scope.row.storeType==2" type="primary">共创店</el-tag>
                <el-tag v-if="scope.row.storeType==3" type="warning">定制店</el-tag>
              </template></el-table-column>

                <el-table-column
                        prop="split"
                        label="拆单状态"
                        width="160"
                >
                    <template slot-scope="scope">
                        （<span style="color: red">￥{{scope.row.realTotalAmount}}</span>）
                        <el-tag v-if="scope.row.split == null || scope.row.split == 0" type="info">未拆单</el-tag>
                        <el-tag v-if="scope.row.split> 0" type="success">已拆单</el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="startTime"
                        label="服务时间"
                        width="180"
                ></el-table-column>
                <el-table-column
                        prop="tg"
                        label="来源"
                        width="80"
                ></el-table-column>
              <el-table-column
                        prop="needsChannelName"
                        label="线索来源渠道"
                        width="120"
                ></el-table-column>
              <el-table-column
                  prop="validOrNot"
                  label="线索标签"
                  width="120"
              >
                <template slot-scope="scope">
                  <el-tag v-if="!scope.row.validOrNot || scope.row.split == 0" type="info">未标识</el-tag>
                  <el-tag v-if="scope.row.validOrNot == 1" type="success">有效线索</el-tag>
                  <el-tag v-if="scope.row.validOrNot == 2" type="error">无效线索</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                  prop="followUpRemark"
                  label="标签备注"
                  width="200"
              ></el-table-column>
                <el-table-column
                        prop="cityName"
                        label="城市"
                        width="80"
                ></el-table-column>
                <el-table-column
                        prop="street"
                        label="街道"
                        width="180"
                        show-overflow-tooltip
                ></el-table-column>

                <el-table-column
                        sortable="custom"
                        prop="remark"
                        label="需求内容"
                        width="180"
                        show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                        v-if="roleId!==42"
                        prop="agentRemark"
                        label="备注"
                        width="180"
                        show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                        label="操作"
                        fixed="right"
                        min-width="480">
                    <template slot-scope="scope">
                        <el-button size="mini" type="primary" @click="toNeedInfo(scope.row.id)">操作</el-button>
                        <el-button size="mini" @click="changeDom=scope.row,remarkDialog=true" type="info"
                                   v-if="roleId!==42">备注
                        </el-button>
                        <el-button size="mini" @click="logModel.current=1,changeDom=scope.row,getLos()" type="info">
                            操作日志
                        </el-button>
                        <el-button size="mini" @click="logModel.current=1,changeDom=scope.row,getLos1()" type="info">
                            流程日志
                        </el-button>
                        <el-button size="mini" @click="dialogVisible=true,changeDom=scope.row" type="primary">需求分发
                        </el-button>
                        <el-button size="mini" @click="todetail(scope.row.billNo)" type="primary"
                                   v-show="scope.row.billNo!==null">订单
                        </el-button>
                        <el-button size="mini" @click="scope.row.status =0,save(scope.row)" type="danger"
                                   v-show="scope.row.status!= 0">取消需求
                        </el-button>
                        <el-button size="mini" @click="scope.row.status =3,save(scope.row)" type="success"
                                   v-show="scope.row.status==1">已读
                        </el-button>
                      <el-button size="mini"  type="primary" @click="showNeedsAlog(scope.row.id)">派单详情</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>


        <el-dialog
            title="备注信息："
            :visible.sync="remarkDialog"
            width="30%">

			<h2>薪资预算：</h2><br>
			<el-input
					v-model.number="changeDom.salary"
			        placeholder="备注客户的薪资预算（月薪）"
					type="textarea"
					onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
			></el-input>
			<br>

			<h2>工作内容：</h2><br>
			<el-input
			        v-model="changeDom.workContent"
			        placeholder="备注员工的工作内容"
			        type="textarea"
			></el-input>
			<br>

			<h2>工作要求：</h2><br>
			<el-input
			        v-model="changeDom.workRequire"
			        placeholder="备注客户对员工的工作要求"
			        type="textarea"
			></el-input>
			<br>

			<h2>工作地点：</h2><br>
			<el-input
			        v-model="changeDom.workSite"
			        placeholder="备注员工的大致工作地点（线索对外展示时使用）"
			        type="textarea"
			></el-input>
			<br>

			<h2>需求备注：</h2><br>
            <el-input
                    v-model="changeDom.agentRemark"
                    placeholder="备注其他需求信息"
                    type="textarea"
            ></el-input>
            <el-divider></el-divider>
            <center>
                <el-button size="mini" @click="save(changeDom)" type="success">更改备注信息</el-button>
            </center>

        </el-dialog>

        <el-dialog
                title="请选择转发给哪个经纪人："
                :visible.sync="dialogVisible"
                class="change_agent_dialog"
                width="30%">
            <div>
                <h3 style="color: #f02d2d;">当前线索信息: </h3>
                客户号码: {{changeDom.phone}}
                <br/>
                当前经纪人: {{changeDom.realName}}
                [ {{changeDom.agentNo}} ]
                <br/>
                开单工号: {{changeDom.channel}}
                <el-tag v-if="changeDom.channel==changeDom.agentNo" type="success">开发单</el-tag>
                <el-tag v-if="changeDom.channel!==changeDom.agentNo" type="info">系统单</el-tag>
            </div>
            <br/>
            <el-tabs v-model="activeName" type="border-card">
                <el-tab-pane label="本门店" name="first">
                    <div v-for="item in agentList" class="agent-box" v-if="item.storeId==storeId">
                        {{item.no}}--{{item.realName}}
                        <el-button size="mini" type="primary"
                                   @click="changeDom.agentId=item.id,save(changeDom),setMessage(item.no,changeDom.id)">需求分发
                        </el-button>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="全部" name="second">
                    <el-input v-model="realName" placeholder="请输入名称或者工号" @input="querySearch"
                              suffix-icon="el-icon-search"></el-input>
                    <div v-for="item in options" class="agent-box">
                        {{item.no}}--{{item.realName}}
                        <el-button size="mini" type="primary"
                                   @click="changeDom.agentId=item.id,save(changeDom),setMessage(item.no,changeDom.id)">需求分发
                        </el-button>
                    </div>
                </el-tab-pane>

            </el-tabs>

        </el-dialog>
        <el-dialog
                title="操作日志："
                :visible.sync="dialog"
                width="80%">
            <el-table :data="logList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable"
                      v-loading="loading2">

                <el-table-column
                        fixed="left"
                        prop="operator"
                        label="操作人"
                        width="80"
                ></el-table-column>
                <el-table-column
                        prop="title"
                        label="类型"
                        width="80"
                ></el-table-column>
                <el-table-column
                        prop="type"
                        label="操作途径"
                        width="80"
                >
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.type=='1'" type="danger">移动端</el-tag>
                        <el-tag v-if="scope.row.type=='0'" type="success">web端</el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="createTime"
                        label="操作时间"
                        width="180"
                ></el-table-column>
                <el-table-column
                        prop="message"
                        label="操作内容"
                ></el-table-column>
            </el-table>


            <div class="pagination">
                <Page :total="pageInfo2.total"
                      @on-change="onChange2"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange2"
                      :page-size="pageInfo2.size"/>
            </div>
        </el-dialog>

        <el-dialog
                title="流程日志："
                :visible.sync="dialog1"
                width="80%">
            <el-table :data="logList1" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable"
                      v-loading="loading3">

                <el-table-column
                        fixed="left"
                        prop="realName"
                        label="操作人"
                        width="80"
                ></el-table-column>
                <el-table-column
                        prop="type"
                        label="类型"
                        width="80"
                ></el-table-column>
                <el-table-column
                        prop="creatTime"
                        label="操作时间"
                        width="180"
                ></el-table-column>
                <el-table-column
                        prop="processingRemarks"
                        label="操作内容"
                ></el-table-column>
            </el-table>


            <div class="pagination">
                <Page :total="pageInfo2.total"
                      @on-change="onChange2"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange2"
                      :page-size="pageInfo2.size"/>
            </div>
        </el-dialog>

      <el-dialog
          title="算法详情："
          :visible.sync="showOrderNeedsAlogDialg"
          width="80%">
        <el-table :data="orderNeedsAlgorithmLogList" :header-cell-style="{background:'#ddd'}" border class="table">

          <el-table-column
              fixed="left"
              prop="orderNeedsLatLng"
              label="线索经纬度"
          ></el-table-column>
          <el-table-column
              prop="storeName"
              label="门店名"
          ></el-table-column>
          <el-table-column
              prop="storeLatLng"
              label="门店经纬度"
          ></el-table-column>
          <el-table-column
              prop="distance"
              label="门店距离(千米)"
          >
          </el-table-column>
          <el-table-column
              prop="serviceScore"
              label="门店服务分"
          ></el-table-column>
          <el-table-column
              prop="waitOrderNeedsTime"
              label="等单时长"
          ></el-table-column>
          <el-table-column
              prop="distanceCountDex"
              label="距离测算结果"
          ></el-table-column>
          <el-table-column
              prop="serviceScoreCountDex"
              label="服务分测算结果"
          ></el-table-column>
          <el-table-column
              prop="waitOrderCountDex"
              label="等单测算结果"
          ></el-table-column>
          <el-table-column
              prop="resultDex"
              label="算法结果分数"
          ></el-table-column>
        </el-table>

      </el-dialog>

    </div>
</template>

<script>
    // import {formatDate} from '@/components/common/utils.js'
    export default {
        data() {
            return {
              showOrderNeedsAlogDialg: false,
              orderNeedsAlgorithmLogList:[],
              serviceOptions: [], //服务区域
              serviceArray: [],
              storeTypeOptions: [{
                value: 6,
                label: '承包店'
              },{
                value: 3,
                label: '共创店'
              }, {
                value: 2,
                label: '直营店'
              },{
                value: 1,
                label: '平台'
              }],
                realName: null,
                options: [],
                showAgentStore: false,
                activeName: 'first',
                roleId: Number(localStorage.getItem("roleId")),
                storeId: Number(localStorage.getItem("storeId")),
                logList: [],
                logList1: [],
                changeDom: {
                    agentRemark: null,
                    salary: null,
                    workContent: null,
                    workRequire: null,
                    workSite: null
                },
                agentList: [],
              needsChannelList: [],
              needsTagList: [
                {text: "未标识",value: 0},
                {text: "有效线索",value: 1},
                {text: "无效线索",value: 2},
              ],
                dialogVisible: false,
                remarkDialog: false,
                dialog: false,
                dialog1: false,
              needsOptions: [{
                value: 2,
                label: '常规线索'
              }, {
                value: 1,
                label: '毛线索'
              }],
                isPayoffoptions: [{
                    value: null,
                    label: '全部'
                }, {
                    value: 0,
                    label: '已取消'
                }, {
                    value: 1,
                    label: '未处理'
                }, {
                    value: 2,
                    label: '已开单'
                }, {
                    value: 3,
                    label: '已读'
                },{
                    value: 4,
                    label: '待支付'
                },],
                dic: [],
                loading: true,
                loading2: true,
                loading3: true,
                tableData: [],
                pageSizeOpts: [10, 20, 30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                pageInfo2: {total: 10, size: 10, current: 1, pages: 1},
                pageInfo3: {total: 10, size: 10, current: 1, pages: 1},
                screenWidth: '80%',//新增对话框 宽度
                saveModal: false,
                addModal: false,
                addConModal: false,
                show: {},
                storeList: [],
                model: {
                    id: null,
                    isKf: null,
                    channel: null,
                    needStatusList: [],
                    agentId: null,
                    myId: null,
                    billNo: null,
                    needsType: null,
                    storeId: Number(localStorage.getItem("storeId")),
                    time: null,
                    statusList: [],
                    phone: null,
                    status: null,
                    split: null,
                    ProductName: null,
                    realName: null,
                    current: 1,
                    size: 10,
                    orderBy: 'startTime DESC',
                    arearId:null
                },
                logModel: {
                    orderNeedsId: null,
                    current: 1,
                    size: 10,
                },
                logModel1: {
                    orderNeedId: null,
                    current: 1,
                    size: 10,
                },
                update: {
                    id: null,
                    realName: null,

                },
                showSeachStore: false,
                list: null
            };
        },

        created() {
            //this.noLogin();
            this.getAllNeedsChannel();
            this.getData();
            this.getAgent();
            this.getDic();
            this.getStore()
            if (localStorage.getItem("roleId") == '1') {
                this.showAgentStore = true
            }
            if (localStorage.getItem("roleId") == '1') {
                // ||localStorage.getItem("id")=="26992"||localStorage.getItem("id")=="16521"
                this.showSeachStore = true
            }
          // 服务区域下拉框
          this.$getData("getCityArea").then(res => {
            res.data.unshift({ label: "请选择", value: "" });
            this.serviceOptions = res.data;
          });

        },
        computed: {},
        methods: {
          getAllNeedsChannel(){
            this.$getData("getAllNeedsChannel", {}, {}).then(res => {
              if (res.status == 200) {
                this.needsChannelList = res.data
              } else {
                this.$message.error("查询线索来源渠道!");
              }
            })
          },
          showNeedsAlog(id) {
            this.$getData("orderNeedsAlogList", {orderNeedsId: id}, {}).then(res => {
              this.orderNeedsAlgorithmLogList = res.data;
              this.showOrderNeedsAlogDialg = true;
            })

          },
            querySearch(queryString) {
                // console.log(queryString)
                var restaurants = this.agentList;
                var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
                // 调用 callback 返回建议列表的数据
                // cb(results);
                this.options = results
            },
            createFilter(queryString) {
                return (restaurant) => {
                    return (restaurant.realName.toLowerCase().indexOf(queryString.toLowerCase()) === 0 || restaurant.no.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                };
            },
            // noLogin() {
            //     this.$postData(
            //         "noTest",
            //         {
            //             account: localStorage.getItem("account"),
            //             password: "000",
            //         },
            //         {}
            //     ).then(res => {
            //         if (res.meta.state == 200) {
            //
            //         } else {
            //             localStorage.clear()
            //             this.$router.push("/");
            //         }
            //     });
            // },
             changeCityArea(e) {
              this.model.arearId = e[1];
           },
            getStore() {
                this.$postData("store_getByList", {}, {}).then(res => {
                    if (res.status == 200) {
                        this.storeList = res.data;
                    }
                });
            },
            toAgentStore() {
                let routeData = this.$router.resolve({path: '/agentStore'});//path：跳转页面的相对路径，query：参数
                window.open(routeData.href, '_blank');
            },
            toOrderNeedData() {
                let routeData = this.$router.resolve({path: '/orderNeedData'});//path：跳转页面的相对路径，query：参数
                window.open(routeData.href, '_blank');
            },
            getDic() {
                this.$postUrl("get_dict", 160, null, {}).then(res => {
                    if (res.status == 200) {
                        this.dic = res.data
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            toNeedInfo(id) {

                let routeData = this.$router.resolve({path: '/needInfo', query: {"id": id}});//path：跳转页面的相对路径，query：参数
                window.open(routeData.href, '_blank');
            },
            toMember(phone) {
                this.$router.push({path: '/agentMemberByPhone', query: {"bindTel": phone}})
            },
            todetail(billNo) {
                let url = window.location.href;
                window.open(url.substring(0, url.length - 15) + 'orderInfo?billNo=' + billNo);
                //this.$router.push({path: 'orderInfo', query: {billNo: billNo}})
            },
            setMessage(agentNo,orderNeedsId) {
                $.ajax({
                    type: "post",//type可以为post也可以为get
                    url: " https://xpush.xiaoyujia.com/goPush/send",
                    contentType: "application/json",
                    data: JSON.stringify({
                        content: "您有一条来自大数据后端手动分发的线索,请进入家姐联盟》合伙人》线索进行接单; https://jiajie.xiaoyujia.com/pages-work/operations/clew/clewIndex?id="+orderNeedsId,
                        agentid: "0",
                        no: agentNo
                    }),//这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                    dataType: "json",//这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                    success: function (data) {
                    },
                    error: function (data) {
                        //alert("出现了错误！");
                    }
                })

            },
            getAgent() {
                let store = Number(localStorage.getItem("storeId"));
                this.$postData("agent_list", {storeId: null}, {}).then(res => {
                    if (res.status == 200) {
                        this.agentList = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            save(dom) {
				// 对薪资的空值进行处理
				if(dom.salary == null || dom.salary == 0) {
					dom.salary = 0.00
				}
                dom.kdId = localStorage.getItem("id");
                this.$postData("orderNeeds_save", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.$Message.success('修改成功');
                        this.dialogVisible = false;
                        this.dialog = false;
                        this.remarkDialog = false;
                        this.getData();
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })
            },
            sortChange: function (column, prop, order) {

                if (column == null) {
                    this.model.orderBy = "";
                } else {
                    if (column.order == "ascending") {
                        this.model.orderBy = column.prop + " ASC";
                    }
                    if (column.order == "descending") {
                        this.model.orderBy = column.prop + " DESC";
                    }
                }
                this.getData();
            },
            getData() {
                this.loading = true;

                this.model.myId = localStorage.getItem("id");
                this.$postData("orderNeeds_page", this.model, {}).then(res => {
                    if (res.status == 200) {
                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total;
                        this.loading = false;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })

            },
            getLos() {
                this.loading2 = true;
                this.logModel.orderNeedsId = this.changeDom.id;
                this.$postData("page_orderNeedsLog", this.logModel, {}).then(res => {
                    if (res.status == 200) {
                        this.logList = res.data.records;
                        this.pageInfo2.current = res.data.current;
                        this.pageInfo2.size = res.data.size;
                        this.pageInfo2.total = res.data.total;
                        this.dialog = true;
                        this.loading2 = false;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })

            },
            getLos1() {
                this.loading3 = true
                this.logModel1.orderNeedId = this.changeDom.id;
                this.$postData("orderNeedTrackPage", this.logModel1, {}).then(res => {
                    if (res.status == 200) {
                        this.logList1 = res.data.records;
                        this.pageInfo3.current = res.data.current;
                        this.pageInfo3.size = res.data.size;
                        this.pageInfo3.total = res.data.total;
                        this.dialog1 = true;
                        this.loading3 = false;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })

            },
            query() {
              console.log(JSON.stringify(this.serviceArray))
                if (this.model.time != null) {
                    this.model.startDateTime = this.model.time[0];
                    this.model.endDateTime = this.model.time[1];
                }
                if (this.model.time == null) {
                    this.model.startDateTime = null;
                    this.model.endDateTime = null;
                }

                this.getData();
            },
            re() {
                this.model.tg = null;
                this.model.channel = null;
                this.model.needsChannel = null;
                this.model.validOrNot = null;
                this.model.time = null;
                this.model.needStatusList = [];
                this.model.startDateTime = null;
                this.model.endDateTime = null;
                this.model.isPayoff = null;
                this.model.statusList = [];
                this.model.phone = null;
                this.model.ProductName = null;
                this.model.realName = null;
                this.serviceArray = [];
                this.model.arearId = null;
                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {

                this.model.size = size;
                this.getData();
            },
            onPageSizeChange2(size) {

                this.logModel.size = size;
                this.getLos()
            },
            // 跳转页码

            onChange(index) {

                this.model.current = index;
                this.getData();
            },
            onChange2(index) {
                this.logModel.current = index;
                this.getLos()
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
                this.updateModal = false;
                this.addModal = false;
                this.addConModal = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },
            edit(id) {
                this.show = id;

                this.saveModal = true;
            },
            editCon(id) {
                this.show = id;
                this.addConModal = true;
            },
            exportExcel() {
                this.$message.success('正在导出中,可能需要1~2分钟，请稍等...');
                this.loading = true;
              if (this.model.time != null) {
                this.model.startDateTime = this.model.time[0];
                this.model.endDateTime = this.model.time[1];
              }
              if (this.model.time == null) {
                this.model.startDateTime = null;
                this.model.endDateTime = null;
              }
                this.$postData("exportOrderNeeds", this.model, {responseType: "arraybuffer"}).then(res => {
                    this.blobExport({
                        tableName: "线索数据",
                        res: res
                    });
                });
            },
            blobExport({tableName, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tableName + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
                this.loading = false;
            },
        }
    };
</script>

<style scoped>
    .el-checkbox.is-bordered + .el-checkbox.is-bordered {
        margin-left: 0;
    }

    .el-checkbox {
        margin-right: 5px;
    }

    .agent-box {
        padding: 10px;
        border: 1px solid #ddd;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;

    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }

    >>> .change_agent_dialog .el-dialog__body {
        padding: 10px 20px 20px 20px;
    }
</style>
