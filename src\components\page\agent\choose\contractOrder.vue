<template>
    <div>
        <div class="myorder">
            <span>订单内容：{{order.productName}}</span>
            <span>订单号：{{order.billNo}}</span>
            <!--            <span>保姆名称：{{order.realName}}</span>-->
            <!--            <span>经纪人名称：{{order.agentName}}</span>-->
            <!--            <el-button type="info" v-show="order.id!=null" @click="dom.orderId=null,uporder()">取消绑定</el-button>-->
        </div>
        <div class="changeorder">
            <el-select
                    v-show="order.id==null"
                    v-model="value"
                    filterable
                    remote
                    reserve-keyword
                    @change="changeOrder"
                    placeholder="请输入订单编号"
                    :remote-method="remoteMethod"
                    :loading="loading">
                <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                </el-option>
            </el-select>
            <el-button type="primary" v-show="order.id==null" @click="dom.orderId=value,uporder()">确认绑定</el-button>
            <!--            <el-button type="primary" v-show="order.id!=null" @click="dom.orderId=value,uporder()">更换绑定</el-button>-->
            <el-button type="danger" v-show="order.id!=null" @click="removeOrderId()">解除绑定</el-button>
            <el-button type="primary" v-show="order.id!=null" @click="todetail(order.billNo)">查看订单</el-button>

        </div>
        <!--        {{this.dom.orderId}}-->
        <!--        {{order}}-->
    </div>
</template>

<script>
    export default {
        props: ['model'],
        name: "contractOrder",
        data() {
            return {
                order: {},
                dom: this.model,
                options: [],
                value: [],
                list: [],
                loading: false
            }
        },
        created() {
            // console.log(this.dom);
            if (this.dom.orderId != null) {
                this.getOrder(this.dom.orderId);
            }
        },
        methods: {
            todetail(billNo) {
                let url = window.location.href;
                window.open(url.substring(0, url.length - 13) + 'orderInfo?billNo=' + billNo);
                //this.$router.push({path: 'orderInfo', query: {billNo: billNo}})
            },
            removeOrderId() {
                this.$postData("removeOrderId", this.dom, {}).then(res => {
                    if (res.status == 200) {
                        this.dom.orderId = null;
                        this.order.id = null;
                        this.order.productName = null;
                        this.order.billNo = null;
                        this.$message.success("解除绑定成功，" + res.msg);
                    } else {
                        this.order.id = null;
                        this.order.productName = null;
                        this.order.billNo = null;
                        this.dom.orderId = null;
                        this.$message.error(res.msg);
                    }
                })
            },
            uporder() {
                // console.log(this.dom.orderId);
                if (this.dom.orderId == null || this.dom.orderId == '') {
                    return this.$message.success("请输入订单号，并在下拉选择中选中该订单，");
                } else {
                    this.$postData("up_contract", this.dom, {}).then(res => {
                        if (res.status == 200) {
                            this.$message.success("修改成功，" + res.msg);
                            if (this.dom.orderId != null) {
                                this.getOrder(this.dom.orderId)
                            } else {
                                this.order = {
                                    id: null,
                                    billNo: null,
                                    realName: null,
                                    agentName: null
                                }
                            }
                        } else {
                            this.order.id = null;
                            this.order.productName = null;
                            this.order.billNo = null;
                            this.dom.orderId = null;
                            this.$message.error(res.msg);
                        }
                    }).catch(e => {
                        this.$message.error("修改失败，" + e.message);
                    })
                }

            },
            changeOrder(model) {
                console.log(model)
            },
            remoteMethod(query) {
                if (query !== '' && query.length > 6) {
                    this.loading = true;
                    this.options = [];
                    this.$postData("order_list", {billNo: query}, {}).then(res => {
                        if (res.status == 200) {
                            res.data.forEach(v => {
                                this.options.push({value: v.id, label: v.billNo});
                                this.loading = false;
                            })
                        } else {
                            this.$message.error("修改失败，" + res.msg);
                        }
                    })

                } else {
                    this.options = [];
                }
            },
            getOrder(id) {
                this.$postUrl("get_order", id, {}).then(res => {
                    if (res.status == 200) {
                        this.order = res.data;
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })
            }
        }
    }
</script>

<style scoped>
    .changeorder {
        padding: 20px;
    }

    .myorder {
        width: 100%;
    }

    .myorder span {
        font-size: 18px;
        padding-right: 40px;
    }

</style>
