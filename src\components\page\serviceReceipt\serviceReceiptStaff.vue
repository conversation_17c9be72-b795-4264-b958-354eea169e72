<template>
  <div class="container">
    <div class="handle-box">
      <el-form ref="form" :model="pageInfo">
        <el-row>
          <el-col :span="4">
            <el-form-item label="员工工号">
              <el-input
                  clearable
                  v-model="dto.serviceNo"
                  placeholder="员工工号"
                  style="width:120px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="员工名字">
              <el-input
                  clearable
                  v-model="dto.serviceName"
                  placeholder="员工名字"
                  style="width:120px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="订单号">
              <el-input
                  clearable
                  v-model="dto.orderNo"
                  placeholder="订单号"
                  style="width:180px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="服务产品">
              <el-input
                  clearable
                  v-model="dto.serviceProject"
                  placeholder="服务产品"
                  style="width:180px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
        <el-col :span="8" class="block">
          <el-form-item label="订单日期">
            <el-date-picker
                v-model="dto.days"
                type="daterange"
                format="yyyy 年 MM 月 dd 日"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
          <el-col :span="3" class="block">
            <el-form-item label="评分">
              <Select filterable clearable style="width:80px" v-model="dto.evaluateGrade"
                      @on-change="query()">
                <Option value="">请选择</Option>
                <Option value="0">不满意</Option>
                <Option value="1">一般</Option>
                <Option value="2">满意</Option>
              </Select>
            </el-form-item>
          </el-col>
        <el-col :span="4" class="block">
          <el-button type="primary" @click="query()" icon="el-icon-search" style="margin-left: 20px">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="reset()" style="margin-left: 20px">重置</el-button>
        </el-col>
        </el-row>
      </el-form>
    </div>

    <el-table :data="list" class="table"
              :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}"
              v-loading="loading">
      <el-table-column
          prop="serviceNo"
          width="100"
          label="员工工号">
      </el-table-column>
      <el-table-column
          prop="serviceName"
          width="100"
          label="员工名字">
      </el-table-column>
      <el-table-column
          prop="orderNo"
          width="150"
          label="订单号">
      </el-table-column>
      <el-table-column
          width="100"
          prop="serviceProject"
          label="服务产品">
      </el-table-column>
      <el-table-column
          width="100"
          prop="serviceData"
          label="日期"
      ></el-table-column>
      <el-table-column
          width="100"
          prop="serviceTimeStart"
          label="开始时间"
      ></el-table-column>
      <el-table-column
          width="100"
          prop="serviceTimeEnd"
          label="结束时间">
      </el-table-column>
      <el-table-column
          width="80"
          prop="evaluateGrade"
          label="评分">
        <template slot-scope="scope">
          <el-tag effect="dark"
                  :type="scope.row.evaluateGrade === '不满意' ? 'danger' : ''"
                  disable-transitions>{{scope.row.evaluateGrade}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          label="操作">
        <template slot-scope="scope">
          <el-row>
            <el-col :span="24">
              <div style="display: flex">
                <el-button
                    size="small "
                    icon="el-icon-thumb"
                    @click="edit(scope.row.orderNo)"
                    type="text">查看
                </el-button>
                <el-button
                    size="small "
                    type="text"
                    @click="onJournal(scope.row.orderNo)"
                    icon="el-icon-tickets">
                  日志
                </el-button>
              </div>
            </el-col>
          </el-row>
        </template>
      </el-table-column>
    </el-table>

    <el-drawer :visible.sync="journalInfoDrawer" size="90%">
      <el-table :data="journalList" class="table" ref="multipleTable" v-loading="journalInfoLoading"
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
        <el-table-column width="150" prop="orderNo" label="订单号"></el-table-column>
        <el-table-column width="150" prop="operationTime" label="操作时间"></el-table-column>
        <el-table-column width="185" prop="operationPeople" label="操作人"></el-table-column>
        <el-table-column width="60" prop="amountIncurred" label="金额"></el-table-column>
        <el-table-column width="120" prop="operationType" label="操作类型"></el-table-column>
        <el-table-column prop="operationContent" label="操作内容"></el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="journalInfo.total"
              @on-change="next"
              :current="this.dao.current"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="journalInfoSizeOpts"
              @on-page-size-change="onJournalIn"
              :page-size="10"/>
      </div>
    </el-drawer>

    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChange"
            :current="this.dto.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="10"/>
    </div>
    <el-drawer :visible.sync="drawer" size="90%" >
      <display :order="order"></display>
    </el-drawer>
  </div>
</template>

<script>
//引入组件
import display from "./display";

export default {
  name: "serviceReceiptStaff",
  // 注册组件
  components: {
    display,
  },
  data() {
    return {
      order: null,
      loading: true,
      list: null,
      drawer: false,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      dto: {
        evaluateGrade: null,
        serviceNo: null,
        serviceName: null,
        orderNo: null,
        serviceProject: null,
        days: [],
        endTime: null,
        state: null,
        size: 10,
        current: 1,
      },
      journalInfoLoading: true,
      journalList: null,
      journalInfoSizeOpts: [10, 20, 30],
      journalInfo: {total: 10, size: 10, current: 1, pages: 1},
      journalInfoDrawer: false,
      dao: {
        orderNo: null,
        size: 10,
        current: 1,
      },
    }
  },
  created() {
    this.getData();
  },
  methods: {
    edit(orderNo) {
      this.drawer = true
      this.order = orderNo
    },
    getData() {
      this.dto.startTime = null;
      this.dto.endTime = null;
      if (this.dto.days != null && this.dto.days.length > 0) {
        this.dto.startTime = this.dto.days[0];
        this.dto.endTime = this.dto.days[1];
      }
      this.$postData("selectServiceEmployee", this.dto).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.list.forEach(function (e) {
            if (e.evaluateGrade === 1) {
              e.evaluateGrade = "一般"
            } else if (e.evaluateGrade === 2) {
              e.evaluateGrade = "满意"
            } else {
              e.evaluateGrade = "不满意"
            }
          })
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    query(){
      this.loading = true;
      this.dto.current = 1;
      this.getData();
    },
    reset(){
      this.dto.orderNo= null;
      this.dto.serviceProject= null;
      this.dto.days= [];
      this.dto.evaluateGrade=null;
      this.dto.serviceNo=null;
      this.dto.serviceName=null;
      this.dto.state=null;
      this.getData();
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dto.size = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dto.current = index;
      this.getData();
    },
    //个人日志
    onJournal(orderNo) {
      this.journalInfoDrawer = true
      this.dao.orderNo = orderNo;
      this.getserviceAcceptancFormLogDESC();
    },
    //日志
    getserviceAcceptancFormLogDESC() {
      this.journalInfoLoading = false;
      this.$postData("getserviceAcceptancFormLogDESC", this.dao).then(res => {
        if (res.status === 200) {
          this.journalList = res.data.records;
          this.journalInfo.current = res.data.current;
          this.journalInfo.size = res.data.size;
          this.journalInfo.total = res.data.total
        }
      })
    },
  },
}
</script>

<style scoped>
.handle-box {
  /*margin-bottom: 10px;*/
  font-weight: bold !important;
}
table {
  border-collapse: collapse;/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
  border-left:#C8B9AE solid 1px;
  border-top:#C8B9AE solid 1px;
}
table td{
  border-right:#C8B9AE solid 1px;
  border-bottom:#C8B9AE solid 1px;
}

th {
  background: #eee;
  font-weight: normal;
}
tr {
  background: #fff;
}
tr:hover {
  background: #cc0;
}
</style>