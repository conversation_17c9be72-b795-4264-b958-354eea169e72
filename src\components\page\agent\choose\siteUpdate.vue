<template style="background-color: #000">

            <el-tabs type="card" :stretch="true" >
                <el-tab-pane label="基本信息">
                <Row>
                <Col span="24">

                        <el-form ref="dom" :model="dom" :rules="ruleValidate">
                            <Row>

                            <Col span="12">

                                    <el-form-item   prop="name">
                                        <div class="label-name">名称:</div>
                                        <el-input placeholder="请输入名称" v-model="dom.name" style="width: 70%">

                                        </el-input>
                                    </el-form-item >

                                    <el-form-item   prop="street">
                                        <div class="label-name">位置:</div>
                                        <el-input placeholder="请输入位置" v-model="dom.street" style="width: 70%">
                                        </el-input>
                                    </el-form-item >


                                 </Col>
                                <Col span="12">

                                <el-form-item  >
                                    <div class="label-name">城市区域:</div>
                                    <city-choose @init-choose="initChooseProject"  :getcity=setCity></city-choose>
                                </el-form-item>

                            </Col>
                                <Col span="24">
                                    <p style="padding: 5px;color: #999"><Button type="primary" @click="geoCode">获取经纬度</Button>//注意：经纬度将在输入位置与城市区域后获取</p>
                                    <Col span="12">
                                        <el-form-item  prop="lng">
                                            <div class="label-name">经度:</div>
                                            <el-input placeholder="请输入地址" v-model="dom.lng"  style="width: 70%" id="lng">
                                            </el-input>
                                        </el-form-item >
                                    </Col>
                                    <Col span="12">
                                        <el-form-item   prop="lat">
                                            <div class="label-name">纬度:</div>
                                            <el-input placeholder="请输入联系方式" v-model="dom.lat"  style="width: 70%" id="lat">
                                            </el-input>
                                        </el-form-item >
                                    </Col>
                                </Col>

                            </Row>

                                    <div style="margin-right: 100px;float: right">
                                        <Button type="primary" @click="save('dom')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </div>
                                </el-form>
                    </Col>


            </Row>
                </el-tab-pane>
                <el-tab-pane label="所属经纪人">
                    <center>
                        <el-autocomplete
                                v-model="dom.realName"
                                :fetch-suggestions="querySearchAsync"
                                placeholder="请输入内容"
                                @select="handleSelect"
                                :trigger-on-focus="false">
                            <template slot="prepend">所属经纪人:</template>
                            <el-button slot="append" icon="el-icon-delete"  @click="upTo">解除绑定</el-button>
                        </el-autocomplete>
                        <el-button slot="append" icon="el-icon-delete" type="primary" @click="saveTo">更改绑定</el-button>

                    </center>
                </el-tab-pane>
            </el-tabs>

</template>


<script>
    import cityChoose from '@/components/page/agent/minichoose/cityChoose.vue'
    export default {
        props:['model'],
        data() {
            return {

                restaurants:[],
                dom:this.model,
                setCity:{
                    cityId:this.model.cityId,
                    areaId:this.model.areaId,
                },
                addTo:{
                    employeeId:null,
                    siteId:null
                },
                baomuModel:{
                    RealName:null,
                    No:null,
                },

                ruleValidate: {
                    name: [
                        {required: true, message: '请输入编号', trigger: 'change'}
                    ],
                    street: [
                        {required: true, message: '请选择名称', trigger: 'change'}
                    ],
                    lng: [
                        {required: true, message: '请选择联系方式', trigger: 'change'}
                    ],
                    lat: [
                        {required: true, message: '请输入地址', trigger: 'blur'}
                    ],

                },
            }
        },
        components: {
            'cityChoose': cityChoose,
        },
        created: function () {
            console.log(this.dom)
            this.addTo.siteId=this.dom.id;
        },
        methods: {
            geoCode() {
                var geocoder = new AMap.Geocoder({
                    city: "", //城市设为北京，默认：“全国”
                });
                var address  = this.dom.cityName+this.dom.areaName+this.dom.street;
                geocoder.getLocation(address, (status, result)=> {
                    if (status === 'complete'&&result.geocodes.length) {
                        this.dom.lng= result.geocodes[0].location.lng;
                        this.dom.lat= result.geocodes[0].location.lat;

                    }else{
                        this.$message.error("获取经纬度失败" );
                    }
                });
            },
            /*
            城市选择
             */
            initChooseProject(city) {
                if (city!=null) {
                    this.dom.cityId = city.cityId
                    this.dom.areaId = city.areaId
                    this.dom.cityName=city.cityName
                    this.dom.areaName=city.areaName
                }
            },
            upTo(){

                this.$postData("up_employeeSite", this.addTo, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.success("解绑成功，" + res.msg);
                        this.chooseThisModel();
                    } else {
                        this.$message.error("解绑失败，" + res.msg);
                    }
                })
            },
            saveTo(){
                this.$postData("add_employeeSite", this.addTo, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.success("绑定成功，" + res.msg);
                        this.chooseThisModel();
                    } else {
                        this.$message.error("绑定失败，" + res.msg);
                    }
                })
            },

            save(name) {


                if(this.dom.cityId==null || this.dom.areaId==null){
                    this.$Message.success('请选择城市区域');
                    return false;
                }
                this.$refs[name].validate(valid => {
                    if (valid) {
                        this.$postData("update_site", this.dom, {}).then(res => {
                            if (res.status == 200) {
                                this.$Message.success('修改成功');
                                this.chooseThisModel();
                            } else {
                                this.$message.error("修改失败，" + res.msg);
                            }
                        })
                        console.log("验证成功")
                    }
                    else {
                        return false;
                    }
                });




            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.dom=null;
                this.$emit('init-choose', "");
            },
            //------------
            handleSelect(item) {
                this.addTo.employeeId=item.name;
                this.addTo.siteId=this.dom.id;

            },
            querySearchAsync(queryString, cb) {
                this.restaurants=[]
                this.baomuModel.RealName=this.dom.realName;
                this.$postData("agent_list", this.baomuModel, {}).then(res => {
                    if (res.status == 200) {
                        this.list = res.data;
                        this.list.forEach((item, index, arr) => {
                            var a={}
                            a.value=item.realName;
                            a.name=item.id;
                            this.restaurants.push(a);
                        });
                        this.list=[]
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                var restaurants = this.restaurants;
                var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants;
                cb(restaurants);
            },
            createStateFilter(queryString) {
                return (state) => {
                    return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                };
            },

        },

    }
</script>
<style>
    {
        background: #409eff;
        color: #fff;
    }
</style>

<style scoped>
    .label-name{
        float: left;
        text-align: center;
        width: 15%;
    }
    .ivu-col-span-9{
        padding-top: 30px;
    }
    .el-input-group{
        width: 80%;
    }
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

