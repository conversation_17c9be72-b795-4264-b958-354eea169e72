<template>
	<div class="container">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">保姆统计</el-menu-item>
			<el-menu-item index="1">线索统计</el-menu-item>
		</el-menu>

		<!-- 搜索栏目 -->
		<div v-if="choiceIndexNow==0">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="保姆姓名、开发人姓名、手机号等"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="员工状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option label="下架" value="2"></el-option>
									<el-option label="上架" value="1"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-form-item label="入驻时间" style="margin-right: 20px">
								<el-date-picker v-model="showDate" type="datetimerange" :picker-options="pickerOptions"
									range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
									value-format="yyyy-MM-dd HH:mm:ss" align="right">
								</el-date-picker>
							</el-form-item>
						</el-col>

						<el-col :span="6" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column prop="id" width="80" label="编号" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="no" label="工号">
					<template slot-scope="scope">
						<span>{{scope.row.no}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="realName" label="姓名">
					<template slot-scope="scope">
						<span>{{scope.row.realName}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="age" label="年龄" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.age||'-'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="phone" label="手机号">
					<template slot-scope="scope">
						<span>{{scope.row.phone||'-'}}</span>
					</template>
				</el-table-column>

				<!-- 				<el-table-column width="140" prop="address" label="城市">
					<template slot-scope="scope">
						<span>{{scope.row.address||'-'}}</span>
					</template>
				</el-table-column> -->

				<el-table-column width="140" prop="createDate" label="注册时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.createDate}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="registerWay" label="注册渠道">
					<template slot-scope="scope">
						<span>{{scope.row.registerWay}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="introducerName" label="开发人姓名">
					<template slot-scope="scope">
						<span>{{scope.row.introducerName || '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="introducerPhone" label="开发人电话">
					<template slot-scope="scope">
						<span>{{scope.row.introducerPhone || '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="nowState" label="状态">
					<template slot-scope="scope">
						<div>
							<el-tag :type="scope.row.nowState=='上架'?'success':'info'">{{scope.row.nowState}}</el-tag>
						</div>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="putTime" label="上架时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.putTime|| '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="storeName" label="门店">
					<template slot-scope="scope">
						<span>{{scope.row.storeName || '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="authEmployeeName" label="鉴定人姓名">
					<template slot-scope="scope">
						<span>{{scope.row.authEmployeeName || '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="authState" label="鉴定状态">
					<template slot-scope="scope">
						<span>{{scope.row.authState || '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="authWorkType" label="鉴定工种">
					<template slot-scope="scope">
						<span>{{scope.row.authWorkType || '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="authLevel" label="鉴定等级">
					<template slot-scope="scope">
						<span>{{scope.row.authLevel || '暂无'}}</span>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<div v-if="choiceIndexNow==1">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="需求名称、经纪人、开发人名等"></el-input>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="需求状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option label="电联" value="0"></el-option>
									<el-option label="需求" value="1"></el-option>
									<el-option label="预算" value="2"></el-option>
									<el-option label="匹配" value="3"></el-option>
									<el-option label="面试" value="4"></el-option>
									<el-option label="签约" value="5"></el-option>
									<el-option label="结算" value="6"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="6">
							<el-form-item label="需求时间" style="margin-right: 20px">
								<el-date-picker v-model="showDate" type="datetimerange" :picker-options="pickerOptions"
									range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
									value-format="yyyy-MM-dd HH:mm:ss" align="right">
								</el-date-picker>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>
							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column prop="id" width="80" label="编号" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="productName" label="产品名">
					<template slot-scope="scope">
						<span>{{scope.row.productName}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="storeName" label="门店">
					<template slot-scope="scope">
						<span>{{scope.row.storeName}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="cityName" label="地区">
					<template slot-scope="scope">
						<span>{{scope.row.cityName||''}}{{scope.row.areaName||''}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="agentNo" label="经纪人工号">
					<template slot-scope="scope">
						<span>{{scope.row.agentNo}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="agentName" label="经纪人">
					<template slot-scope="scope">
						<span>{{scope.row.agentName||'-'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="channelName" label="开发人">
					<template slot-scope="scope">
						<span>{{scope.row.channelName||'-'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="channelPhone" label="开发人电话">
					<template slot-scope="scope">
						<span>{{scope.row.channelPhone}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="flowStatus" label="需求状态" sortable>
					<template slot-scope="scope">
						<div>
							<el-tag :type="formatTypeStyle(scope.row.flowStatus)">{{scope.row.flowStatusName}}</el-tag>
						</div>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="startTime" label="需求时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.startTime|| '-'}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作">
					<template slot-scope="scope">
						<el-button @click="openOrderNeeds(scope.row.id)" type="warning" size="small"
							style="margin-left: 10px">线索详情</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<el-drawer size="25%" :with-header="false" :visible.sync="viewModal" direction="rtl">
			<iframe id="iframeId" :src="url" frameborder="0" style="width: 400px;height: 800px;margin: 0 auto;"
				scrolling="auto">
			</iframe>
		</el-drawer>

	</div>

</template>

<script>
	import Vue from 'vue';

	export default {
		name: "certificate",

		data() {
			return {
				url: '',
				imageUrl: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				excelUploadId: 1,
				excelUploadUrl: "",

				isEdit: false,
				showEdit: true,
				imgModal: false,
				viewModal: false,
				detailType: 0,
				loading: true,
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				pageSizeOpts: [10, 20, 50, 100],
				pickerOptions: {
					shortcuts: [{
						text: '昨天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}]
				},
				showDate: '',
				list: [],
				typeList: [],
				switchStateList: [{
						id: 0,
						typeName: "关闭",
						type: "info"
					},
					{
						id: 1,
						typeName: "开启",
						type: "success"
					}
				],
				choiceIndex: '0',
				choiceIndexNow: '0',
				choiceItemIndex: 0,
				choiceItem: {},
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				searchType: null,
				searchState: null,
				quer: {
					search: "",
					state: null,
					orderBy: "createTime ASC",
					current: 1,
					size: 10
				},
				stateList: [{
					value: 0,
					text: "下架",
					type: "info"
				}, {
					value: 1,
					text: "上架",
					type: "success"
				}],
				typeStyleList: [{
						id: 0,
						type: "success"
					},
					{
						id: 1,
						type: "info"
					},
					{
						id: 2,
						type: "warning"
					}, {
						id: 3,
						type: "primary"
					}
				],
			}
		},
		created() {
			this.getData()
		},
		methods: {
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceIndex = key
				this.expands = []
				this.showEdit = true
				this.reset()
			},
			getData() {
				this.choiceIndexNow = 2
				if (this.choiceIndex == 0) {
					if (this.showDate) {
						this.quer.startTime = this.showDate[0]
						this.quer.endTime = this.showDate[1]
					}
					this.$postData("pageBaomuPartner", this.quer).then(res => {
						this.loading = false
						if (res.status == 200) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
							this.choiceIndexNow = this.choiceIndex
							this.$message.success(res.msg)
						} else {
							this.list = []
							this.$message.error('未查询到相关保姆数据!')
							this.choiceIndexNow = this.choiceIndex
						}
					})
				} else if (this.choiceIndex == 1) {
					if (this.showDate) {
						this.quer.startDateTime = this.showDate[0]
						this.quer.endDateTime = this.showDate[1]
					}
					this.$postData("pageOrderNeedsPartner", this.quer).then(res => {
						this.loading = false
						if (res.status == 200) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
							this.choiceIndexNow = this.choiceIndex
							this.$message.success(res.msg)
						} else {
							this.list = []
							this.$message.error('未查询到相关线索数据!')
							this.choiceIndexNow = this.choiceIndex
						}
					})
				}
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.quer.state = this.searchState
				this.quer.flowStatus = this.searchState
				this.getData()
			},
			// 重置
			reset() {
				this.loading = true
				this.quer.search = ""
				this.quer.state = null
				this.quer.flowStatus = null
				// this.quer.orderBy = "createTime DESC"
				this.quer.startDateTime = null
				this.quer.startTime = null
				this.quer.endDateTime = null
				this.quer.endTime = null
				this.quer.current = 1

				this.searchType = null
				this.searchState = null
				this.showDate = ''
				this.isEdit = false
				this.getData()
			},
			formatTypeStyle(type) {
				return this.typeStyleList[type % 4].type
			},
			openOrderNeeds(id) {
				this.url = 'https://jiajie.xiaoyujia.com/pages-work/operations/clew/clewPage?id=' + id
				this.viewModal = true
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "成就图标") {
					tips = "推荐尺寸：200*200"
				} else if (column.label == "成就图片") {
					tips = "推荐尺寸：200*200"
				} else if (column.label == "等级") {
					tips = "数字越大，等级越高"
				} else if (column.label == "成就类型") {
					tips = "可根据类型进行分类"
				} else if (column.label == "成立条件") {
					tips = "与达标数量进行联动，判定是否达成"
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 打开图片预览
			openImg(url) {
				this.imageUrl = url || this.blankImg
				this.imgModal = true
			},
			// 打开视频预览
			openVideo(url) {
				if (url != null && url != '') {
					this.videoUrl = url
				}
				this.videoModal = true
			},
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>