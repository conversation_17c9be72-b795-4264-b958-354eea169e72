<!-- 线索跟踪 OrderNeeds Track -->
<template>
    <div class="container">
        <div class="handle-box">
            <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="线索ID">
                    <el-input v-model="dom.id" placeholder="请输入线索ID" clearable
                              @keyup.enter.native="onQuery"></el-input>
                </el-form-item>
              <el-form-item label="客户手机号">
                <el-input v-model="dom.phone" placeholder="请输入客户手机号" clearable
                          @keyup.enter.native="onQuery"></el-input>
              </el-form-item>
                <el-form-item label="来源渠道">
                    <el-select ref="tgSel" filterable v-model="dom.tg" placeholder="请选择来源渠道" clearable>
                        <el-option
                                v-for="(item,index) in tgList" :key="index" :label="item.text" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
              <el-form-item label="线索来源渠道">
                <el-select ref="tgSel" filterable v-model="dom.needsChannel" placeholder="请选择线索来源渠道" clearable>
                  <el-option
                      v-for="(item,index) in needsChannelList" :key="index" :label="item.text" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
                <el-form-item label="跟进周期">
                    <el-select ref="tgSel" filterable v-model="dom.trackDay" placeholder="请选择跟进周期" clearable>
                        <el-option
                                v-for="(item,index) in trackList" :key="index" :label="item.text" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="经纪人姓名">
                    <el-input v-model="dom.agentName" placeholder="请输入经纪人姓名" clearable
                              @keyup.enter.native="onQuery"></el-input>
                </el-form-item>
              <el-form-item label="开发人">
                <el-input v-model="dom.developer" placeholder="请输入工号、名称" clearable
                          @keyup.enter.native="onQuery"></el-input>
              </el-form-item>
              <el-form-item label="回访状态">
              <el-select v-model="dom.followUpFlag" placeholder="请选择回访状态" clearable>
                <el-option
                    v-for="item in followUpOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
              </el-form-item>
              <el-form-item label="线索类型">
                <el-select v-model="dom.needsType" placeholder="请选择线索类型" clearable>
                  <el-option
                      v-for="item in needsOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="成交状态">
                <el-select v-model="dom.dealState" placeholder="请选择成交状态" clearable>
                  <el-option
                      v-for="item in dealStateOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="拆单状态">
                <el-select v-model="dom.ifSplit" placeholder="请选择拆单状态" clearable>
                  <el-option
                      v-for="item in ifSplitOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="线索状态">
                <el-select v-model="dom.flowStatus" placeholder="请选择线索状态" clearable>
                  <el-option
                      v-for="item in flowStatusOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="所属门店">
                <el-select v-model="dom.storeId" filterable  placeholder="请选择所属门店" clearable>
                  <el-option
                      v-for="item in storeOptions"
                      :key="item.id" :label="item.storeName" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否开发">
                <el-select v-model="dom.ifDevelop" placeholder="请选择是否开发" clearable>
                  <el-option
                      v-for="item in ifDevelopOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="所属门店类型">
                <el-select v-model="dom.storeType" placeholder="请选择所属门店类型" clearable>
                  <el-option
                      v-for="item in storeTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
                <el-form-item label="创建时间周期">
                    <el-date-picker
                            :clearable="false"
                            v-model="dom.times"
                            type="daterange"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            range-separator="~"
                            @change="changeTime"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
              <el-form-item label="所属大区">
                <el-select v-model="dom.storeRegionId" filterable  placeholder="请选择所属大区" clearable>
                  <el-option
                      v-for="item in regionOptions"
                      :key="item.id" :label="item.regionName" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
                <el-form-item label="需求状态">
                  <el-checkbox-group v-model="dom.statusList">
                    <el-checkbox label="0" name="0" border>已取消</el-checkbox>
                    <el-checkbox label="1" name="1" border>未处理</el-checkbox>
                    <el-checkbox label="2" name="2" border>已开单</el-checkbox>
                    <el-checkbox label="3" name="3" border>已读</el-checkbox>
                    <el-checkbox label="4" name="4" border>待支付</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              <el-form-item label="拆单时间">
                <el-date-picker
                    :clearable="false"
                    v-model="dom.splitTimes"
                    type="daterange"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    range-separator="~"
                    @change="splitChangeTime"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
                <el-form-item label="线索标签">
                  <el-select   filterable v-model="dom.validOrNot" placeholder="请选择线索标签" clearable>
                    <el-option
                        v-for="(item,index) in needsTagList" :key="index" :label="item.text" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
<!--              <el-form-item label="线索来源">-->
<!--                <el-select   filterable v-model="dom.needsSource" placeholder="请选择线索来源" clearable>-->
<!--                  <el-option-->
<!--                      v-for="(item,index) in needsSourceList" :key="index" :label="item.text" :value="item.value">-->
<!--                  </el-option>-->
<!--                </el-select>-->
<!--              </el-form-item>-->
              <el-row>
                <el-form-item>
                    <el-button icon="el-icon-search" type="primary" @click="onQuery" v-loading="loading">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-refresh" @click="reset()" v-loading="loading">重置</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-download" type="primary" @click="exportExcel" v-loading="loading">导出
                    </el-button>
                </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-download" type="info" @click="exportAll" v-if="roleId!=3" v-loading="loading">导出回访记录
                </el-button>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-download" type="info" @click="exportHair" v-if="roleId!=3" v-loading="loading">导出毛线索记录
                </el-button>
              </el-form-item>
                <el-form-item>
                  <div style="font-size: 20px;font-weight: bold;color: red;">成单量：{{needsData.orderFormTotal}}</div>
                </el-form-item>
                <el-form-item/>
                <el-form-item>
                  <div style="font-size: 20px;font-weight: bold;color: red">成单率：{{needsData.orderFormRate}}%</div>
                </el-form-item>
                <el-form-item/>
                <el-form-item>
                  <div style="font-size: 20px;font-weight: bold;color: red">营业额：￥{{needsData.orderFormSum}}</div>
                </el-form-item>
              </el-row>
            </el-form>

            <el-table :data="orderNeedsTrackDataList" height="500px" v-loading="loading"
                      :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
                <el-table-column
                        label="序号"
                        type="index"
                        align="center">
                </el-table-column>
                <el-table-column
                        prop="id"
                        label="线索ID"
                        align="center"
                ></el-table-column>
              <el-table-column
                  prop="phone"
                  label="客户手机号"
                  width="180"
                  align="center"
              ></el-table-column>
              <el-table-column
                  prop="street"
                  label="服务地址"
                  width="140"
                  align="center"
              ></el-table-column>
              <el-table-column
                  prop="status"
                  label="需求状态"
                  width="80">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.status=='0'" type="danger">已取消</el-tag>
                  <el-tag v-if="scope.row.status=='1'" type="info">未处理</el-tag>
                  <el-tag v-else-if="scope.row.status=='2'" type="warning">已开单</el-tag>
                  <el-tag v-else-if="scope.row.status=='3'" type="success">已读</el-tag>
                  <el-tag v-else-if="scope.row.status=='4'" type="info">待支付</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                  prop="storeName"
                  label="所属门店"
                  width="180"
                  align="left">
              </el-table-column>
                <el-table-column
                        prop="agentName"
                        label="所属经纪人"
                        width="170"
                        align="left">
                </el-table-column>
              <el-table-column
                  prop="channel"
                  label="线索开发人"
                  width="170"
                  align="left">
              </el-table-column>
                <el-table-column
                        prop="productName"
                        label="服务项目"
                        align="left">
                </el-table-column>
              <el-table-column
                  prop="salary"
                  label="服务薪资"
                  align="left">
              </el-table-column>
              <el-table-column
                  prop="servicePay"
                  label="合同薪资"
                  align="left">
              </el-table-column>
              <el-table-column
                  prop="agencyFee"
                  label="中介费"
                  align="left">
              </el-table-column>
              <el-table-column
                  prop="amount"
                  label="支付金额"
                  align="left">
              </el-table-column>
                <el-table-column
                        prop="currentStage"
                        label="当前阶段"
                        align="left">
                </el-table-column>
                <el-table-column
                        prop="trackDays"
                        label="跟进天数"
                        width="100"
                        align="right">
                </el-table-column>
              <el-table-column
                  prop="exchangeNum"
                  label="阿姨调换次数"
                  width="100"
                  align="right">
              </el-table-column>
              <el-table-column
                  prop="ifSigning"
                  label="是否老客户续单"
                  width="120"
                  align="right">
              </el-table-column>
                <el-table-column
                        prop="tg"
                        width="100"
                        label="来源渠道"
                        align="center">
                </el-table-column>
              <el-table-column
                  prop="needsChannelName"
                  width="100"
                  label="线索来源渠道"
                  align="center">
              </el-table-column>
              <el-table-column
                  prop="validOrNot"
                  label="线索标签"
                  width="120"
              >
                <template slot-scope="scope">
                  <el-tag v-if="!scope.row.validOrNot || scope.row.split == 0" type="info">未标识</el-tag>
                  <el-tag v-if="scope.row.validOrNot == 1" type="success">有效线索</el-tag>
                  <el-tag v-if="scope.row.validOrNot == 2" type="error">无效线索</el-tag>
                </template>
              </el-table-column>
<!--              <el-table-column-->
<!--                  prop="needsSource"-->
<!--                  label="线索来源"-->
<!--                  width="160"-->
<!--              ></el-table-column>-->
              <el-table-column
                  prop="followUpRemark"
                  label="标签备注"
                  width="200"
              ></el-table-column>
                <el-table-column
                        prop="createTime"
                        label="创建时间"
                        align="center">
                </el-table-column>
              <el-table-column
                  prop="splitTime"
                  label="拆单时间"
                  align="center">
              </el-table-column>
              <el-table-column width="140" label="客户回访记录">
                <template slot-scope="scope">
                  <el-tag type="primary" @click="lookFollowUp(scope.row)">点击查看</el-tag>
                </template>
              </el-table-column>
              <el-table-column width="140" label="合同">
                <template slot-scope="scope">
                  <el-tag type="primary" @click="lookContract(scope.row)">点击查看</el-tag>
                </template>
              </el-table-column>
                <el-table-column
                        width="80"
                        label="操作"
                        align="center">
                    <template slot-scope="scope">
                        <el-button type="primary" size="mini" plain @click="showOrderNeedsRemarkDialog(scope.row.id)">
                            查看
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChangePage"
                      :current="dom.current"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="dom.size"/>
            </div>
        </div>
        <el-dialog
                title="订单备注"
                :visible.sync="showRemarkDialog"
                width="30%">
            <div v-for="(item, index) in orderNeedsRemarkList" :key="index">

                <div style="color: #333;">
                    <div>
                        {{ index + 1 }}:&nbsp;
                        <span>
                            <span style="font-weight: bold;">创建时间 : </span>
                            {{ item.createTime }}
                        </span> &emsp;
                        <span>
                            <span style="font-weight: bold;">创建人 : </span>
                            {{ item.operator }}
                        </span> &emsp;
                        <span> ->&emsp;{{ item.title }} </span>
                        <div style="height: 1px;"></div>
                        <div>
                            <span style="font-weight: bold;">操作内容 : </span>
                            {{ item.message }}
                        </div>
                    </div>
                </div>
                <br/>
            </div>
        </el-dialog>

      <el-dialog
          title="回访记录："
          :visible.sync="dialogFlag"
          width="80%">
        <div style="display: flex">
          <el-date-picker v-model="days" type="daterange" unlink-panels :picker-options="dzPickerOptions"
                          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
          </el-date-picker>
          <div style="width: 20px"></div>
          <el-select v-model="followUp.type" placeholder="回访类型" clearable>
            <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
          <el-button style="margin-left: 10px;margin-bottom: 20px;" type="primary" icon="el-icon-search"
                     @click="getFollowUp">搜索
          </el-button>
          <el-button style="margin-left: 10px;margin-bottom: 20px;" type="success" icon="el-icon-circle-plus-outline"
                     @click="exportFollowUp">导出回访信息
          </el-button>
        </div>
        <el-table :data="followUpList" v-loading="followUpLoading" border stripe
                  :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
                  :expand-row-keys="expands" @expand-change="expandSelect">

          <el-table-column width="160" prop="communicateTime" label="回访时间">
            <template slot-scope="scope">
              <span>{{ scope.row.communicateTime }}</span>
            </template>
          </el-table-column>

          <el-table-column width="140" prop="realName" label="回访人">
            <template slot-scope="scope">
              <span>{{scope.row.realName+'-'+scope.row.employeeNo}}</span>
            </template>
          </el-table-column>

          <el-table-column width="150" prop="storeName" label="回访人门员店">
            <template slot-scope="scope">
              <span>{{ scope.row.storeName||'未知'}}</span>
            </template>
          </el-table-column>


          <el-table-column width="140" prop="type" label="回访类型">
            <template slot-scope="scope">
              <span>{{
                  scope.row.type == 1 ? '订单' : scope.row.type == 2 ? '询价' :
                      scope.row.type == 3 ? '投诉' : scope.row.type == 4 ? '催人' : scope.row.type == 5 ? '加盟' :
                          scope.row.type == 6 ? '员工服务' :
                              scope.row.type == 7 ? '招聘' : scope.row.type == 8 ? '业务合作' : scope.row.type == 9 ? '品控回访'
                                  : scope.row.type == 10 ? '其他'
                                      : scope.row.type == 11 ? '家政咨询' : scope.row.type == 12 ? '估价跟踪' : scope.row.type == 13 ? '订单溢出'
                                          : scope.row.type == 14 ? '跟单'
                                              : scope.row.type == 15 ? '无服务' : scope.row.type == 30 ? '反馈' : '未知'

                }}</span>
            </template>
          </el-table-column>

          <el-table-column width="250" prop="remarks" label="回访备注">
            <template slot-scope="scope">
              <span>{{ scope.row.remarks}}</span>
            </template>
          </el-table-column>

          <el-table-column width="140" prop="id" label="会员id">
            <template slot-scope="scope">
              <span>{{ scope.row.id}}</span>
            </template>
          </el-table-column>


          <el-table-column width="200" prop="account" label="会员账号">
            <template slot-scope="scope">
              <span>{{ scope.row.account}}</span>
            </template>
          </el-table-column>


          <el-table-column width="200" prop="bindTel" label="会员手机号">
            <template slot-scope="scope">
              <span>{{ scope.row.bindTel}}</span>
            </template>
          </el-table-column>


          <el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
            <template slot-scope="scope">
              <el-button @click="openEdit(scope.row)" type="success" size="small" :disabled="scope.row.isEdit"
                         v-if="!scope.row.isEdit" icon="el-icon-edit">同步为区域负责人
              </el-button>
              <el-button @click="openEdit(scope.row)" type="primary" size="small" :disabled="scope.row.isEdit"
                         v-if="!scope.row.isEdit" icon="el-icon-edit">区域转移
              </el-button>

            </template>
          </el-table-column>

        </el-table>
        <div class="pagination">
          <Page :total="followUpPageInfo.total" @on-change="followUpOnChange" :current="followUpPageInfo.current" :show-total="true"
                :show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="followUpPageSizeChange"
                :page-size="followUpPageInfo.size" />
        </div>

      </el-dialog>
      <el-dialog
          title="导出回访记录："
          :visible.sync="exportDialogFlag"
          width="80%">
        <el-form ref="form">
          <el-row>
            <el-col :span="7">
              <el-form-item label="回访时间">
                <el-date-picker v-model="followUpDays" type="daterange" unlink-panels :picker-options="dzPickerOptions"
                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="经纪人">
                <el-input v-model="exportDto.searchStr" placeholder="请输入姓名、工号" clearable></el-input>
              </el-form-item>
              </el-col>
            <el-col :span="4" style="padding-left: 40px;">
              <el-form-item label="线索类型">
                <el-select v-model="exportDto.needsType" placeholder="请选择线索类型" clearable>
                  <el-option
                      v-for="item in needsOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" style="padding-left: 40px;">
              <el-form-item label="回访类型">
            <el-select v-model="exportDto.type" placeholder="请选择回访类型" clearable>
              <el-option
                  v-for="item in typeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" style="margin-top: 30px;margin-left: 50px">
              <el-button type="info"
                         style="margin-bottom: 15px"
                         @click="exportAllFollowUp"
                         icon="el-icon-download">导出
              </el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-dialog>

      <el-dialog
          title="导出毛线索记录："
          :visible.sync="exportHairgNeedsFlag"
          width="80%">
        <el-form ref="form">
          <el-row>
            <el-col :span="7">
              <el-form-item label="分发时间">
                <el-date-picker v-model="distributeDays" type="daterange" unlink-panels :picker-options="dzPickerOptions"
                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="4" style="margin-top: 30px;margin-left: 50px">
              <el-button type="info"
                         style="margin-bottom: 15px"
                         @click="exportHairNeeds"
                         icon="el-icon-download">导出
              </el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-dialog>

      <el-link :href="hrefUrl" target="_blank" id="elLink"/>

    </div>
</template>

<script>
    import moment from "moment/moment";

    export default {
        name: "orderNeedsTrack",
        data() {
            return {
                loading: false,
              exportDialogFlag: false,
              needsTagList: [
                {text: "未标识",value: 0},
                {text: "有效线索",value: 1},
                {text: "无效线索",value: 2},
              ],
              needsSourceList: [
                {text: "区域直派",value: 0},
                {text: "积分轮派",value: 1},
                {text: "他人分配",value: 2},
              ],
              roleId: localStorage.getItem("roleId"),
              exportHairgNeedsFlag: false,
              dialogFlag: false,
              needsData: {},
              followUpLoading: true,
              hrefUrl: "",
              followUpList: [],
              days: [],
              exportHairDto: {
                startTime: '',
                endTime: '',
              },
              exportDto: {
                startTime: '',
                endTime: '',
                needsType: '',
                type: '',
                searchStr: '',
              },
              followUpDays: [],
              storeOptions: [],
              regionOptions: [],
              distributeDays: [],
              needsOptions: [{
                value: 2,
                label: '常规线索'
              }, {
                value: 1,
                label: '毛线索'
              }],
              ifSplitOptions: [{
                value: 2,
                label: '未拆单'
              }, {
                value: 1,
                label: '已拆单'
              }],
              storeTypeOptions: [{
                value: 6,
                label: '承包店'
              },{
                value: 3,
                label: '共创店'
              }, {
                value: 2,
                label: '直营店'
              },{
                value: 1,
                label: '平台'
              }],
              ifDevelopOptions: [{
                value: 2,
                label: '否'
              }, {
                value: 1,
                label: '是'
              }],
              flowStatusOptions: [{
                value: 1,
                label: '电联'
              }, {
                value: 2,
                label: '需求'
              }, {
                value: 3,
                label: '预算'
              }, {
                value: 4,
                label: '匹配'
              }, {
                value: 5,
                label: '面试'
              }, {
                value: 6,
                label: '签约'
              }, {
                value: 7,
                label: '结算'
              }],
              dealStateOptions: [{
                value: 2,
                label: '未成交'
              }, {
                value: 1,
                label: '已成交'
              }],
              followUpOptions: [{
                value: 2,
                label: '未回访'
              }, {
                value: 1,
                label: '已回访'
              }],
              typeOptions: [{
                value: 1,
                label: '订单'
              }, {
                value: 2,
                label: '询价'
              }, {
                value: 3,
                label: '投诉'
              }, {
                value: 4,
                label: '催人'
              }, {
                value: 5,
                label: '加盟'
              }, {
                value: 6,
                label: '员工服务'
              }, {
                value: 7,
                label: '招聘'
              }, {
                value: 8,
                label: '业务合作'
              }, {
                value: 9,
                label: '品控回访'
              }, {
                value: 10,
                label: '其他'
              }, {
                value: 11,
                label: '家政咨询'
              }, {
                value: 12,
                label: '估价跟踪'
              }, {
                value: 13,
                label: '订单溢出'
              }, {
                value: 14,
                label: '跟单'
              }, {
                value: 15,
                label: '无服务'
              }, {
                value: 30,
                label: '反馈'
              }],
              dzPickerOptions: {
                shortcuts: [{
                  text: '最近一周',
                  onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                  text: '最近一个月',
                  onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                  text: '最近三个月',
                  onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                    picker.$emit('pick', [start, end]);
                  }
                }]
              },
              followUpPageInfo: {
                total: 10,
                size: 5,
                current: 1,
                pages: 1
              },
              followUp: {
                size: 10,
                current: 1,
                type: '',
                needsId: '',
                startTime: '',
                endTime: '',
              },
                dom: {
                    statusList: [],
                    id: null,
                    phone: null,
                    tg: null,
                    needsChannel: null,
                    agentName: null,
                    developer: null,
                    followUpFlag: null,
                    needsType: null,
                    dealState: null,
                    ifSplit: null,
                    storeType: null,
                    storeId: null,
                    storeRegionId: null,
                    ifDevelop: null,
                    flowStatus: null,
                    times: null,
                    splitTimes: null,
                    validOrNot: null,
                    needsSource: null,
                    startTime: null,
                    endTime: null,
                    splitStartTime: null,
                    splitEndTime: null,
                    employeeId: localStorage.getItem("id"),
                    trackDay: null,
                    size: 15,
                    current: 1
                },
                pageSizeOpts: [10, 15, 20, 30, 50, 100],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                tgList: [],
              needsChannelList: [],
                trackList: [
                    {text: '小于 3 天', value: 3},
                    {text: '小于 7 天', value: 7},
                    {text: '小于 30 天', value: 30},
                    {text: '小于 90 天', value: 90},
                ],
                orderNeedsTrackDataList: [],
                showRemarkDialog: false,
                orderNeedsRemarkList: []
            }
        },
        created() {
            // this.dom.times = this.timeDefault();
            this.getNeedsAllTg();
            this.getAllNeedsChannel();
            this.getData();
            this.getAllFranchiseStore();
            this.getAllStoreRegion();
        },
        methods: {
          lookContract(item){
            this.$getData("getContractByNeedsId", {id:item.id}, {}).then(res => {
              if (res.status == 200) {
                  this.hrefUrl = res.data
                setTimeout(() => {
                  document.getElementById("elLink").click();
                }, 1000);
              } else {
                this.$message.error(res.msg);
              }
            })
          },
          getNeedsAllTg(){
            this.$getData("getNeedsAllTg", {}, {}).then(res => {
              if (res.status == 200) {
                this.tgList = res.data
              } else {
                this.$message.error("查询来源渠道!");
              }
            })
          },
          getAllNeedsChannel(){
            this.$getData("getAllNeedsChannel", {}, {}).then(res => {
              if (res.status == 200) {
                this.needsChannelList = res.data
              } else {
                this.$message.error("查询线索来源渠道!");
              }
            })
          },
          getAllStoreRegion(){
            this.$getData("getAllStoreRegion", {id:localStorage.getItem("id")}, {}).then(res => {
              if (res.code == 0) {
                this.regionOptions = res.data
              } else {
                this.$message.error("查询大区分类失败!");
              }
            })
          },
          getAllFranchiseStore(){
            this.$getData("getAllFranchiseStore", {type:1}, {}).then(res => {
              if (res.code == 0) {
                this.storeOptions = res.data
                let obj = {
                  id: 2,
                  storeName: '厦门小羽佳家政'
                }
                this.storeOptions.push(obj)
              } else {
                this.$message.error("查询筛选门店失败!");
              }
            })
          },
          exportAll(){
            this.exportDialogFlag = true
          },
          exportHair(){
            this.exportHairgNeedsFlag = true
          },
          exportHairNeeds(){
            this.exportHairDto.startTime = '';
            this.exportHairDto.endTime = '';
            if (this.distributeDays != null && this.distributeDays.length > 0) {
              this.exportHairDto.startTime = moment(this.distributeDays[0]).format("YYYY-MM-DD");
              this.exportHairDto.endTime = moment(this.distributeDays[1]).format("YYYY-MM-DD")
            }
            this.hrefUrl = "https://biapi.xiaoyujia.com/orderNeeds/exportHairNeeds?startTime="
                +this.exportHairDto.startTime+"&endTime="+this.exportHairDto.endTime
            setTimeout(() => {
              document.getElementById("elLink").click();
            }, 1000);
          },
          exportAllFollowUp(){
            this.exportDto.startTime = '';
            this.exportDto.endTime = '';
            if (this.followUpDays != null && this.followUpDays.length > 0) {
              this.exportDto.startTime = moment(this.followUpDays[0]).format("YYYY-MM-DD");
              this.exportDto.endTime = moment(this.followUpDays[1]).format("YYYY-MM-DD")
            }
            this.hrefUrl = "https://biapi.xiaoyujia.com/orderNeeds/exportAllFollowUp?type="+this.exportDto.type+
                "&startTime="+this.exportDto.startTime+"&endTime="+this.exportDto.endTime+"&needsType="+this.exportDto.needsType
                +"&searchStr="+this.exportDto.searchStr
            setTimeout(() => {
              document.getElementById("elLink").click();
            }, 1000);
          },
          exportFollowUp(){
            this.followUp.startTime = '';
            this.followUp.endTime = '';
            if (this.days != null && this.days.length > 0) {
              this.followUp.startTime = moment(this.days[0]).format("YYYY-MM-DD");
              this.followUp.endTime = moment(this.days[1]).format("YYYY-MM-DD")
            }
            this.hrefUrl = "https://biapi.xiaoyujia.com/orderNeeds/exportFollowUp?type="+this.followUp.type+
                "&startTime="+this.followUp.startTime+"&endTime="+this.followUp.endTime
            setTimeout(() => {
              document.getElementById("elLink").click();
            }, 1000);
          },
          // 页码大小
          followUpPageSizeChange(size) {
            this.followUpLoading = true;
            this.followUp.size = size
            this.getFollowUp()
          },
          // 跳转页码
          followUpOnChange(index) {
            this.followUpLoading = true;
            this.followUp.current = index
            this.getFollowUp()
          },
          lookFollowUp(item){
            this.followUp.needsId = item.id
            this.getFollowUp()
              this.dialogFlag = true
            },
            getFollowUp(){
              this.followUp.startTime = '';
              this.followUp.endTime = '';
              if (this.days != null && this.days.length > 0) {
                this.followUp.startTime = moment(this.days[0]).format("YYYY-MM-DD");
                this.followUp.endTime = moment(this.days[1]).format("YYYY-MM-DD")
              }
              // 获取绑定门店
              this.$getData("getFollowUp", this.followUp).then(res => {
                this.followUpLoading = false
                if (res.code == 0) {
                  this.followUp.type = ''
                  this.followUp.startTime = ''
                  this.followUp.endTime = ''
                  this.followUpList = res.data.records
                  this.followUpPageInfo.current = res.data.current
                  this.followUpPageInfo.size = res.data.size
                  this.followUpPageInfo.total = res.data.total
                } else {
                  this.$message.error('查询回访记录失败，请稍后重试!')
                }
              })
            },
            loadStart() {
                this.loading = true;
            },
            loadEnd() {
                this.loading = false;
            },
            timeDefault() {
                const invTime = (1000 * 60 * 60 * 24);
                const endDateNs = new Date();
                const startDateNs = new Date(endDateNs.getTime() - (invTime * 30));
                // 月，日 不够10补0
                let defaultStartTime = startDateNs.getFullYear() + '-'
                    + ((startDateNs.getMonth() + 1) >= 10 ? (startDateNs.getMonth() + 1) : '0' + (startDateNs.getMonth() + 1)) + '-' + (startDateNs.getDate() >= 10 ? startDateNs.getDate() : '0' + startDateNs.getDate());
                let defaultEndTime = endDateNs.getFullYear() + '-' + ((endDateNs.getMonth() + 1) >= 10 ? (endDateNs.getMonth() + 1) : '0' + (endDateNs.getMonth() + 1)) + '-' + (endDateNs.getDate() >= 10 ? endDateNs.getDate() : '0' + endDateNs.getDate());
                this.dom.startTime = defaultStartTime + ' 00:00:00';
                this.dom.endTime = defaultEndTime + ' 23:59:59';
                return [defaultStartTime, defaultEndTime];
            },
            changeTime(v, s) {
                this.dom.startTime = v[0];
                this.dom.endTime = v[1];
            },
            splitChangeTime(v, s) {
              this.dom.splitStartTime = v[0];
              this.dom.splitEndTime = v[1];
            },
            onQuery() {
                this.pageInfo.current = 1;
                this.dom.current = 1;
                this.getData();
            },
            getData() {
                this.loadStart();
                this.orderNeedsTrackDataList = [];
                this.$postData("orderNeedsTrackPage", JSON.stringify(this.dom), {}).then((res) => {
                    if (res.status === 200) {
                        this.orderNeedsTrackDataList = res.data.orderNeedsTrackPage.records;
                        this.pageInfo.current = res.data.orderNeedsTrackPage.current;
                        this.pageInfo.size = res.data.orderNeedsTrackPage.size;
                        this.pageInfo.total = res.data.orderNeedsTrackPage.total;
                        this.needsData = res.data.needsData;
                        this.loadEnd();
                    } else {
                        this.$message.error(res.msg);
                        this.pageInfo.current = 1;
                        this.pageInfo.size = 0;
                        this.loadEnd();
                    }
                });
            },
            reset() {
                this.pageInfo.current = 1;
                this.dom.current = 1;
                this.dom.id = null;
                this.dom.tg = null;
                this.dom.needsChannel = null;
                this.dom.agentName = null;
                this.dom.startTime = null;
                this.dom.splitStartTime = null;
                this.dom.phone = null;
                this.dom.developer = null;
                this.dom.endTime = null;
                this.dom.splitEndTime = null;
                this.dom.statusList=[]
                this.dom.trackDay = null;
                this.dom.needsType = null;
                this.dom.followUpFlag = null;
                // this.dom.times = this.timeDefault();
                this.dom.times = null;
                this.dom.splitTimes = null;
                this.dom.validOrNot = null;
                this.dom.needsSource = null;
                this.getData();
            },
            onChangePage(index) {
                this.dom.current = index;
                this.getData();
            },
            onPageSizeChange(size) {
                this.loading = true;
                this.dom.size = size;
                this.getData();
            },
            exportExcel() {
                this.loadStart();
                this.$postData("orderNeedsTrackExport", JSON.stringify(this.dom), {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tableName: "线索跟踪数据", res: res
                    });
                })
            },
            blobExport({tableName, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tableName + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
                this.loadEnd();
            },

            async showOrderNeedsRemarkDialog(id) {
                this.orderNeedsRemarkList = [];
                await this.getOrderNeedsRemark(id);
            },
            async getOrderNeedsRemark(id) {
                await this.$getData("getOrderNeedsLogById", {id: id}, {}).then(res => {
                    if (res.status === 200) {
                        const list = res.data;
                        if (list == null || list.length === 0) {
                            return this.$message('该线索暂无备注');
                        }
                        this.orderNeedsRemarkList = list;
                        this.showRemarkDialog = true;
                    } else {
                        this.$message.error("失败，" + res.msg);
                    }
                })

            }
        }
    }
</script>

<style scoped>
    /deep/ .el-table__body-wrapper::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }
    /deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 3px;
    }
</style>
