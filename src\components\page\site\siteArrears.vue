<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="6">
          <el-form-item label="会员账号">
            <el-input
                clearable
                @change="query()"
                v-model="dto.name"
                placeholder="会员账号"
                style="width:190px"
                class="handle-input mr10"
            ></el-input>
          </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="子订单号">
              <el-input
                  clearable
                  @change="query()"
                  v-model="dto.billNo"
                  placeholder="子订单号"
                  style="width:190px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="月份">
              <el-date-picker
                  @change="query()"
                  v-model="dto.days"
                  type="daterange"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button type="info"
                       :loading="loadingExcel"
                       @click="download"
                       icon="el-icon-download">导出
            </el-button>
          </el-col>
        </el-row>
      </el-form>
      <h2>订单总欠款:{{totalArrears}}</h2>
      <el-table :data="list" class="table" ref="multipleTable"
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}"
                @selection-change="handleSelectionChange" v-loading="loading">
        <el-table-column
            prop="siteProject.name"
            width="220"
            label="会员账号">
        </el-table-column>
        <el-table-column
            width="150"
            prop="billNo"
            label="子订单号">
        </el-table-column>
        <el-table-column
            width="100"
            prop="order.startTime"
            label="订单时间">
        </el-table-column>
        <el-table-column
            width="80"
            prop="order.realTotalAmount"
            label="合同金额"
        ></el-table-column>
        <el-table-column width="105" prop="order.orderState" label="欠款金额">
          <template slot-scope="scope">
            <el-tag :type="scope.row.order.orderState < '80' ? 'success' : 'danger'"
                    disable-transitions>
              {{ scope.row.order.orderState }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="pageInfo.total"
              @on-change="onChange"
              :current="this.dto.pageNum"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onPageSizeChange"
              :page-size="10"/>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";

export default {
  data() {
    return {
      totalArrears: null,
      loading: true,
      pageSizeOpts: [10, 20, 30],
      list: null,
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      loadingExcel: false,
      dto: {
        billNo:null,
        name:null,
        days: [],
        evaluate:null,
        pageSize: 10,
        pageNum: 1,
        startTime: null,
        endTime: null,
      },
    };
  },
  created() {
    this.dto.endTime=moment(new Date()).format('yyyy-MM-DD')
    this.getData();
  },
  computed: {},
  methods: {
    download() {
      this.loadingExcel = true;
      this.$postData("exportOrderArrears", this.dto, {
        responseType: "arraybuffer"
      }).then(res => {
        this.loadingExcel = false;
        this.blobExport({
          tablename: "合同欠款",
          res: res
        });
      })
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
    query() {
      this.loading = true;
      this.dto.pageNum = 1;
      this.getData();
    },
    getData() {
      if (this.dto.days != null && this.dto.days.length > 0) {
        this.dto.startTime = this.dto.days[0];
        this.dto.endTime = this.dto.days[1]
      }
      this.$postData("siteOrder_selectOrderArrears", this.dto).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.list.forEach(function (e) {
            if(e.order.orderState<80){
              e.order.orderState="欠款"+e.order.realTotalAmount
            }
            e.order.startTime=moment(e.order.startTime).format('YYYY/MM')
          })
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total;
        }
      });
      this.$postData("siteOrder_totalOrderDebt", this.dto).then(res => {
        if (res.status === 200) {
          this.totalArrears=res.data;
        }
      });
    },
    handleSelectionChange(val) {
      console.log(val)
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dto.pageSize = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dto.pageNum = index;
      this.getData();
    },
  },
};
</script>

<style scoped>
.table {
  width: 100%;
  font-size: 13px;
}
</style>
