<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <!--<Steps :current="current">-->
            <!--<Step title="填写违纪信息"></Step>-->
            <!--<Step title="审核阶段"></Step>-->
            <!--<Step title="处理阶段"></Step>-->
            <!--<Step title="已结案"></Step>-->
            <!--</Steps>-->
            <div style="margin-top: 10px;width: 100%">
                <div id="searchDiv">
                    <Form ref="formItem" :model="formItem" :label-width="80" label-position="left"
                          :rules="ruleValidate">
                        <div v-if="current===1">
                            <FormItem label="订单编号" prop="billNo">
                                <Input placeholder="订单编号"
                                       @on-change="getByOrder"
                                       style="width: 80%"
                                       v-model="formItem.billNo">
                                </Input>
                            </FormItem>
                            <FormItem label="服务项目" prop="productName">
                                <Input placeholder="服务项目" style="width: 80%"
                                       v-model="formItem.productName">
                                </Input>
                            </FormItem>
                            <FormItem label="违纪人员" prop="employeeNames">
                                <Row>
                                    <Col span="19">
                                        <Input placeholder="违纪人员" style="width: 100%"
                                               disabled
                                               v-model="formItem.employeeNames">
                                        </Input>
                                    </Col>
                                    <Col span="4">
                                        <Button type="primary" @click="userModal=true">选择</Button>
                                    </Col>
                                </Row>
                            </FormItem>
                          <FormItem label="通报群聊" prop="robotIds">
                            <Row>
                              <Col span="19">
                                <Input placeholder="通报群聊" style="width: 100%"
                                       disabled
                                       v-model="robotNames">
                                </Input>
                              </Col>
                              <Col span="4">
                                <Button type="primary" @click="bitSwarmRobots=true,getSwarmRobots()">选择</Button>
                              </Col>
                            </Row>
                          </FormItem>
                            <FormItem label="违纪类型" prop="type">
                                <Select v-model="formItem.type" style="width:80%">
                                    <Option v-for="item in typeList" :value="item.id">{{ item.text}}</Option>
                                </Select>
                            </FormItem>
                            <FormItem label="备注">
                                <Input v-model="formItem.remark" type="textarea" :rows="3"
                                       style="width: 80%"
                                       placeholder="备注"/>
                            </FormItem>
                        </div>
                        <div v-if="current===2">
                            <FormItem label="核实过程" prop="content">
                                <Input v-model="formItem.content" type="textarea" :rows="3"
                                       style="width: 80%"
                                       placeholder="核实过程"/>
                            </FormItem>
                            <FormItem label="上传材料">
                                <Upload
                                        multiple
                                        :action="imgUrl"
                                        :on-success="onSuccess"
                                        :on-preview="handlePictureCardPreview"
                                        :on-remove="handleRemove"
                                        :before-upload="before"
                                        :on-exceed="onExcees"
                                        :default-file-list="filesList"
                                        :max-size="10240"
                                        :data="files">
                                    <Button icon="ios-cloud-upload-outline">点击上传</Button>
                                </Upload>
                            </FormItem>
                            <FormItem label="违纪判定">
                                <RadioGroup v-model="formItem.violation">
                                    <Radio label="1">有效违纪</Radio>
                                    <Radio label="2">无效违纪</Radio>
                                </RadioGroup>
                            </FormItem>
                        </div>
                        <div v-if="current===3">
                            <FormItem label="处罚等级" prop="violationEm.level">
                                <Select v-model="formItem.violationEm.level" style="width:80%">
                                    <Option v-for="item in levelList" :value="item.id">{{ item.text}}</Option>
                                </Select>
                            </FormItem>
                            <FormItem label="违纪罚金" prop="violationEm.offenceFine">
                                <Input v-model="formItem.violationEm.offenceFine"
                                       style="width: 80%"
                                       @on-blur="offenceBlur"
                                       placeholder="违纪罚金"/>
                            </FormItem>
                            <FormItem label="停工处罚" prop="violationEm.punishment">
                                <Select v-model="formItem.violationEm.punishment" style="width:80%">
                                    <Option v-for="item in periodList" :value="item.id">{{ item.text}}</Option>
                                </Select>
                            </FormItem>
                            <FormItem label="行政扣分">
                                <el-input-number v-model="formItem.violationEm.changeScore"
                                                 @change="handleChange"
                                                 :min="-100" :max="0">
                                </el-input-number>
                            </FormItem>
                            <FormItem label="是否回炉培训">
                                <RadioGroup v-model="formItem.train">
                                    <Radio label="true">是</Radio>
                                    <Radio label="false">否</Radio>
                                </RadioGroup>
                            </FormItem>
                            <FormItem label="推荐案例">
                                <RadioGroup v-model="formItem.case">
                                    <Radio label="true">是</Radio>
                                    <Radio label="false">否</Radio>
                                </RadioGroup>
                            </FormItem>
                        </div>
                        <FormItem>
                            <Button type="info" @click="next1" v-if="current>1">上一步</Button>
                            <Button type="primary" v-if="current>=1&&current<=2" @click="next"
                                    style="margin-left: 3rem">{{formItem.violation==='2'?"完成":"下一步"}}
                            </Button>
                            <Button type="primary" v-if="current===3" @click="next" :loading="buttonLoading"
                                    style="margin-left: 3rem">完成
                            </Button>
                        </FormItem>
                    </Form>
                </div>
            </div>
        </div>
        <Modal v-model="userModal" class="Modal" :width="screenWidth" title="违纪人员"
               :mask-closable="false">
            <div class="addBody">
                <employees-choose v-if="userModal" @init-choose="initChooseProject"
                                  @close-modal="closeCurrModal"></employees-choose>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="bitSwarmRobots" class="Modal" width="35%" title="选择推送机器人"
               :mask-closable="false">
          <el-input
              v-model="swarmRobots.name"
              placeholder="机器人名称"
              style="width:180px"
              class="handle-input mr10"
          ></el-input>
          <el-button type="primary" @click="getSwarmRobots(),swarmRobots.current=1">搜索</el-button>
          <el-button style="margin-left: 25%" type="success" @click="confirmSelect()">确定</el-button>
        <el-table @selection-change="handleSelectionChange" :data="robotList" :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" key="list"
                  ref="multipleTable" stripe v-loading="loading" class="table" border>
          <el-table-column
              type="selection"
              width="55">
          </el-table-column>
          <el-table-column prop="name" label="机器人名称"
                           :show-overflow-tooltip="true" width="550">
          </el-table-column>
        </el-table>
        <div class="pagination">
          <Page :total="pageInfo.total"
                @on-change="onChange"
                :current="this.swarmRobots.current"
                :show-total="true"
                :show-sizer="true"
                :page-size-opts="pageSizeOpts"
                @on-page-size-change="onPageSizeChange"
                :page-size="10"/>
        </div>
          <div slot="footer">
          </div>
        </Modal>
    </div>
</template>


<script>
    import employeesChoose from '@/components/page/wordOrder/choose/employeesChoose.vue'

    export default {
        data() {
            return {
                userModal: false,
              multipleSelection: [],
                imgUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
                current: 1,
                buttonLoading: false,
              pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                bitSwarmRobots: false,
              pageSizeOpts: [10, 20, 30],
                screenWidth: '35%',
                filesList: [],
              robotNames: '',
              loading: true,
              swarmRobots: {
                name: null,
                webhook: null,
                storeId: null,
                size: 10,
                current: 1,
              },
                robotList: [],
                files: {
                    fileName: "Ts/"
                },
                formItem: {
                    id: null,
                    state: 1,
                    stage: 1,
                    billNo: null,
                    productName: null,
                    employeeId: localStorage.getItem('id'),
                    type: null,
                    robotIds: [],
                    file: null,
                    employeeNames: null,
                    remark: null,
                    content: null,
                    violationEm: {
                        employeeNo: null,
                        employeeName: null,
                        changeScore: null,
                        level: null,
                        punishment: null,
                        offenceFine: null,
                    },
                    violation: "1",
                    case: "false",
                    train: "false"
                },
                ruleValidate: {
                    billNo: [
                        {required: true, message: '填写订单编号', trigger: 'blur'}
                    ],
                    productName: [
                        {required: true, message: '填写服务项目', trigger: 'blur'},
                    ],
                    employeeNames: [
                        {required: true, message: '选择违纪人员', trigger: 'change'},
                    ],
                    type: [
                        {required: true, message: '选择违纪类型', trigger: 'change', type: "number"}
                    ],
                    content: [
                        {required: true, message: '填写核实内容', trigger: 'blur'}
                    ],
                    'violationEm.level': [
                        {required: true, message: '选择处罚等级', trigger: 'change', type: "number"}
                    ],
                    'violationEm.offenceFine': [
                        {required: true, message: '填写违纪罚金', trigger: 'change'},
                        {required: true, pattern: /(^[\-0-9][0-9]*(.[0-9]+)?)$/, message: '请输入数字', trigger: 'change'}
                    ],
                    'violationEm.punishment': [
                        {required: true, message: '选择停工处罚', trigger: 'change', type: "number"}
                    ]
                },
                periodList: [],
                levelList: [],
                typeList: []
            }
        },
        components: {
            'employeesChoose': employeesChoose
        },
        created: function () {
            this.getPeriodList();
            this.getLevelList();
            this.getTypeList();
        },
        methods: {
          handleSelectionChange(val) {
            this.multipleSelection = val;
          },
          confirmSelect(){
            if (!this.multipleSelection){
              return  this.$message.error("请至少选择一个要推送的群聊机器人，");
            }
            let robotNames = ''
            let robotIds = ''
            for (let i = 0; i < this.multipleSelection.length; i++) {
              robotNames+= this.multipleSelection[i].name+","
              robotIds+= this.multipleSelection[i].id+","
            }
            this.robotNames = robotNames.substring(0,robotNames.length-1)
            this.formItem.robotIds = robotIds.substring(0,robotIds.length-1)
            this.bitSwarmRobots = false
          },
          // 跳转页码
          onChange(index) {
            this.loading = true;
            this.swarmRobots.current = index;
            this.getSwarmRobots();
          },
          // 页码大小
          onPageSizeChange(size) {
            this.loading = true;
            this.swarmRobots.size = size;
            this.getSwarmRobots();
          },
          getSwarmRobots() {
            this.$postData("getSwarmRobots", this.swarmRobots).then(res => {
              this.loading = false;
              if (res.status === 200) {
                this.robotList = res.data.records;
                this.pageInfo.current = res.data.current;
                this.pageInfo.size = res.data.size;
                this.pageInfo.total = res.data.total
              } else {
                return this.$message.error(res);
              }
            })
          },
            offenceBlur() {
                if (this.formItem.violationEm.offenceFine === '' &&
                    this.formItem.violationEm.offenceFine == null) {
                    return
                }
                if (this.formItem.violationEm.offenceFine.indexOf("-") < 0) {
                    this.formItem.violationEm.offenceFine = "-" + this.formItem.violationEm.offenceFine
                }
            },

            handleChange(value) {
                this.formItem.violationEm.changeScore = value
            },
            getByOrder() {
                this.$getData("getByOrderNo", {
                    orderNo: this.formItem.billNo
                }).then(res => {
                    if (res.status === 200) {
                        if (res.data) {
                            this.formItem.productName = res.data.productName;
                        } else {
                            this.formItem.productName = null
                        }
                    }
                })
            },
            getTypeList() {
                this.$postUrl("get_dict", 130, null, {}).then(res => {
                    if (res.status === 200) {
                        this.typeList = res.data;
                    }
                })
            },
            getPeriodList() {
                this.$postUrl("get_dict", 123, null, {}).then(res => {
                    if (res.status === 200) {
                        this.periodList = res.data;
                    }
                })
            },
            getLevelList() {
                this.$postUrl("get_dict", 118, null, {}).then(res => {
                    if (res.status === 200) {
                        this.levelList = res.data
                    }
                })
            },
            next1() {
                if (this.current === 1) {
                    this.current = 1;
                } else {
                    this.current -= 1;
                }
            },
            next() {
                this.save('formItem');
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.userModal = false;
            },
            initChooseProject(data) {
                let nos = "";
                let name = "";
                let names = "";
                data.forEach((item, i) => {
                    nos += item.no;
                    name += item.realName;
                    names += item.no + item.realName;
                    if (i !== data.length - 1) {
                        nos += ",";
                        name += ",";
                        names += ","
                    }
                });
                this.formItem.violationEm.employeeNo = nos;
                this.formItem.violationEm.employeeName = name;
                this.formItem.employeeNames = names;
                this.closeCurrModal()
            },
            save(name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        let state = this.current;
                        if (state >= this.formItem.state) {
                            this.formItem.state++;
                            this.formItem.stage++;
                        }
                        if (state === 3) {
                            this.buttonLoading = true;
                            this.formItem.state = 3;
                            this.formItem.stage = 3;
                        }
                        if (this.formItem.id != null) {
                            this.update()
                        } else {
                            this.insert()
                        }
                    }
                })
            },
            update() {
                this.$postData("violation_update", this.formItem).then(res => {
                        this.buttonLoading = false;
                        if (res.status === 200) {
                            this.formItem.violationEm = res.data.violationEm;
                            if (this.current === 3 || this.formItem.violation === "2") {
                                this.chooseThisModel()
                            } else {
                                this.current += 1;
                            }
                        } else {
                            this.$message.error("添加失败，" + res.meta.msg);
                        }
                    }
                )
            },
            insert() {
              console.log(this.formItem)
                // this.$postData("violation_insert", this.formItem).then(res => {
                //     if (res.status === 200) {
                //         this.current += 1;
                //         this.formItem.id = res.data.id
                //     } else {
                //         this.$message.error("添加失败，" + res.meta.msg);
                //     }
                // })
            }
            ,
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            }
            ,
            onSuccess(res) {
               ;
                if (res.status === 200) {
                    this.$Message.success('上传成功');
                    if (this.formItem.file) {
                        this.formItem.filee = this.formItem.file + "," + res.data;
                    } else {
                        this.formItem.file = res.data;
                    }
                    let url = decodeURIComponent(res.data);
                    let file = {
                        name: url.substring(url.lastIndexOf("/") + 1, url.length),
                        url: url
                    };
                    this.filesList.push(file);
                }
            },
            handlePictureCardPreview(file) {
                console.log(file);
                this.download(file.url, file.name);
            }
            ,
            onExcees() {
                this.$Message.success('只可上传一个文件');
            }
            ,
            download(src, fileName) {
                let x = new XMLHttpRequest();
                x.open("GET", src, true);
                x.responseType = 'blob';
                x.onload = function (e) {
                    let url = window.URL.createObjectURL(x.response);
                    let a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;
                    a.click();
                };
                x.send();
            }
            ,
            handleRemove(file) {
                console.log(file);
                let names = this.formItem.file.split(",");
                for (let i = 0; i < names.length; i++) {
                    if (file.url === decodeURIComponent(names[i])) {
                        names.splice(i, 1);
                        this.formItem.file = names.join(",");
                        break;
                    }
                }
                for (let i = 0; i < this.filesList.length; i++) {
                    if (file.url === decodeURIComponent(this.filesList[i].url)) {
                        this.filesList.splice(i, 1);
                        break;
                    }
                }
            }
            ,
            before() {
                if (this.formItem.file) {
                    let list = this.formItem.file.split(",");
                    const check = list.length < 2;
                    if (!check) {
                        this.$Notice.warning({
                            title: '最多可上传2个附件,多文件请打包上传'
                        });
                    }
                    return check;
                }
            }
        },


    }
</script>


<style scoped>


    /* 查询div*/
    #searchDiv {
        width: 80%;
        margin: 0 auto;
        font-weight: bold;
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }
</style>

