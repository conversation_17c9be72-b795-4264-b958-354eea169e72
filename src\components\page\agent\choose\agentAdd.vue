<template style="background-color: #000">

            <el-tabs type="card" :stretch="true" >
                <el-tab-pane label="基本信息">
                <Row>
                <Col span="24">

                        <el-form ref="dom" :model="dom" :rules="ruleValidate">
                            <Row>
                            <Col span="6" style="padding: 50px">

                                <div style="padding-left: 30%;padding-bottom: 10px"><h3>员工头像</h3></div>

                                <el-dialog :visible.sync="dialogImg">
                                    <img width="100%" :src="dom.headPortrait" alt="" >
                                </el-dialog>
                                <el-image
                                        @click="dialogImg=true"
                                        style="width: 100%; height: 200px;min-width: 150px;"
                                        :src="dom.headPortrait"
                                >
                                </el-image>
                                <el-upload
                                        class="upload-demo"
                                        action="https://biapi.xiaoyujia.com/files/uploadFiles"
                                        list-type="picture"
                                        :on-success="handleAvatarSuccess">
                                    <el-button size="small" type="primary">选择头像</el-button>
                                </el-upload>

                            </Col>
                            <Col span="8">

                                    <el-form-item   prop="no">
                                        <div class="label-name">编号:</div>
                                        <el-input placeholder="请输入编号" v-model="dom.no" style="width: 70%;">
                                        </el-input>
                                    </el-form-item >

                                    <el-form-item   prop="realName" >
                                        <div class="label-name">名称:</div>
                                        <el-input placeholder="请输入名称" v-model="dom.realName" style="width: 70%;">
                                        </el-input>
                                    </el-form-item >

                                    <el-form-item   prop="address">
                                        <div class="label-name">地址:</div>
                                        <el-input placeholder="请输入地址" v-model="dom.address" style="width: 70%;">
                                        </el-input>
                                    </el-form-item >

                                    <el-form-item   prop="phone" >
                                        <div class="label-name">手机:</div>
                                        <el-input placeholder="请输入联系方式" v-model="dom.phone" style="width: 70%;">
                                        </el-input>
                                    </el-form-item >


                            </Col>
                                <Col span="8">
                                    <el-form-item   prop="entryTime">
                                        <div class="label-name">入职时间:</div>
                                        <el-date-picker
                                                style="width: 70%;"
                                                v-model="dom.entryTime"
                                                type="datetime"
                                                value-format="yyyy-MM-dd HH:mm:ss"
                                                placeholder="入职时间">
                                        </el-date-picker>
                                    </el-form-item >
                                    <el-form-item   prop="state">
                                        <div class="label-name">员工状态:</div>
                                        <el-select v-model="dom.state" clearable placeholder="请选择"   style="width: 70%;">
                                            <el-option

                                                    v-for="item in states"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item >
                                    <el-form-item  >
                                        <div class="label-name">城市区域:</div>
                                        <city-choose @init-choose="initChooseProject" :model="dom"></city-choose>
                                    </el-form-item>
                                </Col>

                            </Row>

                                    <div style="margin-right: 100px;float: right">
                                        <Button type="primary" @click="save('dom')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </div>
                                </el-form>
                    </Col>


            </Row>
                </el-tab-pane>

            </el-tabs>

</template>


<script>
    import cityChoose from '@/components/page/agent/minichoose/cityChoose.vue'
    export default {

        data() {
            return {
                states:[{
                    value:null,
                    label: '全部'
                },{
                    value:1,
                    label: '上架'
                }, {
                    value: 2,
                    label: '下架'
                }, {
                    value: 3,
                    label: '离职'
                },],
                dialogImg:false,
                dom:{
                    state:null,
                    cityId:null,
                    areaId:null,
                    headPortrait:null,
                    no:null,
                    realName:null,
                    address:null,
                    phone:null,
                    entryTime:null,
                    storeId:Number(localStorage.getItem("storeId")) ,

                },
                formItem: {
                    No: null,
                    RealName: null,
                    Phone: null,
                    Address:null,
                },

                ruleValidate: {
                    no: [
                        {required: true, message: '请输入编号', trigger: 'blur'}
                    ],
                    realName: [
                        {required: true, message: '请选择名称', trigger: 'blur'}
                    ],
                    phone: [
                        {required: true, message: '请选择联系方式', trigger: 'blur'},
                        { min: 11, max: 11, message: '请输入11位手机号', trigger: 'blur' }
                    ],
                    address: [
                        {required: true, message: '请输入地址', trigger: 'blur'}
                    ],

                },
            }
        },
        components: {
            'cityChoose': cityChoose,
        },
        created: function () {
            console.log(this.dom)
        },
        methods: {
            handleAvatarSuccess(res, file) {
                this.dom.headPortrait = res.data;
            },
            /*
            城市选择
             */
            initChooseProject(city) {
                this.dom.cityId=city.cityId
                this.dom.areaId=city.areaId
                console.log(this.dom)
            },

            save(name) {
                this.$refs[name].validate(valid => {
                    if (valid) {
                        this.$postData("add_agent", this.dom, {}).then(res => {

                            if (res.status == 200) {
                                this.$Message.success('修改成功');
                                this.chooseThisModel();
                            } else {
                                this.$Message.error("工号已存在，" );
                            }
                        },error=>{
                            this.$Message.error("工号已存在，" );
                        })


                    } else {
                        console.log("error submit!!");
                        return false;
                    }
                });



            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.dom=null;
                this.$emit('init-choose', "");
            },

        },

    }
</script>
<style>
    {
        background: #409eff;
        color: #fff;
    }
</style>

<style scoped>
    .label-name{
        float: left;
        text-align: center;
        width: 20%;
    }
    .startWord{
        background-color: #F5F7FA;
        color: #909399;
        vertical-align: middle;

        position: relative;
        border: 1px solid #DCDFE6;
        border-radius: 4px;
        padding: 0 20px;
        width: 1px;
        white-space: nowrap;
        float: left;
        padding-right: 50px;
    }
    .ivu-col-span-9{
        padding-top: 30px;
    }
    .el-input-group{
        width: 80%;
    }
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;

    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

