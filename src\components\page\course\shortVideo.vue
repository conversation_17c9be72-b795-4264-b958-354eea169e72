<template>
	<div class="container">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">短视频列表</el-menu-item>
			<el-menu-item index="1">短视频分组</el-menu-item>
		</el-menu>

		<!-- 搜索栏目 -->
		<div v-if="choiceIndexNow==0">
			<div class="handle-box" style="margin: 20px 20px;">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="短视频标题、内容"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="短视频类型" style="margin-right: 20px">
								<el-select v-model="searchType" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="短视频状态" style="margin-right: 20px">
								<el-select v-model="searchState" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(0,0)">添加
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">

				<el-table-column prop="id" width="60" label="编号">
				</el-table-column>

				<el-table-column width="140" prop="videoTitle" label="短视频标题">
				</el-table-column>

				<el-table-column width="140" prop="videoContent" label="短视频内容">
				</el-table-column>

				<el-table-column width="140" prop="videoKeyWords" label="关键词" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="scope.row.videoKeyWords||scope.row.isEdit">
							<ul v-for="(item,index) in keyList" :key="index">
								<li class="tags-li" v-for="(item1,index1) in scope.row.keyListArray" :key="index1"
									v-if="item.text==item1">
									<span>
										{{ item.text }}
									</span>
								</li>
							</ul>
						</div>

						<div v-if="!scope.row.videoKeyWords&&!scope.row.isEdit">
							<span>暂未设置</span>
						</div>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="videoType" label="短视频类型" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-tag v-if="item.id == scope.row.videoType" v-for="(item,index) in typeList" :key="index"
							:type="typeStyleList[index%4].type">{{item.typeName}}</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="120" prop="videoContent" label="短视频链接">
					<template slot-scope="scope">
						<el-button @click="openVideo(scope.row.videoUrl)" type="primary" size="small"
							:disabled="!scope.row.videoUrl" icon="el-icon-zoom-in">预览
						</el-button>
					</template>
				</el-table-column>

				<el-table-column width="125" prop="videoImg" label="短视频封面" :render-header="renderHeader">
					<template slot-scope="scope">
						<img :src="scope.row.videoImg||blankImg" style="width: 100px;height: 100px;"
							@click="openImg(scope.row.videoImg||blankImg)">
					</template>
				</el-table-column>


				<el-table-column prop="videoDuration" label="短视频时长" width="120" sortable>
					<template slot-scope="scope">
						<span>{{formatDuration(scope.row.videoDuration)}}</span>
					</template>
				</el-table-column>

				<el-table-column width="180" label="查看权限" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="scope.row.authList!='0'">
							<ul v-for="(item,index) in authList" :key="index">
								<li class="tags-li" v-for="(item1,index1) in scope.row.authListArray" :key="index1"
									v-if="item.id==parseInt(item1)">
									<span>
										{{ item.name }}
									</span>
								</li>
							</ul>
						</div>

						<div v-if="scope.row.authList==0">
							<span>暂未设置</span>
						</div>
					</template>
				</el-table-column>

				<el-table-column width="122" label="关联课程" prop="courseId">
					<template slot-scope="scope">
						<span v-if="scope.row.courseId==item.id" v-for="(item,index) in courseList" :key="index">
							{{item.courseTitle}}
						</span>
						<span v-if="!scope.row.courseId">
							暂未设置
						</span>
					</template>
				</el-table-column>

				<el-table-column width="122" label="关联导师" prop="teacherId">
					<template slot-scope="scope">
						<span v-if="scope.row.teacherId==item.id" v-for="(item,index) in teacherList" :key="index">
							{{item.realName}}
						</span>
						<span v-if="!scope.row.teacherId">
							暂未设置
						</span>
					</template>
				</el-table-column>

				<el-table-column width="102" label="状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.videoState == 0" type="info">下架</el-tag>
						<el-tag v-if="scope.row.videoState == 1" type="success">上架</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creator" label="创建人">
					<template slot-scope="scope">
						<span v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
						<span v-else>{{scope.row.creator||''}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="200" label="操作">
					<template slot-scope="scope">
						<el-button @click="openModal(1,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<div v-if="choiceIndexNow==1">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="分组标题、内容"></el-input>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="分组状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>
							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>
							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(2,0)">添加
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands">

				<el-table-column prop="id" width="60" label="编号">
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="groupName" label="分组标题">
				</el-table-column>

				<el-table-column width="140" prop="groupContent" label="分组内容">
				</el-table-column>

				<el-table-column width="125" prop="groupImg" label="分组图片" :render-header="renderHeader">
					<template slot-scope="scope">
						<img :src="scope.row.groupImg||blankImg" style="width: 100px;height: 100px;"
							@click="openImg(scope.row.groupImg||blankImg)">
					</template>
				</el-table-column>

				<el-table-column width="125" prop="groupIcon" label="分组图标" :render-header="renderHeader">
					<template slot-scope="scope">
						<img :src="scope.row.groupIcon||blankImg" style="width: 100px;height: 100px;"
							@click="openImg(scope.row.groupIcon||blankImg)">
					</template>
				</el-table-column>

				<el-table-column width="180" label="分组查看权限" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="scope.row.authList!=0">
							<ul v-for="(item,index) in authList" :key="index">
								<li class="tags-li" v-for="(item1,index1) in scope.row.authListArray" :key="index1"
									v-if="item.id==parseInt(item1)">
									<span>
										{{ item.name }}
									</span>
								</li>
							</ul>
						</div>

						<div v-if="scope.row.authList==0">
							<span>暂未设置</span>
						</div>
					</template>
				</el-table-column>

				<el-table-column width="102" label="状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.groupState == 0" type="info">下架</el-tag>
						<el-tag v-if="scope.row.groupState == 1" type="success">上架</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creator" label="创建人">
					<template slot-scope="scope">
						<span v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
						<span v-else>{{scope.row.creator||''}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="200" label="操作">
					<template slot-scope="scope">
						<el-button @click="openModal(3,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--添加短视频-->
		<el-drawer size="60%" :with-header="false" :visible.sync="shortVideoModal" direction="rtl">
			<div v-if="choiceItem">
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">短视频信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 短视频标题：">
								<el-input v-model="choiceItem.videoTitle" type="textarea" class="handle-input mr10"
									placeholder="请输入短视频标题" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="* 短视频内容：">
								<el-input v-model="choiceItem.videoContent" type="textarea" class="handle-input mr10"
									placeholder="请输入短视频内容描述" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="* 短视频链接：">
								<el-input v-model="choiceItem.videoUrl" type="textarea" class="handle-input mr10"
									placeholder="请输入短视频链接" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item>
								<el-button @click="openVideo(choiceItem.videoUrl)" type="primary" size="small"
									:disabled="!choiceItem.videoUrl" icon="el-icon-zoom-in">预览
								</el-button>
								<el-upload :action="fileUploadUrl" list-type="picture" :before-upload="beforeFileUpload"
									:on-success="fileUploadSuccess" :on-error="fileUploadError" :show-file-list="false"
									:data="fileUploadData" class="upload-demo inline-block">
									<el-button @click="fileUpload(0)" type="warning" size="small"
										style="margin-left: 10px" icon="el-icon-upload2">普通上传</el-button>
								</el-upload>
								<el-button @click="openModal(4,0)" type="info" size="small" style="margin-left: 10px"
									icon="el-icon-upload2">阿里云点播</el-button>
								<el-tooltip class="item" effect="dark" content="推荐上传到阿里云点播控制台，再将链接复制到这，以获得更流畅的视频播放体验"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="短视频封面：">
								<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
									list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="choiceItem.videoImg||blankImg" style="width: 150px;height: 150px;"
										@click="openImgUpload(0,0)">
								</el-upload>
							</el-form-item>

							<el-form-item label="关联课程：">
								<el-select v-model="choiceItem.courseId" placeholder="请选择" filterable>
									<el-option v-for="(item,index) in courseList" :key="index"
										:label="item.id+'：'+item.courseTitle" :value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="在短视频下方显示相关课程，并可在详情页通过点击卡片查看课程详情"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="关联导师：">
								<el-select v-model="choiceItem.teacherId" placeholder="请选择" filterable>
									<el-option v-for="(item,index) in teacherList" :key="index"
										:label="item.id+'：'+item.realName" :value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="在短视频下方显示导师课程报名数，并可在详情页通过点击头像查看导师详情"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="关键词：" v-if="detailType==1&&choiceIndex==0">
								<div v-if="choiceItem.keyListArray.length">
									<ul v-for="(item,index) in keyList" :key="index">
										<li class="tags-li" v-for="(item1,index1) in choiceItem.keyListArray"
											:key="index1" v-if="item.text==item1">
											<span>
												{{ item.text }}
											</span>
											<span class="tags-li-icon" @click="closeKeyTags(index1)"><i
													class="el-icon-close"></i></span>
										</li>
									</ul>

								</div>
								<div v-if="!choiceItem.keyListArray.length">
									<span>暂未设置</span>
								</div>
								<el-button type="primary" @click="keyListModal=true" plain
									icon="el-icon-plus">添加标签</el-button>
							</el-form-item>

							<el-form-item label="查看权限：" v-if="detailType==1">
								<template slot-scope="scope">
									<div v-if="choiceItem.authListArray.length">
										<ul v-for=" (item,index) in authList" :key="index">
											<li class="tags-li" v-for="(item1,index1) in choiceItem.authListArray"
												:key="index1" v-if="item.id==parseInt(item1)">
												<span>
													{{ item.name }}
												</span>
												<span class="tags-li-icon" @click="closeAuthTags(index1)"><i
														class="el-icon-close"></i></span>
											</li>
										</ul>

									</div>
									<div v-if="!choiceItem.authListArray.length">
										<span>暂未设置</span>
									</div>
									<el-button type="primary" @click="authIdListModal=true" plain
										icon="el-icon-plus">添加角色</el-button>
								</template>
							</el-form-item>
						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="短视频类型：">
								<el-select v-model="choiceItem.videoType" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="可根据类型进行分类" placement="top-start">
									<i class="el-icon-group" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="* 短视频分组：">
								<el-select v-model="choiceItem.courseGroupId" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in groupList" :key="index" :label="item.groupName"
										:value="item.id"></el-option>
								</el-select>
								<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(2,0)"
									style="margin-left: 20px">添加
								</el-button>
							</el-form-item>

							<el-form-item label="短视频时长：" v-if="detailType==1">
								<!-- 	<el-input v-model="choiceItem.videoDuration" class="handle-input mr10"
									placeholder="时长/秒(保存后将自动转换格式)"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input> 秒 -->
								<span>{{formatDuration(choiceItem.videoDuration)}}</span>
								<el-tooltip class="item" effect="dark" content="无需手动填写，在保存短视频链接后将自动计算时长"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="短视频状态：">
								<el-select v-model="choiceItem.videoState" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>

						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="shortVideoModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="insertShortVideo()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateShortVideo(choiceItem)" type="success" size="small" v-if="detailType==1">保存
					</el-button>
				</div>

			</div>
		</el-drawer>

		<!--添加短视频分组-->
		<el-drawer size="60%" :with-header="false" :visible.sync="shortVideoGroupModal" direction="rtl">
			<div v-if="choiceItem">
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">短视频分组信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 分组标题：">
								<el-input v-model="choiceItem.groupName" type="textarea" class="handle-input mr10"
									placeholder="请输入分组标题">
								</el-input>
							</el-form-item>

							<el-form-item label="* 分组内容：">
								<el-input v-model="choiceItem.groupContent" type="textarea" class="handle-input mr10"
									placeholder="请输入分组内容描述">
								</el-input>
							</el-form-item>

							<el-form-item label="分组封面：">
								<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
									list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="choiceItem.groupImg||blankImg" style="width: 150px;height: 150px;"
										@click="openImgUpload(1,0)">
								</el-upload>
							</el-form-item>

							<el-form-item label="分组图标：">
								<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
									list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="choiceItem.groupIcon||blankImg" style="width: 150px;height: 150px;"
										@click="openImgUpload(2,0)">
								</el-upload>
							</el-form-item>
						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="查看权限：" v-if="detailType==1">
								<div v-if="choiceItem.authListArray.length">
									<ul v-for=" (item,index) in authList" :key="index">
										<li class="tags-li" v-for="(item1,index1) in choiceItem.authListArray"
											:key="index1" v-if="item.id==parseInt(item1)">
											<span>
												{{ item.name }}
											</span>
											<span class="tags-li-icon" @click="closeAuthTags(index1)"><i
													class="el-icon-close"></i></span>
										</li>
									</ul>

								</div>
								<div v-if="!choiceItem.authListArray.length">
									<span>暂未设置</span>
								</div>
								<el-button type="primary" @click="authIdListModal=true" plain
									icon="el-icon-plus">添加角色</el-button>
							</el-form-item>

							<el-form-item label="分组状态：" v-if="detailType==1">
								<el-select v-model="choiceItem.groupState" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="shortVideoGroupModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="insertShortVideoGroup()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateShortVideoGroup(choiceItem)" type="success" size="small"
						v-if="detailType==1">保存
					</el-button>
				</div>

			</div>
		</el-drawer>

		<!-- 添加标签 -->
		<el-dialog :visible.sync="keyListModal" width="60%" title="添加短视频关键词标签" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;" v-if="choiceIndex==0&&choiceItem">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-checkbox-group v-model="choiceItem.keyListArray">
						<el-checkbox-button v-for="(val,index) in keyList" :key="index" :label="val.text">{{ val.text }}
						</el-checkbox-button>
					</el-checkbox-group>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="keyListModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="keyListModal=false" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

		<!-- 添加查看权限 -->
		<el-dialog :visible.sync="authIdListModal" width="60%" title="添加查看权限" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;"
				v-if="(choiceIndex==0||choiceIndex==1)&&choiceItem">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-checkbox-group v-model="choiceItem.authListArray" value-key="id">
						<el-checkbox-button v-for="(val,index) in authList" :key="index" :label="val.id">{{ val.name }}
						</el-checkbox-button>
					</el-checkbox-group>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="authIdListModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="authIdListModal=false" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--视频预览-->
		<el-dialog :visible.sync="videoModal" width="40%" title="视频预览" :mask-closable="false">
			<video style="width: 100%;height: auto;margin: 0 auto;" :controls="true" :enable-progress-gesture="true"
				:initial-time="0" :show-center-play-btn="true" autoplay :src="videoUrl" custom-cache="false">
			</video>
		</el-dialog>

	</div>

</template>

<script>
	import Vue from 'vue';

	export default {
		name: "shortVideo",

		data() {
			return {
				// 可设置
				// 是否超级管理员（可执行删除等敏感操作）
				isAdmin: false,

				url: '',
				imageUrl: '',
				videoUrl: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				// 文件上传地址
				fileUploadUrl: 'https://api.xiaoyujia.com/system/uploadFile',
				fileUploadData: {
					route: 'shortVideo'
				},
				uploadFileType: 0,

				// baseUrl: 'http://**************:9999/',
				baseUrl: 'https://api.xiaoyujia.com/',
				uploadUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
				deleteTips: "确定删除选中短视频吗？该操作无法恢复，请谨慎操作!",
				isEdit: false,
				showEdit: true,
				imgModal: false,
				videoModal: false,
				shortVideoModal: false,
				shortVideoGroupModal: false,
				groupIdListModal: false,
				shortVideoRecordModal: false,
				keyListModal: false,
				authIdListModal: false,
				detailType: 0,
				loading: true,
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				pageSizeOpts: [20, 50, 100],
				list: [],
				courseList: [],
				teacherList: [],
				liveRoomList: [],
				groupList: [],
				shortVideoRecordList: [],
				shortVideoAnswerRecordList: [],
				keyList: [],
				choiceIndex: '0',
				choiceIndexNow: '0',
				choiceItemIndex: 0,
				choiceItem: null,
				choiceRecordId: 0,
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				signInTips: '',
				searchText: '',
				searchType: null,
				searchState: null,
				searchRequired: null,
				quer: {
					search: "",
					groupIdList: null,
					videoState: null,
					typeState: null,
					orderBy: "t.id ASC",
					current: 1,
					size: 20
				},
				switchStateList: [{
						id: 0,
						typeName: "关闭",
						type: "info"
					},
					{
						id: 1,
						typeName: "开启",
						type: "success"
					}
				],
				typeList: [{
					id: 0,
					typeName: "家姐课堂"
				}, {
					id: 1,
					typeName: "家姐联盟"
				}],
				stateList: [{
					value: 0,
					text: "下架",
					type: "info"
				}, {
					value: 1,
					text: "上架",
					type: "success"
				}],
				typeStyleList: [{
						id: 0,
						type: "success"
					},
					{
						id: 1,
						type: "info"
					},
					{
						id: 2,
						type: "warning"
					}, {
						id: 3,
						type: "primary"
					}
				],
				authList: [{
					id: 1,
					name: '普通会员'
				}, {
					id: 2,
					name: '行政员工'
				}, {
					id: 3,
					name: '服务员工'
				}, {
					id: 4,
					name: '保姆月嫂'
				}, {
					id: 5,
					name: '共创店'
				}, {
					id: 6,
					name: '合伙人'
				}, {
					id: 7,
					name: '门店店长'
				}, {
					id: 8,
					name: '陪跑店店长'
				}],
				searchTime: [],
				pickerOptions: {
					shortcuts: [{
						text: '昨天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '今天',
						onClick(picker) {
							picker.$emit('pick', new Date());
						}
					}, {
						text: '明天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周前',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周后',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}]
				},
				pickerOptions1: {
					shortcuts: [{
						text: '今天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '昨天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}]
				}
			}
		},
		created() {
			if (this.$route.query.isAdmin) {
				this.isAdmin = true
			}
			this.getData()
		},
		methods: {
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceItem = null
				this.choiceIndex = key
				this.expands = []
				this.showEdit = true
				this.reset()
			},
			getData() {
				this.choiceIndexNow = 2
				if (this.choiceIndex == 0) {
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'course/pageShortVideo',
						data: JSON.stringify(this.quer),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							this.loading = false
							if (res.code == 0) {
								this.list = res.data.records
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
								this.choiceIndexNow = this.choiceIndex
								for (let item of this.list) {
									// 追加视频标签
									let videoKeyWords = item.videoKeyWords || ''
									this.$set(item, "keyListArray", videoKeyWords.split(','))

									// 追加视频查看权限
									this.$set(item, "authListArray", this.strToArray(item.authList))
								}
							} else {
								this.list = []
								this.$message.error('未查询到相关短视频!')
								this.choiceIndexNow = this.choiceIndex
							}
						},
					})
					this.listShortVideoGroup()
					this.listCourse()
					this.listCourseTeacher()
				} else if (this.choiceIndex == 1) {
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'course/pageShortVideoGroup',
						data: JSON.stringify(this.quer),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							this.loading = false
							if (res.code == 0) {
								this.list = res.data.records
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
								this.choiceIndexNow = this.choiceIndex
								for (let item of this.list) {
									this.$set(item, "authListArray", this.strToArray(item.authList))
								}
							} else {
								this.list = []
								this.$message.error('未查询到相关分组!')
								this.choiceIndexNow = this.choiceIndex
							}
						},
					})
				}
				this.getDict()
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.quer.videoState = this.searchState
				this.quer.groupState = this.searchState
				this.quer.videoType = this.searchType
				this.quer.required = this.searchRequired
				this.getData()
			},
			// 重置
			reset() {
				this.loading = true
				this.quer.search = ""
				this.quer.videoState = null
				this.quer.videoType = null
				this.quer.groupState = null
				this.quer.required = null

				this.quer.orderBy = "t.id ASC"
				this.quer.current = 1

				this.searchText = ''
				this.searchState = null
				this.searchType = null
				this.searchRequired = null
				this.isEdit = false
				this.getData()
			},
			// 获取分组
			listShortVideoGroup() {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'course/listShortVideoGroup',
					data: JSON.stringify({
						groupState: 1
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.groupList = res.data
						} else {
							this.groupList = []
						}
					},
				})
			},
			// 获取课程列表
			listCourse() {
				this.$postData("listCourse", {}).then(res => {
					if (res.status == 200) {
						this.courseList = res.data
					}
				})
			},
			// 获取导师列表
			listCourseTeacher() {
				$.ajax({
					type: "POST",
					url: 'https://api.xiaoyujia.com/course/listCourseTeacher',
					data: JSON.stringify({
						state: null
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.teacherList = res.data
						}
					},
				})
			},
			getDict() {
				this.$postUrl("get_dict", 250, null, {}).then(res => {
					if (res.status == 200) {
						this.keyList = res.data
					} else {
						this.keyList = []
					}
				})
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
						this.choiceRecordId = row.id
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "分组图片") {
					tips = "推荐尺寸：250*250"
				} else if (column.label == "分组图标") {
					tips = "推荐尺寸：250*250"
				} else if (column.label == "短视频类型") {
					tips = "可根据类型进行分类"
				} else if (column.label == "关键词") {
					tips = "短视频话题"
				} else if (column.label == "查看权限") {
					tips = "设置哪类人员可以查看该短视频，未设置则所有人可见"
				} else if (column.label == "短视频封面") {
					tips = "作为静态展示的素材"
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			// 将字符串转化为数组
			strToArray(val) {
				let arrayString = []
				let array = []
				arrayString = val.split(',')
				arrayString.forEach(it => {
					let value = parseInt(it)
					if (value != null && value != 0) {
						array.push(value)
					}
				})
				return array
			},
			formatDuration(duration, defaultStr) {
				if (!defaultStr) {
					defaultStr = '暂未录入'
				}
				let result = ""
				let sencond = 0
				let min = 0
				let hour = 0
				let day = 0
				// 小于一分钟
				if (duration <= 60) {
					sencond = Math.floor(parseFloat(duration / 1.0))
					if (sencond == 0) {
						result = defaultStr
					} else {
						result = sencond + "秒"
					}
				}

				// 小于二小时
				if (duration > 60 && duration <= 3600) {
					min = Math.floor(parseFloat(duration / 60.0))
					sencond = Math.floor(duration - (min * 60)).toFixed(0)
					if (min == 0) {
						result = defaultStr
					} else {
						result = min + "分钟" + sencond + "秒"
					}
				}
				// 大于二小时 小于一天
				if (duration > 3600 && duration <= 86400) {
					hour = Math.floor(parseFloat(duration / 3600))
					min = parseFloat((duration - (hour * 3600)) / 60).toFixed(0)
					if (min == 0) {
						result = hour + "小时"
					} else {
						result = hour + "小时" + min + "分钟"
					}
				}
				// 大于一天
				else if (duration > 86400) {
					day = Math.floor(parseFloat(duration / 86400))
					hour = parseFloat((duration - (day * 86400)) / 3600).toFixed(0)
					if (hour == 0) {
						result = day + "天"
					} else {
						result = day + "天" + hour + "小时"
					}
				}
				return result
			},
			formatTypeStyle(type) {
				return this.typeStyleList[type % 4].type
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 短视频Excel模板下载
			excelDownload() {
				this.$postData("excelDownload", null, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('短视频Excel模板下载成功!')
					this.blobExport({
						tablename: "短视频Excel模板",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.$set(this.choiceItem, "videoImg", res.data)
				} else if (this.uploadImgType == 1) {
					this.$set(this.choiceItem, "groupImg", res.data)
				} else if (this.uploadImgType == 2) {
					this.$set(this.choiceItem, "groupIcon", res.data)
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadId = index
				let tips = ''
				if (this.uploadImgType == 0) {
					tips = "推荐尺寸：750*1000（3：4）"
				} else if (this.uploadImgType == 1) {
					tips = '推荐上传尺寸：250*250（1：1）'
				} else if (this.uploadImgType == 2) {
					tips = '推荐上传尺寸：250*250（1：1）'
				}
				this.$message.info(tips)
			},
			// 打开图片预览
			openImg(url) {
				this.imageUrl = url || this.blankImg
				this.imgModal = true
			},
			// 打开视频预览
			openVideo(url) {
				if (url != null && url != '') {
					this.videoUrl = url
				}
				this.videoModal = true
			},
			// 将字符串转化为数组
			strToArray(val) {
				let arrayString = []
				let array = []
				arrayString = val.split(',')
				arrayString.forEach(it => {
					let value = parseInt(it)
					if (value != null && value != 0) {
						array.push(value)
					}
				})
				return array
			},
			openModal(index, index1) {
				if (index == 0) {
					this.detailType = 0
					this.choiceItem = {}
					this.choiceItem.authListArray = []
					this.choiceItem.keyListArray = []
					this.shortVideoModal = true
				} else if (index == 1) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.shortVideoModal = true
				} else if (index == 2) {
					this.detailType = 0
					this.choiceItem = {}
					this.choiceItem.authListArray = []
					this.shortVideoGroupModal = true
				} else if (index == 3) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.shortVideoGroupModal = true
				} else if (index == 4) {
					window.open(
						'https://vod.console.aliyun.com/?spm=5176.12672711.categories-n-products.dvod.8f791fa37JVfKI#/media/video/list'
					)
				}
			},
			//打开员工信息详情
			rowClick(id) {

			},
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			// 添加课程权限列表
			addAuthIdList() {
				this.authIdListModal = false
			},
			// 删除短视频权限标签
			closeAuthTags(index, index1) {
				this.choiceItem.authListArray.splice(index1, 1)
			},
			// 添加短视频
			insertShortVideo() {
				let shortVideo = this.choiceItem
				if (!shortVideo.videoTitle) {
					this.$message.error('请填写短视频标题！')
				} else if (!shortVideo.videoContent) {
					this.$message.error('请填写短视频内容！')
				} else if (!shortVideo.videoUrl) {
					this.$message.error('请填写短视频链接！')
				} else {
					let no = localStorage.getItem('account') || 'admin'
					this.$set(shortVideo, "creator", no)

					// 格式化权限角色列表
					shortVideo.authList = shortVideo.authListArray.join(',')
					if (shortVideo.authList.length == 0) {
						shortVideo.authList = '0'
					}
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'course/insertShortVideo',
						data: JSON.stringify(shortVideo),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.code == 0) {
								this.$message.success('短视频添加成功!')
								this.shortVideoModal = false
								this.list.push(res.data)
							} else {
								this.$message.error('短视频添加失败！' + res.msg)
							}
						},
					})
				}
			},
			// 短视频分组
			insertShortVideoGroup() {
				let shortVideoGroup = this.choiceItem
				if (!shortVideoGroup.groupName) {
					this.$message.error('请填写分组标题！')
				} else if (!shortVideoGroup.groupContent) {
					this.$message.error('请填写分组内容！')
				} else {
					let no = localStorage.getItem('account') || 'admin'
					this.$set(shortVideoGroup, "creator", no)
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'course/insertShortVideoGroup',
						data: JSON.stringify(shortVideoGroup),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.code == 0) {
								this.$message.success('短视频分组添加成功!')
								this.shortVideoGroupModal = false
								this.list.push(res.data)
							} else {
								this.$message.error('短视频分组添加失败！' + res.msg)
							}
						},
					})
				}
			},
			// 更改短视频
			updateShortVideo(val) {
				// 格式化课程标签
				if (val.keyListArray.length != 0) {
					val.videoKeyWords = val.keyListArray.join(',')
					if (val.videoKeyWords.length == 0) {
						val.videoKeyWords = ''
					}
				}

				// 格式化权限角色列表
				val.authList = val.authListArray.join(',')
				if (val.authList.length == 0) {
					val.authList = '0'
				}
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'course/updateShortVideo',
					data: JSON.stringify(val),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.$message.success('短视频更新成功!')
							this.list[this.choiceItemIndex] = this.choiceItem
						} else {
							this.$message.error('短视频更新失败！' + res.msg)
						}
					},
				})
			},
			// 更改短视频分组
			updateShortVideoGroup(val) {
				// 格式化权限角色列表
				val.authList = val.authListArray.join(',')
				if (val.authList.length == 0) {
					val.authList = '0'
				}

				$.ajax({
					type: "POST",
					url: this.baseUrl + 'course/updateShortVideoGroup',
					data: JSON.stringify(val),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.$message.success('短视频分组更新成功!')
							this.list[this.choiceItemIndex] = this.choiceItem
						} else {
							this.$message.error('短视频分组更新失败！' + res.msg)
						}
					},
				})
			},
			beforeFileUpload() {
				this.loading = true
			},
			// 章节素材文件上传成功
			fileUploadSuccess(res, file) {
				this.loading = false
				if (this.uploadFileType == 0) {
					this.choiceItem.videoUrl = res.data
				}
				this.$message.success('短视频文件上传成功！')
			},
			// 章节素材文件上传失败
			fileUploadError(err) {
				this.loading = false
				this.$message.error('短视频文件上传失败！请重试！' + err.msg)
			},
			// 课程章节素材文件上传
			fileUpload(value) {
				this.uploadFileType = value
				this.fileUploadUrl = fileUploadUrl
			},
			beforeImgUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!');
				}
				return isLt2M;
			}
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>