<template>

  <div>
    <div class="handle-box">
      <el-form ref="form" :model="pageInfo">
        <el-row>
                    <el-col :span="5">
                      <el-form-item label="是否规范">
                        <el-switch
                            @change="changeMode()"
                            v-model="quer.standard"
                            active-text="是"
                            inactive-text="否">
                        </el-switch>
                      </el-form-item>
                    </el-col>
          <el-col :span="5">
            <el-form-item label="所属部门">
              <el-select v-model="value" placeholder="请选择">
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-button type="primary" @click="query()" icon="el-icon-search">搜索</el-button>
            <el-button type="success" :loading="loadingExcel" icon="el-icon-download" @click="exportExcel()" >导出
            </el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="员工工号">
              <el-input v-model="quer.dealNo" placeholder="工号"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="服务时间区间">
              <el-date-picker
                  v-model="date1"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>


      </el-form>
    </div>
    <el-table :data="list" height="500px" v-loading="loading" border
              :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
      <el-table-column
          prop="createTime"
          width="200"
          label="订单结束日期">
      </el-table-column>
      <el-table-column
          prop="name"
          label="所属部门">

      </el-table-column>
      <el-table-column
          prop="channel"
          label="员工工号">
      </el-table-column>
      <el-table-column
          prop="realName"
          label="员工姓名">
      </el-table-column>
      <el-table-column
          prop="sop"
          label="是否规范">
      </el-table-column>
    </el-table>
    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChange"
            :current="this.dto.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="10"/>
    </div>

  </div>
</template>

<script>
export default {
  name: "standardEmployee",
  data() {
    return {
      options: [{
        value: null,
        label: '全部'
      },{
        value: '9',
        label: '保洁部'
      }, {
        value: '10',
        label: '搬家部'
      }, {
        value: '11',
        label: '维修部'
      }, {
        value: '39',
        label: '管家部'
      }, {
        value: '41',
        label: '家电服务'
      }],
      value: null,
      list: null,
      loading: false,
      date1: null,
      dto: [],
      tableFlag: false,
      loadingExcel: false,
      pageSizeOpts: [10, 20, 40],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      quer: {
        size: 10,
        current: 1,
        workOrderType: 3,
        dealNo: null,
        startTime: null,
        endTime: null,
        standard:true,
      },
    }
  },
  created() {

  },
  methods: {
    changeMode(){
      this.quer.current = 1;
    },
    getData() {
    this.query();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.quer.current = index;
      this.getData();
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.quer.size = size;
      this.getData();
    },
    query() {
      if (this.date1 !=null){
        this.quer.startTime = this.date1[0];
        this.quer.endTime = this.date1[1];
      } else {
        return  this.$message({
          message: '请选择时间区间',
          type: 'warning'
        });
      }
      this.tableFlag = this.quer.repurchase;
      this.$postData("getStandardEmployee", this.quer).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    exportExcel(){
      if (this.date1 !=null){
        this.quer.startTime = this.date1[0];
        this.quer.endTime = this.date1[1];
      } else {
        return  this.$message({
          message: '请选择时间区间',
          type: 'warning'
        });
      this.loadingExcel = true;
      this.$postData("getStandardEmployeeExcel", this.quer, {
        responseType: "arraybuffer"
      }).then(res => {
        this.loadingExcel = false;
        this.blobExport({
          tablename: "规范员工",
          res: res
        });
      })
    }},
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
    }
  }

</script>

<style scoped>

</style>
