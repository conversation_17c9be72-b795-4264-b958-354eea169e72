<template>
    <div class="table">
        <div class="container">

            <div class="handle-box">
                <el-form ref="form">

                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="保底抽成">
                                <el-input
                                        type="number"
                                        v-model="model.baseRoy"
                                        placeholder="保底抽成"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="备注信息">
                                <el-input
                                        v-model="model.remark"
                                        placeholder="备注信息"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" >

                            <el-form-item>
                                <el-button type="success"  round @click="query()">搜索</el-button>
                                <el-button type="info"  round @click="re()">重置</el-button>
                                <el-button type="success" round  @click="addModal=true">添加</el-button>
                            </el-form-item>

                        </el-col>
                    </el-row>

                </el-form>
            </div>
            <el-table :data="list" :header-cell-style="{background:'#ddd'}" @sort-change="sortChange" border class="table" ref="multipleTable" @cell-dblclick="edit">
                <el-table-column
                        type="selection"
                        width="35">
                </el-table-column>
                <el-table-column
                        prop="remark"
                        fixed="left"
                        label="备注信息"
                        sortable="custom"
                        width="120"
                ></el-table-column>

                <el-table-column
                        prop="baseRoy"
                        label="保底抽成(%)"
                        sortable="custom"
                        width="130"
                ></el-table-column>
                <el-table-column
                        prop="baseIn"
                        label="抽成起算金额"
                        sortable="custom"
                        width="130"
                ></el-table-column>
                <el-table-column
                        sortable="custom"
                        prop="addRoy"
                        label="每W提成(%)"
                        width="130"
                ></el-table-column>
<!--                <el-table-column-->
<!--                        prop="workRemark"-->
<!--                        label="工作记录"-->
<!--                        width="150"-->
<!--                >-->
<!--                </el-table-column>-->
                <el-table-column
                        sortable="custom"
                        prop="maxRoy"
                        label="封顶提成(%)"
                        width="130"
                ></el-table-column>
                <el-table-column
                        sortable="custom"
                        prop="eroy"
                        label="开发单提成(%)"
                        width="140"
                ></el-table-column>
                <el-table-column
                        sortable="custom"
                        prop="proy"
                        label="自有保姆提成(%)"
                        width="160"
                ></el-table-column>
                <el-table-column
                        sortable="custom"
                        prop="validityDate"
                        label="有效期"
                        width="180"
                ></el-table-column>
                <el-table-column
                        sortable="custom"
                        prop="createDate"
                        label="创建时间"
                        width="180"
                ></el-table-column>


                <el-table-column
                        label="操作"
                        fixed="right"
                        min-width="150">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="edit(scope.row)" type="primary">编辑</el-button>
                        <el-button size="mini" @click="doSet(scope.row.id)" type="primary">结算</el-button>
                   </template>

                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>

        <Modal v-model="saveModal" class="Modal" :width="screenWidth"  title="添加" :mask-closable="false" >
            <div class="addBody">

                <settlementRuleUpdate v-if="saveModal" @init-choose="initChooseProject"
                                @close-modal="closeCurrModal" :model="show"></settlementRuleUpdate>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="doModal" class="Modal" :width="doscreenWidth"  title="结算" :mask-closable="false" >
            <div class="addBody">
                <settlementRuleDo v-if="doModal" @init-choose="initChooseProject"
                                @close-modal="closeCurrModal" :model="doid"></settlementRuleDo>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="addModal" class="Modal" :width="screenWidth"   title="添加" :mask-closable="false" >
            <div class="addBody" style="min-height:400px">
                <settlementRuleAdd v-if="addModal" @init-choose="initChooseProject"
                         @close-modal="closeCurrModal" ></settlementRuleAdd>
            </div>
            <div slot="footer">
            </div>
        </Modal>

    </div>
</template>

<script>
    import settlementRuleAdd from '@/components/page/agent/choose/settlementRuleAdd.vue'
    import settlementRuleUpdate from '@/components/page/agent/choose/settlementRuleUpdate.vue'
    import settlementRuleDo from '@/components/page/agent/choose/settlementRuleDo.vue'
    import {formatDate} from '@/components/common/utils.js'
    export default {
        data() {
            return {
                doid:null,
                doModal:false,
                tableData: [],
                pageSizeOpts:[10,20,30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                screenWidth: '80%',//新增对话框 宽度
                doscreenWidth:'50%',
                saveModal: false,
                addModal:false,
                show:{},
                model: {
                    baseRoy: null,
                    remark:null,
                    current: 1,
                    size: 10
                },
                update:{
                    id:null,
                    realName:null,

                },
                list:null
            };
        },
        components: {
            'settlementRuleAdd': settlementRuleAdd,
            'settlementRuleUpdate': settlementRuleUpdate,
            'settlementRuleDo':settlementRuleDo
        },
        created() {
            this.getData();
        },
        computed: {
        },
        methods: {
            sortChange: function(column, prop, order) {

                if (column == null) {
                    this.model.orderBy = "";
                } else {
                    if (column.order == "ascending") {
                        this.model.orderBy = column.prop + " ASC";
                    }
                    if (column.order == "descending") {
                        this.model.orderBy = column.prop + " DESC";
                    }
                }
                this.getData();
            },
            getData() {
                this.$postData("settlementPage", this.model, {}).then(res => {
                    if (res.status == 200) {

                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            query() {
                this.getData();
            },
            re(){
                this.model.baseRoy=null,
                this.model.remark=null
                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.model.size = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                console.log(index)
                this.model.current = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
                this.updateModal = false;
                this.addModal=false;
                this.doModal=false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },
            edit(id){
                this.show=id;
                console.log(this.model)
                this.saveModal=true;
            },
            doSet(id){
                this.doid=id;
                this.doModal=true;
            }
        }
    };
</script>

<style scoped>

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
