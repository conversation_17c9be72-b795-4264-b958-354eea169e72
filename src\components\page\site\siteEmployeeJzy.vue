<template>
  <div class="container">
    <div class="handle-box">
<!--    <el-form ref="form">-->
<!--    <el-row>-->
<!--      <el-col :span="6">-->
<!--        <el-form-item label="姓名">-->
<!--          <el-select-->
<!--              style="width: 70%"-->
<!--              v-model="employeeId"-->
<!--              clearable-->
<!--              filterable-->
<!--              remote-->
<!--              reserve-keyword-->
<!--              placeholder="请输入员工姓名"-->
<!--              :remote-method="remoteMethod"-->
<!--              @change="changEmployee"-->
<!--              :loading="employeeloading">-->
<!--            <el-option-->
<!--                v-for="item in employeeoptions"-->
<!--                :key="item.id"-->
<!--                :label="item.realName + '(' + item.no +')' "-->
<!--                :value="item.id">-->
<!--            </el-option>-->
<!--          </el-select>-->

<!--        </el-form-item>-->
<!--      </el-col>-->
<!--    </el-row>-->

<!--    </el-form>-->
      <el-form ref="form" class="form-inline">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="姓名" label-position="right">
              <el-input v-model="realName" placeholder="请输入姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="身份证" label-position="right">
              <el-input v-model="idCard" placeholder="请输入身份证"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="display: flex; align-items: flex-start;">
            <el-button type="primary" @click="search">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 数据展示区域 -->
    <div v-if="showResult" class="result-container">
      <!-- 查询成功显示数据 -->
      <div v-if="hasData" class="data-section">
<!--        <el-card class="summary-card">-->
<!--          <div slot="header" class="card-header">-->
<!--            <span>诉讼数据统计</span>-->
<!--          </div>-->
<!--          <el-row :gutter="20">-->
<!--            <el-col :span="6">-->
<!--              <div class="stat-item">-->
<!--                <div class="stat-number">{{ caseData.count.totalCount }}</div>-->
<!--                <div class="stat-label">总案件数</div>-->
<!--              </div>-->
<!--            </el-col>-->
<!--            <el-col :span="6">-->
<!--              <div class="stat-item">-->
<!--                <div class="stat-number">{{ caseData.count.endTotalCount }}</div>-->
<!--                <div class="stat-label">已结案件数</div>-->
<!--              </div>-->
<!--            </el-col>-->
<!--            <el-col :span="6">-->
<!--              <div class="stat-item">-->
<!--                <div class="stat-number">{{ caseData.count.openTotalCount }}</div>-->
<!--                <div class="stat-label">未结案件数</div>-->
<!--              </div>-->
<!--            </el-col>-->
<!--            <el-col :span="6">-->
<!--              <div class="stat-item">-->
<!--                <div class="stat-number">{{ caseData.count.totalMoney }}万元</div>-->
<!--                <div class="stat-label">涉案金额</div>-->
<!--              </div>-->
<!--            </el-col>-->
<!--          </el-row>-->
<!--        </el-card>-->

        <!-- 案件详情 -->
        <el-card class="detail-card" v-if="caseData.caseData && caseData.caseData.length > 0">
          <div slot="header" class="card-header">
            <span>案件详情</span>
          </div>
          <div v-for="(caseItem, index) in caseData.caseData" :key="index">
            <div v-if="caseItem.implement && caseItem.implement.cases && caseItem.implement.cases.length > 0">
              <div v-for="(caseDetail, caseIndex) in caseItem.implement.cases" :key="caseIndex" class="case-detail">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="case-info">
                      <div class="info-item">
                        <span class="label">案件编号：</span>
                        <span class="value">{{ caseDetail.caseNo }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">案件类型：</span>
                        <span class="value">{{ caseDetail.caseType }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">审理法院：</span>
                        <span class="value">{{ caseDetail.court }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">案件状态：</span>
                        <span class="value">{{ caseDetail.caseProStage }}</span>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="case-info">
                      <div class="info-item">
                        <span class="label">案件金额：</span>
                        <span class="value">{{ caseDetail.endCaseMoney }}元</span>
                      </div>
                      <div class="info-item">
                        <span class="label">结案日期：</span>
                        <span class="value">{{ caseDetail.endCaseDate }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">结案方式：</span>
                        <span class="value">{{ caseDetail.endCaseWay }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">案件事由：</span>
                        <span class="value">{{ caseDetail.caseCause }}</span>
                      </div>
                    </div>
                  </el-col>
                </el-row>
                <div class="party-info">
                  <div class="party-title">当事人信息：</div>
                  <div v-for="(party, partyIndex) in caseDetail.partyName" :key="partyIndex" class="party-item">
                    <span class="party-name">{{ party.name }}</span>
                    <span class="party-position">({{ party.partyPosition }})</span>
                    <span class="party-type">{{ party.partyType }}</span>
                  </div>
                </div>
                <div v-if="caseDetail.judgmentResult" class="judgment-result">
                  <div class="result-title">判决结果：</div>
                  <div class="result-content">{{ caseDetail.judgmentResult }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 查无结果提示 -->
      <div v-else class="no-data">
        <el-empty description="查无涉诉结果" :image-size="200"></el-empty>
      </div>
    </div>

  </div>

</template>

<script >
export default {
  data() {
    return {
      realName:'',
      idCard:'',
      employeeloading: false,
      employeeoptions: [],
      employeeId: null,
      showResult: false,
      hasData: false,
      caseData: {
        count: {},
        caseData: []
      }
    }
  },
  methods: {
    changEmployee(item) {
    },
    remoteMethod(query) {
      this.employeeoptions = [];

      if (query) {
        this.employeeloading = true;
        this.$postData("getByService", {name:query,pageNum:1,pageSize:500}, {}).then(res => {
          if (res.status === 200) {
            this.employeeloading = false;
            res.data.list.forEach(v =>{
              var data = {};
              data.id = v.id;
              data.realName = v.realName;
              data.no = v.no;
              this.employeeoptions.push(data);
            })
          } else {
            this.$message.error("查询失败，" + res.msg);
          }
        })
      } else {
        this.employeeoptions = [];
      }
    },
    search() {
      if (!this.realName || !this.idCard) {
        this.$message.warning("姓名或者身份证查询不能为空");
        return;
      }
      const idRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))((0[1-9])|([1-2][0-9])|30|31)\d{3}[\dXx]$/;
      if (!idRegex.test(this.idCard)) {
        this.$message.warning("身份证号码格式不正确");
        return;
      }

      this.$postData("siteEmployeeJZYQuery",{realName:this.realName,idcard:this.idCard,creName:localStorage.getItem("realName")},{}).then(res =>{
        if (res.status === 200) {
          this.showResult = true;

          if (res.data.resultMsg === "查询成功") {
            this.hasData = true;
            try {
              // 解析返回的data字段
              const dataObj = JSON.parse(res.data.data);
              this.caseData = dataObj;
            } catch (error) {
              console.error("数据解析失败:", error);
              this.$message.error("数据解析失败");
              this.hasData = false;
            }
          } else if (res.data.resultMsg === "查无结果") {
            this.hasData = false;
            this.$message.info("查无结果");
          } else {
            this.hasData = false;
            this.$message.warning(res.data.resultMsg || "查询异常");
          }
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    }
  }
}

</script>

<style scoped>
.container {
  padding: 0px !important;
  background: #fff;
  border: none !important;
  border-radius: 5px;
}
.handle-box {
  font-weight: bold !important;
  margin-bottom: 20px;
}

.result-container {
  margin-top: 20px;
}

.summary-card, .detail-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: bold;
  font-size: 16px;
}

.stat-item {
  text-align: center;
  padding: 20px 0;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.case-detail {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  background-color: #FAFAFA;
}

.case-info {
  margin-bottom: 15px;
}

.info-item {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  color: #333;
  min-width: 80px;
}

.value {
  color: #666;
  flex: 1;
}

.party-info {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #EBEEF5;
}

.party-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.party-item {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.party-name {
  font-weight: bold;
  color: #409EFF;
  margin-right: 10px;
}

.party-position {
  color: #666;
  margin-right: 10px;
}

.party-type {
  color: #999;
  font-size: 12px;
}

.judgment-result {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #EBEEF5;
}

.result-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.result-content {
  color: #666;
  line-height: 1.6;
  background-color: #F5F7FA;
  padding: 10px;
  border-radius: 4px;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}
.form-inline .el-form-item {
  margin-right: 24px;
  margin-bottom: 0;
}
.form-inline .el-input {
  width: 240px;
}
.form-inline .el-button {
  min-width: 80px;
  height: 40px;
  margin-top: 0;
}
@media (max-width: 900px) {
  .form-inline .el-input {
    width: 100%;
  }
}
</style>
