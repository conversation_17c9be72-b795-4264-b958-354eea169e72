<template style="background-color: #000">

            <el-tabs type="card" :stretch="true" >
                <el-tab-pane label="留言信息">
                <Row>
                <Col span="24">

                        <el-form ref="form" :model="dom" :rules="ruleValidate">
                            <Row>
                            <Col span="12">

                                    <el-form-item   prop="agentName">
                                        <div class="label-name">经理名:</div>
                                        <el-input placeholder="经理名" v-model="dom.agentName"  :disabled="true" style="width: 70%">
                                        </el-input>
                                    </el-form-item >

                                    <el-form-item   prop="memberName">
                                        <div class="label-name">客户名:</div>
                                        <el-input placeholder="请输入客户名" v-model="dom.memberName"  :disabled="true" style="width: 70%">

                                        </el-input>
                                    </el-form-item >
                                <el-form-item   prop="communicateTime" >
                                    <div class="label-name">备注信息:</div>
                                    <el-input
                                            style="width: 70%"
                                            type="textarea"
                                            :autosize="{ minRows: 2}"
                                            placeholder="请输入内容"
                                            v-model="dom.remarks">

                                    </el-input>
                                </el-form-item>
                            </Col>
                                <Col span="12">

                                    <el-form-item   prop="baomuName">
                                        <div class="label-name">保姆名:</div>
                                        <el-input placeholder="请输入保姆名" v-model="dom.baomuName"  :disabled="true" style="width: 70%">
                                        </el-input>
                                    </el-form-item >

                                    <el-form-item   prop="communicateTime">
                                        <div class="label-name">咨询时间:</div>
                                        <el-input placeholder="请输入咨询时间" v-model="dom.communicateTime"  :disabled="true" style="width: 70%">

                                        </el-input>
                                    </el-form-item >
                                    <el-form-item   prop="communicateTime" >
                                        <div class="label-name">留言内容:</div>
                                        <el-input
                                                style="width: 70%"
                                                type="textarea"
                                                placeholder="请输入内容"
                                                v-model="dom.content">

                                        </el-input>
                                    </el-form-item>
                            </Col>



                            </Row>

                                    <div style="margin-right: 100px;float: right">
                                        <Button type="primary" @click="save('dom')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </div>
                                </el-form>
                    </Col>


            </Row>
                </el-tab-pane>
<!--                <el-tab-pane label="配置管理">配置管理</el-tab-pane>-->
            </el-tabs>

</template>


<script>

    export default {
        props:['model'],
        data() {
            return {
                dom:this.model,
                formItem: {
                    No: null,
                    RealName: null,
                    Phone: null,
                    Address:null,
                },

                ruleValidate: {
                    name: [
                        {required: true, message: '请输入编号', trigger: 'change'}
                    ],
                    street: [
                        {required: true, message: '请选择名称', trigger: 'change'}
                    ],
                    lng: [
                        {required: true, message: '请选择联系方式', trigger: 'change'}
                    ],
                    lat: [
                        {required: true, message: '请输入地址', trigger: 'blur'}
                    ],

                },
            }
        },
        components: {
        },
        created: function () {
            console.log(this.dom)
        },
        methods: {
            save(name) {
                        this.$postData("update_communicate", this.dom, {}).then(res => {
                            if (res.status == 200) {
                                this.$Message.success('修改成功');
                                this.chooseThisModel();
                            } else {
                                this.$message.error("修改失败，" + res.msg);
                            }
                        })


            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.dom=null;
                this.$emit('init-choose', "");
            },

        },

    }
</script>
<style>

</style>

<style scoped>
    .label-name{
        float: left;
        text-align: center;
        width: 15%;
    }

    .ivu-col-span-9{
        padding-top: 30px;
    }
    .el-input-group{
        width: 80%;
    }
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

