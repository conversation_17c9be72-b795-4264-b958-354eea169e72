<template>
    <div class="table">
        <el-dialog title="展示地址" :visible.sync="dialogTableVisible">
            <vue-qr :text="showUrl" :margin="0"  colorLight="#fff"  :logoScale="0.3" :size="100" class="hbeimg"></vue-qr>
        </el-dialog>
        <div class="container">
            <el-form :model="dom"  label-width="100px" class="demo-ruleForm">
                <el-row>
<!--                    <el-col :span="8">-->
<!--                        <el-form-item label="名称" prop="lng">-->
<!--                            <el-input v-model="dom.name"></el-input>-->
<!--                        </el-form-item>-->
<!--                    </el-col>-->
<!--                    <el-col :span="8">-->
<!--                        <el-form-item label="状态" prop="lat">-->
<!--                            <el-radio v-model="dom.status" :label="1" border>启用中</el-radio>-->
<!--                            <el-radio v-model="dom.status" :label="0" border>停用中</el-radio>-->
<!--                        </el-form-item>-->
<!--                    </el-col>-->
                    <el-col :span="12">
                        <el-form-item>

<!--                            <el-button type="primary"  @click="getMarketingList()" icon="el-icon-search" round plain>查询</el-button>-->
                            <el-button type="info"  @click="toEdit('zl')" icon="el-icon-circle-plus-outline" round plain>新增助力活动</el-button>
                            <el-button type="info"  @click="toEdit('bb')" icon="el-icon-circle-plus-outline" round plain>新增博饼活动</el-button>
                            <el-button type="info"  @click="toEdit('cdm')" icon="el-icon-circle-plus-outline" round plain>新增猜灯谜活动</el-button>
                            <el-button type="info"  @click="toEdit('jgg')" icon="el-icon-circle-plus-outline" round plain>新增九宫格抽奖活动</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>

              <el-row>
                <el-button type="primary"  @click="$router.push('/yingxiaoCensus')" plain>统计分析</el-button>
              </el-row>

            </el-form>
        </div>
        <el-row :gutter="20">



        <div v-for="(item,index) in showList" :key="index" style="margin: 20px;line-height: 24px"   >
            <el-col :span="12" style="margin-bottom: 20px">
            <el-card shadow="hover">
                <div >
                    <h2>
                        <i class="el-icon-brush"/>  &nbsp;{{item.activityName}}
<!--                        <el-tag :type="item.status==0?'danger':'success'">{{item.status==0?'停用':'启用'}}</el-tag>-->
                        <el-button type="primary" icon="el-icon-edit" style="float: right;margin-left:  20px" @click="toEdit(item)">编辑</el-button>
                        <el-button type="warning" icon="el-icon-view" style="float: right;margin-left:  20px" @click="showQr(item)">预览</el-button>
                        <el-button type="primary" icon="el-icon-edit" style="float: right;margin-left:  20px" @click="modalUp=true,sho(item)">活动通知推送</el-button>
<!--                        <el-button type="warning" icon="el-icon-view"  style="float: right;margin-left:  20px" @click="showCensus(item.id)">活动统计分析</el-button>-->
<!--                        <el-switch-->
<!--                                @change="saveMarketing(item)"-->
<!--                                style="float: right"-->
<!--                                v-model="item.status"-->
<!--                                :active-value="1"-->
<!--                                :inactive-value="0"-->
<!--                                active-color="#13ce66"-->
<!--                                inactive-color="#ff4949">-->
<!--                        </el-switch>-->
                    </h2>
                    <el-row>
                        <el-col :span="24">
                            <div><i class="el-icon-collection-tag"></i>活动编号：{{item.activeCode}}</div>
                        </el-col>
                        <el-col :span="12">
                            <div><i class="el-icon-date"></i>创建时间：{{item.startTime}}</div>
                        </el-col>
                      <el-col :span="12">
                        <div><i class="el-icon-date"></i>结束时间：{{item.endTime}}</div>
                      </el-col>
                        <el-col :span="12">
                            <div><i class="el-icon-view"/>参与人数：{{item.invitCount}}</div>
                        </el-col>
                      <el-col :span="12">
                        <div><i class="el-icon-view"/>得奖人数：{{item.winCount}}</div>
                      </el-col>
                    </el-row>
                </div>
            </el-card>
            </el-col>
        </div>
        </el-row>

      <activityPush :message="pushList" v-if="modalUp"
                    @init-choose="initChooseProject">
      </activityPush>

    </div>

</template>

<script>

import activityPush from '@/components/page/cms/activityPush.vue'
    import vueQr from 'vue-qr'
    export default {
        name: "activityPageByYingXiao",
        components: {
            vueQr,
          "activityPush": activityPush,
        },
        data () {
            return {
              modalUp:false,
                dom:{
                    name:null,
                    status:1,
                },
                showList:[],
                dialogTableVisible: false,
                showUrl: null,
                pushList:[],
            }
        },
        created(){
            this.getMarketingList()
        },
        methods:{
          sho(val){
            this.pushList = val;
          },
            showQr(item){
                //
              if (item == 'zl' || item.activityType == 'zl'){
                this.showUrl="https://activity.xiaoyujia.com/friendsHelp?activityCode="+item.activeCode
              } else if (item == 'bb' || item.activityType == 'bobing'){
                this.showUrl="https://activity.xiaoyujia.com/boBing?activityCode="+item.activeCode
              }
              console.info(this.showUrl)
                this.dialogTableVisible=true
            },
            toEdit(item){
              if (item == 'zl' || item.activityType == 'zl'){
                this.$router.push({path:'/creatActivityByYingXiao',query:{"id":item.id}})
              } else if (item == 'bb' || item.activityType == 'bobing'){
                this.$router.push({path:'/creatActivityByYingXiaoBoBing',query:{"id":item.id}})
              } else if (item == 'cdm' || item.activityType == 'cdm'){
                this.$router.push({path:'/createActivityByGuessWord',query:{"id":item.id}})
              } else if (item == 'jgg' || item.activityType == 'jgg'){
                this.$router.push({path:'/createActivityByJiuGongGe',query:{"id":item.id}})
              }
            },
            getMarketingList(){
                this.$getData("getActiveByYXList", null,null).then(res => {
                    if (res.status == 200) {
                        this.showList=res.data
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }

                })
            },
            saveMarketing(item){
                this.$postData("saveMarketing", item,null).then(res => {
                    if (res.status == 200) {
                        this.$message.success("更新成功")
                    } else {
                        this.$message.error("更新失败，");
                    }

                })
            },
          initChooseProject() {
            this.modalUp = false;
          },
        }
    }
</script>

<style scoped>

</style>
