<template>
	<div class="container">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">直播管理</el-menu-item>
			<el-menu-item index="1">直播分组</el-menu-item>
			<el-menu-item index="2">回放计划</el-menu-item>
		</el-menu>

		<div v-if="choiceIndexNow==0">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="3">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="直播标题、主播名"></el-input>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="直播渠道" style="margin-right: 20px">
								<el-select v-model="choiceSourceName" @change="choiceSourece">
									<el-option :label="item.name" :value="item.value" v-for="(item,index) in sourceList"
										:key="index"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="直播类型" style="margin-right: 20px">
								<el-select v-model="searchType">
									<el-option label="" value=""></el-option>
									<el-option label="手机直播" value="0"></el-option>
									<el-option label="推流直播" value="1"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="直播分组" style="margin-right: 20px">
								<el-select v-model="searchGroup">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in groupList" :key="index" :label="item.groupName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="直播状态" style="margin-right: 20px">
								<el-select v-model="searchStatus">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in statusList" :key="index" :label="item.name"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>
							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>
							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(0,0)">添加
							</el-button>
							<el-button type="danger" @click="openModal(4,0)">直播后台
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column prop="roomid" width="60" label="编号">
					<template slot-scope="scope">
						<span>{{scope.row.roomid}}</span>
					</template>
				</el-table-column>

				<el-table-column width="100" prop="name" label="直播标题">
					<template slot-scope="scope">
						<span>{{scope.row.name}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="anchor_name" label="主播名">
					<template slot-scope="scope">
						<span>{{scope.row.anchor_name||'暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" label="主播头像">
					<template slot-scope="scope">
						<img :src="scope.row.appletLive.anchorImg||blankImg" style="width: 100px;height: 100px;"
							@click="openImg(scope.row.appletLive.anchorImg||blankImg)">
					</template>
				</el-table-column>

				<!-- 				<el-table-column width="140" prop="share_img" label="直播图">
					<template slot-scope="scope">
						<img :src="scope.row.share_img||blankImg" style="width: 100px;height: 100px;"
							@click="openImg(scope.row.share_img)">
					</template>
				</el-table-column> -->

				<el-table-column width="140" label="直播封面">
					<template slot-scope="scope">
						<img :src="scope.row.appletLive.liveBanner||blankImg" style="width: 100px;height: 100px;"
							@click="openImg(scope.row.appletLive.liveBanner||blankImg)">
					</template>
				</el-table-column>

				<el-table-column width="140" prop="live_type" label="直播类型">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.live_type==0" type="success">手机直播</el-tag>
						<el-tag v-if="scope.row.live_type==1" type="danger">推流直播</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="statusName" label="直播状态">
					<template slot-scope="scope">
						<span>{{scope.row.statusName||'暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" label="直播展示">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.appletLive.liveState ==0" type="danger">关闭</el-tag>
						<el-tag v-if="scope.row.appletLive.liveState ==1" type="success">开启</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="140" label="回放" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-tag @click="getAppletLiveReplay(scope.$index)">查看</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="140" label="推流地址" :render-header="renderHeader">
					<template slot-scope="scope">
						<div>
							<el-tag @click="getAppletLivePushUrl(scope.$index)"
								v-if="scope.row.live_type==1">查看</el-tag>
							<span v-if="scope.row.live_type==0">非推流</span>
						</div>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="share_img" label="分享图" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-tag v-if="!scope.row.posterUrl" @click="getAppletLiveSharedCode(scope.$index)">查看</el-tag>
						<el-tag v-if="scope.row.posterUrl" @click="openUrl(scope.row.posterUrl)">预览海报</el-tag>
						<el-tag v-if="scope.row.cdnUrl" @click="openUrl(scope.row.cdnUrl)">预览二维码</el-tag>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
					<template slot-scope="scope">
						<el-button @click="openModal(7,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<div v-if="choiceIndexNow==1">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="3">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="分组标题、主播列表"></el-input>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="分组状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(1,0)">添加
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column prop="id" width="60" label="编号">
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="180" prop="groupName" label="分组名称">
					<template slot-scope="scope">
						<span v-if="!scope.row.isEdit">{{scope.row.groupName}}</span>

						<el-input v-model="scope.row.groupName" style="width: 100%;" type="textarea"
							v-if="scope.row.isEdit" placeholder="请输入分组名称" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column width="180" prop="anchorList" label="主播列表" :render-header="renderHeader">
					<template slot-scope="scope">
						<span v-if="!scope.row.isEdit">{{scope.row.anchorList||'暂无'}}</span>

						<el-input v-model="scope.row.anchorList" style="width: 100%;" type="textarea"
							v-if="scope.row.isEdit" placeholder="请输入主播列表" :autosize="{ minRows: 4, maxRows: 10}">
						</el-input>
					</template>
				</el-table-column>

				<el-table-column width="180" prop="typeState" label="分组状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.typeState == item.value&&!scope.row.isEdit" :type="item.type"
							v-for="(item,index) in stateList" :key="index">{{item.text}}</el-tag>

						<el-select v-model="scope.row.typeState" placeholder="请选择" style="width: 100px;"
							v-if="scope.row.isEdit">
							<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
								:value="item.value"></el-option>
						</el-select>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
					<template slot-scope="scope">
						<el-button @click="scope.row.isEdit = true" type="success" size="small"
							:disabled="scope.row.isEdit" v-if="!scope.row.isEdit" icon="el-icon-edit">修改
						</el-button>
						<el-button @click="updateAppletLiveGroup(scope.row)" type="primary" size="small"
							v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
						</el-button>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<div v-if="choiceIndexNow==2">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="3">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="回放计划标题、内容"></el-input>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="直播渠道" style="margin-right: 20px">
								<el-select v-model="choiceSourceName" @change="choiceSourece">
									<el-option :label="item.name" :value="item.value" v-for="(item,index) in sourceList"
										:key="index"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="计划状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>
							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>
							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(2,0)">添加
							</el-button>
							<el-button type="danger" @click="openModal(5,0)">当前计划
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column prop="id" width="60" label="编号">
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="planTitle" label="计划标题">
					<template slot-scope="scope">
						<span>{{scope.row.planTitle}}</span>
					</template>
				</el-table-column>

				<el-table-column width="180" prop="planContent" label="计划内容">
					<template slot-scope="scope">
						<span>{{scope.row.planContent||'暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="source" label="直播渠道">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.source==item.value" v-for="(item,index) in sourceList" :key="index"
							:type="formatTagStyle(index)">{{item.name}}</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="planType" label="计划类型" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.planType==item.value" v-for="(item,index) in planTypeList" :key="index"
							:type="formatTagStyle(index+2)">{{item.name}}</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="planStatus" label="计划状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.planStatus == item.value&&!scope.row.isEdit" :type="item.type"
							v-for="(item,index) in stateList" :key="index">{{item.text}}</el-tag>
					</template>
				</el-table-column>

				<el-table-column prop="startTime" label="排期直播间" width="200">
					<template slot-scope="scope">
						<span v-if="item.roomid==scope.row.roomId" v-for="(item,index) in liveList" :key="index">
							{{item.roomid}}：{{item.name}}
						</span>
						<span v-if="!scope.row.roomId">-</span>
					</template>
				</el-table-column>

				<el-table-column prop="startTime" label="开始日期" width="200" sortable>
					<template slot-scope="scope">
						<span>
							{{scope.row.startTime||'-'}}
						</span>
					</template>
				</el-table-column>

				<el-table-column prop="endTime" label="结束日期" width="200" sortable>
					<template slot-scope="scope">
						<span>
							{{scope.row.endTime||'-'}}
						</span>
					</template>
				</el-table-column>

				<el-table-column prop="startTime" label="轮播直播间列表" width="200">
					<template slot-scope="scope">
						<ul v-for="(item,index) in liveList" :key="index">
							<li class="tags-li" v-for="(item1,index1) in scope.row.roomIdListArray" :key="index1"
								v-if="item.roomid==parseInt(item1)">
								<span>
									{{ item.roomid+'：'+item.name }}
								</span>
							</li>
						</ul>
						<span v-if="!scope.row.roomIdList">-</span>
					</template>
				</el-table-column>

				<el-table-column prop="intervalTime" label="轮播间隔" width="140" :render-header="renderHeader">
					<template slot-scope="scope">
						<span>{{scope.row.intervalTime}}</span>
						<span v-if="scope.row.planType==0">-</span>
						<el-tag v-if="scope.row.intervalType==item.value&&scope.row.planType==1"
							v-for="(item,index) in intervalTypeList" :key="index"
							:type="formatTagStyle(index+2)">{{item.name}}</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creater" label="创建人">
					<template slot-scope="scope">
						<span v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
						<span v-if="!scope.row.creatorName">{{scope.row.creator}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
					<template slot-scope="scope">
						<el-button @click="openModal(3,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
						<el-button @click="updateAppletLivePlayBackPlan(scope.row)" type="primary" size="small"
							v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
						</el-button>
						<el-popconfirm title="确定该回放计划吗？删除后无法恢复，请谨慎操作!"
							@confirm="deleteAppletLivePlayBackPlan(scope.row)">
							<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
								icon="el-icon-delete">删除
							</el-button>
						</el-popconfirm>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--视频预览-->
		<el-dialog :visible.sync="videoModal" width="40%" title="视频预览" :mask-closable="false">
			<video style="width: 100%;height: auto;margin: 0 auto;" :controls="true" :enable-progress-gesture="true"
				:initial-time="0" :show-center-play-btn="true" autoplay :src="videoUrl" custom-cache="false">
			</video>
		</el-dialog>

		<!-- 网页预览 -->
		<el-drawer size="25%" :with-header="false" :visible.sync="viewModal" direction="rtl">
			<iframe id="iframeId" :src="viewPageUrl" frameborder="0" style="width: 400px;height: 800px;margin: 0 auto;"
				scrolling="auto">
			</iframe>
		</el-drawer>

		<!-- 地址预览 -->
		<el-dialog :visible.sync="textModal" width="30%" title="地址预览" :mask-closable="false">
			<div><span>服务器地址：</span><a @click="copyText(serverUrl)">{{serverUrl}}</a></div>
			<div><span>串流密钥：</span><a @click="copyText(serverSecretUrl)">{{serverSecretUrl}}</a></div>
		</el-dialog>

		<!-- 当前计划 -->
		<el-dialog :visible.sync="playBackPlanNowModal" width="30%" title="当前计划" :mask-closable="false">
			<div><span style="font-size: 20px;font-weight: bold;">正在生效的计划</span></div>
			<div><span>计划标题：{{playBackPlanNow.planTitle}}</span></div>
			<div><span>计划内容：{{playBackPlanNow.planContent||'-'}}</span></div>
			<div>直播渠道：<el-tag v-if="playBackPlanNow.source==item.value" v-for="(item,index) in sourceList" :key="index"
					:type="formatTagStyle(index)">{{item.name}}</el-tag></div>
			<div>计划类型：<el-tag v-if="playBackPlanNow.planType==item.value" v-for="(item,index) in planTypeList"
					:key="index" :type="formatTagStyle(index+2)">{{item.name}}</el-tag></div>
			<div v-if="playBackPlanNow.planType==0">
				<div>排期直播间：
					<span v-if="item.roomid==playBackPlanNow.roomId" v-for="(item,index) in liveList" :key="index">
						{{item.roomid}}：{{item.name}}
					</span>
				</div>
				<div><span>开始日期：{{playBackPlanNow.startTime||'-'}}</span></div>
				<div><span>结束日期：{{playBackPlanNow.endTime||'-'}}</span></div>
			</div>
			<div v-if="playBackPlanNow.planType==1">
				<div style="width: 400px;">
					轮播直播列表：
					<div v-for="(item,index) in liveList" :key="index">
						<span v-for="(item1,index1) in playBackPlanNow.roomIdListArray" :key="index1"
							v-if="item.roomid==parseInt(item1)">
							{{ item.roomid+'：'+item.name }}
						</span>
					</div>
					<div><span>轮播间隔：{{playBackPlanNow.intervalTime}}</span><el-tag
							v-if="playBackPlanNow.intervalType==item.value" v-for="(item,index) in intervalTypeList"
							:key="index" :type="formatTagStyle(index+2)">{{item.name}}</el-tag></div>
				</div>
			</div>
			<div>创建信息：<span
					v-if="playBackPlanNow.creatorName">{{playBackPlanNow.creatorName}}({{playBackPlanNow.creator}})</span>
				<span v-if="!playBackPlanNow.creatorName">{{playBackPlanNow.creator}}</span>
				<span>-{{playBackPlanNow.createTime||'-'}}创建</span>
			</div>
			<div><span style="color: red;">*将自动选取最新且上架中的回放计划，与小程序直播浮窗中的显示同步</span>
			</div>
			<br>
			<div v-if="appletLiveNow">
				<div><span style="font-size: 20px;font-weight: bold;">正在播放的回放</span></div>
				<div><span>房间id：{{appletLiveNow.roomid}}</span></div>
				<div><span>房间名：{{appletLiveNow.name}}</span></div>
			</div>
		</el-dialog>

		<!-- 添加轮播直播间列表 -->
		<el-dialog :visible.sync="roomIdListModal" width="60%" title="添加轮播直播间列表" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;" v-if="choiceItem">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-checkbox-group v-model="choiceItem.roomIdListArray" value-key="id">
						<el-checkbox-button v-for="(val,index) in liveList" :key="index"
							:label="val.roomid">{{ val.roomid+'：'+val.name }}
						</el-checkbox-button>
					</el-checkbox-group>
				</el-row>

				<div style="margin: 20px 400px;width: 100%;">
					<el-button @click="roomIdListModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="roomIdListModal=false" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

		<!--添加直播-->
		<el-drawer size="60%" :with-header="false" :visible.sync="liveModal" direction="rtl">
			<div>
				<el-row style="width:100%;height: auto;margin: 20px 20px;" v-if="detailType==0">
					<h2 class="detail-title">直播信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 直播标题：">
								<el-input v-model="choiceItem.name" type="textarea" class="handle-input mr10"
									placeholder="请输入直播标题">
								</el-input>
							</el-form-item>

							<el-form-item label="* 主播昵称：">
								<el-input v-model="choiceItem.anchorName" type="textarea" class="handle-input mr10"
									placeholder="请输入主播昵称">
								</el-input>
							</el-form-item>

							<el-form-item label="* 主播微信：">
								<el-input v-model="choiceItem.anchorWechat" type="textarea" class="handle-input mr10"
									placeholder="请输入主播微信">
								</el-input>
							</el-form-item>

							<el-form-item label="* 直播分享：">
								<el-upload class="upload-demo" :action="appletUploadUrl" list-type="picture"
									:on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="choiceItem.shareImg||blankImg" style="width: 200px;height: 200px;"
										@click="openImgUpload(0,0)">
								</el-upload>
							</el-form-item>

							<el-form-item label="* 直播封面：">
								<el-upload class="upload-demo" :action="appletUploadUrl" list-type="picture"
									:on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="choiceItem.feedsImg||blankImg" style="width: 200px;height: 200px;"
										@click="openImgUpload(1,0)">
								</el-upload>
							</el-form-item>

							<el-form-item label="* 直播背景：">
								<el-upload class="upload-demo" :action="appletUploadUrl" list-type="picture"
									:on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="choiceItem.coverImg||blankImg" style="width: 200px;height: 200px;"
										@click="openImgUpload(2,0)">
								</el-upload>
							</el-form-item>
						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 直播类型：">
								<el-select v-model="choiceItem.type" placeholder="请选择" style="width: 100px;">
									<el-option label="手机直播" value="0"></el-option>
									<el-option label="推流直播" value="1"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="决定直播方式，设定后不可更改" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="* 点赞功能：">
								<el-select v-model="choiceItem.closeLike" placeholder="请选择" style="width: 100px;">
									<el-option label="关闭" value="0"></el-option>
									<el-option label="开启" value="1"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="若关闭，观众端将隐藏点赞按钮，直播开始后不允许开启"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="* 货架功能：">
								<el-select v-model="choiceItem.closeGoods" placeholder="请选择" style="width: 100px;">
									<el-option label="关闭" value="0"></el-option>
									<el-option label="开启" value="1"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="若关闭，观众端将隐藏商品货架，直播开始后不允许开启"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="* 评论功能：">
								<el-select v-model="choiceItem.closeComment" placeholder="请选择" style="width: 100px;">
									<el-option label="关闭" value="0"></el-option>
									<el-option label="开启" value="1"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="若关闭，观众端将隐藏评论入口，直播开始后不允许开启"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="* 开始时间：">
								<el-date-picker v-model="choiceItem.startTime1" type="datetime"
									value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00']"
									placeholder="请选择直播开始时间" :picker-options="pickerOptions">
								</el-date-picker>
							</el-form-item>

							<el-form-item label="* 结束时间：">
								<el-date-picker v-model="choiceItem.endTime1" type="datetime"
									value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00']"
									placeholder="请选择直播结束时间" :picker-options="pickerOptions">
								</el-date-picker>
							</el-form-item>

							<el-form-item label="* 直播渠道：">
								<el-select v-model="choiceItem.source" placeholder="请选择" style="width: 100px;"
									@change="choiceSourece1">
									<el-option :label="item.name" :value="item.value" v-for="(item,index) in sourceList"
										:key="index"></el-option>
								</el-select>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<el-row style="width:100%;height: auto;margin: 20px 20px;" v-if="detailType==1&&choiceItem.appletLive">
					<h2 class="detail-title">直播信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 直播标题：">
								<el-input v-model="choiceItem.appletLive.liveName" type="textarea"
									class="handle-input mr10" placeholder="请输入直播标题">
								</el-input>
							</el-form-item>

							<el-form-item label="主播头像：">
								<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
									list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="choiceItem.appletLive.anchorImg||blankImg"
										style="width: 200px;height: 200px;" @click="openImgUpload(3,0)">
								</el-upload>
							</el-form-item>

							<el-form-item label="直播封面：">
								<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
									list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="choiceItem.appletLive.liveBanner||blankImg"
										style="width: 200px;height: 200px;" @click="openImgUpload(4,0)">
								</el-upload>
							</el-form-item>
						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="直播展示：">
								<el-select v-model="choiceItem.appletLive.liveState">
									<el-option v-for="(item,index) in switchStateList" :key="index"
										:label="item.typeName" :value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="设定后将控制是否显示在直播列表" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="liveModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="createAppletLiveRoom()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateAppletLive(choiceItem)" type="success" size="small" v-if="detailType==1">保存
					</el-button>
				</div>

			</div>
		</el-drawer>

		<!--添加直播分组-->
		<el-drawer size="60%" :with-header="false" :visible.sync="liveGroupModal" direction="rtl">
			<div>
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">直播分组信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 分组标题：">
								<el-input v-model="choiceItem.groupName" type="textarea" class="handle-input mr10"
									placeholder="请输入分组标题">
								</el-input>
							</el-form-item>

							<el-form-item label="主播列表：">
								<el-input v-model="choiceItem.anchorList" type="textarea" class="handle-input mr10"
									placeholder="请输入主播列表">
								</el-input>
							</el-form-item>
						</el-form>

					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="liveGroupModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="insertAppletLiveGroup()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
				</div>

			</div>
		</el-drawer>

		<!--回放计划-->
		<el-drawer size="60%" :with-header="false" :visible.sync="backPlayModal" direction="rtl">
			<div>
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">回放计划</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 计划标题：">
								<el-input v-model="choiceItem.planTitle" type="textarea" class="handle-input mr10"
									placeholder="请输回放计划标题">
								</el-input>
							</el-form-item>

							<el-form-item label="计划内容：">
								<el-input v-model="choiceItem.planContent" type="textarea" class="handle-input mr10"
									placeholder="请输入回放计划内容描述">
								</el-input>
							</el-form-item>

							<el-form-item label="直播渠道：">
								<el-select v-model="choiceItem.source">
									<el-option :label="item.name" :value="item.value" v-for="(item,index) in sourceList"
										:key="index"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="计划类型：">
								<el-select v-model="choiceItem.planType" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in planTypeList" :key="index" :label="item.name"
										:value="item.value"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark"
									content="【排期模式】需填写1.排期直播间2.开始日期3.结束日期；【轮播模式】需填写1.轮播直播间列表2.轮播间隔"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="计划状态：" v-if="detailType==1">
								<el-select v-model="choiceItem.planStatus" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="排期直播：">
								<el-select v-model="choiceItem.roomId" placeholder="请选择" style="width: 220px;"
									:disabled="choiceItem.planType!=0">
									<el-option v-for="(item,index) in liveList" :key="index"
										:label="item.roomid+'：'+item.name" :value="item.roomid"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="开始日期：">
								<el-date-picker v-model="choiceItem.startTime" type="datetime"
									:disabled="choiceItem.planType!=0" value-format="yyyy-MM-dd HH:mm:ss"
									:default-time="['00:00:00']" placeholder="请选择回放开始日期"
									:picker-options="pickerOptions">
								</el-date-picker>
							</el-form-item>

							<el-form-item label="结束日期：">
								<el-date-picker v-model="choiceItem.endTime" type="datetime"
									:disabled="choiceItem.planType!=0" value-format="yyyy-MM-dd HH:mm:ss"
									:default-time="['00:00:00']" placeholder="请选择回放结束日期"
									:picker-options="pickerOptions">
								</el-date-picker>
							</el-form-item>

							<el-form-item label="轮播直播：">
								<div>
									<ul v-for="(item,index) in liveList" :key="index">
										<li class="tags-li" v-for="(item1,index1) in choiceItem.roomIdListArray"
											:key="index1" v-if="item.roomid==parseInt(item1)">
											<span>
												{{ item.roomid+'：'+item.name }}
											</span>
											<span class="tags-li-icon" @click="closeTags(index1)"><i
													class="el-icon-close"></i></span>
										</li>
									</ul>

									<span v-if="!choiceItem.roomIdList">暂未设置</span>
									<el-button type="primary" @click="openModal(6,0)" plain
										:disabled="choiceItem.planType!=1" icon="el-icon-plus">添加直播间</el-button>
								</div>

							</el-form-item>

							<el-form-item label="轮播间隔：">
								<el-input v-model="choiceItem.intervalTime" style="width: 20%;" placeholder="输入间隔时间"
									:disabled="choiceItem.planType!=1"
									onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8">
								</el-input>
								<el-select v-model="choiceItem.intervalType" placeholder="请选择" style="width: 100px;"
									:disabled="choiceItem.planType!=1">
									<el-option v-for="(item,index) in intervalTypeList" :key="index" :label="item.name"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="创建时间：" v-if="detailType==1">
								<span>{{choiceItem.createTime}}</span>
							</el-form-item>

							<el-form-item label="创建人：" v-if="detailType==1">
								<span
									v-if="choiceItem.creatorName">{{choiceItem.creatorName}}({{choiceItem.creator}})</span>
								<span v-if="!choiceItem.creatorName">{{choiceItem.creator}}</span>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="backPlayModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="insertAppletLivePlayBackPlan()" type="success" size="small"
						v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateAppletLivePlayBackPlan(choiceItem)" type="success" size="small"
						v-if="detailType==1">保存
					</el-button>
				</div>
			</div>
		</el-drawer>
	</div>

</template>

<script>
	import Vue from 'vue';

	export default {
		name: "appletLiveList",
		data() {
			return {
				url: '',
				imageUrl: '',
				videoUrl: '',
				viewPageUrl: '',
				serverUrl: 'rtmp://wxalivepush.weixin.qq.com/live/',
				serverSecretUrl: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				excelUploadId: 1,
				excelUploadUrl: "",
				appletUploadUrl: "",

				// 测试时使用
				// uploadUrl: "http://localhost:8063/files/uploadFiles",
				// excelUploadUrlOrgin: "http://localhost:8063/courseContent/excelImport",
				// weiXinUploadUrl: "http://**************:9999/openapi/uploadAppletMedia",
				// baseUrl: "http://**************:9999/",

				uploadUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
				excelUploadUrlOrgin: "https://biapi.xiaoyujia.com/courseContent/excelImport",
				weiXinUploadUrl: "https://api.xiaoyujia.com/openapi/uploadAppletMedia",
				baseUrl: "https://api.xiaoyujia.com/",
				deleteTips: "确定删除选中直播吗？该操作无法恢复，请谨慎操作!",
				isEdit: false,
				showEdit: true,
				imgModal: false,
				videoModal: false,
				viewModal: false,
				textModal: false,
				liveModal: false,
				liveGroupModal: false,
				backPlayModal: false,
				playBackPlanNowModal: false,
				roomIdListModal: false,
				detailType: 0,
				loading: true,
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				pageSizeOpts: [10, 20, 50, 100],
				list: [],
				groupList: [],
				liveList: [],
				pickerOptions: {
					shortcuts: [{
						text: '今天',
						onClick(picker) {
							picker.$emit('pick', new Date());
						}
					}, {
						text: '明天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '后天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24 * 2);
							picker.$emit('pick', date);
						}
					}]
				},
				switchStateList: [{
						id: 0,
						typeName: "关闭",
						type: "info"
					},
					{
						id: 1,
						typeName: "开启",
						type: "success"
					}
				],
				statusList: [{
					id: 101,
					name: '直播中'
				}, {
					id: 102,
					name: '未开始'
				}, {
					id: 103,
					name: '已结束'
				}, {
					id: 104,
					name: '禁播'
				}, {
					id: 105,
					name: '暂停'
				}, {
					id: 106,
					name: '异常'
				}, {
					id: 107,
					name: '已过期'
				}],
				choiceIndex: '0',
				choiceIndexNow: '0',
				choiceItemIndex: 0,
				choiceItem: {},
				playBackPlanNow: {},
				appletLiveNow: null,
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				searchGroup: null,
				searchType: null,
				searchState: null,
				searchStatus: null,
				quer: {
					search: "",
					current: 1,
					size: 10
				},
				stateList: [{
					value: 0,
					text: "下架",
					type: "info"
				}, {
					value: 1,
					text: "上架",
					type: "success"
				}],
				typeStyleList: [{
						id: 0,
						type: "success"
					},
					{
						id: 1,
						type: "info"
					},
					{
						id: 2,
						type: "warning"
					}, {
						id: 3,
						type: "primary"
					}
				],
				accessToken: '',
				choiceSourceValue: '',
				choiceSourceName: '',
				start: 0,
				limit: 20,
				sourceList: [{
					name: '家姐课堂',
					value: 'xyjCourse'
				}, {
					name: '小羽佳家政',
					value: 'xyjShop'
				}],
				// 默认渠道
				defaultSource: {
					// name: '家姐课堂',
					// value: 'xyjCourse',
					name: '小羽佳家政',
					value: 'xyjShop'
				},
				planTypeList: [{
					name: '排期模式',
					value: 0
				}, {
					name: '轮播模式',
					value: 1
				}],
				intervalTypeList: [{
					name: '分钟',
					value: 0
				}, {
					name: '小时',
					value: 1
				}, {
					name: '天',
					value: 2
				}],
			}
		},
		created() {
			this.choiceSourceValue = this.defaultSource.value
			this.choiceSourceName = this.defaultSource.name
			this.getData()
		},
		methods: {
			choiceSourece(e) {
				this.choiceSourceValue = e
			},
			choiceSourece1(e) {
				this.$set(this.choiceItem, 'shareImg', '')
				this.$set(this.choiceItem, 'coverImg', '')
				this.$set(this.choiceItem, 'feedsImg', '')
				this.$message.success('渠道切换成功，需重新上传图片!')
			},
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceIndex = key
				this.expands = []
				this.showEdit = true
				this.reset()
			},
			copyText(url) {
				let oInput = document.createElement('input');
				oInput.value = url;
				document.body.appendChild(oInput);
				oInput.select();
				document.execCommand("Copy");
				this.$message({
					message: '复制成功',
					type: 'success'
				});
				oInput.remove()
			},
			getData() {
				this.choiceIndexNow = 3
				let data = this.quer
				if (this.choiceIndex == 0) {
					this.$set(data, 'source', this.choiceSourceValue)
					this.$set(data, 'limit', data.size)
					this.$set(data, 'start', (data.current - 1) * data.size)
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'openapi/listAppletLive',
						data: JSON.stringify(data),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							this.loading = false
							if (res.code == 0) {
								this.list = res.data
								this.pageInfo.size = this.limit
								this.pageInfo.total = res.data.length
								for (let item of this.list) {
									this.$set(item, "isEdit", false)
								}
								this.choiceIndexNow = this.choiceIndex
							} else {
								this.list = []
								this.$message.error('未查询到相关直播!')
								this.choiceIndexNow = this.choiceIndex
							}
						},
					})
					this.listAppletLiveGroup()
				} else if (this.choiceIndex == 1) {
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'openapi/pageAppletLiveGroup',
						data: JSON.stringify(data),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							this.loading = false
							if (res.code == 0) {
								this.list = res.data.records
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
								for (let item of this.list) {
									this.$set(item, "isEdit", false)
								}
								this.choiceIndexNow = this.choiceIndex
							} else {
								this.list = []
								this.$message.error('未查询到相关分组!')
								this.choiceIndexNow = this.choiceIndex
							}
						},
					})
				} else if (this.choiceIndex == 2) {
					this.$set(data, 'source', this.choiceSourceValue)
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'openapi/pageAppletLivePlayBackPlan',
						data: JSON.stringify(data),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							this.loading = false
							if (res.code == 0) {
								this.list = res.data.records
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
								for (let item of this.list) {
									this.$set(item, "isEdit", false)
									this.$set(item, "roomIdListArray", this.strToArray(item.roomIdList))
								}
								this.choiceIndexNow = this.choiceIndex
							} else {
								this.list = []
								this.$message.error('未查询到相关回放计划!')
								this.choiceIndexNow = this.choiceIndex
							}
						},
					})

					this.$set(data, 'source', this.choiceSourceValue)
					this.$set(data, 'limit', 200)
					this.$set(data, 'start', 0)
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'openapi/listAppletLive',
						data: JSON.stringify(data),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							this.loading = false
							if (res.code == 0) {
								this.liveList = res.data
							} else {
								this.liveList = []
							}
						},
					})
				}
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.quer.typeState = this.searchState
				this.quer.planStatus = this.searchState
				this.quer.liveStatus = this.searchStatus
				this.quer.liveType = this.searchType
				this.quer.groupId = this.searchGroup

				this.getData()
			},
			// 重置
			reset() {
				this.loading = true
				this.quer.search = ""
				this.quer.typeState = null
				this.quer.planStatus = null
				this.quer.liveStatus = null
				this.quer.liveType = null
				this.quer.groupId = null

				this.quer.current = 1
				this.quer.start = 0
				this.quer.limit = 20
				this.searchState = null
				this.searchStatus = null
				this.searchType = null
				this.searchGroup = null

				this.isEdit = false
				this.choiceSourceValue = this.defaultSource.value
				this.choiceSourceName = this.defaultSource.name
				this.getData()
			},
			listAppletLiveGroup() {
				$.ajax({
					type: "GET",
					url: this.baseUrl + "openapi/listAppletLiveGroup",
					success: res => {
						this.loading = false
						if (res.code == 0) {
							this.groupList = res.data
						} else {
							this.groupList = []
							// this.$message.error('未查询到相关直播分组!')
						}
					}
				})
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "回放") {
					tips = "直播时开启回放功能，在直播结束后可查看回放视频"
				} else if (column.label == "推流地址") {
					tips = "还未开始的推流直播才可查看"
				} else if (column.label == "主播列表") {
					tips = "通过主播名来进行分组"
				} else if (column.label == "计划类型") {
					tips = "排期模式：固定某个时段播放某个回放；轮播模式：每隔一段时间，根据回放列表进行轮播"
				} else if (column.label == "轮播间隔") {
					tips = "轮播模式下的回放切换间隔时间"
				} else if (column.label == "间隔单位") {
					tips = "轮播间隔时间单位"
				} else if (column.label == "排期直播") {
					tips = "需要排期的直播间"
				} else if (column.label == "轮播直播") {
					tips = "需要轮播的直播间回放列表"
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			// 将字符串转化为数组
			strToArray(val) {
				if (!val) {
					return []
				}
				let arrayString = []
				let array = []
				arrayString = val.split(',')
				arrayString.forEach(it => {
					let value = parseInt(it)
					if (value != null && value != 0) {
						array.push(value)
					}
				})
				return array
			},
			// 格式化直播类型
			formatGroup(val) {
				let result = "暂无"
				let name = val.anchor
				for (let item of this.groupList) {
					if (item.anchorList.inclouds(name)) {
						result = item.groupName
						break
					}
				}
				return result
			},
			formatTagStyle(type) {
				return this.typeStyleList[type % 4].type
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.$set(this.choiceItem, "shareImg", res.data.media_id)
					this.$message.success('直播分享图上传成功！')
				} else if (this.uploadImgType == 1) {
					this.$set(this.choiceItem, "feedsImg", res.data.media_id)
					this.$message.success('直播封面图上传成功！')
				} else if (this.uploadImgType == 2) {
					this.$set(this.choiceItem, "coverImg", res.data.media_id)
					this.$message.success('直播背景图上传成功！')
				} else if (this.uploadImgType == 3) {
					this.$set(this.choiceItem.appletLive, "anchorImg", JSON.stringify(res.data).replaceAll('"', ''))
					this.$message.success()
				} else if (this.uploadImgType == 4) {
					this.$set(this.choiceItem.appletLive, "liveBanner", JSON.stringify(res.data).replaceAll('"', ''))
					this.$message.success('直播封面上传成功！')
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadId = index

				if (this.uploadImgType == 0) {
					this.$message.info('推荐上传尺寸：800*640，大小不超过2M')
				} else if (this.uploadImgType == 1) {
					this.$message.info('推荐上传尺寸：800*800，大小不超过2M')
				} else if (this.uploadImgType == 2) {
					this.$message.info('推荐上传尺寸：1080*1920，大小不超过2M')
				} else if (this.uploadImgType == 3) {
					this.$message.info('推荐上传尺寸：800*800，大小不超过2M')
				} else if (this.uploadImgType == 4) {
					this.$message.info('推荐上传尺寸：1080*1920，大小不超过2M')
				}
			},
			// 打开图片预览
			openImg(url) {
				this.imageUrl = url || this.blankImg
				this.imgModal = true
			},
			// 打开视频预览
			openVideo(url) {
				if (url) {
					this.videoUrl = url
				}
				this.videoModal = true
			},
			// 打开预览
			openView(url) {
				this.viewPageUrl = url
				this.viewModal = true
			},
			openText() {
				this.textModal = true
			},
			// 打开链接
			openUrl(url) {
				this.$message.info('若无法查看可在打开页面后再次键入回车按钮！')
				window.open(url)
			},
			openModal(index, index1) {
				if (index == 0) {
					this.detailType = 0
					this.choiceItem = {
						closeLike: "1",
						closeGoods: "1",
						closeComment: "1",
						source: this.choiceSourceValue
					}
					this.liveModal = true
					this.appletUploadUrl = this.weiXinUploadUrl + '/' + this.choiceSourceValue +
						'/image'
				} else if (index == 1) {
					this.choiceItem = {}
					this.liveGroupModal = true
				} else if (index == 2) {
					this.detailType = 0
					this.choiceItem = {
						planType: 0,
						roomIdListArray: [],
						source: this.choiceSourceValue
					}
					this.backPlayModal = true
				} else if (index == 3) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.backPlayModal = true
				} else if (index == 4) {
					this.openUrl('https://live.weixin.qq.com/livemp/broadcast/create')
				} else if (index == 5) {
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'openapi/getPlayBackPlanNow',
						data: JSON.stringify({
							source: this.choiceSourceValue
						}),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.code == 0) {
								this.playBackPlanNow = res.data
								this.$set(this.playBackPlanNow, "roomIdListArray", this.strToArray(this
									.playBackPlanNow.roomIdList))
								this.playBackPlanNowModal = true
								this.getAppletLiveNow()
							} else {
								this.$message.error('当前暂无回放计划哦！')
							}
						},
						error: res => {
							this.$message.error('当前暂无回放计划哦！')
						}
					})
				} else if (index == 6) {
					if (this.detailType == 0) {
						if (!this.choiceItem.roomIdListArray || this.choiceItem.roomIdListArray.length == 0) {
							this.$set(this.choiceItem, "roomIdListArray", [])
						}
					} else if (this.detailType == 1) {
						this.choiceItem = this.list[this.choiceItemIndex]
					}
					this.roomIdListModal = true
				} else if (index == 7) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.liveModal = true
				}
			},
			getAppletLiveNow() {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'openapi/getAppletLiveNow',
					data: JSON.stringify({
						source: this.choiceSourceValue
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.appletLiveNow = res.data
						} else {
							this.appletLiveNow = null
							this.$message.error('当前暂无回放计划哦！')
						}
					},
					error: res => {
						this.appletLiveNow = null
						this.$message.error('当前暂无回放计划哦！')
					}
				})
			},
			//打开详情
			rowClick(id) {

			},
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			getAppletLiveReplay(index) {
				let id = this.list[index].roomid
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'openapi/getAppletLiveReplay',
					data: JSON.stringify({
						source: this.choiceSourceValue,
						roomId: id,
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.openVideo(res.data)
						} else {
							this.$message.error('暂无直播回放哦！')
						}
					},
					error: res => {
						this.$message.error('暂无直播回放哦！')
					}
				})
			},
			getAppletLivePushUrl(index) {
				let id = this.list[index].roomid
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'openapi/getAppletLivePushUrl',
					data: JSON.stringify({
						source: this.choiceSourceValue,
						roomId: id,
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.serverSecretUrl = res.data.replace(this.serverUrl, '')
							this.openText()
						} else {
							this.$message.error('暂时无法获取推流地址！')
						}
					},
					error: res => {
						this.$message.error('暂时无法获取推流地址！')
					}
				})
			},
			getAppletLiveSharedCode(index) {
				let id = this.list[index].roomid
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'openapi/getAppletLiveSharedCode',
					data: JSON.stringify({
						source: this.choiceSourceValue,
						roomId: id,
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.$set(this.list[index], 'posterUrl', res.data.posterUrl)
							this.$set(this.list[index], 'cdnUrl', res.data.cdnUrl)
						} else {
							this.$message.error('暂时无法获取推流地址！')
						}
					},
					error: res => {
						this.$message.error('暂时无法获取推流地址！')
					}
				})
			},
			checkTime() {
				let result = true
				let startTime = this.choiceItem.startTime1
				let endTime = this.choiceItem.endTime1
				if (!startTime || !endTime) {
					result = false
					this.$message.error('开始时间或结束时间未填写！')
				} else if (new Date(endTime).getTime() - new Date(startTime).getTime() <= 0) {
					result = false
					this.$message.error('结束时间应大于开始时间！')
				}
				return result
			},
			// 添加直播
			createAppletLiveRoom() {
				let val = this.choiceItem
				if (!this.checkTime()) {} else if (!val.name) {
					this.$message.error('请填写直播标题！')
				} else if (!val.anchorName) {
					this.$message.error('请填写主播昵称！')
				} else if (!val.anchorName) {
					this.$message.error('请填写主播微信号！')
				} else if (!val.shareImg) {
					this.$message.error('请上传直播间分享图！')
				} else if (!val.feedsImg) {
					this.$message.error('请上传直播间封面图！')
				} else if (!val.coverImg) {
					this.$message.error('请上传直播间背景图！')
				} else if (!val.source) {
					this.$message.error('请选择直播渠道！')
				} else {
					this.$set(val, 'closeReplay', 1)
					this.$set(val, 'closeShare', 1)
					this.$set(val, 'closeKf', 1)
					this.$set(val, 'startTime', new Date(val.startTime1).getTime() / 1000)
					this.$set(val, 'endTime', new Date(val.endTime1).getTime() / 1000)
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'openapi/createAppletLiveRoom',
						data: JSON.stringify(val),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.code == 0) {
								this.$message.success('直播间创建成功!直播间id：' + res.data.roomId)
								this.liveModal = false
								this.reset()
							} else {
								this.formatErrorMsg(res.msg)
							}
						},
						error: res => {
							this.formatErrorMsg(res.msg)
						}
					})
				}
			},
			formatErrorMsg(msg) {
				if (!msg) {
					return this.$message.error(msg)
				}
				if (msg.includes('coverimg')) {
					this.$message.error('直播背景图格式错误！请按推荐格式上传！')
				} else if (msg.includes('shareimg')) {
					this.$message.error('直播分享图格式错误！请按推荐格式上传！')
				} else if (msg.includes('feedsimg')) {
					this.$message.error('直播封面图格式错误！请按推荐格式上传！')
				} else if (msg.includes('startTime') || msg.includes('endTime')) {
					this.$message.error('请检查开始时间和结束时间！开始时间不能距当前过近！')
				} else {
					this.$message.error(msg)
				}
			},
			// 删除权限标签
			closeTags(index) {
				if (this.choiceItem.planType == 0) {
					return this.$message.error('请切换至轮播模式再进行修改！')
				}
				this.choiceItem.roomIdListArray.splice(index, 1)
			},
			// 直播类型
			insertAppletLiveGroup() {
				let val = this.choiceItem
				if (!val.groupName) {
					this.$message.error('请填写直播分组名称！')
				} else {
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'openapi/insertAppletLiveGroup',
						data: JSON.stringify(val),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.code == 0) {
								this.$message.success('直播类型添加成功!')
								this.liveGroupModal = false
								this.list.push(res.data)
							} else {
								this.$message.error(res.msg)
							}
						},
						error: res => {
							this.$message.error(res.msg)
						}
					})
				}
			},
			updateAppletLiveGroup(val) {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'openapi/updateAppletLiveGroup',
					data: JSON.stringify(val),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						this.loading = false
						if (res.code == 0) {
							this.$message.success('分组更新成功!')
							val.isEdit = false
							this.isEdit = false
						} else {
							this.$message.error('分组更新失败！' + res.msg)
						}
					},
				})
			},
			// 添加回放计划
			insertAppletLivePlayBackPlan() {
				let val = this.choiceItem
				if (!val.planTitle) {
					this.$message.error('请填写回放计划名称！')
				} else if (!val.source) {
					this.$message.error('请填写直播渠道！')
				} else if (val.planType == null) {
					this.$message.error('请填写计划类型！')
				} else {
					if (val.roomIdListArray) {
						val.roomIdList = val.roomIdListArray.join(',')
						if (!val.roomIdList.length) {
							val.roomIdList = ''
						}
					}
					this.$set(val, "creator", localStorage.getItem('account') || 'admin')
					this.$set(val, "planStatus", 1)
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'openapi/insertAppletLivePlayBackPlan',
						data: JSON.stringify(val),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.code == 0) {
								this.$message.success('回放计划添加成功!')
								this.backPlayModal = false
								this.list.push(res.data)
							} else {
								this.$message.error(res.msg)
							}
						},
						error: res => {
							this.$message.error(res.msg)
						}
					})
				}
			},
			// 更新直播间
			updateAppletLive(val) {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'openapi/updateAppletLive',
					data: JSON.stringify(val.appletLive),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						this.loading = false
						if (res.code == 0) {
							this.$message.success('直播间更新成功!')
							this.list[this.choiceItemIndex] = this.choiceItem
						} else {
							this.$message.error('直播间更新失败！' + res.msg)
						}
					},
				})
			},
			// 更新直播计划
			updateAppletLivePlayBackPlan(val) {
				if (val.roomIdListArray) {
					val.roomIdList = val.roomIdListArray.join(',')
					if (!val.roomIdList.length) {
						val.roomIdList = ''
					}
				}
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'openapi/updateAppletLivePlayBackPlan',
					data: JSON.stringify(val),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						this.loading = false
						if (res.code == 0) {
							this.$message.success('计划更新成功!')
							this.list[this.getIndex(val.id)] = this.choiceItem
						} else {
							this.$message.error('计划更新失败！' + res.msg)
						}
					},
				})
			},
			// 删除回放计划
			deleteAppletLivePlayBackPlan(val) {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'openapi/deleteAppletLivePlayBackPlan',
					data: JSON.stringify(val),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						this.loading = false
						if (res.code == 0) {
							this.$message.success('计划删除成功!')
							this.$delete(this.list, this.getIndex(val.id))
						} else {
							this.$message.error('计划删除失败！' + res.msg)
						}
					},
				})
			},
			// 更改直播
			editAppletLiveRoom(val) {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'openapi/editAppletLiveRoom',
					data: JSON.stringify(val),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.$message.success('直播更新成功!')
							this.list[this.choiceItemIndex] = this.choiceItem
						} else {
							this.$message.error('直播更新失败！' + res.msg)
						}
					},
				})
			},
			beforeImgUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!');
				}
				return isLt2M;
			}
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>