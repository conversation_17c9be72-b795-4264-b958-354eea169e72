<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">
                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="月份">
                                <el-date-picker
                                        @change="getData()"
                                        v-model="dto.month"
                                        type="month"
                                        format="yyyy-MM"
                                        value-format="yyyy-MM"
                                        placeholder="选择月">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="类型">
                                <Select filterable clearable style="width: 120px"
                                        v-model="dto.type"
                                        @on-change="getData()">
                                    <Option value="">请选择</Option>
                                    <Option :value="1">加班</Option>
                                    <Option :value="2">请假</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row style="margin-top: 10px">
                        <el-col :span="24">
                            <el-form-item>
                                <el-button type="success" icon="el-icon-date"
                                           @click="leave('请假',2,2,'name3')">请假
                                </el-button>
<!--type事项类型 1 加班 2 请假  deductionType扣款类型 1 奖励 2扣款-->
                                <el-button type="success" icon="el-icon-date"
                                           @click="leave('加班',1,1,'name4')">加班
                                </el-button>
                                <el-button icon="el-icon-refresh" @click="getData">刷新
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-table :data="list" class="table" ref="multipleTable"
                      :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}"
                      v-loading="loading">
                <el-table-column
                        width="50"
                        prop="type"
                        label="类型">
                    <template slot-scope="scope">
                        <span v-if="scope.row.type===1">加班</span>
                        <span v-if="scope.row.type===2">请假</span>
                    </template>
                </el-table-column>
                <el-table-column
                        width="80"
                        prop="deductionType"
                        label="扣款类型">
                    <template slot-scope="scope">
                        <span v-if="scope.row.deductionType===1">奖励</span>
                        <span v-if="scope.row.deductionType===2">扣款</span>
                    </template>
                </el-table-column>
                <el-table-column
                        width="150"
                        prop="state"
                        label="开始时间">
                    <template slot-scope="scope">
                        <span>{{scope.row.startTime}}</span>
                        <p>{{scope.row.endTime}}</p>
                    </template>
                </el-table-column>
                <el-table-column
                        width="90"
                        prop="social"
                        label="时长(小时)">
                    <template slot-scope="scope">
                        <span>{{scope.row.duration/60}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        width="80"
                        prop="amount"
                        label="金额">
                </el-table-column>
                <el-table-column
                        width="170"
                        prop="describe"
                        label="描述">
                </el-table-column>
<!--                <el-table-column-->
<!--                        width="200"-->
<!--                        prop="bankCard"-->
<!--                        label="被调度项目名称">-->
<!--                    <template slot-scope="scope">-->
<!--                        <span>{{scope.row.project!=null?scope.row.project.name:""}}</span>-->
<!--                    </template>-->
<!--                </el-table-column>-->
                <el-table-column
                        fixed="right"
                        width="150"
                        label="操作">
                    <template slot-scope="scope">
                        <el-row>
                            <el-col :span="24">
                                <div style="display: flex">
                                    <el-button
                                            size="small "
                                            type="text"
                                            @click="deleteOrder(scope.row.id)"
                                            icon="el-icon-delete">
                                        删除
                                    </el-button>
                                </div>
                            </el-col>
                        </el-row>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>

</template>

<script>
    import moment from "moment";

    export default {
        props: ['projectId', 'employee'],
        data() {
            return {
                bindLoading: false,
                loading: true,
                loadingExcel: false,
                list: null,
                dto: {
                    month: null,
                    projectId: this.projectId,
                    employeeId: this.employee.id,
                },
            };
        },
        components: {},
        watch: {
            projectId: {
                handler(newValue, oldValue) {
                    this.projectId = newValue;
                    this.getData();
                }
            },
            employee: {
                handler(newValue, oldValue) {
                    this.employee = newValue;
                }
            },
        },
        created() {
            this.dto.month = moment(new Date()).format('YYYY-MM');
            this.getData();
        },
        computed: {},
        methods: {
            update(item) {
                this.$postData("siteEmployee_update", item).then(res => {
                })
            },
            deleteOrder(id) {
                this.$confirm('此操作将永久删除该员工事项, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$postUrl("siteWagesMatter_delete", id).then(res => {
                        this.getData()
                    });
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },
            leave(name, type, deductionType, name1) {
              let employee = this.employee;
              let siteWagesDetails={
                projectId:this.projectId,
                employeeId:employee.id,
                wagesMonth:this.dto.month+"-01",
              }
                this.$postData("selectSiteWagesDetail", siteWagesDetails).then(res => {
                  if (res.status === 200) {
                    if(res.data){
                      let data = {
                        name: employee.realName + "-" + name,
                        tabName: name1,
                        content: "siteLeave",
                        type: type,
                        deductionType: deductionType,
                        name1: name
                      };
                      this.$emit('init-choose', data, employee);
                    }else {
                      this.$message({
                        message: '请先创建当月工资',
                        type: 'warning'
                      });
                    }
                  }
                });
            },
            getData() {
                this.$postData("siteWagesMatter_getByMonth", this.dto).then(res => {
                    this.loading = false;
                    if (res.status === 200) {
                        this.list = res.data;
                    }
                });
            },
            exportExcel() {
                this.loadingExcel = true;
                this.$postData("tsExportList", this.dto, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.loadingExcel = false;
                    this.blobExport({
                        tablename: "投诉工单",
                        res: res
                    });
                });
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
        }
    };
</script>

<style scoped>
    .handle-box {
        /*margin-bottom: 10px;*/
        font-weight: bold !important;
    }

    .el-form-item__label {
        font-weight: bold !important;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;

    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }

    .container {
        padding: 0px !important;
        background: #fff;
        border: none !important;
        border-radius: 5px;
    }

    .el-date-editor .el-range-separator {
        padding: 0 5px;
        line-height: 32px;
        width: 7%;
        color: #303133;
    }
</style>
