<template>

    <div class="table">

        <div class="container">
            <el-page-header @back="goBack" content="蜂窝编辑">
            </el-page-header>
            <el-divider></el-divider>
            <el-row>
                <el-col :span="16">
                    <el-form :model="dom" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                        <el-form-item label="参数名称" prop="name">
                            <el-input v-model="dom.name"></el-input>
                        </el-form-item>


                        <el-form-item label="参数介绍" prop="detail">
                            <el-input v-model="dom.detail" type="textarea" maxlength="150"
                                      :autosize="{ minRows: 4}"
                                      show-word-limit>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="数据更新" prop="dayUp">
                            <el-radio-group v-model="dom.isDay">
                                <el-radio :label=1>每日更新</el-radio>
                                <el-radio :label=0>手动更新</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="期望值" prop="hopeParam">
                            <el-input v-model="dom.hopeParam"></el-input>
                        </el-form-item>
                        <el-form-item label="排序比重" prop="orderNum">
                            <el-input-number v-model="dom.orderNum":min="0" :max="10" label="描述文字"></el-input-number>
                        </el-form-item>
                        <el-form-item label="sql语句" prop="sqlText">
                            <el-input v-model="dom.sqlText" type="textarea" maxlength="1500"
                                      :autosize="{ minRows: 4}"
                                      show-word-limit>
                            </el-input>
                            <el-button type="primary" @click="sqlText()">测试查询</el-button>
                            <span style="color: #f00f14;padding-left: 20px">测试结果：<b>{{text}}</b></span>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="submitForm('ruleForm')">立即{{dom.id==null?'创建':'保存'}}</el-button>
                            <el-button @click="resetForm('ruleForm')">重置</el-button>
                            <el-button @click="dom.status=0,submitForm('ruleForm')" type="danger" v-if="dom.id!==null&&dom.status==1">停用</el-button>
                            <el-button @click="dom.status=1,submitForm('ruleForm')" type="danger" v-if="dom.id!==null&&dom.status==0">恢复启用</el-button>
                        </el-form-item>

                    </el-form>
                </el-col>
                <el-col :span="8">
                    <el-card style="padding: 20px;margin-left: 20px">
                        <h2>注意事项</h2>
                        <ul style="color: #9e9e9e;padding-top: 5px">
                            <li>1.非每日更新的参数将不会自动计算</li>
                            <li>2.每日更新的参数将在每日23:50自动进行数据更新</li>
                            <li>3.sql语句请不要出现任务更新，插入的语句。否则不会进行操作。</li>
                            <li>4.sql语句获取参数请命名为“params”</li>
                            <li>5.排序比重值越大，越靠近目标值</li>
                            <li>6.期望值为0时。数据不继续期望值比对</li>
                        </ul>
                    </el-card>
                </el-col>

            </el-row>

        </div>
    </div>
</template>

<script>
    export default {
        name: "parameterEdit",
        data(){
            return{
                id:this.$route.query.id,
                rules: {
                    name: [
                        {required: true, message: '请输入活动名称', trigger: 'blur'},
                        {min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur'}
                    ],
                    sqlText: [
                        {required: true, message: '请输入sqlText', trigger: 'blur'},
                        {min: 10, max: 1500, message: '长度在 10 到 250 个字符', trigger: 'blur'}
                    ],
                },
                text:'未测试',
                dom:{
                    id:null,
                    name:null,
                    detail:null,
                    sqlText:null,
                    type:null,
                    status:1,
                    creatDate:null,
                    creatPerson:localStorage.getItem("realName"),
                    isDay:1,
                    orderNum:null,
                    hopeParam:0,
                }
            }
        },
        created(){
            if (this.id!==null && this.id!==undefined){
                this.getParameterInfo()
            }
        },
        methods:{
            goBack() {
                this.$router.go(-1)
            },
            getParameterInfo(){
                this.$getData("getParameterInfo", {id:this.id}).then(res => {
                    if (res.status == 200) {
                        this.dom = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            sqlText(){
                this.text="请稍等。语句验证中。"
                this.$postData("sqlText",this.dom,null).then(res => {
                    if (res.status == 200) {
                        this.text = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        console.log(this.dom)
                        this.$postData("saveParameterInfo", this.dom, {}).then(res => {
                            if (res.status == 200) {
                                this.$message.success("保存成功" );
                                this.text='未测试'
                                if (this.id!==null && this.id!==undefined){

                                }else {
                                    this.resetForm('ruleForm')
                                }

                            } else {
                                this.$message.error("保存失败。请稍后重试");
                            }
                        })
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            resetForm(formName) {
                this.$refs[formName].resetFields();
            },
        }
    }
</script>

<style scoped>

</style>
