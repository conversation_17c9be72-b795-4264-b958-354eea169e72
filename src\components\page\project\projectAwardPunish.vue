<template>
    <div class="table">
       <!-- <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item>
                    <i class="el-icon-lx-cascades"></i> 分润奖罚
                </el-breadcrumb-item>
            </el-breadcrumb>
        </div>-->
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">

                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="项目编号">
                                <el-input
                                        v-model="awardQuery.projectNo"
                                        placeholder="项目编号"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="项目名称">
                                <el-input
                                        v-model="awardQuery.name"
                                        placeholder="项目名称"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="奖惩类型">
                                <Select v-model="awardQuery.type" filterable style="width: 200px">
                                    <Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>

                        <el-col style="text-align:right" :span="7">
                            <el-button type="success" style="width:30%"  @click="saveModal=true">添加</el-button>
                        <!--  <el-button type="success" style="width:30%"  @click="ruleModal=true">奖励规则</el-button>-->
                        </el-col>

                    </el-row>
                    <el-row>

                        <el-col :span="10">
                            <el-form-item label="结算时间">
                                <el-date-picker
                                        v-model="awardQuery.settlementTime"
                                        type="daterange"
                                        align="right"
                                        unlink-panels
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        format="yyyy 年 MM 月 dd 日"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>



                        <el-col :span="6">
                            <el-form-item label="结算月份">
                                <el-date-picker
                                        v-model="awardQuery.settlementTime2"
                                        type="month"
                                        placeholder="选择月"
                                        value-format="yyyy-MM-dd">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>


                        <el-col style="text-align:right" :span="7">
                            <el-button type="primary" style="width:30%" @click="query()">搜索</el-button>
                            <el-button type="success" style="width:30%" @click="exportList">导出Excel</el-button>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div style="text-align:right">{{theader}}</div>
            <el-table :data="data" border class="table" ref="multipleTable">
                <el-table-column
                        prop="projectManage.projectNo"
                        label="项目编号"
                        width="120"
                ></el-table-column>
                <el-table-column
                        prop="projectManage.name"
                        label="项目名称"
                        width="200"
                ></el-table-column>
                <el-table-column
                        prop="typeDesc"
                        label="奖惩类型"
                        width="120"
                ></el-table-column>
                <el-table-column
                        prop="money"
                        label="奖励/惩罚金额"
                        width="120"
                ></el-table-column>
                <el-table-column
                        prop="costName"
                        label="费用名称"
                        width="250"
                ></el-table-column>
                <el-table-column
                        prop="settlementTime"
                        label="结算时间"
                        width="130"
                ></el-table-column>
                <el-table-column
                        prop="employee.realName"
                        label="结算人"
                ></el-table-column>
                <el-table-column
                        label="操作"
                        width="140">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="deleteAward(scope.row.id)" type="danger">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="text-align:right">{{totalIncome}}</div>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="10"/>
            </div>
        </div>

        <Modal v-model="saveModal" class="Modal" :width="screenWidth" title="添加" :mask-closable="false">
            <div class="addBody">

                <project-award-choose v-if="saveModal" @init-choose="initChooseProject"
                                       @close-modal="closeCurrModal"></project-award-choose>
            </div>
            <div slot="footer">
            </div>
        </Modal>
            <!--规则-->
        <Modal v-model="ruleModal" class="Modal" :width="width" title="添加" :mask-closable="false">
            <div class="addBody">

                <project-rule-choose v-if="ruleModal" @init-choose="initChooseProject"
                                      @close-modal="closeCurrModal"></project-rule-choose>
            </div>
            <div slot="footer">
            </div>
        </Modal>
    </div>
</template>

<script>
    import projectAwardChoose from '@/components/page/project/choose/projectAwardChoose.vue'
    import projectRuleChoose from '@/components/page/project/choose/projectRuleChoose.vue'
    import {formatDate} from '@/components/common/utils.js'

    export default {
        data() {
            return {
                tableData: [],
                pageSizeOpts:[10,20,30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                theader: "",
                screenWidth: '40%',//新增对话框 宽度
                width: '45%',//新增对话框 宽度
                saveModal: false,
                ruleModal:false,
                saveLoading: false,
                registerTime: [],
                lastLoginTime: [],
                cityList: [
                    {
                        value: '',
                        label: '请选择'
                    },
                    {
                        value: '1',
                        label: '奖励'
                    },
                    {
                        value: '2',
                        label: '惩罚'
                    },
                ],
                awardQuery: {
                    projectNo: null,
                    employeeId: null,
                    name: null,
                    costName: null,
                    settlementTime: null,
                    SettlementTime1: null,
                    SettlementTime2: null,
                    pageSize: "",
                    pageIndex: "",
                    employeeName: null,
                    type:null,
                    money:null
                },
                totalIncome:null
            };
        },
        components: {
            'projectAwardChoose': projectAwardChoose,
            "projectRuleChoose":projectRuleChoose
        },
        created() {
            this.getData();
        },
        computed: {
            data() {
                return this.tableData.filter(d => {
                    d.settlementTime = d.settlementTime.substring(0, 10);
                    return d;
                });
            }
        },
        methods: {
            getData() {
                this.$postData("projectAward_getByList", this.awardQuery, {}).then(res => {
                    if (res.meta.state == 200) {

                        this.tableData = res.data.tbody.records;
                        this.pageInfo.current = res.data.tbody.current;
                        this.pageInfo.pages = res.data.tbody.pages;
                        this.pageInfo.size = res.data.tbody.size;
                        this.pageInfo.total = res.data.tbody.total
                        this.income(res.data.tbody.records);
                        this.awardQuery.settlementTime1 = null;
                        this.awardQuery.settlementTime = null;
                    } else {
                        this.$message.error("查询失败，" + res.meta.msg);
                    }
                })

            },
            exportList(){
                if (this.awardQuery.settlementTime != null) {
                    this.awardQuery.settlementTime1 = this.awardQuery.settlementTime[1];
                    this.awardQuery.settlementTime = this.awardQuery.settlementTime[0];
                }
                this.$postData("projectAward_export", this.awardQuery, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tablename: "分润奖惩",
                        res: res
                    });
                });
            },
            blobExport({ tablename, res }) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], { type: "application/vnd.ms-excel" });
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
            query() {
                this.awardQuery.pageIndex=1;
                console.log(this.incomeQuery);
                if (this.awardQuery.settlementTime != null) {
                    this.awardQuery.settlementTime1 = this.awardQuery.settlementTime[1];
                    this.awardQuery.settlementTime = this.awardQuery.settlementTime[0];
                }
                this.getData();
            },
            /*
            * 计算总支出收入
            * */
            income(data) {
                let income = 0;
                let expend = 0;
                if (data) {
                    for (let i = 0; i < data.length; i++) {
                        let res = data[i];
                        if (res.type == 1) {
                            income += res.money;
                        } else if (res.type == 2) {
                            expend += res.money;
                        }
                    }
                    income= parseFloat(income).toFixed(2);
                    expend= parseFloat(expend).toFixed(2);
                    this.theader = "奖励: " + income + " 惩罚:" + expend;
                    let total=income+expend;
                    total= parseFloat(total).toFixed(2);
                    this.totalIncome="合计总收益:"+total;
                }

            },
            // 页码大小
            onPageSizeChange(size) {
                this.awardQuery.pageSize = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                console.log(index)
                this.awardQuery.pageIndex = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData();
            },
            deleteAward(id) {
                this.$confirm('此操作将永久性删除, 是否继续?', '提示', {
                    confirmButtonText: '删除',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$getData("projectAward_delete", {id:id}).then(res => {
                        if (res.status == 200) {
                            this.$message.success("删除成功");
                            this.getData();
                        } else {
                            this.$message.error("删除失败" + res.msg);
                        }
                    })

                }).catch(() => {

                });
            },

        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
