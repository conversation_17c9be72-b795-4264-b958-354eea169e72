<template>
	<div class="container">
		<div class="handle-box">
			<el-form :inline="true" :model="pageInfo" class="demo-form-inline">
				<el-form-item label="订单号">
					<el-input v-model="dom.billNo" placeholder="订单号" clearable></el-input>
				</el-form-item>
				<el-form-item label="会员号">
					<el-input v-model="dom.memberAccount" placeholder="会员号" clearable></el-input>
				</el-form-item>
				<el-form-item label="产品名">
					<el-input v-model="dom.productName" placeholder="产品名" clearable></el-input>
				</el-form-item>
				<el-form-item label="订单状态">
					<el-select v-model="dom.orderState" placeholder="请选择">
						<el-option v-for="item in orderStateList" :key="item.value" :label="item.text"
							:value="item.value">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="退款记录">
					<el-select v-model="dom.refundRecord" placeholder="请选择">
						<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="结算时间">
					<el-date-picker v-model="value" type="daterange" range-separator="至" start-placeholder="开始日期"
						end-placeholder="结束日期" value-format="yyyy-MM-dd" align="right">
					</el-date-picker>
				</el-form-item>
				<el-form-item label="操作时间">
					<el-date-picker v-model="value2" type="daterange" range-separator="至" start-placeholder="开始日期"
						end-placeholder="结束日期" value-format="yyyy-MM-dd" align="right">
					</el-date-picker>
				</el-form-item>
				<el-button type="success" @click="query()" icon="el-icon-search">搜索</el-button>
				<el-button icon="el-icon-refresh" @click="reset()">重置</el-button>
				<el-button @click="excel()">导出</el-button>
			</el-form>
		</div>
		<el-table :data="list" :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" stripe
			v-loading="loading" class="table" border style="width: 100%">
			<el-table-column prop="billNo" label="订单号" width="160">
			</el-table-column>
			<el-table-column prop="sourceBillNo" label="总订单号" width="160">
			</el-table-column>
			<el-table-column prop="memberAccount" label="会员号" width="150">
			</el-table-column>
			<el-table-column prop="productName" label="产品名" width="150">
			</el-table-column>
			<el-table-column prop="orderState" label="订单状态" width="110">
				<template slot-scope="scope">
					<span>{{returnState(scope.row.orderState)}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="realTotalAmount" label="订单结算金额" width="110">
			</el-table-column>
			<el-table-column prop="paySettlementTime" label="结算时间" width="160">
			</el-table-column>
			<el-table-column prop="creatDate" label="操作时间" width="160">
			</el-table-column>
			<el-table-column prop="refundedAmount" label="已退款金额" width="100">
			</el-table-column>
			<el-table-column fixed="right" label="操作" width="170">
				<template slot-scope="scope">
					<div style="display: flex">
						<el-button size="small " type="primary" @click="journal(scope.row.billNo)">
							日志
						</el-button>
						<el-button size="small " icon="el-icon-edit-outline" @click="updateChannel(scope.row)">
							编辑
						</el-button>
					</div>
				</template>
			</el-table-column>
		</el-table>
		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="this.dom.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="10" />
		</div>
		<el-dialog title="中介订单退款" :visible.sync="dialogVisible" width="30%" center>
			<el-form ref="form" label-width="100px">
				<el-form-item label="订单号">
					{{ dto.billNo }}
				</el-form-item>
				<el-form-item label="剩余金额">
					{{ remainingAmount }}
				</el-form-item>
				<el-form-item label="退款金额">
					<el-input-number size="large" v-model="dto.refundAmount" controls-position="right" :precision="2"
						:min="0" min="0" :max="remainingAmount"></el-input-number>
				</el-form-item>
				<el-form-item>
					<el-button @click="dialogVisible = false">取 消</el-button>
					<el-button type="primary" @click="determine()">确 定</el-button>
				</el-form-item>
			</el-form>
		</el-dialog>
		<el-drawer title="退款日志!" :visible.sync="table" direction="rtl" size="530px">
			<el-table :data="list2">
				<el-table-column property="billNo" label="订单号" width="150"></el-table-column>
				<el-table-column property="operationName" label="姓名" width="100"></el-table-column>
				<el-table-column property="creatDate" label="时间" width="180"></el-table-column>
				<el-table-column property="refundAmount" label="退款金额" width="100"></el-table-column>
			</el-table>
		</el-drawer>
	</div>
</template>

<script>
	export default {
		name: "refund",
		data() {
			return {
				pageInfo: {
					total: 10,
					size: 10,
					current: 1,
					pages: 1
				},
				pageSizeOpts: [10, 15, 20],
				loading: false,
				table: false,
				dialogVisible: false,
				list: [],
				list2: [],
				value: [],
				value2: [],
				dom: {
					billNo: null,
					memberAccount: null,
					startTime: null,
					endTime: null,
					startTime2: null,
					endTime2: null,
					refundRecord: null,
					channel: null,
					sourceBillNo: null,
					size: 10,
					current: 1,
				},
				dto: {
					billNo: null,
					memberAccount: null,
					realTotalAmount: null,
					paySettlementTime: null,
					refundAmount: null,
					channel: null,
					sourceBillNo: null,
					operationId: localStorage.getItem("id"),
					operationName: localStorage.getItem("realName"),
				},
				options: [{
					value: 1,
					label: '有退款记录'
				}, {
					value: 0,
					label: '无退款记录'
				}, ],
				remainingAmount: '',
				orderStateList: [{
						value: 10,
						text: '已接单'
					},
					{
						value: 20,
						text: '派单待确认'
					},
					{
						value: 30,
						text: '拒绝接单'
					},
					{
						value: 40,
						text: '已派单'
					},
					{
						value: 50,
						text: '执行中'
					},
					{
						value: 60,
						text: '开始服务'
					},
					{
						value: 70,
						text: '服务结束'
					},
					{
						value: 80,
						text: '已完成'
					},
					{
						value: 90,
						text: '已评价'
					},
					{
						value: 99,
						text: '已取消'
					}
				]
			};
		},
		created() {
			this.getData()
		},
		methods: {
			returnState(value) {
				let result = '未知'
				this.orderStateList.forEach(item => {
					if (value == item.value) {
						result = item.text
					}
				})
				return result
			},
			setTime2() {
				this.dom.startTime2 = null
				this.dom.endTime2 = null
				if (this.value2.length === 0) {
					this.dom.startTime2 = null
					this.dom.endTime2 = null
				} else {
					this.dom.startTime2 = this.value2[0]
					this.dom.endTime2 = this.value2[1] + " 23:59:59"
				}
			},
			setTime() {
				this.dom.startTime = null
				this.dom.endTime = null
				if (this.value.length === 0) {
					this.dom.startTime = null
					this.dom.endTime = null
				} else {
					this.dom.startTime = this.value[0]
					this.dom.endTime = this.value[1] + " 23:59:59"
				}
			},
			getData() {
				this.setTime()
				this.setTime2()
				this.loading = true
				this.$postData("selectOrderInterRefundIPage", this.dom).then(res => {
					this.loading = false;
					if (res.status === 200) {
						this.list = res.data.records;
						this.pageInfo.current = res.data.current;
						this.pageInfo.size = res.data.size;
						this.pageInfo.total = res.data.total
						this.list.forEach(item => {
							this.$set(item, 'orderState', 80)
						})
					}
				})
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.dom.size = size;
				this.getData();
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.dom.current = index;
				this.getData();
			}, //删除活码
			//编辑
			updateChannel(OrderInterRefund) {
        this.$getData("getOrderResidueMoney", {
          billNo: OrderInterRefund.billNo
        }).then(res => {
          this.remainingAmount = res
        })
				this.dto.billNo = OrderInterRefund.billNo;
				this.dto.memberAccount = OrderInterRefund.memberAccount;
				this.dto.realTotalAmount = OrderInterRefund.realTotalAmount;
				this.dto.paySettlementTime = OrderInterRefund.paySettlementTime;
				this.dto.channel = OrderInterRefund.channel;
				this.dto.sourceBillNo = OrderInterRefund.sourceBillNo;
				this.dialogVisible = true;
				// this.remainingAmount = OrderInterRefund.realTotalAmount - OrderInterRefund.refundedAmount;
			},
			//搜索
			query() {
				this.dom.size = 10;
				this.dom.current = 1;
				this.getData();
			},
			//重置
			reset() {
				this.dom.billNo = null;
				this.dom.productName = null;
				this.dom.orderState = null;
				this.dom.memberAccount = null;
				this.dto.refundAmount = null;
				this.value = []
				this.getData();
			},
			//确定
			determine() {
				this.dialogVisible = false
				if ('0' !== this.dto.refundAmount) {
					this.addOrderInterRefund(this.dto)
				}
			},
			//添加日志
			addOrderInterRefund(orderInterRefund) {
				this.$postData("addOrderInterRefund", orderInterRefund).then(res => {
					if (res.status === 200) {
						if (res.data === 1) {
							this.$message({
								message: '记录成功',
								type: 'success'
							});
							this.reset();
						} else {
							this.$message({
								showClose: true,
								message: '记录失败',
								type: 'error'
							});
						}
					}
				})
			},
			//查看日志
			journal(billNo) {
				this.$getData("selectOrderInterRefundByBillNo", {
					billNo: billNo
				}).then(res => {
					if (res.status === 200) {
						this.list2 = res.data
						this.table = true
					}
				})
			},
			//导出
			excel() {
				this.setTime()
				this.setTime2()
				if (this.value.length === 0) {
					return this.$message.error("数据量过大。请选择结算时间区间再下载。")
				}
				this.loading = true;
				this.$postData("orderInterRefundExcel", this.dom, {
					responseType: "arraybuffer"
				}).then(res => {
					this.loading = false;
					this.blobExport({
						tablename: "退款管理信息",
						res: res
					});
				})
			},
			//导出
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
		}
	}
</script>

<style scoped>
	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	.demo-table-expand label {
		width: 90px;
		color: #99a9bf;
	}

	.demo-table-expand .el-form-item {
		margin-right: 0;
		margin-bottom: 0;
		width: 50%;
	}
</style>