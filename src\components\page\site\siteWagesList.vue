<template>
  <div class="table">
    <div class="container">
      <div class="handle-box">
        <el-form ref="form">
          <el-row>
            <el-col :span="6">
              <el-form-item label="项目名称">
                <el-input
                    clearable
                    @input="query()"
                    v-model="dto.projectName"
                    placeholder="项目名称"
                    style="width:190px"
                    class="handle-input mr10"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="月份">
                <el-date-picker
                    @change="query()"
                    v-model="dto.month"
                    type="month"
                    format="yyyy-MM"
                    value-format="yyyy-MM"
                    placeholder="选择月">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="服务员工">
                <el-input
                    @input="query()"
                    clearable
                    v-model="dto.name"
                    style="width: 180px"
                    placeholder="服务员工">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="经理">
                <el-input
                    clearable
                    @input="query()"
                    v-model="dto.manager"
                    placeholder="经理"
                    style="width:190px"
                    class="handle-input mr10"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row style="margin-top: 10px">
            <el-col :span="24">
              <el-form-item>
                <el-button type="info"
                           :loading="loadingExcel"
                           @click="download"
                           icon="el-icon-download">导出
                </el-button>
                <el-button type="warning" icon="el-icon-upload2" @click="isModal1=true">导入服务人员
                </el-button>
                <el-button type="warning" icon="el-icon-upload2" @click="isModal=true">导入人员信息
                </el-button>
                <el-button type="success" icon="el-icon-coin"
                           :loading="calculationLoading"
                           @click="calculationWagesMonthBatch()">批量计算
                </el-button>
                <el-button type="primary" :loading="bindLoading" icon="el-icon-connection"
                           @click="bind">绑定人员
                </el-button>
                <el-button type="success" icon="el-icon-news"
                           :loading="wagesMonthBatchLoading"
                           @click="insertWagesMonth()">创建工资
                </el-button>
                <el-button icon="el-icon-refresh" @click="getData">刷新
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-table :data="list" class="table" ref="multipleTable" height="600"
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000',textAlign: 'center'}"
                :cell-style="{textAlign: 'center'}"
                @selection-change="handleSelectionChange"
                @mousedown.native="mouseDownHandler"
                @mouseup.native="mouseUpHandler"
                @mousemove.native="mouseMoveHandler"
                v-loading="loading">
        <el-table-column
            type="selection"
            width="42">
        </el-table-column>
        <el-table-column
                         width="140"
                         prop="project.name"
                         label="项目名称">
          <template slot-scope="scope">
            <div style="text-align: center;">
              <a style="text-decoration:underline;" @click="goYun(scope.row.project.no)"
                 target="_Blank">{{ scope.row.project.name }}</a>
            </div>
          </template>
        </el-table-column>
        <el-table-column
                         width="142"
                         prop="project.name"
                         label="总订单号">
          <template slot-scope="scope">
            <div style="text-align: center;">
              {{ scope.row.project.no }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
            fixed
                         width="100"
                         prop="name"
                         label="服务员工">
          <template slot-scope="scope">
            <span>{{ scope.row.employee.realName }}{{ scope.row.employee.no }}</span>
          </template>
        </el-table-column>
        <el-table-column
            width="60"
            prop="employee2.realName"
            label="经理">
        </el-table-column>
        <el-table-column
            width="95"
            prop="wagesMonth"
            label="工资月份">
        </el-table-column>
        <el-table-column
            width="72"
            prop="basePay"
            label="基本工资">
        </el-table-column>
        <el-table-column
            width="72"
            prop="appointmentAmount"
            label="约定金额">
        </el-table-column>
        <el-table-column
            width="72"
            prop="contractAmount"
            label="合同金额">
        </el-table-column>
        <el-table-column
            width="72"
            prop="workOvertimeAmount"
            label="加班工资">
        </el-table-column>
        <el-table-column
            width="48"
            prop="fullAmount"
            label="满勤">
          <!--<template slot-scope="scope">-->
          <!--<el-input-->
          <!--@blur="update(scope.row)"-->
          <!--v-model="scope.row.fullAmount"-->
          <!--style="width: 65px"-->
          <!--placeholder="满勤">-->
          <!--</el-input>-->
          <!--</template>-->
        </el-table-column>
        <el-table-column
            width="72"
            prop="otherAmount"
            label="其他奖励">
          <template slot-scope="scope">
            <el-input
                @blur="update(scope.row)"
                v-model="scope.row.otherAmount"
                style="width: 65px"
                placeholder="其他奖励">
            </el-input>
          </template>
        </el-table-column>
        <el-table-column
            width="72"
            prop="amountRemark"
            label="奖励备注">
          <template slot-scope="scope">
            <el-input
                @blur="update(scope.row)"
                v-model="scope.row.amountRemark"
                style="width: 65px"
                placeholder="奖励备注">
            </el-input>
          </template>
        </el-table-column>
        <el-table-column
            width="72"
            prop="workAmount"
            label="岗位工资">
        </el-table-column>
        <el-table-column
            width="72"
            prop="shouldAmount"
            label="应发工资">
        </el-table-column>
        <el-table-column
            width="72"
            prop="deductionAmount"
            label="扣款金额">
          <template slot-scope="scope">
            <el-input
                @blur="update(scope.row)"
                v-model="scope.row.deductionAmount"
                style="width: 65px"
                placeholder="扣款金额">
            </el-input>
          </template>
        </el-table-column>
        <el-table-column
            width="72"
            prop="deductionRemark"
            label="扣款备注">
          <template slot-scope="scope">
            <el-input
                @blur="update(scope.row)"
                v-model="scope.row.deductionRemark"
                style="width: 65px"
                placeholder="扣款备注">
            </el-input>
          </template>
        </el-table-column>
        <el-table-column
            width="65"
            prop="socialAmount"
            label="医社保">
          <!--<template slot-scope="scope">-->
          <!--<el-input-->
          <!--@blur="update(scope.row)"-->
          <!--:disabled="!scope.row.siteEmployee.social"-->
          <!--v-model="scope.row.socialAmount"-->
          <!--style="width: 80px"-->
          <!--placeholder="医社保">-->
          <!--</el-input>-->
          <!--</template>-->
        </el-table-column>
<!--        <el-table-column-->
<!--            width="59"-->
<!--            prop="providentAmount"-->
<!--            label="公积金">-->
<!--          &lt;!&ndash;<template slot-scope="scope">&ndash;&gt;-->
<!--          &lt;!&ndash;<el-input&ndash;&gt;-->
<!--          &lt;!&ndash;@blur="update(scope.row)"&ndash;&gt;-->
<!--          &lt;!&ndash;:disabled="!scope.row.siteEmployee.provident"&ndash;&gt;-->
<!--          &lt;!&ndash;v-model="scope.row.providentAmount"&ndash;&gt;-->
<!--          &lt;!&ndash;style="width: 70px"&ndash;&gt;-->
<!--          &lt;!&ndash;placeholder="公积金">&ndash;&gt;-->
<!--          &lt;!&ndash;</el-input>&ndash;&gt;-->
<!--          &lt;!&ndash;</template>&ndash;&gt;-->
<!--        </el-table-column>-->
        <!--<el-table-column-->
        <!--width="80"-->
        <!--prop="personalAmount"-->
        <!--label="所得税">-->
        <!--<template slot-scope="scope">-->
        <!--<el-input-->
        <!--@blur="update(scope.row)"-->
        <!--v-model="scope.row.personalAmount"-->
        <!--style="width: 70px"-->
        <!--placeholder="所得税">-->
        <!--</el-input>-->
        <!--</template>-->
        <!--</el-table-column>-->
        <el-table-column
            width="72"
            prop="actualAmount"
            label="实发工资">
        </el-table-column>
        <el-table-column
            fixed="right"
            width="70"
            label="操作">
          <template slot-scope="scope">
            <el-row>
              <el-col :span="24">
                <div style="display: flex">
                  <el-button
                      size="small "
                      type="text"
                      @click="goProject(scope.row.projectId)"
                      icon="el-icon-thumb">
                    详情
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination" style="height: 1px">
        <Page :total="pageInfo.total"
              @on-change="onChange"
              :current="this.dto.pageNum"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onPageSizeChange"
              :page-size="10"/>
      </div>
    </div>
    <Modal v-model="isModal1" class="Modal" :width="screenWidth" title="批量导入"
           :mask-closable="false"
           @on-cancel="getData()">
      <div class="addBody">
        <site-employee-excel v-if="isModal1" @init-choose="initChooseProject"
                             @close-modal="closeCurrModal"></site-employee-excel>
      </div>
      <div slot="footer">
      </div>
    </Modal>
    <Modal v-model="isModal" class="Modal" :width="screenWidth" title="批量导入"
           :mask-closable="false"
           @on-cancel="getData()">
      <div class="addBody">
        <site-employee-info-excel v-if="isModal" @init-choose="initChooseProject"
                                  @close-modal="closeCurrModal"></site-employee-info-excel>
      </div>
      <div slot="footer">
      </div>
    </Modal>
  </div>

</template>

<script>
import moment from 'moment'
import siteEmployeeExcel from '@/components/page/site/siteEmployeeExcel.vue'
import siteEmployeeInfoExcel from '@/components/page/site/siteEmployeeInfoExcel.vue'

export default {
  data() {
    return {
      mouseFlag:false,
      mouseOffset:0,
      screenWidth: "35%",
      isModal1: false,
      isModal: false,
      bindLoading: false,
      detailIds: [],
      calculationLoading: false,
      wagesMonthBatchLoading: false,
      loading: true,
      loadingExcel: false,
      list: null,
      pageSizeOpts: [10, 30, 50, 100],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      dto: {
        month: null,
        projectId: null,
        name: null,
        projectName: null,
        manager: null,
        pageSize: 10,
        pageNum: 1,
      },
    };
  },
  components: {
    "siteEmployeeExcel": siteEmployeeExcel,
    "siteEmployeeInfoExcel": siteEmployeeInfoExcel
  },
  created() {
    this.dto.month = moment(new Date()).format('YYYY-MM');
    this.getData();
  },
  computed: {},
  methods: {
    //表格拖拽效果
    mouseDownHandler(e) {
      this.mouseFlag=true;
       this.mouseOffset=e.clientX;
    },
    mouseUpHandler(e) {
      this.mouseFlag=false;
    },
    mouseMoveHandler(e) {
      let divData=this.$refs.multipleTable.bodyWrapper;
      if(this.mouseFlag){
        divData.scrollLeft -=(-this.mouseOffset+(this.mouseOffset=e.clientX));
      }
    },


    bind() {
      this.bindLoading = true;
      this.$postData("siteEmployee_getByBindAll").then(res => {
        this.bindLoading = false;
        if (res.status === 200) {
          this.$message({
            type: 'success',
            message: '绑定成功!'
          });
          this.getData()
        }
      });
    },
    goYun(no) {
      window.open("https://yun.xiaoyujia.com/Account/TokenLogin?Token=" + localStorage.getItem('token') + "&returnUrl=/order/Split?BillNo=" + no);
    },
    goProject(id) {
      //this.$router.push({path: '/siteDetails', query: {"id": id, tabName: "third"}})
      let url = window.location.href.split("/")[0];
      window.open(url + "/siteDetails?id=" + id + "&tabName=third")
    },
    download() {
      this.loadingExcel = true;
      this.$postData("siteWagesDetails_download", this.dto, {
        responseType: "arraybuffer"
      }).then(res => {
        let name = '';
        if (this.dto.month != null) {
          name = this.dto.month
        }
        this.loadingExcel = false;
        this.blobExport({
          tablename: name + "月份工资信息",
          res: res
        });
      })
    },
    handleSelectionChange(list) {
      console.log(list);
      let ids = [];
      list.forEach(item => {
        ids.push(item.id)
      });
      this.detailIds = ids;
    },
    insertWagesMonth() {
      this.wagesMonthBatchLoading = true;
      this.$getData("siteWagesDetails_insertWagesMonth", {month: this.dto.month}).then(res => {
        this.wagesMonthBatchLoading = false;
        if (res.status === 200) {
          this.getData();
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
        }
      })
    },
    calculationWagesMonthBatch() {
      this.calculationLoading = true;
      if (this.detailIds.length <= 0) {
        this.$postData("calculationWagesMonthOneKey", {month: this.dto.month}).then(res => {
          this.calculationLoading = false;
          if (res.status === 200) {
            this.getData();
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
          }
        })
      }else {
        this.$postData("siteWagesDetails_calculationWagesMonthBatch", {detailIds: this.detailIds}).then(res => {
          this.calculationLoading = false;
          if (res.status === 200) {
            this.detailIds = [];
            this.getData();
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
          }
        })
      }
    },
    update(item) {
      this.$postData("siteWagesDetails_update", item).then(res => {
        if (res.status === 200) {
          // this.calculationWagesMonth(item.id);
          this.getData();
        }
      })
    },
    getData() {
      this.loading = true;
      this.$postData("siteWagesDetails_selectPage", this.dto).then(res => {
        this.loading = false;
        if (res.status === 200) {
          console.log(res.data.total);
          this.list = res.data.list;
          this.pageInfo.current = res.data.pageNum;
          this.pageInfo.size = res.data.pageSize;
          this.pageInfo.total = res.data.total
        }
      });
    },
    query() {
      this.loading = true;
      this.dto.pageNum = 1;
      this.getData();
    },
    closeCurrModal(data) {
      this.isModal1 = false;
      this.isModal = false;
    },
    initChooseProject(data) {
      this.closeCurrModal();
      this.getData()
    },

    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dto.pageSize = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dto.pageNum = index;
      this.getData();
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
  }
};
</script>

<style scoped>
.handle-box {
  /*margin-bottom: 10px;*/
  font-weight: bold !important;
}

.el-form-item__label {
  font-weight: bold !important;
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}

.del-dialog-cnt {
  font-size: 16px;
  text-align: center;
}

.table {
  width: 100%;
  font-size: 13px;

}

.red {
  color: #ff0000;
}

.mr10 {
  margin-right: 10px;
}

.container {
  padding: 15px !important;
  background: #fff;
  border: none !important;
  border-radius: 5px;
}

.el-date-editor .el-range-separator {
  padding: 0 5px;
  line-height: 32px;
  width: 7%;
  color: #303133;
}
</style>
