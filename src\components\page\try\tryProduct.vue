<template>
    <div class="table">
        <!--<div class="crumbs">-->
            <!--<el-breadcrumb separator="/">-->
                <!--<el-breadcrumb-item>-->
                    <!--<i class="el-icon-lx-cascades"></i> 项目管理-->
                <!--</el-breadcrumb-item>-->
            <!--</el-breadcrumb>-->
        <!--</div>-->
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">

                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="主标题">
                                <el-input
                                        v-model="product.firstTitle"
                                        placeholder="主标题"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="副标题">
                                <el-input
                                        v-model="product.secondTitle"
                                        placeholder="副标题"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>


                        <el-col :span="8" style="text-align:right">
                            <el-form-item>
                                <el-button type="success" style="width:40%" round @click="saveModal=true">添加</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>


                        <el-col :span="8">
                            <el-form-item label="类型">
                                <Select v-model="product.type" filterable style="width: 200px">
                                    <Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label}}
                                    </Option>
                                </Select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item label="使用数量">
                                <el-input
                                        v-model="product.number"
                                        placeholder="使用数量"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8" style="text-align:right">
                            <el-form-item>
                                <el-button type="success" style="width:40%" round @click="query()">搜索</el-button>
                            </el-form-item>
                        </el-col>


                    </el-row>
                </el-form>
            </div>
            <el-table :data="list" border class="table" ref="multipleTable">
                <el-table-column
                        prop="firstTitle"
                        label="主标题"
                        width="330"

                ></el-table-column>
                <el-table-column
                        prop="secondTitle"
                        label="副标题"
                        width="300"
                ></el-table-column>
                <el-table-column
                        prop="number"
                        label="使用数量"
                        width="120"
                ></el-table-column>
                <el-table-column
                        prop="amount"
                        label="金额"
                        width="100"

                ></el-table-column>
                <el-table-column
                        prop="typeDesc"
                        label="类型"
                        width="110"
                ></el-table-column>
                <el-table-column
                        prop="statusDesc"
                        label="状态"
                        width="110"
                ></el-table-column>
                <el-table-column
                        label="操作"
                        width="200">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="edit(scope.row.id)" type="primary">编辑</el-button>
                        <el-button  v-if="scope.row.status=='1'" size="mini" @click="updateStatus(scope.row.id,scope.row.status)" type="danger">下架</el-button>
                        <el-button  v-if="scope.row.status=='2'" size="mini" @click="updateStatus(scope.row.id,scope.row.status)" type="primary">上架</el-button>
                    </template>

                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>

        <Modal v-model="saveModal" class="Modal" :width="screenWidth"  title="添加" :mask-closable="false" >
            <div class="addBody">

                <try-product-add v-if="saveModal" @init-choose="initChooseProject"
                                @close-modal="closeCurrModal"></try-product-add>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="updateModal" class="Modal" :width="screenWidth"  title="修改" :mask-closable="false" >
            <div class="addBody">
                <try-product-update v-if="updateModal" @init-choose="initChooseProject" :productId="productId"
                                 @close-modal="closeCurrModal"></try-product-update>
            </div>
            <div slot="footer">
            </div>
        </Modal>

    </div>
</template>

<script>
    import tryProductAdd from '@/components/page/try/choose/tryProductAdd.vue'
    import tryProductUpdate from '@/components/page/try/choose/tryProductUpdate.vue'
    import {formatDate} from '@/components/common/utils.js'
    export default {
        data() {
            return {
                productId:null,
                tableData: [],
                pageSizeOpts:[10,20,30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                screenWidth: '50%',//新增对话框 宽度
                saveModal: false,
                updateModal: false,
                product: {
                    firstTitle: null,
                    secondTitle: null,
                    number: null,
                    amount: null,
                    type: null,
                    pageSize:10,
                    pageNum:1,
                    status:null
                },
                update:{
                    id:null,
                    status:null
                },
                cityList: [
                    {
                        value: '',
                        label: '请选择'
                    },
                    {
                        value: '1',
                        label: '试吃'
                    },
                    {
                        value: '2',
                        label: '试用'
                    },
                ],
                list:null
            };
        },
        components: {
            'tryProductAdd': tryProductAdd,
            'tryProductUpdate': tryProductUpdate,
        },
        created() {
            this.getData();
        },
        computed: {
        },
        methods: {
            getData() {
                this.$postData("product_getByList", this.product, {}).then(res => {
                    if (res.status == 200) {

                        this.list = res.data.list;
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            query() {
                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.product.pageSize = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                console.log(index)
                this.product.pageNum = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
                this.updateModal = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },
            edit(id){
                this.productId=id;
                this.updateModal=true;
            },
            updateStatus(id,status){
                console.log(status)
                this.update.id=id;
                let html="";
                let html1="";
                if(status=="1"){
                    this.update.status=2;
                    html="下架"
                    html1="下架成功"
                }else {
                    this.update.status=1;
                    html="上架"
                    html1="上架成功"
                }
                this.$confirm('此操作将永久'+html+'该商品, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$postData("product_updateProduct", this.update, {}).then(res => {
                        if (res.status == 200) {
                            this.$message({
                                type: 'success',
                                message: html1
                            });
                            this.getData();
                        }
                    })

                }).catch(() => {
                });




            }
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
