<template>
  <div class="table">
    <div class="container">
      <div class="handle-box">
        <el-form ref="form">
          <el-row style="margin-top: 10px">
            <el-col :span="24">
              <el-form-item>
                <el-button type="primary" :loading="bindLoading" icon="el-icon-plus"
                           @click="bind">一键绑定
                </el-button>
                <el-button type="success"
                           @click="insertEm1"
                           icon="el-icon-plus">
                  新增正式工
                </el-button>
                <el-button type="success"
                           @click="insertEm"
                           icon="el-icon-plus">
                  临时调度
                </el-button>
                <el-button icon="el-icon-refresh" @click="getData">刷新
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-table :data="list" class="table" ref="multipleTable"
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}"
                v-loading="loading">
        <el-table-column
            width="110"
            prop="name"
            label="名称">
          <template slot-scope="scope">
            <span>{{ scope.row.employee.realName }}{{ scope.row.employee.no }}</span>
            <Tag v-if="scope.row.type===1" color="primary">长期工</Tag>
            <Tag v-if="scope.row.type===2" color="warning">调度工</Tag>

          </template>
        </el-table-column>
        <el-table-column
            width="50"
            prop="state"
            label="状态">
          <template slot-scope="scope">
            <span v-if="scope.row.state===1">在职</span>
            <span style="color: red" v-if="scope.row.state===2">离职</span>
          </template>
        </el-table-column>
        <el-table-column
            width="100"
            prop="social"
            label="是否交医社保">
          <template slot-scope="scope">
            <el-switch
                @change="update(scope.row)"
                v-model="scope.row.social"
                inactive-color="#ff4949">
            </el-switch>
          </template>
        </el-table-column>

        <el-table-column
            width="110"
            prop="socialAmount"
            label="医社保金额">
          <template slot-scope="scope" v-if="scope.row.social">
            <el-input clearable @blur="update(scope.row)" style="width:100px"
                      v-model="scope.row.socialAmount" placeholder="医社保金额"></el-input>
          </template>
        </el-table-column>
        <!--                <el-table-column-->
        <!--                        width="100"-->
        <!--                        prop="provident"-->
        <!--                        label="是否交公积金">-->
        <!--                    <template slot-scope="scope">-->
        <!--                        <el-switch-->
        <!--                                @change="update(scope.row)"-->
        <!--                                v-model="scope.row.provident"-->
        <!--                                inactive-color="#ff4949">-->
        <!--                        </el-switch>-->
        <!--                    </template>-->
        <!--                </el-table-column>-->
        <el-table-column
            width="110"
            prop="appointmentAmount"
            label="约定金额">
          <template slot-scope="scope">
            <el-input
                clearable
                @blur="update(scope.row)"
                style="width:100px"
                v-model="scope.row.appointmentAmount"
                placeholder="约定金额"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column
            width="220"
            prop="bankCard"
            label="银行卡号">
          <!--                    <template slot-scope="scope">-->
          <!--                        <el-input-->
          <!--                                clearable-->
          <!--                                @blur="update(scope.row)"-->
          <!--                                style="width:200px"-->
          <!--                                v-model="scope.row.bankCard"-->
          <!--                                placeholder="银行卡号"-->
          <!--                        ></el-input>-->
          <!--                    </template>-->
        </el-table-column>
        <el-table-column
            width="200"
            prop="idCard"
            label="身份证号">
        </el-table-column>
        <el-table-column width="200" prop="entryTime" label="入职时间">
        </el-table-column>
        <el-table-column width="100" prop="quitTime" label="离职时间">
        </el-table-column>
        <el-table-column width="180" prop="order.totalAmount" label="调度时间">
          <template slot-scope="scope">
            <span>{{ scope.row.dispatchStartTime }}</span>
            <p>{{ scope.row.dispatchEndTime }}</p>
          </template>
        </el-table-column>
        <el-table-column fixed="right" width="190" label="操作">
          <template slot-scope="scope">
            <el-row>
              <el-col :span="24">
                <div style="display: flex">
                  <el-button v-if="scope.row.state!==2"
                             size="small "
                             type="text"
                             @click="matter(scope.row)"
                             icon="el-icon-date">
                    事项
                  </el-button>
                  <el-button v-if="scope.row.type===1&&scope.row.state===1"
                             @click="quit(scope.row)"
                             size="small "
                             type="text"
                             icon="el-icon-set-up">
                    离职
                  </el-button>
                  <el-button v-if="scope.row.state!==2"
                             size="small "
                             type="text"
                             @click="deleteOrderDelete(scope.row.employee.no,scope.row.id)"
                             icon="el-icon-delete">
                    删除
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  props: ['projectId'],
  data() {
    return {
      bindLoading: false,
      loading: true,
      loadingExcel: false,
      list: null,
      dto: {
        name: null,
        days: [],
        startTime: null,
        endTime: null,
        state: null,
        pageSize: 10,
        pageNum: 1,
      },
    };
  },
  components: {},
  created() {
    this.getData();
  },
  computed: {},
  watch: {
    projectId: {
      handler(newValue, oldValue) {
        this.projectId = newValue;
        this.getData();
      }
    }
  },
  methods: {
    update(item) {
      this.$postData("siteEmployee_update", item).then(res => {
      })
    },
    bind() {
      this.bindLoading = true;
      this.$postUrl("siteEmployee_getByBind", this.projectId).then(res => {
        this.bindLoading = false;
        if (res.status === 200) {
          this.$message({
            type: 'success',
            message: '绑定成功!'
          });
          this.getData()
        }
      });
    },
    deleteOrderDelete(no, id) {
      this.$confirm('此操作将删除该员工后面绑定的子订单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$getData("orderWaiterDelete", {no: no, id: id}).then(res => {
          this.getData()
        });
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    quit(item) {
      let employee = item.employee;
      let data = {
        name: employee.realName + "-离职",
        tabName: "name6",
        content: "siteQuit",
        siteEmployeeId: item.id
      };
      this.$emit('init-choose', data, employee);
    },
    insertEm1() {
      let data = {
        name: "新增正式工",
        tabName: "name10",
        content: "siteEmployeeFormal"
      };
      this.$emit('init-choose', data, null);
    },
    insertEm() {
      let data = {
        name: "临时调度",
        tabName: "name5",
        content: "siteInsertEm"
      };
      this.$emit('init-choose', data, null);
    },
    matter(item) {
      let employee = item.employee;
      let data = {
        name: employee.realName + "-事项",
        tabName: "name2",
        content: "siteMatter"
      };
      this.$emit('init-choose', data, employee);
    },
    getData() {
      this.$postUrl("siteEmployee_getByProjectId", this.projectId).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data;
          console.log(res.data)
        }
      });
    },
    exportExcel() {
      this.loadingExcel = true;
      this.$postData("tsExportList", this.dto, {
        responseType: "arraybuffer"
      }).then(res => {
        ;
        this.loadingExcel = false;
        this.blobExport({
          tablename: "投诉工单",
          res: res
        });
      });
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
  }
};
</script>

<style scoped>
.handle-box {
  /*margin-bottom: 10px;*/
  font-weight: bold !important;
}

.el-form-item__label {
  font-weight: bold !important;
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}

.del-dialog-cnt {
  font-size: 16px;
  text-align: center;
}

.table {
  width: 100%;
  font-size: 13px;

}

.red {
  color: #ff0000;
}

.mr10 {
  margin-right: 10px;
}

.container {
  padding: 0px !important;
  background: #fff;
  border: none !important;
  border-radius: 5px;
}

.el-date-editor .el-range-separator {
  padding: 0 5px;
  line-height: 32px;
  width: 7%;
  color: #303133;
}
</style>
