
<template>
<div>
    <el-row :gutter="24" style="height: 50px">
      <el-col :span="6"> <el-input v-model="roleName" placeholder="请输入名称"></el-input></el-col>
      <el-col :span="2"> <el-button type="primary" @click="getData">搜索</el-button></el-col>
    </el-row>
  <el-alert
      title="新增角色和删除角色请去云平台设置,该页面不支持"
      type="warning">
  </el-alert>
    <el-table
        v-loading="loading"
        :data="roleList"
        style="width: 100%;margin-bottom: 20px;">
      <el-table-column
          prop="id"
          label="Id">
      </el-table-column>
      <el-table-column
          prop="name"
          label="角色名称">
      </el-table-column>
      <el-table-column
          prop="description"
          label="描述">
      </el-table-column>

      <el-table-column
          label="操作">
        <template slot-scope="scope">
          <div>
            <el-button type="primary" @click="editForm(scope.row)">权限设置</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

  <el-dialog
      title="权限修改"
      :visible.sync="roleDialog"
      width="30%"
      center
      :close-on-click-modal="false"
  >
    <el-input :disabled="true" v-model="currentRole.name"></el-input>
    <el-tree
        ref="tree"
        :data="currentRoleData.treeList"
        show-checkbox
        node-key="id"
        :default-expanded-keys="currentRoleData.ids"
        :default-checked-keys="currentRoleData.ids"

        >
    </el-tree>


     <span slot="footer" class="dialog-footer">
        <el-button @click="roleDialog = false">取 消</el-button>
        <el-button type="primary" @click="addRole()">确 定</el-button>
      </span>
  </el-dialog>
</div>
</template>

<script>
export default {
  data(){
    return{
      roleList:[],
      roleName:'',
      roleDialog:false,
      currentRole:{},
      currentRoleData:[],
      loading:false,


    }
  },
  created(){
    this.getData()
  },
  methods:{
    editForm(data) {
      this.loading = true;
      this.currentRole = data;
      this.$getData("getRolePermission",{roleId:data.id}).then(res => {
        if (res.status === 200) {
          this.currentRoleData = res.data;
          this.roleDialog = true;
        } else {
          return this.$message({
            type: 'error',
            message: '系统错误'
          });
        }
        this.loading = false;
      });

    },
      addRole() {
        let resultArr = [];
        resultArr = resultArr.concat(this.$refs.tree.getCheckedKeys());
        resultArr = resultArr.concat(this.$refs.tree.getHalfCheckedKeys());
        this.$postData("updateRoleMenu",{roleId:this.currentRole.id,ids:resultArr}).then(res => {
          if (res.status === 200) {
            this.getData();
            return this.$message({
              type: 'success',
              message: '更新成功'
            });
            this.roleDialog = false;
          } else {
            return this.$message({
              type: 'error',
              message: '系统错误'
            });
          }

        });

      },
      getData() {
        this.$getData("getAllRoleList",{name:this.roleName}).then(res => {
          if (res.status === 200) {
            this.roleList = res.data;
          } else {
            return this.$message({
              type: 'error',
              message: '系统错误'
            });
          }

        });
      }
  }
}
</script>
<style>


</style>
