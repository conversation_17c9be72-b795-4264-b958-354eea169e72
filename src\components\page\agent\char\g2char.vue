<template>

        <div :id="id"  :style="'height: '+heightSize+'px'" v-if="charData.length" v-show="show">
        </div>

</template>

<script>
    import { Chart, Util } from '@antv/g2';

    export default {
        props:{
                setCharData:{
                        type: Array,
                        default: () => []
                },
                heightSize: {
                        type: Number,
                        default: 200
                },
        },
        name: "g2char",
        watch:{
            option(){
                this.$nextTick(() => {
                    this.changeChar()
                })
            }
        },
        data() {
            return {


                show:true,
                chart:null,
                id:"id"+Math.ceil(Math.random()*1000),
                // charData:[],
                sum:100,
                charData : [
                    { response: '加载中', num: 100},
                ]
            }
        },
        created(){
            let self=this
            setTimeout(function (){
                self.sum=0
                self.show=false
                self.charData=self.setCharData
                self.charData.forEach(v=>{

                    self.sum=v.num+self.sum
                })
                self.changeChar()
                self.show=true
            }, 1000);

        },
        mounted(){
            this.inintChar(this.charData)
        },
        methods:{
            changeChar(){
                this.chart.changeData(this.charData)

            },
            inintChar(charData){
                const data=charData;
                 this.chart = new Chart({
                    container: this.id,
                    autoFit: true,
                    height: 100,
                });
                this.chart.data(data);

                this.chart.coordinate('theta', {
                    radius: 1
                });
                this.chart.tooltip({
                    showMarkers: true
                });

                const interval = this.chart
                    .interval()
                    .adjust('stack')
                    .position('num')
                    .color('response',['#FF6B3B','#626681','#FFC100','#9FB40F','#76523B','#DAD5B5','#0E8E89','#E19348','#F383A2','#247FEA','#2BCB95','#B1ABF4','#1D42C2','#1D9ED1','#D64BC0','#255634','#8C8C47','#8CDAE5','#8E283B','#791DC9'])
                    .style({ opacity: 0.4 })
                    .state({
                        active: {
                            style: (element) => {
                                const shape = element.shape;
                                return {
                                    matrix: Util.zoom(shape, 1),
                                }
                            }
                        }
                    })
                    .label('response', (val) => {
                        const opacity =  1 ;
                        return {
                            offset: -10,
                            style: {
                                opacity,
                                fill: 'white',
                                fontSize: 12,
                                shadowBlur: 2,
                                shadowColor: 'rgba(0, 0, 0, .45)',
                            },
                            content: (obj) => {
                                    if (this.heightSize>200){
                                            return  obj.response + '\n' + ((obj.num/this.sum)*100).toFixed(2) + '%';
                                    }

                                return  ((obj.num/this.sum)*100).toFixed(2) + '%';
                            },
                        };
                    });

                this.chart.interaction('element-single-selected');
                this.chart.render();
            }
        }
    }
</script>

<style scoped>

</style>
