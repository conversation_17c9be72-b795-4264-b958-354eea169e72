<template xmlns:x="http://www.w3.org/1999/xhtml">
    <div id="pdfDom">
        <div style="float: right;width: 400px">
            <el-card shadow="always">
                <span style="font-size: 25px;float: left;font-weight: bold;color: #f00f14;">{{this.level.rateCount}}分</span>
                <div style="font-size: 25px;font-weight: bold;color: #2d8cf0;text-align: right">
                    {{this.level.rateCount<35?'普通':this.level.rateCount<70?'中级':'高级'}}
                </div>
            </el-card>
            <br>
            <h3>
                姓名：{{dom.realName}}
                <el-divider direction="vertical"></el-divider>
                工号：{{dom.no}}
                <el-divider direction="vertical"></el-divider>
                <br>
                手机号：{{dom.phone}}
                <el-divider direction="vertical"></el-divider>
                身份证：{{dom.idcard}}
                <el-divider direction="vertical"></el-divider>
                <el-divider/>
                <div class="block">
                    <span class="demonstration">经纪人评分({{update.agentRate}})</span>
                    <el-rate
                            v-model="update.agentRate"
                            :colors="colors">
                    </el-rate>
                    <br>
                    <span class="demonstration">理由：</span>
                    <el-input
                            type="textarea"
                            :rows="3"
                            placeholder="请输入内容"
                            v-model="update.remark">
                    </el-input>
                </div>
                <el-divider/>
                <div class="block">
                    <span class="demonstration">客户评分({{update.memberRate}})</span>
                    <el-rate
                            :max="10"
                            v-model="update.memberRate"
                            :colors="colors2">
                    </el-rate>
                </div>
                <el-divider/>
                <el-button v-if="showHandleByRoleId()" type="primary" :loading="loading" @click="saveBaomuLevel">更新</el-button>

            </h3>
        </div>
        <table width="722" border="0" cellpadding="0" cellspacing="0"
               style='width:541.50pt;border-collapse:collapse;table-layout:fixed;'>
            <col width="156" style='mso-width-source:userset;mso-width-alt:4992;'/>
            <col width="72" style='width:54.00pt;'/>
            <col width="198" style='mso-width-source:userset;mso-width-alt:6336;'/>
            <col width="128" style='mso-width-source:userset;mso-width-alt:4096;'/>
            <col width="168" style='mso-width-source:userset;mso-width-alt:5376;'/>
            <tr height="40" style='height:30.00pt;mso-height-source:userset;mso-height-alt:600;'>
                <td class="xl65" height="49.33" width="426" colspan="3" rowspan="2"
                    style='height:37.00pt;width:319.50pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;'
                    x:str>参数/类型
                </td>
                <td class="xl68" width="128" rowspan="2"
                    style='width:96.00pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>
                    分值
                </td>
                <td class="xl69" width="168" rowspan="2"
                    style='width:126.00pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;'
                    x:str>得分
                </td>
            </tr>
            <tr height="9.33" style='height:7.00pt;mso-height-source:userset;mso-height-alt:140;'/>
            <tr height="57.33" style='height:43.00pt;mso-height-source:userset;mso-height-alt:860;'>
                <td class="xl74" height="140" rowspan="3"
                    style='height:105.00pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;'
                    x:str>资质<br/>（系统）<br/>40%
                </td>
                <td class="xl74" colspan="2"
                    style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>家政员证
                </td>
                <td class="xl69" x:num>10</td>
                <td class="xl69" x:num>{{rate[0]}}</td>
            </tr>
            <tr height="36" style='height:27.00pt;mso-height-source:userset;mso-height-alt:540;'>
                <td class="xl74" colspan="2"
                    style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>
                    月嫂/育婴师证/护工证/催乳师证
                </td>
                <td class="xl69" x:num>15</td>
                <td class="xl69" x:num>{{rate[1]}}</td>
            </tr>
            <tr height="46.67" style='height:35.00pt;mso-height-source:userset;mso-height-alt:700;'>
                <td class="xl74" colspan="2"
                    style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>驾驶证
                </td>
                <td class="xl69" x:num>15</td>
                <td class="xl69" x:num>{{rate[2]}}</td>
            </tr>
            <tr height="38" style='height:28.50pt;'>
                <td class="xl74" height="38" style='height:28.50pt;' x:str>年龄<br/>（系统）5%</td>
                <td class="xl74" colspan="2"
                    style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>35-50周
                </td>
                <td class="xl69" x:num>5</td>
                <td class="xl69" x:num>{{rate[3]}}</td>
            </tr>
            <tr height="37.33" style='height:28.00pt;mso-height-source:userset;mso-height-alt:560;'>
                <td class="xl75" height="234.67" rowspan="7"
                    style='height:176.00pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;'
                    x:str>职业技能（人工）<br/>40%
                </td>
                <td class="xl75" colspan="2"
                    style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>玻璃擦
                </td>
                <td class="xl69" x:num>5</td>
                <td class="xl69" x:num>{{rate[4]}}</td>
            </tr>
            <tr height="37.33" style='height:28.00pt;mso-height-source:userset;mso-height-alt:560;'>
                <td class="xl75" colspan="2"
                    style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>收纳整理
                </td>
                <td class="xl69" x:num>5</td>
                <td class="xl69" x:num>{{rate[5]}}</td>
            </tr>
            <tr height="34.67" style='height:26.00pt;mso-height-source:userset;mso-height-alt:520;'>
                <td class="xl75" colspan="2"
                    style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>熨烫
                </td>
                <td class="xl69" x:num>5</td>
                <td class="xl69" x:num>{{rate[6]}}</td>
            </tr>
            <tr height="30.67" style='height:23.00pt;mso-height-source:userset;mso-height-alt:460;'>
                <td class="xl75" colspan="2"
                    style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>电动车
                </td>
                <td class="xl69" x:num>5</td>
                <td class="xl69" x:num>{{rate[7]}}</td>
            </tr>
            <tr height="32" style='height:24.00pt;mso-height-source:userset;mso-height-alt:480;'>
                <td class="xl75" colspan="2"
                    style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>闽南语
                </td>
                <td class="xl69" x:num>5</td>
                <td class="xl69" x:num>{{rate[8]}}</td>
            </tr>
            <tr height="34.67" style='height:26.00pt;mso-height-source:userset;mso-height-alt:520;'>
                <td class="xl75" colspan="2"
                    style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>英语（略懂）
                </td>
                <td class="xl69" x:num>8</td>
                <td class="xl69" x:num>{{rate[9]}}</td>
            </tr>
            <tr height="28" style='height:21.00pt;mso-height-source:userset;mso-height-alt:420;'>
                <td class="xl75" colspan="2"
                    style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>电脑（略懂）
                </td>
                <td class="xl69" x:num>8</td>
                <td class="xl69" x:num>{{rate[10]}}</td>
            </tr>
            <tr height="36" style='height:27.00pt;mso-height-source:userset;mso-height-alt:540;'>
                <td class="xl75" height="76" rowspan="2"
                    style='height:57.00pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;'
                    x:str>增值项（人工）<br/>15%
                </td>
                <td class="xl77" colspan="2"
                    style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>经纪人/鉴定师好评
                </td>
                <td class="xl79" x:num>5</td>
                <td class="xl79" x:num>{{this.level.agentRate}}</td>
            </tr>
            <tr height="40" style='height:30.00pt;mso-height-source:userset;mso-height-alt:600;'>
                <td class="xl77" colspan="2"
                    style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>客户好评
                </td>
                <td class="xl79" x:num>10</td>
                <td class="xl79" x:num>{{this.level.memberRate}}</td>
            </tr>
            <tr height="25.33" style='height:19.00pt;mso-height-source:userset;mso-height-alt:380;'>
                <td class="xl80" height="25.33" colspan="3"
                    style='height:19.00pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;'
                    x:str>总分
                </td>
                <td class="xl80" x:fmla="=SUM(D3:D15)" x:num>101</td>
                <td class="xl80">{{this.level.rateCount}}</td>
            </tr>
            <tr height="22.67" style='height:17.00pt;mso-height-source:userset;mso-height-alt:340;'>
                <td class="xl80" height="22.67" colspan="3"
                    style='height:17.00pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;'
                    x:str>评级
                </td>
                <td class="xl80"></td>
                <td class="xl80">{{this.level.rateCount<35?'普通':this.level.rateCount<70?'中级':'高级'}}</td>
            </tr>

        </table>

        <div>注意：评分标准为=> 0-35 普通 35~70中级 70以上 高级</div>

    </div>

</template>

<script>
    export default {
        props: ['model'],
        name: "BaomuLevel",
        data() {
            return {
                roleId: localStorage.getItem("roleId"),
                loading: false,
                update: {
                    remark: null,
                    id: null,
                    updateName: localStorage.getItem("realName"),
                    agentRate: 0,
                    memberRate: 0,
                },
                colors: ['#99A9BF', '#F7BA2A', '#FF9900'],  // 等同于 { 2: '#99A9BF', 4: { value: '#F7BA2A', excluded: true }, 5: '#FF9900' }
                colors2: {4: '#99A9BF', 8: {value: '#F7BA2A', excluded: true}, 10: '#FF9900'},
                dom: this.model,
                level: null,
                rate: [],
            }
        },
        created() {
            // console.log(this.dom)
            this.getBaomuLevel();
        },
        methods: {
            showHandleByRoleId() {
                if (this.roleId == null) {
                    return false;
                }
                return this.roleId != 85;
            },
            saveBaomuLevel() {
                this.loading = true
                this.$postData("saveBaomuLevel", this.update, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.success("更新成功！");
                        this.loading = false
                        this.getBaomuLevel()

                    } else {
                        this.loading = false
                        this.$message.error("更新失败，" + res.msg);
                    }
                })
            },
            getBaomuLevel() {
                this.$getData("getBaomuLevel", {employeeId: this.dom.id}).then(res => {
                    if (res.status == 200) {

                        this.level = res.data
                        this.rate = this.level.rate.split(',')
                        this.update.agentRate = this.level.agentRate
                        this.update.memberRate = this.level.memberRate
                        this.update.remark = this.level.remark
                        this.update.id = this.level.id

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            /**
             * 关闭当前窗口
             */
            chooseThisModel(name) {
                this.dom = null;
                this.$emit('init-choose', "");
            },
        }
    }
</script>

<style scoped>
    tr {
        mso-height-source: auto;
        mso-ruby-visibility: none;
    }

    col {
        mso-width-source: auto;
        mso-ruby-visibility: none;
    }

    br {
        mso-data-placement: same-cell;
    }

    .font0 {
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 134;
    }

    .font1 {
        color: windowtext;
        font-size: 12.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 134;
    }

    .font2 {
        color: #FF0000;
        font-size: 12.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 134;
    }

    .font3 {
        color: #000000;
        font-size: 12.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 134;
    }

    .font4 {
        color: #800080;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: underline;
        text-underline-style: single;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 0;
    }

    .font5 {
        color: #0000FF;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: underline;
        text-underline-style: single;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 0;
    }

    .font6 {
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 0;
    }

    .font7 {
        color: #FFFFFF;
        font-size: 11.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 0;
    }

    .font8 {
        color: #7F7F7F;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: italic;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 0;
    }

    .font9 {
        color: #9C0006;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 0;
    }

    .font10 {
        color: #FFFFFF;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 0;
    }

    .font11 {
        color: #3F3F76;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 0;
    }

    .font12 {
        color: #006100;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 0;
    }

    .font13 {
        color: #FA7D00;
        font-size: 11.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 0;
    }

    .font14 {
        color: #44546A;
        font-size: 18.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 134;
    }

    .font15 {
        color: #9C6500;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 0;
    }

    .font16 {
        color: #FA7D00;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 0;
    }

    .font17 {
        color: #44546A;
        font-size: 15.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 134;
    }

    .font18 {
        color: #44546A;
        font-size: 11.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 134;
    }

    .font19 {
        color: #FF0000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 0;
    }

    .font20 {
        color: #000000;
        font-size: 11.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 0;
    }

    .font21 {
        color: #44546A;
        font-size: 13.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 134;
    }

    .font22 {
        color: #3F3F3F;
        font-size: 11.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: "宋体";
        mso-generic-font-family: auto;
        mso-font-charset: 0;
    }

    .style0 {
        mso-number-format: "General";
        text-align: general;
        vertical-align: middle;
        white-space: nowrap;
        mso-rotate: 0;
        mso-pattern: auto;
        mso-background-source: auto;
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 134;
        border: none;
        mso-protection: locked visible;
        mso-style-name: "常规";
        mso-style-id: 0;
    }

    .style16 {
        mso-number-format: "_ \0022\00A5\0022* \#\,\#\#0_ \;_ \0022\00A5\0022* \\-\#\,\#\#0_ \;_ \0022\00A5\0022* \0022-\0022_ \;_ \@_ ";
        mso-style-name: "货币[0]";
        mso-style-id: 7;
    }

    .style17 {
        mso-pattern: auto none;
        background: #EDEDED;
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "20% - 强调文字颜色 3";
    }

    .style18 {
        mso-pattern: auto none;
        background: #FFCC99;
        color: #3F3F76;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        border: .5pt solid #7F7F7F;
        mso-style-name: "输入";
    }

    .style19 {
        mso-number-format: "_ \0022\00A5\0022* \#\,\#\#0\.00_ \;_ \0022\00A5\0022* \\-\#\,\#\#0\.00_ \;_ \0022\00A5\0022* \0022-\0022??_ \;_ \@_ ";
        mso-style-name: "货币";
        mso-style-id: 4;
    }

    .style20 {
        mso-number-format: "_ * \#\,\#\#0_ \;_ * \\-\#\,\#\#0_ \;_ * \0022-\0022_ \;_ \@_ ";
        mso-style-name: "千位分隔[0]";
        mso-style-id: 6;
    }

    .style21 {
        mso-pattern: auto none;
        background: #DBDBDB;
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "40% - 强调文字颜色 3";
    }

    .style22 {
        mso-pattern: auto none;
        background: #FFC7CE;
        color: #9C0006;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "差";
    }

    .style23 {
        mso-number-format: "_ * \#\,\#\#0\.00_ \;_ * \\-\#\,\#\#0\.00_ \;_ * \0022-\0022??_ \;_ \@_ ";
        mso-style-name: "千位分隔";
        mso-style-id: 3;
    }

    .style24 {
        mso-pattern: auto none;
        background: #C9C9C9;
        color: #FFFFFF;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "60% - 强调文字颜色 3";
    }

    .style25 {
        color: #0000FF;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: underline;
        text-underline-style: single;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "超链接";
        mso-style-id: 8;
    }

    .style26 {
        mso-number-format: "0%";
        mso-style-name: "百分比";
        mso-style-id: 5;
    }

    .style27 {
        color: #800080;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: underline;
        text-underline-style: single;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "已访问的超链接";
        mso-style-id: 9;
    }

    .style28 {
        mso-pattern: auto none;
        background: #FFFFCC;
        border: .5pt solid #B2B2B2;
        mso-style-name: "注释";
    }

    .style29 {
        mso-pattern: auto none;
        background: #F4B084;
        color: #FFFFFF;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "60% - 强调文字颜色 2";
    }

    .style30 {
        color: #44546A;
        font-size: 11.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 134;
        mso-style-name: "标题 4";
    }

    .style31 {
        color: #FF0000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "警告文本";
    }

    .style32 {
        color: #44546A;
        font-size: 18.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 134;
        mso-style-name: "标题";
    }

    .style33 {
        color: #7F7F7F;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: italic;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "解释性文本";
    }

    .style34 {
        color: #44546A;
        font-size: 15.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 134;
        border-bottom: 1.0pt solid #5B9BD5;
        mso-style-name: "标题 1";
    }

    .style35 {
        color: #44546A;
        font-size: 13.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 134;
        border-bottom: 1.0pt solid #5B9BD5;
        mso-style-name: "标题 2";
    }

    .style36 {
        mso-pattern: auto none;
        background: #9BC2E6;
        color: #FFFFFF;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "60% - 强调文字颜色 1";
    }

    .style37 {
        color: #44546A;
        font-size: 11.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 134;
        border-bottom: 1.0pt solid #ACCCEA;
        mso-style-name: "标题 3";
    }

    .style38 {
        mso-pattern: auto none;
        background: #FFD966;
        color: #FFFFFF;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "60% - 强调文字颜色 4";
    }

    .style39 {
        mso-pattern: auto none;
        background: #F2F2F2;
        color: #3F3F3F;
        font-size: 11.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        border: .5pt solid #3F3F3F;
        mso-style-name: "输出";
    }

    .style40 {
        mso-pattern: auto none;
        background: #F2F2F2;
        color: #FA7D00;
        font-size: 11.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        border: .5pt solid #7F7F7F;
        mso-style-name: "计算";
    }

    .style41 {
        mso-pattern: auto none;
        background: #A5A5A5;
        color: #FFFFFF;
        font-size: 11.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        border: 2.0pt double #3F3F3F;
        mso-style-name: "检查单元格";
    }

    .style42 {
        mso-pattern: auto none;
        background: #E2EFDA;
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "20% - 强调文字颜色 6";
    }

    .style43 {
        mso-pattern: auto none;
        background: #ED7D31;
        color: #FFFFFF;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "强调文字颜色 2";
    }

    .style44 {
        color: #FA7D00;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        border-bottom: 2.0pt double #FF8001;
        mso-style-name: "链接单元格";
    }

    .style45 {
        color: #000000;
        font-size: 11.0pt;
        font-weight: 700;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        border-top: .5pt solid #5B9BD5;
        border-bottom: 2.0pt double #5B9BD5;
        mso-style-name: "汇总";
    }

    .style46 {
        mso-pattern: auto none;
        background: #C6EFCE;
        color: #006100;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "好";
    }

    .style47 {
        mso-pattern: auto none;
        background: #FFEB9C;
        color: #9C6500;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "适中";
    }

    .style48 {
        mso-pattern: auto none;
        background: #D9E1F2;
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "20% - 强调文字颜色 5";
    }

    .style49 {
        mso-pattern: auto none;
        background: #5B9BD5;
        color: #FFFFFF;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "强调文字颜色 1";
    }

    .style50 {
        mso-pattern: auto none;
        background: #DDEBF7;
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "20% - 强调文字颜色 1";
    }

    .style51 {
        mso-pattern: auto none;
        background: #BDD7EE;
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "40% - 强调文字颜色 1";
    }

    .style52 {
        mso-pattern: auto none;
        background: #FCE4D6;
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "20% - 强调文字颜色 2";
    }

    .style53 {
        mso-pattern: auto none;
        background: #F8CBAD;
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "40% - 强调文字颜色 2";
    }

    .style54 {
        mso-pattern: auto none;
        background: #A5A5A5;
        color: #FFFFFF;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "强调文字颜色 3";
    }

    .style55 {
        mso-pattern: auto none;
        background: #FFC000;
        color: #FFFFFF;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "强调文字颜色 4";
    }

    .style56 {
        mso-pattern: auto none;
        background: #FFF2CC;
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "20% - 强调文字颜色 4";
    }

    .style57 {
        mso-pattern: auto none;
        background: #FFE699;
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "40% - 强调文字颜色 4";
    }

    .style58 {
        mso-pattern: auto none;
        background: #4472C4;
        color: #FFFFFF;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "强调文字颜色 5";
    }

    .style59 {
        mso-pattern: auto none;
        background: #B4C6E7;
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "40% - 强调文字颜色 5";
    }

    .style60 {
        mso-pattern: auto none;
        background: #8EA9DB;
        color: #FFFFFF;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "60% - 强调文字颜色 5";
    }

    .style61 {
        mso-pattern: auto none;
        background: #70AD47;
        color: #FFFFFF;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "强调文字颜色 6";
    }

    .style62 {
        mso-pattern: auto none;
        background: #C6E0B4;
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "40% - 强调文字颜色 6";
    }

    .style63 {
        mso-pattern: auto none;
        background: #A9D08E;
        color: #FFFFFF;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 0;
        mso-style-name: "60% - 强调文字颜色 6";
    }

    td {
        mso-style-parent: style0;
        padding-top: 1px;
        padding-right: 1px;
        padding-left: 1px;
        mso-ignore: padding;
        mso-number-format: "General";
        text-align: general;
        vertical-align: middle;
        white-space: nowrap;
        mso-rotate: 0;
        mso-pattern: auto;
        mso-background-source: auto;
        color: #000000;
        font-size: 11.0pt;
        font-weight: 400;
        font-style: normal;
        text-decoration: none;
        font-family: 宋体;
        mso-generic-font-family: auto;
        mso-font-charset: 134;
        border: none;
        mso-protection: locked visible;
    }

    .xl65 {
        mso-style-parent: style0;
        text-align: center;
        white-space: normal;
        color: windowtext;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border-left: .5pt solid windowtext;
        border-top: .5pt solid windowtext;
    }

    .xl66 {
        mso-style-parent: style0;
        text-align: center;
        white-space: normal;
        color: windowtext;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border-top: .5pt solid windowtext;
    }

    .xl67 {
        mso-style-parent: style0;
        text-align: center;
        white-space: normal;
        color: windowtext;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border-top: .5pt solid windowtext;
        border-right: .5pt solid windowtext;
    }

    .xl68 {
        mso-style-parent: style0;
        text-align: center;
        white-space: normal;
        mso-pattern: auto none;
        background: #FCE4D6;
        color: windowtext;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border-left: .5pt solid windowtext;
        border-top: .5pt solid windowtext;
        border-right: .5pt solid windowtext;
    }

    .xl69 {
        mso-style-parent: style0;
        text-align: center;
        white-space: normal;
        mso-pattern: auto none;
        background: #FCE4D6;
        color: windowtext;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border: .5pt solid windowtext;
    }

    .xl70 {
        mso-style-parent: style0;
        text-align: center;
        white-space: normal;
        color: windowtext;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border-left: .5pt solid windowtext;
        border-bottom: .5pt solid windowtext;
    }

    .xl71 {
        mso-style-parent: style0;
        text-align: center;
        white-space: normal;
        color: windowtext;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border-bottom: .5pt solid windowtext;
    }

    .xl72 {
        mso-style-parent: style0;
        text-align: center;
        white-space: normal;
        color: windowtext;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border-right: .5pt solid windowtext;
        border-bottom: .5pt solid windowtext;
    }

    .xl73 {
        mso-style-parent: style0;
        text-align: center;
        white-space: normal;
        mso-pattern: auto none;
        background: #FCE4D6;
        color: windowtext;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border-left: .5pt solid windowtext;
        border-right: .5pt solid windowtext;
        border-bottom: .5pt solid windowtext;
    }

    .xl74 {
        mso-style-parent: style0;
        text-align: center;
        white-space: normal;
        color: windowtext;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border: .5pt solid windowtext;
    }

    .xl75 {
        mso-style-parent: style0;
        text-align: center;
        white-space: normal;
        mso-pattern: auto none;
        background: #FFFFFF;
        color: windowtext;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border: .5pt solid windowtext;
    }

    .xl76 {
        mso-style-parent: style0;
        text-align: center;
        white-space: normal;
        mso-pattern: auto none;
        background: #FFFFFF;
        color: #FF0000;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border: .5pt solid windowtext;
    }

    .xl77 {
        mso-style-parent: style0;
        text-align: center;
        white-space: normal;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border: .5pt solid windowtext;
    }

    .xl78 {
        mso-style-parent: style0;
        text-align: center;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border: .5pt solid windowtext;
    }

    .xl79 {
        mso-style-parent: style0;
        text-align: center;
        mso-pattern: auto none;
        background: #FCE4D6;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border: .5pt solid windowtext;
    }

    .xl80 {
        mso-style-parent: style0;
        text-align: center;
        white-space: normal;
        mso-pattern: auto none;
        background: #FFFF00;
        color: windowtext;
        font-size: 12.0pt;
        font-weight: 700;
        mso-font-charset: 134;
        border: .5pt solid windowtext;
    }
</style>
