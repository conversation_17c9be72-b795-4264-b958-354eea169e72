<template>
    <div class="table">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item>
                    <i class="el-icon-lx-cascades"></i> 订单统计
                </el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo" :inline="true">
                    <el-row>
                        <el-col :span="10">
                            <el-form-item label="服务项目">
                                <el-select
                                    v-model="productCategory"
                                    placeholder="请选择"
                                    @change="changeValue"
                                    style="width:180px"
                                    class="mr10"
                                >
                                    <el-option
                                        v-for="item in options"
                                        :key="item.value"
                                        :label="item.text"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                                <el-select
                                    v-model="dto.productId"
                                    placeholder="请选择"
                                    style="width:180px"
                                    class="mr10"
                                >
                                    <el-option
                                        v-for="item in options_children"
                                        :key="item.value"
                                        :label="item.text"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="10">
                            <el-form-item label="服务金额">
                                <el-input
                                    v-model="dto.consumptionTotal1"
                                    placeholder="消费范围"
                                    style="width:150px"
                                    class="handle-input mr10"
                                ></el-input>-
                                <el-input
                                    v-model="dto.consumptionTotal2"
                                    placeholder="消费范围"
                                    style="width:150px"
                                    class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4" style="text-align:right">
                            <el-button type="primary" @click="onSubmit">搜索</el-button>
                            <el-button type="success" @click="exportExcel">导出Excel</el-button>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="10">
                            <el-form-item label="服务时间">
                                <el-date-picker
                                    v-model="realStartTime"
                                    type="daterange"
                                    align="right"
                                    unlink-panels
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    format="yyyy 年 MM 月 dd 日"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="10">
                            <el-form-item label="取消时间">
                                <el-date-picker
                                    v-model="cancelTime"
                                    type="daterange"
                                    align="right"
                                    unlink-panels
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    format="yyyy 年 MM 月 dd 日"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="10">
                            <el-form-item label="服务区域">
                                <el-cascader :options="serviceOptions" v-model="serviceArray"></el-cascader>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="服务地址">
                                <el-input
                                    v-model="dto.street"
                                    placeholder="服务地址"
                                    style="width:250px"
                                    class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="会员ID">
                                <el-input
                                    v-model="dto.memberId"
                                    placeholder="会员ID"
                                    style="width:150px"
                                    class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="下单时间">
                                <el-date-picker
                                    v-model="dto.createTime1"
                                    type="datetime"
                                    placeholder="开始时间"
                                    format="yyyy-MM-dd HH:mm"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                            <!-- </el-col>
                            <el-col :span="6">-->
                            <el-form-item label="至">
                                <el-date-picker
                                    v-model="dto.createTime2"
                                    type="datetime"
                                    placeholder="结束时间"
                                    format="yyyy-MM-dd HH:mm"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="服务状态">
                                <el-select
                                    v-model="dto.orderState"
                                    placeholder="请选择"
                                    style="width:180px"
                                    class="mr10"
                                >
                                    <el-option
                                        v-for="item in options_orderState"
                                        :key="item.value"
                                        :label="item.text"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div style="text-align:right">{{theader}}</div>
            <el-table
                :data="data"
                border
                class="table"
                ref="multipleTable"
                @sort-change="sortChange"
            >
                <el-table-column
                    prop="billNo"
                    label="订单号"
                    width="100"
                    sortable="custom"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                    prop="code"
                    label="开发人"
                    width="100"
                    sortable="custom"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                    prop="belongChannel"
                    label="所属渠道"
                    width="120"
                    sortable="custom"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                    prop="orderState"
                    label="服务状态"
                    width="120"
                    sortable="custom"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                    prop="productName"
                    label="服务项目"
                    width="100"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column prop="areaName" label="服务区域" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column
                    prop="street"
                    label="服务地址"
                    width="120"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                    prop="totalAmount"
                    label="服务金额"
                    sortable="custom"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                    prop="amount"
                    label="实付金额"
                    sortable="custom"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                    prop="realStartTime"
                    label="服务时间"
                    sortable="custom"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                    prop="createTime"
                    label="下单时间"
                    sortable="custom"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                    prop="cancelTime"
                    label="取消时间"
                    sortable="custom"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column prop="cancelReason" label="取消原因" :show-overflow-tooltip="true"></el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    background
                    layout="total,sizes,prev, pager, next,jumper"
                    :total="pageInfo.total"
                    :page-size="pageInfo.size"
                    :current-page="pageInfo.current"
                    :page-sizes="[10,20,50,100]"
                    :page-count="pageInfo.pages"
                    @size-change="sizeChange"
                    @current-change="currentChange"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "basetable",
    data() {
        return {
            tableData: [],
            multipleSelection: [],

            pageInfo: { total: 10, size: 10, current: 1, pages: 1 },
            theader: "",
            registerTime: [],

            productCategory: "",
            options: [], //服务项目一级
            options_children: [], //服务项目二级
            realStartTime: [],
            cancelTime: [],
            serviceOptions: [], //服务区域
            serviceArray: [],
            options_orderState: [],
            dto: {
                areaId: "",
                productId: "",
                realStartTime1: "",
                realStartTime2: "",
                cancelTime1: "",
                cancelTime2: "",
                street: "",
                totalAmount1: "",
                totalAmount2: "",
                createTime1: "",
                createTime2: "",
                orderState: "",
                memberId: "",

                export: "",
                orderBy: "",
                pageIndex: "",
                pageSize: ""
            }
        };
    },
    created() {
        this.$getData("getProductCategory").then(res => {
            res.data.unshift({ text: "请选择", value: "" });
            this.options = res.data;
        });
        // 服务区域下拉框
        this.$getData("getCityArea").then(res => {
            res.data.unshift({ label: "请选择", value: "" });
            this.serviceOptions = res.data;
        });

        this.$getData("getOrderState", {}).then(res => {
            res.data.unshift({ text: "请选择", value: "" });
            this.options_orderState = res.data;
        });

        this.getData();
    },
    computed: {
        data() {
            return this.tableData.filter(d => {
                d.realStartTime =
                    d.realStartTime == null
                        ? null
                        : d.realStartTime.replace("T", " ").substring(0, 19);
                d.createTime =
                    d.createTime == null
                        ? null
                        : d.createTime.replace("T", " ").substring(0, 19);
                d.cancelTime =
                    d.cancelTime == null
                        ? null
                        : d.cancelTime.replace("T", " ").substring(0, 19);
                return d;
            });
        }
    },
    methods: {
        changeValue(value) {
            this.dto.productId = "";
            this.$getData("getProductName", { productCategoryId: value }).then(
                res => {
                    this.options_children = res.data;
                }
            );
        },
        getData() {
            this.openFullScreen();
            this.$postData("operate_cancelOrders", this.dto, {})
                .then(res => {
                    if (res.meta.state == 200) {
                        this.tableData = res.data.tbody.records;
                        this.pageInfo = res.data.tbody;

                        let theader = "";
                        for (let i of res.data.theader) {
                            theader += i + " ";
                        }
                        this.theader = theader;
                        this.closeFullScreen();
                    } else {
                        this.closeFullScreen();
                        this.$message.error("查询失败，" + res.meta.msg);
                    }
                })
                .catch(error => {
                    this.closeFullScreen();
                    this.$message.error("查询失败，系统超时");
                });
        },
        onSubmit() {
            this.dto.realStartTime1 = this.realStartTime?this.realStartTime[0]||"":"";
            this.dto.realStartTime2 = this.realStartTime?this.realStartTime[1]||"":"";
            this.dto.cancelTime1 = this.cancelTime?this.cancelTime[0]||"":"";
            this.dto.cancelTime2 = this.cancelTime?this.cancelTime[1]||"":"";

            this.dto.areaId = this.serviceArray[this.serviceArray.length - 1];
            this.dto.export = "";
            // this.dto.orderBy = "";
            // this.dto.pageSize = "";
            this.dto.pageIndex = "1";

            this.getData();
        },
        exportExcel() {
            this.dto.export = "Excel";
            this.$postData("operate_cancelOrders", this.dto, {
                responseType: "arraybuffer"
            }).then(res => {
                this.blobExport({
                    tablename: "订单统计",
                    res: res
                });
                this.dto.export = ""
            });
        },
        // 页码大小
        sizeChange(size) {
            this.dto.pageSize = size;
            this.dto.pageIndex = 1;
            this.getData();
        },
        // 跳转页码
        currentChange(index) {
            this.dto.pageIndex = index;
            this.getData();
        },
        blobExport({ tablename, res }) {
            const aLink = document.createElement("a");
            let blob = new Blob([res], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.download = tablename + ".xlsx";
            document.body.appendChild(aLink);
            aLink.click();
            document.body.removeChild(aLink);
        },
        openFullScreen() {
            const loading = this.$loading({
                lock: true,
                text: "Loading",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)"
            });
        },
        closeFullScreen() {
            const loading = this.$loading({
                lock: true,
                text: "Loading",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)"
            });
            loading.close();
        },
        sortChange: function(column, prop, order) {
            if (column == null) {
                this.dto.orderBy = "";
            } else {
                if (column.order == "ascending") {
                    this.dto.orderBy = column.prop + " ASC";
                }
                if (column.order == "descending") {
                    this.dto.orderBy = column.prop + " DESC";
                }
            }
            this.getData();
        },
        sortChange: function(column, prop, order) {
            if (column == null) {
                this.dto.orderBy = "";
            } else {
                if (column.order == "ascending") {
                    this.dto.orderBy = column.prop + " ASC";
                }
                if (column.order == "descending") {
                    this.dto.orderBy = column.prop + " DESC";
                }
            }
            this.getData();
        }
    }
};
</script>

<style scoped>
.handle-box {
    margin-bottom: 20px;
}

.handle-select {
    width: 120px;
}

.handle-input {
    width: 300px;
    display: inline-block;
}
.del-dialog-cnt {
    font-size: 16px;
    text-align: center;
}
.table {
    width: 100%;
    font-size: 14px;
}
.red {
    color: #ff0000;
}
.mr10 {
    margin-right: 10px;
}
</style>
