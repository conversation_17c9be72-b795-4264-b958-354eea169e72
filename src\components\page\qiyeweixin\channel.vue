<template>
  <div class="container">
    <div class="handle-box">
      <el-form :inline="true" :model="pageInfo" class="demo-form-inline">
        <el-form-item>
          <el-button type="primary" plain @click="change()">切换</el-button>
        </el-form-item>
        <el-form-item label="活码名称" v-if="!group">
          <el-input v-model="dom.name" placeholder="活码名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="使用部门" v-if="!group">
          <el-input v-model="dom.partyName" placeholder="使用者部门" clearable></el-input>
        </el-form-item>
        <el-form-item label="使用员工" v-if="!group">
          <el-input v-model="dom.userName" placeholder="使用者名称" clearable></el-input>
        </el-form-item>
        <el-form-item v-if="!group">
          <el-button type="primary" @click="onSubmit">查询</el-button>
        </el-form-item>
        <el-form-item v-if="!group">
          <el-button icon="el-icon-refresh" @click="reset()">
            重置
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" plain icon="el-icon-plus" @click="dialogFormVisible = true">创建活码</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" icon="el-icon-plus" @click="createGroup = true">创建组</el-button>
        </el-form-item>
        <el-form-item v-if="!group">
          <el-button v-if="!choice" type="warning" @click="choice = !choice">移动二维码</el-button>
          <el-button v-if="choice" type="primary" @click="move()" plain>移动到</el-button>
          <el-select v-if="choice" v-model="value" clearable placeholder="请选择">
            <el-option
                v-for="item in list2"
                :key="item.id"
                :label="item.name"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="group">
      <el-table :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" key="list2"
                :data="list2" @row-dblclick="groupDetails" stripe v-loading="loading2" class="table" border
                style="width: 100%">
        <el-table-column
            label="名称"
            width="250">
          <template slot-scope="scope">
            <el-row>
              <el-col :span="6"><i class="el-icon-folder" style="font-size:40px"></i></el-col>
              <el-col :span="18">
                <div v-if="scope.row.id!==rename" style="margin-left: 5px">{{ scope.row.name }}</div>
                <el-input v-if="scope.row.id===rename" clearable @change="renaming(scope.row)"
                          v-model="scope.row.name" placeholder="请输入内容"></el-input>
                <div style="margin-left: 5px">{{ scope.row.quantity }}个二维码</div>
              </el-col>
            </el-row>
          </template>
        </el-table-column>
        <el-table-column
            label="添加的好友数"
            width="180">
          <template slot-scope="scope">
            <div slot="reference" class="name-wrapper">
              <el-tag size="medium">{{ scope.row.num }}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" v-if="scope.row.id!==rename" type="warning" @click="handleEdit(scope.row.id)">重命名
            </el-button>
            <el-button size="mini" v-if="scope.row.id===rename" type="warning" plain @click="cancel()">取消重命名</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="pageInfo2.total"
              @on-change="onChange2"
              :current="this.dam.current"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts2"
              @on-page-size-change="onPageSizeChange2"
              :page-size="10"/>
      </div>
    </div>
    <div v-if="!group">
      <el-table :data="list" :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" key="list"
                stripe v-loading="loading" class="table" border style="width: 100%" @select="pitchOn"
                @select-all="pitchOns">
        <el-table-column v-if="choice"
                         type="selection"
                         width="55">
        </el-table-column>
        <el-table-column
            prop="name"
            label="活码名称"
            :show-overflow-tooltip="true"
            width="100">
        </el-table-column>
        <el-table-column
            prop="type"
            label="类型"
            width="64">
          <template slot-scope="scope">
            <el-tag :type="scope.row.type === 1 ? 'success' : ''"
                    disable-transitions>
            <span v-if=" scope.row.type===1">
                            单人
                        </span>
              <span v-if=" scope.row.type===2">
                            多人
                        </span>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
            prop="partyName"
            label="部门"
            :show-overflow-tooltip="true"
            width="110">
        </el-table-column>
        <el-table-column
            prop="userName"
            label="使用员工"
            :show-overflow-tooltip="true"
            width="100">
        </el-table-column>
        <el-table-column
            prop="welcomeType"
            label="回复类型"
            width="70">
          <template slot-scope="scope">
            <span v-if=" scope.row.welcomeType==='0'">
                            文本
                        </span>
            <span v-if=" scope.row.welcomeType==='1'">
                            图片
                        </span>
            <span v-if=" scope.row.welcomeType==='2'">
                            图文
                        </span>
            <span v-if=" scope.row.welcomeType==='3'">
                            小程序
                        </span>
          </template>
        </el-table-column>
        <el-table-column
            prop="label"
            label="标签"
            :show-overflow-tooltip="true"
            width="150">
        </el-table-column>
        <el-table-column fixed="right" label="操作">
          <template slot-scope="scope">
            <div style="display: flex">
              <el-button size="small" type="info" plain    v-show="scope.row.qiyeChannelGroupById!==4&&scope.row.qiyeChannelGroupById!==8"
                         @click="statistics(scope.row.id,scope.row.qiyeStateId)">
                统计
              </el-button>
              <el-button size="small " icon="el-icon-download" type="primary"
                         v-show="scope.row.qiyeChannelGroupById!==4&&scope.row.qiyeChannelGroupById!==8"
                         @click="downloadUrl(scope.row)">
                下载二维码
              </el-button>
              <el-button size="small " icon="el-icon-document-copy" type="success"
                         v-show="scope.row.qiyeChannelGroupById!==4&&scope.row.qiyeChannelGroupById!==8"
                         class="tag-read" :data-clipboard-text="scope.row.qr_code" @click="copyUrl()">
                复制链接
              </el-button>
              <el-button size="small " icon="el-icon-edit-outline"
                         @click="updateChannel(scope.row.id)">
                编辑
              </el-button>
              <el-button v-if="(roleId==='1'||id==='28782')&&scope.row.qiyeChannelGroupById!==4&&scope.row.qiyeChannelGroupById!==8" size="small "
                         icon="el-icon-delete" type="warning" plain
                         @click="deleteChannel(scope.row.config_id)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="pageInfo.total"
              @on-change="onChange"
              :current="this.dom.current"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onPageSizeChange"
              :page-size="10"/>
      </div>
    </div>
    <el-dialog title="建立活码" :visible.sync="dialogFormVisible" center>
      <qiyeState @childByvalue="childByvalue" :reset2="reset"
                 :qiyeChannelGroupById="this.dom.qiyeChannelGroupById"></qiyeState>
    </el-dialog>
    <el-dialog title="活码" :visible.sync="editChannel" center>
      <updateState @updateByvalue="updateByvalue" :qiyeChannelId="qiyeChannelId"></updateState>
      <div slot="footer" class="dialog-footer">
      </div>
    </el-dialog>
    <el-dialog title="创建组" :visible.sync="createGroup" center>
      <el-row>
        <el-col :span="4">组名：</el-col>
        <el-col :span="20">
          <el-input placeholder="请输入组名" v-model="input1"></el-input>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
    <el-button @click="createGroup = false">取 消</el-button>
    <el-button type="primary" @click="addGroup()">确 定</el-button>
  </span>
    </el-dialog>
  </div>
</template>
<script>
//引入组件
import Clipboard from "clipboard";
import qiyeState from "./qiyeState";
import updateState from "./updateState";


export default {
  name: "channel",
  // 注册组件
  components: {
    qiyeState,
    updateState,
  },
  data() {
    return {
      id: localStorage.getItem("id"),
      rename: null,
      choice: false,
      group: true,
      input1: '',
      value: null,
      qrCode: [],
      qiyeChannelId: null,
      dialogFormVisible: false,
      createGroup: false,
      editChannel: false,
      roleId: null,
      list: null,
      list2: null,
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      pageInfo2: {total: 10, size: 10, current: 1, pages: 1},
      loading: true,
      loading2: true,
      pageSizeOpts: [10, 20, 30],
      pageSizeOpts2: [10, 20, 30],
      dom: {
        name: null,
        userName: null,
        partyName: null,
        config_id: null,
        creatorId: null,
        qiyeChannelGroupById: null,
        size: 10,
        current: 1,
      },
      dam: {
        size: 10,
        current: 1,
      }
    };
  },
  created() {
    if (localStorage.getItem("roleId") === '1' || localStorage.getItem("roleId") === '9' || localStorage.getItem("roleId") === '78') {
      this.getData()
      this.selectQiyeChannelGroupByIPage()
    } else {
      this.dom.creatorId = localStorage.getItem("id")
      this.getData()
      this.selectQiyeChannelGroupByIPage()
    }
    this.roleId = localStorage.getItem("roleId")
  },
  methods: {
    change() {
      this.group = !this.group
      this.dom.size = 10
      this.dom.current = 1
      this.getData();
      this.selectQiyeChannelGroupByIPage()
    },
    renaming(val) {
      this.$postData("updateQiyeChannelGroupBy", val).then(res => {
        if (res.status === 200) {
          if (res.data === 1) {
            this.$message({message: '重命名成功', type: 'success'});
          } else {
            this.$message.error({message: '重命名失败', type: 'warning'});
          }
        }
      })
      this.rename = null
    },
    cancel() {
      this.rename = null
    },
    handleEdit(val) {
      this.rename = val
    },
    pitchOns(val) {
      let bbb = []
      val.forEach(function (item) {
        bbb.push(item.id)
      })
      this.qrCode = bbb
    },
    pitchOn(val) {
      let aaa = []
      val.forEach(function (item) {
        aaa.push(item.id)
      })
      this.qrCode = aaa
    },
    move() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      if (this.qrCode.length < 1) {
        return this.$message.error('请先选择要移动的二维码');
      }
      if (this.value === null) {
        return this.$message.error('请选择要移动过去的分组');
      }
      let a = {
        qrCode: this.qrCode.join(","),
        value: this.value,
      }
      this.$postData("mobileQrCod", a).then(res => {
        if (res.status === 200) {
          if (res.data === 1) {
            this.$message({message: '移动成功', type: 'success'});

          } else {
            this.$message.error({message: '移动失败', type: 'warning'});
          }
          loading.close();
          this.choice = !this.choice
          this.getData();
        }
      })

    },
    groupDetails(val) {
      this.group = !this.group
      this.dom.qiyeChannelGroupById = val.id
      this.getData();
    },
    addGroup() {
      if (this.input1 == null || this.input1 === '') {
        return this.$message.error({message: '分组名不能为空', type: 'warning'});
      }
      this.loading2 = false
      this.$getData("addGroup", {name: this.input1}).then(res => {
        if (res.status === 200) {
          if (res.data === 1) {
            this.$message({message: '创建分组成功', type: 'success'});
          } else {
            this.$message.error({message: '创建分组失败', type: 'warning'});
          }
          this.createGroup = false
          this.input1 = null
          this.selectQiyeChannelGroupByIPage()
        }
      })
    },
    reset() {
      this.dom.name = null;
      this.dom.userName = null;
      this.dom.partyName = null;
      this.getData();
    },
    onSubmit() {
      this.loading = true;
      this.dom.current = 1;
      this.getData();
    },
    selectQiyeChannelGroupByIPage() {
      this.$postData("selectQiyeChannelGroupByIPage", this.dam).then(res => {
        this.loading2 = false;
        if (res.status === 200) {
          this.list2 = res.data.records;
          this.pageInfo2.current = res.data.current;
          this.pageInfo2.size = res.data.size;
          this.pageInfo2.total = res.data.total
        }
      })
    },
    getData() {
      this.$postData("selectQiyeChannelIPage", this.dom).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dom.size = size;
      this.getData();
    },
    onPageSizeChange2(size) {
      this.loading2 = true;
      this.dam.size = size;
      this.selectQiyeChannelGroupByIPage();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dom.current = index;
      this.getData();
    },
    onChange2(index) {
      this.loading2 = true;
      this.dam.current = index;
      this.selectQiyeChannelGroupByIPage();
    },
    //删除活码
    deleteChannel(config_id) {
      this.loading = true;
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$getData("delQiyeState", {config_id: config_id, operatorId: localStorage.getItem("id")}).then(res => {
          if (res.status === 200) {
            if (res.data) {
              this.loading = false;
              this.$message({message: '删除成功', type: 'success'});
              this.getData();
            } else {
              this.$message.error('删除失败');
            }
          }
        })
      }).catch(() => {
        this.loading = false;
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },//更新活码
    updateChannel(id) {
      this.editChannel = true
      this.qiyeChannelId = id
    },
    statistics(id, qiyeStateId) {
      this.$router.push({path: '/statistics', query: {id: id, qiyeStateId: qiyeStateId}});
    },
    //下载二维码
    downloadUrl(qiyeChannel) {
      var image = new Image();
      image.setAttribute("crossOrigin", "anonymous");
      var _this = this;
      image.onload = function () {
        var canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        var context = canvas.getContext("2d");
        context.drawImage(image, 0, 0, image.width, image.height);
        var url = canvas.toDataURL("image/png"); //得到图片的base64编码数据
        var a = document.createElement("a"); // 生成一个a元素
        var event = new MouseEvent("click"); // 创建一个单击事件
        a.download = _this.projectName || qiyeChannel.name; // 设置图片名称
        a.href = url; // 将生成的URL设置为a.href属性
        a.dispatchEvent(event); // 触发a的单击事件
      };
      image.src = qiyeChannel.qr_code;
    },
    //复制链接
    copyUrl() {
      var clipboard = new Clipboard('.tag-read')
      clipboard.on('success', e => {
        this.$message({
          message: '复制成功',
          type: 'success'
        });
        // 释放内存
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        this.$message.error('该浏览器不支持自动复制');
        // 不支持复制
        // 释放内存
        clipboard.destroy()
      })
    },
    childByvalue(val) {
      this.dialogFormVisible = val
    },
    updateByvalue(val) {
      this.editChannel = val
      this.reset()
    },
  },
}
</script>

<style scoped>
table {
  border-collapse: collapse; /*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
  border-left: #C8B9AE solid 1px;
  border-top: #C8B9AE solid 1px;
}

.demo-table-expand {
  font-size: 0;
}

.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
</style>
