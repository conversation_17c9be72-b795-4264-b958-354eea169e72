<template>
    <div style="background:#fff;padding: 20px;">

        <el-row :gutter="20">
            <el-col :span="4">
                <el-card class="box-card">
                    <div style="line-height: 20px">

                        <center>
                            <img :src="show.headPortrait" width="120px">
                        </center>

                        <br>
                        <el-tag v-if="show.baomuLevel==1" type="Danger" @click="openby(show,0)">百优保姆</el-tag>
                        <el-tag v-else-if="show.baomuLevel==0 " type="info" @click="openby(show,2)">普通保姆</el-tag>
                        <el-tag v-else-if="show.baomuLevel==2" type="warning" @click="openby(show,1)">推荐保姆</el-tag>

                        <el-tag v-if="show.state==1" type="success">上架</el-tag>
                        <el-tag v-else-if="show.state==2" type="warning">下架</el-tag>
                        <el-tag v-else-if="show.state==3" type="warning">离职</el-tag>
                        <el-tag v-else-if="show.state==4" type="danger">黑名单</el-tag>
                        <br>
                        <br>
                        <el-card shadow="hover">
                            <h3>
                                名称:<b>{{show.realName}}</b><br>
                                编号:<b>{{show.no}}</b><br>

                            </h3>

                            手机:{{show.phone}}<br>
                            年龄:{{show.age}}<br>
                            经纪人:{{show.agentName}}<br>
                            经验:{{show.workYear}}年<br>
                        </el-card>
                        <br>
                        <center>
                            <el-button type="primary" icon="el-icon-refresh" @click="re()">刷新信息</el-button>
                        </center>
                        <br>
                        <div style="line-height: 22px">
                            现住地:{{show.address}}<br>
                            户籍:{{show.hometown}}<br>
                            入职时间:<b>{{show.entryTime}}</b><br>
                            <div>工作方式:{{show.baomuWorkType}}</div>
                            <div>工作类型:{{show.workType}}</div>
                            <div>健康状态:{{show.health}}</div>
                            <div>服务类型:{{show.serverContent}}</div>
                            <div>招聘来源:{{show.source}}</div>
                            <div>其他技能:{{show.otherSkills}}</div>
                        </div>

                    </div>
                </el-card>
            </el-col>
            <el-col :span="20">
                <el-tabs type="border-card" v-if="loding">
                    <el-tab-pane label="基本资料">
                        <div class="addBody">
                            <baomuUpdate :model="show" style="max-width: 1200px"></baomuUpdate>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="等级">
                        <BaomuLevel :model="show" style="max-width: 1200px"></BaomuLevel>
                    </el-tab-pane>
                    <el-tab-pane label="认证">
                        <baomuImg :model="show"></baomuImg>
                    </el-tab-pane>
                    <el-tab-pane label="经验">
                        <baomuExp :model="show"></baomuExp>
                    </el-tab-pane>
                    <el-tab-pane label="技能">
                        <baomuSkill :model="show"></baomuSkill>
                    </el-tab-pane>
                    <el-tab-pane label="日志">
                        <baomuWorkLog :employeeId="show.id"></baomuWorkLog>
                    </el-tab-pane>
                    <el-tab-pane label="回访记录">
                        <el-collapse v-model="activeNames">
                            <el-collapse-item :title="task.title+' ( '+ task.employeeName+' ) - '+task.creatTime"
                                              :name="task.title" :label="task.id" v-for="(task,index) in taskList"
                                              :key="index">
                                <div class="addBody"
                                     style="min-height: 400px;width: 50%;transform:scaleX(0.8);-webkit-transform:scaleX(0.8);  -moz-transform:scaleX(0.8);border: 1px solid #ddd;padding: 15px">
                                    <responseInfo :id="task.id"></responseInfo>
                                </div>
                            </el-collapse-item>
                        </el-collapse>
                        <!--                <el-tabs tab-position="left" type="border-card">-->
                        <!--                    <el-tab-pane :label="task.title" v-for="(task,index) in taskList" :key="index" @click="showRow(task)">-->

                        <!--                        <div class="addBody" style="min-height: 400px;">-->
                        <!--                            <responseInfo :id="task.id"></responseInfo>-->
                        <!--                        </div>-->
                        <!--                    </el-tab-pane>-->
                        <!--                </el-tabs>-->
                    </el-tab-pane>
                    <el-tab-pane label="展示">
                        <el-row>
                            <!--                            <el-button type="primary" icon="el-icon-refresh" @click="reH()">刷新信息</el-button>-->
                            <center>
                                <el-col :span="12">

                                    <iframe
                                            id="iframeId" :src="url" frameborder="0" class="pc iframe" scrolling="auto">
                                    </iframe>

                                </el-col>

                            </center>
                            <el-col :span="12">
                                <h3>保姆海报</h3><br>
                                <baomuhb :model="show" style="max-width: 280px"></baomuhb>
                            </el-col>

                        </el-row>

                    </el-tab-pane>
                </el-tabs>
            </el-col>
        </el-row>

        <el-backtop target=".page-component__scroll .el-scrollbar__wrap"></el-backtop>
    </div>

</template>

<script>
    import responseInfo from '@/components/page/agent/info/responseInfo.vue'
    import baomuAdd from '@/components/page/agent/choose/baomuAdd.vue'
    import baomuUpdate from '@/components/page/agent/choose/baomuUpdate.vue'
    import baomuExp from '@/components/page/agent/choose/baomuExp.vue'
    import baomuSkill from '@/components/page/agent/choose/baomuSkill.vue'
    import baomuImg from '@/components/page/agent/choose/baomuImg.vue'
    import baomuhb from '@/components/page/agent/choose/baomuhb.vue'
    import baomurate from '@/components/page/agent/choose/baomurate.vue'
    import baomuWorkLog from "@/components/page/agent/choose/baomuWorkLog";
    import BaomuLevel from "@/components/page/agent/choose/BaomuLevel";
    import jzyDetail from "@/components/page/agent/choose/jzyDetail";
    import {dateFormat} from 'vux'

    export default {
        props: ['baomuid'],
        name: "baomuInfo",
        data() {
            return {
                url: null,
                activeNames: [],
                taskList: [],
                showBaomuUpdate: true,
                loding: false,
                id: this.$route.query.id,
                show: {
                    id: null,
                    baomuId: null,
                },
            }
        },
        created() {
            if ((this.$route.query.id == null || this.$route.query.id == undefined) && this.baomuid != null) {
                this.id = this.baomuid
            }

            // console.log(this.baomuid)
            this.getData();
            this.getTaskData();
        },
        methods: {
            reH() {
                this.url = null;
                this.url = 'https://agent.xiaoyujia.com/nannyInfo/' + this.show.id;
            },
            getTaskData() {
                // if (this.user.roleId!=='1'){
                //     if (this.user.roleId !=='66'){
                //         this.form.employeeId=this.user.id
                //     }else {
                //         this.form.storeId=this.user.storeId
                //     }
                // }
                let dom = {
                    status: 1,
                    baomuId: this.id
                };
                this.$postData("taskList", dom, {}).then(res => {
                    if (res.status === 200) {
                        this.taskList = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            re() {
                this.$router.go(0);
            },
            getData() {
                // this.loding=false
                // this.show.id=parseInt(this.id)
                this.show.baomuId = parseInt(this.id);
                this.$postData("baomu_list", this.show, {}).then(res => {
                    if (res.status === 200) {
                        this.show = res.data[0];
                        this.show.baomuId = this.show.id;
                        this.loding = true;
                        this.url = 'https://agent.xiaoyujia.com/nannyInfo/' + this.show.id;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            openby(item, baomuLevel) {
                // if (baomuLevel!=2){
                //     if (this.account!='820' && this.account!='789'){
                //         return this.$message.error("你无权操作")
                //     }
                // }
                const roleId = localStorage.getItem('roleId');
                if (roleId == null) {
                    return this.$message.error("你无权操作");
                }
                if ((roleId !== '1' || roleId !== '9')) {
                    return this.$message.error("你无权操作");
                }
                this.$confirm('是否确认更改保姆等级', '认证确认', {
                    distinguishCancelAndClose: true,
                    confirmButtonText: '确定',
                    cancelButtonText: baomuLevel == 2 ? '取消' : '驳回',
                    type: 'warning',
                    callback: action => {
                        let baomuWorkLog = {
                            employeeId: item.id,
                            workContent: baomuLevel == 0 ? '变更为（普通保姆）' : baomuLevel == 1 ? '变更为（百优保姆）' : '变更为（推荐保姆）',
                            remark: null,
                            status: 1,
                            billNo: null,
                            contractNo: null,
                            startTime: dateFormat(new Date(), 'YYYY-MM-DD HH:mm:ss'),
                            endTime: null,
                            creatTime: dateFormat(new Date(), 'YYYY-MM-DD HH:mm:ss'),
                            crePerson: localStorage.getItem("realName"),
                            type: 0,
                            title: '保姆级别变更'
                        }
                        if (action === 'confirm') {

                            let baomu = {
                                baomuId: item.id,
                                baomuLevel: baomuLevel
                            };
                            this.$postData("updateBaomuLevelByBaomuId", baomu, {}).then(res => {
                                if (res.status === 200) {
                                    item.baomuLevel = baomu.baomuLevel;
                                    this.$Message.success('更改成功');
                                    this.saveBaomuWorkLog(baomuWorkLog);
                                    this.chooseThisModel();
                                } else {
                                    this.$message.error("更改失败，" + res.msg);
                                }
                            })
                        }
                        if (action === 'cancel') {
                            // console.log(item)
                            let baomu = {
                                baomuId: item.id,
                                baomuLevel: 0
                            };
                            this.$postData("updateBaomuLevelByBaomuId", baomu, {}).then(res => {
                                if (res.status === 200) {
                                    item.baomuLevel = baomu.baomuLevel;
                                    this.$Message.success('更改成功');
                                    baomuWorkLog.workContent = "推荐驳回为（普通保姆）";
                                    this.saveBaomuWorkLog(baomuWorkLog);
                                    this.chooseThisModel();
                                } else {
                                    this.$message.error("更改失败，" + res.msg);
                                }
                            })
                        }
                    }
                });
            },
        },
        components: {
            'responseInfo': responseInfo,
            "BaomuLevel": BaomuLevel,
            "baomuWorkLog": baomuWorkLog,
            'baomuAdd': baomuAdd,
            'baomuUpdate': baomuUpdate,
            'baomuExp': baomuExp,
            'baomuImg': baomuImg,
            'baomuSkill': baomuSkill,
            'baomuhb': baomuhb,
            'baomurate': baomurate,
            'jzyDetail': jzyDetail,

        },
    }
</script>

<style scoped>

    .iframe {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        border: 1px solid #ddd;
        padding: 10px;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        width: 375px;
        height: 667px;
        background: #fff;
        overflow-y: hidden;
    }


</style>
