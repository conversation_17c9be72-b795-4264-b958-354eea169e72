<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">
                    <el-row style="margin-top: 10px">
                        <el-col :span="24">
                            <el-form-item>
                                <el-button type="primary" :loading="bindLoading" icon="el-icon-plus"
                                           @click="bind">一键绑定
                                </el-button>
                                <!--<el-button type="info" icon="el-icon-download">导出</el-button>-->
                                <el-button icon="el-icon-refresh" @click="getData">刷新
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-table :data="list" class="table" ref="multipleTable"
                      :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}"
                      v-loading="loading">
                <el-table-column
                        width="180"
                        prop="billNo"
                        label="订单号">
                </el-table-column>
                <el-table-column
                        width="170"
                        prop="order.productName"
                        label="服务项目"
                ></el-table-column>
                <el-table-column
                        width="190"
                        prop="order.startTime"
                        label="服务时间"
                ></el-table-column>
                <el-table-column
                        width="120"
                        prop="order.realTotalAmount"
                        label="订单金额"
                ></el-table-column>
                <el-table-column
                        width="120"
                        prop="order.orderStateDesc"
                        label="订单状态"
                ></el-table-column>
                <el-table-column
                        width="170"
                        prop="billingAmount"
                        label="开票金额">
                    <template slot-scope="scope">
                        <el-input
                                @blur="update(scope.row)"
                                style="width: 150px"
                                v-model="scope.row.billingAmount"
                                placeholder="开票金额"
                        ></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                        width="150"
                        label="操作">
                    <template slot-scope="scope">
                        <el-row>
                            <el-col :span="24">
                                <div style="display: flex">
                                    <el-button
                                            size="small "
                                            type="text"
                                            @click="deleteOrder(scope.row.id)"
                                            icon="el-icon-delete">
                                        删除
                                    </el-button>
                                </div>
                            </el-col>
                        </el-row>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
    export default {
        props: ['projectId'],
        data() {
            return {
                bindLoading: false,
                loading: true,
                loadingExcel: false,
                isModal: false,
                isModal1: false,
                modalUp: false,
                list: null,
                screenWidth: "60%",
                pageSizeOpts: [10, 20, 30],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                dto: {
                    name: null,
                    days: [],
                    startTime: null,
                    endTime: null,
                    state: null,
                    pageSize: 10,
                    pageNum: 1,
                },
            };
        },
        components: {},
        watch: {
            projectId: {
                handler(newValue, oldValue) {
                    this.projectId = newValue;
                    this.getData();
                }
            }
        },
        created() {
            this.getData();
        },
        computed: {},
        methods: {
            update(item) {
                this.$postData("siteOrder_update", item).then(res => {
                })
            },
            bind() {
                this.bindLoading = true;
                this.$postUrl("siteOrder_getByBind", this.projectId).then(res => {
                    this.bindLoading = false;
                    if (res.status === 200) {
                        this.$message({
                            type: 'success',
                            message: '绑定成功!'
                        });
                        this.getData()
                    }
                });
            },
            deleteOrder(id) {
                this.$confirm('此操作将永久删除该订单, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$postUrl("siteOrder_delete", id).then(res => {
                        this.getData()
                    });
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },

            getData() {
                this.$postUrl("siteOrder_getByProjectId", this.projectId).then(res => {
                    this.loading = false;
                    if (res.status === 200) {
                       ;
                        this.list = res.data;
                    }
                });
            },
            exportExcel() {
                this.loadingExcel = true;
                this.$postData("tsExportList", this.dto, {
                    responseType: "arraybuffer"
                }).then(res => {
                   ;
                    this.loadingExcel = false;
                    this.blobExport({
                        tablename: "投诉工单",
                        res: res
                    });
                });
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
        }
    };
</script>

<style scoped>
    .handle-box {
        /*margin-bottom: 10px;*/
        font-weight: bold !important;
    }

    .el-form-item__label {
        font-weight: bold !important;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;

    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }

    .el-date-editor .el-range-separator {
        padding: 0 5px;
        line-height: 32px;
        width: 7%;
        color: #303133;
    }

    .container {
        padding: 0px !important;
        background: #fff;
        border: none !important;
        border-radius: 5px;
    }
</style>
