<template>
    <div >
      <el-row :gutter="24" style="height: 50px">
        <el-col :span="2"> <el-button type="success" @click="addMenuBtn(1)">新增父菜单</el-button></el-col>
        <el-col :span="2"> <el-button type="success" @click="addMenuBtn(2)">新增子菜单</el-button></el-col>
      </el-row>

      <el-table
          :data="dataTree"
          style="width: 100%;margin-bottom: 20px;"
          row-key="id"
          stripe
          :tree-props="{children: 'child', hasChildren: 'hasChildren'}">
        <el-table-column
            prop="id"
            label="Id">
        </el-table-column>
        <el-table-column
            prop="name"
            label="菜单名称">
        </el-table-column>
        <el-table-column
            prop="url"
            label="菜单标识">
        </el-table-column>
        <el-table-column
            prop="icon"
            label="图标">
          <template slot-scope="scope">
            <div>
              <i :class="scope.row.icon" />
            </div>
          </template>
        </el-table-column>
        <el-table-column
            label="操作">
          <template slot-scope="scope">
            <div>
              <el-button type="primary" @click="editForm(scope.row)">编辑</el-button>
             <el-button type="danger" @click="deleteForm(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog
          :title="userState === 1 ? '新增菜单' : '修改菜单'"
          :visible.sync="addFormDialog"
          width="30%"
          center
          :close-on-click-modal="false"
      >
        <!-- 新增角色 -->
        <div>
          <el-form ref="ruleForm" :model="addForm" label-width="80px" :rules="userRules">
            <div v-if="userState === 1" style="margin-bottom: 5px">
              当前类型: 新增 {{addType === 1 ? '父' : '子'}} 菜单
            </div>
            <el-form-item label="父菜单" prop="resourceType" v-if="addType != 1">
              <el-select v-model="addForm.parentId" placeholder="请选择父菜单" >
                <el-option
                    v-for="item in dataTree"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="父节点ID" v-if="userState === 2">
              <el-input v-model="addForm.parentId" :disabled="true" />
            </el-form-item>
            <el-form-item label="标识码" prop="code">
              <el-input v-model="addForm.code" placeholder="标识码必须唯一" />
            </el-form-item>
            <el-form-item label="菜单名称" prop="name">
              <el-input v-model="addForm.name" placeholder="请输入菜单名称" />
            </el-form-item>
            <el-form-item label="菜单路径" prop="url">
              <el-input v-model="addForm.url" placeholder="请输入菜单路径" />
            </el-form-item>
            <el-form-item label="图标地址" prop="icon">
              <el-input
                  v-model="addForm.icon"
                  placeholder="请输入图片地址"
                  @focus="getFocus"
              >
                <template slot="prepend">
                  <i :class="addForm.icon"></i>
                </template>
              </el-input>
            </el-form-item>

          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
        <el-button @click="addFormDialog = false">取 消</el-button>
        <el-button type="primary" @click="addForm_enter('ruleForm')">确 定</el-button>
      </span>
      </el-dialog>
      <MenuIcons :dialog-visible="iconVisible" @choose="chooseIcon" @close="iconVisible = false" ></MenuIcons>
    </div>
</template>

<script>
import MenuIcons from "./MenuIcons.vue";
    export default {
      components: {MenuIcons},
        data(){
            return{
              iconVisible: false,
              addFormDialog: false,
              userState: 1,
              addType:1,
              dataTree:[],
              addForm: {
                id: '', // id
                name: '', // 权限名称
                code: '', // URI
                url: '', // 组件
                icon: '', // 图标地址
                parentId: '', //父节点id
                addType: '',//新增类型
              },
              userRules: {
                name: [
                  { required: true, message: '请输入权限名称', trigger: 'blur' }
                ],
                code: [
                  { required: true, message: '标识码不能为空', trigger: 'blur' }
                ],
                url: [
                  { required: true, message: '请输入菜单路径', trigger: 'blur' }
                ],
                icon: [
                  { required: true, message: '请选择icon', trigger: 'blur' }
                ],
              },
            }
        },
        created(){
            this.getData()
        },
        methods:{
          deleteForm(data) {
            this.$confirm('此操作将永久删除, 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.$postData("deleteMenu",data).then(res => {
                if (res.status === 200) {
                  this.getData();
                  return this.$message({
                    type: 'success',
                    message: '删除成功'
                  });

                } else {
                  return this.$message({
                    type: 'error',
                    message: res.msg
                  });
                }

              });

            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消'
              });
            });
          },
          addMenuBtn(type) {
            Object.keys(this.addForm).forEach(key => {
              this.addForm[key] = ''; // 或者设置其他默认值，比如 null
            });
            this.userState = 1;
            this.addType = type;
            this.addForm.addType = type;
            this.addFormDialog = true;

          },
          getFocus() {
            this.iconVisible = true
          },
          getData() {
            this.$getData("menuTreeData").then(res => {
              if (res.status === 200) {
                this.dataTree = res.data;
              } else {
                return this.$message({
                  type: 'error',
                  message: '系统错误'
                });
              }

            });
          } ,
          editForm (data) {
            this.userState = 2;
            this.addForm = data;
            this.addForm.child = null;
            this.addFormDialog = true;
          },
          // 组件传回值
          chooseIcon(icon) {
            this.addForm.icon = icon
            this.iconVisible = false
          },
          addForm_enter(ruleForm) {
            this.$refs[ruleForm].validate(valid => {
              if (valid) {
                if (this.userState === 1) {
                  //新增
                  if (this.addType === 1) {
                    this.addForm.parentId = 0;
                  }
                 console.log(JSON.stringify(this.addForm))
                  this.$postData("addMenu",this.addForm).then(res => {
                    if (res.status === 200) {
                      this.addFormDialog = false;
                      this.getData();
                      return this.$message({
                        type: 'success',
                        message: '新增成功'
                      });

                    } else {
                      return this.$message({
                        type: 'error',
                        message: res.msg
                      });
                    }

                  });


                } else {
                 //更新
                  console.log(JSON.stringify(this.addForm))
                  this.$postData("updateMenu",this.addForm).then(res => {
                    if (res.status === 200) {
                      this.addFormDialog = false;
                      this.getData();
                      return this.$message({
                        type: 'success',
                        message: '更新成功'
                      });

                    } else {
                      return this.$message({
                        type: 'error',
                        message: res.msg
                      });
                    }

                  });

                }
              } else {
                console.log('error submit!!')
                return false
              }
            })
          }
        }
    }
</script>

<style scoped>

</style>
