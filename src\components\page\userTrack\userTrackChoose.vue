<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <div id="searchDiv">
            </div>
            <div class="fromInput">
                <Form ref="dto" :model="dto" :label-width="80" label-position="left">
                    <FormItem label="订单号:" prop="trainTime">
                        <sapn>{{this.dto.billNo}}</sapn>
                    </FormItem>
                    <FormItem label="图片:">

                        <div v-for="item in images" style="margin-left: 0.5rem;float: left" @click="setShow(item)">
                            <el-image
                                    :src="item"
                                    style="width: 100px; height: 100px"
                                    :preview-src-list="images">
                            </el-image>
                        </div>

                    </FormItem>
                    <FormItem label="视频:">
                        <div v-for="item in dto.videoFile" style="margin-left: 0.5rem;float: left">
                            <video        style="width: 200px; height: 120px"
                                          :src="item.url" controls="controls">
                            </video>
                        </div>

                    </FormItem>
                </Form>
            </div>
            <Modal v-model="show" class="Modal" :width="screenWidth" title="查看"
                   :mask-closable="false">
                <div class="addBody">
                    <img-choose v-if="show" @init-choose="initChooseProject" :url="url"
                                @close-modal="closeCurrModal"></img-choose>
                </div>
                <div slot="footer">
                </div>
            </Modal>
        </div>
    </div>
</template>
<script>
    import imgChoose from '@/components/page/userTrack/imgChoose.vue'

    export default {
        props: ['userTrackId'],
        data() {
            return {
                evaluateList: [],
                screenWidth: '50%',
                show: false,
                dto: {
                    id: this.userTrackId,
                },
                images: [],
                url: null,
                playerOptions : {
                    playbackRates: [0.7, 1.0, 1.5, 2.0], //播放速度
                    autoplay: false, //如果true,浏览器准备好时开始回放。
                    muted: false, // 默认情况下将会消除任何音频。
                    loop: false, // 导致视频一结束就重新开始。
                    preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
                    language: 'zh-CN',
                    aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
                    fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
                    sources: [{
                        type: "",//这里的种类支持很多种：基本视频格式、直播、流媒体等，具体可以参看git网址项目
                        src: "http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/redPackage/1587125199968.mp4" //url地址
                    }],
                    poster: "../../static/images/test.jpg", //你的封面地址
                    // width: document.documentElement.clientWidth, //播放器宽度
                    notSupportedMessage: '此视频暂无法播放，请稍后再试', //允许覆盖Video.js无法播放媒体源时显示的默认信息。
                    controlBar: {
                        timeDivider: true,
                        durationDisplay: true,
                        remainingTimeDisplay: false,
                        fullscreenToggle: true  //全屏按钮
                    }
                }
            }
        },
        components: {
            "imgChoose": imgChoose
        },
        created: function () {
            this.getById();
        },
        methods: {
            getById() {
                this.$getData("userTrack_getById", {id: this.dto.id}).then(res => {
                   ;
                    if (res.status === 200) {
                        this.dto = res.data;
                        if (res.data.imgFile != null) {
                            for (let i = 0; i < res.data.imgFile.length; i++) {
                                this.images.push(res.data.imgFile[i].url)
                            }
                        }
                    }
                })
            },
            setShow(url) {
                // this.show = true;
                // this.url = url
            },
            initChooseProject() {
                this.closeCurrModal();
            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(dto) {
                this.$emit('init-choose', dto);
                //this.update(this[dto])
            },
            closeCurrModal() {
                this.show = false;
            },
        }
    }
</script>


<style scoped>


    .fromInput {
        float: left;
    }

    #bodyDiv {
        font-size: 15px !important;
        background-color: #fff;
        height: 500px;
    }
</style>

