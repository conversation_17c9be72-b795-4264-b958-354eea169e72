<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col span="24">
                    <div>
                        <div id="operBtnDiv">
                            <div id="searchDiv">
                                <Form ref="formItem" :model="formItem" :label-width="80" label-position="right"
                                      :rules="ruleValidate">
                                    <FormItem label="渠道名称" prop="name">
                                        <Input placeholder="渠道名称" v-model="formItem.name">
                                        </Input>
                                    </FormItem>

                                    <FormItem label="渠道码" prop="code">
                                        <Input placeholder="渠道码" v-model="formItem.code">
                                        </Input>
                                    </FormItem>

                                    <FormItem label="渠道类型" prop="type">
                                        <Input placeholder="渠道类型" v-model="formItem.type">
                                        </Input>
                                    </FormItem>

                                    <FormItem>
                                        <Button type="primary" @click="saveProject('formItem')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </FormItem>

                                </Form>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
    </div>
</template>


<script>

    export default {
        data() {
            return {
                userModal: false,
                userLoading: false,
                formItem: {
                    name: null,
                    code: null,   //负责人id
                    type: null,    //项目名称
                },
                ruleValidate: {
                    code: [
                        {required: true, message: '不能为空', trigger: 'blur'}
                    ],
                    name: [
                        {required: true, message: '不能为空', trigger: 'blur'},
                    ],
                    type: [
                        {required: true, message: '不能为空', trigger: 'blur'},
                    ]
                }
            }
        },

        created: function () {

        },
        methods: {

            saveProject(name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        this.$postData("channel_insert", this.formItem, {}).then(res => {
                            if (res.status === 200) {
                                this.$Message.success('添加成功');
                                this.chooseThisModel();
                            } else {
                                this.$message.error("添加失败，" + res.meta.msg);
                            }
                        })
                    }
                })
            },
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            }
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

