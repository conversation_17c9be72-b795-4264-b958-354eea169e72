<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <div id="searchDiv">
            </div>
            <div class="fromInput">
                <Form ref="dto" :model="dto" :label-width="80" label-position="left">

                    <FormItem label="旧密码" prop="trainTime">
                        <el-input
                                type="password"
                                placeholder="请输入内容"
                                v-model="dto.password">
                        </el-input>
                    </FormItem>
                    <FormItem label="新密码" prop="trainTime">
                        <el-input
                                type="password"
                                placeholder="新密码"
                                v-model="dto.newPassword">
                        </el-input>
                    </FormItem>
                    <div style="font-size: 14px;color: #606266;">备注:修改密码后需重新登录</div>
                </Form>
                <div style="text-align: center; margin-top: 2rem; margin-left: 4rem;">
                    <Button type="primary" @click="update">确定</Button>
                    <Button type="success" style="margin-left:3rem" @click="chooseThisModel">取消</Button>
                </div>


            </div>
        </div>
    </div>
</template>
<script>
    // import imgChoose from '@/components/page/userTrack/imgChoose.vue'

    export default {
        data() {
            return {
                dto: {
                    newPassword: "",
                    password: "",
                    id: localStorage.getItem("id"),
                }
            }
        },
        components: {},
        created: function () {

        },
        methods: {
            initChooseProject() {
                this.closeCurrModal();
            },
            /**
             * 关闭当前窗口
             */
            chooseThisModel(dto) {
                this.$emit('init-choose', dto);
                //this.update(this[dto])
            },
            closeCurrModal() {
                this.show = false;
            },
            update() {
                if (this.dto.password === "") {
                    this.$message.error("请输入旧密码");
                    return
                }
                if (this.dto.newPassword === "") {
                    this.$message.error("请输入新密码");
                    return
                }
                this.$postData("updatePassword", this.dto).then(res => {
                    if (res.status === 200) {
                        this.$message({
                            message: '修改成功',
                            type: 'success'
                        });
                        this.$router.push("/login");
                    } else {
                        this.$message.error(res.msg);
                    }
                })
            }
        }
    }
</script>

<style scoped>
    .fromInput {
        float: left;
    }

    #bodyDiv {
        font-size: 15px !important;
        background-color: #fff;
        height: 250px;
    }
</style>

