<template>
  <div class="component-upload-image">
    <el-upload
      multiple
      action="https://api.xiaoyujia.com/system/imageUpload"
      list-type="picture-card"
      :before-upload="handleBeforeUpload"
      :on-success="handleUploadSuccess"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :show-file-list="true"
      :file-list="fileList"
      :on-preview="handlePictureCardPreview"
      :class="{ hide: fileList.length >= limit }"
    >
      <i class="el-icon-plus avatar-uploader-icon"/>
    </el-upload>

    <el-dialog v-model="dialogVisible" title="预览" width="800px">
      <img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto"/>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    modelValue: [String, Object, Array],
    limit: {
      type: Number,
      default: 5
    },
    fileSize: {
      type: Number,
      default: 5
    },
    fileType: {
      type: Array,
      default: () => ['png', 'jpg', 'jpeg']
    }
  },

  data() {
    return {
      dialogImageUrl: '',
      dialogVisible: false,
      fileList: [],
      number: 0
    }
  },

  watch: {
    modelValue: {
      handler(val) {
        this.updateFileList(val);
      },
      immediate: true,
      deep: true
    }
  },

  methods: {
    updateFileList(val) {
      if (val) {
        const list = Array.isArray(val) ? val : val.split(',');
        this.fileList = list.map(item => ({
          url: item.indexOf('http') === 0 ? item : item
        }));
      } else {
        this.fileList = [];
      }
    },

    handleBeforeUpload(file) {
      const isTypeValid = this.fileType.some(type =>
        file.type.includes(type) || file.name.split('.').pop() === type
      );

      if (!isTypeValid) {
        this.$message.error(`文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`);
        return false;
      }

      if (file.size / 1024 / 1024 > this.fileSize) {
        this.$message.error(`文件大小不能超过 ${this.fileSize} MB!`);
        return false;
      }

      this.number++;
      return true;
    },

    handleUploadSuccess(res) {
      if (res.code === 0) {
        this.$emit('input', res.data);
      } else {
        this.$message.error(res.msg || '上传失败');
      }
      this.number--;
    },

    handleExceed() {
      this.$message.error(`上传文件数量不能超过 ${this.limit} 个!`);
    },

    handleUploadError() {
      this.$message.error('上传图片失败');
    },

    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    }
  }
}
</script>

<style scoped>
.hide .el-upload--picture-card {
  display: none;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 148px;
  height: 148px;
  line-height: 148px;
  text-align: center;
}
</style>
