<template>
    <div>
        <Drawer
                title="申诉详情"
                v-model="value3"
                width="40%"
                :mask-closable="false"
                @on-close="chooseThisModel"
                :styles="styles"
        >
            <Form :model="formItem" :label-width="80" label-position="left">
                <Tabs v-model="current">
                    <TabPane label="申诉中" name="4">
                        <div class="searchDiv">
                            <FormItem label="申诉理由" prop="appealReason">
                                <Input v-model="formItem.appealReason" type="textarea" clearable
                                       style="width: 250px"
                                       :autosize="{minRows: 4,maxRows: 4}"
                                       placeholder="申诉理由">
                                </Input>
                            </FormItem>
                            <FormItem label="证据上传">
                                <Upload
                                        multiple
                                        :action="imgUrl"
                                        :on-success="onSuccess"
                                        :on-preview="handlePictureCardPreview"
                                        :on-remove="handleRemove"
                                        :before-upload="before"
                                        :on-exceed="onExcees"
                                        :default-file-list="filesList"
                                        :max-size="10240"
                                        :data="files">
                                    <Button icon="ios-cloud-upload-outline">点击上传</Button>
                                </Upload>
                            </FormItem>

                            <FormItem label="品控核实" prop="appealVerify">
                                <Input v-model="formItem.appealVerify" clearable
                                       type="textarea"
                                       style="width: 250px"
                                       :autosize="{minRows: 4,maxRows: 4}"
                                       placeholder="品控核实">
                                </Input>
                            </FormItem>

                            <FormItem label="是否申诉成功">
                                <el-radio v-model="formItem.effective" :label="false">否</el-radio>
                                <el-radio v-model="formItem.effective" :label="true">是</el-radio>
                            </FormItem>
                            <FormItem label="是否撤销投诉">
                                <el-radio v-model="formItem.revoke" :label="false">否</el-radio>
                                <el-radio v-model="formItem.revoke" :label="true">是</el-radio>
                            </FormItem>
                        </div>
                    </TabPane>
                    <TabPane v-if="formItem.state===5" label="申诉成功" name="5">
                        <div class="searchDiv">
                            <div style="width: 280px">
                                <div style="width: 120px;margin: 0 auto">
                                    <img style="width: 120px;height: 120px"
                                         src="../../../../assets/img/check-circle.png"/>
                                </div>
                                <div style="text-align: center;margin-top: 1rem">申诉成功</div>
                            </div>
                        </div>
                    </TabPane>
                    <TabPane label="申诉失败" v-if="formItem.state===6" name="6">
                        <div class="searchDiv">
                            <div style="width: 280px">
                                <div style="width: 120px;margin: 0 auto">
                                    <img style="width: 120px;height: 120px"
                                         src="../../../../assets/img/close-circle.png"/>
                                </div>
                                <div style="text-align: center;margin-top: 1rem">申诉失败,尽可能的收集更多证据，谢谢</div>
                            </div>
                        </div>
                    </TabPane>
                </Tabs>
            </Form>
            <div class="demo-drawer-footer">
                <Button style="margin-right: 8px" @click="chooseThisModel">取消</Button>
                <Button type="primary" :loading="loading" @click="update">确定</Button>
            </div>
        </Drawer>
    </div>
</template>
<script>
    export default {
        props: ['id'],
        data() {
            return {
                current: 0,
                loading: false,
                filesList: [],
                files: {
                    fileName: "Ts/"
                },
                value3: true,
                imgUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
                styles: {
                    height: 'calc(100% - 55px)',
                    overflow: 'auto',
                    paddingBottom: '53px',
                    position: 'static'
                },
                formItem: {
                    id: this.id,
                },
            }
        },
        created: function () {
            this.getById()
        },
        methods: {
            getById() {
                this.$getData("violation_getByAppeal", {
                    id: this.id
                }).then(res => {
                    if (res.status === 200) {
                        this.formItem = res.data;
                        this.current = this.formItem.state + "";
                        this.getFiles(res.data)
                    }
                })
            },
            getFiles(data) {
                if (data.appealFile) {
                    let split = data.appealFile.split(",");
                    for (let i = 0; i < split.length; i++) {
                        let res = decodeURIComponent(split[i]);
                        let file = {
                            name: res.substring(res.lastIndexOf("/") + 1, res.length),
                            url: res
                        };
                        this.filesList.push(file);
                    }
                }
            },
            update() {
                this.loading = true;
                if (this.formItem.effective) {
                    this.formItem.state = 5
                } else {
                    this.formItem.state = 6
                }
                this.$postData("violation_updateAppeal", this.formItem).then(res => {
                        this.loading = false;
                        if (res.status === 200) {
                            this.getById();
                            this.$message.success("保存成功，" + res.msg);
                        } else {
                            this.$message.error("保存失败，" + res.msg);
                        }
                    }
                )
            },
            chooseThisModel() {
                this.value3 = false;
                this.$emit('init-choose', "");
            },

            onSuccess(res) {
               ;
                if (res.status === 200) {
                    this.$Message.success('上传成功');
                    if (this.formItem.appealFile) {
                        this.formItem.appealFile = this.formItem.appealFile + "," + res.data;
                    } else {
                        this.formItem.appealFile = res.data;
                    }
                    let url = decodeURIComponent(res.data);
                    let file = {
                        name: url.substring(url.lastIndexOf("/") + 1, url.length),
                        url: url
                    };
                    this.filesList.push(file);
                }
            },
            handlePictureCardPreview(file) {
                console.log(file);
                this.download(file.url, file.name);
            },
            onExcees() {
                this.$Message.success('只可上传一个文件');
            },
            download(src, fileName) {
                let x = new XMLHttpRequest();
                x.open("GET", src, true);
                x.responseType = 'blob';
                x.onload = function (e) {
                    let url = window.URL.createObjectURL(x.response);
                    let a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;
                    a.click();
                };
                x.send();
            },
            handleRemove(file) {
                console.log(file);
                let names = this.formItem.appealFile.split(",");
                for (let i = 0; i < names.length; i++) {
                    if (file.url === decodeURIComponent(names[i])) {
                        names.splice(i, 1);
                        this.formItem.appealFile = names.join(",");
                        break;
                    }
                }
                for (let i = 0; i < this.filesList.length; i++) {
                    if (file.url === decodeURIComponent(this.filesList[i].url)) {
                        this.filesList.splice(i, 1);
                        break;
                    }
                }
            },
            before() {
                if (this.formItem.file) {
                    let list = this.formItem.appealFile.split(",");
                    const check = list.length < 2;
                    if (!check) {
                        this.$Notice.warning({
                            title: '最多可上传2个附件,多文件请打包上传'
                        });
                    }
                    return check;
                }
            }
        }
    }
</script>
<style>
    .demo-drawer-footer {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        border-top: 1px solid #e8e8e8;
        padding: 10px 16px;
        text-align: left;
        background: #fff;
    }

    .searchDiv {
        width: 60%;
        margin: 20px auto;
        font-weight: bold;
        font-size: 17px !important;
    }
</style>
