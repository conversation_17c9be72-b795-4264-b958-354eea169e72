<template>
    <div class="table">
        <div class="container">
            <el-collapse>
                <el-collapse-item title="基本信息" name="1">
                    <div class="handle-box">
                        <el-form ref="form">
                            <el-row>
                                <el-col :span="6">
                                    <el-form-item label="合同名称">
                                        <el-input
                                                style="width: 200px"
                                                v-model="dto.name"
                                                placeholder="合同名称"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item label="总订单号">
                                        <el-input
                                                style="width: 200px"
                                                v-model="dto.no"
                                                disabled
                                                placeholder="总订单号"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item label="合同金额">
                                        <el-input
                                                style="width: 200px"
                                                v-model="dto.contractAmount"
                                                placeholder="合同金额"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="5">
                                    <el-form-item label="状态">
                                        <Select filterable clearable style="width: 120px" v-model="dto.state">
                                            <Option :value="1">正常</Option>
                                            <Option :value="2">过期</Option>
                                            <!--<Option v-for="item in stateList" :value="item.id">{{ item.text}}</Option>-->
                                        </Select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row style="margin-top: 10px">
                                <el-col :span="6">
                                    <el-form-item label="基本工资">
                                        <el-input
                                                style="width: 200px"
                                                v-model="dto.basePay"
                                                placeholder="基本工资"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item label="合同经理">
                                        <el-input
                                                @focus="isService=true"
                                                style="width: 200px"
                                                v-model="dto.employee.realName"
                                                placeholder="合同经理"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="10">
                                    <el-form-item label="签订时间">
                                        <el-date-picker
                                                v-model="days"
                                                type="datetimerange"
                                                :picker-options="pickerOptions"
                                                format="yyyy-MM-dd HH:mm:ss"
                                                value-format="yyyy-MM-dd HH:mm:ss"
                                                range-separator="至"
                                                start-placeholder="开始日期"
                                                end-placeholder="结束日期">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row style="margin-top: 10px">
                                <el-col :span="8">
                                    <el-form-item label="合同备注">
                                        <el-input
                                                type="textarea"
                                                :rows="3"
                                                style="width: 300px"
                                                v-model="dto.remarks"
                                                placeholder="合同备注"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="10" style="padding-top: 35px">
                                    <el-form-item>
                                        <el-button
                                                icon="el-icon-refresh"
                                                @click="getById()"
                                                style="margin-left: 20px">
                                            重置
                                        </el-button>
                                        <el-button type="primary"
                                                   @click="update()"
                                                   icon="el-icon-edit"
                                                   style="margin-left: 20px">修改
                                        </el-button>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                </el-collapse-item>
            </el-collapse>
            <div>
                <el-tabs type="border-card" v-model="tabName" @tab-click="tabClick"
                         @tab-remove="tabRemove">
                    <el-tab-pane label="合同订单" name="first">
                        <site-order :projectId="dto.id"></site-order>
                    </el-tab-pane>
                    <el-tab-pane label="合同员工" name="second">
                        <site-employee :projectId="dto.id" @init-choose="addTabs"></site-employee>
                    </el-tab-pane>
                    <el-tab-pane label="员工工资" name="third">
                        <site-wages :projectId="dto.id" @init-choose="addTabs"></site-wages>
                    </el-tab-pane>
                    <el-tab-pane label="开票信息" name="third2">
                        <site-invoice :projectId="dto.id"></site-invoice>
                    </el-tab-pane>
                    <el-tab-pane label="操作日志" name="third1">
                        <site-log :projectId="dto.id" ref="third1"></site-log>
                    </el-tab-pane>
                    <el-tab-pane v-for="(item, index) in tabs"
                                 :name="item.tabName"
                                 closable
                                 :key="index"
                                 :label="item.name">
                        <component :is='item.content'
                                   :ref="item.tabName"
                                   @init-choose="addTabs"
                                   :type="item.type"
                                   :name="item.name1"
                                   :deductionType="item.deductionType"
                                   :projectId="dto.id"
                                   :siteEmployeeId="item.siteEmployeeId"
                                   :employee="employee">
                        </component>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
        <Modal v-model="isService" class="Modal" :width="width" :z-index="9999"
               title="行政人员"
               :mask-closable="false">
            <div class="addBody">
                <employee-choose v-if="isService" @init-choose="initChooseProject"
                                 @close-modal="closeCurrModal"></employee-choose>
            </div>
            <div slot="footer">
            </div>
        </Modal>
    </div>

</template>

<script>
    import employeeChoose from '@/components/page/site/employeeChoose.vue'
    import siteOrder from '@/components/page/site/siteOrder.vue'
    import siteEmployee from '@/components/page/site/siteEmployee.vue'
    import siteLeave from '@/components/page/site/siteLeave.vue'
    import siteMatter from '@/components/page/site/siteMatter.vue'
    import siteInsertEm from '@/components/page/site/siteInsertEm.vue'
    import siteQuit from '@/components/page/site/siteQuit.vue'
    import siteWages from '@/components/page/site/siteWages.vue'
    import siteInvoice from '@/components/page/site/siteInvoice.vue'
    import siteLog from '@/components/page/site/siteLog.vue'
    import siteEmployeeFormal from '@/components/page/site/siteEmployeeFormal.vue'
    export default {
        data() {
            return {
                width: "32%",
                isService: false,
                days: [],
                pickerOptions: {
                    onPick: obj => {
                        this.pickerMinDate = new Date(obj.minDate).getTime();
                    },
                    disabledDate: time => {
                        if (this.pickerMinDate) {
                            const day1 = 366 * 10 * 24 * 3600 * 1000;
                            let maxTime = this.pickerMinDate + day1;
                            let minTime = this.pickerMinDate - day1;
                            return time.getTime() > maxTime || time.getTime() < minTime;
                        }
                    }
                },
                list: null,
                employee: null,
                deductionType: null,
                type: null,
                name: null,
                tabName: this.$route.query.tabName,
                dto: {
                    id: this.$route.query.id,
                    employee: {
                        realName: ''
                    }
                },
                tabs: [],
            };
        },
        components: {
            'employeeChoose': employeeChoose,
            'siteOrder': siteOrder,
            'siteEmployee': siteEmployee,
            'siteLeave': siteLeave,
            'siteMatter': siteMatter,
            'siteInsertEm': siteInsertEm,
            'siteQuit': siteQuit,
            'siteWages': siteWages,
            'siteInvoice':siteInvoice,
            'siteLog':siteLog,
            'siteEmployeeFormal':siteEmployeeFormal
        },
        created() {
            this.getById();
        },
        computed: {},
        watch: {
            $route() {
               if(this.$route.query.id){
                   this.tabName=this.$route.query.tabName;
                   this.dto.id=this.$route.query.id;
                   this.getById();
               }
            },
        },
        methods: {
            tabRemove(targetName) {
                console.log(targetName);
                console.log(this.tabs);
                let list = []; //当前显示的tab数组
                this.tabs.forEach((tab, index) => {
                    if (tab.tabName !== targetName) {
                        list.push(tab)
                    }
                });
                if (list.length === 0) {
                    this.tabName = 'second';
                    this.tabs = []
                } else {
                    this.tabName = list[list.length - 1].tabName;
                    this.tabs = list
                }
            },
            getById() {
                this.$postUrl("siteProject_getById", this.dto.id).then(res => {
                    this.loading = false;
                    if (res.status === 200) {
                       ;
                        this.dto = res.data;
                        this.days = [];
                        this.days.push(this.dto.signTime);
                        this.days.push(this.dto.expireTime);
                    }
                });
            },
            update() {
                this.dto.signTime = null;
                this.dto.expireTime = null;
                if (this.days != null && this.days.length > 0) {
                    this.dto.signTime = this.days[0];
                    this.dto.expireTime = this.days[1]
                }
                this.$postData("siteProject_update", this.dto).then(res => {
                    if (res.status === 200) {
                        this.$message({
                            type: 'success',
                            message: '修改成功!'
                        });
                        this.getById()
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })
            },
            initChooseProject(data) {
                this.dto.manager = data.id;
                this.dto.employee.realName = data.realName;
                this.closeCurrModal();
            },
            addTabs(data, employee) {
                this.employee = employee;
                let flag = true;
                this.tabs.forEach(item => {
                    if (item.tabName === data.tabName) {
                        flag = false;
                    }
                });
                if (flag) {
                    this.tabs.push(data)
                }
                this.tabName = data.tabName;
            },
            tabClick(tabName) {
                this.tabName = tabName.name;
                console.log(this.$refs[tabName.name]);
                if (tabName.name === "name2") {
                    this.$refs[tabName.name][0].getData();
                }
                if (tabName.name === "third1") {
                    this.$refs[tabName.name].getData();
                }
            },
            closeCurrModal(data) {
                this.isService = false;
            },
        },
    };
</script>

<style scoped>
    .handle-box {
        /*margin-bottom: 10px;*/
        font-weight: bold !important;
    }

    .el-form-item__label {
        font-weight: bold !important;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;

    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }

    .container {
        padding: 10px 20px;
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 5px;
    }

    .el-collapse-item__wrap {
        will-change: height;
        background-color: #FFF;
        overflow: hidden;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        border-bottom: none !important;
    }
</style>
