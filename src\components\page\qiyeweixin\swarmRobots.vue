<template>
  <div style=" border: 1px solid #eee;width: 100%">
    <el-container>
      <el-container style="background: white">

        <el-main>

          <h1>群机器人</h1>

          <el-button type="warning" size="large" plain icon="el-icon-plus" @click="addRobots()"
                     v-if="roleId=1">添加群机器人
          </el-button>
          <el-button type="success" size="large" plain icon="el-icon-plus" @click="bitSwarmRobots3 = true">
            添加定时任务
          </el-button>
          <el-button type="primary" size="large" plain icon="el-icon-plus" @click="assignTasks = true">添加指定任务
          </el-button>
          <el-divider></el-divider>
          <el-form ref="form">
            <el-row>
              <el-col :span="6">
                <el-form-item label="机器人名称">
                  <el-input
                      clearable
                      v-model="swarmRobots.name"
                      placeholder="请输入机器人名称"
                      style="width:200px"
                      class="handle-input mr10"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-button type="success" round @click="swarmRobots.current=1,getSwarmRobots()">搜索</el-button>
              </el-col>
            </el-row>
          </el-form>
          <el-table :data="list" :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" key="list"
                    stripe v-loading="loading" class="table" border>
            <el-table-column prop="name" label="机器人名称"
                             :show-overflow-tooltip="true" width="100">
            </el-table-column>
            <el-table-column prop="creatDate" label="创建时间" :formatter="dateFormat"
                             :show-overflow-tooltip="true" width="160">
            </el-table-column>
            <el-table-column fixed="right" label="操作">
              <template slot-scope="scope">
                <div style="display: flex">
                  <el-button size="info" type="primary" plain icon="el-icon-plus" @click="messagePush(scope.row.id)">
                    消息推送
                  </el-button>
                  <el-button size="large " type="success" @click="timedTask(scope.row.id)">
                    所有定时任务
                  </el-button>
                  <el-button size="large " type="primary" @click="assignTasksLog(scope.row.id)">
                    所有指定任务
                  </el-button>
                  <el-button size="info" type="success" @click="log(scope.row.id)">
                    所有推送
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :current="this.swarmRobots.current"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="10"/>
          </div>
        </el-main>
      </el-container>
    </el-container>
    <el-dialog title="添加群机器人" :visible.sync="bitSwarmRobots" center>
      <el-form ref="form" :model="swarmRobots" label-width="100px" style="background:#FFFFFF" label-position="right">
        <el-form-item label="机器人名称" required>
          <el-input v-model="swarmRobots.name" style="width:245px" clearable></el-input>
        </el-form-item>
        <el-form-item label="webhook" required>
          <el-input v-model="swarmRobots.webhook" style="width:245px" clearable></el-input>
        </el-form-item>
      </el-form>

      <el-tabs v-model="activeName" type="border-card">
        <el-tab-pane label="所有门店" name="first" >
          <div v-for="storeData in storeList"  :style="swarmRobots.storeId==storeData.id?'color: red':''" class="agent-box">
            {{storeData.storeName}}
            <el-button size="mini" type="primary" @click="bdStore(storeData)">绑定此门店</el-button>
          </div>
        </el-tab-pane>
        <el-tab-pane label="搜索门店名称" name="second">
          <el-input v-model="storeName" placeholder="请输入名称或者工号" @input="selectAllStore()"
                    suffix-icon="el-icon-search"></el-input>
          <div v-for="item in storeList" class="agent-box" :style="swarmRobots.storeId==item.id?'color: red':''">
            {{item.storeName}}
            <el-button size="mini" type="primary" @click="bdStore(item)">绑定此门店</el-button>
          </div>
        </el-tab-pane>

      </el-tabs>

      <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="addSwarmRobots()">立即添加</el-button>
          </span>
    </el-dialog>
    <el-dialog title="群机器人定时任务" :visible.sync="bitSwarmRobots3" center>
      <el-form ref="form" :model="dom" label-width="100px" style="background:#FFFFFF" label-position="right">
        <el-form-item label="机器人" required>
          <el-radio-group v-model="name" size="large" v-for="(item,index) in list1" :key="index"
                          @change="change(item.id)">
            <el-radio-button :label="item.name"></el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="重复模式" required>
          <el-radio-group v-model="model" size="large" v-for="(item,index) in list2" :key="index"
                          @change="change2(item.value)">
            <el-radio-button :label="item.label"></el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="重复日期" required v-if="dom.model===4 || dom.model===5 ">
          <el-checkbox-group v-model="date" size="large" v-if="dom.model===4">
            <el-checkbox-button :label="1">周一</el-checkbox-button>
            <el-checkbox-button :label="2">周二</el-checkbox-button>
            <el-checkbox-button :label="3">周三</el-checkbox-button>
            <el-checkbox-button :label="4">周四</el-checkbox-button>
            <el-checkbox-button :label="5">周五</el-checkbox-button>
            <el-checkbox-button :label="6">周六</el-checkbox-button>
            <el-checkbox-button :label="7">周日</el-checkbox-button>
          </el-checkbox-group>
          <el-checkbox-group v-model="date" size="large" v-if="dom.model===5">
            <el-checkbox-button :label="1">01</el-checkbox-button>
            <el-checkbox-button :label="2">02</el-checkbox-button>
            <el-checkbox-button :label="3">03</el-checkbox-button>
            <el-checkbox-button :label="4">04</el-checkbox-button>
            <el-checkbox-button :label="5">05</el-checkbox-button>
            <el-checkbox-button :label="6">06</el-checkbox-button>
            <el-checkbox-button :label="7">07</el-checkbox-button>
            <br/>
            <el-checkbox-button :label="8">08</el-checkbox-button>
            <el-checkbox-button :label="9">09</el-checkbox-button>
            <el-checkbox-button :label="10">10</el-checkbox-button>
            <el-checkbox-button :label="11">11</el-checkbox-button>
            <el-checkbox-button :label="12">12</el-checkbox-button>
            <el-checkbox-button :label="13">13</el-checkbox-button>
            <el-checkbox-button :label="14">14</el-checkbox-button>
            <br/>
            <el-checkbox-button :label="15">15</el-checkbox-button>
            <el-checkbox-button :label="16">16</el-checkbox-button>
            <el-checkbox-button :label="17">17</el-checkbox-button>
            <el-checkbox-button :label="18">18</el-checkbox-button>
            <el-checkbox-button :label="19">19</el-checkbox-button>
            <el-checkbox-button :label="20">20</el-checkbox-button>
            <el-checkbox-button :label="21">21</el-checkbox-button>
            <br/>
            <el-checkbox-button :label="22">22</el-checkbox-button>
            <el-checkbox-button :label="23">23</el-checkbox-button>
            <el-checkbox-button :label="24">24</el-checkbox-button>
            <el-checkbox-button :label="25">25</el-checkbox-button>
            <el-checkbox-button :label="26">26</el-checkbox-button>
            <el-checkbox-button :label="27">27</el-checkbox-button>
            <el-checkbox-button :label="28">28</el-checkbox-button>
            <br/>
            <el-checkbox-button :label="29">29</el-checkbox-button>
            <el-checkbox-button :label="30">30</el-checkbox-button>
            <el-checkbox-button :label="31">31</el-checkbox-button>
            <el-checkbox-button :label="0">最后一天</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="时间" required>
          <el-time-select v-model="dom.time" placeholder="任意时间点"
                          :picker-options="{
                          start: '05:00',
                          step: '00:15',
                          end: '23:00'
                          }">
          </el-time-select>
        </el-form-item>
        <el-form-item label="类型" required>
          <el-radio-group v-model="dom.type" size="large">
            <el-radio-button :label="0">文本</el-radio-button>
            <el-radio-button :label="1">图片</el-radio-button>
            <el-radio-button :label="2">图文</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="@所有人" v-if="dom.type===0" required>
          <el-switch v-model="dom.isAll" active-text="是" inactive-text="否"
                     active-color="#13ce66" inactive-color="#ff4949">
          </el-switch>
        </el-form-item>
        <el-form-item label="@用户SQL" v-if="dom.type===0&&!dom.isAll">
          <el-input type="textarea" placeholder="sql只能显示工号"
                    :autosize="{ minRows:2,}" v-model="dom.sql"
                    maxlength="2000" style="width:300px"></el-input>
        </el-form-item>
        <el-form-item label="文本" v-if="dom.type===0" required>
          <el-input type="textarea" placeholder="请输入内容" @input="onInput"
                    @keydown.delete.native="onDeleteKeyDown" @blur="testBlur"
                    :autosize="{ minRows:3,}" v-model="dom.content"
                    maxlength="2000" style="width:300px"></el-input>
        </el-form-item>
        <el-form-item label="参数" v-if="dom.type===0">
          <el-tag v-for="(item, index) in dom.timedTaskParameters" :key="index" closable
                  :disable-transitions="false" @close="handleClose(item)">{{ item.parameterName }}
          </el-tag>
          <el-button class="button-new-tag" size="small" @click="isParameter">添加</el-button>
        </el-form-item>
        <el-form-item label="图片" v-if="dom.type===1" required>
          <el-card class="box-card" shadow="always">
            <div class="text item" @click="selectPicture = true">
              <i style=" font-size: 40px; " class="el-icon-plus" v-if="dom.pic_url===null"></i>
              <el-image class="image" :src=dom.pic_url fit="contain" v-if="dom.pic_url!==null"></el-image>
            </div>
          </el-card>
          <el-dialog title="选择图片" :visible.sync="selectPicture" width="70%" append-to-body>
            <pictureSelection :choiceImg="choiceImg" @picture="picture"></pictureSelection>
          </el-dialog>
        </el-form-item>
        <el-form-item label="图文" v-if="dom.type===2" required>
          <el-card class="box-card" shadow="always">
            <div class="text item" @click="selectGraphics = true">
              <i style=" font-size: 40px; " class="el-icon-plus" v-if="dom.picurl===null"></i>
              <el-image class="image" :src=dom.picurl fit="contain"
                        v-if="dom.picurl!==null"></el-image>
            </div>
          </el-card>
        </el-form-item>
        <el-dialog title="选择图文" :visible.sync="selectGraphics" width="70%" append-to-body>
          <graphicsContent :choiceGraphics="choiceImg" @graphicsContent="graphicsContent"></graphicsContent>
        </el-dialog>
        <el-form-item>
          <el-button type="primary" @click="addTimedTask">建立任务</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog title="群机器人指定任务" :visible.sync="assignTasks" center>
      <el-form ref="form" :model="dom" label-width="100px" style="background:#FFFFFF" label-position="right" center>
        <el-form-item label="机器人" required>
          <el-radio-group v-model="name" size="large" v-for="(item,index) in list" :key="index"
                          @change="change(item.id)">
            <el-radio-button :label="item.name"></el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="时间范围" required>
          <el-input-number v-model="value1" :precision="0" :min="0" :max="23" label="描述文字"></el-input-number>
          <el-input-number v-model="value2" :precision="0" :min="value1" :max="23" label="描述文字"></el-input-number>
        </el-form-item>
        <el-form-item label="间隔时间" required>
          <el-input placeholder="分钟" v-model="value3" onkeyup="value=value.replace(/[^\d]/g,0)" maxlength="4"
                    clearable
                    style="width:80px"></el-input>
          分钟
        </el-form-item>
        <el-form-item label="@所有人" required>
          <el-switch v-model="dom.isAll" active-text="是" inactive-text="否"
                     active-color="#13ce66" inactive-color="#ff4949">
          </el-switch>
        </el-form-item>
        <el-form-item label="@用户SQL" v-if="!dom.isAll">
          <el-input type="textarea" placeholder="sql只能显示工号"
                    :autosize="{ minRows:2,}" v-model="dom.sql"
                    maxlength="2000" style="width:300px"></el-input>
        </el-form-item>
        <el-form-item label="文本" required>
          <el-input type="textarea" placeholder="请输入内容" @input="onInput"
                    @keydown.delete.native="onDeleteKeyDown" @blur="testBlur"
                    :autosize="{ minRows:3,}" v-model="dom.content"
                    maxlength="2000" style="width:300px"></el-input>
        </el-form-item>
        <el-form-item label="参数">
          <el-tag v-for="(item, index) in dom.timedTaskParameters" :key="index" closable
                  :disable-transitions="false" @close="handleClose(item)">{{ item.parameterName }}
          </el-tag>
          <el-button class="button-new-tag" size="small" @click="isParameter">添加</el-button>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="dom.id===null" type="primary" @click="addAssignTasks">建立指定任务</el-button>
        <el-button v-if="dom.id!==null" type="primary" @click="addAssignTasks">修改指定任务</el-button>
      </div>
    </el-dialog>
    <el-drawer :title="name+'定时任务记录'" :visible.sync="isTimedTask" direction="rtl" size="1100px">
      <el-table :data="list4">
        <el-table-column label="当前状态" width="105">
          <template slot-scope="scope">
            <el-button size="large" type="success" v-if="scope.row.state===1" @click="pause(scope.row.id)">
              运行中
            </el-button>
            <el-button size="large" type="primary" v-if="scope.row.state===3" @click="run(scope.row.id)">
              暂停
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="类型" width="50">
          <template slot-scope="scope">
            <span v-if="scope.row.type===0">
              文本
            </span>
            <span v-if="scope.row.type===1">
              图片
            </span>
            <span v-if="scope.row.type===2">
              图文
            </span>
          </template>
        </el-table-column>
        <el-table-column label="重复模式" width="115">
          <template slot-scope="scope">
            <el-button type="primary" plain v-if="scope.row.model===1">
              每天
            </el-button>
            <el-button type="success" plain v-if="scope.row.model===2">
              周一到周五
            </el-button>
            <el-button type="info" plain v-if="scope.row.model===3">
              法定工作日
            </el-button>
            <el-button type="warning" plain v-if="scope.row.model===4">
              按周
            </el-button>
            <el-button type="danger" plain v-if="scope.row.model===5">
              按月
            </el-button>
          </template>
        </el-table-column>
        <el-table-column property="date" label="日期">
          <template slot-scope="scope">
            <span v-if="scope.row.model===4" v-for="(item, index) in scope.row.date" :key="index">
              <el-tag effect="plain" v-if="item==='1'">周一</el-tag>
              <el-tag effect="plain" v-if="item==='2'">周二</el-tag>
              <el-tag effect="plain" v-if="item==='3'">周三</el-tag>
              <el-tag effect="plain" v-if="item==='4'">周四</el-tag>
              <el-tag effect="plain" v-if="item==='5'">周五</el-tag>
              <el-tag effect="plain" v-if="item==='6'">周六</el-tag>
              <el-tag effect="plain" v-if="item==='7'">周日</el-tag>
            </span>
            <span v-if="scope.row.model===5" v-for="(item, index) in scope.row.date" :key="index">
              <el-tag v-if="item!=='0'" effect="plain">{{ item }}</el-tag>
              <el-tag v-if="item==='0'" effect="plain">最后一天</el-tag>
            </span>
          </template>
        </el-table-column>
        <el-table-column property="time" label="时间" :formatter="timeFormat" width="70"></el-table-column>
        <el-table-column property="content" label="文本内容" width="200">
          <template slot-scope="scope">
            <el-popover trigger="hover" placement="top" width="200">
              <p>{{ scope.row.content }}</p>
              <div slot="reference">
                <p size="medium" style="white-space: nowrap;overflow: hidden;text-overflow:ellipsis;">
                  {{ scope.row.content }}</p>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column property="pic_url" label="图片链接" width="100">
          <template slot-scope="scope">
            <el-popover trigger="hover" placement="top" width="200">
              <el-image style="width: 200px;" :src="scope.row.pic_url"></el-image>
              <div slot="reference">
                <p size="medium" style="white-space: nowrap;overflow: hidden;text-overflow:ellipsis;">
                  {{ scope.row.pic_url }}</p>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column property="url" label="图文链接" width="100">
          <template slot-scope="scope">
            <el-popover trigger="hover" placement="top" width="200">
              <p>{{ scope.row.url }}</p>
              <div slot="reference">
                <p size="medium" style="white-space: nowrap;overflow: hidden;text-overflow:ellipsis;">
                  {{ scope.row.url }}</p>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column property="operationName" label="操作人名称" width="90"></el-table-column>
        <!--        <el-table-column property="creatDate" label="创建时间" :formatter="dateFormat" width="150"></el-table-column>-->
        <el-table-column label="操作"   width="200">
          <template slot-scope="scope">
            <el-button size="large" type="danger" @click="deleteSwarmRobots(scope.row.id)">
              删除
            </el-button>
            <el-button size="large" type="primary" @click="updateSwarmRobots(scope.row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>
    <el-drawer :title="name+'指定任务记录'" :visible.sync="isAssignTasks" direction="rtl" size="860px">
      <el-table :data="list5">
        <el-table-column label="当前状态" width="105">
          <template slot-scope="scope">
            <el-popover trigger="hover" placement="top" width="150" v-if="scope.row.state===4">
              <p>点击暂停当前指定任务</p>
              <div slot="reference">
                <el-button size="large" type="success" @click="specifyPause(scope.row.id)">
                  运行中
                </el-button>
              </div>
            </el-popover>
            <el-popover trigger="hover" placement="top" width="150" v-if="scope.row.state===3">
              <p>点击运行当前指定任务</p>
              <div slot="reference">
                <el-button size="large" type="primary" @click="specifyRun(scope.row.id)">
                  暂停
                </el-button>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="@用户SQL" width="150">
          <template slot-scope="scope">
            <el-popover trigger="hover" placement="top" width="200">
              <p>{{ scope.row.sql }}</p>
              <div slot="reference">
                <p size="medium" style="white-space: nowrap;overflow: hidden;text-overflow:ellipsis;">
                  {{ scope.row.sql }}</p>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column property="content" label="文本内容" width="200">
          <template slot-scope="scope">
            <el-popover trigger="hover" placement="top" width="200">
              <p>{{ scope.row.content }}</p>
              <div slot="reference">
                <p size="medium" style="white-space: nowrap;overflow: hidden;text-overflow:ellipsis;">
                  {{ scope.row.content }}</p>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="定时时间" width="155">
          <template slot-scope="scope">
            时间范围：
            <el-tag>{{ scope.row.corn[2] }}</el-tag>
            <br/>
            间隔时间：
            <el-tag>{{ scope.row.corn[1].slice(2) }}分钟</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <template slot-scope="scope">
            <el-button size="large" type="info" @click="specifyLog(scope.row.id)">
              日志
            </el-button>
            <el-button size="large" type="warning" @click="updateSpecify(scope.row)">
              修改
            </el-button>
            <el-button size="large" type="danger" @click="deleteSpecify(scope.row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>
    <el-dialog title="日志" :visible.sync="isAssignTasksLog" center>
      <el-table :data="list6">
        <el-table-column label="类型" width="80">
          <template slot-scope="scope">
            <el-tag type="danger" v-if="scope.row.type===0">
              删除
            </el-tag>
            <el-tag type="success" v-if="scope.row.type===1">
              添加
            </el-tag>
            <el-tag type="warning" v-if="scope.row.type===2">
              修改
            </el-tag>
            <el-tag type="info" v-if="scope.row.type===3">
              暂停
            </el-tag>
            <el-tag v-if="scope.row.type===4">
              运行
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column property="operationId" label="操作人Id" width="100"></el-table-column>
        <el-table-column property="operationName" label="操作人名称" width="100"></el-table-column>
        <el-table-column property="creatDate" label="操作时间" :formatter="dateFormat" width="180"></el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog title="添加参数" :visible.sync="addParameter" center>
      <el-form ref="form" label-width="100px" style="background:#FFFFFF" label-position="right">
        <el-form-item label="参数sql">
          <el-input type="textarea" :autosize="{ minRows:2,}" v-model="parameterSql"
                    maxlength="1000" style="width:300px"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addParameter = false">取 消</el-button>
        <el-button type="primary" @click="insertParameter()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="消息推送" :visible.sync="bitSwarmRobots2" center>
      <el-form ref="form" :model="dom" label-width="100px" style="background:#FFFFFF" label-position="right">
        <el-form-item label="类型" required>
          <el-radio-group v-model="dom.type" size="large">
            <el-radio-button :label="0">文本</el-radio-button>
            <el-radio-button :label="1">图片(素材库)</el-radio-button>
            <el-radio-button :label="3">图片(自选)</el-radio-button>
            <el-radio-button :label="2">图文</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="@所有人" v-if="dom.type===0" required>
          <el-switch v-model="dom.isAll" active-text="是" inactive-text="否"
                     active-color="#13ce66" inactive-color="#ff4949">
          </el-switch>
        </el-form-item>
        <el-form-item label="文本" v-if="dom.type===0" required>
          <el-input type="textarea" placeholder="请输入内容"
                    :autosize="{ minRows:3,}" v-model="dom.content"
                    maxlength="2000" style="width:300px"></el-input>
        </el-form-item>
        <el-form-item label="图片" v-if="dom.type===1" required>
          <el-card class="box-card" shadow="always">
            <div class="text item" @click="selectPicture = true">
              <i style=" font-size: 40px; " class="el-icon-plus" v-if="dom.pic_url===null"></i>
              <el-image class="image" :src=dom.pic_url fit="contain" v-if="dom.pic_url!==null"></el-image>
            </div>
          </el-card>
          <el-dialog title="选择图片" :visible.sync="selectPicture" width="70%" append-to-body>
            <pictureSelection :choiceImg="choiceImg" @picture="picture"></pictureSelection>
          </el-dialog>
        </el-form-item>
        <el-form-item label="图片" v-if="dom.type===3" required>
          <el-card class="" shadow="always">
            <div class="text item">
              <el-upload
                  class="upload-demo"
                  drag
                  action="https://api.xiaoyujia.com/system/imageUpload"
                  multiple
                  :on-success="getSuccess">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              </el-upload>
              <el-image class="image" :src=dom.picurl fit="contain" v-if="dom.picurl!==null"></el-image>
            </div>
          </el-card>
        </el-form-item>
        <el-form-item label="图文" v-if="dom.type===2" required>
          <el-card class="box-card" shadow="always">
            <div class="text item" @click="selectGraphics = true">
              <i style=" font-size: 40px; " class="el-icon-plus" v-if="dom.picurl===null"></i>
              <el-image class="image" :src=dom.picurl fit="contain"
                        v-if="dom.picurl!==null"></el-image>
            </div>
          </el-card>
        </el-form-item>
        <el-dialog title="选择图文" :visible.sync="selectGraphics" width="70%" append-to-body>
          <graphicsContent :choiceGraphics="choiceImg" @graphicsContent="graphicsContent"></graphicsContent>
        </el-dialog>
        <el-form-item>
          <el-button type="primary" size="large" @click="push">立刻推送</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-drawer :title="name+'消息推送记录'" :visible.sync="table" direction="rtl" size="900px">
      <el-table :data="list3">
        <el-table-column label="类型" width="100">
          <template slot-scope="scope">
            <el-button size="large" type="info" v-if="scope.row.type===0">
              文本
            </el-button>
            <el-button size="large" type="success" v-if="scope.row.type===1">
              图片
            </el-button>
            <el-button size="large" type="primary" v-if="scope.row.type===2">
              图文
            </el-button>
          </template>
        </el-table-column>
        <el-table-column property="content" label="文本内容">
          <template slot-scope="scope">
            <el-popover trigger="hover" placement="top" width="200">
              <p>{{ scope.row.content }}</p>
              <div slot="reference">
                <p size="medium" style="white-space: nowrap;overflow: hidden;text-overflow:ellipsis;">
                  {{ scope.row.content }}</p>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column property="pic_url" label="图片链接" width="150">
          <template slot-scope="scope">
            <el-popover trigger="hover" placement="top" width="200">
              <el-image style="width: 200px;" :src="scope.row.pic_url"></el-image>
              <div slot="reference">
                <p size="medium" style="white-space: nowrap;overflow: hidden;text-overflow:ellipsis;">
                  {{ scope.row.pic_url }}</p>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column property="url" label="图文链接" width="150">
          <template slot-scope="scope">
            <el-popover trigger="hover" placement="top" width="200">
              <p>{{ scope.row.url }}</p>
              <div slot="reference">
                <p size="medium" style="white-space: nowrap;overflow: hidden;text-overflow:ellipsis;">
                  {{ scope.row.url }}</p>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column property="creatDate" label="创建时间" :formatter="dateFormat" width="180"></el-table-column>
        <el-table-column property="operationName" label="操作人名称" width="100"></el-table-column>
      </el-table>
    </el-drawer>
    <el-dialog :visible.sync="dialogVisible">
      <el-form ref="form" :model="robotPushMsg" label-width="80px">
        <el-form-item label="推送时间">
          <el-col :span="11">
            <el-time-select v-model="robotPushMsg.time" placeholder="任意时间点"
                            :picker-options="{
                          start: '05:00',
                          step: '00:15',
                          end: '23:00'
                          }">
            </el-time-select>
          </el-col>
        </el-form-item>
        <el-form-item label="推送内容">
          <el-input type="textarea" v-model="robotPushMsg.content"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="subUpdateRobot">确认修改</el-button>
          <el-button>取消</el-button>
        </el-form-item>
      </el-form>
      <br><br>
    </el-dialog>
  </div>
</template>

<script>
import pictureSelection from "./pictureSelection";
import moment from "moment";
import graphicsContent from "./graphicsContent";

export default {
  name: "swarmRobots",
  inject: ['reload'],
  // 注册组件
  components: {
    pictureSelection,
    graphicsContent,
  },
  data() {
    return {
      robotPushMsg: {
      },
      cursor: null,
      addParameter: false,
      storeList: [],
      dialogVisible: false,
      fullscreenLoading: false,
      parameterName: null,
      parameterSql: null,
      roleId: localStorage.getItem("roleId"),
      bitSwarmRobots: false,
      bitSwarmRobots2: false,
      bitSwarmRobots3: false,
      assignTasks: false,
      isTimedTask: false,
      isAssignTasks: false,
      isAssignTasksLog: false,
      table: false,
      choiceImg: true,
      selectPicture: false,
      selectGraphics: false,
      loading: true,
      value1: null,
      value2: null,
      activeName: 'first',
      value3: null,
      list: [],
      list1: [],
      list3: [],
      list4: [],
      list5: [],
      list6: [],
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      swarmRobots: {
        name: null,
        webhook: null,
        storeId: null,
        size: 10,
        current: 1,
      },
      model: null,
      name: null,
      storeName: "",
      date: [],
      dom: {
        id: null,
        swarmRobotsId: null,
        model: null,
        date: null,
        time: null,
        type: null,
        content: null,
        isAll: null,
        pic_url: null,
        title: null,
        desc: null,
        url: null,
        picurl: null,
        corn: null,
        sql: null,
        timedTaskParameters: [],
        operationId: localStorage.getItem("id"),
        operationName: localStorage.getItem("realName"),
      },
      list2: [{
        value: 1,
        label: '每天'
      }, {
        value: 2,
        label: '周一到周五'
      },
        //   {
        //   value: 3,
        //   label: '法定工作日'
        // },
        {
          value: 4,
          label: '按周'
        }, {
          value: 5,
          label: '按月'
        },
      ],
    }
  },
  created() {
    this.getSwarmRobots();
    this.selectSwarmRobots();
  },
  methods: {
    bdStore(item){
      this.swarmRobots.storeId = item.id
      this.$message.success("绑定成功!");
    },
    getSuccess(res,file){
      this.dom.picurl = res.data;
      this.dom.pic_url = res.data;
      console.log(res.data)
    },
    addRobots(){
      this.bitSwarmRobots = true
      this.selectAllStore()
    },
    selectAllStore(){
      this.$getData("selectAllStore", {storeName: this.storeName}).then(res => {
        if (res.status===200) {
          this.storeList = res.data
        }
      })
    },
    subUpdateRobot(){
      var obj = {
        content: this.robotPushMsg.content,
        time: this.robotPushMsg.time,
        id: this.robotPushMsg.id
      }
      this.$postData("updateRobotMsg", obj).then(res => {
        if (res.code === 0) {
          this.$message({message: '编辑成功', type: 'success'});
        } else {
          this.$message({message: '编辑失败', type: 'warning'});
        }
        this.dialogVisible = false
         //刷新
        // this.reload();
      })
    },
    onInput() {
      this.$emit('input', this.dom.content)
    },
    onDeleteKeyDown(e) {
      const {target} = e;
      let selectionStart = `${target.selectionStart}`;
      let selectionEnd = `${target.selectionEnd}`;
      let value = `${target.value}`;
      let deleteValue = null;
      if (selectionStart !== selectionEnd) {
        deleteValue = value.substring(selectionStart, selectionEnd)
      } else {
        deleteValue = value.substring(selectionStart, selectionEnd - 1)
      }
      let parameterNames = [];
      this.dom.timedTaskParameters.forEach(e => {
        parameterNames.push(e.parameterName);
      })
      var parameterName1 = parameterNames.join("");
      let arr = parameterName1.split("");
      let newArr = [...new Set(arr)];
      newArr.forEach(v => {
        if (deleteValue.match(v)) {
          e.preventDefault()
        }
      })
    },
    isParameter() {
      if (this.dom.content == null) {
        return this.$message.error("请先填写文本");
      }
      this.addParameter = true;
    },
    testBlur(e) {
      this.cursor = e.srcElement.selectionStart;
    },
    handleClose(item) {
      this.dom.timedTaskParameters.forEach(e => {
        if (e.parameterName === item.parameterName) {
          this.dom.timedTaskParameters.splice(this.dom.timedTaskParameters.indexOf(e), 1);
        }
      });
      this.dom.content = this.dom.content.replace(item.parameterName, '');
      for (let i = 0; i < this.dom.timedTaskParameters.length; i++) {
        let temp = this.dom.timedTaskParameters[i];
        temp.parameterName = "@" + i + "@";
        let num = Number(i) + Number(1);
        let parameterName = "@" + num + "@";
        this.dom.content = this.dom.content.replace(parameterName, temp.parameterName);
      }
    },
    insertParameter() {
      this.parameterName = "@" + this.dom.timedTaskParameters.length + "@";
      let str = this.dom.content;
      if (str !== null && str !== '') {
        this.dom.content = str.slice(0, this.cursor) + this.parameterName + str.slice(this.cursor);
      } else {
        return this.$message.error("请先填写文本");
      }
      let inputValue = {
        parameterName: this.parameterName,
        parameterSql: this.parameterSql,
      };
      this.dom.timedTaskParameters.push(inputValue);
      this.addParameter = false;
      this.parameterName = null;
      this.parameterSql = null;
    },
    addAssignTasks() {
      if (!this.dom.isAll && (this.value3 === null || this.value3 === '' || this.value2 === null || this.value2 === '' ||
          this.value1 === null || this.value1 === '' || this.dom.swarmRobotsId === null || this.dom.swarmRobotsId === '' ||
          this.dom.content === null || this.dom.content === '')) {
        return this.$message({message: '红色*号不能为空', type: 'warning'});
      }
      if (this.dom.isAll && (this.value3 === null || this.value3 === '' || this.value2 === null || this.value2 === '' ||
          this.value1 === null || this.value1 === '' || this.dom.swarmRobotsId === null ||
          this.dom.swarmRobotsId === '' || this.dom.content === null || this.dom.content === '')) {
        return this.$message({message: '红色*号不能为空', type: 'warning'});
      }
      this.dom.corn = "0 0/" + this.value3 + " " + this.value1 + "-" + this.value2 + " * * ?";
      this.dom.state = 4;
      this.dom.type = 0;
      this.fullscreenLoading = true;
      this.$postData("saveOrEdit", this.dom).then(res => {
        this.fullscreenLoading = false;
        if (res.data) {
          if (this.dom.id != null) {
            this.$message.success("修改成功");
          } else {
            this.$message.success("添加成功");
          }
          //刷新
          this.reload();
        } else if (res.data === null) {
          return this.$message.error("sql错误");
        } else {
          if (this.dom.id != null) {
            return this.$message.error("修改失败");
          } else {
            return this.$message.error("添加失败");
          }
        }
      })
    },
    pause(id) {
      let timedTask = {
        id: id,
        state: 3,
      }
      this.$postData("updateTimedTaskState", timedTask).then(res => {
        if (res.data > 0) {
          this.$message.success("修改成功");
          //刷新
          this.reload();
        } else {
          return this.$message.error("修改失败");
        }
      })
    },
    specifyPause(id) {
      this.$getData("specifyPause", {id: id}).then(res => {
        if (res.data > 0) {
          let timedTaskLog = {
            id: id,
            type: 3,
            timedTaskId: id,
            operationId: localStorage.getItem("id"),
            operationName: localStorage.getItem("realName"),
          }
          this.addTimedTaskLog(timedTaskLog);
          this.$message.success("修改成功");
          //刷新
          this.reload();
        } else {
          return this.$message.error("修改失败");
        }
      })
    },
    addTimedTaskLog(timedTaskLog) {
      this.$postData("addTimedTaskLog", timedTaskLog).then(res => {
      })
    },
    specifyRun(id) {
      this.$getData("specifyRun", {id: id}).then(res => {
        if (res.data > 0) {
          let timedTaskLog = {
            id: id,
            type: 4,
            timedTaskId: id,
            operationId: localStorage.getItem("id"),
            operationName: localStorage.getItem("realName"),
          }
          this.addTimedTaskLog(timedTaskLog);
          this.$message.success("修改成功");
          //刷新
          this.reload();
        } else {
          return this.$message.error("修改失败");
        }
      })
    },
    run(id) {
      let timedTask = {
        id: id,
        state: 1,
      }
      this.$postData("updateTimedTaskState", timedTask).then(res => {
        if (res.data > 0) {
          this.$message.success("修改成功");
          //刷新
          this.reload();
        } else {
          return this.$message.error("修改失败");
        }
      })
    },
    deleteSwarmRobots(id) {
      let timedTask = {
        id: id,
        state: 0,
      }
      this.$postData("updateTimedTaskState", timedTask).then(res => {
        if (res.data > 0) {
          this.$message.success("删除成功");
          //刷新
          this.reload();
        } else {
          return this.$message.error("删除失败");
        }
      })
    },
    updateSwarmRobots(data){
      this.robotPushMsg = data
      this.dialogVisible = true
    },
    deleteSpecify(id) {
      this.$getData("deleteSpecify", {id: id}).then(res => {
        if (res.data > 0) {
          let timedTaskLog = {
            id: id,
            type: 0,
            timedTaskId: id,
            operationId: localStorage.getItem("id"),
            operationName: localStorage.getItem("realName"),
          }
          this.addTimedTaskLog(timedTaskLog);
          this.$message.success("删除成功");
          //刷新
          this.reload();
        } else {
          return this.$message.error("删除失败");
        }
      })
    },
    specifyLog(id) {
      this.$getData("selectTimedTaskLog", {timedTaskId: id}).then(res => {
        if (res.status === 200) {
          this.list6 = res.data;
        } else {
          return this.$message.error(res);
        }
      })
      this.isAssignTasksLog = true;
    },
    updateSpecify(specify) {
      this.getTimedTaskParameter(specify.id);
      this.value3 = specify.corn[1].substring(2);
      this.dom = specify;
      this.assignTasks = true;
      this.value1 = +specify.corn[2].substring(0, specify.corn[2].indexOf("-"));
      this.value2 = specify.corn[2].substring(specify.corn[2].indexOf("-") + 1);
      this.list.forEach(e => {
        if (e.id === specify.swarmRobotsId) {
          this.name = e.name;
        }
      });
    },
    getTimedTaskParameter(timedTaskId) {
      this.$getData("getTimedTaskParameter", {timedTaskId: timedTaskId}).then(res => {
        this.dom.timedTaskParameters = res.data;
      })
    },
    addTimedTask() {
      if (this.dom.name === null || this.dom.name === '' || this.dom.swarmRobotsId === null || this.dom.swarmRobotsId === '') {
        return this.$message({message: '机器人不能为空', type: 'warning'});
      }
      if (this.dom.model === null || this.dom.model === '' || this.dom.time === null || this.dom.time === '') {
        return this.$message({message: '重复模式或者时间不能为空', type: 'warning'});
      }
      if ((this.dom.model === 4 || this.dom.model === 5) && this.date.length < 1) {
        return this.$message({message: '日期不能为空', type: 'warning'});
      }
      if (this.dom.type === null || this.dom.type === '') {
        return this.$message({message: '类型不能为空', type: 'warning'});
      }
      if (this.dom.type === 0 && (this.dom.content === null || this.dom.content === '')) {
        return this.$message({message: '文本不能为空', type: 'warning'});
      }
      if (this.dom.type === 1 && (this.dom.pic_url === null || this.dom.pic_url === '')) {
        return this.$message({message: '图片不能为空', type: 'warning'});
      }
      if (this.dom.type === 2 && (this.dom.title === null || this.dom.title === '' || this.dom.url === null || this.dom.url === '')) {
        return this.$message({message: '标题、链接不能为空', type: 'warning'});
      }
      this.dom.date = this.date.join(",");
      this.$postData("addTimedTask", this.dom).then(res => {
        if (res.data > 0) {
          this.$message.success("添加成功");
          //刷新
          this.reload();
        } else if (res.data === null) {
          return this.$message.error("sql错误");
        } else {
          return this.$message.error("添加失败");
        }
      })
    },
    addSwarmRobots() {
      if (this.swarmRobots.name === '' || this.swarmRobots.name === null || this.swarmRobots.webhook === '' || this.swarmRobots.webhook === null) {
        return this.$message.error("不能放空");
      }
      this.$postData("addSwarmRobots", this.swarmRobots).then(res => {
        if (res.status === 200) {
          this.reset();
          this.$message.success("添加成功!");

        }else {
          this.$message.error(res.msg);

        }
      })
    },
    selectSwarmRobots() {
      this.$getData("selectSwarmRobots",).then(res => {
        if (res.status === 200) {
          this.list1 = res.data;
        }
        console.log(res)
      })
    },
    getSwarmRobots() {
      this.$postData("getSwarmRobots", this.swarmRobots).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        } else {
          return this.$message.error(res);
        }
      })
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.swarmRobots.size = size;
      this.getSwarmRobots();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.swarmRobots.current = index;
      this.getSwarmRobots();
    },
    reset() {
      this.bitSwarmRobots = false;
      this.swarmRobots.name = null;
      this.swarmRobots.webhook = null;
      this.getSwarmRobots();
    },
    messagePush(id) {
      this.dom.swarmRobotsId = id;
      this.bitSwarmRobots2 = true;
      this.list.forEach(e => {
        if (e.id === id) {
          this.name = e.name;
        }
      });
    },
    log(id) {
      this.table = true;
      this.list.forEach(e => {
        if (e.id === id) {
          this.name = e.name;
        }
      });
      this.$getData("messagePushLog", {swarmRobotsId: id}).then(res => {
        if (res.status === 200) {
          this.list3 = res.data;
        } else {
          return this.$message.error(res);
        }
      })
    },
    //选择图片
    picture(val) {
      this.dom.pic_url = val;
      this.selectPicture = false;
    },
    push() {
      if(this.dom.type===3){
        this.dom.type = 1
      }
      this.dom.date = this.date.join(",");
      this.$postData("messagePush", this.dom).then(res => {
        if (res.data > 0) {
          this.$message.success("推送成功");
          this.reload();
        } else {
          return this.$message.error("推送失败");
        }
      })
    },
    change2(value) {
      this.dom.model = value;
    },
    change(id) {
      this.dom.swarmRobotsId = id;
    },
    timeFormat(row, column) {
      var date = row[column.property];
      if (date === undefined) {
        return '';
      }
      return date.substring(0, 8);
    },
    dateFormat(row, column) {
      var date = row[column.property];
      if (date === undefined) {
        return '';
      }
      return moment(date).format("YYYY-MM-DD HH:mm:ss");
    },
    timedTask(id) {
      this.isTimedTask = true;
      this.$getData("timedTaskLog", {swarmRobotsId: id}).then(res => {
        if (res.status === 200) {
          let aaa = [];
          res.data.forEach(e => {
            e.date = e.date.split(",");
            aaa.push(e);
          })
          this.list4 = aaa;
        } else {
          return this.$message.error(res);
        }
      })
      this.list.forEach(e => {
        if (e.id === id) {
          this.name = e.name;
        }
      });
    },
    assignTasksLog(id) {
      this.isAssignTasks = true;
      this.$getData("assignTasksLog", {swarmRobotsId: id}).then(res => {
        if (res.status === 200) {
          res.data.forEach(e => {
            e.corn = e.corn.split(" ");
          })
          this.list5 = res.data;
        } else {
          return this.$message.error(res);
        }
      })
      this.list.forEach(e => {
        if (e.id === id) {
          this.name = e.name;
        }
      });
    },
    graphicsContent(graphics) {
      this.dom.title = graphics.title;
      this.dom.picurl = graphics.picurl;
      this.dom.desc = graphics.desc;
      this.dom.url = graphics.url;
      this.selectGraphics = false;
    },
  },
}
</script>

<style scoped>
.text {
  font-size: 10px;
  text-align: center;
}

.item {
  padding: 10px 0;
}


.agent-box {
  padding: 10px;
  border: 1px solid #ddd;
}

.box-card {
  width: 180px;
}

>>> .el-toopltip__popper {
  font-size: 14px;
  max-width: 40%;
}

.agent-box {
  padding: 10px;
  border: 1px solid #ddd;
}

.el-tag + .el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
</style>