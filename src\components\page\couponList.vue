<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">
                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="优惠券key">
                                <el-input
                                        clearable
                                        v-model="dto.key"
                                        placeholder="批次号"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="批次号">
                                <el-input
                                        clearable
                                        v-model="dto.couponGroupId"
                                        placeholder="批次号"
                                        style="width:170px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="会员账号">
                                <el-input
                                        clearable
                                        v-model="dto.name"
                                        placeholder="支持账号/手机号码"
                                        style="width:170px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="状态">
                                <Select filterable clearable style="width: 120px" v-model="dto.state"
                                        @on-change="query()">
                                    <Option value="">请选择</Option>
                                    <Option value="1">未绑定</Option>
                                    <Option value="2">已绑定</Option>
                                    <Option value="3">已使用</Option>
                                    <Option value="4">已过期</Option>
                                    <Option value="5">已作废</Option>
                                    <Option value="6">已退款</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item>
                                <el-button type="primary" @click="query()">搜索</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-table :data="list" class="table" ref="multipleTable"
                      v-loading="loading">

                <!--<el-table-column-->
                <!--width="100"-->
                <!--label="操作">-->
                <!--<template slot-scope="scope">-->
                <!--<div style="text-align: center">-->
                <!--<div>-->
                <!--<el-button size="mini" @click="edit(scope.row)" type="primary">查看</el-button>-->
                <!--</div>-->
                <!--<div style="margin-top: 10px">-->
                <!--<el-button size="mini" v-if="scope.row.appealReason!=null&&scope.row.appealReason!==''"-->
                <!--@click="appealMush(scope.row)"-->
                <!--type="warning">申诉处理-->
                <!--</el-button>-->
                <!--</div>-->
                <!--<div style="margin-top: 10px">-->
                <!--<el-button size="mini" @click="deleteTs(scope.row.id)" type="danger">删除</el-button>-->
                <!--</div>-->
                <!--</div>-->
                <!--</template>-->
                <!--</el-table-column>-->
                <el-table-column
                        prop="couponGroup.name"
                        width="140"
                        label="名称">
                </el-table-column>
                <el-table-column
                        width="80"
                        prop="couponGroupId"
                        label="批次号"
                ></el-table-column>
                <el-table-column
                        width="180"
                        prop="key"
                        label="key"
                ></el-table-column>
                <el-table-column
                        width="120"
                        prop="member.account"
                        label="会员账号"
                ></el-table-column>
                <el-table-column
                        width="180"
                        prop="useTime"
                        label="激活时间"
                ></el-table-column>
                <el-table-column
                        width="170"
                        prop=""
                        label="有效期">
                    <template slot-scope="scope">
                    <span>
                        {{scope.row.startTime}}
                    </span>
                        <p> {{scope.row.endTime}}</p>

                    </template>
                </el-table-column>
                <el-table-column
                        width="100"
                        prop="couponGroup.faceValue"
                        label="优惠券金额">
                </el-table-column>
                <el-table-column
                        width="100"
                        prop="状态"
                        label="状态">
                    <template slot-scope="scope">
                        <span v-if="scope.row.state===1">未绑定</span>
                        <span v-if="scope.row.state===2">已绑定</span>
                        <span v-if="scope.row.state===3">已使用</span>
                        <span v-if="scope.row.state===4">已过期</span>
                        <span v-if="scope.row.state===5">已作废</span>
                        <span v-if="scope.row.state===6">已退款</span>
                    </template>
                </el-table-column>
                <el-table-column
                        width="150"
                        prop="remark"
                        label="备注">
                </el-table-column>
            </el-table>

            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :current="this.dto.pageNum"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="10"/>
            </div>
        </div>
    </div>

</template>

<script>
    export default {
        data() {
            return {
                loading: true,
                list: null,
                pageSizeOpts: [10, 20, 30],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                dto: {
                    state: null,
                    name: null,
                    phone: null,
                    key: null,
                    couponGroupId: null,
                    pageSize: 10,
                    pageNum: 1,
                },
            }
        },
        components: {},
        created() {
            this.getData();
        },
        computed: {},
        methods: {
            getData() {
                this.$postData("coupon_selectPage", this.dto, {}).then(res => {
                    this.loading = false;
                    if (res.status === 200) {
                       ;
                        this.list = res.data.list;
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    }
                });
            },
            query() {
                this.loading = true;
                this.dto.pageNum = 1;
                this.getData();
            },

            // 页码大小
            onPageSizeChange(size) {
                this.loading = true;
                this.dto.pageSize = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                this.loading = true;
                this.dto.pageNum = index;
                this.getData();
            },
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
