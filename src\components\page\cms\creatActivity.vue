<template>
    <div class="table" >
        <el-dialog title="展示地址" :visible.sync="dialogTableVisible">
            <vue-qr :text="showUrl" :margin="0"  colorLight="#fff"  :logoScale="0.3" :size="100" class="hbeimg"></vue-qr>
        </el-dialog>
        <div style="width:100%;background: #fff;padding: 10px;text-align: right;">

                <el-button type="primary" icon="el-icon-edit" round @click="saveActivity()">保存并预览</el-button>
                <el-button type="warning" icon="el-icon-star-off" round>不保存退出</el-button>
                <el-button type="danger" icon="el-icon-delete" round v-if="indexDom.id!=null" @click="delectDom()">删除</el-button>

        </div>
        <el-drawer
                title="内容封面"
                :visible.sync="drawer"
                direction="rtl"
                size="60%">
            <cmsMaterial type="edit"  @init-choose="initCover"></cmsMaterial>
        </el-drawer>

      <el-drawer
          title="内容封面"
          :visible.sync="drawer1"
          direction="rtl"
          size="60%">
        <cmsMaterial type="edit"  @init-choose="initRowImg"></cmsMaterial>
      </el-drawer>

      <el-drawer
          title="弹窗图片"
          :visible.sync="alertImageDrawer"
          direction="rtl"
          size="60%">
        <cmsMaterial type="edit"  @init-choose="initalertImageCover"></cmsMaterial>
      </el-drawer>
        <el-drawer
                title="内容封面"
                :visible.sync="drawerAlert"
                direction="rtl"
                size="60%">
            <cmsMaterial type="edit"  @init-choose="initAlertCover"></cmsMaterial>
        </el-drawer>
        <div class="container" style="padding:20px;background: #f5f6f6">
        <el-row >
            <el-col :span="6">

                <el-card class="box-card" style="width: 90%;height: 100%">
                    <el-divider content-position="left">组件</el-divider>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-button type="primary" plain icon="el-icon-plus" @click="add('img')">图片图层</el-button>
                        </el-col>
                        <el-col :span="12">
                            <el-button type="primary" plain icon="el-icon-plus" @click="add('text')">文本图层</el-button>
                        </el-col>
                    </el-row>
                  <el-row :gutter="20">
                    <br>
                    <el-col :span="12">
                      <el-button type="primary" plain icon="el-icon-plus" @click="add('rowImgs')">横排双图</el-button>
                    </el-col>
                  </el-row>
                </el-card>
            </el-col>
            <el-col :span="8">

                    <el-card class="box-card" :style="'width:90%;min-height: 650px;'+'margin:'+indexDom.margin+';padding:'+indexDom.padding " :body-style="{ padding: '0px' }">
                        <draggable element="ul" v-model="listdata">
                            <span v-for="(item,index) in listdata" :key="index" @click="changeDom.id==item.id?changeDom=dom:changeDom=item" :style="changeDom.id==item.id?'border: 1px solid #ddd':'border:none'">
                                <img v-if="item.type=='img'" :src="item.text"  :style="'width:'+item.width+';height:'+item.height+';margin:'+item.margin+';padding:'+item.padding "/>
                                <p v-if="item.type=='text'"   :style="'width:'+item.width+';height:'+item.height+';margin:'+item.margin+';padding:'+item.padding  ">
                                    {{item.text}}
                                </p>
                                <div v-if="item.type == 'rowImgs'" >
                                  <ul>
                                    <li style="display: flex;justify-content: space-between;">
                                      <img :src="item.rowImg1" :style="'width:50%'+';height:auto'+';margin:'+item.margin+';padding:'+item.padding ">
                                      <img :src="item.rowImg2" :style="'width:50%'+';height:auto'+';margin:'+item.margin+';padding:'+item.padding ">
                                    </li>
                                  </ul>
                                </div>
                            </span>
                        </draggable>



                        <!-- 调用组件 -->

                        <!-- 展示list数据效果 -->
<!--                        {{listdata}}-->
                    </el-card>

            </el-col>
            <el-col :span="10" style="min-height: 500px" v-if="changeDom.id!=0">
                <el-card class="box-card" >
                    <el-divider>参数设置</el-divider>
                    <el-tabs tab-position="left" type="border-card" style="min-height: 500px">
                        <el-tab-pane label="基础配置">
                            <el-form label-position="right" label-width="80px" :model="changeDom">
                                <el-form-item label="名称">
                                    <el-input v-model="changeDom.name"></el-input>
                                </el-form-item>
                                <el-form-item label="宽度">
                                    <el-input v-model="changeDom.width"></el-input>
                                </el-form-item>
                                <el-form-item label="高度">
                                    <el-input v-model="changeDom.height"></el-input>
                                </el-form-item>
                                <el-form-item label="图片" v-if="changeDom.type=='img'">
                                    <div @click="drawer=true">
                                        <el-image
                                                style="width: 100px; height: 150px;min-width: 150px;"
                                                :src="changeDom.text" >
                                            <div slot="error" class="image-slot"  style="font-size: 25px;color: #ddd;padding: 50px;background: #f1f1f1;text-align: center;">
                                                <i class="el-icon-plus"></i>
                                            </div>
                                        </el-image>
                                    </div>

                                </el-form-item>

                                <el-form-item label="文本" v-if="changeDom.type=='text'">
                                    <el-input v-model="changeDom.text"></el-input>
                                </el-form-item>

                              <el-form-item label="左图" v-if="changeDom.type=='rowImgs'">
                                <div @click="drawer1 = true,rowImgType = 1">
                                  <el-image
                                      style="width: 100px; height: 150px;min-width: 150px;"
                                      :src="changeDom.rowImg1" >
                                    <div slot="error" class="image-slot"  style="font-size: 25px;color: #ddd;padding: 50px;background: #f1f1f1;text-align: center;">
                                      <i class="el-icon-plus"></i>
                                    </div>
                                  </el-image>
                                </div>

                              </el-form-item>

                              <el-form-item label="右图" v-if="changeDom.type=='rowImgs'">
                                <div @click="drawer1 = true,rowImgType = 2">
                                  <el-image
                                      style="width: 100px; height: 150px;min-width: 150px;"
                                      :src="changeDom.rowImg2" >
                                    <div slot="error" class="image-slot"  style="font-size: 25px;color: #ddd;padding: 50px;background: #f1f1f1;text-align: center;">
                                      <i class="el-icon-plus"></i>
                                    </div>
                                  </el-image>
                                </div>

                              </el-form-item>

                                <el-form-item label="超链接">
                                  <div v-if="changeDom.type=='rowImgs'">
                                    <el-tag type="info" >左图跳转链接</el-tag>
                                    <el-input v-model="changeDom.rowImgUrl1" ></el-input>
                                    <el-tag type="info" >右图跳转链接</el-tag>
                                    <el-input v-model="changeDom.rowImgUrl2" ></el-input>
                                  </div>
                                  <div v-else>
                                  <el-tag type="info" @click="changeDom.link='https://card.xiaoyujia.com'">https://card.xiaoyujia.com</el-tag>
                                  <el-divider direction="vertical"></el-divider>
                                  <el-tag type="info" @click="changeDom.link='https://hy.xiaoyujia.com'">https://hy.xiaoyujia.com</el-tag>
                                  <el-input v-model="changeDom.link"></el-input>
                                </div>

                                </el-form-item>
                                <el-form-item label="外边距">
                                    <el-tag type="info" @click="changeDom.margin='0% 0% 0% 0%'">0% 0% 0% 0%</el-tag>
                                    <el-input v-model="changeDom.margin"></el-input>
                                </el-form-item>
                                <el-form-item label="内边距">
                                    <el-tag type="info" @click="changeDom.padding='0% 0% 0% 0%'">0% 0% 0% 0%</el-tag>
                                    <el-input v-model="changeDom.padding"></el-input>
                                </el-form-item>
                            </el-form>
                        </el-tab-pane>
                        <el-tab-pane label="弹窗配置">
                            <el-form label-position="right" label-width="80px" :model="changeDom">
                                <el-form-item label="开启弹窗">
                                    <el-switch
                                            v-model="changeDom.showAlert">
                                    </el-switch>
                                </el-form-item>
                                <el-form-item label="弹窗类型">
                                    <el-radio-group v-model="changeDom.alertType">
                                        <el-radio :label="'text'">文字弹窗</el-radio>
                                        <el-radio :label="'img'">图片弹窗</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item label="弹窗图片" v-if="changeDom.alertType=='img'">
                                    <div @click="drawerAlert=true">
                                        <el-image
                                                style="width: 100px; height: 150px;min-width: 150px;"
                                                :src="changeDom.alertText" >
                                            <div slot="error" class="image-slot"  style="font-size: 25px;color: #ddd;padding: 50px;background: #f1f1f1;text-align: center;">
                                                <i class="el-icon-plus"></i>
                                            </div>
                                        </el-image>
                                    </div>

                                </el-form-item>

                                <el-form-item label="弹窗文本" v-if="changeDom.alertType=='text'">
                                    <el-input v-model="changeDom.alertText"></el-input>
                                </el-form-item>
                            </el-form>
                        </el-tab-pane>
                        <el-tab-pane label="领劵配置">

                        </el-tab-pane>
                    </el-tabs>


                    <el-divider>操作</el-divider>
                    <el-button type="danger" @click="removeDom(changeDom)">移除图层</el-button>
                </el-card>
            </el-col>
            <el-col :span="8" style="min-height: 500px" v-if="changeDom.id==0">
                <el-card class="box-card" >
                    <el-divider>场景配置</el-divider>
                    <el-form label-position="right" label-width="120px" :model="indexDom">
                        <el-form-item label="名称">
                            <el-input v-model="indexDom.name"></el-input>
                        </el-form-item>


                        <el-form-item label="简要">
                            <el-input v-model="indexDom.detail"  type="textarea"></el-input>
                        </el-form-item>

<!--                        <el-form-item label="外边距">-->
<!--                            <el-tag type="info" @click="indexDom.margin='0% 0% 0% 0%'">0% 0% 0% 0%</el-tag>-->
<!--                            <el-input v-model="indexDom.margin"></el-input>-->
<!--                        </el-form-item>-->
<!--                        <el-form-item label="内边距">-->
<!--                            <el-tag type="info" @click="indexDom.padding='0% 0% 0% 0%'">0% 0% 0% 0%</el-tag>-->
<!--                            <el-input v-model="indexDom.padding"></el-input>-->
<!--                        </el-form-item>-->
                      <el-form-item label="是否首页弹窗">
                        <el-tag type="info">设置为是的话，那么其他活动的弹窗将会失效</el-tag>
                        <el-switch
                            v-model="indexDom.alert"
                            active-color="#13ce66"
                            inactive-color="#ff4949"
                            active-text="是"
                            inactive-text="否"
                            :active-value="1"
                            :inactive-value="0">
                        </el-switch>
                      </el-form-item>
                      <el-form-item label="特价结算">
                        <el-switch
                            v-model="indexDom.specialSettlement"
                            active-color="#13ce66"
                            inactive-color="#ff4949"
                            active-text="是"
                            inactive-text="否"
                            :active-value="1"
                            :inactive-value="0">
                        </el-switch>
                      </el-form-item>
                      <el-form-item label="弹窗图片">
                        <el-tag type="info">确定展示弹窗的图片</el-tag>
                        <div @click="alertImageDrawer=true">
                          <el-image
                              style="width: 100px; height: 150px;min-width: 150px;"
                              :src="indexDom.alertImage" >
                            <div slot="error" class="image-slot"  style="font-size: 25px;color: #ddd;padding: 50px;background: #f1f1f1;text-align: center;">
                              <i class="el-icon-plus"></i>
                            </div>
                          </el-image>
                        </div>
                      </el-form-item>
                      <el-form-item label="活动开始时间">
                        <el-tag type="info">活动开始时间</el-tag>
                        <el-date-picker
                            v-model="indexDom.startTime"
                            value-format="yyyy-MM-dd"
                            type="date"
                            placeholder="选择日期">
                        </el-date-picker>
                      </el-form-item>
                      <el-form-item label="活动结束时间">
                        <el-tag type="info">活动结束时间</el-tag>
                        <el-date-picker
                            v-model="indexDom.endTime"
                            value-format="yyyy-MM-dd"
                            type="date"
                            placeholder="选择日期">
                        </el-date-picker>
                      </el-form-item>
                    </el-form>
                </el-card>
            </el-col>
        </el-row>
        </div>
    </div>


</template>

<script>
    import vueQr from 'vue-qr'
    import draggable from 'vuedraggable'
    import cmsMaterial from "./cmsMaterial";
    export default {
        name: "creatActivity",
        components: {
            draggable,
            cmsMaterial,
            vueQr
        },
        data () {
            return {
                dialogTableVisible:false,
                showUrl:null,
                tg:this.$route.query.tg,
                indexDom:{
                    id:null,
                    name:new Date().getTime().toString(),
                    padding:null,
                    margin:null,
                    detail:null,
                    status:null,
                    alert:null,
                  specialSettlement:null,
                },
                activeNames:['1'],
                drawer:false,
                drawer1:false,
                drawerAlert:false,
                rowImgType:1,
              alertImageDrawer:false,
                dom:{
                    link:null,
                    margin:null,
                    padding:null,
                    height:null,
                    type:null,
                    width:null,
                    id: 0,
                    name: null,
                    text:null,
                    showAlert:false,
                    alertType:'text',
                    alertText:'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/cms/null1621330691136b28e1d1b24eabb2a42abc955758824d3.jpg',
                  rowImg1:'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/cms/null1621330691136b28e1d1b24eabb2a42abc955758824d3.jpg',
                  rowImg2:'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/cms/null1621330691136b28e1d1b24eabb2a42abc955758824d3.jpg',
                },
                changeDom:{
                    link:null,
                    margin:null,
                    padding:null,
                    height:null,
                    type:null,
                    width:null,
                    id: 0,
                    name: null,
                    text:null,
                    showAlert:false,
                    alertType:'text',
                    alertText:'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/cms/null1621330691136b28e1d1b24eabb2a42abc955758824d3.jpg',
                    rowImg1:'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/cms/null1621330691136b28e1d1b24eabb2a42abc955758824d3.jpg',
                    rowImg2:'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/cms/null1621330691136b28e1d1b24eabb2a42abc955758824d3.jpg',
                    rowImgUrl1:'',
                    rowImgUrl2:'',

                },
                listdata:[
                ],
                id:this.$route.query.id,

            }
        },
        created(){
            if (this.id!==null && this.id!==undefined){
                this.getMarketingDtoById()
            }
        },
        methods:{
          initRowImg(material) {
            if (this.rowImgType === 1) {

              this.changeDom.rowImg1 = material.url
            } else  {
              this.changeDom.rowImg2 = material.url
            }
            this.drawer1 = false;
          },
          initalertImageCover(material){
            this.indexDom.alertImage=material.url
            this.alertImageDrawer=false
          },
            setAlert(){
                this.listdata.forEach(v=>{
                    v.id=null
                    if (v.showAlert==1){
                        v.showAlert=true
                    }else {
                        v.showAlert=false
                    }
                })
            },
            getMarketingDtoById(){
                this.$getData("getMarketingDtoById", {id:this.id}).then(res => {
                    if (res.status == 200) {
                        if (null==res.data){
                            return this.$message.error("查询失败，");
                        }
                        this.indexDom=res.data.marketing
                        this.listdata=res.data.marketingParams
                        this.setAlert()

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            delectDom(){
                this.indexDom.status=0
                this.saveActivity()
            },
            saveActivity(){
                this.listdata.forEach(v=>{
                    v.id=null
                    if (v.showAlert==true){
                        v.showAlert=1
                    }else {
                        v.showAlert=0
                    }
                })
                let marketingDto={
                    marketing:this.indexDom,
                    marketingParams:this.listdata,
                }
                this.$postData("saveMarketingDto",marketingDto,null).then(res => {
                    if (res.status == 200) {
                        this.indexDom=res.data.marketing
                        this.listdata=res.data.marketingParams
                        this.$message.success("保存成功");
                        this.showUrl ="https://m.xiaoyujia.com/pages/product/activity?code="+ res.data.marketing.code
                        this.dialogTableVisible=true
                        this.setAlert()
                    }else {
                        this.$message.error(res.msg);
                    }
                })

            },
            removeDom(dom){
                this.listdata.some((item, i) => {
                    if (item.id == dom.id ){
                        this.listdata.splice(i,1)
                        // 在数组的some方法中，如果return true，就会立即终止这个数组的后续循环,所以相比较foreach，如果想要终止循环，那么建议使用some
                        return true;
                    }
                })
            },
            add(type){
            console.log(type)
               let dom={
                    link:null,
                   margin:null,
                   padding:null,
                   height:null,
                   type:type,
                   width:'100%',
                   id: new Date().getTime().toString(),
                   name: new Date().getTime().toString(),
                   text:type=='img'?'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/cms/null1621330691136b28e1d1b24eabb2a42abc955758824d3.jpg':'我是文本',
                   showAlert:false,
                   alertType:'text',
                   alertText:'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/cms/null1621330691136b28e1d1b24eabb2a42abc955758824d3.jpg',
                  rowImg1:'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/cms/null1621330691136b28e1d1b24eabb2a42abc955758824d3.jpg',
                  rowImg2:'http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/cms/null1621330691136b28e1d1b24eabb2a42abc955758824d3.jpg'
                }
                this.listdata.push(dom)
            },
            initCover(material){

                this.changeDom.text=material.url
                this.drawer=false

                console.log(material)
            },
            initAlertCover(material){

                this.changeDom.alertText=material.url
                this.drawerAlert=false

                console.log(material)
            },
        }
    }
</script>

<style scoped>
    .el-tabs__header{
        min-height: 500px;
    }
    .el-card__body {
         padding: 0;
    }
    img{
        vertical-align: top
    }
    p{
        word-wrap: break-word;
        word-break: break-all;
        overflow: hidden;
    }
</style>
