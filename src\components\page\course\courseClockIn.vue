<template>
	<div class="container">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">打卡管理</el-menu-item>
			<!-- <el-menu-item index="1">数据分析</el-menu-item> -->
		</el-menu>

		<!-- 搜索栏目 -->
		<div v-if="choiceIndexNow==0">
			<div class="handle-box" style="margin: 20px 20px;">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="打卡活动标题、内容"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="打卡类型" style="margin-right: 20px">
								<el-select v-model="searchType" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="打卡状态" style="margin-right: 20px">
								<el-select v-model="searchState" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(0,0)">添加
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">

				<el-table-column prop="id" width="60" label="编号">
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="clockInTitle" label="活动标题">
					<template slot-scope="scope">
						<span>{{scope.row.clockInTitle}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="clockInContent" label="活动内容">
					<template slot-scope="scope">
						<span>{{scope.row.clockInContent||'暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="clockInType" label="打卡类型" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-tag v-if="item.id == scope.row.clockInType" v-for="(item,index) in typeList" :key="index"
							:type="typeStyleList[index%4].type">{{item.typeName}}</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="122" label="关联课程" prop="courseId">
					<template slot-scope="scope">
						<span v-if="scope.row.courseId==item.id" v-for="(item,index) in courseList" :key="index">
							{{item.courseTitle}}
						</span>
						<span v-if="!scope.row.courseId">
							暂未设置
						</span>
					</template>
				</el-table-column>

				<el-table-column width="120" prop="joinNum" label="参加人数" sortable>
				</el-table-column>

				<el-table-column width="120" prop="clockInNum" label="打卡次数" sortable>
				</el-table-column>

				<el-table-column width="102" prop="collectLimit" label="人数限制" :render-header="renderHeader">
				</el-table-column>

				<el-table-column width="102" label="状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.clockInState == 0" type="info">下架</el-tag>
						<el-tag v-if="scope.row.clockInState == 1" type="success">上架</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="startTime" label="开始日期" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.startTime||'暂未设置'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="endTime" label="结束日期" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.endTime||'暂未设置'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creator" label="创建人">
					<template slot-scope="scope">
						<span v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
						<span v-else>{{scope.row.creator||''}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="200" label="操作">
					<template slot-scope="scope">
						<el-button @click="openModal(1,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
						<el-popconfirm title="确定删除该打卡活动吗？删除后无法恢复，请谨慎操作!" @confirm="deleteCourseClockIn(scope.row)"
							v-if="isAdmin">
							<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
								icon="el-icon-delete">删除
							</el-button>
						</el-popconfirm>
						<el-button @click="openModal(5,scope.$index)" type="warning" size="small" icon="el-icon-s-order"
							style="margin-left: 10px">
							记录
						</el-button>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<div v-if="choiceIndexNow==1">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="问题标题、内容"></el-input>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="是否必填" style="margin-right: 20px">
								<el-select v-model="searchRequired">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in switchStateList" :key="index"
										:label="item.typeName" :value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="问题状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>
							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>
							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(3,0)">添加
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands">

				<el-table-column prop="id" width="60" label="编号">
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="100" prop="questionTitle" label="问题标题">
					<template slot-scope="scope">
						<span>{{scope.row.questionTitle}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="questionContent" label="问题内容">
					<template slot-scope="scope">
						<span>{{scope.row.questionContent||'暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="102" prop="answerType" label="回答类型">
					<template slot-scope="scope">
						<el-tag v-if="item.id == scope.row.answerType" v-for="(item,index) in answerTypeList"
							:key="index" :type="typeStyleList[index%4].type">{{item.typeName}}</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="102" label="状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.questionState == 0" type="info">下架</el-tag>
						<el-tag v-if="scope.row.questionState == 1" type="success">上架</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creator" label="创建人">
					<template slot-scope="scope">
						<span v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
						<span v-else>{{scope.row.creator||''}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
					<template slot-scope="scope">
						<el-button @click="openModal(4,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--添加打卡-->
		<el-drawer size="60%" :with-header="false" :visible.sync="courseClockInModal" direction="rtl">
			<div>
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">打卡信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 活动标题：">
								<el-input v-model="choiceItem.clockInTitle" type="textarea" class="handle-input mr10"
									placeholder="请输入活动标题" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="* 打卡内容：">
								<el-input v-model="choiceItem.clockInContent" type="textarea" class="handle-input mr10"
									placeholder="请输入打卡内容描述" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="关联课程：">
								<el-select v-model="choiceItem.courseId" placeholder="请选择" filterable>
									<el-option v-for="(item,index) in courseList" :key="index"
										:label="item.id+'：'+item.courseTitle" :value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="在对应的课程中显示该打卡" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="人数限制：">
								<el-input v-model="choiceItem.collectLimit" type="number" class="handle-input mr10"
									placeholder="请输入人数限制">
								</el-input>
								<el-tooltip class="item" effect="dark" content="填写人数达到限制后打卡入口关闭" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>
						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="打卡类型：">
								<el-select v-model="choiceItem.clockInType" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="可根据类型进行分类" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="打卡状态：">
								<el-select v-model="choiceItem.clockInState" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item width="250" label="开始日期：">
								<el-date-picker v-model="choiceItem.startTime" type="datetime"
									value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00']"
									placeholder="请选择打卡开始日期" :picker-options="pickerOptions">
								</el-date-picker>
							</el-form-item>

							<el-form-item width="250" label="结束日期：">
								<el-date-picker v-model="choiceItem.endTime" type="datetime"
									value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00']"
									placeholder="请选择打卡结束日期" :picker-options="pickerOptions">
								</el-date-picker>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="courseClockInModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="insertCourseClockIn()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateCourseClockIn(choiceItem)" type="success" size="small"
						v-if="detailType==1">保存
					</el-button>
				</div>

			</div>
		</el-drawer>

		<!--添加打卡问题-->
		<el-drawer size="60%" :with-header="false" :visible.sync="courseClockInQuestionModal" direction="rtl">
			<div>
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">打卡问题信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 问题标题：">
								<el-input v-model="choiceItem.questionTitle" type="textarea" class="handle-input mr10"
									placeholder="请输入问题标题">
								</el-input>
							</el-form-item>

							<el-form-item label="* 问题内容：">
								<el-input v-model="choiceItem.questionContent" type="textarea" class="handle-input mr10"
									placeholder="请输入问题内容描述">
								</el-input>
							</el-form-item>
						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="回答类型：">
								<el-select v-model="choiceItem.answerType">
									<el-option v-for="(item,index) in answerTypeList" :key="index"
										:label="item.typeName" :value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="对用户填写内容进行限制" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="是否必填：">
								<el-select v-model="choiceItem.required">
									<el-option v-for="(item,index) in switchStateList" :key="index"
										:label="item.typeName" :value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="将必填项填写完整才可提交打卡" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="问题状态：" v-if="detailType==1">
								<el-select v-model="choiceItem.questionState" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="courseClockInQuestionModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="insertCourseClockInQuestion()" type="success" size="small"
						v-if="detailType==0">确定添加
					</el-button>
				</div>

			</div>
		</el-drawer>

		<!--课程打卡记录-->
		<el-drawer size="60%" :with-header="false" :visible.sync="courseClockInRecordModal" direction="rtl">
			<div class="handle-box" style="margin: 20px 20px;">
				<h2>课程打卡记录{{signInTips}}</h2>
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="searchText" placeholder="请输入打卡人、心得等"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="9">
							<el-form-item label="筛选时间">
								<div style="width: 180px;display: block;">
									<el-date-picker v-model="searchTime" type="datetimerange" format="yyyy-MM-dd"
										:picker-options="pickerOptions1" range-separator="至" start-placeholder="开始日期"
										end-placeholder="结束日期" align="right" value-format="yyyy-MM-dd HH:mm:ss">
									</el-date-picker>
								</div>
							</el-form-item>
						</el-col>
						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="listCourseClockInRecord()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh"
								@click="searchTime=[];searchText='';listCourseClockInRecord()">重置
							</el-button>
							<!-- <el-button type="danger" @click="recordDownload()">导出</el-button> -->
						</el-col>
					</el-row>
				</el-form>

				<el-table :data="courseClockInRecordList" v-loading="loading" border
					:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
					@expand-change="expandSelect">

					<el-table-column width="120" prop="realName" label="打卡人">
						<template slot-scope="scope">
							<span>{{scope.row.realName||'匿名用户'}}</span>
						</template>
					</el-table-column>

					<el-table-column width="120" prop="headImg" label="头像">
						<template slot-scope="scope">
							<img :src="scope.row.headImg||blankImg" style="width: 50px;height: 50px;"
								@click="openImg(scope.row.headImg||blankImg)" />
						</template>
					</el-table-column>

					<el-table-column width="140" prop="recordExperience" label="心得体会">
						<template slot-scope="scope">
							<span>{{scope.row.recordExperience||'暂无'}}</span>
						</template>
					</el-table-column>
					
					<el-table-column width="120" prop="recordImg" label="打卡图片">
						<template slot-scope="scope">
							<img :src="scope.row.recordImg||blankImg" style="width: 50px;height: 50px;"
								@click="openImg(scope.row.recordImg||blankImg)" />
						</template>
					</el-table-column>

					<el-table-column width="140" prop="recordAddress" label="打卡地点">
						<template slot-scope="scope">
							<span>{{scope.row.recordAddress||'暂无'}}</span>
						</template>
					</el-table-column>

					<el-table-column width="160" prop="createTime" label="打卡时间" sortable>
					</el-table-column>
				</el-table>

				<div style="margin-top: 20px">
					<el-button type="danger" @click="courseClockInRecordModal=false">关闭</el-button>
				</div>
			</div>
		</el-drawer>
	</div>
</template>

<script>
	import Vue from 'vue';

	export default {
		name: "courseClockIn",

		data() {
			return {
				// 可设置
				// 是否超级管理员（可执行删除等敏感操作）
				isAdmin: false,
				baseUrl: 'https://api.xiaoyujia.com/',

				url: '',
				imageUrl: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				excelUploadId: 1,
				excelUploadUrl: "",

				// 测试时使用
				// uploadUrl: "http://localhost:8063/files/uploadFiles",
				// excelUploadUrlOrgin: "http://localhost:8063/courseContent/excelImport",

				uploadUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
				excelUploadUrlOrgin: "https://biapi.xiaoyujia.com/courseContent/excelImport",
				deleteTips: "确定删除选中打卡吗？该操作无法恢复，请谨慎操作!",
				isEdit: false,
				showEdit: true,
				imgModal: false,
				courseClockInModal: false,
				courseClockInQuestionModal: false,
				courseClockInRecordModal: false,
				detailType: 0,
				loading: true,
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				pageSizeOpts: [10, 20, 50, 100],
				list: [],
				courseList: [],
				liveRoomList: [],
				questionList: [],
				courseClockInRecordList: [],
				choiceIndex: '0',
				choiceIndexNow: '0',
				choiceItemIndex: 0,
				choiceItem: {},
				choiceRecordId: 0,
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				signInTips: '',
				searchText: '',
				searchType: null,
				searchState: null,
				searchRequired: null,
				quer: {
					search: "",
					questionIdList: null,
					clockInState: null,
					typeState: null,
					orderBy: "createTime ASC",
					current: 1,
					size: 10
				},
				switchStateList: [{
						id: 0,
						typeName: "关闭",
						type: "info"
					},
					{
						id: 1,
						typeName: "开启",
						type: "success"
					}
				],
				typeList: [{
					id: 0,
					typeName: "默认"
				}],
				stateList: [{
					value: 0,
					text: "下架",
					type: "info"
				}, {
					value: 1,
					text: "上架",
					type: "success"
				}],
				typeStyleList: [{
						id: 0,
						type: "success"
					},
					{
						id: 1,
						type: "info"
					},
					{
						id: 2,
						type: "warning"
					}, {
						id: 3,
						type: "primary"
					}
				],
				answerTypeList: [{
						id: 0,
						typeName: "文本"
					},
					{
						id: 1,
						typeName: "数字"
					}
				],
				searchTime: [],
				pickerOptions: {
					shortcuts: [{
						text: '昨天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '今天',
						onClick(picker) {
							picker.$emit('pick', new Date());
						}
					}, {
						text: '明天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周前',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周后',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}]
				},
				pickerOptions1: {
					shortcuts: [{
						text: '今天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '昨天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}]
				}
			}
		},
		created() {
			if (this.$route.query.isAdmin) {
				this.isAdmin = true
			}
			this.getData()
		},
		methods: {
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceIndex = key
				this.expands = []
				this.showEdit = true
				this.reset()
			},
			getData() {
				this.choiceIndexNow = 2
				if (this.choiceIndex == 0) {
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'course/pageCourseClockIn',
						data: JSON.stringify(this.quer),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							this.loading = false
							if (res.code == 0) {
								this.list = res.data.records
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
								this.choiceIndexNow = this.choiceIndex
							} else {
								this.list = []
								this.$message.error('未查询到相关打卡!')
								this.choiceIndexNow = this.choiceIndex
							}
						},
					})
					this.listCourse()
				} else if (this.choiceIndex == 1) {

				}
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.quer.clockInState = this.searchState
				this.quer.questionState = this.searchState
				this.quer.clockInType = this.searchType
				this.quer.required = this.searchRequired
				this.getData()
			},
			// 重置
			reset() {
				this.loading = true
				this.quer.search = ""
				this.quer.clockInState = null
				this.quer.clockInType = null
				this.quer.questionState = null

				this.quer.orderBy = "t.id ASC"
				this.quer.current = 1

				this.searchText = ''
				this.searchState = null
				this.searchType = null
				this.searchRequired = null
				this.isEdit = false
				this.getData()
			},
			listCourse() {
				this.$postData("listCourse", {}).then(res => {
					if (res.status == 200) {
						this.courseList = res.data
					}
				})
			},
			listCourseClockInRecord() {
				let joinNum = this.list[this.choiceItemIndex].joinNum
				let clockInNum = this.list[this.choiceItemIndex].clockInNum
				let quer = {
					clockInId: this.list[this.choiceItemIndex].id,
					search: this.searchText,
					startTime: null,
					endTime: null,
					orderBy: 't.createTime DESC',
					current: 1,
					size: 999
				}
				// 处理一下时间筛选字段
				if (this.searchTime && this.searchTime.length >= 2) {
					quer.startTime = this.searchTime[0]
					quer.endTime = this.searchTime[1]
				} else {
					quer.startTime = null
					quer.endTime = null
				}
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'course/pageCourseClockInRecord',
					data: JSON.stringify(quer),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						this.loading = false
						if (res.code == 0) {
							this.courseClockInRecordList = res.data.records
							this.signInTips = '（参加人数/签到次数：' + joinNum + '/' + clockInNum + '）'
						} else {
							this.courseClockInRecordList = []
							this.$message.error('未查询到相关打卡记录!')
						}
					},
				})
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
						this.choiceRecordId = row.id
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "打卡图标") {
					tips = "推荐尺寸：200*200"
				} else if (column.label == "打卡图片") {
					tips = "推荐尺寸：200*200"
				} else if (column.label == "打卡类型") {
					tips = "可根据类型进行分类"
				} else if (column.label == "人数限制") {
					tips = "本期打卡活动达到人数限制后，不再允许参与"
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			// 将字符串转化为数组
			strToArray(val) {
				let arrayString = []
				let array = []
				arrayString = val.split(',')
				arrayString.forEach(it => {
					let value = parseInt(it)
					if (value != null && value != 0) {
						array.push(value)
					}
				})
				return array
			},
			// 格式化有效期
			formatValidity(validity) {
				let result = validity ? validity + "天" : "暂未录入"
				if (result == '999天') {
					result = '永久'
				}
				return result
			},
			// 格式化打卡类型
			formatType(val) {
				let result = "暂无"
				let id = val.courseClockInQuestion
				for (let item of this.typeList) {
					if (id == item.id) {
						result = item.typeName
						break
					}
				}
				return result
			},
			formatTypeStyle(type) {
				return this.typeStyleList[type % 4].type
			},
			// 联盟试题模板下载
			recordDownload() {
				let name = this.list[this.choiceItemIndex].clockInTitle || ''
				let id = this.list[this.choiceItemIndex].id || 0
				this.$postData("listCourseClockInRecordExcelDownload", {
					courseClockInId: id
				}, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('课程打卡记录下载成功!')
					this.blobExport({
						tablename: name + "-课程打卡记录",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 打卡Excel模板下载
			excelDownload() {
				this.$postData("excelDownload", null, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('打卡Excel模板下载成功!')
					this.blobExport({
						tablename: "打卡Excel模板",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.$set(this.choiceItem, "courseClockInIcon", res.data)
				} else if (this.uploadImgType == 1) {
					this.$set(this.choiceItem, "courseClockInImg", res.data)
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadId = index

				if (this.uploadImgType == 0) {
					this.$message.info('推荐上传尺寸：250*250')
				} else if (this.uploadImgType == 1) {
					this.$message.info('推荐上传尺寸：250*250')
				}
			},
			// 打开图片预览
			openImg(url) {
				this.imageUrl = url || this.blankImg
				this.imgModal = true
			},
			// 打开视频预览
			openVideo(url) {
				if (url != null && url != '') {
					this.videoUrl = url
				}
				this.videoModal = true
			},

			openModal(index, index1) {
				if (index == 0) {
					this.detailType = 0
					this.choiceItem = {}
					this.courseClockInModal = true
				} else if (index == 1) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.courseClockInModal = true
				} else if (index == 2) {
					this.questionIdListModal = true
				} else if (index == 3) {
					this.detailType = 0
					this.choiceItem = {}
					this.courseClockInQuestionModal = true
				} else if (index == 4) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.courseClockInQuestionModal = true
				} else if (index == 5) {
					this.choiceItemIndex = index1
					this.listCourseClockInRecord()
					this.courseClockInRecordModal = true
				}
			},
			//打开员工信息详情
			rowClick(id) {

			},
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			// 添加打卡
			insertCourseClockIn() {
				let courseClockIn = this.choiceItem
				if (!courseClockIn.clockInTitle) {
					this.$message.error('请填写活动标题！')
				} else if (!courseClockIn.clockInContent) {
					this.$message.error('请填写打卡内容！')
				} else {
					let no = localStorage.getItem('account') || 'admin'
					this.$set(courseClockIn, "creator", no)
					this.$postData("insertCourseClockIn", courseClockIn).then(res => {
						if (res.status == 200) {
							this.$message.success('打卡添加成功!')
							this.courseClockInModal = false
							this.list.push(res.data)
						} else {
							this.$message.error('打卡添加失败！' + res.msg)
						}
					})
				}
			},
			// 打卡问题
			insertCourseClockInQuestion() {
				let courseClockInQuestion = this.choiceItem
				if (!courseClockInQuestion.questionTitle) {
					this.$message.error('请填写问题标题！')
				} else if (!courseClockInQuestion.questionContent) {
					this.$message.error('请填写问题内容！')
				} else {
					let no = localStorage.getItem('account') || 'admin'
					this.$set(courseClockIn, "creator", no)
					this.$postData("insertCourseClockInQuestion", courseClockInQuestion).then(res => {
						if (res.status == 200) {
							this.$message.success('打卡问题添加成功!')
							this.courseClockInQuestionModal = false
							this.list.push(res.data)
						} else {
							this.$message.error('打卡问题添加失败！' + res.msg)
						}
					})
				}
			},
			// 删除打卡
			deleteCourseClockIn(val) {
				this.$postData("deleteCourseClockIn", val).then(res => {
					if (res.status == 200) {
						this.$message.success('打卡删除成功!')
						this.$delete(this.list, this.getIndex(val.id))
					} else {
						this.$message.error('打卡删除失败！' + res.msg)
					}
				})
			},
			// 更改打卡
			updateCourseClockIn(val) {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'course/updateCourseClockIn',
					data: JSON.stringify(val),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						this.loading = false
						if (res.code == 0) {
							this.$message.success('打卡活动更新成功!')
							this.list[this.choiceItemIndex] = this.choiceItem
						} else {
							this.$message.error('打卡活动更新失败！' + res.msg)
						}
					},
				})
			},
			beforeImgUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!');
				}
				return isLt2M;
			}
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>