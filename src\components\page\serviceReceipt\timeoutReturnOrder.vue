<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="9">
            <el-form-item label="下单时间">
              <el-date-picker v-model="days" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item  label="订单状态" >
              <el-select  filterable v-model="form.orderState" clearable placeholder="请选择订单状态">
                <el-option v-for="item in orderStateOptions"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item  label="标签类型">
              <el-select filterable style="width:200px" v-model="form.lblId" clearable placeholder="请选择标签类型">
                <el-option  v-for="item in options"
                            :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="9">
            <el-form-item label="订单编号">
              <el-input
                  clearable
                  v-model="form.billNo"
                  placeholder="请输入订单编号"
                  style="width:350px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="员工工号">
              <el-input
                  clearable
                  v-model="form.serviceNo"
                  placeholder="请输入员工工号"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
            <el-button type="info"
                       style="margin-bottom: 15px"
                       @click="exportTimeoutReturnOrder"
                       icon="el-icon-download">导出
            </el-button>
            <el-button type="primary" round @click="batchPushMsgNotice()">批量推送通知</el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="订单记录">
          <el-table @selection-change="handleSelectionChange" :data="logList" v-loading="loading" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column
                type="selection"
                width="55">
            </el-table-column>
            <el-table-column prop="createTime" label="下单时间" width="160"></el-table-column>
            <el-table-column prop="realStartTime" label="开始服务时间" width="160"></el-table-column>
            <el-table-column prop="billNo" label="订单编号" width="160">
              <template slot-scope="scope">
                <el-link :href="'https://yun.xiaoyujia.com/order/baseinfo?BillNo='+scope.row.billNo" type="primary">{{scope.row.billNo}}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="productName" label="产品名称" width="140"></el-table-column>
            <el-table-column prop="realTotalAmount" label="订单金额" width="100"></el-table-column>
            <el-table-column prop="amount" label="已支付金额" width="100"></el-table-column>
            <el-table-column prop="orderState" label="订单状态" width="100">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.orderState===40" type="warning">已派单</el-tag>
                <el-tag v-if="scope.row.orderState===50" type="danger">执行中</el-tag>
                <el-tag v-if="scope.row.orderState===60" type="primary">开始服务</el-tag>
                <el-tag v-if="scope.row.orderState===70" type="success">服务结束</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="lblId" label="标签" width="120">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.lblId" type="info">{{ scope.row.lblId }}</el-tag>
                <el-tag v-if="!scope.row.lblId" type="danger">未打标签</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="paidInAmount" label="服务人员" width="180">
              <template slot-scope="scope">
                <span>{{scope.row.serviceName+'（'+scope.row.serviceNo+'）'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="hoursDifference" label="超时时间/小时" width="100"></el-table-column>
            <el-table-column fixed="right" min-width="80" label="操作">
              <template slot-scope="scope">
                <el-button @click="pushMsgNotice(scope.row)" type="primary" size="small"
                           icon="el-icon-circle-check">推送通知
                </el-button>

              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-link :href="hrefUrl" target="_blank" id="elLink"/>

  </div>
</template>

<script>
import moment from "moment";
import log from "echarts/src/scale/Log";
export default {
  name: "register",
  data() {
    return {
      logList: [],
      loading: true,
      roleId: localStorage.getItem('roleId'),
      blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
      dialog: false,
      storeId: null,
      hrefUrl: "",
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      days: [],
      form: {
        startTime: '',
        endTime: '',
        serviceNo: '',
        lblId: '',
        orderState: '',
        roleId: localStorage.getItem('roleId'),
        billNo: '',
        current: 1,
        size: 10
      },
      orderStateOptions: [{
        value:  '40',
        label: '已派单'
      },{
        value:  '50',
        label: '执行中'
      },{
        value:  '60',
        label: '开始服务'
      },{
        value:  '70',
        label: '服务结束'
      }],
      options: [{
        value:  '未打标签',
        label: '未打标签'
      },{
        value:  '材料',
        label: '材料'
      },{
        value:  '材料提现',
        label: '材料提现'
      },{
        value:  '长途',
        label: '长途'
      },{
        value:  '长途转账',
        label: '长途转账'
      },{
        value:  '地面无毛发未传',
        label: '地面无毛发未传'
      },{
        value:  '抖音实付',
        label: '抖音实付'
      },{
        value:  '返工',
        label: '返工'
      },{
        value:  '分时',
        label: '分时'
      },{
        value:  '公司',
        label: '公司'
      },{
        value:  '年卡',
        label: '年卡'
      },{
        value:  '其他',
        label: '其他'
      },{
        value:  '年卡',
        label: '年卡'
      },{
        value:  '推荐案例',
        label: '推荐案例'
      },{
        value:  '未加企微',
        label: '未加企微'
      },{
        value:  '现金',
        label: '现金'
      },{
        value:  '异常',
        label: '异常'
      },{
        value:  '异常,其他',
        label: '异常,其他'
      },{
        value:  '异常,其他',
        label: '异常,其他'
      },{
        value:  '优惠券异常',
        label: '优惠券异常'
      },{
        value:  '转账',
        label: '转账'
      },{
        value:  '转账分时',
        label: '转账分时'
      }],
      multipleSelection: [],
      storeOptions: [],
      dzPickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },

  created() {
    this.days.push(moment(new Date()).startOf("month").format("YYYY-MM-DD"))
    this.days.push(moment(new Date()).endOf("month").format("YYYY-MM-DD"))
    this.getData();
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    batchPushMsgNotice(){
      if(this.multipleSelection.length<=0){
        this.$message({message: '请勾选需要推送的订单信息', type: 'error'});
      }
      let arr = []
      for (let i = 0; i < this.multipleSelection.length; i++) {
            let obj = {
              carCode: this.multipleSelection[i].carCode,
              serviceNo: this.multipleSelection[i].serviceNo,
              billNo: this.multipleSelection[i].billNo
            }
            arr.push(obj)
      }
      this.$postData("batchPushMsgNotice", arr).then(res => {
          this.$message({message: res.data, type: 'warning'});
      })
    },
    exportTimeoutReturnOrder(){
      this.form.startTime = '';
      this.form.endTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      this.hrefUrl = "https://biapi.xiaoyujia.com/timeoutReturnOrder/exportTimeoutReturnOrder?startTime="+this.form.startTime+
          "&endTime="+this.form.endTime+"&serviceNo="+this.form.serviceNo+"&lblId="+this.form.lblId+
          "&orderState="+this.form.orderState
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    pushMsgNotice(obj){
      this.$postData("pushMsgNotice", obj).then(res => {
        if (res.code === 0) {
          this.$message({message: res.data, type: 'success'});
        } else {
          this.$message({message: res.msg, type: 'warning'});
        }
      })
    },
    // 页码大小
    onPageSizeChange(size) {
      this.form.size = size;
      this.getData();
    },
    onChange(index) {
      this.form.current = index;
      this.getData();
    },
    getData() {
      this.loading = true;
      this.form.startTime = '';
      this.form.endTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD")  + ' 00:00:00';
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD") + ' 23:59:59'
      }
      this.$getData("getTimeoutReturnOrder", this.form, {}).then(res => {
        this.loading = false
        if (res.code == 0) {
          this.logList = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    }
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
