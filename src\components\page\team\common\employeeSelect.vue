<template>
    <div>
        <div style="text-align: center">

            <el-transfer
                    style="text-align: left; display: inline-block"
                    v-model="selectList"
                    filterable
                    :titles="['可关联员工', '已关联员工']"
                    @change="handleChange"
                    :data="employeeList">
                <span slot-scope="{ option }">{{ option.label }}</span>
            </el-transfer>
            <el-divider></el-divider>
            <el-button type="primary" @click="chooseThisModel">确认选择</el-button>

        </div>
    </div>
</template>

<script>
    export default {
        props:{
            selectList: {
                type: Array,
                default: ()=>[]
            },
        },
        name: "employeeSelect",
        data() {
            return {
                loading:false,
                employeeList:[],

            };
        },
        created(){
            this.getEmployee()
        },
        methods: {
            chooseThisModel() {
                this.$emit('init-choose', this.selectList);
            },
            getEmployee(){
                this.loading=true
                this.$getData("getEmployeeSelectDto", {}).then(res => {
                    if (res.status == 200) {
                        this.employeeList =  [];
                        res.data.forEach((i, index) => {
                            this.employeeList.push({
                                label: i.realName+"/"+i.name,
                                key: i.id,
                            });

                        });
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                    this.loading=false
                })
            },
            handleChange(value, direction, movedKeys) {
                console.log(value);
                console.log( direction);
            }
        }
    }
</script>

<style scoped>

</style>
