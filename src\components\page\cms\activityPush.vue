<template>
    <div>
        <Drawer
                title="设置"
                v-model="value3"
                width="40%"
                :mask-closable="false"
                @on-close="chooseThisModel"
                :styles="styles"
        >
            <Form :model="formItem" :label-width="80" label-position="left">
                        <div class="searchDiv">
                            <FormItem label="跳转链接" prop="url">
                                <Input placeholder="跳转链接"
                                       disabled
                                       v-model="formItem.url">
                                </Input>
                            </FormItem>
                            <FormItem label="推送标题" prop="title" required>
                                <Input placeholder="推送标题"
                                       v-model="formItem.title">
                                </Input>
                                <span>不超过128个字节，超过会自动截断</span>
                            </FormItem>
<!--                            <FormItem label="工资异常推送者" prop="productName">-->
<!--                                <Input placeholder="工资异常推送者"-->
<!--                                       v-model="formItem.recipient">-->
<!--                                </Input>-->
<!--                                <span>仅支持工号填写</span>-->
<!--                            </FormItem>-->
                          <FormItem label="部门">
                            <el-select v-model="dom.party" value-key="id" filterable clearable @change="getPartyUser" placeholder="使用部门">
                              <el-option
                                  v-for="item in partys"
                                  :key="item.id"
                                  :label="item.name"
                                  :value="item.id">
                              </el-option>
                            </el-select>
                          </FormItem>
                          <FormItem label="推送人员"required>
                            <el-select v-model="dom.user" filterable multiple placeholder="使用人员" @change="changeSele">
                              <el-option v-for="item in users"
                                         :key="item.id"
                                         :label="item.name"
                                         :value="item.userid">
                              </el-option>
                            </el-select>
                          </FormItem>

                            <FormItem label="推送描述" required>
                                <Input v-model="formItem.description" type="textarea" :rows="3"
                                       placeholder="推送描述"/>
                                <span>不超过512个字节，超过会自动截断</span>
                            </FormItem>
                        </div>
            </Form>
            <div class="demo-drawer-footer">
                <Button style="margin-right: 8px" @click="chooseThisModel">取消</Button>
                <Button type="primary" @click="update">确定</Button>
            </div>
        </Drawer>
    </div>
</template>
<script>

    export default {
     props:['message'],
        data() {
            return {
                value3: true,
              //部门
              partys: [],
              //人员
              users: [],
              dom:{},
                styles: {
                    height: 'calc(100% - 55px)',
                    overflow: 'auto',
                    paddingBottom: '53px',
                    position: 'static'
                },
                formItem: {
                  title:null,
                  url:null,
                  description:null,
                  users:null,
                },
            }
        },
        created() {
          this.getParty();
         this.formItem.title = this.message.activityName;
          if ( this.message.activityType == 'zl'){
            this.formItem.url="https://activity.xiaoyujia.com/friendsHelp?activityCode="+this.message.activeCode
          } else if (item.activityType == 'bobing'){
            this.formItem.url="https://activity.xiaoyujia.com/boBing?activityCode="+this.message.activeCode
          }

        },
        methods: {
          //部门
          getParty() {
            this.$getData("getParty",).then(res => {
              if (res.status === 200) {
                this.partys = res.data
              }
            })
          },
          //人员
          getPartyUser(id) {
            this.partys.find((v) => {
              if (v.id === id) {
                this.dom.partyName = v.name
              }
            });
            this.$getData("getPartyUser", {id: id}).then(res => {
              if (res.status === 200) {
                this.users = res.data
              }
            })
          },
          changeSele(val) {
            this.dom.userName = [];
            val.forEach(e => {
              this.users.find((v) => {
                if (v.userid === e) {
                  this.dom.userName.push(v.name);
                }
              });
            })
          },
            update() {
              let dom = this.formItem;
              if (dom.title == '' || dom.title == null || dom.description == null || dom.description == ''){
                return this.$message.error("内容不能为空")
              }
              if (this.dom.user.length == 0){
                return this.$message.error("请选择推送人员")
              } else {
                this.formItem.users = this.dom.user;
              }
              console.info(JSON.stringify(this.formItem))
                this.$postData("activityPush", this.formItem).then(res => {
                        if (res.status === 200) {
                            this.$message({
                                type: 'success',
                                message: '推送成功!'
                            });
                            this.chooseThisModel()
                        } else {
                            this.$message.error("推送失败，" + res.msg);
                        }
                    }
                )
            },
            chooseThisModel() {
                this.value3 = false;
                this.$emit('init-choose', "");
            },
        }
    }
</script>
<style>
    .demo-drawer-footer {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        border-top: 1px solid #e8e8e8;
        padding: 10px 16px;
        text-align: left;
        background: #fff;
    }

    .searchDiv {
        width: 60%;
        margin: 20px auto;
        font-weight: bold;
        font-size: 17px !important;
    }
</style>