<template>
  <div style="padding:0 10% 12%;">
    <div class="a">员工活码统计</div>
    <div style="height: 100%; background: #FFFFFF;padding:0 10%;">
      <div style="padding: 20px 0 0;">
        <el-row type="flex" justify="space-between">
          <el-col :span="18">
            <div style="font-size: 18px;color: rgb(0, 0, 0);padding: 15px 0;">基本信息</div>
            <div style="font-size: 15px;padding: 10px 0;"><span style="color: rgb(144, 147, 153);padding:0  25px 0 0;">活码名称</span>
              <span style="color: rgb(0, 0, 0);">{{ qiyeChannel.name }}</span></div>
            <div style="font-size: 15px;padding: 10px 0;"><span style="color: rgb(144, 147, 153);padding:0  25px 0 0;">使用员工</span>
              <el-tag effect="plain" v-for="(item, index) in qiyeChannel.userName" :key="index" style="margin-right : 6px">
                <i class="el-icon-user-solid"></i> {{ item }}
              </el-tag>
            </div>
            <div style="font-size: 15px;padding: 10px 0;"><span style="color: rgb(144, 147, 153);padding:0  25px 0 0;">客户标签</span>
              <el-tag type="info" v-for="(item, index) in qiyeState.label" :key="index" style="margin-right : 6px">{{ item }}</el-tag>
            </div>
          </el-col>
          <el-col :span="5" style="text-align: right">
            <el-link style="font-size: 15px; color: rgb(44, 77, 164);">查看详情<i class="el-icon-arrow-right"></i></el-link>
            <div style="margin : 15px 21px 0 0;">
              <el-image :src="qiyeChannel.qr_code" style="width: 120px; height: 120px;"></el-image>
              <div style="margin : 0 10px 0 0;">
                <el-button type="text" @click="downloadUrl(qiyeChannel)">下载</el-button>
                <el-divider direction="vertical"></el-divider>
                <el-button type="text" class="tag-read" :data-clipboard-text="qiyeChannel.qr_code" @click="copyUrl()">
                  复制链接
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-divider></el-divider>
      <div style="padding: 20px 0;font-size: 18px;">
        <el-row type="flex" justify="space-between">
          <el-col :span="6" style="color: rgb(0, 0, 0);">总览</el-col>
          <el-col :span="6" style="text-align: right">
            <el-link style="font-size: 17px; color: rgb(44, 77, 164);">统计说明<i class="el-icon-arrow-right"></i></el-link>
          </el-col>
        </el-row>
      </div>
      <el-row type="flex" justify="center">
        <el-col :span="8">
          <div class="grid">
            <div>新增客户数</div>
            <div class="grid-a">{{ add }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid">
            流失客户数
            <div class="grid-a">{{ drain }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid">
            净增客户数
            <div class="grid-a">{{ add - drain }}</div>
          </div>
        </el-col>
      </el-row>
      <div style="padding: 30px 0 0 0;font-size: 18px;color: rgb(0, 0, 0);">
        趋势
      </div>
      <el-divider></el-divider>
      <div style="font-size: 18px;color: #909399;">
        统计日期
        <el-radio-group v-model="radio" style="padding: 0 30px ;" @change="statisticalDate()">
          <el-radio-button label="0">今日</el-radio-button>
          <el-radio-button label="1">7日</el-radio-button>
          <el-radio-button label="2">30日</el-radio-button>
        </el-radio-group>
        自定义
        <el-date-picker v-model="value" type="daterange" @change="custom()" value-format="yyyy/MM/dd"
                        :picker-options="pickerOptions"
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </div>
      <div style="margin:30px 0 ">
        <el-row>
          <el-col :span="6">
            <el-card :body-style="{ padding: '0px' }" @click.native="newlyAdded()" shadow="hover">
              <div class="grid-b">
                <div>新增客户数</div>
                <div class="grid-a">{{ addNum }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card :body-style="{ padding: '0px' }" @click.native="wastage()" shadow="hover">
              <div class="grid-c">
                <div>流失客户数</div>
                <div class="grid-a">{{ drainNum }}</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <div id="main" style="height: 350px;"></div>

      <div style="font-size: 18px;margin:12px 0 ">
        <el-row type="flex" justify="space-between">
          <el-col :span="6" style="color: rgb(0, 0, 0);">活码明细</el-col>
          <el-col :span="10" style="text-align: right"></el-col>
          统计类型
          <el-radio-group v-model="radio1">
            <el-radio-button label="按员工">按员工</el-radio-button>
            <el-radio-button label="按日期">按日期</el-radio-button>
          </el-radio-group>
        </el-row>
      </div>
      <hr style='background-color:rgb(220, 223, 230); height:1px; border:none;'/>
      <div style="margin:20px 0" v-if="radio1==='按员工'">
        <span style=" font-size: 18px; color:#909399;">员工</span>
        <el-select v-model="qiyeJournal.userName" clearable filterable placeholder="请选择" size="large"
                   @change="query()" style="margin: 0 15px">
          <el-option v-for="(item,index) in qiyeChannel.userName" :key="index" :value="item">
          </el-option>
        </el-select>
        <el-button style="margin: 0 15px" :loading="loadingExcel" @click="download()">导出列表</el-button>
        <br/>
        <br/>
        <el-table :data="list"
                  style="width: 100%;padding:0  0 12% 0"
                  :header-cell-style="{background:' rgb(236, 238, 245)',textAlign: 'center'}"
                  :cell-style="{ textAlign: 'center' }"
                  :default-sort="{prop: 'date', order: 'descending'}">
          <el-table-column
              prop="name"
              label="员工名称"
              width="150">
          </el-table-column>
          <el-table-column
              prop="added"
              label="新增客户数"
              sortable
              width="180">
          </el-table-column>
          <el-table-column
              prop="drain"
              label="流失客户数"
              sortable>
          </el-table-column>
          <el-table-column
              prop="added"
              label="净增客户数"
              sortable>
            <template slot-scope="scope">
              {{ scope.row.added - scope.row.drain }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div style="margin:20px 0" v-if="radio1==='按日期'">
        <span style=" font-size: 18px; color:#909399;margin: 0 15px 0 0">日期</span>
        <el-date-picker v-model="value2" type="daterange" value-format="yyyy/MM/dd" :picker-options="pickerOptions"
                        @change="byDate()" size="large" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期">
        </el-date-picker>
        <!--        <el-button style="margin: 0 15px" :loading="loadingExcel" @click="download()">导出列表</el-button>-->
        <br/>
        <br/>
        <el-table :data="list2"
                  style="width: 100%;padding:0  0 12% 0"
                  :header-cell-style="{background:' rgb(236, 238, 245)',textAlign: 'center'}"
                  :cell-style="{ textAlign: 'center' }"
                  :default-sort="{prop: 'date', order: 'descending'}">
          <el-table-column
              prop="createTime"
              label="时间"
              width="150">
          </el-table-column>
          <el-table-column
              prop="sumTotal"
              label="客户数"
              sortable
              width="180">
          </el-table-column>
          <el-table-column
              prop="type"
              label="类型"
              sortable>
            <template slot-scope="scope">
              <span v-if=" scope.row.type===1">
                           新增
                        </span>
              <span v-if=" scope.row.type===5">
                           流失
                        </span>

            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

  </div>
</template>

<script>
//引入组件
import Clipboard from "clipboard";
import * as echarts from 'echarts';

export default {
  name: "statistics",
  data() {
    return {
      id: this.$route.query.id,
      qiyeStateId: this.$route.query.qiyeStateId,
      qiyeChannel: {
        name: null,
        userName: [],
        num: null,
        qr_code: null,
      },
      qiyeJournalDto: {
        qiyeStateId: this.$route.query.qiyeStateId,
        startTime: null,
        endTime: null,
      },
      qiyeState: {
        label: [],
      },
      qiyeJournal: {
        qiyeStateId: this.$route.query.qiyeStateId,
        startTime: null,
        endTime: null,
        userName: null,
      },
      //日期禁用
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      add: null,
      drain: null,
      addNum: null,
      drainNum: null,
      loadingExcel: false,
      name1: "新增客户数",
      name2: "#409EFF",
      list: [],
      list2: [],
      // list3: [],
      radio: '0',
      radio1: "按员工",
      value: [],
      value2: [],
      value3: [['00:00'], ['01:00'], ['02:00'], ['03:00'], ['04:00'], ['05:00'], ['06:00'], ['07:00'], ['08:00'], ['09:00'], ['10:00'], ['11:00'],
        ['12:00'], ['13:00'], ['14:00'], ['15:00'], ['16:00'], ['17:00'], ['18:00'], ['19:00'], ['20:00'], ['21:00'], ['22:00'], ['23:00'],],
      value4: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      value5: [],
    };
  },
  created() {
    if (this.id !== null && this.id !== '' && this.id !== undefined) {
      this.getQiyeChannel(this.id)
      this.getQiyeState(this.id)
    }
    this.totalCustomer(this.qiyeStateId)
    this.statisticalDate()
    this.staffAddCustomer()
    this.getQywxCustomerInfoGroupByTimeAndType()
  },

  watch: {
    $route: {
      handler: function (val, oldVal) {
        if (val.query.id !== null && val.query.id !== undefined && val.query.id !== '') {
          this.id = val.query.id
          this.qiyeStateId = val.query.qiyeStateId
          this.radio='0'
          this.getQiyeChannel(this.id)
          this.getQiyeState(this.id)
          this.qiyeJournal.qiyeStateId = this.qiyeStateId
          this.qiyeJournalDto.qiyeStateId = this.qiyeStateId
          this.totalCustomer(val.query.qiyeStateId)
          this.statisticalDate()
          this.staffAddCustomer()
          this.getQywxCustomerInfoGroupByTimeAndType()
        }
      }
    }
  },
  methods: {
    newlyAdded() {
      this.name1 = "新增客户数"
      this.name2 = "#409EFF"
      if(this.radio1==="0"){
        this.hourStatistics()
      }else {
        this.dayStatistics()
      }
    },
    wastage() {
      this.name1 = "流失客户数"
      this.name2 = null
      if(this.radio1==="0"){
        this.drainHourStatistics()
      }else {
        this.drainDayStatistics()
      }
    },
    //基础信息
    getQiyeChannel(id) {
      this.$getData("getQiyeChannel", {id: id}).then(res => {
        if (res.status === 200) {
          this.qiyeChannel = res.data
          this.qiyeChannel.user = (res.data.user || "").split(',')
          this.qiyeChannel.userName = (res.data.userName || "").split(',')
          console.log(this.qiyeChannel.userName)
        }
      })
    },
    //基础信息
    getQiyeState(id) {
      this.$getData("getQiyeState", {id: id}).then(res => {
        if (res.status === 200) {
          this.qiyeState = res.data
          this.qiyeState.label = (res.data.label || "").split(',')
        }
      })
    },
    //总的新增、流失 客户数
    totalCustomer(qiyeStateId) {
      this.$getData("totalCustomer", {qiyeStateId: qiyeStateId}).then(res => {
        if (res.status === 200) {
          this.add = res.data.add
          this.drain = res.data.drain
        }
      })
    },
    //表格
    staffAddCustomer() {
      this.$postData("staffAddCustomer", this.qiyeJournal).then(res => {
        if (res.status === 200) {
          this.list = res.data;
        }
      })
    },
    //根据时间查看新增、流失 客户数
    timeCustomer() {
      this.$postData("timeCustomer", this.qiyeJournal).then(res => {
        if (res.status === 200) {
          this.addNum = res.data.add
          this.drainNum = res.data.drain
        }
      })
    },
    //根据时间查看新增、流失 客户数
    statisticalDate() {
      this.qiyeJournal.endTime = null
      this.qiyeJournal.startTime = null
      this.value = null
      if (this.radio === '0') {
        let yy = new Date().getFullYear();
        let mm = new Date().getMonth() + 1;
        let dd = new Date().getDate() + 1;
        this.qiyeJournal.endTime = yy + '/' + mm + '/' + dd;
        this.qiyeJournal.startTime = yy + '/' + mm + '/' + (dd - 1);
        this.value = [this.qiyeJournal.startTime, this.qiyeJournal.endTime]
        this.timeCustomer()
        if(this.name1==="新增客户数"){
          this.hourStatistics()
        }else {
          this.drainHourStatistics()
        }
      } else if (this.radio === '1') {
        let yy = new Date().getFullYear();
        let mm = new Date().getMonth() + 1;
        let dd = new Date().getDate() + 1;
        this.qiyeJournal.endTime = yy + '/' + mm + '/' + dd;
        let myDate = new Date()
        myDate.setDate(myDate.getDate() - 6)
        this.qiyeJournal.startTime = myDate.getFullYear() + '/' + (myDate.getMonth() + 1) + '/' + myDate.getDate()
        this.value = [this.qiyeJournal.startTime, this.qiyeJournal.endTime]
        this.timeCustomer()
        if(this.name1==="新增客户数"){
          this.dayStatistics()
        }else {
          this.drainDayStatistics()
        }
        this.getdiffdate(this.qiyeJournal.startTime, this.qiyeJournal.endTime)
      } else if (this.radio === '2') {
        let yy = new Date().getFullYear();
        let mm = new Date().getMonth() + 1;
        let dd = new Date().getDate() + 1;
        this.qiyeJournal.endTime = yy + '/' + mm + '/' + dd;
        let myDate = new Date()
        myDate.setDate(myDate.getDate() - 29)
        this.qiyeJournal.startTime = myDate.getFullYear() + '/' + (myDate.getMonth() + 1) + '/' + myDate.getDate()
        this.value = [this.qiyeJournal.startTime, this.qiyeJournal.endTime]
        this.timeCustomer()
        if(this.name1==="新增客户数"){
          this.dayStatistics()
        }else {
          this.drainDayStatistics()
        }
        this.getdiffdate(this.qiyeJournal.startTime, this.qiyeJournal.endTime)
      }
    },
    //自定义时间选择
    custom() {
      this.radio = null
      if (this.value != null && this.value.length > 0) {
        this.qiyeJournal.startTime = this.value[0];
        this.qiyeJournal.endTime = this.value[1];
      }
      this.getdiffdate(this.qiyeJournal.startTime, this.qiyeJournal.endTime)
      this.timeCustomer()
      if(this.name1==="新增客户数"){
        this.dayStatistics()
      }else {
        this.drainDayStatistics()
      }
    },
    getdiffdate(stime, etime) {
      //初始化日期列表，数组
      var diffdate = [];
      var i = 0;
      //开始日期小于等于结束日期,并循环
      while (stime <= etime) {
        diffdate[i] = stime;
        //获取开始日期时间戳
        var stime_ts = new Date(stime).getTime();
        //增加一天时间戳后的日期
        var next_date = stime_ts + (24 * 60 * 60 * 1000);
        //拼接年月日，这里的月份会返回（0-11），所以要+1
        var next_dates_y = new Date(next_date).getFullYear() + '/';
        var next_dates_m = (new Date(next_date).getMonth() + 1 < 10) ? '0' + (new Date(next_date).getMonth() + 1) + '/' : (new Date(next_date).getMonth() + 1) + '/';
        var next_dates_d = (new Date(next_date).getDate() < 10) ? '0' + new Date(next_date).getDate() : new Date(next_date).getDate();
        stime = next_dates_y + next_dates_m + next_dates_d;
        //增加数组key
        i++;
      }
      this.value5=diffdate
    },
    //查询
    query() {
      this.staffAddCustomer()
    },
    byDate() {
      this.qiyeJournalDto.startTime = this.value2[0]
      this.qiyeJournalDto.endTime = this.value2[1]
      this.getQywxCustomerInfoGroupByTimeAndType()
    },
    //复制链接
    copyUrl() {
      var clipboard = new Clipboard('.tag-read')
      clipboard.on('success', e => {
        this.$message({
          message: '复制成功', type: 'success'
        });
        // 释放内存
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        this.$message.error('该浏览器不支持自动复制');
        // 不支持复制
        // 释放内存
        clipboard.destroy()
      })
    },
    //下载二维码
    downloadUrl(qiyeChannel) {
      var image = new Image();
      image.setAttribute("crossOrigin", "anonymous");
      var _this = this;
      image.onload = function () {
        var canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        var context = canvas.getContext("2d");
        context.drawImage(image, 0, 0, image.width, image.height);
        var url = canvas.toDataURL("image/png"); //得到图片的base64编码数据
        var a = document.createElement("a"); // 生成一个a元素
        var event = new MouseEvent("click"); // 创建一个单击事件
        a.download = _this.projectName || qiyeChannel.name; // 设置图片名称
        a.href = url; // 将生成的URL设置为a.href属性
        a.dispatchEvent(event); // 触发a的单击事件
      };
      image.src = qiyeChannel.qr_code;
    },
    //折线图
    drawLine() {
// 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById('main'));
// 绘制图表
      myChart.setOption({
        xAxis: {
          data: this.value3,
          axisPointer: {
            show: true,
            label: {
              show: true,
            }
          }
        },
        yAxis: {},
        toolbox: {
          right: 10,
          feature: {
            saveAsImage: {}
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        series: {
          name: this.name1,
          data: this.value4,
          type: 'line',
          lineStyle: {
            color: this.name2,
          },
          label: {
            show: false,
            position: 'bottom',
            textStyle: {
              fontSize: 20
            }
          },
        },
      });
    },
    // todayCustomer() {
    //   this.$postData("todayCustomer", this.qiyeJournal).then(res => {
    //     if (res.status === 200) {
    //       this.list3 = res.data;
    //     }
    //   })
    // },
    hourStatistics() {
      let _this = this
      this.value4 = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
      this.value3=[['00:00'], ['01:00'], ['02:00'], ['03:00'], ['04:00'], ['05:00'], ['06:00'], ['07:00'], ['08:00'], ['09:00'], ['10:00'], ['11:00'],
        ['12:00'], ['13:00'], ['14:00'], ['15:00'], ['16:00'], ['17:00'], ['18:00'], ['19:00'], ['20:00'], ['21:00'], ['22:00'], ['23:00'],]
      this.$postData("hourStatistics", this.qiyeJournal).then(res => {
        if (res.status === 200) {
          let lis = res.data;
          if (res.data.length > 0) {
            lis.forEach(function (e) {
              _this.value4.splice(e.timehour, 1, e.number)
            })
            }
          this.drawLine()
        }
      })
    },
    drainHourStatistics() {
      let _this = this
      this.value4 = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
      this.value3=[['00:00'], ['01:00'], ['02:00'], ['03:00'], ['04:00'], ['05:00'], ['06:00'], ['07:00'], ['08:00'], ['09:00'], ['10:00'], ['11:00'],
        ['12:00'], ['13:00'], ['14:00'], ['15:00'], ['16:00'], ['17:00'], ['18:00'], ['19:00'], ['20:00'], ['21:00'], ['22:00'], ['23:00'],]
      this.$postData("drainHourStatistics", this.qiyeJournal).then(res => {
        if (res.status === 200) {
          let lis = res.data;
          if (res.data.length > 0) {
            lis.forEach(function (e) {
              _this.value4.splice(e.timehour, 1, e.number)
            })
          }
          this.drawLine()
        }
      })
    },
    dayStatistics() {
      this.$postData("dayStatistics", this.qiyeJournal).then(res => {
        if (res.status === 200) {
          let lis = res.data;
          let a=[]
          let b=[]
          if (res.data.length > 0) {
            lis.forEach(function (e) {
              b.push(e.num)
              a.push(e.date)
            })
            this.value3=a
            this.value4=b
          }else {
            this.value3=this.value5
          }
          this.drawLine()
        }
      })
    },
    drainDayStatistics() {
      this.$postData("drainDayStatistics", this.qiyeJournal).then(res => {
        if (res.status === 200) {
          let lis = res.data;
          let a=[]
          let b=[]
          if (res.data.length > 0) {
              lis.forEach(function (e) {
                b.push(e.num)
                a.push(e.date)
              })
              this.value3=a
              this.value4=b
          }else {
            this.value3=this.value5
          }
          this.drawLine()
        }
      })
    },
    //导出
    download() {
      this.loadingExcel = true;
      if (this.list.length > 0) {
        this.$postData("downloadQiyeJournal", this.qiyeJournal, {responseType: "arraybuffer"}).then(res => {
          this.loadingExcel = false;
          let _this = this.qiyeChannel.name + "-" + this.radio1;
          this.blobExport({
            tablename: _this,
            res: res
          });
        })
      } else {
        this.loadingExcel = false;
        this.$message.error('请继续加油，添加客户微信');
      }
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
    getQywxCustomerInfoGroupByTimeAndType() {
      this.$postData("getQywxCustomerInfoGroupByTimeAndType", this.qiyeJournalDto).then(res => {
        if (res.status === 200) {
          this.list2 = res.data;
        }
      })
    }


  },
}
</script>

<style scoped>
.a {
  border: 1px solid #eee;
  color: rgb(48, 49, 51);
  background: rgb(236, 238, 245);
  font-size: 18px;
  padding: 1%;
  text-align: center;
}


.grid {
  border-radius: 4px;
  color: #909399;
  font-size: 15px;
  background: rgb(236, 238, 245);
  text-align: center;
  padding: 8%;
}

.grid-a {
  color: rgb(0, 0, 0);
  font-size: 30px;
}

.grid-b {
  font-size: 16px;
  background: #F5F7FA;
  padding: 7%;
}

.grid-c {
  border-radius: 4px;
  font-size: 16px;
  padding: 7%;
}
</style>
