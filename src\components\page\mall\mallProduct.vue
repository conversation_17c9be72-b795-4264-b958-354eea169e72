<template>
<div class="app-container">
  <el-card>
    <div class="filter-container">
      <el-input
        v-model="searchForm.productName"
        placeholder="产品名称"
        style="width: 200px;"
        @keyup.enter.native="handleSearch"
      />
      <el-select
        v-model="searchForm.state"
        placeholder="状态"
        clearable
        style="width: 120px;margin-left:10px"
      >
        <el-option label="上架" :value="1" />
        <el-option label="下架" :value="2" />
      </el-select>

      <el-select
          v-model="searchForm.categoryId"
          placeholder="产品分类"
          clearable
          style="width: 180px;margin-right:10px"
      >
        <el-option
            v-for="item in categoryOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
        />
      </el-select>
      <el-button type="primary" @click="handleSearch" style="margin-left:10px">搜索</el-button>
      <el-button type="success" @click="addShop" style="margin-left:10px">新增商品</el-button>
    </div>

    <el-table
      :data="tableData"
      border
      style="width: 100%;margin-top:20px;"
    >
      <el-table-column prop="productId" label="ID" />
      <el-table-column prop="productName" label="产品名称" />
      <el-table-column prop="categoryName" label="分类" />
      <el-table-column label="主图">
        <template slot-scope="scope">
          <el-image
            :src="scope.row.mainImg"
            style="width:80px;height:80px"
            :preview-src-list="[scope.row.mainImg]"
          />
        </template>
      </el-table-column>
      <el-table-column prop="priceRange" label="价格范围" />
      <el-table-column label="状态">
        <template slot-scope="scope">
          {{ scope.row.state | statusFilter }}
        </template>
      </el-table-column>
      <el-table-column prop="volume" label="销量" />
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.state === 2"
            type="success"
            size="mini"
            @click="handleStateChange(scope.row, 1)"
          >
            上架
          </el-button>
          <el-button
            v-if="scope.row.state === 1"
            type="danger"
            size="mini"
            @click="handleStateChange(scope.row, 2)"
          >
            下架
          </el-button>
          <el-button
            type="primary"
            size="mini"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      style="margin-top:20px;"
      @current-change="handleCurrentChange"
      :current-page.sync="pagination.current"
      :page-size="pagination.size"
      layout="total, prev, pager, next"
      :total="pagination.total"
    />
  </el-card>


  <el-dialog title="编辑" :visible.sync="showdialog" append-to-body width="74%" top="10vh" @close="handleDialogClose">
    <el-form ref="postRef" :model="form" :rules="rules"  label-width="80px">
      <div class="addCom common" v-show="activity === 0">
        <el-card class="leftCom">
          <el-form-item label-width="0">
            <WangeDitor
              :key="editorKey"
              style="height: 500px"
              ref="editorVue"
              @changeData="hChangeData"
              :content="form.product.productText" />
          </el-form-item>
        </el-card>

        <el-card class="rightCom">
          <el-form-item label="商品名称" prop="product.productName" required>
            <el-input v-model="form.product.productName" :maxlength="50" show-word-limit />
          </el-form-item>
          <el-form-item label="卖点简介" prop="product.productBrief" required>
            <el-input v-model="form.product.productBrief" :maxlength="50" show-word-limit />
          </el-form-item>
          <el-form-item label="商品分类" prop="product.cateGoryId" required>
            <el-select v-model="form.product.cateGoryId" placeholder="请选择商品分类" clearable style="width: 200px">
              <el-option v-for="item in categoryOptions" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否上架" label-width="120px" prop="product.state" required>
            <el-radio v-model="form.product.state" :label="1">是</el-radio>
            <el-radio v-model="form.product.state" :label="2">否</el-radio>
          </el-form-item>
          <el-form-item label="是否需要物流" label-width="120px" prop="product.logistics" required>
            <el-radio v-model="form.product.logistics" :label="1">是</el-radio>
            <el-radio v-model="form.product.logistics" :label="0">否</el-radio>
          </el-form-item>
          <el-form-item label="是否首页推荐" label-width="120px" prop="product.logistics" required>
            <el-radio v-model="form.product.hot" :label="1">是</el-radio>
            <el-radio v-model="form.product.hot" :label="0">否</el-radio>
          </el-form-item>
        </el-card>
      </div>

      <el-card class="style-information-component" v-show="activity === 1">
        <el-form-item label="商品图片" prop="product.imgs" />
        <div class="upload-wrap">
          <ImageUpload :limit="5" :model-value="form.product.imgs" @input="handleModelValueUpdate" />
        </div>
        <el-button class="add-attr-btn" type="primary" @click="addSkuAttrList" v-if="!form.product.productId">添加规格</el-button>
        <el-table :data="form.skus" style="width: 100%" :header-cell-style="{ background: '#EEF3FF', color: '#333333' }" v-if="!form.product.productId">
          <el-table-column label="sku配图">
            <template slot-scope="scope">
              <ImageUpload :isShowTip="false" :limit="1" :model-value="scope.row.skuImage"
                           @input="val => handleSkuImageModelValueUpdate(val, scope.$index)" />
            </template>
          </el-table-column>
          <el-table-column label="规格">
            <template slot-scope="scope">
              <el-input v-model="scope.row.skuName" />
            </template>
          </el-table-column>
          <el-table-column label="售价">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.price" :controls="false" :max="999999999" :min="0" :precision="2" :step="0.01" />
            </template>
          </el-table-column>
          <el-table-column label="原价">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.originalPrice" :controls="false" :max="999999999" :min="0" :precision="2" :step="0.01" />
            </template>
          </el-table-column>
          <el-table-column label="总库存数">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.totalNum" :controls="false" :max="999999999" :min="0" :step="1" />
            </template>
          </el-table-column>
          <el-table-column label="当前库存数">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.stockNum" :controls="false" :max="999999999" :min="0" :step="1" />
            </template>
          </el-table-column>
          <el-table-column label="起购数量">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.min" :controls="false" :max="999999999" :min="0" :step="1" />
            </template>
          </el-table-column>
          <el-table-column label="单次限购数量">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.max" :controls="false" :max="999999999" :min="0" :step="1" />
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="danger" @click="removeSkuAttr(scope.$index)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-form>
    <template slot="footer">
      <div class="dialog-footer">
        <el-button v-if="activity === 0" type="primary" @click="activity = 1">下一步</el-button>
        <el-button v-if="activity === 1" type="primary" @click="activity = 0">上一步</el-button>
        <el-button v-if="activity === 1" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</div>
</template>

<script>

import WangeDitor from "@/components/common/WangeDitor.vue";
import ImageUpload from "@/components/common/ImageUpload.vue";

export default {
  components: {WangeDitor,ImageUpload},
  filters: {
    statusFilter(state) {
      const statusMap = {
        1: '上架',
        2: '下架'
      }
      return statusMap[state] || '未知'
    }
  },

  data() {
    return {
      activity: 0, // 0:基础信息, 1:SKU信息
      showdialog:false,
      editorKey: 0, // 用于强制重新渲染编辑器
      form: {
        product: {
          productName: '',
          productBrief: '',
          productType: '',
          cateGoryId: '',
          storeId: '',
          state: 1,
          logistics: 1,
          imgs: [],
          productText: ''
        },
        skus: []
      },
      categoryOptions:[],
      tableData: [],
      searchForm: {
        productName: '',
        state: ''
      },
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      rules: {
        'product.productName': [{
          required: true,
          message: '请输入商品名称',
          trigger: 'blur'
        }, ],
        'product.productBrief': [{
          required: true,
          message: '热点简介',
          trigger: 'blur'
        }, ],
        'product.cateGoryId': [{
          required: true,
          message: '请选择商品分类',
          trigger: 'change'
        }, ],
        'product.logistics': [{
          required: true,
          message: '请选择是否需要物流',
          trigger: 'change'
        }, ],
        'product.state': [{
          required: true,
          message: '请选择是否上架',
          trigger: 'change'
        }, ],
        'product.imgs': [{
          required: true,
          type: 'array',
          message: '图片不能为空',
          trigger: 'blur'
        },
          {
            min: 1,
            type: 'array',
            message: '至少需要上传一张图片',
            trigger: 'blur'
          }
        ],
      }
    }
  },

  mounted() {
    this.getList()
    this.loadCategories()
  },

  methods: {
    addShop() {
      // 重置表单数据
      this.form = {
        product: {
          productName: '',
          productBrief: '',
          productType: '',
          cateGoryId: '',
          storeId: '',
          state: 1,
          logistics: 1,
          imgs: [],
          productText: ''
        },
        skus: []
      };
      this.editorKey++; // 强制重新渲染编辑器
      this.showdialog = true;
      // 确保编辑器内容被正确重置
      this.$nextTick(() => {
        if (this.$refs.editorVue) {
          this.$refs.editorVue.setContent('');
        }
      });
    },
    loadCategories() {
      this.$postData('mallCategoryList',{name:null,state:null}).then(res => {
        this.categoryOptions = res.data
          }).catch(() => {
            this.$message.error('分类加载失败')
          })
    },
    getList() {
      const params = {
        ...this.searchForm,
        ...this.pagination
      }
      this.$postData("mallProductList",params).then(res => {
        if (res.data) {
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        }
      })
    },

    handleSearch() {
      this.pagination.current = 1
      this.getList()
    },

    handleCurrentChange(val) {
      this.pagination.current = val
      this.getList()
    },
    handleStateChange(row, newState) {
      this.$confirm(`确定要${newState === 1 ? '上架' : '下架'}该商品吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$getData("updateMallStatus",{ productId: row.productId,
          state: newState}).then(res => {
          if (res.status === 200) {
            this.$message.success('状态更新成功')
            this.getList()
          }
        })
      })
    },
    handleEdit(row) {
      this.cancel();
      this.$getData("mallProductDetail",{ id: row.productId}).then(res => {
        if (res.status === 200) {
          this.form = res.data;
          // 确保productText字段存在
          if (!this.form.product.productText) {
            this.form.product.productText = '';
          }
          this.editorKey++; // 强制重新渲染编辑器
          this.showdialog = true;
          // 确保编辑器内容被正确设置
          this.$nextTick(() => {
            if (this.$refs.editorVue) {
              this.$refs.editorVue.setContent(this.form.product.productText || '');
            }
          });
        }
      })
    },
    addSkuAttrList() {
      this.form.skus.push({
        skuImage: '',
        skuName: '',
        price: 0,
        originalPrice: 0,
        totalNum: 0,
        stockNum: 0,
        min: 1,
        max: 999
      });
    },
    removeSkuAttr(index) {
      this.form.skus.splice(index, 1);
    },
    handleModelValueUpdate(e) {
      if (!e) {
        this.form.product.imgs = [];
      } else {
        this.form.product.imgs = e.split(',')
      }
    },
    handleSkuImageModelValueUpdate(value, index) {
      this.$set(this.form.skus, index, {
        ...this.form.skus[index],
        skuImage: value
      });
    },
    submitForm() {
      this.$refs.postRef.validate(valid => {
        if (valid) {
          if (this.form.product.productId) {
            console.log('更新')
            this.$postData("updateMall",this.form).then(res => {
              if (res.status === 200) {
                this.showdialog = false;
                this.$message.success('成功');
                this.getList();
                this.cancel();
              } else {
                this.$message.error('失败');
              }
            })

          } else {
            console.log('新增')
            this.$postData("insertMall",this.form).then(res => {
              if (res.status === 200) {
                this.showdialog = false;
                this.$message.success('成功');
                this.getList();
                this.cancel();
              } else {
                this.$message.error('失败');
              }
            })
          }

        } else {
          this.$message.error('请填写完整的表单信息');
          return false;
        }
      });
    },
    cancel() {
      this.activity = 0;
      this.editorKey++; // 强制重新渲染编辑器
      // 重置表单数据
      this.form = {
        product: {
          productName: '',
          productBrief: '',
          productType: '',
          cateGoryId: '',
          storeId: '',
          state: 1,
          logistics: 1,
          imgs: [],
          productText: ''
        },
        skus: []
      };
      this.$nextTick(() => {
        this.$refs.postRef && this.$refs.postRef.resetFields();
      });
      this.showdialog = false;
    },
    handleDialogClose() {
      // 对话框关闭时强制销毁编辑器
      this.editorKey++;
      this.activity = 0;
      // 重置表单数据
      this.form = {
        product: {
          productName: '',
          productBrief: '',
          productType: '',
          cateGoryId: '',
          storeId: '',
          state: 1,
          logistics: 1,
          imgs: [],
          productText: ''
        },
        skus: []
      };
      this.$nextTick(() => {
        this.$refs.postRef && this.$refs.postRef.resetFields();
      });
    },
    hChangeData(editDataHtml) {
      this.form.product.productText = editDataHtml;
    }
  }
}
</script>

<style scoped>
.filter-container {
  display: flex;
  gap: 10px;
}
.app-container {
  padding: 20px;
}


/* 基础样式 */
.el-form-item__content {
  margin-left: 0px;
}

.w-e-text-container [data-slate-editor] img {
  max-height: 400px;
}

.common {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 表单容器 */
.addCom {
  justify-content: space-around;
  align-items: unset;
}
.addCom > div {
  width: 100%;
}
.addCom .el-form {
  display: flex;
}
.addCom .leftCom {
  width: 65%;
  background: #ffffff;
}
.addCom .rightCom {
  flex: 1;
  background: #ffffff;
  margin-left: 20px;
}

/* 信息组件 */
.style-information-component {
  width: 100%;
  min-height: 300px;
}
.style-information-component .el-form-item {
  margin-bottom: 10px;
}
.style-information-component .attr-value-list {
  display: flex;
  flex-wrap: wrap;
}
.style-information-component .attr-value-list .main-diagram {
  width: 180px;
}
.style-information-component .attr-value-list .main-diagram .span-wrap {
  position: relative;
  display: inline-block;
  margin-top: 10px;
}
.style-information-component .attr-value-list .main-diagram .span-wrap .attr-actions {
  line-height: 100px;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  text-align: center;
  color: #fff;
  opacity: 0;
  font-size: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-transition: opacity 0.3s;
  transition: opacity 0.3s;
  z-index: 1;
}
.style-information-component .attr-value-list .main-diagram .span-wrap .attr-actions:hover {
  opacity: 1;
}
.style-information-component .attr-value-list .main-diagram .span-wrap .attr-actions:hover .attr-preview {
  display: inline-block;
}
.style-information-component .attr-value-list .main-diagram .span-wrap .attr-actions:hover i {
  color: #fff;
  font-size: 20px;
}
.style-information-component .attr-value-list .main-diagram .span-wrap .attr-preview {
  display: none;
  cursor: pointer;
  font-size: 20px;
  color: #fff;
}
.style-information-component .attr-value-list .main-diagram .span-wrap .attr-delete {
  margin-left: 15px;
  color: #fff;
}
.style-information-component .attr-value-list .main-diagram .attr-value-img {
  width: 100%;
  height: 100%;
}
.style-information-component .attr-value-list .main-diagram .attr-value-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 工具类 */
.m-8 {
  margin-right: 8px;
}

/* 上传按钮 */
.upload-btn {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 110px;
  cursor: pointer;
  border: 2px dashed #2e60f8;
  border-radius: 2px;
  background-color: #f8f9fb;
  text-align: center;
  font-size: 20px;
  color: #2e60f8;
}
.upload-btn i {
  color: #2e60f8;
  font-size: 20px;
}
.upload-btn .upload-title {
  margin-left: 10px;
  font-size: 14px;
}

/* 上传容器 */
.upload-wrap {
  margin-bottom: 25px;
}
.upload-wrap .el-upload-list__item {
  transition: none !important;
}
.upload-wrap .el-upload,
.upload-wrap .el-upload-list__item {
  width: 100px;
  height: 100px;
  line-height: 100px;
}
.upload-wrap .el-progress,
.upload-wrap .el-progress-circle {
  width: 80px !important;
  height: 80px !important;
}

/* 属性列表 */
.sku-attr-list .el-input {
  width: 180px;
}

/* 单一样式 */
.single-style .el-input {
  max-width: 180px;
}

/* 按钮 */
.add-attr-btn {
  margin-bottom: 25px;
}

/* 图片弹窗 */
.check-image-dialog .el-dialog {
  margin-top: 25px;
}
.check-image-dialog .el-dialog .el-dialog__body img {
  max-width: 100%;
  max-height: 100%;
  height: 500px;
  object-fit: contain;
}

/* 删除图标 */
.delImg:hover {
  color: #ffffff;
  background: #2e60f8;
  cursor: pointer;
}
</style>
