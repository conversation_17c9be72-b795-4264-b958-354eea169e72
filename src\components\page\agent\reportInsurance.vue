<template>
  <div class="container">
    <div class="handle-box">
      <el-form :model="dom" :inline="true" ref="dom" class="demo-form-inline">
        <el-form-item label="订单号" prop="billNo">
          <el-input v-model="dom.billNo" placeholder="订单号" clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="合同号" prop="contractNo">
          <el-input v-model="dom.contractNo" placeholder="订单号" clearable
          ></el-input>
        </el-form-item>

        <el-form-item label="选择门店" prop="storeId">
          <el-select ref="storeNameSel" filterable v-model="dom.storeId" placeholder="请选择">
            <el-option
                v-for="(item, index) in storeList"
                :key="index"
                :label="item.storeName"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="onQuery" v-loading="loading">查询</el-button>
          <el-button @click="restQuery">重置</el-button>
        </el-form-item>
      </el-form>


      <el-table :data="rowData" height="500px" v-loading="loading"
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">

        <el-table-column
            prop="billNo"
            label="报险订单号"
            align="center">
        </el-table-column>
        <el-table-column
            prop="contractNo"
            label="报险合同号"
            align="center">
        </el-table-column>
        <el-table-column
            label="受理状态"
        >
          <template slot-scope="scope">
            <el-tag type="info" v-if="scope.row.status === 1">未受理</el-tag>
            <el-tag type="primary" v-if="scope.row.status === 2">受理中</el-tag>
            <el-tag type="success" v-if="scope.row.status === 3">已结案</el-tag>
          </template>
        </el-table-column>
        <el-table-column
            label="报险类型"
        >
          <template slot-scope="scope">
            <el-tag type="primary" v-if="scope.row.insuranceType === 1">物损</el-tag>
            <el-tag type="primary" v-if="scope.row.insuranceType === 2">人伤</el-tag>
          </template>
        </el-table-column>
        <el-table-column
            prop="createTime"
            label="上报时间"
            align="center">
        </el-table-column>
        <el-table-column
            prop="storeName"
            label="报险门店"
            align="center">
        </el-table-column>
        <el-table-column
            prop="opName"
            label="报险经纪人"
            align="center">
        </el-table-column>
        <el-table-column
            prop="reportAmount"
            label="报险金额"
            align="center">
        </el-table-column>

        <el-table-column
            label="操作"
            width="350"
        >
          <template slot-scope="scope">
            <el-button @click="editRow(scope.row)">查看</el-button>
            <el-button @click="editResult(scope.row)" v-if="scope.row.status === 3">结案说明</el-button>
            <el-button @click="download(scope.row)" type="primary">下载报案表</el-button>
            <el-button text type="success" v-if="scope.row.status === 1" @click="updateRow(scope.row)">受理</el-button>
            <el-button text type="warning" v-if="scope.row.status === 1 || scope.row.status === 2" @click="editResult(scope.row)">结案</el-button>
          </template>
        </el-table-column>

      </el-table>
      <div class="pagination">
        <Page :total="pageInfo.total"
              @on-change="onChangePage"
              :current="dom.current"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onPageSizeChange"
              :page-size="dom.size"/>
      </div>

    </div>

    <el-dialog title="报险数据" :visible.sync="dialogFormVisible">
      <el-tabs type="border-card">
        <el-tab-pane label="基础信息">
          <el-form :model="form" label-width="120px" :inline="false">
            <el-form-item label="保姆姓名">
              <el-input v-model="form.employeeName" autocomplete="off" style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item label="保姆身份证号">
              <el-input v-model="form.employeeCarId" autocomplete="off" style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item label="保姆手机号">
              <el-input v-model="form.employeePhone" autocomplete="off" style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item label="客户姓名">
              <el-input v-model="form.customer" autocomplete="off" style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item label="客户手机号">
              <el-input v-model="form.customerPhone" autocomplete="off" style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item label="出险时间">
              <el-input v-model="form.reportTime" autocomplete="off" style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item label="出险地址">
              <el-input type="textarea" autosize v-model="form.insuranceAddr" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="出险时所做工作">
              <el-input v-model="form.reportWork" autosize autocomplete="off" style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item label="出险详细说明">
              <el-input type="textarea" v-model="form.detail" autocomplete="off" style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item label="报险金额">
              <el-input v-model="form.reportAmount" autocomplete="off" style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item label="报险类型">
              <el-tag type="primary" v-if="form.insuranceType === 1">物损</el-tag>
              <el-tag type="primary" v-if="form.insuranceType === 2">人伤</el-tag>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="凭证上传">
          <el-form :model="form" label-width="120px" :inline="false" label-position="top">
            <span v-for="(item,index) in 10">
            <el-form-item :label="uploadListNameList[index]">
              <el-upload :action=action :data="uploadData" :file-list="getFileList(index + 1)"
                         list-type="picture-card" multiple :on-success="function(response, file) { return successPic(response, file,index+1) }">
                <i class="el-icon-plus avatar-uploader-icon"></i>

                <div slot="file" slot-scope="{file}">
                  <img
                      class="el-upload-list__item-thumbnail"
                      :src="file.url" alt=""
                  >
                  <span class="el-upload-list__item-actions">
                     <span
                         class="el-upload-list__item-preview"
                         @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                        </span>
                    <span
                        class="el-upload-list__item-delete"
                        @click="handleRemoveImg(file,index+1)">
                        <i class="el-icon-delete"></i>
                       </span>
                  </span>
                </div>
              </el-upload>
            </el-form-item>
              </span>
          </el-form>

        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="diaUpdate">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogVisibleImg">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>

    <el-dialog :visible.sync="dialogVisibleResult">
      <el-form>
        <el-form-item label="结案说明">
          <el-input v-model="form.statusRemark" type="textarea" :disabled="form.status === 3"></el-input>
        </el-form-item>
        <el-form-item label="附件图片">
          <el-upload :action=action :data="uploadData" :file-list="statusRemarkFileList" :disabled="form.status === 3"
                     list-type="picture-card" multiple :on-success="remarkSuccessImg" :on-remove="remarkRemoveImg">
            <i class="el-icon-plus avatar-uploader-icon"></i>
            <div slot="file" slot-scope="{file}">
              <img
                  class="el-upload-list__item-thumbnail"
                  :src="file.url" alt=""
              >
              <span class="el-upload-list__item-actions">
                        <span
                         class="el-upload-list__item-preview"
                         @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                        </span>
                  </span>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleResult = false">取 消</el-button>
        <el-button type="primary" @click="UpdateRemark">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>


export default {
  name: "insuranceManage",
  data() {
    return {
      statusRemarkFileList:[],
      dialogVisibleImg:false,
      dialogVisibleResult:false,
      dialogImageUrl:'',
      action:'https://api.xiaoyujia.com/system/imageUpload',
      uploadData: {route: 'reportInsurance'},
      formLabelWidth: '120px',
      loading: false,
      storeList: [],
      dialogFormVisible: false,
      form: {},
      dom: {
        billNo: '',
        contractNo: '',
        size: 15,
        current: 1,
        storeId: null
      },

      pageSizeOpts: [10, 15, 20, 30, 50, 100],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      rowData: [],

      fileList1: [],//物损/受伤图
      fileList2: [],//物损发票/医疗保单记录
      fileList3: [],//员工身份证正面
      fileList4: [],//员工身份证反面
      fileList5: [],//手写事故鉴定书
      fileList6: [],//结案时的身份证正反面/银行卡正反面
      fileList7: [],//结案时物损单据/医疗就诊单票据
      fileList8: [],//结案材料
      fileList9: [],//结案说明
      fileList10: [], //事故地址照片：小区大门照片、雇主家大门照片（要有门牌号）
      uploadListNameList:[
          '物损照片/受伤照片',
          '受损物品购买凭证发票/医疗就诊记录(诊断报告/手术记录/住记录)',
          '员工身份证正面',
          '员工身份证反面',
          '事故经过手写图',
          '事故地址照片',
          '客户身份证正反面及银行卡正反面/伤者身份证正反面及授权银行卡正反面',
          '受损物品购买凭证,发票或支付凭证/所有医疗就诊单据及票据原件',
          '保险结案材料理赔协议或和解协议，赔偿协议',
          '结案说明',
      ]
    }
  },
  created() {
    this.onQuery();
    this.getStoreList();
  },
  mounted() {

  },
  methods: {
    download(data) {
      this.$postUrl("downloadReportInsurance", data.id, {}, {
        responseType: "arraybuffer"
      }).then(res => {
        this.$message.success("请稍等，马上开始下载！");
        const aLink = document.createElement("a");
        let blob = new Blob([res]);
        aLink.href = URL.createObjectURL(blob);
        aLink.download = "商业保险报案表" + ".docx";
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink);

      });
    },
    remarkRemoveImg(file) {
      console.log(file)
      let url = file.url;
      const delIndex = this.statusRemarkFileList.findIndex(item => item.url === url);
      this.statusRemarkFileList.splice(delIndex, 1); // 第二个参数表示要移除的元素数量
    },
    remarkSuccessImg(response) {
      this.statusRemarkFileList.push({url:response.data})
    },
    editResult(data) {
      this.form = data;
      if(this.form.statusRemarkFileList) {
        const urlArray = this.form.statusRemarkFileList.split(',');
        this.statusRemarkFileList = urlArray.map(url => ({
          url
        }));
      }
      this.dialogVisibleResult = true;
    },
    handleRemoveImg(file,index) {
      let fileUrl = file.url;
      const delIndex = this[`fileList${index}`].findIndex(item => item.url === fileUrl);
      this[`fileList${index}`].splice(delIndex, 1); // 第二个参数表示要移除的元素数量
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisibleImg = true;
    },
    diaUpdate() {
      for (var i = 1; i < 11; i++) {
        if (!this[`fileList${i}`].length) {
          return  this.$message.error('图片更新不能为空');
        } else {
          this.form[`fileList${i}`] = this[`fileList${i}`].map(item => item.url).join(',');
        }
      }

      this.$postData("updateReportInsurance", this.form, {}).then((res) => {
        if (res.status === 200) {
          this.$message.success('更新成功');
          this.dialogFormVisible = false;
          this.onQuery();
        } else {
          this.$message.error(res.msg);
        }
      });

    },
    successPic(response, file, name) {
      console.log(name)
      const fileListName = `fileList${name}`;
      this[fileListName].push({url:response.data})
    },
    editRow(data) {
      this.$getData("getReportInsuranceById", {id:data.id}, {}).then((res) => {
        if (res.status === 200) {
          this.dialogFormVisible = true;
          this.form = res.data;
          for (var i = 1; i < 11; i++) {
            if(res.data[`fileList${i}`]) {
              const urlArray = res.data[`fileList${i}`].split(',');
              this[`fileList${i}`] = urlArray.map(url => ({
                url
              }));
            }

          }

        } else {
          this.$message.error(res.msg);
          this.loading = false;
        }
      });

    },
    updateRow(data) {
      this.loading = true;
      data.status = 2;
      this.$postData("updateReportInsurance", data, {}).then((res) => {
        if (res.status === 200) {
          this.$message.success('更新成功');
          this.onQuery();
          this.loading = false;
        } else {
          this.$message.error(res.msg);
          this.loading = false;
        }
      });

    },

    restQuery() {
      this.$refs['dom'].resetFields();
    },
    getStoreList() {

      this.$getData("getFranchiseStoreList", {}).then((res) => {
        if (res.status === 200) {
          this.storeList = res.data;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    onQuery() {
      this.loading = true;
      this.$postData("getReportInsuranceList", JSON.stringify(this.dom), {}).then((res) => {
        if (res.status === 200) {
          this.rowData = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total;
          this.loading = false;
        } else {
          this.$message.error(res.msg);
          this.loading = false;
        }
      });
    },
    onChangePage(index) {
      this.dom.current = index;
      this.onQuery();
    },
    onPageSizeChange(size) {
      this.loading = true;
      this.dom.size = size;
      this.onQuery();
    },
    getFileList(num) {
      // 动态返回对应的文件列表
      return this[`fileList${num}`];
    },

    UpdateRemark() {
      this.form.statusRemarkFileList = this.statusRemarkFileList.map(item => item.url).join(',');
      this.form.status = 3;
      this.$postData("updateReportInsurance", this.form, {}).then((res) => {
        if (res.status === 200) {
          this.$message.success('更新成功');
          this.dialogVisibleResult = false;
          this.onQuery();
        } else {
          this.$message.error(res.msg);
        }
      });

    }

  }
}
</script>

<style>

</style>
