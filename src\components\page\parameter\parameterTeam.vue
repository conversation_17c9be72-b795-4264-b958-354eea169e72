<!-- 运营指标 Operational Indicators -->
<template>
    <div class="container">
        <div class="handle-box">
            <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="所属部门" >
                  <Select filterable  v-model="dom.type">
                    <Option :value="1">一野 (客服、私域)</Option>
                    <Option :value="2">二野 (门店[直营、加盟] )</Option>
                    <Option :value="3">三野(大客户经理、驻场业务)</Option>
                  </Select>
                </el-form-item>
              <el-form-item label="门店类型" v-if="dom.type == 1">
                <Select filterable style="width: 130px" v-model="dom.departId" placeholder="部门">
                  <Option :value="null">全部</Option>
                  <Option value="2">私域</Option>
                  <Option value="42">销售部</Option>
                </Select>
              </el-form-item>
              <el-form-item label="门店类型" v-if="dom.type == 2">
                <Select filterable style="width: 130px" v-model="dom.storeType" placeholder="类型:直营/加盟">
                  <Option :value="null">全部</Option>
                  <Option value="1">直营</Option>
                  <Option value="2">加盟</Option>
                </Select>
              </el-form-item>
              <el-form-item label="姓名" >
               <el-input v-model="dom.realName"></el-input>
              </el-form-item>
                <el-form-item label="时间区间">
                    <el-date-picker
                            :clearable="false"
                            v-model="dom.times"
                            type="daterange"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            range-separator="~"
                            @change="changeTime"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-search" type="primary" @click="onQuery" v-loading="loading">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-refresh" @click="reset()" v-loading="loading">
                        重置
                    </el-button>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-download" type="primary" @click="exportExcel" v-loading="loading">导出</el-button>
                </el-form-item>
            </el-form>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-card>
                合计订单量:<span style="color: red">{{ sumData.allOrder }}</span>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card>
                合计营业额:<span style="color: red">{{ sumData.allTurnover }}</span>
              </el-card>
            </el-col>
          </el-row>

          <el-table :data="indicDataList" height="650px" v-loading="loading"
                      :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}"
                       :default-sort = "{prop: 'turnover', order: 'descending'}">

                <el-table-column
                        prop="storeName"
                        label="门店"
                        align="center"
                ></el-table-column>
                <el-table-column
                        prop="realName"
                        label="姓名"
                        align="center">
                </el-table-column>
                <el-table-column
                        prop="depart"
                        label="部门"
                        align="center">
                </el-table-column>
                <el-table-column
                        prop="turnover"
                        label="营业额"
                        align="left">
                </el-table-column>
                <el-table-column
                        prop="orderNum"
                        label="订单数"
                        align="left">
                </el-table-column>
                <el-table-column
                        prop="orderPrice"
                        label="客单价"
                        align="left">
                </el-table-column>

            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChangePage"
                      :current="dom.current"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="dom.size"/>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        data() {
            return {

                loading: false,
                dom: {
                    startTime: null,
                    endTime: null,
                    times: null,
                    storeType: null,
                    type: 1,
                    size: 15,
                    current: 1,
                    departId: null
                },

                pageSizeOpts: [10, 15, 20, 30, 50, 100],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                storeList: [],
                indicDataList: [],
                sumData:{
                  allOrder:0,
                  allTurnover:0,
                },

            }
        },
        async created() {
            this.dom.times = this.timeDefault();
            await this.getStoreData();
            this.getData();
            this.getDataSum();
        },
        mounted() {
        },
        methods: {
          getDataSum() {
            Object.keys(this.sumData).forEach(key => {
              this.sumData[key] = 0; // 或者设置其他默认值，比如 null
            });
            this.$postData("parameterTeamSum", this.dom, {}).then((res) => {
              if (res.status === 200) {
                this.sumData = res.data;
              } else {
                this.$message.error(res.msg);
              }
            });
          },
          timeDefault() {
            const invTime = (1000 * 60 * 60 * 24);
            const endDateNs = new Date();
            const startDateNs = new Date(endDateNs.getTime() - (invTime * 30));
            // 月，日 不够10补0
            let defaultStartTime = startDateNs.getFullYear() + '-'
                + ((startDateNs.getMonth() + 1) >= 10 ? (startDateNs.getMonth() + 1) : '0' + (startDateNs.getMonth() + 1)) + '-' + (startDateNs.getDate() >= 10 ? startDateNs.getDate() : '0' + startDateNs.getDate());
            let defaultEndTime = endDateNs.getFullYear() + '-' + ((endDateNs.getMonth() + 1) >= 10 ? (endDateNs.getMonth() + 1) : '0' + (endDateNs.getMonth() + 1)) + '-' + (endDateNs.getDate() >= 10 ? endDateNs.getDate() : '0' + endDateNs.getDate());
            this.dom.startTime = defaultStartTime + ' 00:00:00';
            this.dom.endTime = defaultEndTime + ' 23:59:59';
            return [defaultStartTime, defaultEndTime];
          },
            changeTime(v, s) {
                this.dom.startTime = v[0];
                this.dom.endTime = v[1];
            },

             getStoreData() {
               this.$postData("store_getByList", {}, {}).then(res => {
                 if (res.status == 200) {
                   this.storeList = res.data;
                 }
               });
            },
            exportExcel() {
                this.loadStart();
                this.$postData("parameterTeamExport", this.dom, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tableName: "蜂窝团队数据",
                        res: res
                    });
                })
            },
            onQuery(){
                this.pageInfo.current = 1;
                this.dom.current = 1;
                this.getData();
              this.getDataSum();
            },
            getData() {
                this.loadStart();
                this.indicDataList = [];
                this.$postData("parameterTeamList", this.dom, {}).then((res) => {
                    if (res.status === 200) {
                        this.indicDataList = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total;
                        this.loadEnd();
                    } else {
                        this.$message.error(res.msg);
                        this.pageInfo.current = 1;
                        this.pageInfo.size = 0;
                        // this.page.total = 10;
                        this.loadEnd();
                    }
                });
            },
            reset() {
                this.pageInfo.current = 1;
                this.dom.current = 1;
                this.dom.storeId = null;
                this.dom.storeType = null;
                this.dom.times = this.timeDefault();
                this.getData();
            },
            onChangePage(index) {
                this.dom.current = index;
                this.getData();
            },
            onPageSizeChange(size) {
                this.loading = true;
                this.dom.size = size;
                this.getData();
            },
            loadStart() {
                this.loading = true;
            },
            loadEnd() {
                this.loading = false;
            },
            blobExport({tableName, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tableName + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
                this.loadEnd();
            },
        }

    };
</script>

<style scoped>
    /*滚动条的宽度*/
    /deep/ .el-table__body-wrapper::-webkit-scrollbar {
        /*横向滚动条*/
        width: 6px;
        /*纵向滚动条 必写*/
        height: 6px;

    }

    /*滚动条的滑块*/
    /deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 3px;
    }

    .el-row {
        margin-bottom: 20px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .el-col {
        border-radius: 4px;
    }

    .bg-purple-dark {
        background: #99a9bf;
    }

    .bg-purple {
        background: #d3dce6;
    }

    .bg-purple-light {
        background: #e5e9f2;
    }

    .grid-content {
        border-radius: 4px;
        min-height: 36px;
    }

    .row-bg {
        padding: 10px 0;
        background-color: #f9fafc;
    }

</style>
