<template>

    <div class="table">
        <div style="padding:0 20px ;margin-top: 20px">
            <el-input
                    @change="getData"
                    placeholder="请输入搜索的标题内容"
                    v-model="model.title"
                    clearable>
                <template slot="prepend">标题名称</template>
                <el-button slot="append" icon="el-icon-search" style="width: 100px" ></el-button>
            </el-input>

        </div>


        <div class="container">
            <el-row>
                <el-col :span="20">
                    <el-tabs v-model="typeName" @tab-click="getData"  >
                        <el-tab-pane label="全部" name="all" ></el-tab-pane>
                        <el-tab-pane :label="item" :name="item" v-for="(item,index) in typeNameList" :key="index"></el-tab-pane>
                    </el-tabs>
                </el-col>
                <el-col :span="4">
                    <el-button type="primary" plain icon="el-icon-plus" style="margin-left: 20px" @click="toCmsEdit(null)">新增文章</el-button>
                </el-col>
            </el-row>

            <div v-for="(item,index) in list" :key="index">
                <el-card shadow="hover" style="margin-bottom: 10px;padding-bottom: 10px">
                    <el-row>
                        <el-col :span="18">
                            <el-image
                                    style="width: 100px; height: 100px;float: left;margin-right: 20px"
                                    :src="item.cover"
                                    fit="cover"></el-image>
                            <h2>{{item.title}}
                                <el-divider direction="vertical"></el-divider>
                                <el-tag :type="item.orderNum<100?'info':item.orderNum<145?'null':'danger'">{{item.orderNum<100?'普通':item.orderNum<145?'热门':'置顶'}}</el-tag></h2>
                            <div style="color: #6f7180;padding: 10px">{{item.abstractInfo}}</div>
                            <div style="color: #9e9e9e;float: right">
                                <i class="el-icon-user-solid"/> {{item.creatPerson}} <el-divider direction="vertical"></el-divider>
                                <i class="el-icon-date"/> {{item.putDate}} <el-divider direction="vertical"></el-divider>
                                <i class="el-icon-view"/> {{item.viewNum}}
                            </div>
                        </el-col>
                        <el-col :span="6">
                            <el-button type="primary" plain icon="el-icon-camera" @click="drawerLink=item.outLink,drawerDom=item.cms,drawer=true">预览</el-button>
                            <el-button type="info" plain icon="el-icon-edit" @click="toCmsEdit(item.id)">编辑</el-button>
                            <el-button type="info" icon="el-icon-document-copy" @click="copyLink(item.cms)">复制链接</el-button>
                        </el-col>
                    </el-row>
                </el-card>


            </div>
        <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
        </div>
        </div>
        <el-drawer
                size="60%"
                title="预览"
                :visible.sync="drawer"
                direction="rtl">
            <div style="padding: 20px">
<!--                <iframe :src="drawerLink" scrolling="auto" width="100%" height="100%" frameborder="0"></iframe>-->
                <div v-html="drawerDom"  class="ql-editor"></div>
<!--                <code>{{drawerDom}}</code>-->
<!--                <pre>{{drawerDom}}</pre>-->
            </div>

        </el-drawer>
    </div>

</template>

<script>

    export default {
        name: "cmsPage",
        data(){
            return{
                drawerLink:null,
                drawerDom:null,
                drawer:false,
                typeName:'all',
                typeNameList:[],
                pageSizeOpts:[10,20,30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                model: {
                    typeName:null,
                    title:null,
                    current: 1,
                    size: 10
                },
                list:[],
            }
        },
        created(){
            this.getTypeNameList();
            this.getData();
        },
        methods:{
            toCmsEdit(id){
                this.$router.push({path:'/cmsEdit',query:{"id":id}})
            },
            getData() {
                if (this.typeName=='all'){
                    this.model.typeName=null
                }else {
                    this.model.typeName=this.typeName
                }
                this.$postData("cmsInfoPage", this.model, {}).then(res => {
                    if (res.status == 200) {

                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getTypeNameList(){
                this.$getData("getTypeNameList", null).then(res => {
                    if (res.status == 200) {
                        this.typeNameList = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            onChange(index) {
                console.log(index)
                this.model.current = index;
                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.model.size = size;
                this.getData();
            },
          //复制链接
          copyLink(link){
console.log(link)
          }
        }
    }
</script>
<style>
     {
        color: #409EFF;
        background: none;
        text-align: center;
    }
</style>

<style scoped>
    iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        //overflow: hidden;
        margin: 0;
        padding: 0;
        border: 0;
    }

    .container{
        border: none;
        background: #f5f6f7;
    }

</style>
