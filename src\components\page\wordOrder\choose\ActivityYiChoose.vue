<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <div id="searchDiv">
            </div>
            <div class="fromInput">
                <Form ref="dto" :model="dto" :label-width="80" label-position="left">
                    <FormItem label="状态" prop="trainTime">
                        <Select filterable style="width: 150px" v-model="dto.state" clearable>
                            <Option :value="1">已提交</Option>
                            <Option :value="2">未通过</Option>
                            <Option :value="3">已通过</Option>
                            <Option :value="4">已转账</Option>
                        </Select>
                    </FormItem>
                    <FormItem label="线上意见" prop="trainTime">
                        <el-input
                                :disabled="true"
                                type="textarea"
                                :rows="5"
                                placeholder="请输入内容"
                                v-model="dto.onOpinion">
                        </el-input>
                    </FormItem>
                    <FormItem label="线上图片:">
                        <div v-for="item in dto.onImages" style="margin-left: 0.5rem;float: left" @click="setShow(item)">
                            <el-image
                                    :src="item"
                                    style="width: 100px; height: 100px">
                            </el-image>
                        </div>
                    </FormItem>
                        <FormItem label="线下意见" prop="trainTime">
                            <el-input
                                    :disabled="true"
                                    type="textarea"
                                    :rows="5"
                                    placeholder="请输入内容"
                                    v-model="dto.lowerOpinion">
                            </el-input>
                    </FormItem>
                    <FormItem label="线下图片:">
                        <div v-for="item in dto.lowerImages" style="margin-left: 0.5rem;float: left" @click="setShow(item)">
                            <el-image
                                    :src="item"
                                    style="width: 100px; height: 100px">
                            </el-image>
                        </div>
                    </FormItem>
                </Form>
                <Button type="primary" @click="update">确定</Button>
                <Button type="success" style="margin-left:3rem" @click="chooseThisModel">取消</Button>
            </div>
            <Modal v-model="show" class="Modal" :width="screenWidth" title="查看"
                   :mask-closable="false">
                <div class="addBody">
                    <img-choose v-if="show" @init-choose="initChooseProject" :url="url"
                                @close-modal="closeCurrModal"></img-choose>
                </div>
                <div slot="footer">
                </div>
            </Modal>
        </div>
    </div>
</template>
<script>
    import imgChoose from '@/components/page/userTrack/imgChoose.vue'

    export default {
        props: ['id'],
        data() {
            return {
                evaluateList: [],
                screenWidth: '50%',
                show: false,
                dto: {
                    id: this.id,
                },
                images: [],
                url: null,
            }
        },
        components: {
            "imgChoose": imgChoose
        },
        created: function () {
            this.getById();
        },
        methods: {
            getById() {
                this.$getData("activityYi_getById", {id: this.dto.id}).then(res => {
                   ;
                    if (res.status === 200) {
                        this.dto = res.data;
                    }
                })
            },
            setShow(url) {
                this.show = true;
                this.url = url
            },
            initChooseProject() {
                this.closeCurrModal();
            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(dto) {
                this.$emit('init-choose', dto);
                //this.update(this[dto])
            },
            closeCurrModal() {
                this.show = false;
            },
            update(){
                this.dto.createTime=new Date(this.dto.createTime);
                this.dto.member=null;
                this.$postData("activityYi_update", this.dto).then(res => {
                   ;
                    if (res.status === 200) {
                       this.chooseThisModel()
                    }
                })
            }
        }
    }
</script>


<style scoped>


    .fromInput {
        float: left;
    }

    #bodyDiv {
        font-size: 15px !important;
        background-color: #fff;
        height: 600px;
    }
</style>

