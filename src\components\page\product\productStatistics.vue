<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">
                    <el-row>
                        <el-col :span="10">
                            <el-form-item label="服务项目">
                                <Select filterable style="width: 180px" @on-change="changeValue"
                                        v-model="productCategory">
                                    <Option v-for="item in options" :value="item.id" :key="item.id">{{ item.name}}
                                    </Option>
                                </Select>
                                <Select filterable style="width: 180px" v-model="dto.productId">
                                    <Option :value="0">全部</Option>
                                    <Option v-for="item in options_children" :value="item.id" :key="item.id">{{
                                        item.name}}
                                    </Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="10">
                            <el-form-item label="结算月份">
                                <el-date-picker
                                        v-model="dto.dt"
                                        type="month"
                                        placeholder="选择月"
                                        format="yyyy 年 MM 月"
                                        value-format="yyyy-MM"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4" style="text-align:right">
                            <el-button type="primary" @click="onSubmit">搜索</el-button>
                            <el-button type="success" @click="exportExcel">导出Excel</el-button>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div style="text-align:right">{{theader}}</div>
            <el-table
                    :data="data"
                    border
                    class="table"
                    ref="multipleTable"
                    @sort-change="sortChange"
            >
                <el-table-column
                        prop="dt"
                        label="结算月份"
                        :show-overflow-tooltip="true"
                        sortable="custom">
                    <template slot-scope="scope">
                        <a  style="text-decoration:underline;" @click="local(scope.row.dt)">{{scope.row.dt}}</a>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="orderNum"
                        label="订单数"
                        :show-overflow-tooltip="true"
                        sortable="custom"
                ></el-table-column>
                <el-table-column
                        prop="totalAmount"
                        label="服务金额"
                        :show-overflow-tooltip="true"
                        sortable="custom"
                ></el-table-column>
                <el-table-column
                        prop="preferentialAmount"
                        label="优惠金额"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column prop="amount" label="实付金额" :show-overflow-tooltip="true"></el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                        background
                        layout="total,sizes,prev, pager, next,jumper"
                        :total="pageInfo.total"
                        :page-size="pageInfo.size"
                        :current-page="pageInfo.current"
                        :page-sizes="[10,20,50,100]"
                        :page-count="pageInfo.pages"
                        @size-change="sizeChange"
                        @current-change="currentChange"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: "basetable",
        data() {
            return {
                tableData: [],
                multipleSelection: [],

                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                theader: "",
                registerTime: [],
                lastLoginTime: [],

                productCategory: null,
                options: [], //服务项目一级
                options_children: [], //服务项目二级
                registerTime: [],
                lastLoginTime: [],
                serviceOptions: [], //服务区域
                serviceArray: [],
                dto: {
                    dt: "",
                    productId: null,

                    export: "",
                    orderBy: "",
                    pageIndex: 1,
                    pageSize: 10,
                    productIds: [],
                }
            };
        },
        created() {
            this.getProduct()
        },
        computed: {
            data() {
                return this.tableData.filter(d => {
                    d.realStartTime =
                        d.realStartTime == null
                            ? null
                            : d.realStartTime.replace("T", " ").substring(0, 19);
                    d.createTime =
                        d.createTime == null
                            ? null
                            : d.createTime.replace("T", " ").substring(0, 19);
                    d.settleTime =
                        d.settleTime == null
                            ? null
                            : d.settleTime.replace("T", " ").substring(0, 19);
                    return d;
                });
            }
        },
        methods: {
            changeValue(value) {
                this.dto.productId = "";
                this.$getData("product_getByProductCategoryId", {productCategoryId: value}).then(
                    res => {
                        if (res.status == 200) {
                            this.options_children = res.data;
                        }

                    }
                );
            },
            getData() {
                this.openFullScreen();
                this.$postData("product_performanceStatistics", this.dto, {})
                    .then(res => {
                        if (res.status == 200) {

                            this.tableData = res.data.list;
                            this.pageInfo.current = res.data.pageNum;
                            this.pageInfo.size = res.data.pageSize;
                            this.pageInfo.total = res.data.total;
                            let orderNum = 0;
                            let amount = 0;
                            for (let i=0; i < res.data.list.length; i++) {
                                let to = res.data.list[i];
                                console.log(to)
                                orderNum += to.orderNum;
                                amount += to.amount;
                            }
                            orderNum= parseFloat(orderNum).toFixed(2)
                            amount= parseFloat(amount).toFixed(2)
                            this.theader = "订单总数：" + orderNum + "  实付总额：" + amount;
                            this.closeFullScreen();
                        } else {
                            this.closeFullScreen();
                            this.$message.error("查询失败，" + res.msg);
                        }
                    })
            },
            getProduct() {
                let list = {
                    name: "贵宾年卡"
                }
                this.$postData("category_queryList", list, {}).then(res => {
                    if (res.status == 200) {
                        this.options = res.data;
                        this.productCategory = res.data[0].id
                        this.$getData("product_getByProductCategoryId", {productCategoryId: this.productCategory}).then(
                            res => {
                                if (res.status == 200) {

                                    this.options_children = res.data;
                                    for (let i = 0; i < res.data.length; i++) {
                                        this.dto.productIds.push(res.data[i].id)
                                    }
                                    this.dto.productId = 0;
                                    this.getData();
                                }
                            }
                        );
                    }
                });
            },
            local(dt){
                let query={
                    dt:dt,
                    productId:this.dto.productId,
                    productCategoryId:this.productCategory,
                    routeProduct:true,
                    list:null,
                }
                this.$router.push({path:'ordersStatistics',query:query});
            },
            onSubmit() {
                this.dto.export = "";
                // this.dto.orderBy = "";
                // this.dto.pageSize = 10;
                //this.dto.pageIndex = "1";

                this.getData();
            },
            exportExcel() {
                this.$postData("product_excel", this.dto, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tablename: "售卡业绩",
                        res: res
                    });
                    this.dto.export = ""
                });
            },
            // 页码大小
            sizeChange(size) {
                this.dto.pageSize = size;
                this.dto.pageIndex = 1;
                this.getData();
            },
            // 跳转页码
            currentChange(index) {
                this.dto.pageIndex = index;
                this.getData();
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                aLink.target = "_blank";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
            openFullScreen() {
                const loading = this.$loading({
                    lock: true,
                    text: "Loading",
                    spinner: "el-icon-loading",
                    background: "rgba(0, 0, 0, 0.7)"
                });
            },
            closeFullScreen() {
                const loading = this.$loading({
                    lock: true,
                    text: "Loading",
                    spinner: "el-icon-loading",
                    background: "rgba(0, 0, 0, 0.7)"
                });
                loading.close();
            },
            sortChange: function (column, prop, order) {
                if (column == null) {
                    this.dto.orderBy = "";
                } else {
                    if (column.order == "ascending") {
                        this.dto.orderBy = column.prop + " ASC";
                    }
                    if (column.order == "descending") {
                        this.dto.orderBy = column.prop + " DESC";
                    }
                }
                this.getData();
            }
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
