<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">
                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="姓名">
                                <el-input
                                        clearable
                                        v-model="dto.name"
                                        placeholder="姓名/工号"
                                        style="width:170px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="4">
                            <el-form-item label="状态">
                                <Select filterable clearable style="width: 120px" v-model="dto.state"
                                        @on-change="query()">
                                    <Option value="">请选择</Option>
                                    <Option value="1">未确定</Option>
                                    <Option value="2">已确定</Option>
                                    <Option value="3">工资异常</Option>
                                    <!--<Option v-for="item in stateList" :value="item.id">{{ item.text}}</Option>-->
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4" style="padding-left: 1rem">
                            <el-form-item label="月份">
                                <el-date-picker
                                        style="width:150px"
                                        v-model="dto.day"
                                        type="month"
                                        format="yyyy-MM"
                                        value-format="yyyy-MM"
                                        placeholder="选择月">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6" style="padding-left: 1rem">
                            <el-form-item label="至">
                                <el-date-picker
                                        style="width:150px"
                                        v-model="dto.day1"
                                        type="month"
                                        format="yyyy-MM"
                                        value-format="yyyy-MM"
                                        placeholder="选择月">
                                </el-date-picker>
                                <el-button type="primary" style="margin-left: 20px" @click="query()">搜索</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row style="margin-top: 10px">
                        <el-col :span="24">
                            <el-form-item>
                                <el-button type="success" style="width:8%"
                                           @click="isModal=true">批量导入
                                </el-button>
                                <el-button type="primary" style="width:8%" @click="modalUp=true">推送设置</el-button>
                                <!--<el-button type="primary" style="width:8%" @click="push">全员推送</el-button>-->
                                <el-button type="success" :loading="batch" @click="batchPush">批量推送</el-button>
                                <el-button type="primary" plain :loading="loading" @click="exportData()">导出</el-button>
                                <!--<el-button type="warning" @click="isModal=true">检测-->
                                <!--</el-button>-->
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-table :data="list" class="table" ref="multipleTable"
                      :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000',textAlign: 'center'}"
                      :cell-style="{textAlign: 'center'}"
                      @selection-change="handleSelectionChange"
                      v-loading="loading">
                <el-table-column
                        type="selection"
                        width="50">
                </el-table-column>
                <el-table-column
                        prop="name"
                        width="140"
                        label="姓名">
                </el-table-column>
                <el-table-column
                        width="100"
                        prop="no"
                        label="工号">
                </el-table-column>
                <el-table-column
                        width="120"
                        prop="departmentName"
                        label="部门">
                </el-table-column>
                <el-table-column
                        width="130"
                        prop="month"
                        label="工资所属月份">
                </el-table-column>
                <el-table-column
                        width="120"
                        prop="state"
                        label="状态">
                    <template slot-scope="scope">
                        <span style="font-size: 15px" v-if=" scope.row.state===1">
                            未确定
                        </span>
                        <span style="font-size: 15px" v-if=" scope.row.state===2">
                            已确定
                        </span>
                        <span style="font-size: 15px;color: red" v-if="scope.row.state===3">
                           工资异常
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                        width="180"
                        prop="errorMessage"
                        label="异常信息"
                ></el-table-column>
                <el-table-column
                        width="180"
                        prop="createTime"
                        label="导入时间"
                ></el-table-column>
                <el-table-column
                        width="200"
                        label="操作">
                    <template slot-scope="scope">
                        <div style="text-align: center">
                            <div v-if="scope.row.state===1">
                                <el-button size="mini" @click="edit(scope.row.no)" type="primary">提醒</el-button>
                            </div>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :current="this.dto.pageNum"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="10"/>
            </div>
        </div>
        <Modal v-model="isModal" class="Modal" :width="screenWidth" title="批量导入"
               :mask-closable="false"
               @on-cancel="getData()">
            <div class="addBody">
                <salary-import v-if="isModal" @init-choose="initChooseProject"
                               @close-modal="closeCurrModal"></salary-import>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="isModal1" class="Modal" :width="screenWidth" title="批量导入"
               :mask-closable="false"
               @on-cancel="getData()">
            <div class="addBody">
                <salary-push v-if="isModal1" @init-choose="initChooseProject"
                             @close-modal="closeCurrModal"></salary-push>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <salary-up v-if="modalUp"
                   @init-choose="initChooseProject">
        </salary-up>
    </div>
</template>

<script>
    import salaryImport from '@/components/page/salary/salaryImport.vue'
    import salaryPush from '@/components/page/salary/salaryPush.vue'
    import salaryUp from '@/components/page/salary/salaryUp.vue'

    export default {
        data() {
            return {
                batch: false,
                loading: true,
                loadingExcel: false,
                isModal: false,
                isModal1: false,
                modalUp: false,
                list: null,
                screenWidth: "60%",
                pageSizeOpts: [10, 20, 30],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                dto: {
                    name: null,
                    days: null,
                    day1: null,
                    state: null,
                    pageSize: 10,
                    pageNum: 1,
                },
                nos: [],
            };
        },
        components: {
            'salaryImport': salaryImport,
            "salaryUp": salaryUp,
            "salaryPush": salaryPush
        },
        created() {
            this.getData();
        },
        computed: {},
        methods: {
            handleSelectionChange(list) {
                let nos = [];
                list.forEach(item => {
                    nos.push(item.no)
                });
                this.nos = nos;
            },
            batchPush() {
                if (this.nos.length <= 0) {
                    this.$message.error("请勾选");
                    return
                }
                this.batch = true;
                this.nos.forEach((item, index) => {
                    if (index === this.nos.length - 1) {
                        this.batch = false;
                        this.nos = [];
                    }
                    this.edit(item);
                });
            },
            push() {
                this.$confirm('此操作将推送企业下所有成员, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$postData("salarySetUp_push", {
                        touser: "@all"
                    }).then(res => {
                        this.$message({
                            type: 'success',
                            message: '消息推送成功!'
                        });
                    });
                }).catch(() => {
                    // this.$message({
                    //     type: 'info',
                    //     message: '已取消删除'
                    // });
                });
            },
            deleteTs(id) {
                this.$confirm('此操作将永久删除该投诉, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$getData("tsOrderDelete", {id: id}).then(res => {
                        this.getData()
                    });
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },

            getData() {
                this.$postData("salaryDetails_salaryDetails", this.dto).then(res => {
                    this.loading = false;
                    if (res.status === 200) {
                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                    }
                });
            },
            edit(no) {
                this.$getData("salarySetUp_goPush", {no: no}).then(res => {
                    if (res.status === 200) {
                        this.$message({
                            type: 'success',
                            message: '提醒成功!'
                        });
                    } else {
                        this.$message.error(no + res.msg);
                    }
                });
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal(data) {
                this.isModal = false;
                this.modalUp = false;
                this.isModal1 = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                //this.getData()
            },
            query() {
                this.loading = true;
                this.dto.pageNum = 1;
                this.getData();
            },

            // 页码大小
            onPageSizeChange(size) {
                this.loading = true;
                this.dto.pageSize = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                this.loading = true;
                this.dto.pageNum = index;
                this.getData();
            },
            exportData() {
                this.loading = true;
                this.$postData("salaryDetails_export", this.dto, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tableName: "工资数据",
                        res: res
                    });
                })
            },
            exportExcel() {
                this.loadingExcel = true;
                this.$postData("tsExportList", this.dto, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.loadingExcel = false;
                    this.blobExport({
                        tableName: "投诉工单",
                        res: res
                    });
                });
            },
            blobExport({tableName, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tableName + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
                this.loading = false;
            },
        }
    };
</script>

<style scoped>
    .handle-box {
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }

    .el-date-editor .el-range-separator {
        padding: 0 5px;
        line-height: 32px;
        width: 7%;
        color: #303133;
    }
</style>
