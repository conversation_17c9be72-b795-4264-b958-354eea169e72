<template>
    <div class="table">
        <div class="container">
            <div>
                <!--<el-button type="success" :loading="bind" @click="getBindInvoice">关联发票-->
                <!--</el-button>-->
            </div>
            <div class="handle-box" style="margin-left: 2rem;width: 95%">
                <Form :label-width="80" style="margin-top: 20px" inline
                      label-position="right">
                    <FormItem label="发票抬头">
                        <Input size="large" @on-blur="update"
                               clearable
                               v-model="siteInvoice.header"
                               style="width: 220px"
                               placeholder="发票抬头"/>
                    </FormItem>
                    <FormItem label="识别号">
                        <Input size="large"
                               clearable
                               @on-blur="update"
                               v-model="siteInvoice.taxpayerIdentityNumber"
                               style="width: 220px"
                               placeholder="识别号"/>
                    </FormItem>
                    <FormItem label="发票类型">
                        <el-select
                                @change="change"
                                style="width: 150px"
                                v-model="siteInvoice.invoiceType">
                            <el-option :value="5" label="增值税专用发票"></el-option>
                            <el-option :value="6" label="增值税普通发票"></el-option>
                        </el-select>
                    </FormItem>
                    <FormItem label="开票类别">
                        <el-select
                                @change="invoiceCategoryChange"
                                style="width: 100px"
                                v-model="siteInvoice.invoiceCategory">
                            <el-option :value="1" label="纸质"></el-option>
                            <el-option :value="2" label="电子"></el-option>
                        </el-select>
                    </FormItem>
                    <FormItem label="专票开户行">
                        <Input size="large"
                               clearable
                               @on-blur="update"
                               v-model="siteInvoice.accountBank"
                               style="width: 220px"
                               placeholder="专票-专票开户行"/>
                    </FormItem>
                    <FormItem label="专票开户银行卡号">
                        <Input size="large"
                               clearable
                               @on-blur="update"
                               v-model="siteInvoice.accountNumber"
                               style="width: 220px"
                               placeholder="专票开户银行卡号"/>
                    </FormItem>
                    <FormItem label="专票电话">
                        <Input size="large"
                               clearable
                               @on-blur="update"
                               v-model="siteInvoice.specialPhone"
                               style="width: 220px"
                               placeholder="专票电话"/>
                    </FormItem>
                    <FormItem label="专票地址">
                        <Input size="large"
                               clearable
                               type="textarea" :rows="3"
                               @on-blur="update"
                               v-model="siteInvoice.specialAddress"
                               style="width: 220px"
                               placeholder="专票地址"/>
                    </FormItem>

                    <FormItem label="邮箱">
                        <Input size="large"
                               @on-blur="update"
                               v-model="siteInvoice.email"
                               style="width: 180px"
                               placeholder="邮箱"/>
                    </FormItem>
                    <FormItem label="收货人">
                        <Input size="large"
                               @on-blur="update"
                               v-model="siteInvoice.spperson"
                               style="width: 180px"
                               placeholder="收货人"/>
                    </FormItem>
                    <FormItem label="收货电话">
                        <Input size="large"
                               @on-blur="update"
                               v-model="siteInvoice.sppersonTel"
                               style="width: 180px"
                               placeholder="收货电话"/>
                    </FormItem>
                    <FormItem label="收货地址">
                        <Input size="large"
                               @on-blur="update"
                               type="textarea" :rows="3"
                               v-model="siteInvoice.receiveAddress"
                               style="width: 250px"
                               placeholder="收货地址"/>
                    </FormItem>
                    <FormItem label="寄送方式">
                        <el-select
                                @change="update"
                                style="width: 100px"
                                v-model="siteInvoice.deliveryType">
                            <el-option :value="1" label="邮寄"></el-option>
                            <el-option :value="2" label="到付"></el-option>
                            <el-option :value="3" label="自取"></el-option>
                            <el-option :value="4" label="邮件"></el-option>
                        </el-select>
                    </FormItem>
                    <FormItem label="发票项目">
                        <el-select filterable
                                   @change="update"
                                   style="width: 150px"
                                   v-model="siteInvoice.categoryId">
                            <el-option v-for="(item, index) in siteInvoice.categories"
                                       :key="index"
                                       :value="item.id"
                                       :label="item.name">
                            </el-option>
                        </el-select>
                    </FormItem>
                    <FormItem label="发票备注">
                        <Input size="large"
                               @on-blur="update"
                               v-model="siteInvoice.remark"
                               style="width: 180px"
                               placeholder="发票备注"/>
                    </FormItem>
                </Form>
            </div>
        </div>
    </div>
</template>
<script>
    export default {
        props: ['projectId'],
        data() {
            return {
                bind: false,
                siteInvoice: {
                    name: null,
                    id: this.siteEmployeeId,
                    projectId: this.projectId,
                    state: 2,
                    type: 1,
                    quitTime: null,
                    employeeId: null,
                    specialPhone:null,
                },
            };
        },
        components: {},
        watch: {
            projectId: {
                handler(newValue, oldValue) {
                    this.projectId = newValue;
                    this.getInvoice();
                }
            }
        },
        created() {
            this.getInvoice();
        },
        computed: {},
        methods: {
            invoiceCategoryChange(val) {
                if (val === 2 && this.siteInvoice.invoiceType === 5) {
                    this.siteInvoice.invoiceCategory = 1;
                    this.$message.error("增值税专用发票只可开纸质");
                    return
                }
                this.update()
            },
            change(val) {
                if (val === 5) {
                    this.siteInvoice.invoiceTypeName = "增值税专用发票";
                    this.siteInvoice.invoiceCategory = 1
                }
                if (val === 6) {
                    this.siteInvoice.invoiceTypeName = "增值税普通发票"
                }
                this.update();
            },
            update() {
              this.$postData("siteInvoice_update", this.siteInvoice).then(res => {
                if (res.status === 200) {
                  this.siteInvoice = res.data
                }
              })
            },
            getInvoice() {
                this.$postUrl("siteInvoice_getByProjectId", this.projectId).then(res => {
                    if (res.status === 200) {
                      if(res.data===null){
                        this.siteInvoice_insert();
                      }else {
                        this.siteInvoice = res.data
                      }
                    }
                })
            },
          siteInvoice_insert() {
            this.$postData("siteInvoice_insert", this.siteInvoice).then(res => {
              if (res.status === 200) {
                this.siteInvoice = res.data
              }
            })
          },
            getBindInvoice() {
                this.bind = true;
                this.$getData("siteInvoice_getBindInvoice", {projectId: this.projectId}).then(res => {
                    this.bind = false;
                    if (res.status === 200) {
                        this.getInvoice();
                        this.$message({
                            type: 'success',
                            message: '操作成功!'
                        });
                    }
                })
            }
        }
    };
</script>

<style scoped>
    .handle-box {
        /*margin-bottom: 10px;*/
        font-weight: bold !important;
    }

    .el-form-item__label {
        font-weight: bold !important;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;

    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }

    .el-date-editor .el-range-separator {
        padding: 0 5px;
        line-height: 32px;
        width: 7%;
        color: #303133;
    }

    .container {
        padding: 0px !important;
        background: #fff;
        border: none !important;
        border-radius: 5px;
    }
</style>
