<template>
    <div class="table">
        <div class="container">
            <div style="padding-bottom: 10px">
                <span class="headTitle">实时数据展示</span>
                <el-divider direction="vertical"></el-divider>
                <el-tag type="danger">红色为达成或增长</el-tag>
                <el-tag>蓝色为未达成或减少</el-tag>
                <el-divider direction="vertical"></el-divider>{{dateNow}}
                <el-divider direction="vertical"></el-divider><el-button @click="initData" type="primary" :loading="!show"  icon="el-icon-refresh" round>数据立即更新</el-button>
            </div>
            <el-row>
                <el-col span="8">
                    <div class="bg-purple" >
                        <el-card shadow="hover">

                            <el-badge :value="(((showData.toDayAmontPay-showData.yuesDayAmontPay)/showData.yuesDayAmontPay)*100).toFixed(2)+'%'" class="item" :type="((showData.toDayAmontPay-showData.yuesDayAmontPay)/showData.yuesDayAmontPay)>0?'danger':'primary'">
                                <div :class="Number(showData.toDayAmontPay)>=Number(showData.yuesDayAmontPay)?'cardParams0':'cardParams'">
                                    <span class="hopeParam">今日 <el-divider direction="vertical"></el-divider></span>{{showData.toDayAmontPay==null?'未计算':showData.toDayAmontPay}}
                                    <span class="hopeParam" v-if="showData.yuesDayAmontPay!=0">/ {{showData.yuesDayAmontPay}}<span class="hopeParam"> <el-divider direction="vertical"></el-divider>昨日</span></span>
                                </div>
                            </el-badge>
                            <div class="myNote">今日订单结算数据</div>
                        </el-card>
                    </div>
                </el-col>
                <el-col span="8">
                    <div class="bg-purple" >
                        <el-card shadow="hover">

                            <el-badge :value="(((showData.toDayKdPay-showData.yuesDayKdPay)/showData.yuesDayKdPay)*100).toFixed(2)+'%'" class="item" :type="((showData.toDayKdPay-showData.yuesDayKdPay)/showData.yuesDayKdPay)>0?'danger':'primary'">
                                <div :class="Number(showData.toDayKdPay)>=Number(showData.yuesDayKdPay)?'cardParams0':'cardParams'">
                                    <span class="hopeParam">今日 <el-divider direction="vertical"></el-divider></span>{{showData.toDayKdPay==null?'未计算':showData.toDayKdPay}}
                                    <span class="hopeParam" v-if="showData.yuesDayKdPay!=0">/ {{showData.yuesDayKdPay}}<span class="hopeParam"> <el-divider direction="vertical"></el-divider>昨日</span></span>
                                </div>
                            </el-badge>
                            <div class="myNote">今日客单价数据</div>
                        </el-card>
                    </div>
                </el-col>
                <el-col span="8">
                    <div class="bg-purple" >
                        <el-card shadow="hover">

                            <el-badge :value="(((showData.toDayAmontCre-showData.yuesAmontCre)/showData.yuesAmontCre)*100).toFixed(2)+'%'" class="item" :type="((showData.toDayAmontCre-showData.yuesAmontCre)/showData.yuesAmontCre)>0?'danger':'primary'">
                                <div :class="Number(showData.toDayAmontCre)>=Number(showData.yuesAmontCre)?'cardParams0':'cardParams'">
                                    <span class="hopeParam">今日 <el-divider direction="vertical"></el-divider></span>{{showData.toDayAmontCre==null?'未计算':showData.toDayAmontCre}}
                                    <span class="hopeParam" v-if="showData.yuesAmontCre!=0">/ {{showData.yuesAmontCre}}<span class="hopeParam"> <el-divider direction="vertical"></el-divider>昨日</span></span>
                                </div>
                            </el-badge>
                            <div class="myNote">今日订单开单数据</div>
                        </el-card>
                    </div>
                </el-col>
                <el-col span="6">
                    <div class="bg-purple" >
                        <el-card shadow="hover">

                            <el-badge :value="(((showData.toMouthAmontPay-showData.yuesMouthAmontPay)/showData.yuesMouthAmontPay)*100).toFixed(2)+'%'" class="item" :type="((showData.toMouthAmontPay-showData.yuesMouthAmontPay)/showData.yuesMouthAmontPay)>0?'danger':'primary'">
                                <div :class="Number(showData.toMouthAmontPay)>=Number(showData.yuesMouthAmontPay)?'cardParams0':'cardParams'">
                                    <span class="hopeParam">本月 <el-divider direction="vertical"></el-divider></span>{{showData.toMouthAmontPay==null?'未计算':showData.toMouthAmontPay}}
                                    <span class="hopeParam" v-if="showData.yuesMouthAmontPay!=0">/ {{showData.yuesMouthAmontPay}}<span class="hopeParam"> <el-divider direction="vertical"></el-divider>上月</span></span>
                                </div>
                            </el-badge>
                            <div class="myNote">本月月订单结算数据</div>
                        </el-card>
                    </div>
                </el-col>
                <el-col span="6">
                    <div class="bg-purple" >
                        <el-card shadow="hover">

                            <el-badge :value="(((showData.toMouthAmontCre-showData.yuesMouthAmontCre)/showData.yuesMouthAmontCre)*100).toFixed(2)+'%'" class="item" :type="((showData.toMouthAmontCre-showData.yuesMouthAmontCre)/showData.yuesMouthAmontCre)>0?'danger':'primary'">
                                <div :class="Number(showData.toMouthAmontCre)>=Number(showData.yuesMouthAmontCre)?'cardParams0':'cardParams'">
                                    <span class="hopeParam">本月 <el-divider direction="vertical"></el-divider></span>{{showData.toMouthAmontCre==null?'未计算':showData.toMouthAmontCre}}
                                    <span class="hopeParam" v-if="showData.yuesMouthAmontCre!=0">/ {{showData.yuesMouthAmontCre}}<span class="hopeParam"> <el-divider direction="vertical"></el-divider>上月</span></span>
                                </div>
                            </el-badge>
                            <div class="myNote">本月订单开单数据</div>
                        </el-card>
                    </div>
                </el-col>
                <el-col span="6">
                    <div class="bg-purple" >
                        <el-card shadow="hover">

                            <el-badge :value="(((showData.toYearAmontPay-showData.yuesYearAmontPay)/showData.yuesYearAmontPay)*100).toFixed(2)+'%'" class="item" :type="((showData.toYearAmontPay-showData.yuesYearAmontPay)/showData.yuesYearAmontPay)>0?'danger':'primary'">
                                <div :class="Number(showData.toYearAmontPay)>=Number(showData.yuesYearAmontPay)?'cardParams0':'cardParams'">
                                    <span class="hopeParam">今年 <el-divider direction="vertical"></el-divider></span>{{showData.toYearAmontPay==null?'未计算':showData.toYearAmontPay}}
                                    <span class="hopeParam" v-if="showData.yuesYearAmontPay!=0">/ {{showData.yuesYearAmontPay}}<span class="hopeParam"> <el-divider direction="vertical"></el-divider>去年</span></span>
                                </div>
                            </el-badge>
                            <div class="myNote">今年订单结算数据</div>
                        </el-card>
                    </div>
                </el-col>
                <el-col span="6">
                    <div class="bg-purple" >
                        <el-card shadow="hover">

                            <el-badge :value="(((showData.toYearAmontCre-showData.yuesYearAmontCre)/showData.yuesYearAmontCre)*100).toFixed(2)+'%'" class="item" :type="((showData.toYearAmontCre-showData.yuesYearAmontCre)/showData.yuesYearAmontCre)>0?'danger':'primary'">
                                <div :class="Number(showData.toYearAmontCre)>=Number(showData.yuesYearAmontCre)?'cardParams0':'cardParams'">
                                    <span class="hopeParam">今年 <el-divider direction="vertical"></el-divider></span>{{showData.toYearAmontCre==null?'未计算':showData.toYearAmontCre}}
                                    <span class="hopeParam" v-if="showData.yuesYearAmontCre!=0">/ {{showData.yuesYearAmontCre}}<span class="hopeParam"> <el-divider direction="vertical"></el-divider>去年</span></span>
                                </div>
                            </el-badge>
                            <div class="myNote">今年订单开单数据</div>
                        </el-card>
                    </div>
                </el-col>

            </el-row>
            <g2column :setCharData="showCharDataList" :heightSize="300"  v-if="show" :showLine="true" :showArea="true" :showColumn="false" :showPoint="false"></g2column>

        </div>
        <div class="container" style="margin-top: 20px">
            <h2><i class="el-icon-date"/>
                <el-divider direction="vertical"></el-divider>指定数据 &nbsp;
                <el-divider direction="vertical"></el-divider>
                <el-date-picker
                        @change="getTeamDataByDay"
                        v-model="value2"
                        type="datetimerange"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :picker-options="pickerOptions"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        align="right">
                </el-date-picker>
                <el-divider direction="vertical"></el-divider>
                <el-radio-group v-model="dom.type" @change="getTeamDataByDay">
                    <el-radio-button :label="0">下单</el-radio-button>
                    <el-radio-button :label="1">结算</el-radio-button>
                </el-radio-group>
                <el-divider direction="vertical"></el-divider>
                <el-radio-group v-model="dom.numType" @change="getTeamDataByDay">
                    <el-radio-button :label="null">金额</el-radio-button>
                    <el-radio-button :label="0">单量</el-radio-button>
                </el-radio-group>
                <el-divider direction="vertical"></el-divider>
                <el-select v-model="dom.productId" filterable placeholder="服务产品选择"  @change="getTeamDataByDay" clearable>
                    <el-option
                            v-for="item in productList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                    </el-option>
                </el-select>
            </h2>
            <div style="margin-top: 10px">
                <el-card shadow="hover">
                    <span class="cardParams0" v-if="dom.numType==null"> <span class="hopeParam">金额：</span>￥ {{orderData.getTotalAmount}}</span>
                    <span class="cardParams0" v-if="dom.numType==0"> <span class="hopeParam">开单数：</span> {{orderData.getTotalAmount}} 单</span>
                    <el-divider direction="vertical"></el-divider>
                    <el-divider direction="vertical"></el-divider>
                    <span class="cardParams0"> <span class="hopeParam">客单价：</span>￥ {{orderData.getDayKdPay}}</span>
                </el-card>
                <el-row :gutter="20">
                    <el-divider >日期分布分析</el-divider>
                    <el-col span="16">
                        <g2column :setCharData="orderData.getTotalAmountByDay" :heightSize="350"  v-if="showColumn" :showLine="false" :showPoint="false" ></g2column>
                    </el-col>
                    <el-col span="8">
                        <g2char :setCharData="orderData.getTotalAmountByDay" :heightSize="350"  v-if="showColumn"></g2char>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-divider >产品分布分析</el-divider>
                    <el-col span="16">
                        <g2column :setCharData="orderData.getAmountByProductName" :heightSize="350"  v-if="showColumn" :showLine="false" :showPoint="false" ></g2column>
                    </el-col>
                    <el-col span="8">
                        <g2char :setCharData="orderData.getAmountByProductName" :heightSize="350"  v-if="showColumn"></g2char>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-divider >时段分布分析</el-divider>
                    <el-col span="16">
                        <g2column :setCharData="orderData.getTotalAmountByHour" :heightSize="350"  v-if="showColumn"  :showLine="true" :showArea="true" :showColumn="false" :showPoint="false"></g2column>
                    </el-col>
                    <el-col span="8">
                        <g2char :setCharData="orderData.getTotalAmountByHour" :heightSize="350"  v-if="showColumn" ></g2char>
                    </el-col>
                </el-row>
<!--                {{orderData}}-->
            </div>
        </div>
    </div>
</template>

<script>
    import g2column from "../agent/char/g2column";
    import g2char from "../agent/char/g2char";
    export default {
        name: "orderData",
        components: {
            "g2column":g2column,
            "g2char":g2char,
        },
        data(){
            return{
                productList:[],
                orderData:{
                    getTotalAmount:0,
                    getTotalAmountByDay:[],
                    getAmountByProductName:[],
                    getTotalAmountByHour:[],
                },
                types: [
                    {
                        id: 0,
                        name: "下单"
                    },
                    {
                        id: 1,
                        name: "结算"
                    }
                ],
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                value2:[],
                dom:{
                    numType:null,
                    type:1,
                    productId:null,
                    startTime:null,
                    endTime:null,
                },
                dateNow:new Date(),
                show:false,
                showColumn:false,
                showCharDataList:[],
                showData:{
                    toDayKdPay:0,
                    yuesDayKdPay:0,
                    toDayAmontPay:0,
                    toHourAmontPay:[],
                    toDayAmontCre:0,
                    toHourAmontCre:[],
                    yuesDayAmontPay:0,
                    yuesHourAmontPay:[],
                    yuesAmontCre:0,
                    yuesHourAmontCre:[],
                    toMouthAmontPay:0,
                    toMouthAmontCre:0,
                    yuesMouthAmontPay:0,
                    yuesMouthAmontCre:0,
                    toYearAmontPay:0,
                    toYearAmontCre:0,
                    yuesYearAmontPay:0,
                    yuesYearAmontCre:0,
                }
            }
        },
        mounted(){
            this.dom.startTime = new Date();
            this.dom.endTime = new Date();
            this.dom.startTime.setTime(this.dom.startTime.getTime() - 3600 * 1000 * 24 * 7);
            this.value2=[this.dateFormat("YYYY-mm-dd HH:MM:SS",this.dom.startTime),this.dateFormat("YYYY-mm-dd HH:MM:SS",this.dom.endTime)]
            this.initData()
            this.getTeamDataByDay()
            this.getProductList()
        },
        methods:{
            dateFormat(fmt, date) {
                let ret;
                const opt = {
                    "Y+": date.getFullYear().toString(),        // 年
                    "m+": (date.getMonth() + 1).toString(),     // 月
                    "d+": date.getDate().toString(),            // 日
                    "H+": date.getHours().toString(),           // 时
                    "M+": date.getMinutes().toString(),         // 分
                    "S+": date.getSeconds().toString()          // 秒
                    // 有其他格式化字符需求可以继续添加，必须转化成字符串
                };
                for (let k in opt) {
                    ret = new RegExp("(" + k + ")").exec(fmt);
                    if (ret) {
                        fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
                    };
                };
                return fmt;
            },
            getTeamDataByDay(){
                this.dom.startTime =this.value2[0];
                this.dom.endTime = this.value2[1];
                this.showColumn=false
                this.$postData("getOrderData",this.dom,null ).then(res => {
                    if (res.status == 200) {
                        this.orderData=res.data
                        this.orderData.getTotalAmountByHour.forEach(v=>{
                            v.response+=':00'
                        })
                        this.showColumn=true
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getProductList(){
                this.$getData("getProductList", null).then(res => {
                    if (res.status == 200) {
                        this.productList=res.data
                    }else {
                        this.$message.error("查询失败，" + res.msg);
                    }

                })
            },
            initData(){
                this.show=false
                this.$getData("initData", null).then(res => {
                    if (res.status == 200) {
                        this.showData=res.data
                        this.showCharDataList=[]
                        this.showData.toHourAmontPay.forEach(v=>{
                            v.type="今日结算"
                            this.showCharDataList.push(v)
                        })
                        this.showData.toHourAmontCre.forEach(v=>{
                            v.type="今日创建"
                            this.showCharDataList.push(v)
                        })
                        this.showData.yuesHourAmontPay.forEach(v=>{
                            v.type="昨日结算"
                            this.showCharDataList.push(v)
                        })
                        this.showData.yuesHourAmontCre.forEach(v=>{
                            v.type="昨日创建"
                            this.showCharDataList.push(v)
                        })
                        this.show=true
                        this.dateNow=new Date();
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
        }

    }
</script>

<style scoped>

    .hopeParam{
        color: #6f7180;
        font-size: 12px;
    }
    .headTitle{
        font-size: 24px;
    }
    .myNote{
        display:-webkit-box;
        text-overflow:ellipsis;
        overflow:hidden;
        -webkit-line-clamp: 1;
        -webkit-box-orient:vertical;
    }
    .bg-purple{
        text-align: center;
    }
    .cardParams{
        font-size: 24px;
        color: #2d8cf0;
    }

    .cardParams0{
        font-size: 24px;
        color: #fa3d32;
    }
</style>
