<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">
                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="会员账户">
                                <el-input
                                        v-model="dto.account"
                                        placeholder="会员账户"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="手机号码">
                                <el-input
                                        v-model="dto.phone"
                                        placeholder="手机号码"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="创建时间">
                                <el-date-picker
                                        v-model="dto.startTime"
                                        type="datetime"
                                        placeholder="开始时间"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" style="text-align: left">
                            <el-form-item label="至">
                                <el-date-picker
                                        v-model="dto.endTime"
                                        type="datetime"
                                        placeholder="结束时间"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="是否转账">
                                <Select filterable style="width: 150px" v-model="dto.recurrence" clearable
                                        @on-change="query">
                                    <Option value="">请选择</Option>
                                    <Option value="false">否</Option>
                                    <Option value="true">是</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="状态">
                                <Select filterable style="width: 150px" v-model="dto.state" clearable
                                        @on-change="query">
                                    <Option value="">请选择</Option>
                                    <Option value="1">已提交</Option>
                                    <Option value="2">未通过</Option>
                                    <Option value="3">已通过</Option>
                                    <Option value="4">已转账</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24" style="text-align:right">
                            <el-form-item>
                                <el-button type="primary" style="width:100px;" @click="query()">搜索</el-button>
                                <el-button type="success" :loading="loadingExcel" @click="exportExcel">导出Excel
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-table :data="list" border class="table"
                      v-loading="loading"
                      ref="multipleTable"
                      tooltip-effect="dark">
                <el-table-column
                        prop="member.account"
                        label="会员账户">
                </el-table-column>
                <el-table-column
                        prop="phone"
                        label="手机号码"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="onOpinion"
                        label="线上意见"
                ></el-table-column>
                <el-table-column
                        prop="lowerOpinion"
                        label="线下意见"
                ></el-table-column>
                <el-table-column
                        prop="createTime"
                        label="创建时间">
                </el-table-column>
                <el-table-column
                        prop="recurrence"
                        label="是否转账">
                    <template slot-scope="scope">
                        <span v-if="scope.row.recurrence===true">是</span>
                        <span v-else>否</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="stateDesc"
                        label="状态"
                ></el-table-column>
                <el-table-column
                        width="170"
                        label="操作">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="edit(scope.row)" type="primary">查看</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      :current="this.dto.pageNum"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="10"/>
            </div>
        </div>
        <Modal v-model="appeal" class="Modal" :width="screenWidth" title="查看"
               :mask-closable="false"
               @on-cancel="getData()">
            <div class="addBody">
                <ActivityYiChoose v-if="appeal" @init-choose="initChooseProject" :id="id"
                                   @close-modal="closeCurrModal"></ActivityYiChoose>
            </div>
            <div slot="footer">
            </div>
        </Modal>
    </div>
</template>
<script>
    import ActivityYiChoose from '@/components/page/wordOrder/choose/ActivityYiChoose.vue'
    export default {
        data() {
            return {
                appeal:false,
                id:null,
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                loadingExcel: false,
                list: [],
                screenWidth: '60%',
                dto: {
                    account: "",
                    phone: "",
                    state: "",
                    startTime: "",
                    endTime: "",
                    recurrence: null,
                    pageSize: 10,
                    pageNum: 1,
                },
            };
        },
        components: {
            'ActivityYiChoose': ActivityYiChoose
        },
        created() {
            this.getData();
        },
        computed: {},
        methods: {
            getData() {
                this.loading = true;
                this.$postData("activityYi_getList", this.dto, {}).then(res => {
                    if (res.status === 200) {
                        this.loading = false;
                       ;
                        this.list = res.data.list;
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.loading = false;
                    }
                });
            },
            query() {
                this.dto.pageNum = 1;
                this.loading = true;
                this.getData();
            },


            // 页码大小
            onPageSizeChange(size) {
                this.loading = true;
                this.dto.pageSize = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                console.log(index);
                this.loading = true;
                this.dto.pageNum = index;
                this.getData();
            },
            exportExcel() {
                this.loadingExcel = true;
                this.$postData("activityYi_exportList", this.dto, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.loadingExcel = false;
                    this.blobExport({
                        tablename: "一搬用户体验报告",
                        res: res
                    });
                });
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
            edit(res) {
                this.id = res.id;
                this.appeal = true;
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.appeal = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
