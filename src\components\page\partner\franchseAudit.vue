<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="9">
            <el-form-item label="申请时间">
              <el-date-picker v-model="form.days" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="经营者名称">
              <el-input
                  clearable
                  v-model="form.name"
                  placeholder="经营者名称"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="介绍人">
              <el-input
                  clearable
                  v-model="form.introducer"
                  placeholder="介绍人"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
<!--            <el-button type="info"-->
<!--                       :loading="loadingExcel"-->
<!--                       @click="download"-->
<!--                       icon="el-icon-download">导出-->
<!--            </el-button>-->
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="店铺管理者手机号">
              <el-input
                  clearable
                  v-model="form.adminPhone"
                  placeholder="店铺管理者手机号"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="店铺管理者姓名">
              <el-input
                  clearable
                  v-model="form.adminName"
                  placeholder="店铺管理者姓名"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="店铺名称">
              <el-input
                  clearable
                  v-model="form.storeName"
                  placeholder="店铺名称"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="公司名称">
              <el-input
                  clearable
                  v-model="form.companyName"
                  placeholder="公司名称"
                  style="width:160px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="经营类目">
              <el-select style="width:160px" v-model="form.manageType" clearable placeholder="请选择经营类目">
                <el-option
                    v-for="item in manageTypeList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item  label="认证类型">
              <el-select style="width:160px" v-model="form.authenticationType" clearable placeholder="认证类型">
                <el-option
                    v-for="item in channel"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item  label="状态">
              <el-select style="width:160px" v-model="form.status" clearable placeholder="请选择状态">
                <el-option v-for="item in options"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="填报记录">
          <el-table :data="logList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column prop="creatDate" label="申请时间" width="90"></el-table-column>
            <el-table-column prop="name" label="营业者名称" width="100"></el-table-column>
            <el-table-column prop="hometown" label="经营者归属地" width="100"></el-table-column>
            <el-table-column prop="idCard" label="经营者身份证号码" width="120"></el-table-column>
            <el-table-column prop="idPositive" label="经营者身份证人像面" width="130">
              <template slot-scope="scope">
                <img v-image-preview :src="scope.row.idPositive"  style="width: 100%" />
              </template>
            </el-table-column>
            <el-table-column prop="idBack" label="经营者身份证国徽面" width="130">
              <template slot-scope="scope">
                <img v-image-preview :src="scope.row.idBack"  style="width: 100%"/>
              </template>
            </el-table-column>
            <el-table-column prop="authenticationType" label="认证类型" width="100">
              <template slot-scope="scope">
                <span>{{scope.row.authenticationType==1?'个体':scope.row.authenticationType==2?'企业':'未知'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="selfIntroduction" label="个人介绍" width="100"></el-table-column>
            <el-table-column prop="nowAdd" label="所在地区" width="100"></el-table-column>
            <el-table-column prop="career" label="从事职业" width="100"></el-table-column>
            <el-table-column prop="income" label="年收入范围" width="100"></el-table-column>
            <el-table-column prop="companyName" label="公司名称" width="100"></el-table-column>
            <el-table-column prop="businessLicenseUrl" label="营业执照" width="120">
              <template slot-scope="scope">
                <img v-image-preview :src="scope.row.businessLicenseUrl"  style="width: 100%"/>
              </template>
            </el-table-column>
            <el-table-column prop="creditCode" label="社会统一信用代码" width="140"></el-table-column>
            <el-table-column prop="doBusinessTime" label="营业期限" width="100"></el-table-column>
            <el-table-column prop="doBusinessAddress" label="经营地址" width="120"></el-table-column>
            <el-table-column prop="introducer" label="介绍人" width="80"></el-table-column>
            <el-table-column prop="storeName" label="店铺名称" width="100"></el-table-column>
            <el-table-column prop="storeLogo" label="店铺logo" width="100">
              <template slot-scope="scope">
                <img v-image-preview :src="scope.row.storeLogo"  style="width: 100%"/>
              </template>
            </el-table-column>
            <el-table-column prop="manageType" label="经营类目" width="100"></el-table-column>
            <el-table-column prop="adminName" label="店铺管理者姓名" width="110"></el-table-column>
            <el-table-column prop="adminPhone" label="店铺管理者手机号" width="120"></el-table-column>
            <el-table-column prop="channel" label="渠道" width="80"></el-table-column>
            <el-table-column prop="disposeId" label="处理人" width="80"></el-table-column>
            <el-table-column prop="disposeTime" label="处理时间" width="80"></el-table-column>
            <el-table-column prop="remark" label="未通过原由" width="100"></el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status==='0'" type="danger">未通过</el-tag>
                <el-tag v-if="scope.row.status==='2'" type="success">已通过</el-tag>
                <el-tag v-else-if="scope.row.status==='1'" type="warning">未处理</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="100">
              <template slot-scope="scope">
                <el-button :disabled="scope.row.status==='2'" @click.native.prevent="showRow(scope.row.franchiseId,scope.row.status)" type="text" size="small">操作
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
        <el-dialog :visible.sync="dialogVisible">
          <el-form>
            <el-form-item label="处理结果">
              <el-tag v-if="dom.status==='0'" type="danger">未通过</el-tag>
              <el-tag v-if="dom.status==='1'" type="warning">未处理</el-tag>
              <el-tag v-if="dom.status==='2'" type="success">已通过</el-tag>
            </el-form-item>
          </el-form>
          <el-button type="primary" @click="innerVisible = true">更新</el-button>
          <br><br>
        </el-dialog>
        <el-dialog title="处理" :visible.sync="innerVisible" append-to-body center>
          <el-form>
            <el-form-item label="状态">
              <el-radio-group v-model="dom.status">
                <el-radio label="0">不通过</el-radio>
                <el-radio label="2">通过</el-radio>
              </el-radio-group>
            </el-form-item>
            <br>
            <el-form-item v-if="dom.status==='0'" label="未通过原由">
              <el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="dom.remark"> </el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="addFranchiseRegisterHandler">确定</el-button>
          </div>
        </el-dialog>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import moment from "moment";

export default {
  name: "register",
  data() {
    return {
      channel: [{id:1,name:"个人"},{id:2,name:"企业"}],
      manageTypeList: [{id:1,name:"招工合作"},{id:2,name:"线索开发"},{id:4,name:"业务分销"},{id:4,name:"培训开发"}],
      logList: [],
      handler: [],
      loadingExcel: false,
      dialogVisible: false,
      innerVisible: false,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      form: {
        days: [],
        status: null,
        companyName: null,
        startTime: null,
        endTime: null,
        name: null,
        introducer: null,
        adminPhone: null,
        adminName: null,
        authenticationType: null,
        manageType: null,
        current: 1,
        size: 10
      },
      dom: {
        status: null,
        disposeId: localStorage.getItem("id"),
        id: null,
        registerId: null,
        remark: null,
      },
      options: [{
        value: '0',
        label: '未通过'
      }, {
        value: '1',
        label: '未处理'
      }, {
        value: '2',
        label: '已通过'
      }
      ],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },
  created() {
    this.getData();
  },
  methods: {
    download() {
      this.loadingExcel = true;
      this.$postData("downloadFranchiseRegister", this.form, {responseType: "arraybuffer"}).then(res => {
        this.loadingExcel = false;
        this.blobExport({
          tablename: "合伙人信息",
          res: res
        });
      })
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
    showRow(franchiseId, status) {
      this.dialogVisible = true;
      this.dom.status = status;
      this.dom.id = franchiseId;
    },
    addFranchiseRegisterHandler() {
      this.innerVisible = false;
      this.$postData("updateFranchiseState", this.dom).then(res => {
        if (res.code === 0) {
          this.$message({message: '编辑成功', type: 'success'});
        } else {
          this.$message({message: '编辑失败', type: 'warning'});
        }
        this.getData();
      })
      this.dialogVisible = false;
    },
    // 页码大小
    onPageSizeChange(size) {
      this.form.size = size;
      this.getData();
    },
    onChange(index) {
      this.form.current = index;
      this.getData();
    },
    getData() {
      this.form.startTime = null;
      this.form.endTime = null;
      if (this.form.days != null && this.form.days.length > 0) {
        this.form.startTime = moment(this.form.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.form.days[1]).format("YYYY-MM-DD")
      }
      this.$postData("getFranchiseMsg", this.form, {}).then(res => {
        if (res.status == 200) {
          this.logList = res.data.records;
          this.logList.forEach(v => {
            v.status = v.status.toString()
          })
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    }
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
