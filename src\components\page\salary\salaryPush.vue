<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col :span="24">
                    <div style="font-size: 14px;
                        margin-bottom: 10px;">支持多成员多部门
                        <el-button size="small" style="margin-left: 20px;" type="success"
                                   @click="push">
                            全员推送
                        </el-button>
                    </div>
                </Col>
            </Row>
            <Row>
                <Col :span="7">
                    <div>
                        <el-input
                                placeholder="输入关键字进行过滤"
                                v-model="filterText">
                        </el-input>
                    </div>
                    <div style="border-right: 1px #000000 dotted;
                    border-bottom: 1px #000000 dotted;
                    height:400px;
                         overflow-x: hidden;">
                        <el-tree
                                :props="defaultProps"
                                :data="departmentList"
                                show-checkbox
                                node-key="id"
                                :expand-on-click-node="false"
                                :filter-node-method="filterNode"
                                :render-content="renderContent">
                        </el-tree>
                    </div>


                </Col>
                <Col :span="10">

                </Col>
            </Row>

        </div>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                defaultProps: {
                    children: 'children',
                    label: 'label'
                },
                data: {account: localStorage.getItem("account")},
                fileList: [],
                loading: false,
                departmentList: []
            }
        },
        components: {},
        created: function () {
            this.getDepartmentList();
        },
        watch: {
            filterText(val) {
                this.$refs.tree.filter(val);
            }
        },
        methods: {
            filterNode(value, data) {
                if (!value) return true;
                return data.label.indexOf(value) !== -1;
            },
            getDepartmentList() {
                this.$getData("salarySetUp_getDepartmentList").then(res => {
                    if (res.status === 200) {
                        this.departmentList = res.data
                    }

                });
            },
            onChange(file, fileList) {
                console.log(fileList);
                this.fileList = fileList
            },
            handleError(file, fileList) {
                this.loading = false;
                console.log(file);
                this.$message({
                    type: 'success',
                    message: '导入失败!'
                });
            },
            handlePictureCardPreview() {
                this.download("https://xyj-pic.oss-cn-shenzhen.aliyuncs.com/TryProduct/Banner/1607928750827333333.xlsx");
            },
            onExcees() {
                this.$Message.success('只可上传一个文件');
            },
            handleRemove(file) {
                this.fileList = [];
            },
            handlePreview(file) {
                this.loading = false;
                console.log(file);
                this.$message({
                    type: 'success',
                    message: '导入成功!'
                });
            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                console.log(322323);
                this.$emit('init-choose', "");
                this.logs = [];
            },

        },

    }
</script>


<style>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;
        height: 500px;

    }

    .el-upload--text {
        background-color: #fff;
        border: none !important;
        border-radius: 0px;
        box-sizing: border-box;
        width: auto !important;
        height: auto !important;
        text-align: center;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

</style>

