<template>
    <div>
        <Drawer
                title="详情"
                v-model="value3"
                width="50%"
                :mask-closable="false"
                @on-close="chooseThisModel"
                :styles="styles"
        >
            <Form :model="formItem" :label-width="80" label-position="left">
                <Tabs v-model="current">
                    <TabPane label="已接单" name="1">
                        <div class="searchDiv">
                            <FormItem label="订单编号" prop="billNo">
                                <Input placeholder="订单编号"
                                       disabled
                                       @on-change="getByOrder"
                                       style="width: 80%"
                                       v-model="formItem.billNo">
                                </Input>
                            </FormItem>
                            <FormItem label="服务项目" prop="productName">
                                <Input placeholder="服务项目" style="width: 80%"
                                       disabled
                                       v-model="formItem.productName">
                                </Input>
                            </FormItem>
                            <FormItem label="违纪人员" prop="employeeNames">
                                <Row>
                                    <Col span="19">
                                        <Input placeholder="违纪人员" style="width: 100%"
                                               disabled
                                               v-model="formItem.employeeNames">
                                        </Input>
                                    </Col>
                                    <Col span="4">
                                        <!--<Button type="primary" @click="userModal=true">选择</Button>-->
                                    </Col>
                                </Row>
                            </FormItem>
                            <FormItem label="违纪类型" prop="type">
                                <Select v-model="formItem.type" style="width:80%">
                                    <Option v-for="item in typeList" :value="item.id">{{ item.text}}</Option>
                                </Select>
                            </FormItem>
                            <FormItem label="备注">
                                <Input v-model="formItem.remark" type="textarea" :rows="3"
                                       style="width: 80%"
                                       placeholder="备注"/>
                            </FormItem>
                        </div>
                    </TabPane>
                    <TabPane label="审核阶段" name="2">
                        <div class="searchDiv">
                            <FormItem label="核实过程" prop="content">
                                <Input v-model="formItem.content" type="textarea" :rows="3"
                                       style="width: 80%"
                                       placeholder="核实过程"/>
                            </FormItem>
                            <FormItem label="上传材料">
                                <Upload
                                        multiple
                                        :action="imgUrl"
                                        :on-success="onSuccess"
                                        :on-preview="handlePictureCardPreview"
                                        :on-remove="handleRemove"
                                        :before-upload="before"
                                        :on-exceed="onExcees"
                                        :default-file-list="filesList"
                                        :max-size="10240"
                                        :data="files">
                                    <Button icon="ios-cloud-upload-outline">点击上传</Button>
                                </Upload>
                            </FormItem>
                            <FormItem label="违纪判定">
                                <RadioGroup v-model="formItem.violation">
                                    <Radio label="1">有效违纪</Radio>
                                    <Radio label="2">无效投诉</Radio>
                                </RadioGroup>
                            </FormItem>
                        </div>
                    </TabPane>
                    <TabPane label="处理阶段" name="3">
                        <div class="searchDiv">
                            <FormItem label="处罚等级">
                                <Select v-model="formItem.violationEm.level" style="width:80%">
                                    <Option v-for="item in levelList" :value="item.id">{{ item.text}}</Option>
                                </Select>
                            </FormItem>
                            <FormItem label="违纪罚金">
                                <Input v-model="formItem.violationEm.offenceFine"
                                       style="width: 80%"
                                       @on-blur="offenceBlur"
                                       placeholder="违纪罚金"/>
                            </FormItem>
                            <FormItem label="停工处罚">
                                <Select v-model="formItem.violationEm.punishment" style="width:80%">
                                    <Option v-for="item in periodList" :value="item.id">{{ item.text}}</Option>
                                </Select>
                            </FormItem>
                            <FormItem label="行政扣分">
                                <el-input-number v-model="formItem.violationEm.changeScore"
                                                 @change="handleChange"
                                                 :min="-100" :max="0">
                                </el-input-number>
                            </FormItem>
                            <FormItem label="是否回炉培训">
                                <RadioGroup v-model="formItem.train">
                                    <Radio label="true">是</Radio>
                                    <Radio label="false">否</Radio>
                                </RadioGroup>
                            </FormItem>
                            <FormItem label="推荐案例">
                                <RadioGroup v-model="formItem.case">
                                    <Radio label="true">是</Radio>
                                    <Radio label="false">否</Radio>
                                </RadioGroup>
                            </FormItem>
                        </div>
                    </TabPane>
                </Tabs>
            </Form>
            <div class="demo-drawer-footer">
                <Button style="margin-right: 8px" @click="chooseThisModel">取消</Button>
                <Button type="primary" @click="update">确定</Button>
            </div>
        </Drawer>
    </div>
</template>
<script>
    export default {
        props: ['id'],
        data() {
            return {
                current: "0",
                filesList: [],
                files: {
                    fileName: "Ts/"
                },
                value3: true,
                imgUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
                styles: {
                    height: 'calc(100% - 55px)',
                    overflow: 'auto',
                    paddingBottom: '53px',
                    position: 'static'
                },
                periodList: [],
                levelList: [],
                typeList: [],
                formItem: {
                    id: this.id,
                    state: 0,
                    billNo: null,
                    productName: null,
                    type: null,
                    file: null,
                    employeeNames: null,
                    remark: null,
                    content: null,
                    violationEm: {
                        employeeNo: null,
                        employeeName: null,
                        changeScore: null,
                        level: null,
                        punishment: null,
                        offenceFine: null,
                    },
                    violation: "1",
                    case: "false",
                    train: "false"
                },
            }
        },
        created: function () {
            this.getPeriodList();
            this.getLevelList();
            this.getTypeList();
            this.getById()
        },
        methods: {
            offenceBlur() {
                if (this.formItem.violationEm.offenceFine === '' &&
                    this.formItem.violationEm.offenceFine == null) {
                    return
                }
                if (this.formItem.violationEm.offenceFine.indexOf("-") < 0) {
                    this.formItem.violationEm.offenceFine = "-" + this.formItem.violationEm.offenceFine
                }
            },
            handleChange(value) {
                this.formItem.violationEm.changeScore = value
            },
            getById() {
                this.$getData("violation_getById", {
                    id: this.id
                }).then(res => {
                    if (res.status === 200) {
                        this.formItem = res.data;
                        this.formItem.violation = this.formItem.violation + "";
                        this.formItem.train = this.formItem.train + "";
                        this.formItem.case = this.formItem.case + "";
                        if (this.formItem.state > 3) {
                            this.current = "3"
                        } else {
                            this.current = this.formItem.state+""
                        }
                        this.getFiles(res.data)
                    }
                })
            },
            getFiles(data) {
                if (data.file) {
                    let split = data.file.split(",");
                    for (let i = 0; i < split.length; i++) {
                        let res = decodeURIComponent(split[i]);
                        let file = {
                            name: res.substring(res.lastIndexOf("/") + 1, res.length),
                            url: res
                        };
                        this.filesList.push(file);
                    }
                }
            },
            update() {
                if (Number(this.current) >= this.formItem.state) {
                    this.formItem.state++
                }
                if (Number(this.current) === 3) {
                    this.formItem.state = 7
                }
                this.$postData("violation_update", this.formItem).then(res => {
                        if (res.status === 200) {
                            this.formItem.violationEm = res.data.violationEm;
                            this.chooseThisModel()
                        } else {
                            this.$message.error("保存失败，" + res.msg);
                        }
                    }
                )
            },
            getByOrder() {
                this.$getData("getByOrderNo", {
                    orderNo: this.formItem.billNo
                }).then(res => {
                    if (res.status === 200) {
                        if (res.data) {
                            this.formItem.productName = res.data.productName;
                        } else {
                            this.formItem.productName = null
                        }
                    }
                })
            },
            chooseThisModel() {
                this.value3 = false;
                this.$emit('init-choose', "");
            },
            getTypeList() {
                this.$postUrl("get_dict", 130, null, {}).then(res => {
                    if (res.status === 200) {
                        this.typeList = res.data;
                    }
                })
            },
            getPeriodList() {
                this.$postUrl("get_dict", 123, null, {}).then(res => {
                    if (res.status === 200) {
                        this.periodList = res.data;
                    }
                })
            },
            getLevelList() {
                this.$postUrl("get_dict", 118, null, {}).then(res => {
                    if (res.status === 200) {
                        this.levelList = res.data
                    }
                })
            },
            onSuccess(res) {
               ;
                if (res.status === 200) {
                    this.$Message.success('上传成功');
                    if (this.formItem.file) {
                        this.formItem.filee = this.formItem.file + "," + res.data;
                    } else {
                        this.formItem.file = res.data;
                    }
                    let url = decodeURIComponent(res.data);
                    let file = {
                        name: url.substring(url.lastIndexOf("/") + 1, url.length),
                        url: url
                    };
                    this.filesList.push(file);
                }
            },
            handlePictureCardPreview(file) {
                console.log(file);
                this.download(file.url, file.name);
            },
            onExcees() {
                this.$Message.success('只可上传一个文件');
            },
            download(src, fileName) {
                let x = new XMLHttpRequest();
                x.open("GET", src, true);
                x.responseType = 'blob';
                x.onload = function (e) {
                    let url = window.URL.createObjectURL(x.response);
                    let a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;
                    a.click();
                };
                x.send();
            },
            handleRemove(file) {
                console.log(file);
                let names = this.formItem.file.split(",");
                for (let i = 0; i < names.length; i++) {
                    if (file.url === decodeURIComponent(names[i])) {
                        names.splice(i, 1);
                        this.formItem.file = names.join(",");
                        break;
                    }
                }
                for (let i = 0; i < this.filesList.length; i++) {
                    if (file.url === decodeURIComponent(this.filesList[i].url)) {
                        this.filesList.splice(i, 1);
                        break;
                    }
                }
            },
            before() {
                if (this.formItem.file) {
                    let list = this.formItem.file.split(",");
                    const check = list.length < 2;
                    if (!check) {
                        this.$Notice.warning({
                            title: '最多可上传2个附件,多文件请打包上传'
                        });
                    }
                    return check;
                }
            }
        }
    }
</script>
<style>
    .demo-drawer-footer {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        border-top: 1px solid #e8e8e8;
        padding: 10px 16px;
        text-align: left;
        background: #fff;
    }

    .searchDiv {
        width: 60%;
        margin: 20px auto;
        font-weight: bold;
        font-size: 17px !important;
    }
</style>
