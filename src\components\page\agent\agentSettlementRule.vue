<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">
                    <div style="color: #f00f14">注意：日期搜索按照如：查询1月1号的结算内容，开始日期为1月1号，结束日期为1月2号。并且数据将不再以时间排序。</div>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="现经纪人">
                                <el-input
                                        v-model="model.agentName"
                                        placeholder="现经纪人"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="原始经纪人">
                                <el-input
                                        v-model="model.originAgentName"
                                        placeholder="原始经纪人"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="门店">
                                <Select filterable style="width:190px" v-model="model.storeId">
                                    <Option value="">请选择</Option>
                                    <Option value="1">平台</Option>
                                    <Option v-for="(item,index) in storeList" :value="item.id" :key="index">
                                        {{item.storeName}}
                                    </Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="客户手机号">
                                <el-input
                                        v-model="model.memberPhone"
                                        placeholder="客户手机号"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="结算日期">
                                <el-date-picker
                                        style="width: 290px;"
                                        v-model="showDate"
                                        type="daterange"
                                        :picker-options="pickerOptions"
                                        range-separator="~"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        value-format="yyyy-MM-dd"
                                        align="right">
                                </el-date-picker>&nbsp;
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="合同截止日期">
                                <el-date-picker
                                        style="width: 290px;"
                                        v-model="contractEndDate"
                                        type="daterange"
                                        :picker-options="pickerOptions"
                                        range-separator="~"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        value-format="yyyy-MM-dd"
                                        align="right">
                                </el-date-picker>&nbsp;
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="8">&emsp;</el-col>
                        <el-col :span="8">&emsp;</el-col>
                        <el-col :span="8">
                            <el-form-item>
                                <el-button type="success" round @click="query()" v-loading="loading"
                                           :disabled="loading">搜索
                                </el-button>
                                <el-button type="info" round @click="re()" v-loading="loading" :disabled="loading">重置
                                </el-button>
                                <el-button type="primary" round @click="showAgent=true" v-loading="loading"
                                           :disabled="loading">分析
                                </el-button>
                                <el-button type="default" round @click="exportExcel()" v-loading="loading"
                                           :disabled="loading">导出
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-row>
                <center>
                    <el-col :span="8">
                        <el-card shadow="hover">
                            营业收入：<span class="allAmount">￥{{(settlementData.allAmount).toFixed(2)}}</span>
                            <span style="color: #6f7180">(均价：￥<span style="color: #f00f14">{{(settlementData.allAmount / pageInfo.total).toFixed(2)}}</span>)</span>
                        </el-card>
                    </el-col>

                    <el-col :span="8">
                        <el-card shadow="hover">
                            提成收入：<span class="allAmount">￥{{(settlementData.allBonus).toFixed(2)}}</span>
                            <span style="color: #6f7180">(均价：￥<span style="color: #f00f14">{{(settlementData.allBonus / pageInfo.total).toFixed(2)}}</span>)</span>
                            <!--                        抽成比例：￥<span class="allAmount">{{(settlementData.allBonus/settlementData.allAmount).toFixed(2)}}%</span>-->
                        </el-card>
                    </el-col>
                    <el-col :span="8">
                        <el-card shadow="hover">
                            月度奖励金：<span class="allAmount">￥{{(settlementData.monthAllAmount == null ? 0 : settlementData.monthAllAmount).toFixed(2)}}</span>
                        </el-card>
                    </el-col>
                </center>
            </el-row>

            <el-table :data="list" :header-cell-style="{background:'#ddd'}" @sort-change="sortChange" border
                      class="table" ref="multipleTable" @cell-dblclick="" v-loading="loading"
                      element-loading-text="拼命加载中"
                      element-loading-spinner="el-icon-loading"
                      element-loading-background="rgba(0, 0, 0, 0.8)">
                <el-table-column
                        fixed="left"
                        label="操作"
                        width="80">
                    <template slot-scope="scope">
                        <el-button type="primary" @click="agentSettlementByBillNo(scope.row.billNo)"
                                   v-if="scope.row.sumBonus==null">结算
                        </el-button>
                        <el-button type="info" @click="" v-if="scope.row.sumBonus!=null"
                                   @click="dom=scope.row,showInfo=true">查看
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="sumBonus"
                        label="抽成奖金"
                        width="100"
                >
                    <template slot-scope="scope">
                        {{scope.row.sumBonus==null?'暂未结算':'￥'+scope.row.sumBonus}}
                    </template>
                </el-table-column>
                <el-table-column
                        prop="no"
                        label="合同编号"
                        sortable="custom"
                        width="105"
                >
                    <template slot-scope="scope">
                        <el-link type="primary" @click="showContract(scope.row.contractId)">{{scope.row.no}}</el-link>
                        ({{scope.row.contractDay}}天)
                    </template>
                </el-table-column>
                <el-table-column
                        prop="billNo"
                        label="订单号"
                        sortable="custom"
                        width="150"
                >
                    <template slot-scope="scope">
                        <el-link type="primary" @click="toBillNo(scope.row.billNo)">{{scope.row.billNo}}</el-link>
                    </template>
                </el-table-column>

                <el-table-column
                        prop="targetBillNo"
                        label="子订单"
                        sortable="custom"
                        width="150"
                ></el-table-column>
                <el-table-column
                        prop="memberPhone"
                        label="客户电话"
                        sortable="custom"
                        width="130"
                >
                    <template slot-scope="scope">
                        <el-link type="primary" @click="toMember(scope.row.memberId)">{{scope.row.memberPhone}}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="memberName"
                        label="客户名称"
                        sortable="custom"
                        width="130"
                >
                </el-table-column>
                <el-table-column
                        prop="memberOrderNum"
                        label="客户保姆订单数"
                        width="130"
                >
                </el-table-column>
                <el-table-column
                        prop="memberAdress"
                        label="服务地址"
                        sortable="custom"
                        width="300"
                ></el-table-column>
                <el-table-column
                        prop="orderCreateTime"
                        label="创建日期"
                        sortable="custom"
                        width="160"
                ></el-table-column>
                <el-table-column
                        prop="paySettlementTime"
                        label="结算日期"
                        sortable="custom"
                        width="160"
                ></el-table-column>
                <el-table-column
                        prop="agentName"
                        label="现经纪人"
                        sortable="custom"
                        width="120"
                ></el-table-column>
                <el-table-column
                        prop="originAgentName"
                        label="原始经纪人"
                        sortable="custom"
                        width="150"
                ></el-table-column>
                <el-table-column
                        prop="realAmount"
                        label="业绩金额"
                        sortable="custom"
                        width="130"
                ></el-table-column>
                <el-table-column
                        prop="amount"
                        label="订单金额"
                        sortable="custom"
                        width="130"
                ></el-table-column>
                <el-table-column
                        prop="realWage"
                        label="阿姨工资"
                        sortable="custom"
                        width="130"
                ></el-table-column>
                <el-table-column
                        prop="productName"
                        label="服务产品"
                        sortable="custom"
                        width="150"
                ></el-table-column>
                <el-table-column
                        prop="serviceType"
                        label="服务类型"
                        sortable="custom"
                        width="150"
                ></el-table-column>
                <el-table-column
                        prop="channel"
                        label="开发人"
                        sortable="custom"
                        width="120"
                ></el-table-column>
                <el-table-column
                        prop="crePerson"
                        label="开单人"
                        sortable="custom"
                        width="120"
                ></el-table-column>
                <el-table-column
                        prop="serviceStarDate"
                        label="合同开始时间"
                        sortable="custom"
                        width="160"
                ></el-table-column>
                <el-table-column
                        prop="serviceEndDate"
                        label="合同截止日期"
                        sortable="custom"
                        width="160"
                ></el-table-column>
                <el-table-column
                        prop="serviceNum"
                        label="更换次数"
                        sortable="custom"
                        width="120"
                ></el-table-column>
                <el-table-column
                        prop="employeeName"
                        label="保姆名称"
                        sortable="custom"
                        width="180"
                >
                    <template slot-scope="scope">
                        <el-link type="primary" @click="toBaomuInfo(scope.row.employeeId)">{{scope.row.employeeName}} -
                            {{scope.row.employeeNo}}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="employeePhone"
                        label="保姆号码"
                        sortable="custom"
                        width="130"
                ></el-table-column>


                <!--                <el-table-column-->
                <!--                        label="操作"-->
                <!--                        fixed="right"-->
                <!--                        min-width="150">-->
                <!--                    <template slot-scope="scope">-->
                <!--                      -->
                <!--                   </template>-->

                <!--                </el-table-column>-->

            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>

        <el-drawer
                size="80%"
                :with-header="false"
                :visible.sync="drawer"
                direction="rtl"
                :before-close="handleClose">
            <contract-info :contractId="contractId" :contract-id="contractId" v-if="contractId!==null"
                           style="width: 100%"></contract-info>
        </el-drawer>
        <el-drawer
                size="70%"
                :with-header="false"
                :visible.sync="showInfo"
                direction="rtl"
                :before-close="InfohandleClose">
            <agentBonusInfo :dom="dom" v-if="showInfo"></agentBonusInfo>
        </el-drawer>
        <el-drawer
                size="70%"
                style="min-width: 700px;"
                :with-header="false"
                :visible.sync="showAgent"
                direction="rtl"
        >

            <div style="padding: 10px;">
                <el-row>
                    <center>
                        <el-col :span="8">
                            <el-card shadow="hover">
                                营业收入: <span class="allAmount">￥{{(settlementData.allAmount).toFixed(2)}}</span>
                                <span style="color: #6f7180">(均价: ￥<span style="color: #f00f14">{{(settlementData.allAmount/pageInfo.total).toFixed(2) }}</span>)</span>
                            </el-card>
                        </el-col>

                        <el-col :span="9" @click="showAgent=true">
                            <el-card shadow="hover">
                                提成收入: <span class="allAmount">￥{{(settlementData.allBonus).toFixed(2)}}</span>
                                <span style="color: #6f7180">(均价: ￥<span style="color: #f00f14">{{(settlementData.allBonus/pageInfo.total).toFixed(2)}}</span>)</span>
                            </el-card>
                        </el-col>
                        <el-col :span="7">
                            <el-card shadow="hover">
                                月度奖励金: <span class="allAmount">￥{{(settlementData.monthAllAmount == null ? 0 : settlementData.monthAllAmount).toFixed(2)}}</span>
                            </el-card>
                        </el-col>
                    </center>
                </el-row>
                <el-tabs v-model="activeName" type="card">
                    <el-tab-pane label="收入分布" name="first">
                        <div>
                            <el-button type="primary" size="mini" @click="exportAgentWages()">导出经纪人工资</el-button>
                            <span style="color: red;"> 注意：工资信息与页面条件搜索相关</span>
                        </div>
                        <el-row>
                            <el-col :span="12">
                                <h2>营业收入</h2>
                                <el-table
                                        v-loading="loading"
                                        height="700"
                                        :data="settlementData.agentsettlementList"
                                        stripe
                                        style="width: 100%;">
                                    <el-table-column
                                            label="序号"
                                            type="index"
                                            align="center"
                                            width="80">
                                    </el-table-column>
                                    <el-table-column
                                            prop="response"
                                            label="经纪人"
                                            width="80">
                                    </el-table-column>
                                    <el-table-column
                                            prop="evalTotalAmount"
                                            label="评价业绩"
                                            align="right"
                                            width="100">
                                    </el-table-column>
                                    <el-table-column
                                            prop="refundTotalAmount"
                                            label="退款业绩"
                                            align="right"
                                            width="100">
                                    </el-table-column>
                                    <el-table-column
                                            prop="bonus"
                                            align="right"
                                            label="业绩金额">
                                        <template slot-scope="scope">
                                            <div style="color: red">￥{{scope.row.num}}</div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                            prop="bonus"
                                            align="right"
                                            label="实际业绩">
                                        <template slot-scope="scope">
                                            <div style="color: red">￥{{scope.row.actualAmount}}</div>
                                        </template>
                                    </el-table-column>
                                </el-table>

                            </el-col>
                            <el-col :span="12">
                                <h2>提成收入（非月份提成）</h2>
                                <el-table
                                        v-loading="loading"
                                        height="700"
                                        :data="settlementData.agentList"
                                        stripe
                                        style="width: 100%;">
                                    <el-table-column
                                            label="序号"
                                            type="index"
                                            width="50">
                                    </el-table-column>
                                    <el-table-column
                                            prop="response"
                                            label="经纪人"
                                            width="100">
                                    </el-table-column>
                                    <!--<el-table-column-->
                                    <!--        prop="bonus"-->
                                    <!--        label="提成">-->
                                    <!--    <template slot-scope="scope">-->
                                    <!--        <div style="color: red">￥{{scope.row.num}}</div>-->
                                    <!--    </template>-->
                                    <!--</el-table-column>-->
                                    <el-table-column
                                            prop="percentage"
                                            label="提成">
                                        <template slot-scope="scope">
                                            <div style="color: red">￥{{scope.row.percentage}}</div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                            prop="realPercentage"
                                            label="实际提成">
                                        <template slot-scope="scope">
                                            <div style="color: red">￥{{scope.row.realPercentage}}</div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                            prop="bonus"
                                            label="招聘奖金">
                                        <template slot-scope="scope">
                                            <div style="color: red">￥{{scope.row.bonus}}</div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                            prop="needsBonus"
                                            label="线索奖金">
                                        <template slot-scope="scope">
                                            <div style="color: red">￥{{scope.row.needsBonus}}</div>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-col>
                        </el-row>

                    </el-tab-pane>
                    <el-tab-pane label="分析" name="second">
                        <el-row>
                            <el-col :span="12">
                                <h2>营业收入</h2>
                                <g2char :setCharData="settlementData.agentsettlementList" :heightSize="700"
                                        v-if="activeName=='second'"></g2char>
                            </el-col>
                            <el-col :span="12">
                                <h2>提成收入（非月份提成）</h2>
                                <g2char :setCharData="settlementData.agentList" :heightSize="700"
                                        v-if="activeName=='second'"></g2char>
                            </el-col>
                        </el-row>

                    </el-tab-pane>
                    <el-tab-pane label="经纪人月度奖金" name="therd">
                        <el-row>
                            <el-col :span="12">
                                <h2>提成收入</h2>
                                <el-table
                                        height="700"
                                        :data="settlementData.monthAll"
                                        stripe
                                        style="width: 100%;">
                                    <el-table-column
                                            type="index"
                                            width="50">
                                    </el-table-column>
                                    <el-table-column
                                            prop="bonus"
                                            label="奖金"
                                            width="100">
                                    </el-table-column>
                                    <el-table-column
                                            prop="realName"
                                            label="经纪人"
                                            width="100">
                                    </el-table-column>
                                    <el-table-column
                                            prop="detail"
                                            label="说明">
                                    </el-table-column>
                                </el-table>
                            </el-col>
                        </el-row>
                    </el-tab-pane>

                </el-tabs>

            </div>
        </el-drawer>

    </div>
</template>

<script>
    import g2char from "./char/g2char";
    import memberInfo from "./info/memberInfo";
    import order from "./info/order";
    import contractInfo from "./info/contractInfo";
    import agentBonusInfo from "./info/agentBonusInfo";

    export default {
        data() {
            return {
                activeName: 'first',
                showAgent: false,
                showInfo: false,
                dom: null,
                loading: true,
                contractId: null,
                drawer: false,
                storeList: [],
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                showDate: null,
                contractEndDate: null,
                doid: null,
                tableData: [],
                pageSizeOpts: [10, 20, 30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                screenWidth: '80%',//新增对话框 宽度
                doscreenWidth: '50%',

                show: {},
                model: {
                    storeId: null,
                    orderBy: null,
                    startTime: null,
                    endTime: null,
                    contractStartTime: null,
                    contractEndTime: null,
                    agentId: null,
                    agentName: null,
                    originAgentName: null,
                    memberPhone: null,
                    current: 1,
                    size: 10
                },
                update: {
                    id: null,
                    realName: null,

                },
                settlementData: {
                    allBonus: 0.00,
                    allAmount: 0.00,
                    monthAllAmount: 0.00,
                    monthAll: [],
                    agentsettlementList: [],
                    agentList: [],
                },
                list: null
            };
        },
        components: {
            "g2char": g2char,
            'memberInfo': memberInfo,
            'order': order,
            'contractInfo': contractInfo,
            'agentBonusInfo': agentBonusInfo,
        },
        created() {
            if (localStorage.getItem("roleId") === "42") {
                this.model.agentId = Number(localStorage.getItem("id"));
            }
            if (localStorage.getItem("roleId") === '66') {
                this.model.storeId = Number(localStorage.getItem("storeId"));
            }
            this.getData();
            this.getStore();
            this.getSettlementData();
        },
        computed: {},
        methods: {
            exportAgentWages() {
                this.loading = true;
                this.$postData("exportSettlementData", this.model, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tableName: "门店经纪人工资数据",
                        res: res
                    });
                })
            },
            blobExport({tableName, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tableName + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
                this.loading = false;
            },
            agentSettlementByBillNo(billNo) {
                this.$postUrl("agentSettlementByBillNo", billNo, {}).then(res => {
                    if (res.status == 200) {
                        this.getData();
                        this.getSettlementData();
                        this.$message.success("成功")
                    } else {
                        this.$message.error("失败，" + res.msg);
                    }
                })
            },
            InfohandleClose() {
                this.dom = null;
                this.showInfo = false;
            },
            handleClose() {
                this.contractId = null;
                this.drawer = false;
            },
            getStore() {
                this.$postData("store_getByList", {}, {}).then(res => {
                    if (res.status == 200) {
                        this.storeList = res.data;
                    }
                });
            },
            showContract(id) {
                this.contractId = id;
                this.drawer = true;
            },
            toBillNo(billNo) {
                let routeData = this.$router.resolve({path: '/orderInfo', query: {"billNo": billNo}});//path：跳转页面的相对路径，query：参数
                window.open(routeData.href, '_blank');
            },
            toBaomuInfo(id) {
                // console.log(id);
                let routeData = this.$router.resolve({path: '/baomuInfo', query: {"id": id}});//path：跳转页面的相对路径，query：参数
                window.open(routeData.href, '_blank');
            },
            toMember(id) {
                // console.log(id);
                let routeData = this.$router.resolve({path: '/memberInfo', query: {"id": id}});//path：跳转页面的相对路径，query：参数
                window.open(routeData.href, '_blank');
            },
            sortChange: function (column, prop, order) {
                if (column == null) {
                    this.model.orderBy = "";
                } else {
                    if (column.order === "ascending") {
                        this.model.orderBy = column.prop + " ASC";
                    }
                    if (column.order === "descending") {
                        this.model.orderBy = column.prop + " DESC";
                    }
                }
                this.getData();
            },
            getData() {
                this.loading = true;
                this.list = [];
                this.$postData("settlementPage", this.model, {}).then(res => {
                    if (res.status == 200) {
                      if(res.data.records.length > 0) {
                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                      } else {
                        this.loading = false;
                        this.$message.success("暂无数据");
                      }

                    } else {
                        this.$message.error("查询失败，查询人数过多" + res.msg);
                    }
                }).finally(_ => {
                    setTimeout(() => {
                        this.loading = false;
                    }, 10000);
                })
            },
            getSettlementData() {
                this.loading = true;
                this.$postData("settlementData", this.model, {}).then(res => {
                    if (res.status == 200) {
                        this.settlementData = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                }).finally(_ => {
                    this.loading = false;
                })
            },
            query() {
                this.loading = true;
                if (this.showDate != null) {
                    this.model.startTime = this.showDate[0];
                    this.model.endTime = this.showDate[1];
                    this.$message.warning("时间查询会导致查询时间变长。预计30秒 ~ 3分钟。请耐心等待。");
                } else {
                    this.model.startTime = null;
                    this.model.endTime = null;
                }
                if (this.contractEndDate != null) {
                    this.model.contractStartTime = this.contractEndDate[0];
                    this.model.contractEndTime = this.contractEndDate[1];
                    this.$message.warning("时间查询会导致查询时间变长。预计30秒 ~ 3分钟。请耐心等待。");
                } else {
                    this.model.contractStartTime = null;
                    this.model.contractEndTime = null;
                }

                this.getSettlementData();
                this.getData();
            },
            re() {
                this.showDate = null;
                this.contractEndDate = null;
                this.model.storeId = null;
                this.model.startTime = null;
                this.model.endTime = null;
                this.model.contractStartTime = null;
                this.model.contractEndTime = null;
                this.model.agentName = null;
                this.model.originAgentName = null;
                this.model.memberPhone = null;
                this.getSettlementData();
                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                // console.log(size);
                this.model.size = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                // console.log(index);
                this.model.current = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
                this.updateModal = false;
                this.addModal = false;
                this.doModal = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },
            edit(id) {
                this.show = id;
                // console.log(this.model);
                this.saveModal = true;
            },
            doSet(id) {
                this.doid = id;
                this.doModal = true;
            },
            exportExcel() {
                if (this.showDate == null) {
                    return this.$message.error('导出必须带上结算时间');
                }
                this.model.startTime = this.showDate[0];
                this.model.endTime = this.showDate[1];
                const diffDay = this.getDiffDay(this.model.startTime, this.model.endTime);
                if (diffDay > 366) {
                    return this.$message.error('导出的数据 时间范围需在一年以内');
                }
                if (this.contractEndDate != null) {
                    this.model.contractStartTime = this.contractEndDate[0];
                    this.model.contractEndTime = this.contractEndDate[1];
                } else {
                    this.model.contractStartTime = null;
                    this.model.contractEndTime = null;
                }
                this.$message.success('正在导出中,可能需要1~2分钟，请稍等...');
                this.loading = true;
                // this.$postData("exportAgentSettlement", this.model).then(res => {
                //     this.loading = false;
                // });
                this.$postData("exportAgentSettlement", this.model,
                    {responseType: "arraybuffer"}).then(res => {
                    this.blobExport({
                        tableName: "结算数据",
                        res: res
                    });
                });
            },
            blobExport({tableName, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tableName + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
                setTimeout(() => {
                    this.loading = false;
                }, 1500);
            },
            getDiffDay(date_1, date_2) {
                // 计算两个日期之间的差值
                let totalDays, diffDate;
                let myDate_1 = Date.parse(date_1);
                let myDate_2 = Date.parse(date_2);
                // 将两个日期都转换为毫秒格式，然后做差
                // 取相差毫秒数的绝对值
                diffDate = Math.abs(myDate_1 - myDate_2);
                // 向下取整
                totalDays = Math.floor(diffDate / (1000 * 3600 * 24));
                // console.log(totalDays)
                // 相差的天数
                return totalDays
            }
        }
    };
</script>

<style scoped>
    .allAmount {
        font-weight: bold;
        /*padding-left: 10px;*/
        color: red;
        font-size: 24px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
