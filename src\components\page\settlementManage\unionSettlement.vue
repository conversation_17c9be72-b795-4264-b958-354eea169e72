<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="9">
            <el-form-item label="提现时间">
              <el-date-picker v-model="days" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商户号">
              <el-input
                  clearable
                  v-model="form.merchantCode"
                  placeholder="请输入商户号"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="提现人名称">
              <el-input
                  clearable
                  v-model="form.name"
                  placeholder="请输入提现人名称"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
            <el-button type="info"
                       style="margin-bottom: 15px"
                       @click="exportUnionCashJournalList"
                       icon="el-icon-download">导出
            </el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="9">
            <el-form-item label="到账时间">
              <el-date-picker v-model="dzDays" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="手机号">
              <el-input
                  clearable
                  v-model="form.phone"
                  placeholder="请输入手机号"
                  style="width:160px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item  label="账户类型">
              <el-select style="width:160px" v-model="form.applicantType" clearable placeholder="请选择账户类型">
                <el-option v-for="item in optionsType"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item  label="状态">
              <el-select style="width:160px" v-model="form.state" clearable placeholder="请选择状态">
                <el-option v-for="item in options"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item  label="发票筛选">
              <el-select style="width:160px" v-model="form.voteType" clearable placeholder="请选择类型">
                <el-option v-for="item in typeOptions"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        <el-col :span="6">
          <el-form-item  label="提现类型">
            <el-select style="width:160px" v-model="form.withdrawalType" clearable placeholder="请选择类型">
              <el-option v-for="item in withdrawalTypeOptions"
                         :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
          <el-col :span="6">
            <el-form-item  label="奖金类型">
              <el-select style="width:160px" v-model="form.amountType" clearable placeholder="请选择类型">
                <el-option v-for="item in amountTypeOptions"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="提现记录">
          <el-table :data="logList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column prop="createTime" label="提现时间" width="90"></el-table-column>
            <el-table-column prop="merchantCode" label="商户号" width="90"></el-table-column>
            <el-table-column prop="traNumber" label="交易编号" width="140"></el-table-column>
            <el-table-column prop="respName" label="提现人" width="140"></el-table-column>
            <el-table-column prop="storeName" label="所属门店" width="140"></el-table-column>
            <el-table-column label="提现金额来源" width="100">
              <template slot-scope="scope">
                <el-tag type="primary" @click="selectOrderList(scope.row.id,scope.row.merchantId),orderObj.current = 1,orderObj.size = 10">点击查看</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="changeAmount" label="提现金额" width="80"></el-table-column>
            <el-table-column prop="deductionAmount" label="扣减金额" width="80"></el-table-column>
            <el-table-column prop="realPaymentAmount" label="真实需打款金额" width="120">
              <template slot-scope="scope">
                <div style="color: red">{{scope.row.realPaymentAmount}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="merchantAccount" label="原账户余额" width="90"></el-table-column>
            <el-table-column prop="amount" label="现账户余额" width="90"></el-table-column>
            <el-table-column prop="applicantType" label="账户类型" width="70">
              <template slot-scope="scope">
                <span>{{scope.row.applicantType==1?'自有员工':
                    scope.row.applicantType==2?'家姐联盟':
                        scope.row.applicantType==3?'加盟商':'未知'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="withdrawalType" label="提现类型" width="70">
              <template slot-scope="scope">
                <span>{{scope.row.withdrawalType==0?'平台代扣':
                    scope.row.withdrawalType==1?'发票提现':
                        scope.row.withdrawalType==2?'零工提现':'未知'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="amountType" label="奖金类型" width="70">
              <template slot-scope="scope">
                <span>{{scope.row.amountType==1?'业务奖金':
                    scope.row.amountType==2?'其他奖金':'未知'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="state" label="状态" width="120">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.state===0" type="warning">未处理</el-tag>
                <el-tag v-if="scope.row.state===1" type="success">已通过</el-tag>
                <el-tag v-if="scope.row.state===2" type="danger">未通过</el-tag>
                <el-tag v-if="scope.row.state===3" type="success">到账成功</el-tag>
                <el-tag v-if="scope.row.state===4" type="danger">到账失败</el-tag>
                <el-tag v-if="scope.row.state===5" type="warning">人力窝审核中</el-tag>
                <el-tag v-if="scope.row.state===6" type="warning">人力窝结算中</el-tag>
                <el-tag v-if="scope.row.state===7" type="success">人力窝结算成功</el-tag>
                <el-tag v-if="scope.row.state===8" type="danger">人力窝审核驳回</el-tag>
                <el-tag v-if="scope.row.state===9" type="danger">人力窝结算异常</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="发票" width="100">
              <template slot-scope="scope">
                <el-tag type="primary" v-if="scope.row.invoiceImgUrl" @click="selectInvoice(scope.row)">点击查看</el-tag>
                <el-tag type="danger" v-if="!scope.row.invoiceImgUrl" >暂无发票</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="bankNo" label="银行卡号" width="160"></el-table-column>
            <el-table-column prop="openBankName" label="所属银行" width="110"></el-table-column>
            <el-table-column prop="openAccount" label="开户支行" width="150"></el-table-column>
            <el-table-column prop="respPhone" label="手机号" width="110"></el-table-column>
            <el-table-column prop="remark" label="类型" width="90"></el-table-column>
            <el-table-column prop="withdrawalRemark" label="备注" width="130"></el-table-column>
            <el-table-column prop="financeRemark" label="财务备注" width="130">
              <template slot-scope="scope">
                <div style="color: red">{{scope.row.financeRemark}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="operator" label="处理人" width="100"></el-table-column>
            <el-table-column prop="operatorTime" label="处理时间" width="90"></el-table-column>
            <el-table-column prop="receivedTime" label="到账时间" width="90"></el-table-column>
            <el-table-column label="操作" fixed="right" width="120">
              <template slot-scope="scope">
                <el-button :disabled="scope.row.state===3||scope.row.state===4||scope.row.state===2" @click.native.prevent="showRow(scope.row.id,scope.row.state,scope.row.applicantType)" type="text" size="small">操作
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
        <el-dialog :visible.sync="dialogVisible">
          <el-form>
            <el-form-item label="处理结果">
              <el-tag v-if="dom.state===0" type="warning">待处理</el-tag>
              <el-tag v-if="dom.state===1" type="success">已通过</el-tag>
              <el-tag v-if="dom.state===2" type="danger">不通过</el-tag>
              <el-tag v-if="dom.state===3" type="warning">到账成功</el-tag>
              <el-tag v-if="dom.state===4" type="success">到账失败</el-tag>
              <el-tag v-if="dom.state===5" type="warning">人力窝审核中</el-tag>
              <el-tag v-if="dom.state===6" type="warning">人力窝结算中</el-tag>
              <el-tag v-if="dom.state===7" type="success">人力窝结算成功</el-tag>
              <el-tag v-if="dom.state===8" type="danger">人力窝审核驳回</el-tag>
              <el-tag v-if="dom.state===9" type="danger">人力窝结算异常</el-tag>
            </el-form-item>
          </el-form>
          <el-button type="primary" :disabled="!roleId==56&&!roleId==57" @click="innerVisible = true">更新</el-button>
          <br><br>
        </el-dialog>
        <el-dialog title="处理" :visible.sync="innerVisible" append-to-body center>
          <el-form>
            <el-form-item label="状态">
              <el-radio-group v-model="dom.state">
                <el-radio v-if="roleId==56||roleId==2" label="1">通过</el-radio>
                <el-radio v-if="roleId==56||roleId==2" label="2">不通过</el-radio>
                <el-radio v-if="roleId==57" label="3">到账成功</el-radio>
                <el-radio v-if="roleId==57&&applicantType==2" label="5">人力窝申请发放</el-radio>
                <el-radio v-if="roleId==57" label="4">到账失败</el-radio>
              </el-radio-group>
            </el-form-item>
            <br>
            <el-form-item v-if="dom.state==='1'" label="财务备注">
              <el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="dom.financeRemark"> </el-input>
            </el-form-item>
            <el-form-item v-if="dom.state==='2'||dom.state==='4'" label="原由">
              <el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="dom.withdrawalRemark"> </el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="addFranchiseRegisterHandler">确定</el-button>
          </div>
        </el-dialog>
      </el-tabs>
    </div>
    <el-dialog
        title="提现来源："
        :visible.sync="dialog"
        width="80%">
      <div style="display: flex">
        <div>招工奖励：{{cashVo.zgj||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <div>线索奖励：{{cashVo.xsj||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <div>开发奖励：{{cashVo.kfj||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <div>其他奖励：{{cashVo.qtj||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <div>上架奖励：{{cashVo.sjj||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <div>佳币奖励：{{cashVo.jbj||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <div>结算总金额：{{cashVo.balanceSumMoney||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <el-button type="info"
                   style="margin-bottom: 15px"
                   @click="download"
                   icon="el-icon-download">导出
        </el-button>
      </div>
      <el-table  :data="orderList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">

        <el-table-column
            fixed="left"
            prop="productName"
            label="服务项目"
            width="120"
        ></el-table-column>
        <el-table-column
            prop="employeeName"
            label="服务人员"
            width="100"
        ></el-table-column>
        <el-table-column
            prop="employeeNo"
            label="服务人员工号"
            width="100"
        ></el-table-column>
        <el-table-column
            prop="amount"
            label="订单金额"
            width="80"
        ></el-table-column>
        <el-table-column
            prop="commission"
            label="收益金额"
            width="100"
        ></el-table-column>
        <el-table-column
            prop="createTime"
            label="结算时间"
            width="140"
        ></el-table-column>
        <el-table-column
            prop="billNo"
            label="订单号"
            width="150"
        ></el-table-column>
        <el-table-column
            prop="settlementType"
            label="收益类型"
        >
          <template slot-scope="scope">
            <span>{{scope.row.settlementType==1?'招工奖励':
                    scope.row.settlementType==2?'线索奖励':
                    scope.row.settlementType==3?'开发奖励':
                    scope.row.settlementType==4?'其他奖励':
                    scope.row.settlementType==5?'上架奖':
                    scope.row.settlementType==6?'佳币奖励':
                    '未知'}}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="pageInfoB.total"
              @on-change="onChangeB"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onPageSizeChangeB"
              :page-size="pageInfoB.size"/>
      </div>
    </el-dialog>
    <el-dialog
        title="发票信息："
        :visible.sync="invoiceDialog"
        width="80%">
      <div>
        <div>图片发票：</div>
        <div v-for="url in txData.imgInvoiceUrlList">
          <el-col :span="3">
            <el-image
                style="width: 100px; height: 100px"
                :src="url"
                :preview-src-list="txData.imgInvoiceUrlList">
            </el-image>
          </el-col>
        </div>
      </div>

      <div style="height: 150px"></div>
      <div style="height: 250px">
        <div>文件发票：</div>
        <div v-for="(url,index) in txData.fileInvoiceUrlList">
          <el-col :span="3">
            <el-link :href="url" type="primary" target="_blank">文件发票{{index+1}}</el-link>
          </el-col>
        </div>
      </div>
    </el-dialog>
    <el-link :href="hrefUrl" target="_blank" id="elLink"/>
  </div>
</template>

<script>
import moment from "moment";
export default {
  name: "register",
  data() {
    return {
      logList: [],
      txData:{},
      orderList: [],
      cashVo: {},
      dialog: false,
      typeOptions: [{
        value:  '1',
        label: '含票'
      },{
        value:  '2',
        label: '无票'
      }],
      withdrawalTypeOptions: [{
        value:  '0',
        label: '平台代扣'
      },{
        value:  '1',
        label: '发票'
      },{
        value:  '2',
        label: '零工'
      }],
      amountTypeOptions: [{
        value:  '1',
        label: '业务奖金'
      },{
        value:  '2',
        label: '其他奖金'
      }],
      roleId: localStorage.getItem('roleId'),
      hrefUrl: "",
      dialogVisible: false,
      invoiceDialog: false,
      innerVisible: false,
      cashId: null,
      applicantType: null,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      pageInfoB: {total: 10, size: 10, current: 1, pages: 1},
      days: [],
      dzDays: [],
      orderObj: {
        id: null,
        merchantId: null,
        current: 1,
        size: 10
      },
      form: {
        state: '',
        applicantType: '',
        startTime: '',
        endTime: '',
        dzStartTime: '',
        dzEndTime: '',
        merchantCode: '',
        name: '',
        voteType: '',
        withdrawalType: '',
        amountType: '',
        roleId: localStorage.getItem('roleId'),
        phone: '',
        current: 1,
        size: 10
      },
      dom: {
        state: null,
        employeeId: localStorage.getItem("id"),
        id: null,
        withdrawalRemark: null,
        financeRemark: null,
        remark: null,
      },
      options: [],
      optionsType: [{
        value: '1',
        label: '自有员工'
      }, {
        value: '2',
        label: '家姐联盟'
      }, {
        value: '3',
        label: '加盟商'
      }
      ],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },
  created() {
    let obj = {
      value:  '1',
      label: '已通过'
    }
    this.options.push(obj)
    if(this.roleId==2||this.roleId==56||this.roleId==1){
      obj = {}
      obj.value = '0'
      obj.label='未处理'
      this.options.push(obj)
      obj = {}
      obj.value = '2'
      obj.label='未通过'
      this.options.push(obj)
      obj = {}
      obj.value = '3'
      obj.label='到账成功'
      this.options.push(obj)
    }
    if(this.roleId==57||this.roleId==1){
      obj = {}
      obj.value = '3'
      obj.label='到账成功'
      this.options.push(obj)
      obj = {}
      obj.value = '4'
      obj.label='到账失败'
      this.options.push(obj)
      obj = {}
      obj.value = '5'
      obj.label='人力窝审核中'
      this.options.push(obj)
      obj = {}
      obj.value = '6'
      obj.label='人力窝结算中'
      this.options.push(obj)
      obj = {}
      obj.value = '7'
      obj.label='人力窝结算成功'
      this.options.push(obj)
      obj = {}
      obj.value = '8'
      obj.label='人力窝审核驳回'
      this.options.push(obj)
      obj = {}
      obj.value = '9'
      obj.label='人力窝结算异常'
      this.options.push(obj)
    }
    this.getData();
  },
  methods: {
    selectInvoice(item){
      this.txData = item
      this.invoiceDialog = true
    },
    exportUnionCashJournalList(){
      this.form.startTime = '';
      this.form.endTime = '';
      this.form.dzStartTime = '';
      this.form.dzEndTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      if (this.dzDays != null && this.dzDays.length > 0) {
        this.form.dzStartTime = moment(this.dzDays[0]).format("YYYY-MM-DD");
        this.form.dzEndTime = moment(this.dzDays[1]).format("YYYY-MM-DD")
      }
      this.hrefUrl = "https://biapi.xiaoyujia.com/unionCashJournal/exportUnionCashJournalList?state="+this.form.state+
          "&applicantType="+this.form.applicantType+"&startTime="+this.form.startTime+"&endTime="+this.form.endTime+
          "&merchantCode="+this.form.merchantCode+"&name="+this.form.name+"&phone="+this.form.phone+"&roleId="+localStorage.getItem('roleId')+
          "&voteType=" + this.form.voteType + "&withdrawalType=" + this.form.withdrawalType + "&amountType=" + this.form.amountType+
          "&dzStartTime="+this.form.dzStartTime+"&dzEndTime="+this.form.dzEndTime
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    download(){
      this.hrefUrl = "https://biapi.xiaoyujia.com/unionCashJournal/exportUnionSettlementDetails?id="+this.orderObj.id+'&merchantId='+this.orderObj.merchantId
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    selectOrderList(id,merchantId){
      this.dialog = true;
      this.orderObj.id=id
      this.orderObj.merchantId=merchantId
      this.$getData("getUnionSettlementDetails", this.orderObj, {}).then(res => {
        if (res.code == 0) {
          this.cashVo = res.data
          this.orderList = res.data.page.records
          this.pageInfoB.current = res.data.page.current;
          this.pageInfoB.size = res.data.page.size;
          this.pageInfoB.total = res.data.page.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    },
    showRow(id, state,applicantType) {
      this.dialogVisible = true;
      this.dom.state = state;
      this.applicantType = applicantType;
      this.dom.id = id;
    },
    addFranchiseRegisterHandler() {
      if(this.dom.state==0){
        return this.$message.error("请选择更新状态!");
      }
      if((this.dom.state==2||this.dom.state==4)&&!this.dom.withdrawalRemark){
       return  this.$message.error("请输入原由!");
      }
      this.innerVisible = false;
      this.$postData("updateCashState", this.dom).then(res => {
        if (res.code === 0) {
          this.dom.withdrawalRemark = ''
          this.dom.financeRemark = ''
          this.$message({message: '更新成功!', type: 'success'});
        } else {
          this.$message({message: res.msg, type: 'warning'});
        }
        this.getData();
      })
      this.dialogVisible = false;
    },
    // 页码大小
    onPageSizeChange(size) {
      this.form.size = size;
      this.getData();
    },
    // 页码大小
    onPageSizeChangeB(size) {
      this.orderObj.size = size;
      this.selectOrderList(this.orderObj.id);
    },
    onChange(index) {
      this.form.current = index;
      this.getData();
    },
    onChangeB(index) {
      this.orderObj.current = index;
      this.selectOrderList(this.orderObj.id);
    },
    getData() {
      this.form.startTime = '';
      this.form.endTime = '';
      this.form.dzStartTime = '';
      this.form.dzEndTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      if (this.dzDays != null && this.dzDays.length > 0) {
        this.form.dzStartTime = moment(this.dzDays[0]).format("YYYY-MM-DD");
        this.form.dzEndTime = moment(this.dzDays[1]).format("YYYY-MM-DD")
      }
      this.$getData("getUnionCashJournalList", this.form, {}).then(res => {
        if (res.code == 0) {
          this.logList = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    }
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
