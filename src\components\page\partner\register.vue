<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="9">
            <el-form-item label="报名时间">
              <el-date-picker v-model="form.days" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="名称">
              <el-input
                  clearable
                  v-model="form.name"
                  placeholder="名称"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="手机号">
              <el-input
                  clearable
                  v-model="form.phone"
                  placeholder="手机号"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
            <el-button type="info"
                       :loading="loadingExcel"
                       @click="download"
                       icon="el-icon-download">导出
            </el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="介绍人">
              <el-input
                  clearable
                  v-model="form.introducer"
                  placeholder="介绍人"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="申请城市">
              <el-input
                  clearable
                  v-model="form.address"
                  placeholder="申请城市"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="渠道">
              <el-select v-model="form.channelId" clearable placeholder="请选择渠道">
                <el-option
                    v-for="item in channel"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态">
              <el-select v-model="form.status" clearable placeholder="请选择状态">
                <el-option v-for="item in options"
                    :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="填报记录">
          <el-table :data="logList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column prop="creatDate" label="报名时间" width="150"></el-table-column>
            <el-table-column prop="name" label="名称" width="100"></el-table-column>
            <el-table-column prop="phone" label="手机号" width="100"></el-table-column>
            <el-table-column prop="introducer" label="介绍人" width="100"></el-table-column>
            <el-table-column prop="remark" label="备注" ></el-table-column>
            <el-table-column prop="address" label="申请城市" width="160"></el-table-column>
            <el-table-column prop="channelName" label="申请渠道" width="100"></el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status==='0'" type="danger">未通过</el-tag>
                <el-tag v-if="scope.row.status==='2'" type="success">已通过</el-tag>
                <el-tag v-if="scope.row.status==='3'" type="primary">完成</el-tag>
                <el-tag v-else-if="scope.row.status==='1'" type="warning">未处理</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="100">
              <template slot-scope="scope">
                <el-button @click.native.prevent="showRow(scope.row.id,scope.row.status)" type="text" size="small">操作
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
        <el-dialog :visible.sync="dialogVisible">
          <el-form>
            <el-form-item label="处理结果">
              <el-tag v-if="dom.status==='0'" type="danger">未通过</el-tag>
              <el-tag v-if="dom.status==='1'" type="warning">未处理</el-tag>
              <el-tag v-if="dom.status==='2'" type="success">已通过</el-tag>
              <el-tag v-if="dom.status==='3'" type="primary">完成</el-tag>
            </el-form-item>
          </el-form>
          <el-button type="primary" @click="innerVisible = true">更新</el-button>
          <br><br>
          <el-form v-for="(item, index) in handler" :key="index">
            <el-form-item :label="'时间:'+item.handlerDate">
              <el-input type="textarea" v-model="item.handlerDetail" autosize :readonly="true"></el-input>
            </el-form-item>
          </el-form>
        </el-dialog>
        <el-dialog title="处理" :visible.sync="innerVisible" append-to-body center>
          <el-form>
            <el-form-item label="状态">
              <el-radio-group v-model="dom.status">
                <el-radio label="0">不通过</el-radio>
                <el-radio label="1">未处理</el-radio>
                <el-radio label="2">通过</el-radio>
                <el-radio label="3">完成</el-radio>
              </el-radio-group>
            </el-form-item>
            <br>
            <el-form-item label="处理建议">
              <el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="dom.handlerDetail"> </el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="addFranchiseRegisterHandler">确定</el-button>
          </div>
        </el-dialog>
      </el-tabs>
    </div>
  </div>
</template>

<script>
export default {
  name: "register",
  data() {
    return {
      channel: [],
      logList: [],
      handler: [],
      loadingExcel: false,
      dialogVisible: false,
      innerVisible: false,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      form: {
        days: [],
        status: null,
        startTime: null,
        endTime: null,
        channelId: null,
        handlerDetail: null,
        current: 1,
        size: 10
      },
      dom: {
        status: null,
        registerId: null,
        handlerId: localStorage.getItem("id"),
        handlerDetail: null,
      },
      options: [{
        value: '0',
        label: '未通过'
      }, {
        value: '1',
        label: '未处理'
      }, {
        value: '2',
        label: '已通过'
      }, {
        value: '3',
        label: '完成'
      }, ],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },
  created() {
    this.getData();
    this.getFranchiseChannel();
  },
  methods: {
    download() {
      this.loadingExcel = true;
      this.$postData("downloadFranchiseRegister", this.form, {responseType: "arraybuffer"}).then(res => {
        this.loadingExcel = false;
        this.blobExport({
          tablename: "合伙人信息",
          res: res
        });
      })
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
    showRow(id, status) {
      this.dialogVisible = true;
      this.dom.status = status;
      this.dom.registerId = id;
      this.getFranchiseRegisterHandler(id);
    },
    getFranchiseRegisterHandler(id) {
      this.$getData("getFranchiseRegisterHandler", {registerId: id}).then(res => {
        this.handler = res.data;
      })
    },
    addFranchiseRegisterHandler() {
      this.loading = this.$loading({
        lock: true,
        text: '拼命加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.innerVisible = false;
      this.$postData("addFranchiseRegisterHandler", this.dom).then(res => {
        this.getFranchiseRegisterHandler(this.dom.registerId);
        this.loading.close();
        if (res.data > 0) {
          this.$message({message: '新增成功', type: 'success'});
        } else {
          this.$message({message: '新增失败', type: 'warning'});
        }
      })
    },
    // 页码大小
    onPageSizeChange(size) {
      this.form.size = size;
      this.getData();
    },
    onChange(index) {
      this.form.current = index;
      this.getData();
    },
    getData() {
      this.form.startTime = null;
      this.form.endTime = null;
      if (this.form.days != null && this.form.days.length > 0) {
        this.form.startTime = this.form.days[0];
        this.form.endTime = this.form.days[1]
      }
      this.$postData("franchiseRegisterPage", this.form, {}).then(res => {
        if (res.status == 200) {
          this.logList = res.data.records;
          this.logList.forEach(v => {
            v.status = v.status.toString()
          })
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    },
    getFranchiseChannel() {
      this.$getData("getFranchiseChannel",).then(res => {
        this.channel = res.data;
      })
    },
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
