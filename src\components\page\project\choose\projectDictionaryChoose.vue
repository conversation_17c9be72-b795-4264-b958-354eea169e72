<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col span="24">
                    <div>
                        <div id="operBtnDiv">
                            <div id="searchDiv">
                                <Form ref="formItem"   :model="formItem" :label-width="80" label-position="left" :rules="ruleValidate">
                                    <FormItem label="类型" prop="type">
                                        <Select v-model="formItem.type" filterable style="width: 200px">
                                            <Option v-for="item in list" :value="item.value">{{ item.name}}</Option>
                                        </Select>
                                    </FormItem>

                                    <FormItem label="项目名称" prop="name">
                                        <Input placeholder="请输入项目名称" v-model="formItem.name" style="width: 200px">
                                        </Input>
                                    </FormItem>

                                    <FormItem>
                                        <Button type="primary" @click="save('formItem')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </FormItem>

                                </Form>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
    </div>
</template>


<script>



    export default {
        data() {
            return {
                formItem: {
                    type: 1,
                    name: null,
                },
                list:[
                    {value:1,name:"预算支出"},
                    {value:2,name:"成本支出"},
                    {value:3,name:"项目性质"},
                    {value:4,name:"收入类型"},
                    {value:5,name:"扣减类型"},
                    {value:6,name:"结算类型"},
        ],

                ruleValidate: {
                    type: [
                        {required: true, message: '请选择类型', trigger: 'blur',type:"number"}
                    ],
                    name: [
                        {required: true, message: '请输入名称', trigger: 'change'},
                    ],
                }
            }
        },
        components: {
        },
        created: function () {
        },
        methods: {

            save(name) {
                console.log(this.formItem);
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        console.log(this.formItem);
                        this.$postData("projectDictionary_save", this.formItem, {}).then(res => {
                            if (res.status == 200) {
                                this.$Message.success('添加成功');
                                this.chooseThisModel();
                            } else {
                                this.$message.error("添加失败，" + res.msg);
                            }
                        })
                    }
                })
            },
            chooseThisModel (name) {
                this.$emit('init-choose', "");
            }
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

