<!--

支持选择多个员工

-->
<template style="background-color: #000">
    <div>
        <Row>
            <Col span="20">
                <el-input
                        v-model="employee.name"
                        placeholder="姓名"
                        style="width:200px"
                        class="handle-input mr10"
                ></el-input>
                <el-button type="primary" @click="getData()">搜索</el-button>
            </Col>
            <Col span="4">
                <el-button type="success" @click="insert">确定</el-button>
            </Col>
        </Row>

        <div style="margin-top: 10px;margin-bottom: 10px">
            <Tag v-for="(item, index) in list" :key="index"
                 closable @on-close="handleClose(item.id)">{{item.no}}{{item.realName}}
            </Tag>
        </div>
        <div style="width: 99%;margin: 0 auto">
            <Table highlight-row ref="currentRowTable"
                   @on-select-all-cancel="selectAll"
                   @on-select-all="selectAll"
                   :columns="columns"
                   @on-select-cancel="selectCancel"
                   :data="data" @on-select="onSelectionChange">
            </Table>
        </div>

        <Page style="margin-top: 10px" :total="pageInfo.total" @on-change="onChange" :show-total="true" :page-size="5"/>
    </div>
</template>


<script>
    export default {
        data() {
            return {
                list: [],
                userModal: false,
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                currentRow: null,
                columns: [
                    {
                        type: 'selection',
                        width: 130,
                        align: 'center'
                    },
                    {
                        title: '工号',
                        key: 'no',
                        height: 10,
                        width: 130,

                    },
                    {
                        title: '姓名',
                        key: 'realName',
                        width: 180,
                    },
                ],
                employee: {
                    name: null,
                    id: null,   //负责人id
                    pageSize: 5,
                    pageNum: 1,
                },
                data: []
            }
        },
        components: {},
        created: function () {
            this.getData();
        },
        methods: {
            getData() {
                this.$postData("getByService", this.employee, {}).then(res => {
                    if (res.status === 200) {
                       ;
                        this.data = res.data.list;
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.userModal = false;
            },
            onChange(index) {
                console.log(index);
                this.employee.pageNum = index;
                this.getData();
            },
            selectCancel(selection, row) {
                let index = "";
                this.list.forEach((el, idx) => {
                    if (row.id === el.id) {
                        index = idx;
                    }
                });
                this.list.splice(index, 1);
            },
            onSelectionChange(selection, row) {
                console.log(row);
                this.list.push(row);
            },
            handleClose(id) {
                let flag = true;
                this.data.forEach((el, index) => {
                    if (id === el.id) {
                        flag=false;
                        this.$refs.currentRowTable.toggleSelect(index)
                    }
                });
                if(flag){
                    let index = "";
                    this.list.forEach((el, idx) => {
                        if (id === el.id) {
                            index = idx;
                        }
                    });
                    this.list.splice(index, 1);
                }
            },
            insert() {
                this.$emit('init-choose', this.list);
            },
            selectAll(selection) {
                this.list = selection
            }
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 150px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

