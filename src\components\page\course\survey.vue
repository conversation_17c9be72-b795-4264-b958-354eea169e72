<template>
	<div class="container">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">问卷管理</el-menu-item>
			<el-menu-item index="1">问题管理</el-menu-item>
		</el-menu>

		<!-- 搜索栏目 -->
		<div v-if="choiceIndexNow==0">
			<div class="handle-box" style="margin: 20px 20px;">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="问卷标题、内容"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="问卷类型" style="margin-right: 20px">
								<el-select v-model="searchType" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="问卷状态" style="margin-right: 20px">
								<el-select v-model="searchState" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(0,0)">添加
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">

				<el-table-column prop="id" width="60" label="编号">
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="surveyTitle" label="问卷标题">
					<template slot-scope="scope">
						<span>{{scope.row.surveyTitle}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="surveyContent" label="问卷内容">
					<template slot-scope="scope">
						<span>{{scope.row.surveyContent||'暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="122" prop="surveyType" label="问卷类型" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-tag v-if="item.id == scope.row.surveyType" v-for="(item,index) in typeList" :key="index"
							:type="typeStyleList[index%4].type">{{item.typeName}}</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="122" label="关联课程" prop="courseId">
					<template slot-scope="scope">
						<span v-if="scope.row.courseId==item.id" v-for="(item,index) in courseList" :key="index">
							{{item.courseTitle}}
						</span>
						<span v-if="!scope.row.courseId">
							暂未设置
						</span>
					</template>
				</el-table-column>

				<el-table-column width="122" label="关联直播" prop="liveRoomId">
					<template slot-scope="scope">
						<span v-if="scope.row.liveRoomId==item.id" v-for="(item,index) in liveRoomList" :key="index">
							{{item.liveRoomName}}
						</span>
						<span v-if="!scope.row.liveRoomId">
							暂未设置
						</span>
					</template>
				</el-table-column>

				<el-table-column width="102" prop="surveyLevel" label="问卷渠道">
					<template slot-scope="scope">
						<span>{{scope.row.surveyLevel?scope.row.surveyLevel:'暂未设置'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="102" prop="recordNum" label="已提交" sortable>
				</el-table-column>

				<el-table-column width="102" prop="collectLimit" label="人数限制" :render-header="renderHeader">
				</el-table-column>

				<el-table-column width="122" prop="collectStoreLimit" label="门店填写限制" :render-header="renderHeader">
				</el-table-column>

				<el-table-column width="160" label="问题列表" :render-header="renderHeader">
					<template slot-scope="scope">
						<div v-if="scope.row.questionIdList!='0'">
							<ul v-for="(item,index) in questionList" :key="index">
								<li class="tags-li" v-for="(item1,index1) in scope.row.questionIdListArray"
									:key="index1" v-if="item.id==parseInt(item1)">
									<span>
										{{ item.questionTitle }}
									</span>
									<span class="tags-li-icon" @click="closeQuestionTags(scope.$index,index1)"><i
											class="el-icon-close" v-if="scope.row.isEdit"></i></span>
								</li>
							</ul>
						</div>

						<div v-if="scope.row.questionIdList=='0'">
							<span>暂未设置</span>
						</div>
					</template>
				</el-table-column>


				<el-table-column width="102" label="状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.surveyState == 0" type="info">下架</el-tag>
						<el-tag v-if="scope.row.surveyState == 1" type="success">上架</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="startTime" label="开始日期" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.startTime||'暂未设置'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="endTime" label="结束日期" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.endTime||'暂未设置'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creator" label="创建人">
					<template slot-scope="scope">
						<span v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
						<span v-else>{{scope.row.creator||''}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="200" label="操作">
					<template slot-scope="scope">
						<el-button @click="openModal(1,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
						<el-popconfirm title="确定删除该问卷吗？删除后无法恢复，请谨慎操作!" @confirm="deleteSurvey(scope.row)"
							v-if="isAdmin">
							<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
								icon="el-icon-delete">删除
							</el-button>
						</el-popconfirm>
						<el-button @click="openModal(5,scope.$index)" type="warning" size="small" icon="el-icon-s-order"
							style="margin-left: 10px">
							记录
						</el-button>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<div v-if="choiceIndexNow==1">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="问题标题、内容"></el-input>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="是否必填" style="margin-right: 20px">
								<el-select v-model="searchRequired">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in switchStateList" :key="index"
										:label="item.typeName" :value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="问题状态" style="margin-right: 20px">
								<el-select v-model="searchState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>
							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>
							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(3,0)">添加
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands">

				<el-table-column prop="id" width="60" label="编号">
					<template slot-scope="scope">
						<span>{{scope.row.id}}</span>
					</template>
				</el-table-column>

				<el-table-column width="100" prop="questionTitle" label="问题标题">
					<template slot-scope="scope">
						<span>{{scope.row.questionTitle}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="questionContent" label="问题内容">
					<template slot-scope="scope">
						<span>{{scope.row.questionContent||'暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="102" prop="answerType" label="回答类型">
					<template slot-scope="scope">
						<el-tag v-if="item.id == scope.row.answerType" v-for="(item,index) in answerTypeList"
							:key="index" :type="typeStyleList[index%4].type">{{item.typeName}}</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="102" label="是否必填">
					<template slot-scope="scope">
						<el-tag v-for="(item,index) in switchStateList" :key="index"
							v-if="scope.row.required == item.id" :type="item.type">{{item.typeName}}</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="102" label="状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.questionState == 0" type="info">下架</el-tag>
						<el-tag v-if="scope.row.questionState == 1" type="success">上架</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creator" label="创建人">
					<template slot-scope="scope">
						<span v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
						<span v-else>{{scope.row.creator||''}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
					<template slot-scope="scope">
						<el-button @click="openModal(4,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
						<el-popconfirm title="确定删除该问题吗？删除后无法恢复，请谨慎操作!" @confirm="deleteSurveyQuestion(scope.row)"
							v-if="isAdmin">
							<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
								icon="el-icon-delete">删除
							</el-button>
						</el-popconfirm>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--添加问卷-->
		<el-drawer size="60%" :with-header="false" :visible.sync="surveyModal" direction="rtl">
			<div>
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">问卷信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 问卷标题：">
								<el-input v-model="choiceItem.surveyTitle" type="textarea" class="handle-input mr10"
									placeholder="请输入问卷标题" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="* 问卷内容：">
								<el-input v-model="choiceItem.surveyContent" type="textarea" class="handle-input mr10"
									placeholder="请输入问卷内容描述" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="关联课程：">
								<el-select v-model="choiceItem.courseId" placeholder="请选择" filterable>
									<el-option v-for="(item,index) in courseList" :key="index"
										:label="item.id+'：'+item.courseTitle" :value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="在对应的课程中显示该问卷" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="关联直播：">
								<el-select v-model="choiceItem.liveRoomId" placeholder="请选择" filterable>
									<el-option v-for="(item,index) in liveRoomList" :key="index"
										:label="item.id+':'+item.liveRoomTitle" :value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="在对应的直播中显示该问卷（功能开发中，尽请期待）"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="问卷渠道：">
								<el-input v-model="choiceItem.source" type="textarea" class="handle-input mr10"
									placeholder="请输入直播相关渠道">
								</el-input>
								<el-tooltip class="item" effect="dark" content="直播对应的渠道（功能开发中，尽请期待）"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="人数限制：">
								<el-input v-model="choiceItem.collectLimit" type="number" class="handle-input mr10"
									placeholder="请输入人数限制">
								</el-input>
								<el-tooltip class="item" effect="dark" content="填写人数达到限制后问卷入口关闭" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="门店限制：">
								<el-input v-model="choiceItem.collectStoreLimit" type="number" class="handle-input mr10"
									placeholder="请输入单个门店填写人数限制">
								</el-input>
								<el-tooltip class="item" effect="dark" content="单门店超过限制填写人数后问卷入口关闭"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>
						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="问卷类型：">
								<el-select v-model="choiceItem.surveyType" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="可根据类型进行分类" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="问卷状态：">
								<el-select v-model="choiceItem.surveyState" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="问题列表：">
								<div>
									<ul v-for="(item,index) in questionList" :key="index">
										<li class="tags-li" v-for="(item1,index1) in choiceItem.questionIdListArray"
											:key="index1" v-if="item.id==parseInt(item1)">
											<span>
												{{ item.questionTitle }}
											</span>
											<span class="tags-li-icon" @click="closeQuestionTags1(index1)"><i
													class="el-icon-close"></i></span>
										</li>
									</ul>
									<el-button type="primary" @click="openModal(2,0)" plain
										icon="el-icon-plus">添加问题</el-button>
								</div>

								<div v-if="!choiceItem.questionIdList||choiceItem.questionIdList=='0'">
									<span>暂未设置</span>
								</div>
							</el-form-item>

							<el-form-item width="250" label="开始日期：">
								<el-date-picker v-model="choiceItem.startTime" type="datetime"
									value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00']"
									placeholder="请选择问卷开始日期" :picker-options="pickerOptions">
								</el-date-picker>
							</el-form-item>

							<el-form-item width="250" label="结束日期：">
								<el-date-picker v-model="choiceItem.endTime" type="datetime"
									value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00']"
									placeholder="请选择问卷结束日期" :picker-options="pickerOptions">
								</el-date-picker>
							</el-form-item>

							<el-form-item label="问卷入口：">
								<el-tag @click="openEntry(0)">入口二维码</el-tag>
								<el-tooltip class="item" effect="dark" content="扫码后可预览/填写问卷" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="surveyModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="insertSurvey()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateSurvey(choiceItem)" type="success" size="small" v-if="detailType==1">保存
					</el-button>
				</div>

			</div>
		</el-drawer>

		<!--添加问卷问题-->
		<el-drawer size="60%" :with-header="false" :visible.sync="surveyQuestionModal" direction="rtl">
			<div>
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">问卷问题信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 问题标题：">
								<el-input v-model="choiceItem.questionTitle" type="textarea" class="handle-input mr10"
									placeholder="请输入问题标题">
								</el-input>
							</el-form-item>

							<el-form-item label="* 问题内容：">
								<el-input v-model="choiceItem.questionContent" type="textarea" class="handle-input mr10"
									placeholder="请输入问题内容描述">
								</el-input>
							</el-form-item>
						</el-form>

					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="回答类型：">
								<el-select v-model="choiceItem.answerType">
									<el-option v-for="(item,index) in answerTypeList" :key="index"
										:label="item.typeName" :value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="对用户填写内容进行限制" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="是否必填：">
								<el-select v-model="choiceItem.required">
									<el-option v-for="(item,index) in switchStateList" :key="index"
										:label="item.typeName" :value="item.id"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="将必填项填写完整才可提交问卷" placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="问题状态：" v-if="detailType==1">
								<el-select v-model="choiceItem.questionState" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="surveyQuestionModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="insertSurveyQuestion()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateSurveyQuestion(choiceItem)" type="success" size="small"
						v-if="detailType==1">保存
					</el-button>
				</div>

			</div>
		</el-drawer>

		<!--问卷调查记录-->
		<el-drawer size="60%" :with-header="false" :visible.sync="surveyRecordModal" direction="rtl">
			<div class="handle-box" style="margin: 20px 20px;">
				<h2>问卷调查记录{{signInTips}}</h2>
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="searchText" placeholder="请输入填写人、门店名称等"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="9">
							<el-form-item label="筛选时间">
								<div style="width: 180px;display: block;">
									<el-date-picker v-model="searchTime" type="datetimerange" format="yyyy-MM-dd"
										:picker-options="pickerOptions1" range-separator="至" start-placeholder="开始日期"
										end-placeholder="结束日期" align="right" value-format="yyyy-MM-dd HH:mm:ss">
									</el-date-picker>
								</div>
							</el-form-item>
						</el-col>
						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="listSurveyRecord()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="searchTime=[];searchText='';listSurveyRecord()">重置
							</el-button>

							<el-button type="danger" @click="recordDownload()">导出</el-button>
						</el-col>
					</el-row>
				</el-form>

				<el-table :data="surveyRecordList" v-loading="loading" border
					:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
					:expand-row-keys="expands" @expand-change="expandSelect">

					<el-table-column type="expand">
						<el-table :data="surveyAnswerRecordList" v-loading="loading"
							style="width: 1800px;margin-top: 20px;" border
							:header-cell-style="{background:'#f8f8f9',fontWeight:400,color:'#000000'}">
							<el-table-column prop="id" width="80" label="编号" sortable>
							</el-table-column>

							<el-table-column prop="questionTitle" width="120" label="问题">
							</el-table-column>

							<el-table-column prop="answerContent" width="120" label="回答">
								<template slot-scope="scope">
									<span v-if="scope.row.answerType!=4">{{scope.row.answerContent||'-'}}</span>
									<img :src="scope.row.answerContent||blankImg" style="width: 50px;height: 50px;"
										@click="openImg(scope.row.answerContent||blankImg)" v-else />
								</template>
							</el-table-column>
						</el-table>
					</el-table-column>

					<el-table-column prop="id" width="80" label="编号" sortable>
					</el-table-column>

					<el-table-column width="140" prop="memberId" label="会员id">
					</el-table-column>

					<el-table-column width="140" prop="employeeNo" label="员工工号">
					</el-table-column>

					<el-table-column width="140" prop="memberName" label="填写人">
						<template slot-scope="scope">
							<span>{{scope.row.employeeName||scope.row.memberName||'匿名用户'}}</span>
						</template>
					</el-table-column>

					<el-table-column width="160" prop="createTime" label="填写时间" sortable>
					</el-table-column>

					<el-table-column width="140" prop="storeName" label="门店名称">
					</el-table-column>

					<el-table-column width="140" prop="signinNum" label="是否签到" sortable>
						<template slot-scope="scope">
							<span>{{scope.row.signinNum?'是':'否'}}</span>
						</template>
					</el-table-column>
				</el-table>

				<div style="margin-top: 20px">
					<el-button type="danger" @click="surveyRecordModal=false">关闭</el-button>
				</div>
			</div>
		</el-drawer>

		<!-- 添加问题列表 -->
		<el-dialog :visible.sync="questionIdListModal" width="60%" title="添加问题列表" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;" v-if="choiceItem">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-checkbox-group v-model="choiceItem.questionIdListArray" value-key="id">
						<el-checkbox-button v-for="(val,index) in questionList" :key="index"
							:label="val.id">{{ val.id }}：{{ val.questionTitle }}
						</el-checkbox-button>
					</el-checkbox-group>
				</el-row>

				<div style="margin: 20px 400px;width: 100%;">
					<el-button @click="questionIdListModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="questionIdListModal=false" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

		<!-- 入口二维码 -->
		<el-dialog :visible.sync="entryModal" width="30%" title="预约活动入口" :mask-closable="false">
			<img :src="entryUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

	</div>

</template>

<script>
	import Vue from 'vue';

	export default {
		name: "survey",

		data() {
			return {
				// 可设置
				// 是否超级管理员（可执行删除等敏感操作）
				isAdmin: false,

				url: '',
				imageUrl: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				excelUploadId: 1,
				excelUploadUrl: "",

				// 测试时使用
				// uploadUrl: "http://localhost:8063/files/uploadFiles",
				// excelUploadUrlOrgin: "http://localhost:8063/courseContent/excelImport",

				uploadUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
				excelUploadUrlOrgin: "https://biapi.xiaoyujia.com/courseContent/excelImport",
				deleteTips: "确定删除选中问卷吗？该操作无法恢复，请谨慎操作!",
				isEdit: false,
				showEdit: true,
				imgModal: false,
				entryUrl: false,
				entryModal: false,
				surveyModal: false,
				surveyQuestionModal: false,
				questionIdListModal: false,
				surveyRecordModal: false,
				detailType: 0,
				loading: true,
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				pageSizeOpts: [10, 20, 50, 100],
				list: [],
				courseList: [],
				liveRoomList: [],
				questionList: [],
				surveyRecordList: [],
				surveyAnswerRecordList: [],
				choiceIndex: '0',
				choiceIndexNow: '0',
				choiceItemIndex: 0,
				choiceItem: {},
				choiceRecordId: 0,
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				signInTips: '',
				searchText: '',
				searchType: null,
				searchState: null,
				searchRequired: null,
				quer: {
					search: "",
					questionIdList: null,
					surveyState: null,
					typeState: null,
					orderBy: "createTime ASC",
					current: 1,
					size: 10
				},
				switchStateList: [{
						id: 0,
						typeName: "关闭",
						type: "info"
					},
					{
						id: 1,
						typeName: "开启",
						type: "success"
					}
				],
				typeList: [{
					id: 0,
					typeName: "普通"
				}],
				stateList: [{
					value: 0,
					text: "下架",
					type: "info"
				}, {
					value: 1,
					text: "上架",
					type: "success"
				}],
				typeStyleList: [{
						id: 0,
						type: "success"
					},
					{
						id: 1,
						type: "info"
					},
					{
						id: 2,
						type: "warning"
					}, {
						id: 3,
						type: "primary"
					}
				],
				answerTypeList: [{
						id: 0,
						typeName: "文本"
					},
					{
						id: 1,
						typeName: "数字"
					},
					{
						id: 2,
						typeName: "判断"
					},
					{
						id: 3,
						typeName: "星级"
					},
					{
						id: 4,
						typeName: "图片"
					}
				],
				searchTime: [],
				pickerOptions: {
					shortcuts: [{
						text: '昨天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '今天',
						onClick(picker) {
							picker.$emit('pick', new Date());
						}
					}, {
						text: '明天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周前',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周后',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}]
				},
				pickerOptions1: {
					shortcuts: [{
						text: '今天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '昨天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}]
				}
			}
		},
		created() {
			if (this.$route.query.isAdmin) {
				this.isAdmin = true
			}
			this.getData()
		},
		methods: {
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceIndex = key
				this.expands = []
				this.showEdit = true
				this.reset()
			},
			getData() {
				this.choiceIndexNow = 2
				if (this.choiceIndex == 0) {
					this.$postData("pageSurvey", this.quer).then(res => {
						this.loading = false
						if (res.status == 200) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
							for (let item of this.list) {
								this.$set(item, "questionIdListArray", this.strToArray(item
									.questionIdList))
							}
							this.choiceIndexNow = this.choiceIndex
						} else {
							this.list = []
							this.$message.error('未查询到相关问卷!')
							this.choiceIndexNow = this.choiceIndex
						}
					})
					this.listSurveyQuestion()
					this.listCourseAndLive()
				} else if (this.choiceIndex == 1) {
					this.$postData("pageSurveyQuestion", this.quer).then(res => {
						this.loading = false
						if (res.status == 200) {
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
							this.choiceIndexNow = this.choiceIndex
						} else {
							this.list = []
							this.$message.error('未查询到相关问题!')
							this.choiceIndexNow = this.choiceIndex
						}
					})
				}
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.quer.surveyState = this.searchState
				this.quer.questionState = this.searchState
				this.quer.surveyType = this.searchType
				this.quer.required = this.searchRequired
				this.getData()
			},
			// 重置
			reset() {
				this.loading = true
				this.quer.search = ""
				this.quer.surveyState = null
				this.quer.surveyType = null
				this.quer.questionState = null
				this.quer.required = null

				this.quer.orderBy = "t.id ASC"
				this.quer.current = 1

				this.searchText = ''
				this.searchState = null
				this.searchType = null
				this.searchRequired = null
				this.isEdit = false
				this.getData()
			},
			listSurveyQuestion() {
				this.$postData("listSurveyQuestion", {
					questionState: 1
				}).then(res => {
					if (res.status == 200) {
						this.questionList = res.data
					} else {
						this.questionList = []
						this.$message.error('未查询到相关问卷题目!')
					}
				})
			},
			listCourseAndLive() {
				this.$postData("listCourse", {}).then(res => {
					if (res.status == 200) {
						this.courseList = res.data
					}
				})
			},
			listSurveyRecord() {
				let quer = {
					surveyId: this.list[this.choiceItemIndex].id,
					search: this.searchText,
					startTime: null,
					endTime: null,
					orderBy: 'id ASC'
				}
				// 处理一下时间筛选字段
				if (this.searchTime && this.searchTime.length >= 2) {
					quer.startTime = this.searchTime[0]
					quer.endTime = this.searchTime[1]
				} else {
					quer.startTime = null
					quer.endTime = null
				}
				this.$postData("listSurveyRecord", quer).then(res => {
					if (res.status == 200) {
						this.surveyRecordList = res.data
						let total = this.surveyRecordList.length
						let count = 0
						this.surveyRecordList.forEach(item => {
							count += (item.signinNum ? 1 : 0)
						})
						let rate = (count / total * 100).toFixed(2) + '%'
						this.signInTips = '（实际签到/报名人数：' + count + '/' + total + '，签到率：' + rate + '）'
					} else {
						this.surveyRecordList = []
						this.$message.error('未查询到相关问卷调查记录!')
					}
				})
			},
			// 获取问卷记录回答详情
			listSurveyAnswerRecord() {
				this.$getUrl("listSurveyAnswerRecord", this.choiceRecordId).then(res => {
					if (res.status == 200) {
						this.surveyAnswerRecordList = res.data
					} else {
						this.surveyAnswerRecordList = []
						this.$message.error('未查询到问卷回答!')
					}
					this.expands.push(this.choiceRecordId)
				})
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						this.showEdit = false
						this.isEdit = false
						this.choiceRecordId = row.id
						this.listSurveyAnswerRecord()
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "问卷图标") {
					tips = "推荐尺寸：200*200"
				} else if (column.label == "问卷图片") {
					tips = "推荐尺寸：200*200"
				} else if (column.label == "等级") {
					tips = "数字越大，等级越高"
				} else if (column.label == "问卷类型") {
					tips = "可根据类型进行分类"
				} else if (column.label == "人数限制") {
					tips = "本期问卷达到填写人数限制后，不再允许填写"
				} else if (column.label == "门店填写限制") {
					tips = "同一个门店最多允许多少个人填写"
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			// 将字符串转化为数组
			strToArray(val) {
				let arrayString = []
				let array = []
				arrayString = val.split(',')
				arrayString.forEach(it => {
					let value = parseInt(it)
					if (value != null && value != 0) {
						array.push(value)
					}
				})
				return array
			},
			// 格式化有效期
			formatValidity(validity) {
				let result = validity ? validity + "天" : "暂未录入"
				if (result == '999天') {
					result = '永久'
				}
				return result
			},
			// 格式化问卷类型
			formatType(val) {
				let result = "暂无"
				let id = val.surveyQuestion
				for (let item of this.typeList) {
					if (id == item.id) {
						result = item.typeName
						break
					}
				}
				return result
			},
			formatTypeStyle(type) {
				return this.typeStyleList[type % 4].type
			},
			// 联盟试题模板下载
			recordDownload() {
				let name = this.list[this.choiceItemIndex].surveyTitle || ''
				let id = this.list[this.choiceItemIndex].id || 0
				this.$postData("listSurveyRecordExcelDownload", {
					surveyId: id
				}, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('问卷调查记录下载成功!')
					this.blobExport({
						tablename: name + "-问卷调查记录",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 问卷Excel模板下载
			excelDownload() {
				this.$postData("excelDownload", null, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('问卷Excel模板下载成功!')
					this.blobExport({
						tablename: "问卷Excel模板",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.$set(this.choiceItem, "surveyIcon", res.data)
				} else if (this.uploadImgType == 1) {
					this.$set(this.choiceItem, "surveyImg", res.data)
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadId = index

				if (this.uploadImgType == 0) {
					this.$message.info('推荐上传尺寸：250*250')
				} else if (this.uploadImgType == 1) {
					this.$message.info('推荐上传尺寸：250*250')
				}
			},
			// 打开图片预览
			openImg(url) {
				this.imageUrl = url || this.blankImg
				this.imgModal = true
			},
			// 打开视频预览
			openVideo(url) {
				if (url != null && url != '') {
					this.videoUrl = url
				}
				this.videoModal = true
			},

			openModal(index, index1) {
				if (index == 0) {
					this.detailType = 0
					this.choiceItem = {}
					this.$set(this.choiceItem, "questionIdListArray", [])
					this.surveyModal = true
				} else if (index == 1) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.surveyModal = true
				} else if (index == 2) {
					if (this.detailType == 0) {
						if (!this.choiceItem.questionIdListArray || this.choiceItem.questionIdListArray.length ==
							0) {
							this.$set(this.choiceItem, "questionIdListArray", [])
						}
					} else if (this.detailType == 1) {
						// this.choiceItem = this.list[this.choiceItemIndex]
					}
					this.questionIdListModal = true
				} else if (index == 3) {
					this.detailType = 0
					this.choiceItem = {}
					this.surveyQuestionModal = true
				} else if (index == 4) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.surveyQuestionModal = true
				} else if (index == 5) {
					this.choiceItemIndex = index1
					this.listSurveyRecord()
					this.surveyRecordModal = true
				}
			},
			openEntry(value, index) {
				let id = this.choiceItem.id
				if (value == 0) {
					$.ajax({
						type: "POST",
						url: 'https://biapi.xiaoyujia.com/image/getWxShareImg',
						data: JSON.stringify({
							textList: [{
								"text": this.choiceItem.roomTitle,
								"fontSize": 50,
								"isCenter": true,
								"color": "0xfed472",
								"isBold": true,
								"x": 0,
								"y": 1020
							}],
							qrCodeStyle: {
								"width": 350,
								"height": 350,
								"x": 200,
								"y": 250
							},
							maxWidth: 600,
							img: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/QR_img/QR_examEntry.png',
							path: 'pages-other/store/survey',
							scene: "id/2*sId/" + id,
							source: "xyjacn",
							type: 1
						}),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.status == 200) {
								this.entryUrl = res.data
								this.entryModal = true
							} else {
								this.$message.error(res.msg)
							}
						},
						error: res => {
							this.$message.error('当前暂无回放录播哦！')
						}
					})

				}
			},
			//打开员工信息详情
			rowClick(id) {

			},
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			// 删除权限标签
			closeQuestionTags(index, index1) {
				this.list[index].questionIdListArray.splice(index1, 1)
			},
			closeQuestionTags1(index, index1) {
				this.choiceItem.questionIdListArray.splice(index1, 1)
			},
			// 添加问卷
			insertSurvey() {
				let survey = this.choiceItem
				if (!survey.surveyTitle) {
					this.$message.error('请填写问卷标题！')
				} else if (!survey.surveyContent) {
					this.$message.error('请填写问卷内容！')
				} else {
					let no = localStorage.getItem('account') || 'admin'
					this.$set(survey, "creator", no)

					// 格式化权限角色列表
					if (survey.questionIdListArray) {
						survey.questionIdList = survey.questionIdListArray.join(',')
						if (!survey.questionIdList.length) {
							survey.questionIdList = '0'
						}
					}
					this.$postData("insertSurvey", survey).then(res => {
						if (res.status == 200) {
							this.$message.success('问卷添加成功!')
							this.surveyModal = false
							this.list.push(res.data)
						} else {
							this.$message.error('问卷添加失败！' + res.msg)
						}
					})
				}
			},
			// 问卷问题
			insertSurveyQuestion() {
				let surveyQuestion = this.choiceItem
				if (!surveyQuestion.questionTitle) {
					this.$message.error('请填写问题标题！')
				} else if (!surveyQuestion.questionContent) {
					this.$message.error('请填写问题内容！')
				} else {
					let no = localStorage.getItem('account') || 'admin'
					this.$set(surveyQuestion, "creator", no)
					this.$postData("insertSurveyQuestion", surveyQuestion).then(res => {
						if (res.status == 200) {
							this.$message.success('问卷问题添加成功!')
							this.surveyQuestionModal = false
							this.list.push(res.data)
						} else {
							this.$message.error('问卷问题添加失败！' + res.msg)
						}
					})
				}
			},
			// 删除问卷
			deleteSurvey(val) {
				this.$postData("deleteSurvey", val).then(res => {
					if (res.status == 200) {
						this.$message.success('问卷删除成功!')
						this.$delete(this.list, this.getIndex(val.id))
					} else {
						this.$message.error('问卷删除失败！' + res.msg)
					}
				})
			},
			// 删除问卷问题
			deleteSurveyQuestion(val) {
				this.$postData("deleteSurveyQuestion", val).then(res => {
					if (res.status == 200) {
						this.$message.success('问卷问题删除成功!')
						this.$delete(this.list, this.getIndex(val.id))
					} else {
						this.$message.error('问卷问题删除失败！' + res.msg)
					}
				})
			},
			// 更改问卷
			updateSurvey(val) {
				// 格式化权限角色列表
				if (val.questionIdListArray) {
					val.questionIdList = val.questionIdListArray.join(',')
					if (!val.questionIdList.length) {
						val.questionIdList = '0'
					}
				}
				this.$postData("updateSurvey", val).then(res => {
					if (res.status == 200) {
						this.$message.success('问卷更新成功!')
						this.list[this.choiceItemIndex] = this.choiceItem
					} else {
						this.$message.error('问卷更新失败！' + res.msg)
					}
				})
			},
			// 更改问卷问题
			updateSurveyQuestion(val) {
				// 格式化权限角色列表
				if (val.questionIdListArray) {
					val.questionIdList = val.questionIdListArray.join(',')
					if (!val.questionIdList.length) {
						val.questionIdList = '0'
					}
				}
				this.$postData("updateSurveyQuestion", val).then(res => {
					if (res.status == 200) {
						this.$message.success('问卷问题更新成功!')
						this.list[this.choiceItemIndex] = this.choiceItem
					} else {
						this.$message.error('问卷问题更新失败！' + res.msg)
					}
				})
			},
			beforeImgUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!');
				}
				return isLt2M;
			}
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>