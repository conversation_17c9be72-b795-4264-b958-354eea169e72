<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <div id="searchDiv">
            </div>
            <div class="fromInput">
                <Form ref="dto" :model="dto"
                      :rules="ruleValidate" :label-width="80" label-position="left">
                    <FormItem label="培训日期" prop="trainTime">
                        <el-date-picker
                                v-model="dto.trainTime"
                                type="date"
                                style="width: 250px"
                                placeholder="选择日期">
                        </el-date-picker>

                    </FormItem>
                    <FormItem label="培训课时" prop="trainPeriod">
                        <Input v-model="dto.trainPeriod" clearable
                               style="width: 250px"
                               placeholder="培训课时">
                        </Input>
                    </FormItem>
                    <FormItem label="考核成绩" prop="trainResult">
                        <Input v-model="dto.trainResult" clearable
                               style="width: 250px"
                               placeholder="考核成绩">
                        </Input>
                    </FormItem>
                    <FormItem label="考核情况" prop="trainSituation">
                        <Select filterable clearable style="width: 120px" v-model="dto.trainSituation">
                            <Option value="">请选择</Option>
                            <Option value="1">合格</Option>
                            <Option value="2">不合格</Option>
                        </Select>
                    </FormItem>
                    <div v-if="show===true">
                        <FormItem label="评价类型" prop="scoreType">
                            <Select filterable clearable
                                    style="width: 250px" v-model="dto.scoreType">
                                <Option value="">请选择</Option>
                                <Option v-for="item in evaluateList" :value="item.id">{{ item.text}}</Option>
                            </Select>
                        </FormItem>

                        <FormItem label="积分扣减" prop="changeScore">
                            <InputNumber :max="100" :min="-100" v-model="dto.changeScore"></InputNumber>
                        </FormItem>

                        <FormItem label="备注">
                            <Input v-model="dto.reamke" type="textarea" clearable
                                   style="width: 250px"
                                   :autosize="{minRows: 4,maxRows: 4}"
                                   placeholder="备注">
                            </Input>
                        </FormItem>
                    </div>
                    <FormItem>
                        <div>
                            <Button style="margin-left:3rem" type="success"
                                    @click="update('dto')"> 保存
                            </Button>
                            <Button style="margin-left: 3rem" @click="chooseThisModel('dto')">取消</Button>
                        </div>
                    </FormItem>
                </Form>
            </div>
        </div>
    </div>
</template>
<script>
    export default {
        props: ['stage'],
        data() {
            return {
                evaluateList: [],
                show: true,
                dto: {
                    id: this.stage.id,
                    trainTime: null,
                    trainRemark: null,
                    trainPeriod: null,
                    trainResult: null,
                    trainSituation: null,
                    trainScoreDetails: {
                        scoreType: null,
                        changeScore: null,
                        reamke: null,
                        employeeNo: null,
                    },
                    scoreType: null,
                    changeScore: null,
                    reamke: null,
                    trainScoreId: null,
                },
                ruleValidate: {
                    trainTime: [
                        {required: true, message: '请选择日期', trigger: 'change', type: 'date'}
                    ],
                    trainPeriod: [
                        {required: true, message: '请填写课时', trigger: 'change'}
                    ],
                    trainResult: [
                        {required: true, message: '请填写考核成绩', trigger: 'change'}
                    ],
                    trainSituation: [
                        {required: true, message: '请选择考核情况', trigger: 'change'}
                    ],
                    scoreType: [
                        {required: true, message: '请选择评价类型 ', trigger: 'change', type: "number"}
                    ],
                    changeScore: [
                        {required: true, message: '请输入积分扣减 ', trigger: 'change',type: "number"}
                    ],
                },
            }
        },
        components: {},
        created: function () {
            this.getById();
            this.getEvaluateList();
        },
        methods: {
            handleChange(value) {
                this.dto.changeScore = value
            },
            getById() {
                this.$getData("getByTrainId", {id: this.dto.id}).then(res => {
                   ;
                    if (res.status === 200) {
                        this.dto = res.data;
                        this.dto.trainSituation = this.dto.trainSituation + "";
                        if (this.dto.trainTime) {
                            this.dto.trainTime = new Date(this.dto.trainTime)
                        }
                        if (this.dto.trainScoreDetails != null) {
                            this.dto.changeScore = this.dto.trainScoreDetails.changeScore;
                            this.dto.scoreType = this.dto.trainScoreDetails.scoreType;
                            this.dto.reamke = this.dto.trainScoreDetails.reamke
                        }
                    }
                })
            },
            update(name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        this.updateTs();
                        if (this.dto.trainScoreId == null || this.dto.trainScoreId === '') {
                            this.storeDetailsInsert()
                        } else {
                            this.storeDetailsUpdate()
                        }
                    }
                })
            },

            updateTs() {
                if (this.dto.lastTime != null) {
                    this.dto.lastTime = new Date(this.dto.lastTime);
                }
                this.$postData("tsUpdate", this.dto, {}).then(res => {
                    if (res.status === 200) {
                        this.$Message.success('保存成功');
                        this.chooseThisModel();
                    } else {
                        this.$message.error("保存失败，" + res.msg);
                    }
                });
            },
            storeDetailsInsert() {
                this.dto.trainScoreDetails.employeeNo = this.dto.employeeCaptain.no;
                this.dto.trainScoreDetails.scoreType = this.dto.scoreType;
                this.dto.trainScoreDetails.changeScore = this.dto.changeScore;
                this.dto.trainScoreDetails.reamke = this.dto.reamke;
                this.$postData("storeDetails_insert", this.dto.trainScoreDetails).then(res => {
                    if (res.status === 200) {
                        this.dto.trainScoreId = res.data;
                        this.updateTs()
                    }
                })
            },
            storeDetailsUpdate() {
                this.dto.trainScoreDetails.employeeNo = this.dto.employeeCaptain.no;
                this.dto.trainScoreDetails.scoreType = this.dto.scoreType;
                this.dto.trainScoreDetails.changeScore = this.dto.changeScore;
                this.dto.trainScoreDetails.reamke = this.dto.reamke;
                this.$postData("storeDetails_update", this.dto.trainScoreDetails).then(res => {
                })
            },
            getEvaluateList() {
                this.$postUrl("get_dict", 121, null, {}).then(res => {
                    if (res.status === 200) {
                       ;
                        this.evaluateList = res.data
                    }
                })
            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(dto) {
                this.$emit('init-choose', dto);
                //this.update(this[dto])
            },
        }
    }
</script>


<style scoped>

    /* 查询div*/
    #searchDiv {
        width: 160px;
        float: left;
        height: 500px;
        margin-left: 1rem;
    }

    .fromInput {
        float: left;
    }

    #bodyDiv {
        font-size: 15px !important;
        background-color: #fff;
        height: 500px;
    }
</style>

