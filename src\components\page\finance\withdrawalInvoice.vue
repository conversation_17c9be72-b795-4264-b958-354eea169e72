<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="9">
            <el-form-item label="申请时间">
              <el-date-picker v-model="days" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="门店名称">
              <el-input
                  clearable
                  v-model="form.storeName"
                  placeholder="请输入门店名称"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item  label="状态">
              <el-select style="width:160px" v-model="form.state" clearable placeholder="请选择状态">
                <el-option v-for="item in options"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item  label="发票类型">
              <el-select style="width:160px" v-model="form.invoiceType" clearable placeholder="请选择发票类型">
                <el-option v-for="item in typeOptions"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="招工发票号">
              <el-input
                  clearable
                  v-model="form.recruitDevelopInvoiceNo"
                  placeholder="请输入发票号"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="平台管理发票号">
              <el-input
                  clearable
                  v-model="form.manageInvoiceNo"
                  placeholder="请输入发票号"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="3">
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="申请记录">
          <el-table :data="logList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column prop="createTime" label="提现时间" width="150"></el-table-column>
            <el-table-column prop="state" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.state===1" type="warning">待处理</el-tag>
                <el-tag v-if="scope.row.state===2" type="success">已开票</el-tag>
                <el-tag v-if="scope.row.state===3" type="danger">已驳回</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="state" label="开票类型" width="80">
              <template slot-scope="scope" >
                <span style="color: red">{{scope.row.state===1?'普票':'专票'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="storeName" label="门店名称" width="160"></el-table-column>
            <el-table-column prop="realName" label="申请人" width="100"></el-table-column>
            <el-table-column prop="recruitDevelopAmount" label="招工开发类开票金额" width="130">
              <template slot-scope="scope">
                <span :style="scope.row.recruitDevelopAmount>0?'color: red;font-size: 23px':''">{{scope.row.recruitDevelopAmount}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="manageAmount" label="平台管理开票金额" width="130">
              <template slot-scope="scope">
                <span :style="scope.row.manageAmount>0?'color: red;font-size: 23px':''">{{scope.row.manageAmount}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="recruitDevelopInvoiceNo" label="招工开发类发票号" width="160"></el-table-column>
            <el-table-column prop="manageInvoiceNo" label="平台管理发票号" width="160"></el-table-column>
            <el-table-column prop="taxId" label="税号" width="170"></el-table-column>
            <el-table-column prop="invoiceHeader" label="发票抬头" width="170"></el-table-column>
            <el-table-column prop="unitAddress" label="单位地址" width="140"></el-table-column>
            <el-table-column prop="phone" label="手机号" width="130"></el-table-column>
            <el-table-column prop="bankOfDeposit" label="开户银行" width="160"></el-table-column>
            <el-table-column prop="bankAccount" label="银行账号" width="160"></el-table-column>
            <el-table-column prop="email" label="电子邮箱" width="160"></el-table-column>
            <el-table-column label="发票金额组成" width="140">
              <template slot-scope="scope">
              <el-tag type="primary" @click="selectOrderList(scope.row.id,scope.row.storeId)">点击查看</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="operatorName" label="处理人" width="80"></el-table-column>
            <el-table-column prop="remark" label="备注" width="100"></el-table-column>
            <el-table-column prop="operateTime" label="处理时间" width="150"></el-table-column>

            <el-table-column label="操作" fixed="right" width="100">
              <template slot-scope="scope">
                <div v-if="(roleId==56||roleId==2||roleId==57)">
                  <el-button v-if="roleId==57||roleId==56||roleId==2" :disabled="scope.row.state===2||scope.row.state===3" @click.native.prevent="showRow(scope.row)" type="text" size="small">操作</el-button>
                </div>

              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
        <el-dialog :visible.sync="dialogVisible">
          <el-form>
            <el-form-item label="处理结果">
              <el-tag v-if="dom.state===1" type="warning">待处理</el-tag>
              <el-tag v-if="dom.state===2" type="success">已开票</el-tag>
              <el-tag v-if="dom.state===3" type="danger">已驳回</el-tag>
            </el-form-item>
          </el-form>
          <el-button type="primary" @click="innerVisible = true">更新</el-button>
          <br><br>
        </el-dialog>
        <el-dialog title="处理" :visible.sync="innerVisible" append-to-body center>
          <el-form>
            <el-form-item label="状态">
              <el-radio-group v-model="dom.state">
                <el-radio  label="2">已开票</el-radio>
                <el-radio  label="3">驳回</el-radio>
              </el-radio-group>
            </el-form-item>
            <br>
            <div v-if="dom.state==='2'" style="font-size: 23px;font-weight: bold">招工发票金额：{{item.recruitDevelopAmount}}</div>
            <div v-if="dom.state==='2'" style="font-size: 23px;font-weight: bold;padding-bottom: 20px">平台管理发票金额：{{item.manageAmount}}</div>
            <el-form-item v-if="dom.state==='2'" label="招工发票号">
              <el-input type="text" :rows="5" placeholder="请输入发票号" v-model="dom.recruitDevelopInvoiceNo"> </el-input>
            </el-form-item>
            <el-form-item v-if="dom.state==='2'" label="平台管理发票号">
              <el-input type="text" :rows="5" placeholder="请输入发票号" v-model="dom.manageInvoiceNo"> </el-input>
            </el-form-item>
            <el-form-item v-if="dom.state==='3'" label="驳回原由">
              <el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="dom.remark"> </el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="updateWithdrawalInvoice">确定</el-button>
          </div>
        </el-dialog>
      </el-tabs>
    </div>
    <el-dialog
        title="导出出账明细："
        :visible.sync="czmxDialogFlag"
        width="80%">
      <el-form ref="form">
        <el-row>
          <el-col :span="7">
            <el-form-item label="提现时间">
              <el-date-picker v-model="dcmxDays" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="到账时间">
              <el-date-picker v-model="dzDays" type="daterange" unlink-panels :picker-options="dzPickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item  label="所属门店" >
              <el-select style="width:220px" filterable v-model="dcmxQuery.storeId" clearable placeholder="请选择所属门店">
                <el-option v-for="item in storeOptions"
                           :key="item.id" :label="item.storeName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3" style="margin-top: 30px;margin-left: 50px">
            <el-button type="info"
                       @click="exportPaymentOut"
                       icon="el-icon-download">导出
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
    <el-dialog
        title="查询门店账户实时余额："
        :visible.sync="storeBalanceFlag"
        width="80%">
      <el-form ref="form">
        <el-row>
          <el-col :span="4">
            <el-form-item  label="查询门店" >
              <el-select style="width:220px" filterable v-model="balanceStoreId" clearable placeholder="请选择查询门店">
                <el-option v-for="item in storeOptions"
                           :key="item.id" :label="item.storeName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3" style="margin-top: 30px;margin-left: 50px">
            <el-button type="primary"
                       style="margin-bottom: 15px"
                       @click="getStoreBalance"
                       icon="el-icon-search"
                       v-loading="selectLoading">查询
            </el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-row style="font-size: 25px;margin-bottom: 20px;margin-top: 20px;">
        <el-col :span="8" >
          <span>累计收入：￥{{storeAccount.accumulateIncome}}</span>
        </el-col>
        <el-col :span="8" >
          <span>累计提现：￥{{storeAccount.accumulateWithdrawal}}</span>
        </el-col>
        <el-col :span="8" >
          <span>账户余额：￥{{storeAccount.residueMoney}}</span>
        </el-col>
      </el-row>
    </el-dialog>
    <el-dialog
        title="提现来源："
        :visible.sync="dialog"
        width="80%">
      <div style="display: flex">
        <div>订单总金额：{{withdrawalData.sumRealPay||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <div>实付总金额：{{withdrawalData.sumAmount||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <div>扣减总金额：{{withdrawalData.sumDeduction||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <div>结算总金额：{{withdrawalData.sumSettle||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <el-button type="info"
                   style="margin-bottom: 15px"
                   @click="download"
                   icon="el-icon-download">导出
        </el-button>
      </div>
      <el-table  :data="orderList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">

        <el-table-column
            fixed="left"
            prop="productName"
            label="服务项目"
            width="120"
        ></el-table-column>
        <el-table-column
            prop="serviceName"
            label="服务人员"
            width="75"
        ></el-table-column>
        <el-table-column
            prop="serviceNo"
            label="服务工号"
            width="121"
        ></el-table-column>
        <el-table-column
            prop="paySettlementTime"
            label="结算时间"
            width="140"
        ></el-table-column>
        <el-table-column
            prop="realTotalAmount"
            label="订单金额"
            width="80"
        ></el-table-column>
        <el-table-column
            prop="amount"
            label="实付金额"
            width="80"
        ></el-table-column>
        <el-table-column
            prop="deductionMoney"
            label="扣减金额"
            width="80"
        ></el-table-column>
        <el-table-column
            prop="settleMoney"
            label="结算金额"
            width="80"
        ></el-table-column>
        <el-table-column
            prop="memberName"
            label="客户名称"
            width="75"
        ></el-table-column>
        <el-table-column
            prop="billNo"
            label="订单编号"
            width="150"
        ></el-table-column>
        <el-table-column
            prop="remark"
            label="扣减说明"
        ></el-table-column>
      </el-table>
    </el-dialog>
    <!--图片预览-->
    <el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
      <img :src="imageUrl!=null?imageUrl:blankImg" style="width: 100%;height: auto;margin: 0 auto;"/>
    </el-dialog>
    <el-link :href="hrefUrl" target="_blank" id="elLink"/>
    <el-dialog
        title="发票信息："
        :visible.sync="invoiceDialog"
        width="80%">
      <div>
      <div>图片发票：</div>
      <div v-for="url in txData.imgInvoiceUrlList">
        <el-col :span="3">
          <el-image
              style="width: 100px; height: 100px"
              :src="url"
              :preview-src-list="txData.imgInvoiceUrlList">
          </el-image>
        </el-col>
      </div>
      </div>

      <div style="height: 150px"></div>
      <div style="height: 250px">
      <div>文件发票：</div>
      <div v-for="(url,index) in txData.fileInvoiceUrlList">
        <el-col :span="3">
          <el-link :href="url" type="primary" target="_blank">文件发票{{index+1}}</el-link>
        </el-col>
      </div>
      </div>
    </el-dialog>

    <el-dialog
        title="银行转账记录查询："
        :visible.sync="dialogCCB"
        width="80%">
      <div style="display: flex">
       <span style="color:red">系统会半小时轮询查询转账进度,如需手动查询，请点击“手动查询”按钮。</span>
        <el-button type="primary" @click="queryccbcallBack">手动查询</el-button>
      </div>
      <el-table  :data="ccbCallBackList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
        <el-table-column
            prop="createTime"
            label="请求时间"
        ></el-table-column>
        <el-table-column
            prop="requestCodeMsg"
            label="请求响应结果"
        ></el-table-column>
        <el-table-column
            prop="requestDealCodeMsg"
            label="处理结果"
        ></el-table-column>
        <el-table-column
            prop="requestType"
            label="请求类型"
        >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.requestType===1" type="warning">转账</el-tag>
            <el-tag v-if="scope.row.requestType===2" type="success">查询</el-tag>
          </template>

        </el-table-column>
      </el-table>
    </el-dialog>


  </div>
</template>

<script>
import moment from "moment";
import log from "echarts/src/scale/Log";
export default {
  name: "register",
  data() {
    return {
      dialogCCB:false,
      selectLoading:false,
      ccbCallBackList:[],
      logList: [],
      imgModal: false,
      handler: [],
      txData: [],
      invoiceDialog: false,
      czmxDialogFlag: false,
      storeBalanceFlag: false,
      orderList: [],
      imageUrl: '',
      roleId: localStorage.getItem('roleId'),
      hrefUrl: "",
      blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
      dialog: false,
      getOrderObj: {
        storeId: null,
        id: null,
      },
      storeId: null,
      withdrawalData: {},
      dialogVisible: false,
      innerVisible: false,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      days: [],
      queryDays: [],
      dcmxDays: [],
      dzDays: [],
      storeAccount:{
        accumulateIncome: 0.00,
        residueMoney: 0.00,
        accumulateWithdrawal: 0.00,
      },
      form: {
        state: '',
        startTime: '',
        endTime: '',
        storeName: '',
        roleId: localStorage.getItem('roleId'),
        accountPeople: '',
        cashier: '',
        manageInvoiceNo: '',
        recruitDevelopInvoiceNo: '',
        billNo: '',
        serialNumber: '',
        id: '',
        invoiceType: '',
        current: 1,
        size: 10
      },
      item: {},
      dom: {
        state: null,
        operatorPeople: localStorage.getItem("id"),
        id: null,
        recruitDevelopInvoiceNo: null,
        manageInvoiceNo: null,
        remark: null,
      },
      options: [{
        value:  '1',
        label: '待处理'
      },{
        value:  '2',
        label: '已开票'
      },{
        value:  '3',
        label: '已驳回'
      }],
      balanceStoreId: null,
      dcmxQuery: {
        storeId: null,
        startTime: '',
        endTime: '',
        dzStartTime: '',
        dzEndTime: '',
      },
      typeOptions: [{
        value:  '1',
        label: '普票'
      },{
        value:  '2',
        label: '专票'
      }],
      storeOptions: [],
      dzPickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },

  created() {
    this.getData();
    // this.getAllFranchiseStore();
  },
  methods: {
    getAllFranchiseStore(){
      this.$getData("getAllFranchiseStore", {}, {}).then(res => {
        if (res.code == 0) {
          this.storeOptions = res.data
        } else {
          this.$message.error("查询筛选门店失败!");
        }
      })
    },
    getStoreBalance(){
      this.selectLoading = true
      this.$getData("getStoreBalance", { storeId: this.balanceStoreId}).then(res => {
        if(res.code==0){
            this.storeAccount = res.data
            this.selectLoading = false
        }else{
          this.selectLoading = false
          this.$message.error("查询门店账户实时余额失败，请稍后重试!");
        }
      })
    },
    exportPaymentOut(){
      if (this.dcmxDays != null && this.dcmxDays.length > 0) {
        this.dcmxQuery.startTime = moment(this.dcmxDays[0]).format("YYYY-MM-DD");
        this.dcmxQuery.endTime = moment(this.dcmxDays[1]).format("YYYY-MM-DD")
      }else{
        this.dcmxQuery.startTime = ""
        this.dcmxQuery.endTime = ""
      }
      if (this.dzDays != null && this.dzDays.length > 0) {
        this.dcmxQuery.dzStartTime = moment(this.dzDays[0]).format("YYYY-MM-DD");
        this.dcmxQuery.dzEndTime = moment(this.dzDays[1]).format("YYYY-MM-DD")
      }else{
        this.dcmxQuery.dzStartTime = ""
        this.dcmxQuery.dzEndTime = ""
      }
      let url = "https://biapi.xiaoyujia.com/storeWithdrawal/exportPaymentOut"
      if (this.dcmxQuery.storeId){
        url = url+"?storeId="+this.dcmxQuery.storeId
      }
      if (this.dcmxQuery.startTime&&!this.dcmxQuery.storeId){
        url = url+"?startTime="+this.dcmxQuery.startTime+"&endTime="+ this.dcmxQuery.endTime
      }else if(this.dcmxQuery.startTime&&this.dcmxQuery.storeId){
        url = url+"&startTime="+this.dcmxQuery.startTime+"&endTime="+ this.dcmxQuery.endTime
      }
      if (this.dcmxQuery.dzStartTime&&!this.dcmxQuery.storeId&&!this.dcmxQuery.startTime){
        url = url+"?dzStartTime="+this.dcmxQuery.dzStartTime+"&dzEndTime="+ this.dcmxQuery.dzEndTime
      }else if(this.dcmxQuery.dzStartTime&&(this.dcmxQuery.storeId||this.dcmxQuery.startTime)){
        url = url+"&dzStartTime="+this.dcmxQuery.dzStartTime+"&dzEndTime="+ this.dcmxQuery.dzEndTime
      }
      this.hrefUrl = url
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    selectOrderList(id,storeId){
      this.$message.error("完善中！");
      return
      this.dialog = true;
      this.getOrderObj.id = id
      this.getOrderObj.storeId = storeId
      this.storeId = storeId
      this.$getData("getWithdrawalOrder", this.getOrderObj, {}).then(res => {
        if (res.code == 0) {
          this.orderList = res.data.list
          this.withdrawalData = res.data
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    },
    showRow(item) {
      this.dialogVisible = true;
      this.item = item
      this.dom.state = item.state;
      this.dom.id = item.id;
    },
    updateWithdrawalInvoice() {
      if(this.dom.state==3&&!this.dom.remark){
        return this.$message.error("请输入驳回原由！");
      }
      if(this.dom.state==2&&this.item.recruitDevelopAmount>0&&!this.dom.recruitDevelopInvoiceNo){
        return  this.$message.error("请输入招工发票号！");
      }
      if(this.dom.state==2&&this.item.manageAmount>0&&!this.dom.manageInvoiceNo){
        return this.$message.error("请输入平台管理发票号！");
      }
      this.innerVisible = false;
      this.$postData("updateWithdrawalInvoice", this.dom).then(res => {
        if (res.code === 0) {
          this.$message({message: res.data, type: 'success'});
          this.dom.manageInvoiceNo = ""
          this.dom.recruitDevelopInvoiceNo = ""
          this.dom.remark = ""
        } else {
          this.$message({message: res.msg, type: 'warning'});
        }
        this.getData();
      })
      this.dialogVisible = false;
    },
    // 页码大小
    onPageSizeChange(size) {
      this.form.size = size;
      this.getData();
    },
    onChange(index) {
      this.form.current = index;
      this.getData();
    },
    getData() {
      this.form.startTime = '';
      this.form.endTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      this.$getData("getWithdrawalInvoice", this.form, {}).then(res => {
        if (res.code == 0) {
          this.logList = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    }
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
