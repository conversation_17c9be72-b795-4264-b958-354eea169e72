<template>
  <div>
    <div class="container">
    <span>申请时间： </span>
    <el-date-picker v-model="days" type="daterange" align="right" unlink-panels
                    :editable="false" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false"
                    range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    :picker-options="pickerOptions" @change="screen">
    </el-date-picker>
    </div>
    <el-row :gutter="10">
      <el-col :span="9">
        <div class="container">
          <el-button type="primary"
                     :loading="loadingExcel"
                     @click="download"
                     icon="el-icon-download">导出
          </el-button>
          <el-table :data="list" class="table" v-loading="loading"
                    :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
            <el-table-column label="排名" prop="" width="50">
              <template slot-scope="scope">
                <span v-text="getIndex(scope.$index)"> </span>
              </template>
            </el-table-column>
            <el-table-column
                prop="introducerName"
                width="120"
                label="推荐人">
            </el-table-column>
            <el-table-column
                prop="name"
                width="120"
                label="名称">
            </el-table-column>
            <el-table-column
                width="70"
                prop="num"
                label="招工数">
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :current="this.dto.current"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="10"/>
          </div>
        </div>
      </el-col>
      <el-col :span="7">
        <div class="container">
          <el-button type="primary"
                     :loading="loadingExcel2"
                     @click="download2"
                     icon="el-icon-download">导出
          </el-button>
          <el-table :data="list2" class="table" v-loading="loading2"
                    :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
            <el-table-column label="排名" prop="" width="50">
              <template slot-scope="scope">
                <span v-text="getIndex2(scope.$index)"> </span>
              </template>
            </el-table-column>
            <el-table-column
                width="100"
                prop="channel"
                label="渠道">
            </el-table-column>
            <el-table-column
                width="70"
                prop="num"
                label="招工数">
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo2.total"
                  @on-change="onChange2"
                  :current="this.dto2.current"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts2"
                  @on-page-size-change="onPageSizeChange2"
                  :page-size="10"/>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="container">
          <el-button type="primary"
                     :loading="loadingExcel3"
                     @click="download3"
                     icon="el-icon-download">导出
          </el-button>
          <el-table :data="list3" class="table" v-loading="loading3"
                    :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
            <el-table-column label="排名" prop="" width="50">
              <template slot-scope="scope">
                <span v-text="getIndex3(scope.$index)"> </span>
              </template>
            </el-table-column>
            <el-table-column
                width="100"
                prop="applicantType"
                label="报名类型">
            </el-table-column>
            <el-table-column
                width="70"
                prop="num"
                label="报名人数">
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo3.total"
                  @on-change="onChange3"
                  :current="this.dto3.current"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts3"
                  @on-page-size-change="onPageSizeChange3"
                  :page-size="10"/>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "recruitmentRanking",
  data() {
    return {
      list: null,
      loading: false,
      loadingExcel: false,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      dto: {
        size: 10,
        startTime: null,
        endTime: null,
        current: 1,
      },
      value1: '',
      days: [],
      list2: null,
      loading2: false,
      loadingExcel2: false,
      pageSizeOpts2: [10, 20, 30],
      pageInfo2: {total: 10, size: 10, current: 1, pages: 1},
      dto2: {
        startTime: null,
        endTime: null,
        size: 10,
        current: 1,
      },
      list3: null,
      loading3: false,
      loadingExcel3: false,
      pageSizeOpts3: [10, 20, 30],
      pageInfo3: {total: 10, size: 10, current: 1, pages: 1},
      dto3: {
        startTime: null,
        endTime: null,
        size: 10,
        current: 1,
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },
  created() {
    this.getData();
    this.recruitmentRankingAndChannel();
    this.recruitmentRankingType();
  },
  methods: {
    screen(){
      this.getData();
      this.recruitmentRankingAndChannel();
      this.recruitmentRankingType();
    },
    //获取表格序号
    getIndex($index) {
      return (this.pageInfo.current - 1) * this.pageInfo.size + $index + 1
    },
    getData() {
      if (this.days != null && this.days.length > 0) {
        this.dto.startTime = this.days[0];
        this.dto.endTime = this.days[1];
      }else {
        this.dto.startTime =null;
        this.dto.endTime =null;
      }
      this.$postData("recruitmentRanking", this.dto).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    download() {
      this.loadingExcel = true;
      this.$postData("recruitDownload", this.dto, {responseType: "arraybuffer"}).then(res => {
        this.loadingExcel = false;
        this.blobExport({tablename: "招聘排行榜-推荐人", res: res});
      })
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dto.size = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dto.current = index;
      this.getData();
    },

    //获取表格序号
    getIndex2($index) {
      return (this.pageInfo2.current - 1) * this.pageInfo2.size + $index + 1
    },
    download2() {
      this.loadingExcel2 = true;
      this.$postData("recruitDownload2", this.dto2, {responseType: "arraybuffer"}).then(res => {
        this.loadingExcel2 = false;
        this.blobExport({tablename: "招聘排行榜-渠道", res: res});
      })
    },
    // 页码大小
    onPageSizeChange2(size) {
      this.loading2 = true;
      this.dto2.size = size;
      this.recruitmentRankingAndChannel();
    },
    // 跳转页码
    onChange2(index) {
      this.loading2 = true;
      this.dto2.current = index;
      this.recruitmentRankingAndChannel();
    },
    recruitmentRankingAndChannel() {
      if (this.days != null && this.days.length > 0) {
        this.dto2.startTime = this.days[0];
        this.dto2.endTime = this.days[1];
      }else {
        this.dto2.startTime =null;
        this.dto2.endTime =null;
      }
      this.$postData("recruitmentRankingAndChannel", this.dto2).then(res => {
        this.loading2 = false;
        if (res.status === 200) {
          this.list2 = res.data.records;
          this.pageInfo2.current = res.data.current;
          this.pageInfo2.size = res.data.size;
          this.pageInfo2.total = res.data.total
        }
      })
    },
    recruitmentRankingType(){
      if (this.days != null && this.days.length > 0) {
        this.dto3.startTime = this.days[0];
        this.dto3.endTime = this.days[1];
      }else {
        this.dto3.startTime =null;
        this.dto3.endTime =null;
      }
      this.$postData("recruitmentRankingType", this.dto3).then(res => {
        this.loading3 = false;
        if (res.status === 200) {
          this.list3 = res.data.records;
          this.pageInfo3.current = res.data.current;
          this.pageInfo3.size = res.data.size;
          this.pageInfo3.total = res.data.total
        }
      })
    },
    //获取表格序号
    getIndex3($index) {
      return (this.pageInfo3.current - 1) * this.pageInfo3.size + $index + 1
    },
    // 页码大小
    onPageSizeChange3(size) {
      this.loading3 = true;
      this.dto3.size = size;
      this.recruitmentRankingType();
    },
    // 跳转页码
    onChange3(index) {
      this.loading3 = true;
      this.dto3.current = index;
      this.recruitmentRankingType();
    },
    download3() {
      this.loadingExcel3 = true;
      this.$postData("recruitDownload3", this.dto3, {responseType: "arraybuffer"}).then(res => {
        this.loadingExcel3 = false;
        this.blobExport({tablename: "招聘排行榜-招工类型", res: res});
      })
    },
  },
}
</script>

<style scoped>

</style>