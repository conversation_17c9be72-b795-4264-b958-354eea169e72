<template style="background-color: #000">
    <div>
        <el-upload
                class="upload-demo"
                ref="upload"
                drag
                :limit="1"
                :on-success="onSuccess"
                :auto-upload="false"
                :action="Url">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传Excel文件</div>
        </el-upload>
        <div>
            <el-button  size="small" @click="exportList">下载模版</el-button>
        </div>
        <div style="margin-left: 60%;height: 40px" >
            <div style="float: left"> <el-button  size="small" @click="submitUpload">确定</el-button></div>
            <div style="float: left"> <el-button  size="small" @click="chooseThisModel">取消</el-button></div>
        </div>

    </div>
</template>


<script>
    export default {
        data() {
            return {
               Url:"http://biapi.xiaoyujia.com",
                testUrl:"http://localhost:8069/income/uploadExcel",
                files:{
                    fileName:"TryProduct/Banner/"
                },
            }
        },
        components: {},
        created: function () {
        },
        methods: {
            submitUpload() {
                this.$refs.upload.submit();
            },
            onSuccess(res, file, fileList){
               ;
                if(res.status==200){
                    this.$Message.success('导入成功');
                    this.chooseThisModel();
                }else {
                    this.$Message.success('导入失败');
                }
            },
            exportList() {
                this.$postData("income_uploadTemplate", {}, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tablename: "收入支出",
                        res: res
                    });
                });
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
            /*
      * 关闭当前窗口
      * */
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            }
        },


    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 150px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

