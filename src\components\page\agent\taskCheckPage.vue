<template>
    <div class="table">
        <div class="container">
            <el-form ref="form">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="处理人">
                            <el-input
                                    clearable
                                    v-model="form.employeeName"
                                    placeholder="处理人"
                                    style="width:200px"
                                    class="handle-input mr10"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="处理门店">
                            <el-input
                                    clearable
                                    v-model="form.storeName"
                                    placeholder="处理门店"
                                    style="width:200px"
                                    class="handle-input mr10"
                            ></el-input>
                        </el-form-item>
                    </el-col>

                    <!--                    <el-col :span="16">-->
                    <!--                        <el-form-item label="操作时间">-->
                    <!--                            <el-date-picker-->
                    <!--                                    v-model="value2"-->
                    <!--                                    type="datetimerange"-->
                    <!--                                    :picker-options="pickerOptions"-->
                    <!--                                    range-separator="至"-->
                    <!--                                    start-placeholder="开始日期"-->
                    <!--                                    end-placeholder="结束日期"-->
                    <!--                                    value-format="yyyy-MM-dd HH:mm:ss"-->
                    <!--                                    align="right">-->
                    <!--                            </el-date-picker>-->
                    <!--                        </el-form-item>-->
                    <!--                    </el-col>-->

                    <el-col :span="8" >

                        <el-form-item>
                            <el-button type="success"  round @click="form.current=1,getData()">搜索</el-button>
                        </el-form-item>

                    </el-col>
                </el-row>
            </el-form>
            <el-radio-group v-model="form.status" @change="form.current=1,getData()">
                <el-radio :label="null">全部</el-radio>
                <el-radio :label="1">已签到</el-radio>
                <el-radio :label="0">未签到</el-radio>
            </el-radio-group>

            <br>
            <br>
            <el-table :data="taskList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable" @cell-dblclick="">
                <el-table-column
                        type="selection"
                        width="40">
                </el-table-column>


                <el-table-column
                        prop="status"
                        label="状态"
                        width="80">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.status==1" type="success">已签到</el-tag>
                        <el-tag v-else-if="scope.row.status==0" type="warning">未签到</el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="creatTime"
                        label="创建时间"
                        width="100"
                >
                    <template slot-scope="scope">
                        {{scope.row.creatTime.substring(0,10)}}
                    </template>
                </el-table-column>
                <el-table-column
                        prop="checkTime"
                        label="签到时间"
                        width="180"
                ></el-table-column>
                <el-table-column
                        prop="employeeName"
                        label="处理人"
                        width="100">
                </el-table-column>
                <el-table-column
                        prop="storeName"
                        label="处理门店"
                        width="180">
                </el-table-column>
                <el-table-column
                        prop="startTime"
                        label="签到时间段">
                    <template slot-scope="scope">
                        {{scope.row.startTime.substring(10,20)}} 至
                        {{scope.row.endTime.substring(10,20)}}
                    </template>
                </el-table-column>

<!--                <el-table-column-->
<!--                        label="操作"-->
<!--                        fixed="right"-->
<!--                        min-width="180">-->
<!--                    <template slot-scope="scope">-->
<!--                        <el-button-->
<!--                                v-if="scope.row.status==1"-->
<!--                                @click.native.prevent="showRow(scope.row)"-->
<!--                                type="text"-->
<!--                                size="small">-->
<!--                            查看详情-->
<!--                        </el-button>-->
<!--                    </template>-->

<!--                </el-table-column>-->
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>

    </div>
</template>

<script>
    export default {
        name: "taskCheckPage",
        data() {
            return {
                responseInfoModal:false,
                show:null,
                taskList:[],
                pageSizeOpts:[10,20,30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                form: {
                    storeName:null,
                    roleId:null,
                    employeeId:null,
                    title:null,
                    search:null,
                    employeeName:null,
                    status:null,
                    current: 1,
                    size: 10
                },
                user:{
                    id:localStorage.getItem("id"),
                    storeId:localStorage.getItem("storeId"),
                    roleId:localStorage.getItem("roleId"),
                }
            }
        },

        created(){
            this.getData();
        },
        methods:{

            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.form.size = size;
                this.getData();
            },
            onChange(index) {
                console.log(index)
                this.form.current = index;
                this.getData();
            },
            getData() {
                if (this.user.roleId!=='1'){
                    if (this.user.roleId !=='66'){
                        this.form.employeeId=this.user.id
                    }else {
                        this.form.storeId=this.user.storeId
                    }
                }
                this.$postData("taskCheckPage", this.form, {}).then(res => {
                    if (res.status == 200) {
                        this.taskList = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
        }

    }
</script>

<style scoped>

</style>
