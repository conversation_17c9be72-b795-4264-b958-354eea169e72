<template>
	<div class="container" id="pdfDom">

		<!-- 搜索栏目 -->
		<div class="handle-box">
			<el-form ref="form" :model="pageInfo">
				<el-row>
					<el-col :span="3">
						<el-form-item label="大区名称" style="margin-right: 20px">
							<el-input v-model="quer.regionName" placeholder="请输入大区名称"></el-input>
						</el-form-item>
					</el-col>
          <el-col :span="3">
            <el-form-item label="区域部门" style="margin-right: 20px">
              <el-input v-model="quer.department" placeholder="请输入区域部门"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="区域商务经理" style="margin-right: 20px">
              <el-input v-model="quer.commerceManagerSearch" placeholder="请输入工号、姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="标准交付" style="margin-right: 20px">
              <el-input v-model="quer.standardDeliverSearch" placeholder="请输入工号、姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="三嫂交付" style="margin-right: 20px">
              <el-input v-model="quer.sanSaoDeliverSearch" placeholder="请输入工号、姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="流量" style="margin-right: 20px">
              <el-input v-model="quer.flowSearch" placeholder="请输入工号、姓名"></el-input>
            </el-form-item>
          </el-col><el-col :span="3">
          <el-form-item label="技术" style="margin-right: 20px">
            <el-input v-model="quer.technologySearch" placeholder="请输入工号、姓名"></el-input>
          </el-form-item>
        </el-col>
          <el-col :span="3">
            <el-form-item label="私域" style="margin-right: 20px">
              <el-input v-model="quer.privateDomainSearch" placeholder="请输入工号、姓名"></el-input>
            </el-form-item>
          </el-col>
				</el-row>
        <el-row>
          <el-col :span="3">
            <el-form-item label="联盟运营" style="margin-right: 20px">
              <el-input v-model="quer.allianceOperationsSearch" placeholder="请输入工号、姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="区域督导" style="margin-right: 20px">
              <el-input v-model="quer.regionalSupervisionSearch" placeholder="请输入工号、姓名"></el-input>
            </el-form-item>
          </el-col>  <el-col :span="3">
          <el-form-item label="培训老师" style="margin-right: 20px">
            <el-input v-model="quer.trainingTeacherSearch" placeholder="请输入工号、姓名"></el-input>
          </el-form-item>
        </el-col>  <el-col :span="3">
          <el-form-item label="设计" style="margin-right: 20px">
            <el-input v-model="quer.designSearch" placeholder="请输入工号、姓名"></el-input>
          </el-form-item>
        </el-col>
          <el-col :span="3">
            <el-form-item label="售后" style="margin-right: 20px">
              <el-input v-model="quer.afterSalesSearch" placeholder="请输入工号、姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="财务" style="margin-right: 20px">
              <el-input v-model="quer.financeSearch" placeholder="请输入工号、姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
          <el-form-item label="行政" style="margin-right: 20px">
            <el-input v-model="quer.administrationSearch" placeholder="请输入工号、姓名"></el-input>
          </el-form-item>
        </el-col>
          <el-col :span="3" style="margin: 32px 0;">
            <el-button type="primary" @click="query()" icon="el-icon-search">搜索
            </el-button>

            <el-button style="margin-left: 10px" type="success" icon="el-icon-circle-plus-outline" @click="addStoreRegion">添加
            </el-button>
          </el-col>
        </el-row>
			</el-form>
		</div>




		<!-- 数据表格 -->
		<el-table :data="list" v-loading="loading" border stripe
			:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
			:expand-row-keys="expands" @expand-change="expandSelect">

			<el-table-column width="170" prop="regionName" label="大区名称">
				<template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.regionName }}</span>

          <el-input v-model="scope.row.regionName" style="width: 100%;" type="text" v-if="scope.row.isEdit"
                    placeholder="请输入大区名称" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>

      <el-table-column width="140" label="门店配置">
        <template slot-scope="scope">
          <el-tag type="primary" @click="selectBingDingStore(scope.row)">点击配置</el-tag>
        </template>
      </el-table-column>


      <el-table-column width="140" prop="commerceManager" label="区域商务经理">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.commerceManagerName+'-'+scope.row.commerceManager }}</span>

          <el-input v-model="scope.row.commerceManager" style="width: 100%;" type="text" v-if="scope.row.isEdit"
                    placeholder="请输入区域商务经理工号" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>

      <el-table-column width="140" prop="standardDeliver" label="标准交付">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.standardDeliverName+'-'+scope.row.standardDeliver }}</span>

          <el-input v-model="scope.row.standardDeliver" style="width: 100%;" type="text" v-if="scope.row.isEdit"
                    placeholder="请输入标准交付工号" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>


      <el-table-column width="140" prop="sanSaoDeliver" label="三嫂交付">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.sanSaoDeliverName+'-'+scope.row.sanSaoDeliver }}</span>

          <el-input v-model="scope.row.sanSaoDeliver" style="width: 100%;" type="text" v-if="scope.row.isEdit"
                    placeholder="请输入三嫂交付工号" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>


      <el-table-column width="140" prop="flow" label="流量">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.flowName+'-'+scope.row.flow }}</span>

          <el-input v-model="scope.row.flow" style="width: 100%;" type="text" v-if="scope.row.isEdit"
                    placeholder="请输入流量工号" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>


      <el-table-column width="140" prop="technology" label="技术">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.technologyName+'-'+scope.row.technology }}</span>

          <el-input v-model="scope.row.technology" style="width: 100%;" type="text" v-if="scope.row.isEdit"
                    placeholder="请输入技术工号" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>



      <el-table-column width="140" prop="privateDomain" label="私域">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.privateDomainName+'-'+scope.row.privateDomain }}</span>

          <el-input v-model="scope.row.privateDomain" style="width: 100%;" type="text" v-if="scope.row.isEdit"
                    placeholder="请输入私域工号" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>



      <el-table-column width="140" prop="allianceOperations" label="联盟运营">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.allianceOperationsName+'-'+scope.row.allianceOperations }}</span>

          <el-input v-model="scope.row.allianceOperations" style="width: 100%;" type="text" v-if="scope.row.isEdit"
                    placeholder="请输入联盟运营工号" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>



      <el-table-column width="140" prop="regionalSupervision" label="区域督导">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.regionalSupervisionName+'-'+scope.row.regionalSupervision }}</span>

          <el-input v-model="scope.row.regionalSupervision" style="width: 100%;" type="text" v-if="scope.row.isEdit"
                    placeholder="请输入区域督导工号" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>


      <el-table-column width="140" prop="commerceManager" label="培训老师">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.trainingTeacherName+'-'+scope.row.trainingTeacher }}</span>

          <el-input v-model="scope.row.trainingTeacher" style="width: 100%;" type="text" v-if="scope.row.isEdit"
                    placeholder="请输入培训老师工号" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>


      <el-table-column width="140" prop="design" label="设计">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.designName+'-'+scope.row.design }}</span>

          <el-input v-model="scope.row.design" style="width: 100%;" type="text" v-if="scope.row.isEdit"
                    placeholder="请输入设计工号" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>


      <el-table-column width="140" prop="afterSales" label="售后">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.afterSalesName+'-'+scope.row.afterSales }}</span>

          <el-input v-model="scope.row.afterSales" style="width: 100%;" type="text" v-if="scope.row.isEdit"
                    placeholder="请输入售后工号" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>


      <el-table-column width="140" prop="finance" label="财务">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.financeName+'-'+scope.row.finance }}</span>

          <el-input v-model="scope.row.finance" style="width: 100%;" type="text" v-if="scope.row.isEdit"
                    placeholder="请输入财务工号" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>


      <el-table-column width="140" prop="commerceManager" label="行政">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.administrationName+'-'+scope.row.administration }}</span>

          <el-input v-model="scope.row.administration" style="width: 100%;" type="text" v-if="scope.row.isEdit"
                    placeholder="请输入行政工号" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>


      <el-table-column width="140" prop="department" label="区域部门">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.department }}</span>

          <el-input v-model="scope.row.department" style="width: 100%;" type="text" v-if="scope.row.isEdit"
                    placeholder="请输入工号、姓名" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>

			<el-table-column width="140" prop="createTime" label="创建时间">
				<template slot-scope="scope">
					<span>{{scope.row.createTime}}</span>
				</template>
			</el-table-column>

			<el-table-column width="130" prop="createPeopleNo" label="创建人">
				<template slot-scope="scope">
					<span >{{scope.row.createPeopleName+'-'+scope.row.createPeopleNo}}</span>
				</template>
			</el-table-column>

      <el-table-column width="140" prop="updateTime" label="更新时间">
        <template slot-scope="scope">
          <span>{{scope.row.updateTime}}</span>
        </template>
      </el-table-column>

      <el-table-column width="130" prop="updatePeopleNo" label="更新人">
        <template slot-scope="scope">
          <span >{{scope.row.updatePeopleName?scope.row.updatePeopleName+'-'+scope.row.updatePeopleNo:'无'}}</span>
        </template>
      </el-table-column>


			<el-table-column fixed="right" min-width="100" label="操作" v-if="showEdit">
				<template slot-scope="scope">
					<el-button @click="openEdit(scope.row)" type="success" size="small" :disabled="scope.row.isEdit"
						v-if="!scope.row.isEdit" icon="el-icon-edit">修改
					</el-button>
					<el-button @click="updateStoreRegion(scope.row)" type="primary" size="small"
						v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
					</el-button>
				</template>
			</el-table-column>

		</el-table>


		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--添加大区-->
		<el-dialog :visible.sync="certModal" width="60%" title="添加区域" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;">
        <el-row style="width:100%;height: auto;margin-bottom: 0px;">
          <el-col :span="12">
            <el-form ref="ruleForm" label-width="160px" class="demo-ruleForm" size="mini">

              <el-form-item label="区域名称：">
                <el-input v-model="storeRegion.regionName" type="text" class="handle-input mr10"
                          placeholder="请输入区域名称">
                </el-input>
              </el-form-item>

              <el-form-item label="区域部门：">
                <el-input v-model="storeRegion.department" type="text" class="handle-input mr10"
                          placeholder="请输入区域部门">
                </el-input>
              </el-form-item>


              <el-form-item label="同步其他区域负责人：">
                <el-select v-model="optionValue" placeholder="请选择大区" clearable @change="changeOption">
                  <el-option
                      v-for="item in regionOptions"
                      :key="item.id"
                      :label="item.regionName"
                      :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>


              <el-form-item label="区域商务经理：">
                <el-input v-model="storeRegion.commerceManager" type="text" class="handle-input mr10"
                          placeholder="请输入区域商务经理">
                </el-input>
              </el-form-item>


              <el-form-item label="标准交付：">
                <el-input v-model="storeRegion.standardDeliver" type="text" class="handle-input mr10"
                          placeholder="请输入标准交付">
                </el-input>
              </el-form-item>


              <el-form-item label="三嫂交付：">
                <el-input v-model="storeRegion.sanSaoDeliver" type="text" class="handle-input mr10"
                          placeholder="请输入三嫂交付">
                </el-input>
              </el-form-item>


              <el-form-item label="流量：">
                <el-input v-model="storeRegion.flow" type="text" class="handle-input mr10"
                          placeholder="请输入流量">
                </el-input>
              </el-form-item>

              <el-form-item label="技术：">
                <el-input v-model="storeRegion.technology" type="text" class="handle-input mr10"
                          placeholder="请输入技术">
                </el-input>
              </el-form-item>


            </el-form>
          </el-col>

          <el-col :span="12">
            <el-form ref="ruleForm" label-width="110px" class="demo-ruleForm" size="mini">

              <el-form-item label="私域：">
                <el-input v-model="storeRegion.privateDomain" type="text" class="handle-input mr10"
                          placeholder="请输入私域">
                </el-input>
              </el-form-item>

              <el-form-item label="联盟运营：">
                <el-input v-model="storeRegion.allianceOperations" type="text" class="handle-input mr10"
                          placeholder="请输入联盟运营">
                </el-input>
              </el-form-item>

              <el-form-item label="区域督导：">
                <el-input v-model="storeRegion.regionalSupervision" type="text" class="handle-input mr10"
                          placeholder="请输入区域督导">
                </el-input>
              </el-form-item>
              <el-form-item label="培训老师：">
                <el-input v-model="storeRegion.trainingTeacher" type="text" class="handle-input mr10"
                          placeholder="请输入培训老师">
                </el-input>
              </el-form-item>

              <el-form-item label="设计：">
                <el-input v-model="storeRegion.design" type="text" class="handle-input mr10"
                          placeholder="请输入设计">
                </el-input>
              </el-form-item>
              <el-form-item label="售后：">
                <el-input v-model="storeRegion.afterSales" type="text" class="handle-input mr10"
                          placeholder="请输入售后">
                </el-input>
              </el-form-item>
              <el-form-item label="财务：">
                <el-input v-model="storeRegion.finance" type="text" class="handle-input mr10"
                          placeholder="请输入财务">
                </el-input>
              </el-form-item>
              <el-form-item label="行政：">
                <el-input v-model="storeRegion.administration" type="text" class="handle-input mr10"
                          placeholder="请输入行政">
                </el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="saveStoreRegion()" type="success" size="small">确认添加
					</el-button>
				</div>

			</div>
		</el-dialog>


    <el-dialog
        title="门店列表："
        :visible.sync="dialogFlag"
        width="80%">
      <div style="display: flex">
          <el-input style="width: 15%;" v-model="store.storeName" placeholder="请输入大区名称"></el-input>
        <div style="width: 20px"></div>
        <el-select v-model="store.state" placeholder="门店状态" clearable>
          <el-option
              v-for="item in stateOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <el-button style="margin-left: 10px;margin-bottom: 20px;" type="primary" icon="el-icon-search"
                   @click="getStoreByRegionId">搜索
        </el-button>
        <el-button style="margin-left: 10px;margin-bottom: 20px;" type="success" icon="el-icon-circle-plus-outline"
                   @click="taskModal=true,getAllFranchiseStore()">添加门店
        </el-button>
      </div>
      <el-table :data="storeList" v-loading="storeLoading" border stripe
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
                :expand-row-keys="expands" @expand-change="expandSelect">

        <el-table-column width="200" prop="storeName" label="门店名称">
          <template slot-scope="scope">
            <span>{{ scope.row.storeName }}</span>
          </template>
        </el-table-column>

        <el-table-column width="140" prop="state" label="门店状态">
          <template slot-scope="scope">
            <span>{{scope.row.state==1?'营业中':'关店中'}}</span>
          </template>
        </el-table-column>

        <el-table-column width="140" prop="commerceManager" label="项目经理">
          <template slot-scope="scope">
            <span>{{ scope.row.commerceManager +'-'+ scope.row.commerceManagerName}}</span>
          </template>
        </el-table-column>

        <el-table-column width="140" prop="sanSaoDeliver" label="三嫂交付">
          <template slot-scope="scope">
            <span>{{ scope.row.sanSaoDeliver +'-'+ scope.row.sanSaoDeliverName}}</span>
          </template>
        </el-table-column>

        <el-table-column width="140" prop="commerceManager" label="标准交付">
          <template slot-scope="scope">
            <span>{{ scope.row.commerceManager +'-'+ scope.row.commerceManagerName}}</span>
          </template>
        </el-table-column>


        <el-table-column width="140" prop="flow" label="流量">
          <template slot-scope="scope">
            <span>{{ scope.row.flow +'-'+ scope.row.flowName}}</span>
          </template>
        </el-table-column>


        <el-table-column width="140" prop="technology" label="技术">
          <template slot-scope="scope">
            <span>{{ scope.row.technology +'-'+ scope.row.technologyName}}</span>
          </template>
        </el-table-column>


        <el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
          <template slot-scope="scope">
            <el-popconfirm title="此操作将会把当前门店所绑定的负责人同步为大区负责人，确认操作吗?" @confirm="synchronousHead(scope.row)">
            <el-button type="success" size="small" slot="reference" icon="el-icon-edit">同步为区域负责人
            </el-button>
            </el-popconfirm>
            <el-button @click="openRegionTransfer(scope.row)" type="primary" size="small" icon="el-icon-edit">区域转移
            </el-button>

          </template>
        </el-table-column>

      </el-table>
      <div class="pagination">
        <Page :total="storePageInfo.total" @on-change="storeOnChange" :current="storePageInfo.current" :show-total="true"
              :show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="storeOnPageSizeChange"
              :page-size="storePageInfo.size" />
      </div>

    </el-dialog>

    <el-dialog :visible.sync="taskModal" width="30%" title="添加区域门店" :mask-closable="false">
      <div style="height: 200px;">
                  <el-select filterable  style="width: 250px;" v-model="addRegionData.id" placeholder="请选择门店" clearable>
                    <el-option v-for="item in storeOptions"
                               :key="item.id" :label="item.storeName" :value="item.id">
                    </el-option>
                  </el-select>
        <div style="width: 100%;margin-top: 50px;">
          <el-button @click="addRegionStore()" type="success" size="small">确定添加
          </el-button>
        </div>

      </div>
    </el-dialog>

    <el-dialog :visible.sync="transferCertModal" width="30%" title="区域转移" :mask-closable="false">
      <div style="height: 200px;">
        <el-select v-model="regionTransfer.changeRegionId" placeholder="请选择大区" clearable>
          <el-option
              v-for="item in regionOptions"
              :key="item.id"
              :label="item.regionName"
              :value="item.id">
          </el-option>
        </el-select>
        <div style="width: 100%;margin-top: 50px;">
          <el-button @click="storeRegionTransfer()" type="success" size="small">确认转移
          </el-button>
        </div>

      </div>
    </el-dialog>

	</div>

</template>

<script>

  export default {
    name: "qaLibraryType",
    components: {
    },
		data() {
			return {
        optionValue: '',
        regionOptions:[],
        addRegionData: {
          id: '',
        },
        stateOptions: [{
          value: 1,
          label: '营业中'
        }, {
          value: 2,
          label: '关店中'
        }],
        ifMustGoOptions: [{
          value: 0,
          label: '否'
        }, {
          value: 1,
          label: '是'
        }],
        taskModal: false,
				isEdit: false,
				showEdit: true,
        store: {
          creater: localStorage.getItem("id"),
          storeRegionId: null,
          state: null,
          storeName: "",
          current: 1,
          size: 10
        },
				certModal: false,
        transferCertModal: false,
        dialogFlag: false,
        storeList: [],
				list: [],
				loading: true,
				storeLoading: true,
				pageSizeOpts: [5, 10, 20],
        storeRegion: {
          createPeople: localStorage.getItem("id"),
          regionName: '',
          commerceManager: '',
          standardDeliver: '',
          sanSaoDeliver: '',
          flow: '',
          technology: '',
          privateDomain: '',
          allianceOperations: '',
          regionalSupervision: '',
          trainingTeacher: '',
          design: '',
          afterSales: '',
          finance: '',
          administration: '',
          department: '',
        },
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
        storePageInfo: {
          total: 10,
          size: 5,
          current: 1,
          pages: 1
        },
				expands: [],
        storeOptions: [],
        regionTransfer: {},
				getRowKeys(row) {
					return row.id
				},
				quer: {
					"regionName": "",
					"department": "",
					"commerceManagerSearch": "",
					"standardDeliverSearch": "",
					"sanSaoDeliverSearch": "",
					"flowSearch": "",
					"technologySearch": "",
					"privateDomainSearch": "",
					"allianceOperationsSearch": "",
					"regionalSupervisionSearch": "",
					"trainingTeacherSearch": "",
					"designSearch": "",
					"afterSalesSearch": "",
					"financeSearch": "",
					"administrationSearch": "",
					"current": 1,
					"size": 10
				},
			}
		},
		created() {
			this.getData()
		},
		methods: {
      getAllFranchiseStore(){
        this.$getData("getAllFranchiseStore", {}, {}).then(res => {
          if (res.code == 0) {
            this.storeOptions = res.data
          } else {
            this.$message.error("查询筛选门店失败!");
          }
        })
      },
      openRegionTransfer(item){
        this.regionTransfer = item
        this.regionTransfer.regionId = this.store.storeRegionId
        let obj = {
          "current": 1,
          "size": 99
        }
        this.$getData("getRegionData", obj).then(res => {
          if (res.code == 0) {
            this.regionOptions = res.data.records
          }
        })
        this.transferCertModal = true
      },
      addStoreRegion(){
        let obj = {
              "current": 1,
              "size": 99
            }
        this.$getData("getRegionData", obj).then(res => {
          if (res.code == 0) {
            this.regionOptions = res.data.records
          }
        })
        this.certModal = true
      },
      addRegionStore(){
        this.addRegionData.regionId = this.store.storeRegionId
        this.$postData("addRegionStore", this.addRegionData).then(res => {
          if (res.code == 0) {
            this.$message.success('添加成功!')
            this.taskModal = false
            this.getStoreByRegionId()
          } else {
            this.$message.error(res.msg)
          }
        })
      },
      storeRegionTransfer(){
        if(this.regionTransfer.regionId==this.regionTransfer.changeRegionId){
          this.$message.error('当前已绑定该大区，请重新选择!')
        }else{
          this.$postData("storeRegionTransfer", this.regionTransfer).then(res => {
            if (res.code == 0) {
              this.$message.success('转移成功!')
              this.transferCertModal = false
              this.getStoreByRegionId()
            } else {
              this.$message.error( res.msg)
            }
          })
        }
      },
      synchronousHead(item){
        item.regionId = this.store.storeRegionId
        this.$postData("synchronousStoreRegionHead", item).then(res => {
          if (res.code == 0) {
            this.$message.success('添加成功!')
            this.getStoreByRegionId()
          } else {
            this.$message.error( res.msg)
          }
        })
      },
      changeOption(){
        for (let i = 0; i < this.regionOptions.length; i++) {
          if(this.regionOptions[i].id==this.optionValue){
            this.storeRegion.commerceManager = this.regionOptions[i].commerceManager
            this.storeRegion.standardDeliver = this.regionOptions[i].standardDeliver
            this.storeRegion.sanSaoDeliver = this.regionOptions[i].sanSaoDeliver
            this.storeRegion.flow = this.regionOptions[i].flow
            this.storeRegion.technology = this.regionOptions[i].technology
            this.storeRegion.allianceOperations = this.regionOptions[i].allianceOperations
            this.storeRegion.privateDomain = this.regionOptions[i].privateDomain
            this.storeRegion.regionalSupervision = this.regionOptions[i].regionalSupervision
            this.storeRegion.trainingTeacher = this.regionOptions[i].trainingTeacher
            this.storeRegion.design = this.regionOptions[i].design
            this.storeRegion.afterSales = this.regionOptions[i].afterSales
            this.storeRegion.finance = this.regionOptions[i].finance
            this.storeRegion.administration = this.regionOptions[i].administration
            this.storeRegion.commerceManager = this.regionOptions[i].commerceManager
          }
        }

      },
      selectBingDingStore(val){
        this.store.storeRegionId = val.id
        this.store.current = 1
        this.store.size = 10
        this.dialogFlag = true
        this.getStoreByRegionId()
      },
      getStoreByRegionId(){
        // 获取绑定门店
        this.$getData("getStoreByRegionId", this.store).then(res => {
          this.storeLoading = false
          if (res.code == 0) {
            this.store.state = null
            this.store.storeName = ''
            this.storeList = res.data.records
            this.storePageInfo.current = res.data.current
            this.storePageInfo.size = res.data.size
            this.storePageInfo.total = res.data.total
          } else {
            this.$message.error('未查询到门店信息!')
          }
        })
      },
			getData() {
				// 获取大区信息
				this.$getData("getRegionData", this.quer).then(res => {
					this.loading = false
					if (res.code == 0) {
						this.list = res.data.records
						this.pageInfo.current = res.data.current
						this.pageInfo.size = res.data.size
						this.pageInfo.total = res.data.total
						// 追加编辑状态位
						for (let item of this.list) {
							this.$set(item, "isEdit", false)
						}
					} else {
						this.list = []
						this.$message.error('未查询到大区信息!')
					}
				})
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.getData()
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
      // 页码大小
      storeOnPageSizeChange(size) {
        this.storeLoading = true;
        this.store.size = size
        this.getStoreByRegionId()
      },
      // 跳转页码
      storeOnChange(index) {
        this.storeLoading = true;
        this.store.current = index
        this.getStoreByRegionId()
      },
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			// 添加区域数据
      saveStoreRegion() {
				// 数据校验
				if (!this.storeRegion.regionName) {
					this.$message.error('请补充完整后添加！')
				} else if (!this.storeRegion.department) {
          this.$message.error('请补充完整后添加！')
        } else if (!this.storeRegion.commerceManager) {
          this.$message.error('请补充完整后添加！')
        } else if (!this.storeRegion.standardDeliver) {
          this.$message.error('请补充完整后添加！')
        } else if (!this.storeRegion.sanSaoDeliver) {
          this.$message.error('请补充完整后添加！')
        } else if (!this.storeRegion.flow) {
          this.$message.error('请补充完整后添加！')
        } else if (!this.storeRegion.technology) {
          this.$message.error('请补充完整后添加！')
        } else if (!this.storeRegion.privateDomain) {
          this.$message.error('请补充完整后添加！')
        } else if (!this.storeRegion.allianceOperations) {
          this.$message.error('请补充完整后添加！')
        } else if (!this.storeRegion.regionalSupervision) {
          this.$message.error('请补充完整后添加！')
        } else if (!this.storeRegion.trainingTeacher) {
          this.$message.error('请补充完整后添加！')
        } else if (!this.storeRegion.design) {
          this.$message.error('请补充完整后添加！')
        } else if (!this.storeRegion.afterSales) {
          this.$message.error('请补充完整后添加！')
        } else if (!this.storeRegion.finance) {
          this.$message.error('请补充完整后添加！')
        } else if (!this.storeRegion.administration) {
          this.$message.error('请补充完整后添加！')
        } else {
					this.$postData("saveStoreRegion", this.storeRegion).then(res => {
						if (res.code == 0) {
							this.$message.success('添加成功!')
							this.certModal = false
              this.optionValue  = ''
              this.storeRegion.regionName  = ''
              this.storeRegion.commerceManager  = ''
              this.storeRegion.standardDeliver  = ''
              this.storeRegion.sanSaoDeliver  = ''
              this.storeRegion.flow  = ''
              this.storeRegion.technology  = ''
              this.storeRegion.privateDomain  = ''
              this.storeRegion.allianceOperations  = ''
              this.storeRegion.regionalSupervision  = ''
              this.storeRegion.trainingTeacher  = ''
              this.storeRegion.design  = ''
              this.storeRegion.afterSales  = ''
              this.storeRegion.finance  = ''
              this.storeRegion.administration  = ''
              this.storeRegion.department  = ''
							this.getData()
						} else {
							this.$message.error( res.msg)
						}
					})
				}
			},
			// 更改区域信息
      updateStoreRegion(val) {
        // 数据校验
        if (!val.regionName) {
          this.$message.error('请补充完整后添加！')
        } else if (!val.department) {
          this.$message.error('请补充完整后添加！')
        } else if (!val.commerceManager) {
          this.$message.error('请补充完整后添加！')
        } else if (!val.standardDeliver) {
          this.$message.error('请补充完整后添加！')
        } else if (!val.sanSaoDeliver) {
          this.$message.error('请补充完整后添加！')
        } else if (!val.flow) {
          this.$message.error('请补充完整后添加！')
        } else if (!val.technology) {
          this.$message.error('请补充完整后添加！')
        } else if (!val.privateDomain) {
          this.$message.error('请补充完整后添加！')
        } else if (!val.allianceOperations) {
          this.$message.error('请补充完整后添加！')
        } else if (!val.regionalSupervision) {
          this.$message.error('请补充完整后添加！')
        } else if (!val.trainingTeacher) {
          this.$message.error('请补充完整后添加！')
        } else if (!val.design) {
          this.$message.error('请补充完整后添加！')
        } else if (!val.afterSales) {
          this.$message.error('请补充完整后添加！')
        } else if (!val.finance) {
          this.$message.error('请补充完整后添加！')
        } else if (!val.administration) {
          this.$message.error('请补充完整后添加！')
        } else {
          val.updatePeople = localStorage.getItem("id")
          this.$postData("updateStoreRegion", val).then(res => {
            if (res.code == 0) {
              this.$message.success('更新成功!')
              this.getData()
              val.isEdit = false
            } else {
              this.$message.error( res.msg)
            }
          })
        }
			},
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>