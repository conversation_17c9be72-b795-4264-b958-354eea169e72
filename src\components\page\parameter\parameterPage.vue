<template>
    <div class="table">
        <div class="container">
            <el-row>
                <div style="padding-bottom: 10px">
                    <span class="headTitle">当日数据展示</span>
                    <el-divider direction="vertical"></el-divider>
                    <el-radio-group v-model="passDay" @change="parameterInfoDtoShow">
                        <el-radio-button :label="0">今天</el-radio-button>
                        <el-radio-button :label="1">昨日</el-radio-button>
                    </el-radio-group>
                    <el-divider direction="vertical"></el-divider>
                    <el-input-number v-model="passDay" @change="parameterInfoDtoShow" :min="0" :max="366" label="描述文字"></el-input-number>
                    <el-divider direction="vertical"></el-divider>
                    <el-button @click="setAllParameterData" type="primary" :loading="setAllParameterDataLoading"  icon="el-icon-refresh" v-if="showBtn">数据立即更新</el-button>
                    <el-divider direction="vertical"></el-divider>
                    <el-button @click="toAdd" type="info"   icon="el-icon-plus" v-if="showBtn">新增蜂窝</el-button>
                    <el-divider direction="vertical"></el-divider>
                    <el-tag type="danger">红色为达成或增长</el-tag>
                    <el-tag>蓝色为未达成或减少</el-tag>

                </div>
                <el-col :span="screenWidth<1920?6:4" v-for="(item,index) in showList" :key="index" v-if="item.status==1">
                    <div class="bg-purple" v-if="!getParamsDataCharLoading">
                        <el-card shadow="hover">

                            <el-badge :value="(((item.params-item.takeParams)/item.takeParams)*100).toFixed(2)+'%'" class="item" :type="((item.params-item.takeParams)/item.takeParams)>0?'danger':'primary'">
                            <div :class="Number(item.params)>=Number(item.hopeParam)?'cardParams0':'cardParams'"  @click="changeChar({index:index})">
                                {{item.params==null?'未计算':item.params}}
                                <span class="hopeParam" v-if="item.hopeParam!=0">/ {{item.hopeParam}}</span>
                            </div>
                            </el-badge>

                            <el-popover
                                    placement="top-start"
                                    :title="item.name"
                                    width="200"
                                    trigger="hover"
                                   >
                                <div style="color: #9e9e9e;">
                                    <div>说明：{{item.detail}}</div>
                                    <div>更新：{{item.isDay==1?'每日更新':'手动更新'}}</div>
                                    <div>排序：{{item.orderNum}}</div>
                                    <div>昨日数据：{{item.takeParams}}</div>
                                    <div style="font-size: 12px">点击可编辑</div>
                                </div>
                                <div class="myNote"  slot="reference" @click="showBtn?toEdit(item.id):null" >{{item.name}}</div>
                            </el-popover>

                        </el-card>
                    </div>
                </el-col>

            </el-row>


        </div>
        <div class="container" style="margin-top: 10px"  v-loading="getParamsDataCharLoading" id="char" v-if="!getParamsDataCharLoading">
            <div style="padding-bottom: 10px">
                <span class="headTitle">数据分析</span>
                <el-divider direction="vertical"></el-divider>
                <el-radio-group v-model="charDom.passDay" @change="getParamsDataChar">
                    <el-radio-button :label="7">七天</el-radio-button>
                    <el-radio-button :label="15">十五天</el-radio-button>
                    <el-radio-button :label="30">三十天</el-radio-button>
                </el-radio-group>
                <el-divider direction="vertical"></el-divider>
                <el-input-number v-model="charDom.passDay" @change="getParamsDataChar" :min="1" :max="1000" label="描述文字"></el-input-number>
                <el-divider direction="vertical"></el-divider>
                <span>总数：<span style="color: #2d8cf0"><b>{{allnum}}</b></span></span>
                <el-divider direction="vertical"></el-divider>
                <span>日均：<span style="color: #2d8cf0"><b> {{(allnum/charDom.passDay).toFixed(2)}}</b></span></span>
                <el-divider direction="vertical"></el-divider>
                <span style="border:1px solid #ddd;padding: 5px">
                    除今日：
                <span>总数：<span style="color: #2d8cf0"><b>{{allnum-charList[(charList.length-1)].num}}</b></span></span>
                <el-divider direction="vertical"></el-divider>
                <span>日均：<span style="color: #2d8cf0"><b>  {{((allnum-charList[(charList.length-1)].num)/(charDom.passDay-1)).toFixed(2)}}</b></span></span>
                </span>
                <el-divider direction="vertical"></el-divider>
                <el-button type="warning" plain @click="setParameterData(charDom.infoId)" icon="el-icon-refresh" v-if="showBtn">更新下列选中380天数据</el-button>

            </div>
            <el-tabs v-model="activeParamName" style="padding-top: 10px" @tab-click="changeChar">
                <el-tab-pane :label="item.name" :name="item.name"  v-for="(item,index) in showList" :key="index" ></el-tab-pane>
            </el-tabs>

            <g2column :setCharData="charList" :heightSize="300"  v-show="!getParamsDataCharLoading" :showLine="false"></g2column>
        </div>
    </div>

</template>

<script>
    import g2column from "../agent/char/g2column";
    export default {
        components: {
            "g2column":g2column,
        },
        name: "parameterPage",
        watch: {
            screenWidth (val) {
                if (!this.timer) {
                    this.screenWidth = val
                }
            }
        },
        data(){
            return{
                showBtn:localStorage.getItem("roleId")==1?true:false,
                passDay:0,
                screenWidth: document.body.clientWidth,
                allnum:0,
                charDom:{
                    infoId:null,
                    passDay:7,
                },
                activeParamName:null,
                showList:[],
                charList:[

                ],
                setAllParameterDataLoading:false,
                getParamsDataCharLoading:false,
            }
        },
        mounted(){
            window.onresize = () => {
                return (() => {
                    this.screenWidth = document.body.clientWidth
                })()
            }
        },
        created(){
            this.parameterInfoDtoShow()
        },
        methods:{
            changeChar(ar){
                this.activeParamName=this.showList[ar.index].name
                this.charDom.infoId=this.showList[ar.index].id
                this.getParamsDataChar()
                location.href="#char"
            },
            toAdd(){
                this.$router.push({path:'/parameterEdit'})
            },
            toEdit(id){
                this.$router.push({path:'/parameterEdit',query:{"id":id }})
            },
            parameterInfoDtoShow(){
                this.$getData("parameterInfoDtoShow", {passDay:this.passDay}).then(res => {
                    if (res.status == 200) {
                        this.showList=res.data
                        this.activeParamName=this.showList[0].name
                        this.charDom.infoId=this.showList[0].id
                        this.getParamsDataChar()
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            setAllParameterData(){
                this.setAllParameterDataLoading=true
                this.$getData("setAllParameterData", {passDay:this.passDay}).then(res => {
                    if (res.status == 200) {
                        this.$message.success("数据更新成功，" + res.msg);
                        this.parameterInfoDtoShow();
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                    this.setAllParameterDataLoading=false
                })
            },
            setParameterData(id){
                this.$getData("setParameterData", {id:id}).then(res => {
                    if (res.status == 200) {
                        this.$message.success("数据更新中，因数据量较大。预计时间十分钟，请稍后更新查看" );
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getParamsDataChar(){
                this.getParamsDataCharLoading=true

                this.$postData("getParamsDataChar",this.charDom,null).then(res => {
                    if (res.status == 200) {
                        this.charList=res.data
                        this.allnum=0
                        if (this.charList.length>0){
                            this.charList.forEach(v=>{
                                v.num=Number(v.num)
                                this.allnum +=Number(v.num)
                            })

                        }

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                    this.getParamsDataCharLoading=false

                })
            },
        }
    }
</script>
<style>
    .el-card__header{
        color: white;
        background: #2d8cf0;
    }
     {
        background: #fff;
        color: #2d8cf0;;
    }
</style>
<style scoped>
    .hopeParam{
        color: #6f7180;
        font-size: 12px;
    }
    .headTitle{
        font-size: 24px;
    }
    .myNote{
        display:-webkit-box;
        text-overflow:ellipsis;
        overflow:hidden;
        -webkit-line-clamp: 1;
        -webkit-box-orient:vertical;
    }
    .bg-purple{
        text-align: center;
    }
    .cardParams{
        font-size: 24px;
        color: #2d8cf0;
    }

    .cardParams0{
        font-size: 24px;
        color: #fa3d32;
    }

</style>
