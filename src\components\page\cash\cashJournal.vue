<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">

                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="会员id">
                                <el-input
                                        v-model="cash.memberId"
                                        placeholder="会员id"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="交易单号">
                                <el-input
                                        v-model="cash.tradingCode"
                                        placeholder="交易单号"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>


                        <el-col :span="8" style="text-align:right">
                            <el-form-item>
                                <el-button type="primary" style="width:20%"  @click="query()">搜索</el-button>
                                <el-button type="success" style="width:20%"  @click="exportList">导出Excel</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>


                     <!--   <el-col :span="8">
                            <el-form-item label="交易类型">
                                <Select v-model="cash.orderType" filterable style="width: 200px">
                                    <Option v-for="item in typeList" :value="item.value" :key="item.value">{{ item.label}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>-->

                        <el-col :span="8">
                            <el-form-item label="交易时间">
                                <el-date-picker
                                        v-model="cash.createTime"
                                        type="daterange"
                                        align="right"
                                        unlink-panels
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        format="yyyy 年 MM 月 dd 日"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div style="text-align:right">{{total}}</div>
            <el-table :data="list" border class="table"
                      @sort-change="sortChange"
                      ref="multipleTable">
                <el-table-column
                        prop="member.id"
                        label="会员id"
                        width="330"
                ></el-table-column>
                <el-table-column
                        prop="member.account"
                        label="会员账户"
                        width="250"
                ></el-table-column>
                <el-table-column
                        prop="orderPay.transactionTypeDesc"
                        label="交易类型"
                        width="120">
                    <template slot-scope="scope">
                        <span>充值</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="amount"
                        label="交易金额"
                        width="125"
                        sortable="custom"
                ></el-table-column>
                <el-table-column
                        prop="tradingCode"
                        label="交易单号"
                        width="250"
                ></el-table-column>
                <el-table-column
                        prop="createTime"
                        label="交易时间"
                        width="200"
                        sortable="custom"
                ></el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>
    </div>
</template>

<script>
    import tryProductAdd from '@/components/page/try/choose/tryProductAdd.vue'
    import tryProductUpdate from '@/components/page/try/choose/tryProductUpdate.vue'
    import {formatDate} from '@/components/common/utils.js'
    export default {
        data() {
            return {
                pageSizeOpts:[10,20,30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                cash:{
                    memberAccount:null,
                    memberId:null,
                    orderType:null,
                    tradingCode:null,
                    pageSize:10,
                    pageNum:1,
                    createTime:null,
                    createTime3:"2019-05-20 00:00:00",
                    cashBy:"createTime DESC",

                },
                total:"",
                typeList: [
                    {
                        value: '',
                        label: '请选择'
                    },
                    {
                        value: '0',
                        label: '消费'
                    },
                    {
                        value: '1',
                        label: '退款'
                    },
                    {
                        value: '2',
                        label: '扣款'
                    },
                ],
                list:null
            };
        },
        components: {
        },
        created() {
            this.getData();
            this.getTotal();
        },
        computed: {
        },
        methods: {
            getData() {
                this.$postData("payment_getList", this.cash, {}).then(res => {
                    if (res.status == 200) {

                        this.list = res.data.list;
                        for(let i=0;i<this.list.length;i++){
                            let account=this.list[i].member.account;
                            this.list[i].member.account= account.substring(0,3) + '****' + account.substring(account.length-4);
                        }
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getTotal(){
                this.$postData("payment_getTotal",this.cash, {},).then(res => {

                    if (res.status == 200) {
                        this.total=res.data
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            query() {
                if(this.cash.createTime){
                    this.cash.createTime3=null
                }
                this.getData();
                this.getTotal();
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.cash.pageSize = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                console.log(index)
                this.cash.pageNum = index;
                this.getData();
            },
            exportList() {
                this.$postData("payment_export", this.cash, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tablename: "充值统计",
                        res: res
                    });
                });
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
            sortChange: function (column, prop, order) {
                console.log(column)
                if (column == null) {
                } else {
                    if (column.order == "ascending") {
                        this.cash.cashBy = column.prop + " ASC";
                    }
                    if (column.order == "descending") {
                        this.cash.cashBy = column.prop + " DESC";
                    }
                }
                this.getData();
            }

        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
