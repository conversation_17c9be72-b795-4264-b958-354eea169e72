<template>
  <el-tabs type="border-card">
    <el-tab-pane label="开发员工">
    <develop-employee></develop-employee>
    </el-tab-pane>
    <el-tab-pane label="复购员工">
    <repurchase-employee></repurchase-employee>
    </el-tab-pane>
    <el-tab-pane label="规范员工">
  <standard-employee></standard-employee>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import repurchaseEmployee from "@/components/page/repurchaseEmployee";
import  standardEmployee from "@/components/page/standardEmployee";
import developEmployee from "@/components/page/developEmployee";
import DevelopEmployee from "@/components/page/developEmployee";
import RepurchaseEmployee from "@/components/page/repurchaseEmployee";
import StandardEmployee from "@/components/page/standardEmployee";
export default {
  name: "repurchaseStatistics",
  components: {StandardEmployee, RepurchaseEmployee, DevelopEmployee}
}
</script>

<style scoped>

</style>
