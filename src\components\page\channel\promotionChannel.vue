<template>
  <div class="table">
    <el-tabs type="border-card">
      <el-tab-pane label="促销渠道统计">
        <div class="container">
          <div class="handle-box">
            <el-form ref="form">
              <el-row>
                <el-col :span="4">
                  <el-form-item label="门店">
                    <el-select filterable style="width:190px" v-model="dom.storeId" clearable>
                      <el-option v-for="(item,index) in storeList" :value="item.id" :key="index"
                                 :label="item.storeName"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="业务渠道">
                    <el-select v-model="dom.buChannel" placeholder="请选择" clearable >
                      <el-option value="0" label="电话"></el-option>
                      <el-option value="55" label="指定订单"></el-option>
                      <el-option value="56" label="抖音小程序"></el-option>
                      <el-option value="1" label="团购"></el-option>
                      <el-option value="2" label="短信"></el-option>
                      <el-option value="3" label="车身广告"></el-option>
                      <el-option value="4" label="反馈"></el-option>
                      <el-option value="5" label="老客户介绍"></el-option>
                      <el-option value="6" label="转接"></el-option>
                      <el-option value="7" label="电梯广告"></el-option>
                      <el-option value="8" label="淘宝"></el-option>
                      <el-option value="9" label="开发"></el-option>
                      <el-option value="10" label="APP"></el-option>
                      <el-option value="11" label="微信"></el-option>
                      <el-option value="12" label="糯米"></el-option>
                      <el-option value="13" label="大众"></el-option>
                      <el-option value="14" label="美团"></el-option>
                      <el-option value="15" label="联系开发"></el-option>
                      <el-option value="16" label="估价单"></el-option>
                      <el-option value="17" label="驻场"></el-option>
                      <el-option value="18" label="公司保洁"></el-option>
                      <el-option value="19" label="大客户"></el-option>
                      <el-option value="20" label="地推"></el-option>
                      <el-option value="21" label="门店"></el-option>
                      <el-option value="22" label="天猫"></el-option>
                      <el-option value="23" label="物业"></el-option>
                      <el-option value="24" label="异业合作"></el-option>
                      <el-option value="25" label="转接114"></el-option>
                      <el-option value="26" label="家好管家"></el-option>
                      <el-option value="27" label="其他"></el-option>
                      <el-option value="28" label="会员卡"></el-option>
                      <el-option value="29" label="年卡"></el-option>
                      <el-option value="30" label="月卡"></el-option>
                      <el-option value="31" label="家好年卡"></el-option>
                      <el-option value="32" label="家好月卡"></el-option>
                      <el-option value="33" label="安卓"></el-option>
                      <el-option value="34" label="IOS"></el-option>
                      <el-option value="35" label="淘宝到家"></el-option>
                      <el-option value="36" label="拼团"></el-option>
                      <el-option value="37" label="小程序"></el-option>
                      <el-option value="38" label="扫码下单"></el-option>
                      <el-option value="39" label="预售"></el-option>
                      <el-option value="40" label="表单"></el-option>
                      <el-option value="41" label="满减"></el-option>
                      <el-option value="42" label="水滴智店"></el-option>
                      <el-option value="43" label="好车网"></el-option>
                      <el-option value="44" label="建发臻选"></el-option>
                      <el-option value="45" label="H5"></el-option>
                      <el-option value="46" label="鸿蒙生态"></el-option>
                      <el-option value="47" label="抖音团购"></el-option>
                      <el-option value="48" label="抖音代金券"></el-option>
                      <el-option value="49" label="快手"></el-option>
                      <el-option value="50" label="小红书"></el-option>

                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="订单来源">
                    <el-select v-model="dom.source" placeholder="请选择" clearable >
                      <el-option value="1" label="汽车广播"></el-option>
                      <el-option value="2" label="公交站牌"></el-option>
                      <el-option value="3" label="微信公众号"></el-option>
                      <el-option value="4" label="微信朋友圈"></el-option>
                      <el-option value="5" label="小羽佳服务员工介绍"></el-option>
                      <el-option value="6" label="好朋友介绍"></el-option>
                      <el-option value="7" label="搬家车厢广告"></el-option>
                      <el-option value="8" label="百度搜索"></el-option>
                      <el-option value="9" label="官网"></el-option>
                      <el-option value="10" label="抖音"></el-option>
                      <el-option value="11" label="侠侣"></el-option>
                      <el-option value="12" label="京东安维"></el-option>
                      <el-option value="13" label="淘宝"></el-option>
                      <el-option value="14" label="美团"></el-option>
                      <el-option value="15" label="饿了么"></el-option>
                      <el-option value="16" label="其它"></el-option>
                      <el-option value="17" label="电话"></el-option>
                      <el-option value="18" label="微小号"></el-option>
                      <el-option value="19" label="短信"></el-option>
                      <el-option value="20" label="百度"></el-option>
                      <el-option value="21" label="城市电视"></el-option>
                      <el-option value="22" label="兴享惠"></el-option>
                      <el-option value="23" label="建行悦理财"></el-option>
                      <el-option value="24" label="住店圈"></el-option>
                      <el-option value="25" label="旅划算"></el-option>
                      <el-option value="26" label="欢乐订"></el-option>
                      <el-option value="27" label="天虹"></el-option>
                      <el-option value="28" label="好车网"></el-option>
                      <el-option value="29" label="魔法阿嬷"></el-option>
                      <el-option value="30" label="水滴智店"></el-option>
                      <el-option value="31" label="分众框架广告"></el-option>
                      <el-option value="32" label="门媒广告"></el-option>
                      <el-option value="33" label="路名牌广告"></el-option>
                      <el-option value="34" label="公交车视频广告"></el-option>
                      <el-option value="35" label="口碑"></el-option>
                      <el-option value="36" label="鲸牌"></el-option>
                      <el-option value="37" label="建发臻选"></el-option>
                      <el-option value="38" label="视频号"></el-option>
                      <el-option value="39" label="鸿蒙生态"></el-option>
                      <el-option value="40" label="快手"></el-option>
                      <el-option value="69" label="思明停车"></el-option>
                      <el-option value="4001" label="居家保洁A3"></el-option>
                      <el-option value="4002" label="三嫂领券"></el-option>
                      <el-option value="4003" label="宠家保洁"></el-option>
                      <el-option value="4004" label="海贝AI室内设计"></el-option>
                      <el-option value="4005" label="招商加盟"></el-option>
                      <el-option value="4006" label="私域三次卡"></el-option>
                      <el-option value="4007" label="天眼查"></el-option>
                      <el-option value="4008" label="小程序在线咨询"></el-option>

                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="结算日期">
                    <el-date-picker
                        v-model="value"
                        type="daterange"
                        :picker-options="pickerOptions"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        align="right">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item>
                    <el-button type="success" @click="query()" icon="el-icon-search">搜索</el-button>
                    <el-button type="info" @click="getDown()" icon="el-icon-download">一键下载</el-button>
                  </el-form-item>
                </el-col>
              </el-row>

            </el-form>
          </div>
          <el-table :data="list" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable"
                    v-loading="loading">
            <el-table-column
                prop="buChannelName"
                fixed="left"
                label="业务渠道"
                width="80">
            </el-table-column>
            <el-table-column
                prop="sourceName"
                fixed="left"
                label="订单渠道"
                width="100">
            </el-table-column>
            <el-table-column
                prop="orderNumber"
                label="订单数"
                width="80"
            ></el-table-column>
            <el-table-column
                prop="billNo"
                label="订单号"
                width="170"
            ></el-table-column>
            <el-table-column
                prop="productName"
                label="服务项目"
                width="120"
            ></el-table-column>
            <el-table-column
                prop="orderAmount"
                label="订单金额"
                width="80"
            ></el-table-column>
            <el-table-column
                prop="preferentialAmount"
                label="系统优惠金额"
                width="110"
            ></el-table-column>
            <el-table-column
                prop="remark"
                label="备注"
            ></el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="优惠券渠道统计">
       <couponChannel></couponChannel>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import couponChannel from "./couponChannel";
export default {

  // 注册组件
  components: {
    couponChannel,
  },
  name: "promotionChannel",
  data() {
    return {
      value: [],
      storeList:[],
      dom: {
        startTime: null,
        endTime: null,
        buChannel: null,
        source: null,
        size: 10, // 每页显示条数
        current: 1, // 当前页
      },
      loading: false,
      list: [],
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 89);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },
  created() {
    this.getData();
    this.getStore();
  },
  methods: {
    getStore() {
      this.$postData("store_getByList", {}, {}).then(res => {
        if (res.status == 200) {
          this.storeList = res.data;
        }
      });
    },
    setTime() {
      this.dom.startTime = null
      this.dom.endTime = null
      if (this.value.length === 0) {
        this.dom.startTime = null
        this.dom.endTime = null
      } else {
        this.dom.startTime = this.value[0]
        this.dom.endTime = this.value[1]+" 23:59:59"
      }
    },
    getData() {
      this.setTime( )
      this.loading = true
      this.$postData("promotionChannelStatistics", this.dom, {}).then(res => {
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
        this.loading = false
      })
      // this.$postData("dataCouponDto", this.model, {}).then(res => {
      //   if (res.status == 200) {
      //
      //     this.all=res.data;
      //   } else {
      //     this.$message.error("查询失败，" + res.msg);
      //   }
      //   this.loading=false
      // })
    },
    // 页码大小
    onPageSizeChange(size) {
      this.dom.size = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.dom.current = index;
      this.getData();
    },
    query() {
      this.getData();
    },
    getDown() {
      this.setTime()
      if (this.value.length === 0) {
        return this.$message.error("数据量过大。请选择结算时间区间再下载。")
      }
      this.loading = true;
      this.$postData("exportPromotionChannelStatistics", this.dom, {
        responseType: "arraybuffer"
      }).then(res => {
        this.loading = false;
        this.blobExport({
          tablename: "促销渠道信息",
          res: res
        });
      })
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
  },
}
</script>

<style scoped>
.table {
  width: 100%;
  font-size: 14px;
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}

.del-dialog-cnt {
  font-size: 16px;
  text-align: center;
}

.red {
  font-size: 15px;
  font-weight: bold;
  color: #ff0000;
}

.mr10 {
  margin-right: 10px;
}
</style>
