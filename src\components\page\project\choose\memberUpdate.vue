<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col span="24">
                    <div>
                        <div id="operBtnDiv">
                            <div id="searchDiv">
                                <Form ref="formItem" :model="formItem" :label-width="80" label-position="left"
                                      :rules="ruleValidate">

                                    <FormItem label="角色" prop="role" style="width: 80%">
                                        <Input placeholder="请输入角色" v-model="formItem.role">
                                        </Input>
                                    </FormItem>
                                    <FormItem label="项目资金" prop="capital" style="width: 80%">
                                        <Input placeholder="请输入项目资金" v-model="formItem.capital"  @on-change="getProportion">
                                        </Input>
                                    </FormItem>

                                    <FormItem label="分润比例" prop="proportion" style="width: 80%">
                                        <Input placeholder="请输入分润比例" v-model="formItem.proportion">
                                        </Input>
                                    </FormItem>

                                    <FormItem>
                                        <Button type="primary" @click="saveProjectMember('formItem')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </FormItem>


                                </Form>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
       <!-- <Modal v-model="usersModal" class="Modal" :width="screenWidth" title="成员">
            <div class="addBody">

                <employees-choose v-if="usersModal" @init-choose="initChooseProject"
                                  @close-modal="closeCurrModal"></employees-choose>
            </div>
            <div slot="footer">
                <el-button @click="closeCurrModal">取消</el-button>
                <el-button type="primary" :loading="userLoading" @click="closeCurrModal">确定</el-button>
            </div>
        </Modal>-->
    </div>
</template>


<script>
    import employeesChoose from '@/components/page/project/choose/employeesChoose.vue'


    export default {
        props: ['member'],
        data() {
            return {
                employees:null,
                usersModal: false,
                userLoading: false,
                screenWidth: '30%',
                cityList: [],
                amount:this.member.capital,
                formItem: {
                    id:this.member.memberId,
                    role: null,
                    capital:null,
                    proportion: null,
                },
                ruleValidate: {
                    capital: [
                        {required: true, message: '请输入项目资金', trigger: 'blur'},
                    ],
                    role: [
                        {required: true, message: '请输入角色名称', trigger: 'blur'}
                    ],
                    proportion: [
                        {required: true, message: '请输入分润比例', trigger: 'blur'},
                    ]
                }
            }
        },
        components: {
            'employeesChoose': employeesChoose
        },
        created: function () {
            this.getData();
        },
        methods: {
            getData() {
                this.$getData("projectMember_getById",  {id:this.member.memberId}).then(res => {
                    if (res.status == 200) {

                        this.formItem=res.data
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getProportion() {
                if(this.formItem.capital){
                    let tempVal = parseFloat(this.formItem.capital/this.amount).toFixed(3)
                    console.log(tempVal)
                    this.formItem.proportion= tempVal*100+"%"
                }
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.usersModal = false;
            },
            saveProjectMember(name) {
                this.formItem.capital=this.formItem.capital+"";
                this.$refs[name].validate((valid) => {
                    if (valid) {
                            this.$postData("projectMember_update", this.formItem, {}).then(res => {
                                if (res.status == 200) {
                                    this.$Message.success('修改成功');
                                    this.chooseThisModel();
                                } else {
                                    this.$message.error("修改失败，" + res.msg);
                                }
                            })

                    }
                })
            },
            /*
            * 关闭当前窗口
            * */
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            },
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

