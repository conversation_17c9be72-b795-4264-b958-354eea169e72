<template>
    <div class="table">

        <div class="container">
            <el-form :model="dom" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <el-row>
                    <h2>{{storeName}}</h2><span style="color: #C9C9C9">注意：如果经纬度不正确。请提报正确经纬度。</span>
                    <el-divider></el-divider>
                    <el-col :span="8">
                        <el-form-item label="经度" prop="lng">
                            <el-input v-model="dom.lng"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="纬度" prop="lat">
                            <el-input v-model="dom.lat"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="日期" prop="passDay">
                            <el-input v-model="dom.passDay" type="number"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="日期" prop="passDay">
                            <el-radio-group v-model="dom.passDay">
                                <el-radio-button :label="-1">明天</el-radio-button>
                                <el-radio-button :label="0">今天</el-radio-button>
                                <el-radio-button :label="1">昨天</el-radio-button>
                                <el-radio-button :label="2">两天前</el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item>
                            <el-button type="primary" @click="submitForm('ruleForm')">查询</el-button>
                            <el-divider direction="vertical"></el-divider>
                            <a href="https://lbs.amap.com/tools/picker" target="_blank">查询经纬度</a>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <div class="block">
                            <el-form-item label="离店距离">
                                <el-slider
                                        :max="3000"
                                        v-model="value"
                                        range
                                        :marks="marks">
                                </el-slider>
                            </el-form-item>
                        </div>
                    </el-col>
                </el-row>


            </el-form>


        </div>
        <div v-loading="loading">
            <!--            <el-divider>共 {{showList.length}} 条</el-divider>-->
            <div v-for="(item,index) in showList" :key="index" style="margin: 20px;line-height: 24px"
                 v-if="item.gap>value[0] & item.gap<value[1]">
                <el-card shadow="hover">
                    <div>
                        <el-tag>{{item.ProductName}}</el-tag>
                        <el-divider direction="vertical"></el-divider>
                        <el-tag type="danger">￥{{item.TotalAmount}}</el-tag>
                        <el-divider direction="vertical"></el-divider>
                        <el-link type="primary" @click="toMember(item.MemberId)">
                            <el-tag type="warning" effect="dark" v-if="item.lblId==='20'"> 勿扰标记</el-tag>
                            <el-tag type="danger" effect="dark" v-if="item.level==='6'">VIP客户</el-tag>
                            <i class="el-icon-user"></i>{{item.Contact}}
                        </el-link>
                        <el-divider direction="vertical"></el-divider>
                        <i class="el-icon-phone-outline"></i>{{item.Tel}}
                        <el-divider direction="vertical"></el-divider>
                        <i class="el-icon-position"></i> {{item.gap.toFixed(2)}}米
                        <el-divider direction="vertical"></el-divider>
                        <i class="el-icon-receiving"></i><b>{{item.BillNo}}</b>

                    </div>


                    <a href="javascript:void(0);" @click="clickModifyRqName(item)"><i
                            class="el-icon-location-information"></i>{{item.RQuartersName}}</a>
                    <el-divider direction="vertical"></el-divider>
                    <i class="el-icon-map-location"></i>{{item.Street}}

                    <el-divider direction="vertical"></el-divider>
                    <i class="el-icon-date"></i> {{item.StartTime.substr(0,10)}} - {{item.SkuFixedTime}}
                    <el-divider direction="vertical"></el-divider>
                    <i class="el-icon-price-tag"></i>{{item.Remark}}
                    <br>
                    <i class="el-icon-date"></i>订单创建时间： {{item.CreateTime}}
                    <el-divider direction="vertical"></el-divider>
                    {{Number(item.num)>0?'客户回访次数'+item.num+'次':''}}

                    <!--如果 isShowDistribution > 0 则代表该订单被分配过 isRejectGoHome 客户是否拒绝上户 -->
                    <el-button
                            v-if="(item.isShowDistribution == null || item.isShowDistribution == 0) && (item.isRejectGoHome == null ||item.isRejectGoHome == 0)"
                            type="primary" style="float: right"
                            @click="dialogVisible=true,changeDom=item"
                    >
                        上户分配
                    </el-button>
                </el-card>

            </div>
        </div>
        <el-dialog
                title="请选择分配给哪个经纪人："
                :visible.sync="dialogVisible"
                width="30%">
            <!--            <div>-->
            <!--                &lt;!&ndash;                {{changeDom}}&ndash;&gt;-->
            <!--                客户号码：{{changeDom}}<br>-->

            <!--            </div>-->
            <el-tabs v-model="activeName" type="border-card">
                <el-tab-pane label="本门店" name="first">
                    <div v-for="item in agentList" class="agent-box" v-if="item.storeId==storeId">
                        {{item.no}}--{{item.realName}}
                        <el-button size="mini" type="primary" @click="toShTask(item.id)">
                            上户分配
                        </el-button>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="全部" name="second">
                    <el-input v-model="realName" placeholder="请输入名称或者工号" @input="querySearch"
                              suffix-icon="el-icon-search"></el-input>
                    <div v-for="item in options" class="agent-box">
                        {{item.no}}--{{item.realName}}
                        <el-button size="mini" type="primary" @click="toShTask(item.id)">上户分配</el-button>
                    </div>
                </el-tab-pane>

            </el-tabs>

        </el-dialog>
        <el-dialog
                title="修改小区位置"
                :visible.sync="showModifyDialog"
                :destroy-on-close="true"
                width="75%">
            <div class="el-dialog-sty">
                <el-row>
                    <el-col :span="8">
                        地址：
                        <el-select
                                style="width: 350px"
                                v-model="localVal"
                                filterable
                                remote
                                clearable
                                reserve-keyword
                                placeholder="请输入关键词"
                                :remote-method="remoteMethod"
                                @change="clickAddr"
                                :loading="loadingStore">
                            <el-option
                                    v-for="(item, index) in localOptions"
                                    :key="index"
                                    :label="item.label"
                                    :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="1"> &nbsp;</el-col>
                    <el-col :span="15">
                        地图位置：
                        <addr-map v-if="showMap" ref="mapRef" :width="'45vw'" :height="'37vh'" :lng="localInfo.lng"
                                  :lat="localInfo.lat" :zoom="z"></addr-map>
                    </el-col>
                </el-row>
                <el-row style="margin: 10px 0;">
                    <el-col :span="11"> &nbsp;</el-col>
                    <el-col :span="11"> &nbsp;</el-col>
                    <el-col :span="2">
                        <el-button type="primary" @click="modifyRqName" :disabled="disModifyDisBtn">确认修改</el-button>
                    </el-col>
                </el-row>
            </div>
        </el-dialog>

    </div>
</template>

<script>
    import addrMap from "../agent/char/addrMap";

    export default {
        name: "orderByLngAndLat",
        components: {
            "addrMap": addrMap
        },
        data() {
            return {
                disModifyDisBtn: false,
                z: 16,
                loadingStore: false,
                showMap: true,
                localOptions: [
                    {
                        name: null,
                        value: null
                    }
                ],
                localVal: null,
                localInfo: {
                    lng: 118.102327,
                    lat: 24.489777
                },
                // memberInfo: {
                //     rqName: null,
                //     lng: null,
                //     lat: null
                // },
                memberInfo: {
                    id: null,
                    orderAddrId: null,
                    rqName: null,
                    address: null,
                    lng: null,
                    lat: null,
                    employeeId: localStorage.getItem('id')
                },
                showModifyDialog: false,
                disToShTaskBtn: false,
                realName: null,
                marks: {
                    0: '门店地址',
                    100: '100米',
                    500: '500米',
                    1500: {
                        style: {
                            color: '#1989FA'
                        },
                        label: this.$createElement('strong', '1500米')
                    }
                },
                value: [0, 3000],
                storeName: '自定义模式',
                changeDom: null,
                storeId: localStorage.getItem("storeId"),
                agentList: [],
                options: [],
                activeName: 'first',
                dialogVisible: false,
                loading: false,
                dom: {
                    lng: '118.139048',
                    lat: '24.495575',
                    passDay: 0,
                    storeId: localStorage.getItem("storeId")
                },
                showList: [],
                rules: {
                    lng: [
                        {required: true, message: '请输入', trigger: 'blur'},
                    ],
                    lat: [
                        {required: true, message: '请输入', trigger: 'blur'},
                    ],
                    passDay: [
                        {required: true, message: '请输入', trigger: 'blur'},
                    ],
                }
            }
        },
        created() {
            this.getAgent();
            this.selectLngLatByPrimaryKey();
        },
        methods: {
            clickModifyRqName(item) {
                // console.info(item);
                this.memberInfo.orderAddrId = item.orderAddrId;
                this.memberInfo.id = item.mId;
                this.showModifyDialog =true;
            },
            /**
             * 修改小区名字（同时修改会员地址中的定位）
             */
            modifyRqName() {
                this.disModifyDisBtn = true;
                // console.info(this.memberInfo);
                if (this.memberInfo.id == null || this.memberInfo.id === "" || this.memberInfo.orderAddrId == null || this.memberInfo.orderAddrId === "") {
                    this.disModifyDisBtn = false;
                    return this.$message.error('无法获取会员或订单地址信息！');
                }
                if (this.memberInfo.rqName == null || this.memberInfo.rqName === "") {
                    this.disModifyDisBtn = false;
                    return this.$message.error('请填写并在选择项中选择要修改的小区！');
                }
                if (this.memberInfo.lng == null || this.memberInfo.lng === "" || this.memberInfo.lat == null || this.memberInfo.lat === "") {
                    // this.$toast.fail('请填写并在选择项中选择要修改的小区地址！');
                    this.disModifyDisBtn = false;
                    return this.$message.error('经纬度获取失败！');
                }
                if (this.memberInfo.employeeId == null || this.memberInfo.employeeId === "") {
                    this.disModifyDisBtn = false;
                    return this.$message.error('请重新登录！');
                }
                this.$postData("updateMemberInfoAndOrderAddrInfo", this.memberInfo).then(res => {
                    if (res.status === 200) {
                        this.disModifyDisBtn = false;
                        this.$message.success("修改成功！");
                    } else {
                        this.$message.error(res.msg);
                        this.disModifyDisBtn = false;
                    }
                })
            },
            clickAddr(val) {
                console.info(this.localVal);
                if (this.localVal == null) {
                    return this.$message.error("请选择下拉框中的值！");
                }
                if (this.localVal.indexOf(";") === -1) {
                    return this.$message.error("请填写更详细的地址，再从下拉框中选择！");
                }
                let splitVal = this.localVal.split(";");
                if (splitVal.length !== 2) {
                    return this.$message.error("获取数据失败，请填写更详细的地址，再从下拉框中选择！");
                }
                let splitLngAndLat = splitVal[0].split(",");
                this.memberInfo.rqName = splitVal[1];
                if (splitLngAndLat.length !== 2) {
                    return this.$message.error("获取经纬度失败，请填写更详细的地址，再从下拉框中选择！");
                }
                const ln = Number(splitLngAndLat[0]);
                const la = Number(splitLngAndLat[1]);
                this.memberInfo.lng = ln;
                this.memberInfo.lat = la;
                this.localInfo.lng = ln;
                this.localInfo.lat = la;
                this.showMap = true;
                // this.$refs.mapRef.reloadMap(this.localInfo);
                // console.info(this.memberInfo);
                // this.memberInfo.lngLat = val.value;
            },
            remoteMethod(query) {
                this.loadingStore = true;
                this.showMap = false;
                const ref = this;
                if (query !== '') {
                    ref.localOptions = [];
                    $.ajax({
                        type: "get",//type可以为post也可以为get
                        url: "https://restapi.amap.com/v3/assistant/inputtips?key=474d516ebd223dc8c2a9e3c3ca12f568&keywords=" + query + "&type=&location=&city=&datatype=poi",
                        data: {},//这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                        dataType: "json",//这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                        success: function (data) {
                            ref.searchResult = [];
                            // console.log(data);
                            // address 强转为字符串，避免控制层报错
                            data.tips.forEach(function (item, index) {
                                // console.info('val', item);
                                const l = "【" + item.name + "】" + item.district + item.address;
                                ref.localOptions.push({
                                    label: l,
                                    value: item.location + ";" + item.name + ""
                                })
                            })
                        },
                        error: function (data) {
                            alert("出现了错误！");
                        }
                    })
                } else {
                    this.localOptions = [];
                }
                this.loadingStore = false;
            },
            querySearch(queryString) {
                var restaurants = this.agentList;
                var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
                // 调用 callback 返回建议列表的数据
                // cb(results);
                this.options = results;
            },
            createFilter(queryString) {
                return (restaurant) => {
                    return (restaurant.realName.toLowerCase().indexOf(queryString.toLowerCase()) === 0 || restaurant.no.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                };
            },
            toShTask(employeeId) {
                let task = {
                    employeeId: employeeId,
                    memberId: this.changeDom.MemberId,
                    orderId: this.changeDom.Id
                };
                this.disToShTaskBtn = true;
                // console.log(task)
                this.$postData("initAgent31", task, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.success('上户分配成功')
                    } else {
                        this.$message.error("上户分配失败，" + res.msg);
                    }
                    this.dialogVisible = false;
                    this.disToShTaskBtn = false;
                })

            },
            selectLngLatByPrimaryKey() {

                this.$getData("selectLngLatByPrimaryKey", {id: this.storeId},).then(res => {
                    if (res.status == 200) {
                        //this.agentList = res.data;
                        if (res.data.length > 0) {
                            this.dom.lng = res.data[0].lng;
                            this.dom.lat = res.data[0].lat;
                            this.storeName = res.data[0].StoreName;
                        }
                        this.getListBylngAndLat();
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getAgent() {
                // let store = Number(localStorage.getItem("storeId"));
                // this.$postData("agent_list", {storeId: null}, {}).then(res => {
                //     if (res.status == 200) {
                //         this.agentList = res.data;
                //         this.options = this.agentList;
                //     } else {
                //         this.$message.error("查询失败，" + res.msg);
                //     }
                // })
                this.$getData("getAgentAndShopownerAndCommManager", {}).then((res) => {
                    if (res.status === 200) {
                        this.agentList = res.data;
                        this.options = this.agentList;
                    } else {
                        this.$toast.fail("查询失败" + res.msg);
                    }
                });
            },
            toMember(id) {
                // console.log(id);
                let routeData = this.$router.resolve({path: '/memberInfo', query: {"id": id}}); //path：跳转页面的相对路径，query：参数
                window.open(routeData.href, '_blank');
            },
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        this.getListBylngAndLat();
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            getListBylngAndLat() {
                this.loading = true;
                if (this.dom.storeId != null && this.dom.storeId === "1") {
                    this.dom.storeId = null;
                }
                this.$postData("getListBylngAndLat", this.dom, null).then(res => {
                    if (res.status == 200) {
                        this.showList = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                    this.loading = false
                })
            },
        }

    }
</script>

<style scoped>
    .agent-box {
        padding: 10px;
        border: 1px solid #ddd;
    }

    /deep/ .el-dialog .el-dialog-sty {
        height: 42vh;
    }
</style>
