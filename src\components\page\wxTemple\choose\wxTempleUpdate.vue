<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col span="24">
                    <div>
                        <div id="operBtnDiv">
                            <div id="searchDiv">
                                <Form ref="formItem" :model="formItem" :label-width="80" label-position="right"
                                       :rules="ruleValidate">
                                    <Row>
                                        <FormItem label="模版Id" prop="templeId">
                                            <Input placeholder="请输入模版Id" v-model="formItem.templeId" readonly="readonly">
                                            </Input>
                                        </FormItem>
                                    </Row>

                                    <Row>
                                        <Col :span="12">
                                            <FormItem label="标题" prop="title">
                                                <Input placeholder="请输入标题" v-model="formItem.title">
                                                </Input>
                                            </FormItem>
                                        </Col>
                                        <Col :span="12">
                                            <FormItem label="使用数量" prop="keyNum">
                                                <Input placeholder="请输入数量" v-model="formItem.keyNum">
                                                </Input>
                                            </FormItem>
                                        </Col>
                                    </Row>

                                    <Row>
                                        <Col :span="12">
                                            <FormItem label="头部" prop="firstData">
                                                <Input placeholder="请输入头部" v-model="formItem.firstData">
                                                </Input>
                                            </FormItem>
                                        </Col>
                                        <Col :span="12">
                                            <FormItem label="尾部" prop="remarkData">
                                                <Input placeholder="请输入尾部" v-model="formItem.remarkData">
                                                </Input>
                                            </FormItem>
                                        </Col>
                                    </Row>


                                    <div style="margin-left: 80px">
                                        <Button type="primary" @click="update('formItem')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </div>
                                </Form>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
    </div>
</template>


<script>

    export default {
        props: ['templeId'],
        data() {
            return {
                formItem: {
                    id: this.templeId,
                    templeId: null,
                    title: null,
                    firstData: null,
                    remarkData: null,
                    keyNum: null
                },
                ruleValidate: {
                    templeId: [
                        {required: true, message: '请输入模版Id', trigger: 'change'}
                    ],
                    title: [
                        {required: true, message: '请输入标题', trigger: 'change'}
                    ],
                    firstData: [
                        {required: true, message: '请输入头部', trigger: 'change'}
                    ],
                    remarkData: [
                        {required: true, message: '请输入尾部', trigger: 'change'}
                    ],
                    keyNum: [
                        {required: true, message: '请输入数量'},
                        {required: true, pattern: /(^[\-0-9][0-9]*(.[0-9]+)?)$/, message: '请输入数字', trigger: 'change'}
                    ],
                },
            }
        },
        components: {},
        created() {
            this.getData();
        },
        methods: {
            getData() {
                this.$getData("push_getById", {wxTempleManageId: this.templeId}).then(res => {
                    if (res.status == 200) {

                        this.formItem = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },

            update(name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        this.$postData("push_update", this.formItem, {}).then(res => {
                            if (res.status == 200) {
                                this.$Message.success('修改成功');
                                this.chooseThisModel()
                            } else {
                                this.$message.error("修改失败，" + res.msg);
                            }
                        })
                    }
                })
            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            },
            handleRemove(file, fileList) {
                console.log(file, fileList);
                this.formItem.icon = null
            },
            handlePictureCardPreview(file) {
                this.dialogImageUrl = file.url;
                this.dialogVisible = true;
                console.log(file);
            },
            onExcees() {
                this.$Message.success('只可上传一张图片');
            },
            onSuccess(res, file, fileList) {
               ;
                if (res.status == 200) {
                    this.$Message.success('上传成功');
                    this.formItem.icon = res.data
                }
            }
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

