<template>
    <div>
        <div v-for="(item,index) in responseList" :key="index">
            <div class="title">{{item.title}}</div>
            <div class="response"><span style="color: #bdbdbd">答：</span>{{item.response}}</div>
            <div style="float: right">{{item.creatTime}}</div>
            <el-divider></el-divider>
        </div>

    </div>

</template>

<script>
    export default {
        name: "responseInfo",
        props:['id'],
        data(){
            return{
                responseList:[],
                taskId:this.id
            }
        },
        created(){
            this.getResponseByTaskId(this.taskId)
        },
        methods:{
            getResponseByTaskId(id) {
                this.$postUrl("getResponseByTaskId",id,null,null).then(res => {
                    if (res.status == 200) {
                        this.responseList=res.data
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
        }
    }
</script>

<style scoped>
    .response{
        padding: 20px;
        font-size: 24px;
    }
    .title{
        color: #8e8e8e;
        font-size: 18px;
        padding-left: 10px;
        border-left: 5px solid #2d8cf0;
    }

</style>
