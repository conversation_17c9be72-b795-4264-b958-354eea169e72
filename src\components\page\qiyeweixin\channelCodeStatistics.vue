<template>
  <div style="padding: 0.5% 10%;">
    <div class="a">渠道码统计</div>
    <div style="height: 100%; background: #FFFFFF;padding:0 10%;">
      <div style="padding-top: 20px;">
        <el-row type="flex" justify="space-between">
          <el-col :span="15">
            <div class="b" style="color: rgb(0, 0, 0)">
              基本信息
            </div>
            <div class="b">
              <span style="color: rgb(144, 147, 153);padding-right:58px;">名称</span>
              <span style="color: rgb(0, 0, 0);">{{ dom.name }}</span>
            </div>
            <div class="b">
              <span style="color: rgb(144, 147, 153);padding-right:43px;">创建者</span>
              <span style="color: rgb(0, 0, 0);">{{ dom.createrName }}</span>
            </div>
            <div class="b">
              <span style="color: rgb(144, 147, 153);padding-right:57px;">渠道</span>
              <span style="color: rgb(0, 0, 0);">{{ dom.channel }}</span>
            </div>
            <div class="b">
              <span style="color: rgb(144, 147, 153);padding-right:25px;">创建时间</span>
              <span style="color: rgb(0, 0, 0);">{{ dom.createTime }}</span>
            </div>
            <el-divider></el-divider>
            <div style="font-size: 18px;color: rgb(0, 0, 0);padding-bottom:15px;">总览</div>
            <div style="border-radius: 4px; color: #909399; font-size: 15px;
            background: rgb(236, 238, 245);text-align: center; padding: 35px;">
              <el-row type="flex" justify="center">
                <el-col :span="12">
                  <div>扫码量</div>
                  <div class="grid-a">{{ dom.num }}</div>
                </el-col>
                <el-col :span="12">
                  <div>下单量</div>
                  <div class="grid-a">{{ drain }}</div>
                </el-col>
              </el-row>
            </div>
          </el-col>
          <el-col :span="8" style="text-align: center;">
            <el-image :src="dom.img"
                      style="width: 220px; height: 364px;box-shadow: 0 2px 4px 0 ;rgba(0, 0, 0, .04);border-radius: 4px"></el-image>
            <el-row type="flex" justify="center">
              <el-col :span="8">
                <el-button type="text" style="font-size: 18px" @click="downloadUrl(dom.img)">下载</el-button>
              </el-col>
              <el-col :span="2" style="font-size: 20px">
                <el-divider direction="vertical"></el-divider>
              </el-col>
              <el-col :span="8">
                <el-button type="text" style="font-size: 18px" class="tag-read" :data-clipboard-text="dom.img"
                           @click="copyUrl()">
                  复制
                </el-button>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <el-divider></el-divider>
      <div style="font-size: 18px;color: rgb(0, 0, 0);">
        日期
        <el-radio-group v-model="radio" style="padding: 0 30px ;" @change="statisticalDate()">
          <el-radio-button label="0">今日</el-radio-button>
          <el-radio-button label="1">7日</el-radio-button>
          <el-radio-button label="2">30日</el-radio-button>
        </el-radio-group>
        自定义
        <el-date-picker v-model="value" type="daterange" @change="custom()" value-format="yyyy/MM/dd"
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </div>
      <div style="padding:20px 0 ">
        <el-row>
          <el-col :span="6">
            <el-card :body-style="{ padding: '0px' }" shadow="hover">
              <div class="grid-b">
                <div>扫码量</div>
                <div class="grid-a">{{ addNum }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card :body-style="{ padding: '0px' }" shadow="hover">
              <div class="grid-c">
                <div>下单量</div>
                <div class="grid-a">{{ drainNum }}</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
//引入组件
import Clipboard from "clipboard";
import * as echarts from 'echarts';

export default {
  name: "channelCodeStatistics",
  data() {
    return {
      id: this.$route.query.id,
      radio: '0',
      value: [],
      drain: null,
      addNum: null,
      drainNum: null,
      dom: {
        name: null,
        createrName: null,
        img: null,
      },
      dto: {
        channelCodeId: null,
        startTime: null,
        endTime: null,
      },
    };
  },
  created() {
    if (this.id !== null && this.id !== '' && this.id !== undefined) {
      this.getChannelCodeById(this.id);
      this.customerByChannelCodeId(this.id);
      this.dto.channelCodeId = this.id;
      this.dto.startTime = new Date().getFullYear() + "/" + (new Date().getMonth() + 1) + "/" + new Date().getDate();
      this.dto.endTime = new Date().getFullYear() + "/" + (new Date().getMonth() + 1) + "/" + (new Date().getDate() + 1);
      this.customerByTime()
    }
  },
  watch: {
    $route: {
      handler: function (val, oldVal) {
        if (val.query.id !== null && val.query.id !== undefined && val.query.id !== '') {
          this.id = val.query.id;
          this.dto.channelCodeId = val.query.id;
          this.dto.startTime = new Date().getFullYear() + "/" + (new Date().getMonth() + 1) + "/" + new Date().getDate();
          this.dto.endTime = new Date().getFullYear() + "/" + (new Date().getMonth() + 1) + "/" + (new Date().getDate() + 1);
          this.getChannelCodeById(val.query.id);
          this.customerByChannelCodeId(val.query.id);
          this.customerByTime();
        }
      }
    }
  },
  methods: {
    //基础信息
    getChannelCodeById(id) {
      this.$getData("getChannelCodeById", {id: id}).then(res => {
        if (res.status === 200) {
          this.dom = res.data
        }
      })
    },
    customerByChannelCodeId(id) {
      this.$getData("customerByChannelCodeId", {id: id}).then(res => {
        if (res.status === 200) {
          this.drain = res.data
        }
      })
    },
    //复制链接
    copyUrl() {
      var clipboard = new Clipboard('.tag-read')
      clipboard.on('success', e => {
        this.$message({
          message: '复制成功', type: 'success'
        });
        // 释放内存
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        this.$message.error('该浏览器不支持自动复制');
        // 不支持复制
        // 释放内存
        clipboard.destroy()
      })
    },
    //下载二维码
    downloadUrl(img) {
      window.open(img)
    },
    //根据时间查看新增、流失 客户数
    customerByTime() {
      this.$postData("customerByTime", this.dto).then(res => {
        if (res.status === 200) {
          this.addNum = res.data.add
          this.drainNum = res.data.drain
        }
      })
    },
    //根据时间查看新增、流失 客户数
    statisticalDate() {
      this.dto.endTime = null
      this.dto.startTime = null
      this.value = null
      if (this.radio === '0') {
        let yy = new Date().getFullYear();
        let mm = new Date().getMonth() + 1;
        let dd = new Date().getDate() + 1;
        this.dto.endTime = yy + '/' + mm + '/' + dd;
        this.dto.startTime = yy + '/' + mm + '/' + (dd - 1);
        this.value = [this.dto.startTime, this.dto.endTime]
        this.customerByTime()
      } else if (this.radio === '1') {
        let yy = new Date().getFullYear();
        let mm = new Date().getMonth() + 1;
        let dd = new Date().getDate() + 1;
        this.dto.endTime = yy + '/' + mm + '/' + dd;
        let myDate = new Date()
        myDate.setDate(myDate.getDate() - 6)
        this.dto.startTime = myDate.getFullYear() + '/' + (myDate.getMonth() + 1) + '/' + myDate.getDate()
        this.value = [this.dto.startTime, this.dto.endTime]
        this.customerByTime()
      } else if (this.radio === '2') {
        let yy = new Date().getFullYear();
        let mm = new Date().getMonth() + 1;
        let dd = new Date().getDate() + 1;
        this.dto.endTime = yy + '/' + mm + '/' + dd;
        let myDate = new Date()
        myDate.setDate(myDate.getDate() - 29)
        this.dto.startTime = myDate.getFullYear() + '/' + (myDate.getMonth() + 1) + '/' + myDate.getDate()
        this.value = [this.dto.startTime, this.dto.endTime]
        this.customerByTime()
      }
    },
    //自定义时间选择
    custom() {
      this.radio = null
      if (this.value != null && this.value.length > 0) {
        this.dto.startTime = this.value[0];
        this.dto.endTime = this.value[1];
      }
      this.customerByTime()
    },
  },
}
</script>

<style scoped>
.a {
  border: 1px solid #eee;
  color: rgb(48, 49, 51);
  background: rgb(236, 238, 245);
  font-size: 18px;
  padding: 1%;
  text-align: center;
}

.b {
  font-size: 15px;
  padding-top: 15px;
}

.grid-a {
  color: rgb(0, 0, 0);
  font-size: 30px;
}

.grid-b {
  font-size: 16px;
  background: #F5F7FA;
  padding: 7%;
}

.grid-c {
  border-radius: 4px;
  font-size: 16px;
  padding: 7%;
}
</style>
