<template>
	<div class="container" id="pdfDom">

		<!-- 搜索栏目 -->
		<div class="handle-box">
			<el-form ref="form" :model="pageInfo">
				<el-row>
					<el-col :span="6">
						<el-form-item label="搜索关键词" style="margin-right: 20px">
							<el-input v-model="quer.search" placeholder="通知标题、内容、备注及接收人等"></el-input>
						</el-form-item>
					</el-col>

					<el-col :span="3">
						<el-form-item label="通知分组" style="margin-right: 20px">
							<el-select v-model="searchGroup">
								<el-option label="" value=""></el-option>
								<el-option v-for="(item,index) in groupList" :key="index" :label="item.groupName"
									:value="item.id"></el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :span="3">
						<el-form-item label="通知类型" style="margin-right: 20px">
							<el-select v-model="searchType">
								<el-option label="" value=""></el-option>
								<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
									:value="item.id"></el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :span="3">
						<el-form-item label="通知状态" style="margin-right: 20px">
							<el-select v-model="searchState">
								<el-option label="" value=""></el-option>
								<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
									:value="item.value"></el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :span="3">
						<el-form-item label="发布类型" style="margin-right: 20px">
							<el-select v-model="searchPublicNotice">
								<el-option label="" value=""></el-option>
								<el-option v-for="(item,index) in publicNoticeList" :key="index" :label="item.text"
									:value="item.value"></el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :span="6" style="margin: 32px 0;">
						<el-button type="primary" @click="query()" icon="el-icon-search">搜索
						</el-button>

						<el-button icon="el-icon-refresh" @click="reset()">重置
						</el-button>

						<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(0,0)">添加
						</el-button>

						<!-- 						<el-button type="success" @click="imgToPdf(blankImg)">测试
						</el-button> -->
					</el-col>
				</el-row>

			</el-form>
		</div>

		<!-- 数据表格 -->
		<el-table :data="list" v-loading="loading" border stripe
			:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
			:expand-row-keys="expands" @expand-change="expandSelect">

			<el-table-column prop="id" width="90" label="编号" sortable>
				<template slot-scope="scope">
					<span>{{scope.row.id}}</span>
				</template>
			</el-table-column>
			
			<el-table-column width="122" label="发布类型" :render-header="renderHeader">>
				<template slot-scope="scope">
					<div v-if="!scope.row.isEdit">
						<el-tag v-if="scope.row.publicNotice == 0" type="info">单人通知</el-tag>
						<el-tag v-if="scope.row.publicNotice == 1" type="success">公共通知</el-tag>
					</div>
			
					<el-select v-model="scope.row.publicNotice" placeholder="请选择" style="width: 100px;"
						v-if="scope.row.isEdit">
						<el-option v-for="(item,index) in publicNoticeList" :key="index" :label="item.text"
							:value="item.value"></el-option>
					</el-select>
				</template>
			</el-table-column>
			
			<el-table-column width="180" label="查看权限" :render-header="renderHeader">
				<template slot-scope="scope">
					<div v-if="scope.row.authList!=0||scope.row.isEdit">
						<ul v-for="(item,index) in authList" :key="index">
							<li class="tags-li" v-for="(item1,index1) in scope.row.authListArray" :key="index1"
								v-if="item.id==parseInt(item1)">
								<span>
									{{ item.name }}
								</span>
								<span class="tags-li-icon" @click="closeAuthTags(scope.$index,index1)"><i
										class="el-icon-close" v-if="scope.row.isEdit"></i></span>
							</li>
						</ul>
						<el-button type="primary" @click="openModal(1,scope.$index)" plain icon="el-icon-plus"
							v-if="scope.row.isEdit">添加角色</el-button>
					</div>
			
					<div v-if="scope.row.authList==0&&!scope.row.isEdit">
						<span>暂未设置</span>
					</div>
				</template>
			</el-table-column>
			
			<el-table-column width="122" label="通知状态">
				<template slot-scope="scope">
					<div v-if="!scope.row.isEdit">
						<el-tag v-if="item.value == scope.row.informState" v-for="(item,index) in stateList"
							:key="index" :type="item.type">{{item.text}}</el-tag>
					</div>
			
					<el-select v-model="scope.row.informState" placeholder="请选择" style="width: 100px;"
						v-if="scope.row.isEdit">
						<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
							:value="item.value"></el-option>
					</el-select>
				</template>
			</el-table-column>

			<el-table-column width="140" prop="informTitle" label="通知标题">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{scope.row.informTitle}}</span>

					<el-input v-model="scope.row.informTitle" style="width: 100%;" type="textarea"
						v-if="scope.row.isEdit" placeholder="请输入通知标题" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>

			<el-table-column width="180" prop="informContent" label="通知内容" :render-header="renderHeader">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{formatLongStr(scope.row.informContent)}}</span>

					<el-input v-model="scope.row.informContent" style="width: 100%;" type="textarea"
						v-if="scope.row.isEdit" placeholder="请输入通知内容" :autosize="{ minRows: 8, maxRows: 20}">
					</el-input>
				</template>
			</el-table-column>

			<el-table-column width="180" prop="informContent" label="内容详情" :render-header="renderHeader">
				<template slot-scope="scope">
					<el-tag @click="openContent(scope.$index)">查看详情</el-tag>
				</template>
			</el-table-column>

			<el-table-column width="140" prop="informRemark" label="通知备注" :render-header="renderHeader">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{scope.row.informRemark||'暂无'}}</span>

					<el-input v-model="scope.row.informRemark" style="width: 100%;" type="textarea"
						v-if="scope.row.isEdit" placeholder="请输入通知备注" :autosize="{ minRows: 4, maxRows: 10}">
					</el-input>
				</template>
			</el-table-column>

			<el-table-column width="122" prop="informGrouping" label="通知分组">
				<template slot-scope="scope">
					<div v-if="!scope.row.isEdit">
						<el-tag v-if="item.id == scope.row.informGrouping" v-for="(item,index) in groupList"
							:key="index"
							:type="formatTypeStyle(scope.row.informGrouping||0)">{{formatGroup(scope.row)}}</el-tag>
					</div>

					<el-select v-model="scope.row.informGrouping" placeholder="请选择" style="width: 100px;"
						v-if="scope.row.isEdit">
						<el-option v-for="(item,index) in groupList" :key="index" :label="item.groupName"
							:value="item.id"></el-option>
					</el-select>
				</template>
			</el-table-column>

			<el-table-column width="122" prop="informType" label="通知类型" :render-header="renderHeader">
				<template slot-scope="scope">
					<div v-if="!scope.row.isEdit">
						<el-tag v-if="item.id == scope.row.informType" v-for="(item,index) in typeList" :key="index"
							:type="formatTypeStyle(scope.row.informType||0)">{{formatType(scope.row)}}</el-tag>
					</div>

					<el-select v-model="scope.row.informType" placeholder="请选择" style="width: 100px;"
						v-if="scope.row.isEdit">
						<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
							:value="item.id"></el-option>
					</el-select>
				</template>
			</el-table-column>

			<el-table-column width="140" prop="informImg" label="通知图片" :render-header="renderHeader">
				<template slot-scope="scope">
					<el-upload class="upload-demo" :action="uploadUrl" :disabled="!scope.row.isEdit" list-type="picture"
						:on-success="imgUploadSuccess" :show-file-list="false">
						<img :src="scope.row.informImg!=null?scope.row.informImg:blankImg"
							style="width: 120px;height: 84px;"
							@click="scope.row.isEdit?openImgUpload(0,scope.$index):openImg(scope.row.informImg)">
					</el-upload>
				</template>
			</el-table-column>

			<el-table-column width="140" prop="informUrl" label="通知链接" :render-header="renderHeader">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{scope.row.informUrl||'暂无'}}</span>

					<el-input v-model="scope.row.informUrl" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
						placeholder="请输入通知跳转链接" :autosize="{ minRows: 4, maxRows: 10}">
					</el-input>
				</template>
			</el-table-column>

			<el-table-column width="130" prop="creater" label="接收人">
				<template slot-scope="scope">
					<span>{{scope.row.employeeName||scope.row.memberName||'-'}}</span>
				</template>
			</el-table-column>

			<el-table-column width="130" prop="creater" label="创建人">
				<template slot-scope="scope">
					<span>{{scope.row.senderName||'系统'}}</span>
				</template>
			</el-table-column>

			<el-table-column width="180" prop="creTime" label="创建时间" sortable>
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{scope.row.creTime}}</span>
					<el-date-picker v-model="scope.row.creTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
						:default-time="['00:00:00']" v-if="scope.row.isEdit" placeholder="请选择考试开始日期"
						:picker-options="pickerOptions">
					</el-date-picker>
				</template>
			</el-table-column>

			<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
				<template slot-scope="scope">
					<el-button @click="openEdit(scope.row)" type="success" size="small" :disabled="scope.row.isEdit"
						v-if="!scope.row.isEdit" icon="el-icon-edit">修改
					</el-button>
					<el-button @click="updateUnionInform(scope.row)" type="primary" size="small" v-if="scope.row.isEdit"
						icon="el-icon-circle-check">保存
					</el-button>
					<el-button @click="openView(scope.row)" type="primary" size="small" :disabled="scope.row.isEdit"
						v-if="!scope.row.isEdit" icon="el-icon-document">预览
					</el-button>
					<el-popconfirm title="确定删除该消息通知吗？删除后无法恢复，请谨慎操作!" @confirm="deleteUnionInform(scope.row)"
						v-if="scope.row.isEdit">
						<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
							:disabled="!scope.row.isEdit" icon="el-icon-delete">删除
						</el-button>
					</el-popconfirm>
				</template>
			</el-table-column>

		</el-table>


		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!-- 消息通知预览 -->
		<el-drawer size="25%" :with-header="false" :visible.sync="viewModal" direction="rtl" :show-close="false">
			<iframe id="iframeId" :src="viewPageUrl" frameborder="0" style="width: 400px;height: 800px;margin: 0 auto;"
				scrolling="auto">
			</iframe>
		</el-drawer>

		<!--添加消息通知-->
		<el-drawer size="60%" :with-header="false" :visible.sync="informModal" direction="rtl">
			<div>
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<h2 class="detail-title">消息通知</h2>
					<el-col :span="10">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 通知标题：">
								<el-input v-model="unionInform.informTitle" type="textarea" class="handle-input mr10"
									placeholder="请输入通知标题">
								</el-input>
							</el-form-item>

							<el-form-item label="* 通知内容：">
								<el-input v-model="unionInform.informContent" type="textarea" class="handle-input mr10"
									placeholder="请输入通知内容描述">
								</el-input>
							</el-form-item>

							<el-form-item label="通知备注：">
								<el-input v-model="unionInform.informRemark" type="textarea" class="handle-input mr10"
									placeholder="请输入通知备注">
								</el-input>
							</el-form-item>

							<el-form-item label="通知链接：">
								<el-input v-model="unionInform.informUrl" type="textarea" class="handle-input mr10"
									placeholder="请输入通知链接">
								</el-input>
							</el-form-item>

							<el-form-item label="通知图片：">
								<el-upload class="upload-demo" :action="uploadUrl" list-type="picture"
									:on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="unionInform.informImg!=null?unionInform.informImg:blankImg"
										style="width: 350px;height: 245px;" @click="openImgUpload(1,0)">
								</el-upload>
							</el-form-item>
						</el-form>
					</el-col>

					<el-col :span="14">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 通知分组：">
								<el-select v-model="unionInform.informGrouping" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in groupList" :key="index" :label="item.groupName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="* 通知类型：">
								<el-select v-model="unionInform.informType" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="发布类型：">
								<el-select v-model="unionInform.publicNotice" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in publicNoticeList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
								<el-tooltip class="item" effect="dark" content="默认为单人通知，若设置了查看权限，则自动变更为多人通知"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="查看权限：">
								<div>
									<ul v-for="(item,index) in authList" :key="index">
										<li class="tags-li" v-for="(item1,index1) in unionInform.authListArray"
											:key="index1" v-if="item.id==parseInt(item1)">
											<span>
												{{ item.name }}
											</span>
											<span class="tags-li-icon" @click="closeAuthTags1(index1)"><i
													class="el-icon-close"></i></span>
										</li>
									</ul>
									<el-button type="primary" @click="openModal(2,0)" plain
										icon="el-icon-plus">添加角色</el-button>
								</div>

								<div v-if="unionInform.authList==0">
									<span>暂未设置</span>
								</div>
							</el-form-item>

							<el-form-item label="接收人：">
								<div v-if="eList.length==0">
									<span>暂未设置</span>
								</div>

								<el-transfer v-model="eList" filterable :filter-method="filterMethod"
									:titles="['员工列表', '接收人列表']" filter-placeholder="请输入员工姓名" :props="{
								      key: 'id',
								      label: 'realName'
								    }" :data="employeeList">
								</el-transfer>
							</el-form-item>

						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="informModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addUnionInform()" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-drawer>


		<!-- 消息通知内容编辑 -->
		<el-drawer size="80%" :with-header="false" :visible.sync="contentModal" direction="rtl"
			:wrapperClosable="false">
			<div class="drawerForm" style="padding: 40px 40px;">
				<h1>消息通知-内容详情编辑</h1>
				<h2 style="margin-top: 20px;">内容编辑器（推荐使用）</h2>
				<div style="border: 1px solid #ccc;margin: 20px 0;">
					<Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig"
						mode="default" />
					<Editor style="height: 500px; overflow-y: hidden;" v-model="contentText"
						:defaultConfig="editorConfig" :mode="mode" @onCreated="onCreated" />
				</div>

				<el-button @click="updateUnionInform1(list[choiceItemIndex])" type="primary" size="small"
					icon="el-icon-circle-check">保存/预览
				</el-button>
				<el-button @click="contentModal=false">关闭</el-button>

				<el-divider></el-divider>
				<h2>内容详情列表（后续弃用）</h2>
				<el-form ref="form">
					<el-table :data="contentList" ref="table">
						<el-table-column width="60">
							<template :slot-scope="scope">
								<i class="el-icon-s-operation cursor-pointer"></i>
							</template>
						</el-table-column>
						<el-table-column prop="id" label="编号" width="80">
							<template slot-scope="scope">
								<div>{{scope.row.id}}</div>
							</template>
						</el-table-column>

						<el-table-column width="180" prop="contentText" label="内容文本">
							<template slot-scope="scope">
								<div v-if="!isEditContent">{{scope.row.contentText||'暂无'}}</div>
								<el-input v-model="scope.row.contentText" style="width: 100%;" type="textarea"
									v-if="isEditContent" placeholder="请输入内容文本" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</template>
						</el-table-column>


						<el-table-column width="180" prop="contentRemark" label="内容备注" :render-header="renderHeader">
							<template slot-scope="scope">
								<div v-if="!isEditContent">{{scope.row.contentRemark||'暂无'}}</div>
								<el-input v-model="scope.row.contentRemark" style="width: 100%;" type="textarea"
									v-if="isEditContent" placeholder="请输入内容备注" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</template>
						</el-table-column>

						<el-table-column width="180" prop="contentUrl" label="内容链接">
							<template slot-scope="scope">
								<div v-if="!isEditContent">{{scope.row.contentUrl||'暂无'}}</div>
								<el-input v-model="scope.row.contentUrl" style="width: 100%;" type="textarea"
									v-if="isEditContent" placeholder="请输入内容链接" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</template>
						</el-table-column>

						<el-table-column width="140" prop="contentImg" label="内容图片">
							<template slot-scope="scope">
								<el-upload class="upload-demo" :action="uploadUrl" :disabled="!isEditContent"
									list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="scope.row.contentImg?scope.row.contentImg:blankImg"
										style="width: 120px;height: auto;"
										@click="isEditContent?openImgUpload(2,scope.$index):openImg(scope.row.contentImg)">
								</el-upload>
							</template>
						</el-table-column>


						<el-table-column width="160" prop="sort" label="内容序号">
							<template slot-scope="scope">
								<div v-if="!isEditContent">{{scope.row.sort}}</div>
								<el-input-number v-model="scope.row.sort" v-if="isEditContent" :min="1" :max="100"
									label="请输入内容序号"></el-input-number>
							</template>
						</el-table-column>

						<el-table-column width="130" prop="createrName" label="创建人">
							<template slot-scope="scope">
								<div>
									{{scope.row.createrName||''}}{{scope.row.createrNo?'('+scope.row.createrNo+')':''}}
								</div>
							</template>
						</el-table-column>

						<el-table-column width="140" prop="creTime" label="创建时间">
							<template slot-scope="scope">
								<div>
									{{scope.row.creTime||formatDate(new Date())}}
								</div>
							</template>
						</el-table-column>

						<el-table-column fixed="right" label="操作" width="160">
							<template slot-scope="scope">
								<el-popconfirm title="确定删除该内容吗？删除后将无法恢复，请谨慎操作!"
									@confirm="deleteUnionInformContent(scope.$index)">
									<el-button type="danger" size="small" slot="reference" :disabled="!isEditContent"
										icon="el-icon-delete">删除
									</el-button>
								</el-popconfirm>
							</template>
						</el-table-column>
					</el-table>

					<el-form-item style="margin-top: 20px;">
						<el-button @click="isEditContent = true" type="success" size="small" :disabled="isEditContent"
							v-if="!isEditContent" icon="el-icon-edit">修改
						</el-button>
						<el-button @click="updateUnionInformContent()" type="primary" size="small" v-if="isEditContent"
							icon="el-icon-circle-check">更新
						</el-button>
						<el-button @click="contentModal=false">关闭</el-button>
						<el-button @click="addContent" type="warning" size="small" v-if="isEditContent"
							icon="el-icon-circle-plus-outline">添加
						</el-button>
					</el-form-item>
				</el-form>
			</div>
		</el-drawer>

		<!-- 添加通知查看权限 -->
		<el-dialog :visible.sync="authIdListModal" width="60%" title="添加通知查看权限" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;" v-if="choiceItem">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-checkbox-group v-model="choiceItem.authListArray" value-key="id">
						<el-checkbox-button v-for="(val,index) in authList" :key="index" :label="val.id">{{ val.name }}
						</el-checkbox-button>
					</el-checkbox-group>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="authIdListModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="addAuthIdList()" type="success" size="small">确定添加
					</el-button>
				</div>
			</div>
		</el-dialog>

	</div>

</template>

<script>
	import Vue from 'vue';
	import Sortable from "sortablejs";
	import {
		Editor,
		Toolbar
	} from '@wangeditor/editor-for-vue'
	export default {
		name: "unionInform",
		components: {
			Editor,
			Toolbar
		},
		data() {
			return {
				url: '',
				imageUrl: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				excelUploadId: 1,
				excelUploadUrl: "",
				employeeId: localStorage.getItem("id") || null,

				editor: null,
				contentText: '<p>hello</p>',
				toolbarConfig: {},
				editorConfig: {
					placeholder: "请输入内容...",
					readOnly: false,
					MENU_CONF: {
						//上传参考https://www.wangeditor.com/v5/menu-config.html#%E5%9F%BA%E6%9C%AC%E9%85%8D%E7%BD%AE
						uploadImage: {
							server: `https://api.xiaoyujia.com/system/imageUpload`,
							// 超时时间，默认为 10 秒
							timeout: 30 * 1000, // 5s
							fieldName: "file",
							// 将 meta 拼接到 url 参数中，默认 false
							metaWithUrl: false, // join params to url
							// 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
							meta: {
								watermark: 1,
							},
							// 自定义增加 http  header
							// headers: {
							//   Accept: "text/x-json",
							//   otherKey: "xxx"
							// },
							// 跨域是否传递 cookie ，默认为 false
							withCredentials: true,
							// 自定义增加 http  header
							// headers: {
							//   Accept: "text/x-json",
							//   otherKey: "xxx"
							// },
							maxFileSize: 10 * 1024 * 1024, // 10M
							base64LimitSize: 5 * 1024, // insert base64 format, if file's size less than 5kb
							// 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
							allowedFileTypes: ["image/*"],
							// 最多可上传几个文件，默认为 100
							maxNumberOfFiles: 30,
							onBeforeUpload(file) {
								console.log("onBeforeUpload", file);
								return file; // will upload this file
								// return false // prevent upload
							},
							onProgress(progress) {
								console.log("onProgress", progress);
							},
							onSuccess(file, res) {
								console.log("onSuccess", file, res);
							},
							onFailed(file, res) {
								alert(res.message);
								console.log("onFailed", file, res);
							},
							onError(file, err, res) {
								alert(err.message);
								console.error("onError", file, err, res);
							},
							// 自定义插入图片
							customInsert(res, insertFn) {
								insertFn(res.data, '消息图片', '消息图片')
							},
						},
						uploadVideo: {
							server: "https://api.xiaoyujia.com/system/uploadFile",
							fieldName: "file",
							// 单个文件的最大体积限制，默认为 10M
							maxFileSize: 100 * 1024 * 1024, // 100M
							// 最多可上传几个文件，默认为 5
							maxNumberOfFiles: 5,
							// 选择文件时的类型限制，默认为 ['video/*'] 。如不想限制，则设置为 []
							allowedFileTypes: ["video/*"],
							// 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
							meta: {

							},
							// 将 meta 拼接到 url 参数中，默认 false
							metaWithUrl: false,
							// 自定义增加 http  header
							// headers: {
							//   Accept: "text/x-json",
							//   otherKey: "xxx"
							// },
							// 跨域是否传递 cookie ，默认为 false
							withCredentials: true,
							// 超时时间，默认为 30 秒
							timeout: 30 * 1000, // 15 秒
							// 上传进度的回调函数
							onProgress(progress) {
								console.log("progress", progress);
							},
							// 单个文件上传成功之后
							onSuccess(file, res) {
								console.log(`${file.name} 上传成功`, res);
							},
							// 单个文件上传失败
							onFailed(file, res) {
								console.log(`${file.name} 上传失败`, res);
							},
							// 上传错误，或者触发 timeout 超时
							onError(file, err, res) {
								console.log(`${file.name} 上传出错`, err, res);
							},
							// 自定义插入视频
							customInsert(res, insertFn) {
								insertFn(res.data, '消息视频', '消息视频')
							},
						}
					}
				},

				// 测试时使用
				// uploadUrl: "http://localhost:8063/files/uploadFiles",
				// excelUploadUrlOrgin: "http://localhost:8063/courseContent/excelImport",

				uploadUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
				excelUploadUrlOrgin: "https://biapi.xiaoyujia.com/courseContent/excelImport",
				deleteTips: "确定删除选中通知吗？该操作无法恢复，请谨慎操作!",
				isEdit: false,
				isEditContent: false,
				showEdit: true,
				viewModal: false,
				imgModal: false,
				informModal: false,
				contentModal: false,
				authIdListModal: false,
				viewPageUrl: '',
				list: [],
				contentList: [],
				eList: [],
				employeeList: [],
				unionInform: {},
				choiceItem: null,
				choiceItemIndex: 0,
				loading: true,
				pageSizeOpts: [10, 20, 50],
				pageInfo: {
					total: 10,
					size: 10,
					current: 1,
					pages: 1
				},
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				searchType: null,
				searchGroup: null,
				searchState: null,
				searchPublicNotice: 1,
				quer: {
					publicNotice: 1,
					search: "",
					typeList: null,
					state: null,
					orderBy: "u.creTime DESC",
					current: 1,
					size: 10
				},
				quer1: {
					realName: "",
				},
				authList: [{
					id: 1,
					name: '普通会员'
				}, {
					id: 2,
					name: '行政员工'
				}, {
					id: 3,
					name: '服务员工'
				}, {
					id: 4,
					name: '保姆月嫂'
				}, {
					id: 5,
					name: '共创店'
				}, {
					id: 6,
					name: '合伙人'
				}, {
					id: 7,
					name: '门店店长'
				}, {
					id: 8,
					name: '陪跑店店长'
				}],
				stateList: [{
					value: 0,
					text: "未读",
					type: "info"
				}, {
					value: 1,
					text: "已读",
					type: "success"
				}, {
					value: 2,
					text: "删除",
					type: "warning"
				}],
				publicNoticeList: [{
					value: 0,
					text: "单人通知",
					type: "info"
				}, {
					value: 1,
					text: "公共通知",
					type: "success"
				}],
				typeStyleList: [{
						id: 0,
						type: "success"
					},
					{
						id: 1,
						type: "info"
					},
					{
						id: 2,
						type: "warning"
					}, {
						id: 3,
						type: "primary"
					}
				],
				typeList: [],
				groupList: [],
				activeRows: [],
				pickerOptions: {
					shortcuts: [{
						text: '昨天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '今天',
						onClick(picker) {
							picker.$emit('pick', new Date());
						}
					}, {
						text: '明天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周前',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周后',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}]
				},
			}
		},
		created() {
			this.getData()
		},
		beforeDestroy() {
			const editor = this.editor
			if (editor == null) return
			editor.destroy() // 组件销毁时，及时销毁编辑器
		},
		methods: {
			onCreated(editor) {
				this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
			},
			// 初始化拖动排序功能
			initSort() {
				const tbody = this.$refs.table.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
				Sortable.create(tbody, {
					animation: 300,
					onMove: () => {
						if (!this.activeRows.length) {
							this.activeRows = this.contentList
						}
					},
					onEnd: e => {
						if (e.oldIndex !== e.newIndex) { // 根据自己项目需求增添条件限制
							let arr = JSON.parse(JSON.stringify(this.activeRows))
							const oldRow = this.activeRows[e.oldIndex] // 移动的那个元素
							const newRow = this.activeRows[e.newIndex] // 新的元素
							arr.splice(e.newIndex, 0, arr.splice(e.oldIndex, 1)[0]);
							this.activeRows = arr
						}
					}
				})
			},
			deepClone(arr) {
				const copy = []
				for (let i = 0; i < arr.length; i++) {
					if (Array.isArray(arr[i])) {
						copy[i] = this.deepClone(arr[i])
					} else if (typeof arr[i] === 'object' && arr[i] !== null) {
						copy[i] = this.deepClone(arr[i])
					} else {
						copy[i] = arr[i]
					}
				}
				return copy
			},
			filterMethod(query, item) {
				return item.realName.indexOf(query) > -1;
			},
			getData() {
				// 获取列表
				this.$postData("pageUnionInform", this.quer).then(res => {
					this.loading = false
					if (res.status == 200) {
						this.list = res.data.records
						this.pageInfo.current = res.data.current
						this.pageInfo.size = res.data.size
						this.pageInfo.total = res.data.total
						// 追加编辑状态位
						for (let item of this.list) {
							this.$set(item, "isEdit", false)
							// 追加报名权限
							this.$set(item, "authListArray", this.strToArray(item.authList))
						}
					} else {
						this.list = []
						this.$message.error('未查询到相关通知记录!')
					}
				})

				this.getType()
				this.getGroup()
			},
			listUnionInformContent() {
				this.contentList = []
				this.activeRows = []
				let id = this.list[this.choiceItemIndex].id
				this.contentText = this.list[this.choiceItemIndex].informContentText || ''
				this.$getUrl("listUnionInformContent", id).then(res => {
					this.loading = false
					if (res.status == 200) {
						this.contentList = res.data
						this.contentList.forEach(item => {
							this.$set(item, 'isEdit', false)
						})
						this.initSort()
					} else {
						this.contentList = []
						this.activeRows = []
					}
				})
			},
			listEmployee() {
				this.$postData("employee_getByList", this.quer1).then(res => {
					this.loading = false
					if (res.status == 200) {
						this.employeeList = res.data
					} else {
						this.employeeList = []
						this.$message.error('未查询到相关员工!')
					}
				})
			},
			getType() {
				this.$getData("listAllUnionInformType").then(res => {
					this.loading = false
					if (res.status == 200) {
						this.typeList = res.data
					} else {
						this.typeList = []
						this.$message.error('未查询到相关考试/题库类型!')
					}
				})
			},
			getGroup() {
				this.$getData("listAllUnionInformGroup").then(res => {
					this.loading = false
					if (res.status == 200) {
						this.groupList = res.data
					} else {
						this.groupList = []
						this.$message.error('未查询到相关考试/题库类型!')
					}
				})
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1

				this.quer.informGrouping = this.searchGroup
				this.quer.informType = this.searchType
				this.quer.informState = this.searchState
				this.quer.publicNotice = this.searchPublicNotice

				this.getData()
			},
			// 重置
			reset() {
				this.loading = true
				this.quer.search = ""
				this.quer.orderBy = "u.creTime DESC"
				this.quer.current = 1

				this.quer.informGrouping = null
				this.quer.informType = null
				this.quer.informState = null
				this.quer.publicNotice = null

				this.searchGroup = null
				this.searchType = null
				this.searchState = null
				this.searchPublicNotice = null
				this.isEdit = false
				this.getData()
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 多选
			handleSelectionChange(val) {
				this.multipleSelection = val
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "通知内容") {
					tips = "支持通过“|”换行输入，且可以插入图片链接（适用于简单通知）"
				} else if (column.label == "内容详情") {
					tips = "可设置详细图文段落及跳转链接（适用于复杂通知）"
				} else if (column.label == "通知备注") {
					tips = "将以括号形式显示"
				} else if (column.label == "通知图片") {
					tips = "附带的图片"
				} else if (column.label == "通知链接") {
					tips = "可通过通知跳转到小程序对应页面"
				} else if (column.label == "发布类型") {
					tips = "单人通知：只针对某个人，公共通知：针对多人。"
				} else if (column.label == "查看权限") {
					tips = "只针对某些人可见（若启用则需设置为公共通知）"
				} else if (column.label == "内容备注") {
					tips = "将以红色文本显示"
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			// 将字符串转化为数组
			strToArray(val) {
				let arrayString = []
				let array = []
				arrayString = val.split(',')
				arrayString.forEach(it => {
					let value = parseInt(it)
					if (value != null && value != 0) {
						array.push(value)
					}
				})
				return array
			},
			// 时间格式化
			formatDate(value) {
				if (value == null) {
					return "-"
				}
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				return y + '.' + MM + '.' + d;
			},
			formatLongStr(str) {
				if (!str) {
					return "暂无"
				} else {
					let long = 240
					if (str.length > long) {
						str = str.substring(0, long) + "..."
					}
					return str
				}
			},
			formatTypeStyle(type) {
				return this.typeStyleList[type % 4].type
			},
			// 格式化类型
			formatType(val) {
				let result = "暂无"
				let id = val.informType
				for (let item of this.typeList) {
					if (id == item.id) {
						result = item.typeName
						break
					}
				}
				return result
			},
			formatGroup(val) {
				let result = "暂无"
				let id = val.informGrouping
				for (let item of this.groupList) {
					if (id == item.id) {
						result = item.groupName
						break
					}
				}
				return result
			},
			// 通知Excel导入
			excelImport(index) {
				let id = this.list[index].id
				let excelUploadUrl = this.excelUploadUrlOrgin
				excelUploadUrl += "/" + id
				this.excelUploadUrl = excelUploadUrl
				this.excelUploadId = index
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 通知Excel模板下载
			excelDownload() {
				this.$postData("excelDownload", null, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('通知Excel模板下载成功!')
					this.blobExport({
						tablename: "通知Excel模板",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// Excel表格上传成功
			excelUploadSuccess(res, file) {
				this.$message.success('章节导入成功！点击保存按钮即可录入哦！')
				// 将解析到的结果加入到章节列表中
				for (let item of res.data) {
					this.list[this.excelUploadId].courseContent.push(item)
				}
			},
			// Excel表格上传失败
			excelUploadError() {
				this.$message.error('章节内容解析失败！请使用正确的Excel模板进行上传！')
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.list[this.uploadId].informImg = res.data
				} else if (this.uploadImgType == 1) {
					this.$set(this.unionInform, "informImg", res.data)
				} else if (this.uploadImgType == 2) {
					this.$set(this.contentList[this.uploadId], "contentImg", res.data)
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadId = index
				this.$message.info('请上传图片！')
			},
			// 打开图片预览
			openImg(url) {
				if (url != null && url != '') {
					this.imageUrl = url
				} else {
					this.imageUrl = this.blankImg
				}
				this.imgModal = true
			},
			// 打开预览
			openView(val) {
				let id = val.id
				this.viewPageUrl = "https://jiajie.xiaoyujia.com/pages-mine/inform/inform-detail?id=" + id
				this.viewModal = true
			},
			openContent(index) {
				this.choiceItemIndex = index
				this.listUnionInformContent()
				this.contentModal = true
				this.isEditContent = false
			},
			openModal(index, index1) {
				if (index == 0) {
					this.unionInform = {}
					this.listEmployee()
					this.informModal = true
				} else if (index == 1) {
					this.choiceItem = this.list[index1]
					this.authIdListModal = true
				} else if (index == 2) {
					if (!this.unionInform.authListArray || this.unionInform.authListArray.length == 0) {
						this.$set(this.unionInform, "authListArray", [])
					}
					this.choiceItem = this.unionInform
					this.authIdListModal = true
				}
			},
			//打开员工信息详情
			rowClick(id) {
				// this.baomuId = baomuId
				// this.informModal = true
				// this.loading = true
				// this.getBaomuDetail(baomuId)
			},
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			// 添加权限列表
			addAuthIdList() {
				this.authIdListModal = false
			},
			// 删除权限标签
			closeAuthTags(index, index1) {
				this.list[index].authListArray.splice(index1, 1)
			},
			closeAuthTags1(index, index1) {
				this.unionInform.authListArray.splice(index1, 1)
			},
			// 添加通知
			addUnionInform() {
				let inform = this.unionInform
				// 数据校验
				if (!inform.informTitle) {
					this.$message.error('请填写通知标题！')
				} else if (!inform.informContent) {
					this.$message.error('请填写通知内容！')
				} else if (!inform.informType) {
					this.$message.error('请填写通知类型！')
				} else if (!inform.informGrouping) {
					this.$message.error('请填写通知分组！')
				} else {
					let id = localStorage.getItem("id") || 0
					// 暂时全设为管理员发布
					id = 0
					this.$set(inform, "senderId", id)

					// 格式化权限角色列表
					if (inform.authListArray) {
						inform.authList = inform.authListArray.join(',')
						if (!inform.authList.length) {
							inform.authList = '0'
						} else {
							// 设置了查看权限，则自动修正为多人通知
							this.$set(inform, 'publicNotice', 1)
						}
					}

					if (this.eList.length) {
						this.$set(inform, 'employeeIdList', this.eList)
					}

					this.$postData("insertUnionInform", inform).then(res => {
						if (res.status == 200) {
							this.$message.success('通知添加成功!')
							this.informModal = false
							this.eList = []
							this.getData()
						} else {
							this.$message.error('通知添加失败！' + res.msg)
						}
					})
				}
			},
			// 删除通知
			deleteUnionInform(val) {
				this.$postData("deleteUnionInform", val).then(res => {
					if (res.status == 200) {
						this.$message.success('通知删除成功!')
						val.isEdit = false
						this.getData()
					} else {
						this.$message.error('通知删除失败！' + res.msg)
					}
				})
			},
			deleteUnionInformContent(index) {
				let item = this.contentList[index]
				if (item.id) {
					this.$postData("deleteUnionInformContent", item).then(res => {
						if (res.status != 200) {
							this.$message.error('删除失败！' + res.msg)
						}
					})
				}
				this.$message.success('删除成功!')
				this.$delete(this.contentList, index)
			},
			// 更改通知
			updateUnionInform(val) {
				// 格式化权限角色列表
				if (val.authListArray) {
					val.authList = val.authListArray.join(',')
					if (!val.authList.length) {
						val.authList = '0'
					}
				}

				this.$postData("updateUnionInform", val).then(res => {
					if (res.status == 200) {
						this.$message.success('通知更新成功!')
						val.isEdit = false
					} else {
						this.$message.error('通知更新失败！' + res.msg)
					}
				})
			},
			updateUnionInform1(val) {
				val.informContentText = this.contentText || ''
				this.updateUnionInform(val)
				setTimeout(() => {
					this.openView(val)
				}, 600)
			},
			updateUnionInformContent() {
				let val = this.contentList
				if (this.activeRows.length) {
					let count = 1
					this.activeRows.forEach(item => {
						item.sort = count
						count++
					})
					val = this.activeRows
				}
				this.$postData("updateUnionInformContentList", val).then(res => {
					if (res.status == 200) {
						this.$message.success('更新成功!')
					} else {
						this.$message.error('更新失败！' + res.msg)
					}
				})
			},
			// 添加消息内容
			addContent() {
				let sort = 1
				if (this.contentList.length > 0) {
					sort = this.contentList[this.contentList.length - 1].sort + 1
				}
				let data = {
					id: null,
					contentText: '',
					contentRemark: '',
					contentImg: '',
					contentUrl: '',
					contentState: 1,
					creTime: null,
					creater: this.employeeId,
					sort: sort,
					unionInformId: this.list[this.choiceItemIndex].id
				}
				this.contentList.push(data)
			},
			beforeImgUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!');
				}
				return isLt2M;
			}
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>

<style src="@wangeditor/editor/dist/css/style.css"></style>