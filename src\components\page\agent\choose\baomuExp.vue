<template style="background-color: #000">

            <el-tabs type="card" :stretch="true" >
                <el-dialog title="经验编辑" :visible.sync="dialogTableVisible">
                    <el-form ref="upform" :model="upExpModel" style="padding-bottom: 50px">
                        <el-form-item   prop="workContent">
                            <div class="label-name">工作内容:</div>
                            <el-input placeholder="请输入工作内容" type="textarea" v-model="upExpModel.workContent" style="width: 70%">
                            </el-input>
                        </el-form-item >

                        <el-form-item   prop="remark">
                            <div class="label-name">备注:</div>
                            <el-input placeholder="请输入备注" type="textarea" v-model="upExpModel.remark" style="width: 70%">
                            </el-input>
                        </el-form-item >
                        <el-form-item   prop="startWorkTime">
                            <div class="label-name">开始时间:</div>
                            <el-date-picker
                                    style="width: 70%"
                                    v-model="upExpModel.startWorkTime"
                                    type="month"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    placeholder="开始时间">
                            </el-date-picker>
                        </el-form-item >
                        <el-form-item   prop="endWorkTime" label="">
                            <div class="label-name">结束时间:</div>
                            <el-date-picker
                                    style="width: 70%"
                                    v-model="upExpModel.endWorkTime"
                                    type="month"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    placeholder="结束时间">
                            </el-date-picker>
                        </el-form-item >
                        <div style="margin-right: 100px;float: right;">
                            <Button v-if="showHandleByRoleId()" type="primary" @click="update(upExpModel,true)">确定</Button>
                        </div>
                    </el-form>
                </el-dialog>
                <el-dialog :visible.sync="dialogVisible">
                    <img width="100%" :src="dialogImageUrl" alt="">
                </el-dialog>
                <el-collapse v-model="activeNames" >

                    <el-collapse-item title="新增工作经验" name="1">
                        <template slot="title">
                            <el-button type="primary" >添加工作经验</el-button>
                        </template>
                        <el-form ref="form" :model="expModel">
                                    <el-form-item   prop="workContent">
                                        <div class="label-name">工作内容:</div>
                                        <el-input placeholder="请输入工作内容" type="textarea" v-model="expModel.workContent" style="width: 70%">
                                        </el-input>
                                    </el-form-item >

                                    <el-form-item   prop="remark">
                                        <div class="label-name">备注:</div>
                                        <el-input placeholder="请输入备注" v-model="expModel.remark" style="width: 70%">
                                        </el-input>
                                    </el-form-item >
                                    <el-form-item   prop="startWorkTime">
                                        <div class="label-name">开始时间:</div>
                                        <el-date-picker
                                                style="width: 70%"
                                                v-model="expModel.startWorkTime"
                                                type="month"
                                                value-format="yyyy-MM-dd HH:mm:ss"
                                                placeholder="开始时间">
                                        </el-date-picker>
                                    </el-form-item >
                                <el-form-item   prop="endWorkTime" label="">
                                    <div class="label-name">结束时间:</div>
                                    <el-date-picker
                                            style="width: 70%"
                                            v-model="expModel.endWorkTime"
                                            type="month"
                                            value-format="yyyy-MM-dd HH:mm:ss"
                                            placeholder="结束时间">
                                    </el-date-picker>
                                </el-form-item >

                            <div style="margin-right: 100px;float: right">
                                <Button v-if="showHandleByRoleId()" type="primary" @click="save('form')">确定</Button>
                            </div>
                        </el-form>
                    </el-collapse-item>
                </el-collapse>
                <div style="    font-size: 17px;font-weight: 600; color: #999;padding-left: 10px;border-left: thick double #409eff;padding-top: 20px">已有工作经验列表</div>
                <el-table
                        default-expand-all
                        :data="tableData"
                        style="width: 100%">
                    <el-table-column type="expand" >
                        <template slot-scope="props" >
                            <el-form label-position="left" inline class="demo-table-expand"  style="background: #f3f3f3;">
                                <el-form-item >
                                    <div v-for="img in props.row.showimgs" style="float: left">
                                        <div >
                                            <img
                                                    style="width: 148px;height: 148px; margin: 5px;"
                                                    :src="img" alt=""
                                                    @click="handlePictureCardPreview(img)"
                                            >
                                        </div>
                                        <el-button v-if="showHandleByRoleId()" type="danger" style="width: 148px;margin: 0px 5px 0px 5px;" @click="removeImg(props.row,img)">删除</el-button>
                                    </div>


                                </el-form-item>

                            </el-form>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="workContent"
                            label="工作内容"
                            width="180">
                    </el-table-column>

                    <el-table-column
                            prop="startWorkTime"
                            label="开始日期"
                            width="180">
                    </el-table-column>
                    <el-table-column
                            prop="endWorkTime"
                            label="结束日期"
                            width="180">
                    </el-table-column>
                    <el-table-column
                            prop="remark"
                            label="备注">
                    </el-table-column>
                    <el-table-column
                            label="操作"
                            min-width="200">
                        <template slot-scope="scope">
                            <el-button v-if="showHandleByRoleId()" size="mini" @click="delbaomuExp(scope.row.id)" type="info">删除</el-button>
                            <el-button v-if="showHandleByRoleId()" size="mini" @click="upbaomuExp(scope.row)" type="primary">编辑</el-button>
                            <el-upload
                                    style="float: left"
                                    class="upload-demo"
                                    action="https://biapi.xiaoyujia.com/files/uploadFiles"
                                    list-type="picture"
                                    :show-file-list="false"
                                    :on-success="handleAvatarSuccess">
                                <el-button v-if="showHandleByRoleId()" size="mini" type="primary" @click="insertImg(scope.row)">添加图片</el-button>
                            </el-upload>
                        </template>

                    </el-table-column>
                </el-table>
            </el-tabs>

</template>


<script>

    export default {
        props:['model'],
        data() {
            return {
                roleId: localStorage.getItem("roleId"),
                upexp:{},
                expId:null,
                dialogImageUrl: '',
                dialogVisible: false,
                dialogTableVisible: false,
                disabled: false,
                expModel:{
                    baomuID:null,
                    workContent:null,
                    startWorkTime:null,
                    remark:null,
                    endWorkTime:null,
                    imgs:null,
                },
                upExpModel:{
                    baomuID:null,
                    workContent:null,
                    startWorkTime:null,
                    remark:null,
                    endWorkTime:null,
                    imgs:null,
                },
                activeNames:[],
                tableData: [],
                dom:this.model,
                imgs:[
                ],
                formItem: {
                },
                fileList:[]
            }
        },
        components: {
        },
        created: function () {
            this.getData()
            console.log(this.dom)
        },
        methods: {
            showHandleByRoleId() {
                if (this.roleId == null) {
                    return false;
                }
                return this.roleId != 85;
            },
            insertImg(row){
                this.upexp=row;
                console.log(row)
            },
            removeByValue(arr, val) {
                for(var i = 0; i < arr.length; i++) {
                    if(arr[i] == val) {
                        arr.splice(i, 1);
                        break;
                    }
                }
            },
            removeImg(row,img){
                this.removeByValue(row.showimgs,img)
                row.imgs=row.showimgs.toString();
                this.update(row,false)

            },
            /* 函数功能：字符串按照指定字符串分割转换为数组
                参数:
                str :需转换的字符串
                substr:分割字符串
                返回值:
                转换后的数组
                 */
            StringToArray(str,substr) {

                var arrTmp = new Array();
                if(substr=="") {
                    arrTmp.push(str);
                    return arrTmp;
                }
                var i=0, j=0, k=str.length;
                while(i<k) {
                    j = str.indexOf(substr,i);
                    if(j!=-1) {
                        if(str.substring(i,j)!="") { arrTmp.push(str.substring(i,j)); }
                        i = j+1;
                    } else {
                        if(str.substring(i,k)!="") { arrTmp.push(str.substring(i,k)); }
                        i = k;
                    }
                }
                return arrTmp;
            },
            handleAvatarSuccess(res, file){
                if (this.upexp.imgs==null){
                    this.upexp.imgs=res.data;
                }else {
                    this.upexp.imgs+=","+res.data;
                }

               this.update(this.upexp,true);

            },

            handlePictureCardPreview(url) {
                this.dialogImageUrl = url;
                this.dialogVisible = true;
            },
            getData() {
                this.$postUrl("baomuExp_list", this.dom.id, this.formItem, {}).then(res => {
                    if (res.status == 200) {
                        res.data.forEach((item, index) => {
                          if (item.imgs==null){
                              item.showimgs=[]
                          }else {
                              item.showimgs=this.StringToArray(item.imgs,",")
                          }
                        });
                        this.tableData = res.data;


                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            upbaomuExp(item){
                this.upExpModel=item
                this.dialogTableVisible=true

            },
            delbaomuExp(id){
                this.$postUrl("del_baomuExp", id, null, {}).then(res => {
                    if (res.status == 200) {

                        this.$message.success("删除成功，" + res.msg);
                        this.getData()

                    } else {
                        this.$message.error("删除失败，" + res.msg);
                    }
                })
            },
            save(name) {
                this.expModel.baomuID=this.dom.id;
				if(this.expModel.startWorkTime==null ||this.expModel.endWorkTime==null) {
					this.$message.error('请完善工作开始时间和结束时间！')
					return
				}

                        this.$postData("add_baomuExp", this.expModel, {}).then(res => {
                            if (res.status == 200) {
                                this.$message.success('新增成功');
                                this.activeNames=[]
                                this.getData()
                            } else {
                                this.$message.error("新增失败，" + res.msg);
                            }
                        })
            },
            update(exp,up) {
                this.$postData("update_baomuExp", exp, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.success('更新成功');
                        this.dialogTableVisible=false
                    if (up){
                        this.getData()
                    }
                    } else {
                        this.$message.error("更新失败，" + res.msg);
                    }
                })
            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.dom=null;
                this.$emit('init-choose', "");
            },

        },

    }
</script>
<style>
    {
        background: #409eff;
        color: #fff;
    }
</style>

<style scoped>
    .label-name{
        float: left;
        text-align: center;
        width: 15%;
    }
    .ivu-col-span-9{
        padding-top: 30px;
    }
    .el-input-group{
        width: 80%;
    }
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

