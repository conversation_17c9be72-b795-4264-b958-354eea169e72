<template>
  <div>
    <Drawer
        title="详情"
        v-model="value3"
        width="50%"
        :mask-closable="false"
        @on-close="chooseThisModel"
        :styles="styles">
      <Form :model="formItem" :label-width="80" label-position="left">
        <div style="font-size: 16px; padding-left: 15px;
    font-weight: bold;">同步分类
        </div>
        <div class="searchDiv">
          <FormItem label="批量页数" prop="billNo">
            <Input placeholder="批量页数"
                   disabled
                   style="width: 80%"
                   v-model="pageNum">
            </Input>
          </FormItem>
          <FormItem label="每页获取数">
            <Input placeholder="每页获取数" style="width: 80%"
                   disabled
                   v-model="pageSize">
            </Input>
            <div>如果结果集的长度小于pageSize，不需要进行下一页查询</div>
          </FormItem>

          <FormItem label="" prop="productName">
            <Button type="primary" :loading="loading" @click="getCategory">发送请求</Button>
          </FormItem>
        </div>
      </Form>
      <Form :model="formItem" :label-width="80" label-position="left">
        <div style="font-size: 16px; padding-left: 15px;
    font-weight: bold;">同步商品
        </div>
        <div style="font-size: 16px; padding-left: 15px;">
          注：当分类发生变化时，请及时优先同步分类再同步商品，确保数据的准确性。
        </div>
        <div class="searchDiv">
          <FormItem label="商品分类">
            <Select filterable clearable style="width: 120px" v-model="categoryUid">
              <Option value="">请选择</Option>
              <Option v-for="item in categoryList" :value="item.uid" :key="item.id">{{ item.name }}</Option>
            </Select>
            <div>
              当不选择分类时，系统默认同步全分类商品（不建议）。
            </div>
          </FormItem>

          <FormItem label="批量页数" prop="billNo">
            <Input placeholder="批量页数"
                   style="width: 80%"
                   v-model="pageNum1">
            </Input>
          </FormItem>
          <FormItem label="每页获取数">
            <Input placeholder="每页获取数" style="width: 80%"
                   disabled
                   v-model="pageSize">
            </Input>
            <div>如果结果集的长度小于pageSize，不需要进行下一页查询</div>
          </FormItem>


          <FormItem label="" prop="productName">
            <Button type="primary" :loading="loading1" @click="getGoods">发送请求</Button>
          </FormItem>
        </div>
      </Form>
      <!--      <div class="demo-drawer-footer">-->
      <!--        <Button style="margin-right: 8px" @click="chooseThisModel">取消</Button>-->
      <!--        <Button type="primary" @click="update">确定</Button>-->
      <!--      </div>-->
    </Drawer>
  </div>
</template>
<script>
export default {
  data() {
    return {
      loading: false,
      loading1: false,
      formItem: {},
      pageNum: 1,
      pageNum1: 1,
      pageSize: 100,
      value3: true,
      styles: {
        height: 'calc(100% - 55px)',
        overflow: 'auto',
        paddingBottom: '53px',
        position: 'static'
      },
      categoryList: [],
      categoryUid: null
    }
  },
  created: function () {
    this.getCategoryList();
  },
  methods: {
    getGoods() {
      if (this.pageNum1 == null || this.pageNum1 === '') {
        this.$message.error("请输入批量页数");
        return
      }
      this.loading1 = true
      if (this.categoryUid != null && this.categoryUid !== '') {
        this.getGoodsByCategoryUid()
      } else {
        this.getGoodsAll();
      }
    },
    getGoodsAll() {
      this.$getData("queryProductPages", {pageNum: this.pageNum1}).then(res => {
        this.loading1 = false

        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '同步成功!'
          });
        }
      })
    },
    getGoodsByCategoryUid() {
      this.$getData("queryProductPages_categoryId", {
        pageNum: this.pageNum1,
        categoryId: this.categoryUid
      }).then(res => {
        this.loading1 = false

        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '同步成功!'
          });
        }
      })
    },
    getCategory() {
      this.loading = true
      this.$getData("queryProductCategoryPages").then(res => {
        this.loading = false

        if (res.code === 0) {
          this.getCategoryList();
          this.$message({
            type: 'success',
            message: '同步成功!'
          });
        }
      })
    },
    getCategoryList() {
      this.$getData("collectRecordList_selectCategoryAll").then(res => {
        if (res.status === 200) {
          this.categoryList = res.data;
        }
      })
    },
    chooseThisModel() {
      this.value3 = false;
      this.$emit('init-choose', "");
    },
    getTypeList() {
      this.$postUrl("get_dict", 130, null, {}).then(res => {
        if (res.status === 200) {
          this.typeList = res.data;
        }
      })
    }
  }
}
</script>
<style>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: left;
  background: #fff;
}

.searchDiv {
  width: 60%;
  margin: 20px auto;
  font-weight: bold;
  font-size: 17px !important;
}
</style>
