<template style="background-color: #000">
    <div>
      <el-alert
          title="如果要扣分那么数值是为负数，如果为正数表示加分"
          type="warning"
          effect="dark">
      </el-alert>
      <divider></divider>
        <el-row>
            <el-col :span="5">
                <div style="text-align: center;">
                    订单编号
                </div>
            </el-col>
            <el-col :span="2">
                <div style="text-align: center">
                    工号
                </div>
            </el-col>
            <el-col :span="5">
                <div style="text-align: center">
                    违纪罚金
                </div>
            </el-col>
            <el-col :span="5">
                <div style="text-align: center">
                    行政处罚
                </div>
            </el-col>
            <el-col :span="5">
                <div style="text-align: center">
                    行政扣分
                </div>
            </el-col>
        </el-row>

        <el-row v-for="(item, index) in data" :key="index" style="margin-top: 2rem">
            <el-col :span="5">
                <div style="text-align: center">
                    {{item.billNo}}
                </div>
            </el-col>
            <el-col :span="2">
                <div style="text-align: center">
                    {{item.employeeNo}}{{item.employee.realName}}
                </div>
            </el-col>
            <el-col :span="5">
                <div style="text-align: center">
                    <Select filterable @on-change="onFined(item)"
                            style="width: 130px" v-model="item.fine">
                        <Option value="">请选择</Option>
                        <Option v-for="(item1, indexs) in finedList" :key="indexs" :value="item1.id">{{ item1.text}}</Option>
                    </Select>
                </div>
            </el-col>
            <el-col :span="5">
                <div style="text-align: center">
                    <Select filterable @on-change="onAdminFine(item)"
                            style="width: 130px" v-model="item.adminFine">
                        <Option value="">请选择</Option>
                        <Option v-for="(item1, indexs) in adminFinedList" :key="indexs" :value="item1.id">{{ item1.text}}</Option>
                    </Select>
                </div>
            </el-col>
            <el-col :span="5">
                <div style="text-align: center">
                    <el-input-number style="width: 100px" v-model="item.scoreDetails.changeScore"
                                     @change="handleChange(item)"
                                     :min="-100" :max="100">
                    </el-input-number>
                </div>
            </el-col>
        </el-row>
    </div>
</template>


<script>
    export default {
        props: ['tsId'],
        data() {
            return {
                userModal: false,
                data: [],
                finedList: [],
                adminFinedList: [],
                evaluateList: []
            }
        },
        components: {},
        created: function () {
            this.getData();
            this.getFine();
            this.getAdminFineList();
            this.getEvaluateList();
        },
        methods: {
            getData() {
                this.$getData("getByTsId", {tsId: this.tsId}).then(res => {
                    if (res.status === 200) {
                       ;
                        this.data = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getFine() {
                this.$postUrl("get_dict", 117, null, {}).then(res => {
                    if (res.status === 200) {
                        this.finedList = res.data;
                    }
                })
            },
            getAdminFineList() {
                this.$postUrl("get_dict", 118, null, {}).then(res => {
                    if (res.status === 200) {
                        this.adminFinedList = res.data
                    }
                })
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.userModal = false;
            },
            handleClearCurrentRow() {
                this.$refs.currentRowTable.clearCurrentRow();
            },
            onChange(index) {
                console.log(index);
                this.employee.pageNum = index;
                this.getData();
            },
            onCurrentChange(currentRow, oldCurrentRow) {
                // console.log(currentRow.no);
                // this.$emit('init-choose', currentRow.no);

            },
            handleChange(item) {
                console.log(item);
                if (item.scoreId == null || item.scoreId === '') {
                    this.storeDetailsInsert(item)
                } else {
                    this.storeDetailsUpdate(item)
                }
            },
            onFined(item) {
                if (item.fundId == null) {
                    this.insetFund(item);
                } else {
                    this.updateFund(item);
                }
            },
            setChangeAmount(id) {
                for (let i = 0; i < this.finedList.length; i++) {
                    if (id === this.finedList[i].id) {
                        return this.finedList[i].text.replace(/[^0-9]/ig, "")
                    }
                }
            },
            insetFund(item) {
                let changeAmount = 0;
                changeAmount = this.setChangeAmount(item.fine);
                let fundDetails = {
                    employeeNo: item.employeeNo,
                    employeeFundDetailsType: 90,
                    changeAmount: -changeAmount,
                    billno: item.billNo,
                    productName: item.order.productName
                };
                console.log(fundDetails);
                this.$postData("fundDetails_insert", fundDetails).then(res => {
                    if (res.status === 200) {
                        item.fundId = res.data;
                        this.update(item)
                    }
                })
            },
            updateFund(item) {
                item.fund.changeAmount = -this.setChangeAmount(item.fine);
                this.$postData("fundDetails_update", item.fund).then(res => {
                })
            },

            onAdminFine(item) {
                console.log(item);
                this.update(item)
            },
            update(item) {
                this.$postData("emOrder_update", item, {}).then(res => {
                    if (res.status === 200) {
                        //this.$Message.success('保存成功');
                        this.getData()
                    } else {
                        this.$message.error("保存失败，" + res.msg);
                    }
                });
            },
            storeDetailsInsert(dto) {
                dto.scoreDetails.employeeNo = dto.employeeNo;
                dto.scoreDetails.scoreType = this.evaluateList[0].id;
                dto.scoreDetails.reamke = "有效投诉";
                dto.scoreDetails.billNo = dto.billNo;
                this.$postData("storeDetails_insert", dto.scoreDetails).then(res => {
                    if (res.status === 200) {
                        dto.scoreId = res.data;
                        this.update(dto)
                    }
                })
            },
            storeDetailsUpdate(dto) {
              dto.scoreDetails.billNo = dto.billNo;
                this.$postData("storeDetails_update", dto.scoreDetails).then(res => {
                })
            },
            getEvaluateList() {
                this.$postUrl("get_dict", 121, null, {}).then(res => {
                    if (res.status === 200) {
                       ;
                        this.evaluateList = res.data
                    }
                })
            },
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 150px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

