<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="9">
            <el-form-item label="结算时间">
              <el-date-picker v-model="days" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="门店名称">
              <el-input
                  clearable
                  v-model="form.storeName"
                  placeholder="请输入门店名称"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
            <el-button type="info"
                       style="margin-bottom: 15px"
                       @click="exportStoreWithdrawal"
                       icon="el-icon-download">导出
            </el-button>
          </el-col>
          <el-col :span="3">
            <el-button type="primary"
                       @click="storeBalanceFlag=true"
                       icon="el-icon-search">查询门店账户实时余额
            </el-button>
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 20px">
          <el-col :span="4">
            <div style="font-size: 15px;font-weight: bold;color: red">累计订单金额：￥{{totalOrderAmount}}</div>
          </el-col>
          <el-col :span="4">
            <div style="font-size: 15px;font-weight: bold;color: red">累计实付金额：￥{{totalPaidInAmount}}</div>
          </el-col>
          <el-col :span="4">
            <div style="font-size: 15px;font-weight: bold;color: red">累计平台费：￥{{totalManagementFee}}</div>
          </el-col>
          <el-col :span="4">
            <div style="font-size: 15px;font-weight: bold;color: red">累计扣减总额：￥{{totalDeduction}}</div>
          </el-col>
          <el-col :span="4">
            <div style="font-size: 15px;font-weight: bold;color: red">累计结算金额：￥{{totalSettleMoney}}</div>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="收入详情">
          <el-table :data="logList" v-loading="loading" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column prop="storeName" label="门店名称" width="180"></el-table-column>
            <el-table-column prop="paySettlementTime" label="结算时间" width="180"></el-table-column>
            <el-table-column prop="billNo" label="订单编号" width="160"></el-table-column>
            <el-table-column prop="sourceBillNo" label="集团单号" width="140"></el-table-column>
            <el-table-column prop="productName" label="服务项目" width="120"></el-table-column>
            <el-table-column prop="crePerson" label="开单人" width="180"></el-table-column>
            <el-table-column prop="channel" label="开发人" width="180"></el-table-column>
            <el-table-column prop="realTotalAmount" label="订单金额" width="80"></el-table-column>
            <el-table-column prop="paidInAmount" label="实付金额" width="80"></el-table-column>
            <el-table-column prop="clueDeduction" label="线索扣减" width="80"></el-table-column>
            <el-table-column prop="recruitDeduction" label="招工扣减" width="80"></el-table-column>
            <el-table-column prop="developDeduction" label="开发扣减" width="80"></el-table-column>
            <el-table-column prop="developRewardDeduction" label="开发奖扣减" width="80"></el-table-column>
            <el-table-column prop="clueRewardDeduction" label="线索奖扣减" width="80"></el-table-column>
            <el-table-column prop="recruitRewardDeduction" label="招工奖扣减" width="80"></el-table-column>
            <el-table-column prop="preferentialDeduction" label="系统优惠/优惠券扣减" width="80"></el-table-column>
            <el-table-column prop="managementFee" label="平台使用费" width="80"></el-table-column>
            <el-table-column prop="settleMoney" label="结算金额" width="80"></el-table-column>
            <el-table-column prop="remark" label="扣减说明" width="250"></el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-dialog
        title="查询门店账户实时余额："
        :visible.sync="storeBalanceFlag"
        width="80%">
      <el-form ref="form">
        <el-row>
          <el-col :span="4">
            <el-form-item  label="查询门店" >
              <el-select style="width:220px" filterable v-model="balanceStoreId" clearable placeholder="请选择查询门店">
                <el-option v-for="item in storeOptions"
                           :key="item.id" :label="item.storeName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3" style="margin-top: 30px;margin-left: 50px">
            <el-button type="primary"
                       style="margin-bottom: 15px"
                       @click="getStoreBalance"
                       icon="el-icon-search"
                       v-loading="selectLoading">查询
            </el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-row style="font-size: 25px;margin-bottom: 20px;margin-top: 20px;">
        <el-col :span="8" >
          <span>累计收入：￥{{storeAccount.accumulateIncome}}</span>
        </el-col>
        <el-col :span="8" >
          <span>累计提现：￥{{storeAccount.accumulateWithdrawal}}</span>
        </el-col>
        <el-col :span="8" >
          <span>账户余额：￥{{storeAccount.residueMoney}}</span>
        </el-col>
      </el-row>
    </el-dialog>
    <el-link :href="hrefUrl" target="_blank" id="elLink"/>


  </div>
</template>

<script>
import moment from "moment";
import log from "echarts/src/scale/Log";
export default {
  name: "register",
  data() {
    return {
      totalOrderAmount: 0.00,
      totalPaidInAmount: 0.00,
      totalManagementFee: 0.00,
      totalDeduction: 0.00,
      totalSettleMoney: 0.00,
      dialogCCB:false,
      selectLoading:false,
      ccbCallBackList:[],
      logList: [],
      imgModal: false,
      handler: [],
      txData: [],
      invoiceDialog: false,
      loading: true,
      storeBalanceFlag: false,
      orderList: [],
      imageUrl: '',
      roleId: localStorage.getItem('roleId'),
      hrefUrl: "",
      blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
      dialog: false,
      getOrderObj: {
        storeId: null,
        id: null,
      },
      storeId: null,
      withdrawalData: {},
      dialogVisible: false,
      innerVisible: false,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      days: [],
      queryDays: [],
      dcmxDays: [],
      dzDays: [],
      storeAccount:{
        accumulateIncome: 0.00,
        residueMoney: 0.00,
        accumulateWithdrawal: 0.00,
      },
      form: {
        state: '',
        startTime: '',
        endTime: '',
        dzStartTime: '',
        dzEndTime: '',
        storeName: '',
        roleId: localStorage.getItem('roleId'),
        accountPeople: '',
        cashier: '',
        billNo: '',
        serialNumber: '',
        id: '',
        voteType: '',
        current: 1,
        size: 10
      },
      dom: {
        state: null,
        operatorPeople: localStorage.getItem("id"),
        id: null,
        refusalReason: null,
        financeRemark: null,
        remark: null,
      },
      options: [],
      balanceStoreId: null,
      dcmxQuery: {
        storeId: null,
        startTime: '',
        endTime: '',
        dzStartTime: '',
        dzEndTime: '',
      },
      typeOptions: [{
        value:  '1',
        label: '含票'
      },{
        value:  '2',
        label: '无票'
      }],
      storeOptions: [],
      dzPickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },

  created() {
    this.days.push(moment(new Date()).startOf("month").format("YYYY-MM-DD"))
    this.days.push(moment(new Date()).endOf("month").format("YYYY-MM-DD"))
    this.getData();
    this.getAllFranchiseStore();
  },
  methods: {
    getAllFranchiseStore(){
      this.$getData("getAllFranchiseStore", {}, {}).then(res => {
        if (res.code == 0) {
          this.storeOptions = res.data
        } else {
          this.$message.error("查询筛选门店失败!");
        }
      })
    },
    getStoreBalance(){
      this.selectLoading = true
      this.$getData("getStoreBalance", { storeId: this.balanceStoreId}).then(res => {
        if(res.code==0){
            this.storeAccount = res.data
            this.selectLoading = false
        }else{
          this.selectLoading = false
          this.$message.error("查询门店账户实时余额失败，请稍后重试!");
        }
      })
    },
    exportPaymentOut(){
      if (this.dcmxDays != null && this.dcmxDays.length > 0) {
        this.dcmxQuery.startTime = moment(this.dcmxDays[0]).format("YYYY-MM-DD");
        this.dcmxQuery.endTime = moment(this.dcmxDays[1]).format("YYYY-MM-DD")
      }else{
        this.dcmxQuery.startTime = ""
        this.dcmxQuery.endTime = ""
      }
      if (this.dzDays != null && this.dzDays.length > 0) {
        this.dcmxQuery.dzStartTime = moment(this.dzDays[0]).format("YYYY-MM-DD");
        this.dcmxQuery.dzEndTime = moment(this.dzDays[1]).format("YYYY-MM-DD")
      }else{
        this.dcmxQuery.dzStartTime = ""
        this.dcmxQuery.dzEndTime = ""
      }
      let url = "https://biapi.xiaoyujia.com/storeWithdrawal/exportPaymentOut"
      if (this.dcmxQuery.storeId){
        url = url+"?storeId="+this.dcmxQuery.storeId
      }
      if (this.dcmxQuery.startTime&&!this.dcmxQuery.storeId){
        url = url+"?startTime="+this.dcmxQuery.startTime+"&endTime="+ this.dcmxQuery.endTime
      }else if(this.dcmxQuery.startTime&&this.dcmxQuery.storeId){
        url = url+"&startTime="+this.dcmxQuery.startTime+"&endTime="+ this.dcmxQuery.endTime
      }
      if (this.dcmxQuery.dzStartTime&&!this.dcmxQuery.storeId&&!this.dcmxQuery.startTime){
        url = url+"?dzStartTime="+this.dcmxQuery.dzStartTime+"&dzEndTime="+ this.dcmxQuery.dzEndTime
      }else if(this.dcmxQuery.dzStartTime&&(this.dcmxQuery.storeId||this.dcmxQuery.startTime)){
        url = url+"&dzStartTime="+this.dcmxQuery.dzStartTime+"&dzEndTime="+ this.dcmxQuery.dzEndTime
      }
      this.hrefUrl = url
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    selectInvoice(item){
      this.txData = item
      this.invoiceDialog = true
    },
    queryccbcallBack() {
      this.$getData("queryccbcallBack", {id:this.getOrderObj.id}, {}).then(res => {
        if (res.code == 0) {
          this.selectCCBList(this.getOrderObj.id)
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    },
    exportStoreWithdrawal(){
      this.form.startTime = '';
      this.form.endTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      this.hrefUrl = "https://biapi.xiaoyujia.com/aiRun/exportAllStoreBill?storeName="+this.form.storeName+
          "&startTime="+this.form.startTime+"&endTime="+this.form.endTime
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    showRow(id, state) {
      this.dialogVisible = true;
      this.dom.state = state;
      this.dom.id = id;
    },
    // 页码大小
    onPageSizeChange(size) {
      this.form.size = size;
      this.getData();
    },
    onChange(index) {
      this.form.current = index;
      this.getData();
    },
    getData() {
      this.loading = true;
      this.form.startTime = '';
      this.form.endTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      this.$getData("getStoreIncomeBill", this.form, {}).then(res => {
        this.loading = false
        if (res.code == 0) {
          this.logList = res.data.pageResult.records;
          this.totalOrderAmount = res.data.totalOrderAmount;
          this.totalPaidInAmount = res.data.totalPaidInAmount;
          this.totalManagementFee = res.data.totalManagementFee;
          this.totalDeduction = res.data.totalDeduction;
          this.totalSettleMoney = res.data.totalSettleMoney;
          this.pageInfo.current = res.data.pageResult.current;
          this.pageInfo.size = res.data.pageResult.size;
          this.pageInfo.total = res.data.pageResult.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    }
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
