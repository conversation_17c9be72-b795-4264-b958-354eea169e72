<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col span="24">
                    <div>
                        <div id="operBtnDiv">
                            <div id="searchDiv">
                                <Form ref="formItem" :model="formItem" :label-width="80" label-position="left"
                                      :inline="true"  :rules="ruleValidate">
                                    <FormItem label="主标题" prop="firstTitle">
                                        <Input placeholder="请输入主标题" v-model="formItem.firstTitle">
                                        </Input>
                                    </FormItem>

                                    <FormItem label="副标题" prop="secondTitle">
                                        <Input placeholder="请输入副标题" v-model="formItem.secondTitle">
                                        </Input>
                                    </FormItem>

                                    <FormItem label="使用数量" prop="number">
                                        <Input placeholder="请输入使用数量" v-model="formItem.number">
                                        </Input>
                                    </FormItem>

                                    <FormItem label="金额" prop="amount">
                                        <Input placeholder="请输入金额" v-model="formItem.amount">
                                        </Input>
                                    </FormItem>
                                    <FormItem label="类型" prop="type"  :inline="true">
                                        <Select v-model="formItem.type" filterable>
                                            <Option v-for="item in typeList" :value="item.value"
                                                    :key="item.value">{{ item.label }}
                                            </Option>
                                        </Select>
                                    </FormItem>

                                    <FormItem label="状态" prop="status" >
                                        <Select v-model="formItem.status" filterable>
                                            <Option v-for="item in statusList" :value="item.value"
                                                    :key="item.value">{{ item.label }}
                                            </Option>
                                        </Select>
                                    </FormItem>
                                    <FormItem label="图片" prop="icon" >
                                        <el-upload
                                                :action="testUrl"
                                                list-type="picture-card"
                                                :limit="1"
                                                :on-success="onSuccess"
                                                :on-preview="handlePictureCardPreview"
                                                :on-remove="handleRemove"
                                                :on-exceed="onExcees"
                                                :file-list="fileList"
                                                :data="files">
                                            <i class="el-icon-plus"></i>
                                        </el-upload>
                                        <el-dialog :visible.sync="dialogVisible">
                                            <img width="100%" :src="dialogImageUrl" alt="">
                                        </el-dialog>
                                    </FormItem>

                                    <div style="margin-left: 80px">
                                        <Button type="primary" @click="update('formItem')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </div>
                                </Form>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
    </div>
</template>


<script>

    export default {
        props:['productId'],
        data() {
            return {
                imgUrl:"https://biapi.xiaoyujia.com/files/uploadFiles",
                testUrl:"http://localhost:8063/files/uploadFiles",
                typeList: [
                    {
                        value: '1',
                        label: '试吃'
                    },
                    {
                        value: '2',
                        label: '试用'
                    },
                ],
                statusList: [
                    {
                        value: '1',
                        label: '上架'
                    },
                    {
                        value: '2',
                        label: '下架'
                    },
                ],
                formItem: {
                    id:this.productId,
                    firstTitle: null,
                    secondTitle: null,
                    number: null,
                    amount: null,
                    type: null,
                    status:null,
                    icon:null,
                },
                files:{
                    fileName:"TryProduct/Banner/"
                },
                dialogImageUrl: '',
                dialogVisible: false,
                ruleValidate: {
                    firstTitle: [
                        {required: true, message: '请输入主标题', trigger: 'change'}
                    ],
                    icon: [
                        {required: true, message: '请选择图片', trigger: 'change'}
                    ],
                    type: [
                        {required: true, message: '请选择类型', trigger: 'change'}
                    ],
                    status: [
                        {required: true, message: '请选择状态', trigger: 'change'}
                    ],
                    secondTitle: [
                        {required: true, message: '请输入副标题', trigger: 'blur'}
                    ],
                    amount: [
                        {required: true, message: '请输入金额'},
                        {required: true, pattern: /(^[\-0-9][0-9]*(.[0-9]+)?)$/, message: '请输入数字', trigger: 'change'}
                    ],
                    number: [
                        {required: true, message: '请输入金额'},
                        {required: true, pattern: /(^[\-0-9][0-9]*(.[0-9]+)?)$/, message: '请输入数字', trigger: 'change'}
                    ],

                },
                fileList:[{
                    name: '',
                    url:''
                }]
            }
        },
        components: {
        },
        created (){
            this.getData();
        },
        methods: {
            getData() {
                let products={
                    id:this.productId
                }
                this.$postData("product_getById", products, {}).then(res => {
                    if (res.status == 200) {

                        this.formItem = res.data;
                        this.formItem.type=""+res.data.type
                        this.formItem.status=""+res.data.status
                        this.fileList[0].url=res.data.icon
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },

            update(name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        this.$postData("product_updateProduct", this.formItem, {}).then(res => {
                            if (res.status == 200) {
                                this.$Message.success('修改成功');
                                this.chooseThisModel()
                            } else {
                                this.$message.error("修改失败，" + res.msg);
                            }
                        })
                    }
                })
            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            },
            handleRemove(file, fileList) {
                console.log(file, fileList);
                this.formItem.icon=null
            },
            handlePictureCardPreview(file) {
                this.dialogImageUrl = file.url;
                this.dialogVisible = true;
                console.log(file);
            },
            onExcees(){
                this.$Message.success('只可上传一张图片');
            },
            onSuccess(res, file, fileList){
               ;
                if(res.status==200){
                    this.$Message.success('上传成功');
                    this.formItem.icon=res.data
                }
            }
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

