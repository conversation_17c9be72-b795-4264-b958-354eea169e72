<template>
    <div class="table">
        <el-tabs type="border-card">
            <el-tab-pane label="中介订单">
                <div>
                    <div class="container">
                        <div class="handle-box">
                            <el-form ref="form">

                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="订单编号">
                                            <el-input
                                                    v-model="model.billNo"
                                                    placeholder="订单编号"
                                                    style="width:190px"
                                                    class="handle-input mr10"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="订单服务">
                                            <el-input
                                                    v-model="model.productName"
                                                    placeholder="订单服务"
                                                    style="width:190px"
                                                    class="handle-input mr10"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="经纪人">
                                            <el-input
                                                    v-model="model.agentName"
                                                    placeholder="经纪人"
                                                    style="width:200px"
                                                    class="handle-input mr10"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="客户账号">
                                            <el-input
                                                    v-model="model.account"
                                                    placeholder="客户账号"
                                                    style="width:200px"
                                                    class="handle-input mr10"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="客户手机号">
                                            <el-input
                                                    v-model="model.bindTel"
                                                    placeholder="客户手机号"
                                                    style="width:200px"
                                                    class="handle-input mr10"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item prop="isPayoff" label="结算状态">

                                            <el-select v-model="model.isPayoff" clearable placeholder="请选择">
                                                <el-option
                                                        v-for="item in isPayoffoptions"
                                                        :key="item.value"
                                                        :label="item.label"
                                                        :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>

                                    </el-col>
                                  <el-col :span="8">
                                    <el-form-item label="服务时间">
                                      <el-date-picker
                                          v-model="serviceDate"
                                          type="daterange"
                                          style="width: 250px"
                                          range-separator="~"
                                          start-placeholder="开始日期"
                                          end-placeholder="结束日期"
                                          value-format="yyyy-MM-dd"
                                          align="right">
                                      </el-date-picker>&nbsp;
                                    </el-form-item>

                                  </el-col>

                                    <el-col :span="8">

                                        <el-form-item>
                                            <el-button type="success" round @click="query()">搜索</el-button>
                                            <el-button type="info" round @click="re()">重置</el-button>
                                            <!--                                <el-button type="success" round  @click="addModal=true">添加</el-button>-->
                                        </el-form-item>

                                    </el-col>
                                </el-row>

                            </el-form>
                        </div>
                        <el-table :data="list" :header-cell-style="{background:'#ddd'}" @sort-change="sortChange" border
                                  class="table" ref="multipleTable" @cell-dblclick="edit" v-loading="loading">

                            <el-table-column
                                    type="selection"
                                    width="40">
                            </el-table-column>
                            <el-table-column
                                    fixed="left"
                                    prop="billNo"
                                    label="订单编号"
                                    width="180"
                            ></el-table-column>
                            <el-table-column

                                    prop="channel"
                                    label="经纪人编号"
                                    width="120"
                                    sortable="custom"
                            ></el-table-column>
                            <el-table-column
                                    prop="realName"
                                    label="保姆"
                                    width="120"
                                    sortable="custom"
                            ></el-table-column>
                            <!--                <el-table-column-->
                            <!--                        prop="name"-->
                            <!--                        label="会员名"-->
                            <!--                        width="120"-->
                            <!--                        sortable="custom"-->
                            <!--                ></el-table-column>-->
                            <el-table-column
                                    prop="account"
                                    label="会员信息"
                                    width="300"
                                    sortable="custom"
                            >
                                <template slot-scope="scope">
                                    <span @click="toMemberByMemberId(scope.row.memberId)">{{scope.row.name}}</span>
                                    <el-link type="primary" @click="toMember(scope.row.bindTel)">{{scope.row.bindTel}}
                                    </el-link>

                                    <el-tag type="success" v-show="scope.row.lable!==null">{{scope.row.lable}}</el-tag>
                                    <el-tag type="info" v-show="scope.row.lable==null">无标签</el-tag>
                                    <el-popover
                                            placement="top-start"
                                            width="200"
                                            trigger="hover"
                                            :content="scope.row.remark==null?'无备注信息':scope.row.remark">
                                        <el-button slot="reference" v-show="scope.row.remark!=null">
                                            {{scope.row.remark==null?null:((scope.row.remark).substring(0,5)+'...')}}
                                        </el-button>
                                    </el-popover>
                                </template>
                            </el-table-column>


                            <!--                <el-table-column-->
                            <!--                        prop="totalAmount"-->
                            <!--                        label="订单金额"-->
                            <!--                        width="120"-->
                            <!--                        sortable="custom"-->
                            <!--                ></el-table-column>-->
                            <el-table-column
                                    prop="splitTime"
                                    label="拆单状态"
                                    width="120"
                                    sortable="custom">
                                <template slot-scope="scope">
                                    <el-tag v-if="scope.row.splitTime!=null" type="success">已拆单</el-tag>
                                    <el-tag v-else-if="scope.row.splitTime==null" type="warning">未拆单</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    prop="state"
                                    label="是否欠款"
                                    width="120"
                                    sortable="custom">
                                <template slot-scope="scope">
                                    <el-tag v-if="scope.row.realTotalAmount==scope.row.amount" type="success">否</el-tag>
                                    <el-tag v-else-if="scope.row.realTotalAmount!=scope.row.amount" type="warning">是
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    prop="amount"
                                    label="付款金额"
                                    width="120"
                                    sortable="custom"
                            ></el-table-column>

                            <!--                <el-table-column-->
                            <!--                        prop="realTotalAmount"-->
                            <!--                        label="最终金额"-->
                            <!--                        width="120"-->
                            <!--                        sortable="custom"-->
                            <!--                ></el-table-column>-->

                            <el-table-column
                                    prop="productName"
                                    label="订单服务"
                                    width="120"
                                    sortable="custom"
                            ></el-table-column>
                            <el-table-column
                                    prop="isPayoff"
                                    label="是否已结算"
                                    width="120"
                                    sortable="custom">
                                <template slot-scope="scope">
                                    <el-tag v-if="scope.row.isPayoff==true" type="success">已结算</el-tag>
                                    <el-tag v-else-if="scope.row.isPayoff==false" type="warning">未结算</el-tag>
                                </template>
                            </el-table-column>


                            <!--                <el-table-column-->
                            <!--                        prop="workRemark"-->
                            <!--                        label="工作记录"-->
                            <!--                        width="150"-->
                            <!--                >-->
                            <!--                </el-table-column>-->

                            <!--                <el-table-column-->
                            <!--                        prop="skuFixedTime"-->
                            <!--                        label="下单时间"-->
                            <!--                        width="180"-->
                            <!--                        sortable="custom"-->
                            <!--                ></el-table-column>-->
                            <el-table-column
                                    prop="startTime"
                                    label="服务时间"
                                    width="180"
                                    sortable="custom"
                            ></el-table-column>
                            <el-table-column
                                    sortable="custom"
                                    prop="street"
                                    label="服务地点"
                                    min-width="120"
                                    show-overflow-tooltip
                            ></el-table-column>
                            <el-table-column
                                    sortable="custom"
                                    prop="remark"
                                    label="备注"
                                    show-overflow-tooltip
                            ></el-table-column>
                            <el-table-column
                                    label="操作"
                                    fixed="right"
                                    min-width="80">
                                <template slot-scope="scope">
                                    <!--                        <el-button size="mini" @click="edit(scope.row)" type="primary">编辑</el-button>-->
                                    <el-button size="mini" @click="todetail(scope.row.billNo)" type="primary">详情
                                    </el-button>
                                    <!--                        <el-button size="mini" @click="scope.row.isPayoff=true,save(scope.row)" type="primary" v-if="scope.row.isPayoff==false">结算</el-button>-->
                                    <!--                        <el-button size="mini" type="primary" v-if="scope.row.isPayoff==false">结算</el-button>-->
                                    <!--                        <el-button size="mini" @click="editCon(scope.row)" type="primary">生成合同</el-button>-->
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="pagination">
                            <Page :total="pageInfo.total"
                                  @on-change="onChange"
                                  :show-total="true"
                                  :show-sizer="true"
                                  :page-size-opts="pageSizeOpts"
                                  @on-page-size-change="onPageSizeChange"
                                  :page-size="pageInfo.size"/>
                        </div>
                    </div>

                    <Modal v-model="saveModal" class="Modal" :width="screenWidth" title="订单详情" :mask-closable="false">
                        <div class="addBody">

                            <orderUpdate v-if="saveModal" @init-choose="initChooseProject"
                                         @close-modal="closeCurrModal" :model="show"></orderUpdate>
                        </div>
                        <div slot="footer">
                        </div>
                    </Modal>
                    <Modal v-model="addModal" class="Modal" :width="screenWidth" title="添加订单" :mask-closable="false">
                        <div class="addBody" style="min-height:400px">
                            <orderAdd v-if="addModal" @init-choose="initChooseProject"
                                      @close-modal="closeCurrModal"></orderAdd>
                        </div>
                        <div slot="footer">
                        </div>
                    </Modal>

                    <Modal v-model="addConModal" class="Modal" :width="screenWidth" title="添加合同" :mask-closable="false">
                        <div class="addBody">
                            <ordercontractAdd v-if="addConModal" @init-choose="initChooseProject"
                                              @close-modal="closeCurrModal" :model="show"></ordercontractAdd>
                        </div>
                        <div slot="footer">
                        </div>
                    </Modal>
                </div>
            </el-tab-pane>
            <el-tab-pane label="ABC单">
                <agentAbcOrder></agentAbcOrder>
            </el-tab-pane>
            <el-tab-pane label="交付订单">
                <agentAbcCCOrder></agentAbcCCOrder>
            </el-tab-pane>

        </el-tabs>


    </div>
</template>

<script>
    import orderAdd from '@/components/page/agent/choose/orderAdd.vue'
    import orderUpdate from '@/components/page/agent/choose/orderUpdate.vue'
    import ordercontractAdd from '@/components/page/agent/choose/ordercontractAdd.vue'
    import {formatDate} from '@/components/common/utils.js'
    import agentAbcOrder from "./agentAbcOrder";
    import agentAbcCCOrder from "./agentAbcCCOrder";

    export default {
        data() {
            return {
                isPayoffoptions: [{
                    value: null,
                    label: '全部'
                }, {
                    value: false,
                    label: '未结算'
                }, {
                    value: true,
                    label: '已结算'
                },],
                loading: true,
                tableData: [],
                pageSizeOpts: [10, 20, 30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                screenWidth: '80%',//新增对话框 宽度
                saveModal: false,
                addModal: false,
                addConModal: false,
                show: {},
              serviceDate: null,
                model: {
                  serviceStartDate:null,
                  serviceEndDate:null,
                    bindTel: null,
                    account: null,
                    storeId: Number(localStorage.getItem("storeId")),
                    isPayoff: null,
                    productName: null,
                    agentName: null,
                    current: 1,
                    size: 10,
                    billNo: null,
                    orderBy: 'o.CreateTime DESC'
                },
                update: {
                    id: null,
                    realName: null,

                },
                list: null
            };
        },
        components: {
            'agentAbcOrder': agentAbcOrder,
            'orderAdd': orderAdd,
            'orderUpdate': orderUpdate,
            'ordercontractAdd': ordercontractAdd,
            'agentAbcCCOrder': agentAbcCCOrder
        },
        created() {
            this.getData();
        },
        computed: {},
        methods: {
            toMemberByMemberId(phone) {
                this.$router.push({path: '/memberInfo', query: {"id": phone}})
            },
            toMember(phone) {
                this.$router.push({path: '/agentMemberByPhone', query: {"bindTel": phone}})
            },
            todetail(billNo) {
                let url = window.location.href;
                window.open(url.substring(0, url.length - 10) + 'orderInfo?billNo=' + billNo)
                //this.$router.push({path: 'orderInfo', query: {billNo: billNo}})
            },
            save(dom) {

                this.$postData("update_order", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.$Message.success('修改成功');
                        this.getData();
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })
            },
            sortChange: function (column, prop, order) {

                if (column == null) {
                    this.model.orderBy = "";
                } else {
                    if (column.order == "ascending") {
                        this.model.orderBy = column.prop + " ASC";
                    }
                    if (column.order == "descending") {
                        this.model.orderBy = column.prop + " DESC";
                    }
                }
                this.getData();
            },
            getData() {
                if (localStorage.getItem("roleId") == 42) {
                    this.model.storeId = null;
                }
                this.loading = true;
                this.$postData("order_page", this.model, {}).then(res => {
                    if (res.status == 200) {
                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total;
                        this.loading = false;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })

            },
            query() {
              if (this.serviceDate) {
                this.model.serviceStartDate = this.serviceDate[0];
                this.model.serviceEndDate = this.serviceDate[1];
              }
                this.getData();
            },
            re() {
                this.model.accont = null;
                this.model.bindTel = null;
                this.model.isPayoff = null;
                this.model.productName = null;
                this.model.agentName = null;
                this.model.billNo = null;
                this.model.serviceStartDate = null;
                this.model.serviceEndDate = null;
                this.model.settlementStartDate = null;
                this.model.serviceStartDate = null;
                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                // console.log(size)
                this.model.size = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                // console.log(index)
                this.model.current = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
                this.updateModal = false;
                this.addModal = false;
                this.addConModal = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData();
            },
            edit(id) {
                this.show = id;
                // console.log(this.model)
                this.saveModal = true;
            },
            editCon(id) {
                this.show = id;
                this.addConModal = true;
            }
        }
    };
</script>

<style scoped>

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
