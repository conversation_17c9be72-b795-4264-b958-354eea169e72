<template style="background-color: #000">

            <el-tabs type="card" :stretch="true" >
                <el-dialog title="保姆技能编辑" :visible.sync="dialogTableVisible">
                    <el-form ref="upform" :model="upExpModel" style="padding-bottom: 50px">

                        <el-form-item   prop="groupSkill">
                            <div class="label-name">技能分组:</div>
                            <el-input placeholder="请输入备注" v-model="upExpModel.typeText" :disabled="true" style="width: 70%">
                            </el-input>
                        </el-form-item>
                        <el-form-item   prop="groupSkill">
                            <div class="label-name">二级分组:</div>
                            <el-input placeholder="请输入备注" v-model="upExpModel.text" :disabled="true" style="width: 70%">
                            </el-input>
                        </el-form-item>
                        <el-form-item   prop="workContent" v-show="upExpModel.text!='烹饪技能' ">
                            <div class="label-name">技能说明:</div>
                            <el-input placeholder="请输入技能说明" v-model="upExpModel.content" style="width: 70%">
                            </el-input>
                        </el-form-item >
                        <el-form-item   prop="baomuWorkType" v-show="upExpModel.text=='烹饪技能' ">
                            <div class="label-name">技能说明:</div>
                            <el-select v-model="upContents" multiple placeholder="请选择" style="width: 70%">
                                <el-option
                                        v-for="item in zfs"
                                        :key="item"
                                        :label="item"
                                        :value="item">
                                </el-option>
                            </el-select>
                        </el-form-item >
                        <!--                                    <el-form-item   prop="groupSkill">-->
                        <!--                                        <div class="label-name">技能分组:</div>-->
                        <!--                                        <el-input placeholder="请输入技能说明" v-model="expModel.groupSkill" style="width: 70%">-->
                        <!--                                        </el-input>-->
                        <!--                                    </el-form-item >-->

                        <el-form-item   prop="remark">
                            <div class="label-name">备注:</div>
                            <el-input placeholder="请输入备注" v-model="upExpModel.remark" style="width: 70%">
                            </el-input>
                        </el-form-item >




                        <div style="margin-right: 100px;float: right">
                            <Button v-if="showHandleByRoleId()" type="primary" @click="update(upExpModel,upExpModel.status)">确定</Button>
                        </div>
                    </el-form>
                </el-dialog>
                <el-collapse v-model="activeNames" >
                    <el-collapse-item title="新增工作经验" name="1">
                        <template slot="title">
                            <el-button type="primary" >添加保姆技能</el-button>
                        </template>
                        <el-form ref="form" :model="expModel">

                            <el-form-item   prop="groupSkill">
                                <div class="label-name">技能分组:</div>

                                <el-select v-model="expModel.group" placeholder="请选择" style="width: 70%" @change="changeSkills2">
                                    <el-option
                                            v-for="item in groupSkills"
                                            :key="item.option"
                                            :label="item.typeText"
                                            :value="item.option">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item   prop="groupSkill">
                                <div class="label-name">二级分组:</div>
                                <el-select v-model="expModel.groupSkill" placeholder="请选择" style="width: 70%">
                                    <el-option
                                            v-for="item in skills2"
                                            :key="item.id"
                                            :label="item.text"
                                            :value="item.id">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                                    <el-form-item   prop="workContent" v-show="expModel.groupSkill!=134 && expModel.groupSkill!=142">
                                        <div class="label-name">技能说明:</div>
                                        <el-input placeholder="请输入技能说明" v-model="expModel.content" style="width: 70%">
                                        </el-input>
                                    </el-form-item >

                                    <el-form-item   prop="baomuWorkType" v-show="expModel.groupSkill==134 ">
                                        <div class="label-name">技能说明:</div>
                                        <el-select v-model="contents" multiple placeholder="请选择" style="width: 70%">
                                            <el-option
                                                    v-for="item in zfs"
                                                    :key="item"
                                                    :label="item"
                                                    :value="item">
                                            </el-option>
                                        </el-select>
                                    </el-form-item >
                                    <el-form-item   prop="baomuWorkType" v-show="expModel.groupSkill==142 ">
                                        <div class="label-name">技能说明:</div>
                                        <el-select v-model="contents" multiple placeholder="请选择" style="width: 70%">
                                            <el-option
                                                    v-for="item in qts"
                                                    :key="item"
                                                    :label="item"
                                                    :value="item">
                                            </el-option>
                                        </el-select>
                                    </el-form-item >
<!--                                    <el-form-item   prop="groupSkill">-->
<!--                                        <div class="label-name">技能分组:</div>-->
<!--                                        <el-input placeholder="请输入技能说明" v-model="expModel.groupSkill" style="width: 70%">-->
<!--                                        </el-input>-->
<!--                                    </el-form-item >-->

                                    <el-form-item   prop="remark">
                                        <div class="label-name">备注:</div>
                                        <el-input placeholder="请输入备注" v-model="expModel.remark" style="width: 70%">
                                        </el-input>
                                    </el-form-item >




                            <div style="margin-right: 100px;float: right">
                                <Button v-if="showHandleByRoleId()" type="primary" @click="save('form')">确定</Button>
                            </div>
                        </el-form>
                    </el-collapse-item>
                </el-collapse>
                <div style="    font-size: 17px;font-weight: 600; color: #999;padding-left: 10px;border-left: thick double #409eff;padding-top: 20px">已有工作经验列表
                    <el-button size="mini" @click="getUrl" type="primary" style="float: right;margin-right: 50px">获取Url</el-button>
                </div>
                <el-table
                        @selection-change="handleSelectionChange"
                        :data="tableData"
                        style="width: 100%">
                    <el-table-column
                            type="selection"
                            width="35">
                    </el-table-column>
                    <el-table-column
                            prop="content"
                            label="技能说明"
                            width="180">
                    </el-table-column>
                    <el-table-column
                            prop="typeText"
                            label="分组">
                    </el-table-column>
                    <el-table-column
                            prop="text"
                            label="二级分组">
                    </el-table-column>
                    <el-table-column
                            prop="remark"
                            label="备注">
                    </el-table-column>
                    <el-table-column
                            prop="state"
                            label="状态"
                            width="120">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.status==1" type="success">展示</el-tag>
                            <el-tag v-if="showHandleByRoleId()" v-else-if="scope.row.status==0" type="warning">暂存</el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column
                            label="操作"
                            fixed="right"
                            min-width="180">
                        <template slot-scope="scope">
                            <el-button v-if="showHandleByRoleId()" size="mini" @click="delbaomuExp(scope.row.id)" type="info">删除</el-button>
                            <el-button v-if="showHandleByRoleId()" size="mini" @click="upbaomuExp(scope.row)" type="primary">编辑</el-button>
                            <el-button v-if="showHandleByRoleId() && scope.row.status==1" size="mini" @click="update(scope.row,0)" type="primary">设为暂存</el-button>
                            <el-button v-if="showHandleByRoleId() && scope.row.status==0" size="mini" @click="update(scope.row,1)" type="info">设为展示</el-button>
                        </template>

                    </el-table-column>
                </el-table>
                <el-dialog
                        title="Url地址"
                        :visible.sync="dialogVisible"
                        :modal="false"
                        width="50%"
                        >

<!--                    <el-input placeholder="请输入技能说明" v-model="url"  id="urlinput">-->
<!--                    </el-input>-->
                    <article id="article">
                        {{url}}
                    </article>
                    <span slot="footer" class="dialog-footer">
                        <el-button @click="dialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="dialogVisible = false,copyurl()">复制Url</el-button>
                      </span>
                </el-dialog>
            </el-tabs>

</template>


<script>

    export default {
        props:['model'],
        data() {
            return {
                roleId: localStorage.getItem("roleId"),
                contents:[],
                upContents:[],
                groupSkills:[],
                skills2:[],
                skills:[],
                zfs:[],
                qts:[],
                url:null,
                dialogVisible:false,
                dialogTableVisible:false,
                multipleSelection: [],
                expModel:{
                    baomuID:null,
                    content:null,
                    remark:null,
                    status:1
                },
                upExpModel:{
                    baomuID:null,
                    content:null,
                    remark:null,
                    status:null
                },
                activeNames:[],
                tableData: [],
                dom:this.model,
                formItem: {

                },


            }
        },
        components: {
        },
        created: function () {
            this.getData()
            this.getDict()
            console.log(this.dom)
        },
        methods: {
            showHandleByRoleId() {
                if (this.roleId == null) {
                    return false;
                }
                return this.roleId != 85;
            },
            changeSkills2(){
                this.skills2=[];
                    this.skills.forEach(v=>{
                        if (v.option==this.expModel.group){
                            this.skills2.push(v)
                        }
                    })
            },
            copyArticle(event){
                const range = document.createRange();
                range.selectNode(document.getElementById('article'));
                const selection = window.getSelection();
                if(selection.rangeCount > 0) selection.removeAllRanges();
                selection.addRange(range);
                document.execCommand('copy');
            },
            copyurl(){
                this.copyArticle()
                return this.$message.success("复制成功。");
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            getUrl(){
                if (this.multipleSelection[0]==null){
                    return this.$message.error("请勾选分享技能（可多选）。");
                }
                var skills=null;
                this.multipleSelection.forEach((item, index, arr) => {
                    if (index==0){
                        skills=item.id
                    }else {
                        skills +=","+item.id
                    }
                });
                this.url="https://agent.xiaoyujia.com/nannyInfo/"+this.dom.id+"/"+localStorage.getItem("id")+ "?skills="+skills
                    this.dialogVisible=true
                    console.log(localStorage)
                    console.log(this.multipleSelection)

            },

            update(dom,state) {
                if (dom.text=='烹饪技能'){
                    dom.content=null
                    if (this.upContents.length>0){
                        this.upContents.forEach(v=>{
                            if ( dom.content==null){
                                dom.content=v
                            }else {
                                dom.content=dom.content+","+v
                            }
                        });
                    }
                }
                dom.status=state;
                this.$postData("update_baomuSkill", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.getData()
                        this.upContents=[]
                        this.dialogTableVisible=false
                        this.$Message.success('修改成功');
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })

            },
            getData() {
                this.$postUrl("baomuSkill_list", this.dom.id, this.formItem, {}).then(res => {
                    if (res.status == 200) {

                        this.tableData = res.data;

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getDict() {
                this.$postUrl("get_dict", 29, null, {}).then(res => {
                    if (res.status == 200) {

                        this.skills=res.data

                    } else {
                        this.$message.error("查询失败，" + res.msg);s
                    }
                })
                this.$postUrl("get_dictText", 29, null, {}).then(res => {
                    if (res.status == 200) {
                        this.groupSkills=res.data
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                this.$postUrl("get_dict",24, null, {}).then(res => {
                    if (res.status == 200) {
                        res.data.forEach(v=>{
                            this.zfs.push(v.text)
                        })
                        console.log(this.zfs)

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                this.$postUrl("get_dict",25, null, {}).then(res => {
                    if (res.status == 200) {
                        res.data.forEach(v=>{
                            this.qts.push(v.text)
                        })
                        console.log(this.qts)

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            upbaomuExp(item){
                this.upExpModel=item
                if (item.content!=null && item.text=='烹饪技能'){
                    this.upContents=this.StringToArray(item.content,',')
                }
                this.dialogTableVisible=true
            },
            delbaomuExp(id){
                this.$postUrl("del_baomuSkill", id, null, {}).then(res => {
                    if (res.status == 200) {

                        this.getData()
                        this.$message.success("删除成功，" + res.msg);


                    } else {
                        this.$message.error("删除失败，" + res.msg);
                    }
                })
            },
            save(name) {

                if (this.contents.length>0 && this.expModel.groupSkill==134){
                    this.expModel.content=null
                    this.contents.forEach(v=>{
                        if ( this.expModel.content==null){
                            this.expModel.content=v
                        }else {
                            this.expModel.content=this.expModel.content+","+v
                        }
                    });
                }
                if (this.contents.length>0 && this.expModel.groupSkill==142){
                    this.expModel.content=null
                    this.contents.forEach(v=>{
                        if ( this.expModel.content==null){
                            this.expModel.content=v
                        }else {
                            this.expModel.content=this.expModel.content+","+v
                        }
                    });
                }
                this.expModel.baomuID=this.dom.id;

                if(this.expModel.groupSkill==null){
                    this.$message.error("请选择技能分组与二级分组" );
                }else {
                    this.$postData("add_baomuExpbaomuSkill", this.expModel, {}).then(res => {
                        if (res.status == 200) {
                            this.$Message.success('新增成功');
                            this.activeNames=[]
                            this.contents=[]
                            this.expModel.content=null
                            this.getData()
                        } else {
                            this.$message.error("新增失败，" + res.msg);
                        }
                    })
                }



            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.dom=null;
                this.$emit('init-choose', "");
            },
            StringToArray(str,substr) {

                var arrTmp = new Array();
                if(substr=="") {
                    arrTmp.push(str);
                    return arrTmp;
                }
                var i=0, j=0, k=str.length;
                while(i<k) {
                    j = str.indexOf(substr,i);
                    if(j!=-1) {
                        if(str.substring(i,j)!="") { arrTmp.push(str.substring(i,j)); }
                        i = j+1;
                    } else {
                        if(str.substring(i,k)!="") { arrTmp.push(str.substring(i,k)); }
                        i = k;
                    }
                }
                return arrTmp;
            },

        },

    }
</script>
<style>
    {
        background: #409eff;
        color: #fff;
    }
</style>

<style scoped>
    .label-name{
        float: left;
        text-align: center;
        width: 15%;
    }
    .ivu-col-span-9{
        padding-top: 30px;
    }
    .el-input-group{
        width: 80%;
    }
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

