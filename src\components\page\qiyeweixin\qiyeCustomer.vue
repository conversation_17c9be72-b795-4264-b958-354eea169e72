<template>
  <div class="container">
    <div class="handle-box">
      <el-form :inline="true" :model="pageInfo" class="demo-form-inline">
        <el-form-item>
          <el-button type="primary" @click="hitLabel()">批量添加标签</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="list" :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}"
              @selection-change="handleSelectionChange"      stripe v-loading="loading" class="table" border style="width: 100%">
      <el-table-column
          type="selection"
          width="55">
      </el-table-column>
      <el-table-column
          prop="name"
          label="名称"
          :show-overflow-tooltip="true"
          width="100">
      </el-table-column>
      <el-table-column
          prop="memberId"
          label="会员Id"
          width="110">
      </el-table-column>
      <el-table-column  id="demo"
                        prop="avatar"
                        label="头像"
                        :show-overflow-tooltip="true"
                        width="100">
      </el-table-column>
      <el-table-column
          prop="type"
          label="微信/企业微信"
          width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.type === 1 ? 'success' : 'primary'"
                  disable-transitions>
            <span v-if=" scope.row.type===1">
                            微信
                        </span>
            <span v-if=" scope.row.type===2">
                           企业微信
                        </span>
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="gender"
          label="性别"
          width="80">
        <template slot-scope="scope">
            <el-tag type="info" v-if=" scope.row.gender===0">
                            未知
                        </el-tag>
          <el-tag v-if=" scope.row.gender===1">
                            男性
                        </el-tag>
          <el-tag type="danger" v-if=" scope.row.gender===2">
                           女性
                        </el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="createtime"
          label="添加时间"
          width="200">
      </el-table-column>
      <el-table-column
          prop="tag_name"
          label="标签">
      </el-table-column>
    </el-table>
    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChange"
            :current="this.dom.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="10"/>
    </div>
    <el-dialog title="选择标签" :visible.sync="dialogVisible" width="550px" append-to-body>
      <div v-for="item in qiyeLabelGroup">
        <el-form  ref="form" label-width="100px" style="background:#FFFFFF" label-position="right">
        <el-form-item :label="item.group_name">
          <el-checkbox-group v-model="labels" value-key="id">
            <el-checkbox-button v-for="(val,index) in qiyeLabel" v-show="val.qiyeLabelGroupId===item.id"
                                :key="index" :label="val.name"
                                @change="change(val.id)">{{ val.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        </el-form>
      </div>
      <div style="margin-top: 15px;">
        <el-row type="flex" justify="center">
          <el-col :span="3">
            <el-button @click="dialogVisible = false" size="large">取 消</el-button>
          </el-col>
          <el-col :span="3"></el-col>
          <el-col :span="3">
            <el-button type="success" size="large" @click="markTag()" >确定</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: "qiyeCustomer",
  data() {
    return {
      userid: this.$route.query.userid,
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      pageSizeOpts: [10, 15, 20],
      loading: false,
      list: [],
      dom: {
        size: 10,
        current: 1,
        userid: null,
      },
      qiyeCustomerLabel:{
        userid: null,
        external_userid:[],
        add_tag:[],
      },
      labels:[],
      //标签组
      qiyeLabelGroup: {
        group_id: [],
        group_name: [],
      },
      //标签
      qiyeLabel: {
        id: [],
        name: [],
        qiyeLabelGroupId: [],
      },
      dialogVisible: false,
    };
  },
  created() {
      this.getData()
      this.getQiyeLabelGroup()
      this.getQiyeLabel()
  },
  watch: {
    '$route.query.userid'(Val) {
      this.userid=Val
      this.getData()
      this.getQiyeLabelGroup()
      this.getQiyeLabel()
    },
  },
  methods: {
    getData() {
      this.dom.userid=this.userid
      this.$postData("getQiyeCustomerAdds", this.dom).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dom.size = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dom.current = index;
      this.getData();
    },
    //打标签
    hitLabel(){
    this.dialogVisible = true
    },
    handleSelectionChange(val) {
      let a=[]
      val.forEach(e => {
        a.push(e.external_userid)
      })
      this.qiyeCustomerLabel.external_userid = a;
    },
    //标签组
    getQiyeLabelGroup() {
      this.$getData("getQiyeLabelGroup",).then(res => {
        if (res.status === 200) {
          this.qiyeLabelGroup = res.data
        }
      })
    },
    //标签
    getQiyeLabel() {
      this.$getData("getQiyeLabel",).then(res => {
        if (res.status === 200) {
          this.qiyeLabel = res.data
        }
      })
    },
    //标签
    change(id) {
      if (this.qiyeCustomerLabel.add_tag.indexOf(id) >= 0) {
        this.qiyeCustomerLabel.add_tag.splice(this.qiyeCustomerLabel.add_tag.indexOf(id), 1);
      } else {
        this.qiyeCustomerLabel.add_tag.push(id);
      }
    },
    markTag() {
      this.dialogVisible=false
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
      this.qiyeCustomerLabel.userid=this.userid;
      this.$postData("editCustomerMarkTag",this.qiyeCustomerLabel).then(res => {
        console.log(res)
        if (res.status === 200) {
          loading.close();
          this.$message({message: '添加成功', type: 'success'});
        }
      })
    },
  }
}
</script>

<style scoped>
table {
  border-collapse: collapse; /*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
  border-left: #C8B9AE solid 1px;
  border-top: #C8B9AE solid 1px;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
</style>