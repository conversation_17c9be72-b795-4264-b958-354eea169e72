<template>
	<div class="container" id="pdfDom">

		<!-- 搜索栏目 -->
		<div class="handle-box">
			<el-form ref="form" :model="pageInfo">
				<el-row>
					<el-col :span="6">
						<el-form-item label="阶段名称" style="margin-right: 20px">
							<el-input v-model="quer.flowName" placeholder="请输入阶段名称"></el-input>
						</el-form-item>
					</el-col>

          <el-col :span="6">
            <el-form-item label="是否为必走阶段" style="margin-right: 20px">
              <el-select v-model="quer.ifMustGo" placeholder="请选择是否为必走阶段" clearable>
                <el-option
                    v-for="item in ifMustGoOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="所属计划" style="margin-right: 20px">
              <el-select v-model="quer.planId" placeholder="请选择所属计划" clearable>
                <el-option
                    v-for="item in planOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

					<el-col :span="6" style="margin: 32px 0;">
						<el-button type="primary" @click="query()" icon="el-icon-search">搜索
						</el-button>

						<el-button style="margin-left: 10px" type="success" icon="el-icon-circle-plus-outline" @click="certModal = true">添加
						</el-button>
					</el-col>
				</el-row>

			</el-form>
		</div>




		<!-- 数据表格 -->
		<el-table :data="list" v-loading="loading" border stripe
			:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
			:expand-row-keys="expands" @expand-change="expandSelect">

			<el-table-column width="170" prop="flowName" label="阶段名称">
				<template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.flowName }}</span>

          <el-input v-model="scope.row.flowName" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
                    placeholder="请输入阶段名称" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>

      <el-table-column width="140" prop="flowSort" label="阶段排序">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.flowSort }}</span>

          <el-input v-model="scope.row.flowSort" style="width: 100%;" type="number" v-if="scope.row.isEdit"
                    placeholder="请输入阶段排序" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>

      <el-table-column width="140" label="绑定任务">
        <template slot-scope="scope">
          <el-tag type="primary" @click="selectBingDingTask(scope.row)">点击配置</el-tag>
        </template>
      </el-table-column>

			<el-table-column width="140" prop="planTitle" label="所属计划">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{scope.row.planTitle}}</span>

          <el-select v-if="scope.row.isEdit" v-model="scope.row.planId" placeholder="请选择所属计划">
            <el-option
                v-for="item in planOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
			</el-table-column>

      <el-table-column width="140" prop="ifMustGo" label="是否必走阶段">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{scope.row.ifMustGo==1?'是':'否'}}</span>
          <el-radio v-if="scope.row.isEdit" v-model="scope.row.ifMustGo" label="0">否</el-radio>
          <el-radio v-if="scope.row.isEdit" v-model="scope.row.ifMustGo" label="1">是</el-radio>
        </template>
      </el-table-column>

			<el-table-column width="140" prop="createTime" label="创建时间">
				<template slot-scope="scope">
					<span>{{scope.row.createTime}}</span>
				</template>
			</el-table-column>

			<el-table-column width="130" prop="creater" label="创建人">
				<template slot-scope="scope">
					<span >{{scope.row.creater}}</span>
				</template>
			</el-table-column>

			<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
				<template slot-scope="scope">
					<el-button @click="openEdit(scope.row)" type="success" size="small" :disabled="scope.row.isEdit"
						v-if="!scope.row.isEdit" icon="el-icon-edit">修改
					</el-button>
					<el-button @click="updateUnionFlow(scope.row)" type="primary" size="small"
						v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
					</el-button>
					<el-popconfirm title="此操作会将所有绑定此阶段的阶段任务一起删除，确定删除吗？" @confirm="deleteUnionFlow(scope.row)">
						<el-button type="danger" size="small" slot="reference" style="margin-left: 10px" icon="el-icon-delete">删除
						</el-button>
					</el-popconfirm>

				</template>
			</el-table-column>

		</el-table>


		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--添加成长阶段-->
		<el-dialog :visible.sync="certModal" width="60%" title="添加成长阶段" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="110px" class="demo-ruleForm" size="mini">

							<el-form-item label="阶段名称：">
								<el-input v-model="unionFlow.flowName" type="textarea" class="handle-input mr10"
									placeholder="请输入阶段名称">
								</el-input>
							</el-form-item>


              <el-form-item label="阶段排序：">
                <el-input v-model="unionFlow.flowSort" type="number" class="handle-input mr10"
                          placeholder="请输入阶段排序">
                </el-input>
              </el-form-item>

              <el-form-item label="是否必走阶段：">
                <el-radio v-model="unionFlow.ifMustGo" label="0">否</el-radio>
                <el-radio v-model="unionFlow.ifMustGo" label="1">是</el-radio>
              </el-form-item>

              <el-form-item label="所属计划：">
                <el-select v-model="unionFlow.planId" placeholder="请选择所属计划" clearable>
                  <el-option
                      v-for="item in planOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="saveUnionFlow()" type="success" size="small">确定添加
					</el-button>
				</div>

			</div>
		</el-dialog>


    <el-dialog
        title="绑定任务："
        :visible.sync="dialogFlag"
        width="80%">
      <div style="display: flex">
        <el-button style="margin-left: 10px;margin-bottom: 20px;" type="success" icon="el-icon-circle-plus-outline"
                   @click="taskModal=true">添加绑定任务
        </el-button>
      </div>
      <el-table :data="taskList" border stripe
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
                :expand-row-keys="expands" @expand-change="expandSelect">

        <el-table-column width="140" prop="taskTitle" label="任务标题">
          <template slot-scope="scope">
            <span>{{ scope.row.taskTitle }}</span>
          </template>
        </el-table-column>


        <el-table-column width="140" prop="taskIntroduce" label="任务介绍">
          <template slot-scope="scope">
            <span>{{ scope.row.taskIntroduce }}</span>
          </template>
        </el-table-column>


        <el-table-column width="140" prop="sort" label="任务排序">
          <template slot-scope="scope">
            <span v-if="!scope.row.isEdit">{{ scope.row.sort }}</span>
            <el-input v-model="scope.row.sort" style="width: 100%;" type="number" v-if="scope.row.isEdit"
                      placeholder="请输入任务排序" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
          </template>
        </el-table-column>

        <el-table-column width="140" prop="taskType" label="任务类型">
          <template slot-scope="scope">
            <span>{{scope.row.taskType==1?'链接':scope.row.taskType==0?'表单':'未知'}}</span>
          </template>
        </el-table-column>

        <el-table-column width="140" prop="endFlag" label="是否为截止任务">
          <template slot-scope="scope">
            <span v-if="!scope.row.isEdit">{{scope.row.endFlag==1?'是':'否'}}</span>
            <el-radio v-if="scope.row.isEdit" v-model="scope.row.endFlag" label="0">否</el-radio>
            <el-radio v-if="scope.row.isEdit" v-model="scope.row.endFlag" label="1">是</el-radio>
          </template>
        </el-table-column>

        <el-table-column width="140" prop="appid" label="跳转小程序AppId">
          <template slot-scope="scope">
            <span>{{ scope.row.appid }}</span>
          </template>
        </el-table-column>

        <el-table-column width="140" prop="reward" label="任务奖励">
          <template slot-scope="scope">
            <span>{{scope.row.reward==1?'实物':scope.row.reward==0?'佳币':scope.row.reward==2?'奖金':'未知'}}</span>
          </template>
        </el-table-column>


        <el-table-column width="140" prop="appid" label="奖励数量">
          <template slot-scope="scope">
            <span>{{ scope.row.rewardNumber }}</span>
          </template>
        </el-table-column>


        <el-table-column width="140" prop="appid" label="实物奖励">
          <template slot-scope="scope">
            <span>{{ scope.row.rewardThing }}</span>
          </template>
        </el-table-column>

        <el-table-column width="140" prop="appid" label="跳转链接">
          <template slot-scope="scope">
            <span>{{ scope.row.linkUrl }}</span>
          </template>
        </el-table-column>


        <el-table-column width="140" prop="createTime" label="创建时间">
          <template slot-scope="scope">
            <span>{{scope.row.createTime}}</span>
          </template>
        </el-table-column>

        <el-table-column width="130" prop="creater" label="创建人">
          <template slot-scope="scope">
            <span >{{scope.row.creater}}</span>
          </template>
        </el-table-column>

        <el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
          <template slot-scope="scope">
            <el-button @click="openEdit(scope.row)" type="success" size="small" :disabled="scope.row.isEdit"
                       v-if="!scope.row.isEdit" icon="el-icon-edit">修改
            </el-button>
            <el-button @click="updateFlowTask(scope.row)" type="primary" size="small"
                       v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
            </el-button>
            <el-popconfirm title="确定删除吗？" @confirm="deleteFlowTask(scope.row)">
              <el-button type="danger" size="small" slot="reference" style="margin-left: 10px" icon="el-icon-delete">删除
              </el-button>
            </el-popconfirm>

          </template>
        </el-table-column>

      </el-table>
    </el-dialog>

    <!--添加绑定任务-->
    <el-dialog :visible.sync="taskModal" width="60%" title="添加绑定任务" :mask-closable="false">
      <div style="height: 500px;overflow: hidden;overflow-y: scroll;">
        <el-row style="width:100%;height: auto;margin-bottom: 0px;">
          <el-col :span="12">
            <el-form ref="ruleForm" label-width="110px" class="demo-ruleForm" size="mini">

              <el-form-item label="任务排序：">
                <el-input v-model="unionFlowTask.sort" type="number" class="handle-input mr10"
                          placeholder="请输入任务排序">
                </el-input>
              </el-form-item>

              <el-form-item label="成长任务：">
                <el-select v-model="unionFlowTask.taskId" placeholder="请选择成长任务" clearable>
                  <el-option
                      v-for="item in taskOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>

                <el-form-item label="是否为截止任务" style="margin-right: 20px">
                  <el-select v-model="unionFlowTask.endFlag" placeholder="请选择是否为截止任务" clearable>
                    <el-option
                        v-for="item in ifMustGoOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>


            </el-form>
          </el-col>
        </el-row>

        <div style="margin: 0 400px;width: 100%;">
          <el-button @click="saveFlowTask()" type="success" size="small">确定添加
          </el-button>
        </div>

      </div>
    </el-dialog>

	</div>

</template>

<script>

  export default {
    name: "qaLibraryType",
    components: {
    },
		data() {
			return {
				url: '',
				uploadId: 1,
        planOptions:[],
        ifMustGoOptions: [{
          value: 0,
          label: '否'
        }, {
          value: 1,
          label: '是'
        }],
				excelUploadId: 1,
        isModal: false,
        taskOptions:[],
        taskModal: false,
				isEdit: false,
				showEdit: true,
        unionFlowTask: {
          creater: localStorage.getItem("id"),
          taskId: null
        },
				certModal: false,
        dialogFlag: false,
        taskList: [],
				list: [],
				loading: true,
				pageSizeOpts: [5, 10, 20],
        unionFlow: {
          creater: localStorage.getItem("id")
        },
				certType: {},
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				expands: [],
				getRowKeys(row) {
					return row.id
				},
				quer: {
					"flowName": "",
					"ifMustGo": "",
					"planId": null,
					"current": 1,
					"size": 10
				},
			}
		},
		created() {
			this.getData()
			this.getAllUnionPlan()
      this.getAllUnionTask()
		},
		methods: {
      // 删除任务任务
      deleteFlowTask(val) {
        this.$postData("deleteFlowTask", val).then(res => {
          if (res.code == 0) {
            this.$message.success('删除成功!')
            this.getFlowTask()
          } else {
            this.$message.error('删除失败！' + res.msg)
          }
        })
      },
      // 更改阶段任务
      updateFlowTask(val) {
        // 数据校验
        if (!val.sort) {
          this.$message.error('请输入任务排序！')
        } else if (val.sort<= 0) {
          this.$message.error('任务排序不可为负数或0！')
        } else {
          val.creater = localStorage.getItem("id")
          this.$postData("updateFlowTask", val).then(res => {
            if (res.code == 0) {
              this.$message.success('更新成功!')
              this.getFlowTask()
              val.isEdit = false
            } else {
              this.$message.error('更新失败！' + res.msg)
            }
          })
        }
      },
      // 添加表单任务
      saveFlowTask() {
        // 数据校验
        if (!this.unionFlowTask.sort) {
          this.$message.error('请输入任务排序！')
        } else if (this.unionFlowTask.sort<= 0) {
          this.$message.error('任务排序不可为负数或0！')
        } else if (this.unionFlowTask.taskId==null) {
          this.$message.error('请选择成长任务！')
        } else if (this.unionFlowTask.endFlag==null) {
          this.$message.error('请选择是否为截止任务！')
        } else {
          this.$postData("saveFlowTask", this.unionFlowTask).then(res => {
            if (res.code == 0) {
              this.$message.success('添加成功!')
              this.taskModal = false
              this.unionFlowTask.sort = ''
              this.unionFlowTask.formId = null
              this.getFlowTask()
            } else {
              this.$message.error('添加失败！' + res.msg)
            }
          })
        }
      },
      getAllUnionTask() {
        // 获取所有成长表单
        this.$getData("getAllUnionTask", null).then(res => {
          if (res.code == 0) {
            this.taskOptions = res.data
          } else {
            this.taskOptions = []
            this.$message.error('未查询到成长表单!')
          }
        })
      },
      selectBingDingTask(val){
        this.unionFlowTask.flowId = val.id
        this.dialogFlag = true
        this.getFlowTask()
      },
      getFlowTask(){
        // 获取阶段任务
        this.$getData("getFlowTask", this.unionFlowTask).then(res => {
          if (res.code == 0) {
            this.taskList = res.data
            // 追加编辑状态位
            for (let item of this.taskList) {
              this.$set(item, "isEdit", false)
            }
          } else {
            this.$message.error('未查询到任务表单!')
          }
        })
      },
      getAllUnionPlan() {
        // 获取所有成长计划
        this.$getData("getAllUnionPlan", null).then(res => {
          if (res.code == 0) {
             this.planOptions = res.data
          } else {
            this.planOptions = []
            this.$message.error('未查询到成长计划!')
          }
        })
      },
			getData() {
				// 获取成长阶段
				this.$getData("getUnionFlow", this.quer).then(res => {
					this.loading = false
					if (res.code == 0) {
						this.list = res.data.records
						this.pageInfo.current = res.data.current
						this.pageInfo.size = res.data.size
						this.pageInfo.total = res.data.total
						// 追加编辑状态位
						for (let item of this.list) {
							this.$set(item, "isEdit", false)
						}
					} else {
						this.list = []
						this.$message.error('未查询到成长阶段!')
					}
				})
        this.getAllUnionPlan()
        this.getAllUnionTask()
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.getData()
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			// 添加成长阶段
      saveUnionFlow() {
				// 数据校验
				if (!this.unionFlow.flowName) {
					this.$message.error('请输入阶段名称！')
				} else if (!this.unionFlow.flowSort) {
          this.$message.error('请输入阶段排序！')
        } else if (this.unionFlow.flowSort<= 0) {
          this.$message.error('阶段排序不可为负数或0！')
        } else if (this.unionFlow.planId==null) {
          this.$message.error('请选择所属计划！')
        } else if (this.unionFlow.ifMustGo==null) {
          this.$message.error('请选择是否为必走阶段！')
        } else {
					this.$postData("saveUnionFlow", this.unionFlow).then(res => {
						if (res.code == 0) {
							this.$message.success('添加成功!')
							this.certModal = false
              this.unionFlow.flowName = ''
              this.unionFlow.ifMustGo = ''
              this.unionFlow.flowSort = ''
              this.unionFlow.planId = null
							this.getData()
						} else {
							this.$message.error('添加失败！' + res.msg)
						}
					})
				}
			},
			// 删除成长计划
      deleteUnionFlow(val) {
				this.$postData("deleteUnionFlow", val).then(res => {
					if (res.code == 0) {
						this.$message.success('删除成功!')
						this.getData()
					} else {
						this.$message.error('删除失败！' + res.msg)
					}
				})
			},
			// 更改成长阶段
      updateUnionFlow(val) {
        // 数据校验
        if (!val.flowName) {
          this.$message.error('请输入阶段名称！')
        } else if (!val.flowSort) {
          this.$message.error('请输入阶段排序！')
        } else if (val.flowSort<= 0) {
          this.$message.error('阶段排序不可为负数或0！')
        } else if (val.planId==null) {
          this.$message.error('请选择所属计划！')
        } else {
          val.creater = localStorage.getItem("id")
          this.$postData("updateUnionFlow", val).then(res => {
            if (res.code == 0) {
              this.$message.success('更新成功!')
              this.getData()
              val.isEdit = false
            } else {
              this.$message.error('更新失败！' + res.msg)
            }
          })
        }
			},
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>