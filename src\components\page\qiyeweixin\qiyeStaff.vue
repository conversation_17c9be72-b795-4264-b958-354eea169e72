<template>
  <div class="container">
    <div class="handle-box">
      <el-form :inline="true" :model="pageInfo" class="demo-form-inline">
        <el-form-item label="名称">
          <el-input v-model="dom.name" placeholder="名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="主部门" >
          <el-select v-model="dom.main_departmentName" value-key="id" filterable clearable placeholder="使用部门">
            <el-option
                v-for="item in partys"
                :key="item.id"
                :label="item.name"
                :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="电话">
          <el-input v-model="dom.mobile" placeholder="电话" clearable ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-refresh" @click="reset()">
            重置
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-popconfirm title="确定删除吗？" @confirm="batchDeleteUser()">
                      <el-button type="danger" style="height: 35px;width: 120px;" size="success" slot="reference">
                        批量删除员工
                      </el-button>
          </el-popconfirm>

        </el-form-item>
      </el-form>
    </div>
    <el-table :data="list" ref="multipleTable" :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}"
              @selection-change="handleSelectionChange" stripe v-loading="loading" class="table" border style="width: 100%">
      <el-table-column
          type="selection"
          width="40">
      </el-table-column>
      <el-table-column
          prop="name"
          label="名称"
          :show-overflow-tooltip="true"
          width="100">
      </el-table-column>
      <el-table-column
          prop="mobile"
          label="电话"
          width="110">
      </el-table-column>
      <el-table-column  id="demo"
          prop="avatar"
          label="头像"
          :show-overflow-tooltip="true"
          width="100">
      </el-table-column>
      <el-table-column
          prop="main_departmentName"
          label="企微部门"
          width="120">
      </el-table-column>
      <el-table-column
          prop="position"
          label="职位信息"
          width="80">
      </el-table-column>
      <el-table-column
          prop="tagidsName"
          label="标签"
          width="80">
      </el-table-column>
      <el-table-column
          prop="linkStatus"
          label="绑定员工账号"
          width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.linkStatus === 0 ? 'danger' : 'success'" effect="dark"
                  disable-transitions>
            <span v-if=" scope.row.linkStatus===1">
                            已绑定
                        </span>
            <span v-if=" scope.row.linkStatus===0">
                            未绑定
                        </span>
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作">
        <template slot-scope="scope">
          <div style="display: flex">
            <el-button size="small" type="primary" plain
                         @click="customer(scope.row.userid)">
              我的客户
            </el-button>
            <el-button size="small "  type="info" plain
                       @click="updateInformation(scope.row)">
              信息更改
            </el-button>
            <el-button size="success" type="primary" plain
                       @click="myApplication(scope.row.userid)">
              我的申请
            </el-button>
            <el-popconfirm title="确定删除吗？" @confirm="deleteUser(scope.row.userid)">
              <el-button type="danger" size="success" slot="reference" style="margin-left: 10px">删除员工
              </el-button>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChange"
            :current="this.dom.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="10"/>
    </div>
    <el-dialog title="信息更改申请" :visible.sync="department" width="500px" center>
        <el-form ref="form" label-width="150px">
          <el-form-item label="名称">
            {{replace.name}}
          </el-form-item>
          <el-form-item label="电话">
            {{replace.mobile }}
          </el-form-item>
          <el-form-item label="部门">
            {{replace.main_departmentName}}
          </el-form-item>
          <el-form-item label="职位">
            {{replace.position }}
          </el-form-item>
          <el-form-item label="新的部门" required>
            <el-select v-model="replace.updateDepartmentName" value-key="id"  @change="getPartyUser" filterable clearable placeholder="使用部门">
              <el-option
                  v-for="item in partys"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="新的职位" >
            <el-input v-model="replace.updatePosition" clearable  maxlength="10" style="width:200px" ></el-input>
          </el-form-item>
          <el-form-item label="标签" >
            <el-tag
                v-for="tag in replace.updateTagidsName"
                :key="tag"
                :disable-transitions="false"
                @close="handleClose(tag)"
                effect="plain"
                closable>
              {{tag}}
            </el-tag>
            <el-button  class="button-new-tag" size="small" @click="showInput">添加标签</el-button>
          </el-form-item>
        </el-form>
      <span slot="footer" class="dialog-footer">
    <el-button @click="department = false">取 消</el-button>
    <el-button type="primary" @click="determine()">确 定</el-button>
  </span>
    </el-dialog>
    <el-drawer
        title="我的申请" size="80%"
        :visible.sync="drawer"
        direction="rtl">
      <el-table :data="list2" :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}"
                stripe v-loading="loading" class="table" border style="width: 100%">
        <el-table-column
            prop="isAdopt"
            label="申请是否通过">
        </el-table-column>
        <el-table-column
            prop="name"
            label="名称"
            :show-overflow-tooltip="true"
            width="100">
        </el-table-column>
        <el-table-column
            prop="mobile"
            label="电话"
            width="100">
        </el-table-column>
        <el-table-column
            prop="main_departmentName"
            label="部门"
            width="110">
        </el-table-column>
        <el-table-column
            prop="position"
            label="职位"
            width="90">
        </el-table-column>
        <el-table-column
            prop="tagids"
            label="标签"
            :show-overflow-tooltip="true"
            width="100">
        </el-table-column>
        <el-table-column
            prop="updateDepartmentName"
            label="新的部门"
            width="110">
        </el-table-column>
        <el-table-column
            prop="updatePosition"
            label="新的职位"
            width="90">
        </el-table-column>
        <el-table-column
            prop="updateTagids"
            label="新的标签"
            :show-overflow-tooltip="true"
            width="100">
        </el-table-column>
        <el-table-column
            prop="creatDate"
            label="申请时间"
            width="170">
        </el-table-column>
      </el-table>
    </el-drawer>
    <el-dialog title="选择标签" :visible.sync="dialogVisible" width="550px" append-to-body>
          <el-checkbox-group v-model="replace.updateTagidsName" value-key="id"  >
            <el-checkbox-button v-for="item in list3" @change="addTag(item.tagid)"
                                :key="item.tagid" :label="item.tagname">{{ item.tagname}}
            </el-checkbox-button>
          </el-checkbox-group>
    </el-dialog>
    注：未激活的企业微信不显示。
  </div>
</template>

<script>

export default {
  name: "qiyeStaff",
  inject: ['reload'],
  data() {
    return {
      multipleSelection: [],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      pageInfo2: {total: 10, size: 10, current: 1, pages: 1},
      pageSizeOpts: [10, 15, 20],
      loading: false,
      list: [],
      list2: [],
      list3: [],
      partys: [],
      dialogVisible: false,
      // inputVisible: false,
      department:false,
      position:false,
      drawer: false,
      dom: {
        name:null,
        mobile:null,
        departmentName:null,
        userid:null,
        size: 10,
        current: 1,
      },
      replace:{
        userid:null,
        name:null,
        mobile:null,
        main_department:null,
        main_departmentName:null,
        position:null,
        avatar:null,
        updateDepartment:null,
        updateDepartmentName:null,
        updatePosition:null,
        tagids:[],
        tagidsName:[],
        updateTagids:[],
        updateTagidsName:[],
      },
    };
  },
  created() {
    this.getData()
    this.getParty()
    this.getQiyeStaffLabel()
  },
  methods: {
    batchDeleteUser(){
      if (this.multipleSelection.length<=0){
        this.$message.error('请选择要删除的信息!')
        return
      }
      this.$postData("batchDeleteUser", this.multipleSelection).then(res => {
        if (res.status === 200) {
          this.multipleSelection = []
          this.$message.success('删除成功!')
          this.reset()
        }else {
          this.$message.error('删除失败!')
        }
      })
    },
    deleteUser(val){
      let arr = [val]
      this.$postData("batchDeleteUser", arr).then(res => {
        if (res.status === 200) {
          this.$message.success('删除成功!')
          this.reset()
        }else {
          this.$message.error('删除失败!')
        }
      })
    },
    handleSelectionChange(val) {
      let arr = []
      for (let i = 0; i < val.length; i++) {
        arr.push(val[i].userid)
      }
      this.multipleSelection = arr;
    },
    addTag(){
      this.replace.updateTagids = [];
      this.replace.updateTagidsName.forEach(e => {
        this.list3.find((v) => {
          if (v.tagname === e) {
            this.replace.updateTagids.push(v.tagid);
          }
        });
      })
    },
    //添加标签
    showInput() {
      this.dialogVisible = true;
    },
    //删除标签
    handleClose(tag) {
      this.replace.updateTagids.splice(this.replace.updateTagids.indexOf(tag), 1);
    },
    onSubmit(){
      this.dom.size=10
      this.dom.current=1
      this.getData();
    },
    //重置
    reset(){
      this.dom.name=null;
      this.dom.mobile=null;
      this.dom.departmentName=null;
      this.dom.userid=null;
      this.getData();
    },
    getData() {
      this.$postData("selectQiyeWeixinUserIPage", this.dom).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    getQiyeStaffLabel(){
      this.$getData("getQiyeStaffLabel").then(res =>{
        if (res.status === 200) {
          this.list3 =res.data;
        }
      })
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dom.size = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dom.current = index;
      this.getData();
    },//删除活码
    img() {
      var demo = document.getElementById("demo");
      var gg = demo.getElementsByTagName("img");
      var ei = document.getElementById("enlarge_images");
      for (let i = 0; i < gg.length; i++) {
        var ts = gg[i];
        ts.onmousemove = function (event) {
          event = event || window.event;
          ei.style.display = "block";
          ei.innerHTML = '<img src="' + this.src + '" />';
          ei.style.top = document.body.scrollTop + event.clientY + 10 + "px";
          ei.style.left = document.body.scrollLeft + event.clientX + 10 + "px";
        }
        ts.onmouseout = function () {
          ei.innerHTML = "";
          ei.style.display = "none";
        }
        ts.onclick = function () {
          window.open(this.src);
        }
      }
    },
    //我的客户
    customer(userid){
      this.$router.push({path:'/qiyeCustomer',query:{userid:userid}});
    },
    //信息更换
    updateInformation(val){
      console.log(val)
      this.replace.userid=val.userid;
      this.replace.name=val.name;
      this.replace.mobile=val.mobile;
      this.replace.main_department=val.main_department;
      this.replace.main_departmentName=val.main_departmentName;
      this.replace.position=val.position;
      this.replace.avatar=val.avatar;
      if(null!=val.tagids){
        this.replace.tagids=val.tagids.split(",");
        this.replace.tagidsName=val.tagidsName.split(",");
        this.replace.updateTagids=val.tagids.split(",");
        this.replace.updateTagidsName=val.tagidsName.split(",");
      }
      this.department=true;
    },
    getPartyUser(id) {
      this.partys.find((v) => {
        if (v.name === id) {
          this.replace.updateDepartment = v.id

        }
      });
    },
    myApplication(userid){
      this.drawer = true
      let qiyeApply={
            userid:userid,
      }
      this.$postData("selectQiyeApplyIPage",qiyeApply).then(res =>{
        if (res.status === 200) {
          this.list2 = res.data.records;
          this.pageInfo2.current = res.data.current;
          this.pageInfo2.size = res.data.size;
          this.pageInfo2.total = res.data.total
        }
      })
    },
    determine(){
      this.department=false;
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.replace.tagids=this.replace.tagids.join(",")
      this.replace.tagidsName=this.replace.tagidsName.join(",")
      this.replace.updateTagids=this.replace.updateTagids.join(",")
      this.replace.updateTagidsName=this.replace.updateTagidsName.join(",")
      this.$postData("addQiyeApply",this.replace).then(res =>{
        if (res.status === 200) {
          if(res.data===0){
            loading.close();
            this.$message({message: '发送申请信息更改成功', type: 'success'});
            //刷新
            this.reload();
          }else {
            loading.close();
            this.$message.error('发送申请信息更改失败，当前部门无负责人。请从新申请');
            //刷新
            this.reload();
          }
        }
      })
    },
    getParty() {
      this.$getData("getParty",).then(res => {
        if (res.status === 200) {
          this.partys = res.data
        }
      })
    },
  }
}
</script>

<style scoped>
.el-tag {
  margin-left: 10px;
}
table {
  border-collapse: collapse; /*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
  border-left: #C8B9AE solid 1px;
  border-top: #C8B9AE solid 1px;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
</style>