<template>
  <div style="border: 1px solid #ccc">
    <Toolbar
        style="border-bottom: 1px solid #ccc"
        :editor="editor"
        :defaultConfig="toolbarConfig"
        :mode="mode"
    />
    <Editor
        style="height: 450px; overflow-y: hidden"
        v-model="html"
        :defaultConfig="editorConfig"
        :mode="mode"
        @onChange="onChange"
        @onCreated="onCreated"
    />
  </div>
</template>
<script>
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
export default {
  components: { Editor, Toolbar },
  props: {
    content: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      editor: null,
      html: "",
      toolbarConfig: {},
      editorConfig: {
        placeholder: "请输入内容...",
        MENU_CONF: {
          //   配置上传图片
          uploadImage: {
            server:"https://api.xiaoyujia.com/system/imageUpload",
            fieldName: "file",
            meta: {},
            // 请求头
            // headers: { token: localStorage.getItem("token") },
            timeout: 5 * 1000, // 5s 超时时间
            //选择文件时的类型限制，默认为['image/*'] 如不想限制，则设置为 []
            allowedFileTypes: ["image/*"],
            maxFileSize: 30 * 1024 * 1024, //1g //设置大点 不然图片过大会报错
            // base64LimitSize: 1000000 * 1024, // 1g 以下插入 base64
            // 自定义插入图片
            customInsert(res, insertFn) {
              // 从 res 中找到 url alt href ，然后插图图片
              console.log(res, "wang上传---res");
              if (res.code === 0) {
                insertFn(res.data);
              }else {
                this.$message.error(res.msg)
              }



            },
          },
          //   配置上传视频
          uploadVideo: {
            server:"",
            fieldName: "file",
            meta: {},
            // 请求头
            headers: { token: localStorage.getItem("token")},
            timeout: 5 * 1000, // 5s 超时时间
            //选择文件时的类型限制，默认为['video/*'] 如不想限制，则设置为 []
            allowedFileTypes: ["video/*"],
            // 自定义插入视频
            customInsert(res, insertFn) {
              insertFn(res.data);
            },
          },
        },
      },
      mode: "default", // 'default' or 'simple'
    };
  },
  methods: {
    onChange(editor) {
      this.$emit("changeData", this.html);
    },
    onCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
      this.toolbarConfig = {
        toolbarKeys: [//自定义菜单选项
          // 菜单 key
          "headerSelect",
          // 分割线
          "|",

          // 菜单 key
          "bold",
          "divider",
           "uploadImage",
           "deleteImage",
            "imageWidth30",
          "italic",
          // 菜单组，包含多个菜单
          {
            key: "group-more-style", // 必填，要以 group 开头
            title: "更多样式", // 必填
            iconSvg: "<svg>123</svg>", // 可选
            menuKeys: ["through", "code", "clearStyle"], // 下级菜单 key ，必填
          },
          "underline",
          "color",
          "bgColor",
        ],
        excludeKeys: [
          // 'fullScreen',
          // 'bold', 'underline', 'italic', 'through', 'code', 'sub', 'sup', 'clearStyle', 'color', 'bgColor', 'fontSize', 'fontFamily',
          //  'indent', 'delIndent', 'justifyLeft', 'justifyRight', 'justifyCenter', 'justifyJustify', 'lineHeight', 'insertImage', 'deleteImage',
          //  'editImage', 'viewImageLink', 'imageWidth30', 'imageWidth50', 'imageWidth100', 'divider', 'emotion', 'insertLink', 'editLink',
          // 'unLink', 'viewLink', 'codeBlock', 'blockquote', 'headerSelect', 'header1', 'header2', 'header3', 'header4', 'header5', 'todo',
          // 'redo', 'undo', 'fullScreen', 'enter', 'bulletedList', 'numberedList', 'insertTable', 'deleteTable', 'insertTableRow',
          // 'deleteTableRow', 'insertTableCol', 'deleteTableCol', 'tableHeader', 'tableFullWidth', 'insertVideo', 'uploadVideo', 'editVideoSize',
          //  'uploadImage', 'codeSelectLang','group-more-style
          "sub",
          "sup",
        ],
      };
      // 编辑器创建后，如果有内容则设置
      if (this.content) {
        this.setContent(this.content);
      }
    },
    setContent(content) {
      this.html = content || '';
      if (this.editor) {
        this.editor.setHtml(this.html);
      }
    }
  },
  watch: {
    content: {
      handler(newVal) {
        this.setContent(newVal);
      },
      immediate: true
    }
  },
  created() {
    this.html = this.content;
  },
  mounted() {},
  beforeDestroy() {
    const editor = this.editor;
    if (editor == null) return;
    editor.destroy(); // 组件销毁时，及时销毁编辑器
  },
};
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
