<template>
    <div>
        <Drawer
                title="详情"
                v-model="value3"
                width="50%"
                :mask-closable="false"
                @on-close="chooseThisModel"
                :styles="styles"
        >
            <Form ref="formItem" :model="formItem" :label-width="80" label-position="left">
                <Tabs v-model="current">
                    <TabPane label="已接单" name="1">
                        <div class="searchDiv">
                            <FormItem label="订单编号">
                                <Input placeholder="订单编号"
                                       @on-change="getByOrder"
                                       disabled
                                       style="width: 80%"
                                       v-model="formItem.billNo">
                                </Input>
                            </FormItem>
                            <FormItem label="服务项目">
                                <Input placeholder="服务项目" style="width: 80%"
                                       disabled
                                       v-model="formItem.productName">
                                </Input>
                            </FormItem>
                            <FormItem label="表彰人员">
                                <Row>
                                    <Col span="19">
                                        <Input placeholder="表彰人员" style="width: 100%"
                                               disabled
                                               v-model="formItem.employeeNames">
                                        </Input>
                                    </Col>
                                    <Col span="4">
                                        <!--<Button type="primary" @click="userModal=true">选择</Button>-->
                                    </Col>
                                </Row>
                            </FormItem>
                            <FormItem label="表彰等级">
                                <Select v-model="formItem.level" style="width:80%">
                                    <Option v-for="item in levelList" :value="item.id">{{ item.text}}</Option>
                                </Select>
                            </FormItem>
                            <FormItem label="备注">
                                <Input v-model="formItem.remark" type="textarea" :rows="3"
                                       style="width: 80%"
                                       placeholder="备注"/>
                            </FormItem>
                        </div>
                    </TabPane>
                    <TabPane label="审核阶段" name="2">
                        <div class="searchDiv">
                            <FormItem label="核实过程">
                                <Input v-model="formItem.content" type="textarea" :rows="3"
                                       style="width: 80%"
                                       placeholder="核实过程"/>
                            </FormItem>
                            <FormItem label="上传材料">
                                <Upload
                                        multiple
                                        :action="imgUrl"
                                        :on-success="onSuccess"
                                        :on-preview="handlePictureCardPreview"
                                        :on-remove="handleRemove"
                                        :before-upload="before"
                                        :on-exceed="onExcees"
                                        :default-file-list="filesList"
                                        :max-size="10240"
                                        :data="files">
                                    <Button icon="ios-cloud-upload-outline">点击上传</Button>
                                </Upload>
                            </FormItem>
                            <FormItem label="表彰判定">
                                <RadioGroup v-model="formItem.determine">
                                    <Radio label="1">有效表彰</Radio>
                                    <Radio label="2">无效表彰</Radio>
                                </RadioGroup>
                            </FormItem>
                            <FormItem label="总经办核实">
                                <Input v-model="formItem.approval" type="textarea" :rows="3"
                                       style="width: 80%"
                                       placeholder="核实过程"/>
                            </FormItem>
                        </div>
                    </TabPane>
                    <TabPane label="处理阶段" name="3">
                        <div class="searchDiv">
                            <FormItem label="表彰金额">
                                <Input v-model="formItem.fine"
                                       style="width: 80%"
                                       placeholder="表彰金额"/>
                            </FormItem>
                            <FormItem label="行政分奖励">
                                <el-input-number v-model="formItem.changeScore"
                                                 @change="handleChange"
                                                 :min="0" :max="100">
                                </el-input-number>
                            </FormItem>
                            <FormItem label="推荐案例">
                                <RadioGroup v-model="formItem.case">
                                    <Radio label="true">是</Radio>
                                    <Radio label="false">否</Radio>
                                </RadioGroup>
                            </FormItem>
                        </div>
                    </TabPane>
                    <TabPane label="已结案" name="7">
                        <div class="searchDiv">
                        </div>
                    </TabPane>
                </Tabs>
                <FormItem>
                    <div class="demo-footer">
                        <Button type="info" @click="next1" v-if="current!=='1'">上一步</Button>
                        <Button type="primary" v-if="current==='2'||current==='1'" @click="next"
                                style="margin-left: 3rem">{{formItem.determine==='2'?"完成":"下一步"}}
                        </Button>
                        <Button type="primary" v-if="current==='3'" @click="next"
                                style="margin-left: 3rem">完成
                        </Button>
                    </div>
                </FormItem>
            </Form>
        </Drawer>
    </div>
</template>
<script>
    import employeesChoose from '@/components/page/wordOrder/choose/employeesChoose.vue'

    export default {
        props: ['id'],
        data() {
            return {
                userModal: false,
                current: "1",
                filesList: [],
                files: {
                    fileName: "Ts/"
                },
                value3: true,
                imgUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
                styles: {
                    height: 'calc(100% - 55px)',
                    overflow: 'auto',
                    paddingBottom: '53px',
                    position: 'static'
                },
                periodList: [],
                levelList: [],
                typeList: [],
                formItem: {
                    id: null,
                    state: 1,
                    billNo: null,
                    productName: null,
                    file: null,
                    employeeNames: null,
                    remark: null,
                    content: null,
                    fine: "",
                    case: "true",
                    changeScore: null,
                    determine: '1',
                    level: null,
                    approval: null
                },
            }
        },
        components: {
            'employeesChoose': employeesChoose
        },
        created: function () {
            this.getLevelList();
            this.getById();
        },
        methods: {
            getFiles(data) {
                if (data.file) {
                    let split = data.file.split(",");
                    for (let i = 0; i < split.length; i++) {
                        let res = decodeURIComponent(split[i]);
                        let file = {
                            name: res.substring(res.lastIndexOf("/") + 1, res.length),
                            url: res
                        };
                        this.filesList.push(file);
                    }
                }
            },
            getById() {
                this.$getData("commend_getById", {
                    id: this.id
                }).then(res => {
                    if (res.status === 200) {
                        this.formItem = res.data;
                        this.formItem.determine = this.formItem.determine + "";
                        this.formItem.case = this.formItem.case + "";
                        this.current = this.formItem.state + "";
                        this.getFiles(res.data)
                    }
                })
            },
            next1() {
                if (this.current === "1") {
                    this.current = "1";
                } else {
                    this.current = Number(this.current) - 1 + "";
                }
            },
            next() {
                this.update();
            },
            update() {
                if (Number(this.current) >= this.formItem.state) {
                    this.formItem.state++
                }
                if (Number(this.current) === 3) {
                    this.formItem.state = 7
                }
                this.$postData("commend_update", this.formItem).then(res => {
                    if (res.status === 200) {
                        if (Number(this.current) === 3) {
                            this.current = "7"
                        } else {
                            this.current = Number(this.current) + 1 + "";
                        }
                    } else {
                        this.$message.error("保存失败，" + res.msg);
                    }
                })
            },
            handleChange(value) {
                this.formItem.changeScore = value
            },
            getByOrder() {
                this.$getData("getByOrderNo", {
                    orderNo: this.formItem.billNo
                }).then(res => {
                    if (res.status === 200) {
                        if (res.data) {
                            this.formItem.productName = res.data.productName;
                        } else {
                            this.formItem.productName = null
                        }
                    }
                })
            },
            chooseThisModel() {
                this.value3 = false;
                this.$emit('init-choose', "");
            },
            getLevelList() {
                this.$postUrl("get_dict", 131, null, {}).then(res => {
                    if (res.status === 200) {
                        this.levelList = res.data
                    }
                })
            },
            onSuccess(res) {
               ;
                if (res.status === 200) {
                    this.$Message.success('上传成功');
                    if (this.formItem.file) {
                        this.formItem.filee = this.formItem.file + "," + res.data;
                    } else {
                        this.formItem.file = res.data;
                    }
                    let url = decodeURIComponent(res.data);
                    let file = {
                        name: url.substring(url.lastIndexOf("/") + 1, url.length),
                        url: url
                    };
                    this.filesList.push(file);
                }
            },
            handlePictureCardPreview(file) {
                console.log(file);
                this.download(file.url, file.name);
            },
            onExcees() {
                this.$Message.success('只可上传一个文件');
            },
            download(src, fileName) {
                let x = new XMLHttpRequest();
                x.open("GET", src, true);
                x.responseType = 'blob';
                x.onload = function (e) {
                    let url = window.URL.createObjectURL(x.response);
                    let a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;
                    a.click();
                };
                x.send();
            },
            handleRemove(file) {
                console.log(file);
                let names = this.formItem.file.split(",");
                for (let i = 0; i < names.length; i++) {
                    if (file.url === decodeURIComponent(names[i])) {
                        names.splice(i, 1);
                        this.formItem.file = names.join(",");
                        break;
                    }
                }
                for (let i = 0; i < this.filesList.length; i++) {
                    if (file.url === decodeURIComponent(this.filesList[i].url)) {
                        this.filesList.splice(i, 1);
                        break;
                    }
                }
            },
            before() {
                if (this.formItem.file) {
                    let list = this.formItem.file.split(",");
                    const check = list.length < 2;
                    if (!check) {
                        this.$Notice.warning({
                            title: '最多可上传2个附件,多文件请打包上传'
                        });
                    }
                    return check;
                }
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.userModal = false;
            },
        }
    }
</script>
<style>
    .demo-footer {
        width: 100%;
        margin-left: 100px;
        margin-top: 30px;
    }

    .searchDiv {
        width: 60%;
        margin: 20px auto;
        font-weight: bold;
        font-size: 17px !important;
    }
</style>
