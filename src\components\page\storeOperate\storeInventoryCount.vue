<template>
  <div>
    <div class="handle-box">
      <el-form ref="form" :model="pageInfo" label-width="80px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="选择门店">
              <el-select ref="storeNameSel" filterable v-model="quer.storeId" placeholder="请选择">
                <el-option
                    v-for="(item, index) in storeList"
                    :key="index"
                    :label="item.text"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-button type="primary" @click="query()" icon="el-icon-search"
                       style="margin-left: 20px;float: right">搜索
            </el-button>

          </el-col>
        </el-row>

      </el-form>
    </div>
    <el-table :data="list" height="500px" v-loading="loading"
              :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
      <el-table-column
          prop="storeName"
          width="500"
          label="门店">

      </el-table-column>
      <el-table-column
          width="110"
          prop="allStock"
          label="总物料">
      </el-table-column>
      <el-table-column
          width="160"
          prop="stock"
          label="剩余物料">

      </el-table-column>
      <el-table-column
          width="160"
          label="详情">
        <template slot-scope="scope">
          <el-button @click="drawer = true,getStockInfo(scope.row.storeId)" style="float: right">所有物料出入库明细</el-button>
        </template>
      </el-table-column>


    </el-table>
    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChange"
            :current="this.dto.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="10"/>
    </div>

    <el-drawer
        title="物料来源去处详情"
        :visible.sync="drawer">
      <el-container>
        <el-header>
          <el-radio-group v-model="radio1" style="top: 200px" @change="changeState">
            <!--            <el-radio-button :label="0">所有</el-radio-button>-->
            <el-radio-button :label="2">出库</el-radio-button>
            <el-radio-button :label="1">入库</el-radio-button>
          </el-radio-group>
        </el-header>
        <el-main>
          <el-card v-for="(item , i) in stockList" class="box-card" :key="i">
            <div>操作物品：{{ item.goodsName }}</div>
            <div>操作时间：{{ item.creatTime }}</div>
            <div v-if="radio1 === 2">
              出库状态：
              <span v-if="item.state === 1">待出库</span>
              <span v-if="item.state === 2">已出库</span>
              <span v-if="item.state === 3">待核销</span>
              <span v-if="item.state === 4">已核销</span>
            </div>
            <div v-if="item.sellType ===0">销售类别：购买核销</div>
            <div v-if="item.sellType ===1">销售类别：领用核销</div>
            <div v-if="item.sellType ===2">销售类别：客户下单核销</div>
            <div>操作人：{{ item.realName }}</div>
            <div>数量：{{ item.number }}</div>
            <div v-if="item.remark !=null">备注：{{item.remark}}</div>
          </el-card>
        </el-main>
      </el-container>
    </el-drawer>


  </div>
</template>

<script>
import Vue from 'vue';

export default {
  name: "storeInventoryCount",

  data() {
    return {
      list: null,
      loading: false,
      storeList: [],
      stockList: [],
      drawer: false, //详情
      chukuList:[], //出库集合
      drawer1: false, //出库
      storeName:null,
      radio1: 1,
      pageSizeOpts: [20, 40, 60],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      quer: {
        storeId: null,
        size: 100,
        current: 1,
      },
      storeId:null,
      dto: [],

    }
  },
  created() {

    this.getData();
    this.getStoreInfo(); //下拉框门店选择

  },
  methods: {

    getData() {
      this.$postData("getAllStoreInventory", this.quer).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    query() {
      this.storeName = this.$refs.storeNameSel.selectedLabel
      this.loading = true;
      this.quer.current = 1;
      this.getData();
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.quer.size = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.quer.current = index;
      this.getData();
    },
    changeState(val) {
      this.quer.state = val;
      this.getStockInfo(this.storeId);
    },

    getStoreInfo() {
      this.$getData("getAllStore", {}, {}).then(res => {
        if (res.status == 200) {
          this.storeList = res.data;
        }
      });
    },
    getStockInfo(id) {
      this.storeId = id;
     let param = {
       storeId : id,
       state : this.radio1,
     }
      this.$postData("getStoreInventoryInfo", param, {}).then(res => {
        if (res.status == 200) {
          this.stockList = res.data;
        }
      });
    },


    getStoreOut(){
      this.quer.size = 500;
      this.getChuKuData();

    },

    getStoreIn(){
      this.$getData("selectAllCollect", null).then(res => {
        if (res.code === 0) {
          this.collectType = res.data;
        }
      })


    },


  },
}
</script>

<style scoped>
.handle-box {
  /*margin-bottom: 10px;*/
  font-weight: bold !important;
}

table {
  border-collapse: collapse; /*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
  border-left: #C8B9AE solid 1px;
  border-top: #C8B9AE solid 1px;
}

table td {
  border-right: #C8B9AE solid 1px;
  border-bottom: #C8B9AE solid 1px;
}

th {
  background: #eee;
  font-weight: normal;
}

tr {
  background: #fff;
}

tr:hover {
  background: #cc0;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

>>> .el-upload--text {
  width: 200px;
  height: 150px;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
