<!--

支持选择单个员工

-->
<template style="background-color: #000">
    <div>
        <el-input
                v-model="employee.name"
                placeholder="姓名"
                style="width:180px"
                class="handle-input mr10"
                @input="getData()"
        ></el-input>
        <el-button type="primary" @click="getData()">搜索</el-button>
        <Table highlight-row ref="currentRowTable" :columns="columns" :data="data"
               @on-current-change="onCurrentChange"></Table>
        <Page :total="pageInfo.total" @on-change="onChange" :show-total="true" :page-size="5"/>
    </div>
</template>


<script>
    export default {
        data() {
            return {
                userModal: false,
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                currentRow: null,
                columns: [
                    {
                        type: 'index',
                        width: 100,
                        align: 'center',

                    },
                    {
                        title: '工号',
                        key: 'no',
                        height: 10,
                        width: 130,

                    },
                    {
                        title: '姓名',
                        key: 'realName',
                        width: 150,
                    },
                ],
                employee: {
                    name: null,
                    id: null,   //负责人id
                    pageSize: 5,
                    pageNum: 1,
                },
                data: []
            }
        },
        components: {},
        created: function () {
            this.getData();
        },
        methods: {
            getData() {
                this.$postData("getByService", this.employee, {}).then(res => {
                    if (res.status === 200) {
                       ;
                        this.data = res.data.list;
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.userModal = false;
            },
            handleClearCurrentRow() {
                this.$refs.currentRowTable.clearCurrentRow();
            },
            onChange(index) {
                console.log(index);
                this.employee.pageNum = index;
                this.getData();
            },
            onCurrentChange(currentRow, oldCurrentRow) {
                console.log(currentRow.no);
                this.$emit('init-choose', currentRow);

            }
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 150px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

