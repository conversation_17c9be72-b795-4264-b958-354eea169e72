<!--

获取优惠券批次列表，单选

-->
<template style="background-color: #000">
    <div>
        <el-input
                v-model="couponQuery.name"
                placeholder="名称"
                style="width:180px"
                class="handle-input mr10"
        ></el-input>
        <el-button type="primary" @click="query()">搜索</el-button>
        <el-table
                ref="singleTable"
                :data="list"
                highlight-current-row
                @current-change="change"
                @selection-change="change"
                style="width: 100%">
            <el-table-column
                    type="index"
                    width="50">
            </el-table-column>
            <el-table-column
                    property="name"
                    label="名称"
                    width="120">
            </el-table-column>
            <el-table-column
                    property="validity"
                    label="有效期"
                    width="120">
            </el-table-column>
            <el-table-column
                    property="publishNum"
                    label="数量">
            </el-table-column>
        </el-table>
        <Page :total="pageInfo.total" @on-change="onChange" :show-total="true" :page-size="5"/>
    </div>
</template>


<script>
    export default {
        data() {
            return {
                userModal: false,
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                couponQuery:{
                    name: null,
                    id: null,
                    pageSize: 5,
                    pageNum:1
                },
                list: null
            }
        },
        components: {},
        created: function () {
            this.getData();
        },
        methods: {
            getData() {
                this.$postData("couponGroup_getByList", this.couponQuery ,{}).then(res => {
                    if (res.status == 200) {
                       ;
                        this.list=res.data.list;
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            setCurrent(row) {
                this.$refs.singleTable.setCurrentRow(row);
            },
            change(selection) {
            console.log(selection);
                this.$emit('init-choose', selection);
            },

            handleClearCurrentRow() {
                this.$refs.currentRowTable.clearCurrentRow();
            },
            onChange(index) {
                this.couponQuery.pageNum = index;
                this.getData();
            },
            query(){
                this.couponQuery.pageNum=1;
                this.pageInfo.current=1
                this.getData()
            }
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 150px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

