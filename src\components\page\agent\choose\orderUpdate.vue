<template style="background-color: #000">

            <el-tabs type="card" :stretch="true" >
                <el-tab-pane label="基本信息">
                <Row>
                <Col span="24">

                    <el-form ref="form" :model="dom" :rules="ruleValidate">
                        <Row>
                            <Col span="12">
                                <el-form-item   prop="agentName">
                                    <div class="label-name">经纪人:</div>
                                    <el-autocomplete
                                            style="width: 70%"
                                            v-model="dom.agentName"
                                            :fetch-suggestions="querySearchAsync"
                                            placeholder="请输入内容"
                                            @select="handleSelect"
                                            :trigger-on-focus="false">
                                    </el-autocomplete>
                                </el-form-item >
                                <el-form-item   prop="name">
                                    <div class="label-name">用户名:</div>
                                    <el-autocomplete
                                            style="width: 70%"
                                            v-model="dom.name"
                                            :fetch-suggestions="queryMember"
                                            placeholder="请输入内容"
                                            @select="handleSelectMember"
                                            :trigger-on-focus="false">
                                    </el-autocomplete>
                                </el-form-item >
                                <el-form-item   prop="realName">
                                    <div class="label-name">所属保姆:</div>
                                    <el-autocomplete
                                            style="width: 70%"
                                            v-model="dom.realName"
                                            :fetch-suggestions="querybaomu"
                                            placeholder="请输入内容"
                                            @select="handleSelectBaomu"
                                            :trigger-on-focus="false">
                                    </el-autocomplete>
                                </el-form-item >


                                <el-form-item   prop="billNo">
                                    <div class="label-name">发票编号:</div>
                                    <el-input placeholder="请输入编号" v-model="dom.billNo" style="width: 70%" readonly>
                                    </el-input>
                                </el-form-item >

                                <el-form-item   prop="totalAmount">
                                    <div class="label-name">订单金额:</div>
                                    <el-input placeholder="请输入订单金额" v-model="dom.totalAmount"  style="width: 70%" readonly>
                                    </el-input>
                                </el-form-item >
                                <el-form-item   prop="remark">
                                    <div class="label-name">备注:</div>
                                    <el-input placeholder="请输入内容" type="textarea" v-model="dom.remark"  style="width: 70%" readonly>
                                    </el-input>
                                </el-form-item >
                            </Col>
                            <Col span="12">
                                <el-form-item   prop="amount">
                                    <div class="label-name">付款金额:</div>
                                    <el-input placeholder="请输入付款金额" v-model="dom.amount"  style="width: 70%" readonly>
                                    </el-input>
                                </el-form-item >
                                <el-form-item   prop="realTotalAmount">
                                    <div class="label-name">最终金额:</div>
                                    <el-input placeholder="请输入最终金额" v-model="dom.realTotalAmount"  style="width: 70%" readonly>
                                    </el-input>
                                </el-form-item >

                                <el-form-item   prop="productName">
                                    <div class="label-name">订单服务:</div>
                                    <el-input placeholder="请输入订单服务" v-model="dom.productName"  style="width: 70%" readonly>
                                    </el-input>
                                </el-form-item >
                                <el-form-item   prop="isPayoff">
                                    <div class="label-name">结算状态:</div>

                                    <el-select v-model="dom.isPayoff" clearable placeholder="请选择"  style="width: 70%" readonly>
                                        <el-option
                                                readonly
                                                v-for="item in isPayoffoptions"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item >
                                <el-form-item   prop="startTime" label="">
                                    <div class="label-name">服务时间:</div>
                                    <el-date-picker
                                            readonly
                                            style="width: 70%"
                                            v-model="dom.startTime"
                                            type="datetime"
                                            value-format="yyyy-MM-dd HH:mm:ss"
                                            placeholder="开始时间">
                                    </el-date-picker>
                                </el-form-item >
                                <el-form-item   prop="skuFixedTime" label="">
                                    <div class="label-name">下单时间:</div>
                                    <el-date-picker
                                            readonly
                                            style="width: 70%"
                                            v-model="dom.skuFixedTime"
                                            type="datetime"
                                            value-format="yyyy-MM-dd HH:mm:ss"
                                            placeholder="下单时间">
                                    </el-date-picker>
                                </el-form-item >
                            </Col>


                        </Row>

                        <div style="margin-right: 100px;float: right">
                            <Button type="primary" @click="save('form')">确定</Button>
                            <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                        </div>
                    </el-form>
                    </Col>


            </Row>
                </el-tab-pane>
<!--                <el-tab-pane label="配置管理">配置管理</el-tab-pane>-->
            </el-tabs>

</template>


<script>

    export default {
        props:['model'],
        data() {
            return {
                isPayoffoptions:[{
                    value:false,
                    label: '未结算'
                }, {
                    value: true,
                    label: '已结算'
                },],
                dom:this.model,
                agentName:null,
                agentNo:null,
                restaurants:[],
                agentmodel:{
                    RealName:null,
                    No:null,
                },

                ruleValidate: {
                    name: [
                        {required: true, message: '请输入编号', trigger: 'change'}
                    ],
                    street: [
                        {required: true, message: '请选择名称', trigger: 'change'}
                    ],
                    lng: [
                        {required: true, message: '请选择联系方式', trigger: 'change'}
                    ],
                    lat: [
                        {required: true, message: '请输入地址', trigger: 'blur'}
                    ],

                },
            }
        },
        components: {
        },
        created: function () {
            console.log(this.dom)
        },
        methods: {

            save(name) {
                console.log(this.dom)
                        this.$postData("update_order", this.dom, {}).then(res => {
                            if (res.status == 200) {
                                this.$Message.success('修改成功');
                                this.chooseThisModel();
                            } else {
                                this.$message.error("修改失败，" + res.msg);
                            }
                        })
            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.dom=null;
                this.$emit('init-choose', "");
            },
            handleSelect(item) {
                this.dom.agentNo=item.name
            },
            handleSelectBaomu(item) {
                this.dom.productId=item.name
            },
            handleSelectMember(item) {
                this.dom.memberId=item.name
            },
            queryMember(queryString, cb) {
                this.restaurants=[]
                this.$postUrl("member_list",this.dom.name ,{}, {}).then(res => {
                    if (res.status == 200) {

                        this.list = res.data;
                        this.list.forEach((item, index, arr) => {
                            var a={}
                            a.value=item.RealName;// +/"+item.Account;
                            a.name=item.Id;
                            this.restaurants.push(a);
                        });
                        this.list=[]
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                var restaurants = this.restaurants;
                var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants;
                cb(restaurants);
            },
            querybaomu(queryString, cb) {
                this.restaurants=[]
                this.agentmodel.RealName=this.dom.realName;
                this.$postData("baomu_list", this.agentmodel, {}).then(res => {
                    if (res.status == 200) {
                        this.list = res.data;
                        this.list.forEach((item, index, arr) => {
                            var a={}
                            a.value=item.realName;
                            a.name=item.id;
                            this.restaurants.push(a);
                        });
                        this.list=[]
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                var restaurants = this.restaurants;
                var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants;
                cb(restaurants);
            },
            querySearchAsync(queryString, cb) {
                this.restaurants=[]
                this.agentmodel.RealName=this.dom.agentName;
                this.$postData("agent_list", this.agentmodel, {}).then(res => {
                    if (res.status == 200) {
                        this.list = res.data;
                        this.list.forEach((item, index, arr) => {
                            var a={}
                            a.value=item.realName;
                            a.name=item.no;
                            this.restaurants.push(a);
                        });
                        this.list=[]
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                var restaurants = this.restaurants;
                var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants;
                cb(restaurants);
            },
            createStateFilter(queryString) {
                return (state) => {
                    return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                };
            },

        },

    }
</script>
<style>
    {
        background: #409eff;
        color: #fff;
    }
</style>

<style scoped>
    .label-name{
        float: left;
        text-align: center;
        width: 15%;
    }
    .el-autocomplete{
        width: 80%;
    }
    .startWord{
        background-color: #F5F7FA;
        color: #909399;
        vertical-align: middle;

        position: relative;
        border: 1px solid #DCDFE6;
        border-radius: 4px;
        padding: 0 20px;
        width: 1px;
        white-space: nowrap;
        float: left;
        padding-right: 50px;
    }
    .ivu-col-span-9{
        padding-top: 30px;
    }
    .el-input-group{
        width: 80%;
    }
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

