<template>
  <div class="container">
    <div class="handle-box">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="选择门店">
          <el-select ref="storeNameSel" filterable v-model="dom.storeId" placeholder="请选择门店" clearable>
            <el-option
                v-for="(item,index) in storeList"
                :key="index"
                :label="item.name"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间">
          <el-date-picker
              v-model="time"
              type="daterange"
              range-separator="至"
              value-format="yyyy-MM-dd HH:mm:ss"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="onQuery" v-loading="loading">
            查询
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-refresh" @click="reset()" v-loading="loading">
            重置
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-download" type="primary" plain @click="exportExcel" v-loading="loading">
            导出
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="dialogFormVisible = true" v-loading="loading">
            新增奖惩
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-row justify="start">
        <el-col :span="24">
          <el-card shadow="hover">
            保证金汇总：<span class="allAmount">￥{{allMoney}}</span>
          </el-card>
        </el-col>
    </el-row>

    <el-table :data="dataList" v-loading="loading" style="width: 100%"
              :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
      <el-table-column
          label="序号"
          type="index"
          align="center">
      </el-table-column>
      <el-table-column
          prop="storeName"
          label="门店名称"
          align="center"
      ></el-table-column>
      <el-table-column
          label="类型"
          align="center">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.type === 1">增加</el-tag>
          <el-tag type="danger" v-if="scope.row.type === 2">扣减</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          label="缘由"
          prop="ruleName"
          align="center">
      </el-table-column>
      <el-table-column
          prop="originAmount"
          label="保证金金额"
          align="center">
      </el-table-column>
        <el-table-column
            prop="changeAmount"
            label="变更金额"
            align="center">
        </el-table-column>
          <el-table-column
              prop="amount"
              label="剩余保证金"
              align="center">
          </el-table-column>
            <el-table-column
                prop="remark"
                label="备注"
                align="center">
      </el-table-column>
      <el-table-column
          label="关联订单号"
          prop="transactionNum"
          align="center">
      </el-table-column>
      <el-table-column
          label="操作人"
          prop="createName"
          align="center">
      </el-table-column>
      <el-table-column
          label="操作时间"
          prop="createDate"
          align="center">
      </el-table-column>
    </el-table>
    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChangePage"
            :current="dom.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="dom.size"/>
    </div>

    <el-dialog title="奖惩录入" :visible.sync="dialogFormVisible">
      <el-form :model="addLog" :rules="rules" ref="ruleForm">
        <el-form-item label="选址门店" prop="storeId">
          <el-select filterable v-model="addLog.storeId" placeholder="请选择门店" clearable>
            <el-option
                v-for="(item,index) in storeList"
                :key="index"
                :label="item.name"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
<!--        <el-form-item label="奖惩类型" prop="type">-->
<!--          <el-select filterable v-model="addLog.type" placeholder="请选择类型" clearable>-->
<!--            <el-option-->
<!--                v-for="(item,index) in typeList"-->
<!--                :key="index"-->
<!--                :label="item.name"-->
<!--                :value="item.id">-->
<!--            </el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
                <el-form-item label="奖惩列表">
                  <el-select filterable v-model="ruleId" placeholder="请选择类型" clearable @change="changeRule">
                    <el-option
                        v-for="(item,index) in agentStoreRuleList"
                        :key="index"
                        :label="item.ruleTitle"
                        :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
        <div v-if="rule.id">
          <el-form-item label="类型">
            <el-tag type="danger" v-if="rule.ruleType === 0">惩罚</el-tag>
            <el-tag type="success" v-if="rule.ruleType === 1">奖励</el-tag>
          </el-form-item>
          <el-form-item label="说明">
           <div>{{rule.ruleContent}}</div>
          </el-form-item>
          <el-form-item label="变更金额(惩罚请用负号表示)" >
            <el-input-number v-model="addLog.changeAmount"></el-input-number>
          </el-form-item>
        </div>
        <el-form-item label="关联单据" prop="transactionNum">
          <el-input v-model="addLog.transactionNum"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="addLog.remark"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false,resetForm()">取 消</el-button>
        <el-button type="primary" @click="addLogInfo()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "earnestMoney",
  data() {
    return {
      dialogFormVisible:false,
      loading: false,
      storeList: [],
      time:[],
      ruleId:null,
      allMoney:0,
      dom: {
        size: 15,
        current: 1,
        storeId:null,
        startDate:null,
        endDate:null
      },
      addLog:{
        storeId:null,
        type:null,
        transactionNum:null,
        remark:null,
        changeAmount:null,
        ruleId:null
      },
      rule:{},
      dataList: [],
      agentStoreRuleList:[],
      typeList:[{id:1,name:'增加'},{id:2,name: '扣减'}],
      pageSizeOpts: [10, 20, 50, 100, 150, 200],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      rules: {
        storeId: [
          { required: true, message: '请选择门店', trigger: 'change' }
        ],
        remark: [
          { required: true, message: '请填写备注', trigger: 'blur' }
        ],
        transactionNum: [
          { required: true, message: '请填写流水单据', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.listAgentStoreRule()
    this.getData();
    this.getStoreList();
  },
  methods:{
    getEarnestMoneyAll(){
      this.$postData("getEarnestMoneyAll", this.dom).then(res => {
        if (res.status === 200) {
          this.allMoney = res.data;
        }
      })
    },
    changeRule(e){
     this.ruleId = e;
     this.rule = {};
     if (this.ruleId){
       this.agentStoreRuleList.forEach(v =>{
         if (v.id === e){
           this.rule = v;
         }
       })
       if (this.rule.ruleType === 0){
         this.addLog.changeAmount = Number(-this.rule.ruleAmount);
       } else {
         this.addLog.changeAmount = Number(this.rule.ruleAmount);
       }
     }

    },
    listAgentStoreRule(){
      this.$postData("listAgentStoreRule", {search:''}, {}).then(res => {
        if (res.status == 200) {
          this.agentStoreRuleList = res.data
          // 追加编辑状态位

        } else {
          this.$message.error(res.msg)
          this.agentStoreRuleList = []
        }
      })
    },
    resetForm(){
      this.$refs['ruleForm'].resetFields();
    },
    addLogInfo(){
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          if (!this.rule.id){
            this.$message.error('请选择奖惩类型');
          }
          this.addLog.ruleId = this.rule.id;

          switch(this.rule.ruleType){
            case 0:
              this.addLog.type = 2;
              break
            case 1:
              this.addLog.type = 1;
              break
            default:
              break
          }
          this.addLog.creater = localStorage.getItem('id');
          this.$postData("addEarnestMoney", this.addLog).then(res => {
            if (res.status === 200) {
              this.dialogFormVisible = false;
              this.$message.success(res.data);
              this.$refs['ruleForm'].resetFields();
              this.getData();
            } else {
              this.$message.error(res.msg);
            }
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    getStoreList(){
      this.$getData("earnestMoneyStoreList").then(res => {
        if (res.status === 200) {
          this.storeList = res.data;
        }
      })
    },
    reset() {
      this.dom.size = 15;
      this.dom.current = 1;
      this.dom.startDate = null;
      this.dom.endDate = null;
      this.dom.storeId = null;
      this.getData();
    },
    onChangePage(index) {
      this.dom.current = index;
      this.getData();
    },
    onPageSizeChange(size) {
      this.dom.size = size;
      this.getData();
    },
    getData() {
      this.getEarnestMoneyAll();
      this.loading = true;
      this.$postData("earnestMoneyPageList", this.dom).then(res => {
        if (res.status === 200) {
          this.loading = false;
          this.dataList = [];
          this.dataList = res.data.records;
          this.pageInfo.total = res.data.total;
        }
      })
    },
    exportExcel() {
      this.loading = true;
      this.$postData("earnestMoneyExportExcel", JSON.stringify(this.dom), {
        responseType: "arraybuffer"
      }).then(res => {
        this.blobExport({
          tableName: "保证金流水记录",
          res: res
        });
      });
    },
    onQuery() {
      if (this.time){
        this.dom.startDate = this.time[0];
        this.dom.endDate = this.time[1];
      }
      this.getData();
    },
    blobExport({tableName, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tableName + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
      const that = this;
      setTimeout(function () {
        that.loading = false;
      }, 2500);

    },
  }

}
</script>

<style scoped>
.allAmount {
  font-weight: bold;
  /*padding-left: 10px;*/
  color: red;
  font-size: 24px;
}
</style>
