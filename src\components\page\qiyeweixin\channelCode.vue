<template>
  <div class="container">
    <div class="handle-box">
      <el-form v-loading="loading" :inline="true" class="demo-form-inline">
        <el-form-item>
          <el-button type="success" plain icon="el-icon-plus" @click="createChannelCode = true">创建渠道码</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="list2" :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" key="list2"
                class="table" border style="width: 100%">
        <el-table-column prop="name" label="名称" :show-overflow-tooltip="true" width="90">
        </el-table-column>
        <el-table-column prop="type" label="类型" width="90">
          <template slot-scope="scope">
            <el-tag type="success" v-if=" scope.row.type===1">主页</el-tag>
            <el-tag v-if=" scope.row.type===2">产品详情</el-tag>
            <el-tag type="warning" v-if=" scope.row.type===3">活动主页</el-tag>
            <el-tag type="info" v-if=" scope.row.type===4">产品推荐</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createrName" label="创建者"
                         :show-overflow-tooltip="true" width="80">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" :formatter="dateFormat"
                         :show-overflow-tooltip="true" width="150">
        </el-table-column>
        <el-table-column property="content" label="二维码" width="250">
          <template slot-scope="scope">
            <el-popover trigger="hover" placement="top" width="250">
              <el-image :src=scope.row.img fit="contain"></el-image>
              <div slot="reference">
                <p size="medium" style="white-space: nowrap;overflow: hidden;text-overflow:ellipsis;">
                  {{ scope.row.img }}</p>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="large" type="primary" @click="downloadUrl(scope.row)">
              下载
            </el-button>
            <el-button size="large" type="success"
                       class="tag-read" :data-clipboard-text="scope.row.img" @click="copyUrl">
              复制
            </el-button>
            <el-button size="large" type="info" @click="statistics(scope.row.id)">
              统计
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="pageInfo.total"
              @on-change="onChange"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onPageSizeChange"
              :page-size="pageInfo.size"/>
      </div>
    </div>
    <el-dialog title="建立渠道码" :visible.sync="createChannelCode" center width="500px">
      <el-form ref="form" label-width="100px" style="background:#FFFFFF" label-position="right">
        <el-form-item label="名称" required>
          <el-input v-model="dom.name" maxlength="10" clearable @keyup.native="change" style="width:200px"></el-input>
        </el-form-item>
        <el-form-item label="类型" required>
          <el-radio-group v-model="dom.type">
            <el-radio-button :label="1">主页</el-radio-button>
            <el-radio-button :label="2">产品详情</el-radio-button>
            <el-radio-button :label="3">活动主页</el-radio-button>
            <el-radio-button :label="4">产品推荐</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="渠道" required v-if="dom.type!==3">
          <el-select v-model="dom.channel" clearable filterable placeholder="请选择" @blur="selectBlur">
            <el-option v-for="item in list3" :key="item.value" :label="item.text" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="来源" required v-if="dom.type===1||dom.type===2">
          <el-radio-group v-model="dom.source">
            <el-radio-button :label="'appminiprogram'" v-if="dom.type!==3">小羽佳</el-radio-button>
            <el-radio-button :label="'aygj'" v-if="dom.type===1">阿姨管家</el-radio-button>
            <el-radio-button :label="'bmgj'" v-if="dom.type===1">保姆管家</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="产品" v-if="dom.type===2" required>
          <el-select v-model="dom.productId" clearable filterable placeholder="请选择">
            <el-option v-for="item in list" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="活动" v-if="dom.type===3" required>
          <el-select v-model="code" clearable filterable placeholder="请选择">
            <el-option v-for="item in list4" :key="item.code" :label="item.name" :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
         <el-button @click="createChannelCode = false">取 消</el-button>
         <el-button type="primary" @click="addChannelCode">确 定</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from "moment";
import Clipboard from "clipboard";
import {pinyin} from 'pinyin-pro';

export default {
  name: "channelCode",
  inject: ['reload'],
  data() {
    return {
      loading: false,
      createChannelCode: false,
      code: null,
      list: [],
      list2: [],
      list3: [],
      list4: [],
      pageSizeOpts:[10,20,30,50,100],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      dom: {
        name: null,
        type: null,
        source: null,
        channel: null,
        path: null,
        scene: null,
        productId: null,
        title: "小羽佳家政",
        creater: localStorage.getItem("id"),
        createrName: localStorage.getItem("realName"),
      },
    };
  },
  created() {
    this.getProduct();
    this.getChannelCode();
    this.getChannelByIsDelete();
    this.getMarketingByEndTime();
  },
  methods: {
    selectBlur(e) {
      this.dom.channel = e.target.value
    },
    statistics(id) {
      this.$router.push({path: '/channelCodeStatistics', query: {id: id}});
    },
    getMarketingByEndTime() {
      this.$getData("getMarketingByEndTime",).then(res => {
        if (res.status === 200) {
          this.list4 = res.data;
        }
      })
    },
    getChannelByIsDelete() {
      this.$getData("getChannelByIsDelete",).then(res => {
        if (res.meta.state === 200) {
          this.list3 = res.data;
        }
      })
    },
    // 页码大小
    onPageSizeChange(size) {
      this.dom.size = size;
      this.getChannelCode();
    },
    // 跳转页码
    onChange(index) {
      this.dom.current = index;
      this.getChannelCode();
    },
    change() {
      if (this.dom.name != null) {
        let data1 = pinyin(this.dom.name, {toneType: 'none'}).toString();
        this.dom.channel = data1.replace(/\s*/g, "");
      }
    },
    getProduct() {
      this.$getData("getProduct",).then(res => {
        if (res.status === 200) {
          this.list = res.data;
        }
      })
    },
    getChannelCode() {
      this.$postData("getChannelCode", this.dom).then(res => {
        if (res.status === 200) {
          this.list2 = res.data.records;
          this.pageInfo.total = res.data.total;
          this.pageInfo.pages = res.data.pages;
        }
      })
    },
    addChannelCode() {
      if (this.dom.name === null || this.dom.type === null) {
        return this.$message({message: '带红色星号不能为空', type: 'warning'});
      }
      if ((this.dom.type === 1 || this.dom.type === 2) && (this.dom.channel === null || this.dom.source === null)) {
        return this.$message({message: '带红色星号不能为空', type: 'warning'});
      }
      if (this.dom.type === 1) {
        this.dom.productId === null;
        if (this.dom.source === "appminiprogram") {
          this.dom.path = "pages/index/pageMain";
        } else if (this.dom.source === "aygj") {
          this.dom.path = null;
        } else {
          this.dom.path = "pages/index";
        }
        this.dom.scene = "a/3*c/" + this.dom.channel;
      }
      if (this.dom.type === 2) {
        if (this.dom.productId === null) {
          return this.$message({message: '产品不能为空', type: 'warning'});
        }
        this.dom.path = "pages/product/productDetail";
        this.dom.scene = "a/3*p/" + this.dom.productId + "*c/" + this.dom.channel;
      }
      if (this.dom.type === 3) {
        this.dom.source = "m.xiaoyujia.com";
        this.dom.channel = null;
        this.dom.path = "pages/product/activity";
        this.dom.scene = "code=" + this.code;
      }
      if (this.dom.type === 4) {
        this.dom.source = "m.xiaoyujia.com";
        this.dom.path = "pages/product/aiServiceCoupon ";
        this.dom.scene = "a/3*c/" + this.dom.channel;
      }
      if (this.dom.scene.length > 30) {
        return this.$message({message: '渠道参数过长', type: 'warning'});
      }
      this.loading = true;
      this.createChannelCode = false;
      this.$postData("addChannelCode", this.dom).then(res => {
        this.loading = false;
        if (res.status === 200) {
          if (res.data > 0) {
            this.$message({message: '创建成功', type: 'success'});
            this.reload();
          } else {
            this.$message({message: "创建失败", type: 'error'});
          }
        } else {
          this.$message({message: res.msg, type: 'error'});
        }
      })
    },
    dateFormat(row, column) {
      var date = row[column.property];
      if (date === undefined) {
        return '';
      }
      return moment(date).format("YYYY-MM-DD HH:mm:ss");
    },
    //下载二维码
    downloadUrl(url) {
      let a = document.createElement('a');
      a.download = url.name;
      a.href = url.img;
      a.click();
    },
    //复制链接
    copyUrl() {
      var clipboard = new Clipboard('.tag-read')
      clipboard.on('success', e => {
        this.$message({
          message: '复制成功',
          type: 'success'
        });
        // 释放内存
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        this.$message.error('该浏览器不支持自动复制');
        // 不支持复制
        // 释放内存
        clipboard.destroy()
      })
    },
  },
}
</script>

<style scoped>

.pagination {
  margin: 20px 0;
  text-align: right;
}
</style>