<template>
    <el-row style="padding-left: 20px;padding-right: 20px">
        <el-col :span="24">
            <el-dialog title="添加签名公章图片" :visible.sync="dialogFormVisible">
                <el-form :model="form">
                    <el-form-item label="认证图片名称" >
<!--                        <el-input v-model="form.siginType" autocomplete="off"></el-input>-->
                        <el-select v-model="form.siginType" placeholder="请选择">
                            <el-option label="法人签名" value=3 v-show="this.dom.siginType==3"></el-option>
                            <el-option label="公章" value=4 v-show="this.dom.siginType==3"></el-option>
                            <el-option label="个人签名" value=2 v-show="this.dom.siginType==2"></el-option>
                            <el-option label="保姆签名" value=1 v-show="this.dom.siginType==1"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-upload
                            :limit="1"
                            class="upload-demo"
                            action="https://biapi.xiaoyujia.com/files/uploadFiles"
                            list-type="picture"
                            :on-success="handleAvatarSuccess"><!--:show-file-list="false"-->
                        <el-button size="mini" type="primary" @click="dialogFormVisible = true" >选择图片</el-button>
                    </el-upload>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogFormVisible = false">取 消</el-button>
                    <el-button type="primary" @click="addcontractSigin">确 定</el-button>
                </div>
            </el-dialog>

            <el-button size="mini" type="primary" @click="dialogFormVisible = true"   style="float: right" v-show="showbtn">添加图片</el-button>
        </el-col>

        <el-col :span="12" v-show="this.dom.siginType==3">
            <h3>已有法人图片:</h3>
            <el-dialog :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
            <el-col :span="12" v-for="(item, index) in sigins" :key="index">
                <el-card style="margin: 20px;">
                    <el-image
                            :src="item.siginUrl"
                            @click="handlePictureCardPreview(item.siginUrl)"
                            class="image"></el-image>
                    <div style="padding: 14px;">
                    <span>
                        <span v-if="item.siginType==3">法人签名</span>
                        <span v-if="item.siginType==4">公章</span>
                        <el-tag v-if="item.status==1" type="success">发布</el-tag>
                        <el-tag v-else-if="item.status!=1" type="warning">暂存</el-tag>
                    </span>
                        <div class="bottom clearfix">

                            <el-button type="danger" class="delbutton" @click="del(item.id)">删除</el-button>
                            <el-button v-if="item.status==1" type="info" class="delbutton" @click="up(item,0)">暂存</el-button>
                            <el-button v-if="item.status!=1" type="primary" class="delbutton" @click="up(item,1)">发布</el-button>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-col>

        <el-col :span="12" v-show="this.dom.siginType==3">
            <h3>已有公章图片:</h3>
            <el-dialog :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
            <el-col :span="12" v-for="(item, index) in sigins4" :key="index">
                <el-card style="margin: 20px;">
                    <el-image
                            :src="item.siginUrl"
                            @click="handlePictureCardPreview(item.siginUrl)"
                            class="image"></el-image>
                    <div style="padding: 14px;">
                    <span>
                        <span v-if="item.siginType==3">法人签名</span>
                        <span v-if="item.siginType==4">公章</span>
                        <el-tag v-if="item.status==1" type="success">发布</el-tag>
                        <el-tag v-else-if="item.status!=1" type="warning">暂存</el-tag>
                    </span>
                        <div class="bottom clearfix">

                            <el-button type="danger" class="delbutton" @click="del(item.id)">删除</el-button>
                            <el-button v-if="item.status==1" type="info" class="delbutton" @click="up(item,0)">暂存</el-button>
                            <el-button v-if="item.status!=1" type="primary" class="delbutton" @click="up(item,1)">发布</el-button>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-col>
        <el-col :span="24" v-show="this.dom.siginType==2">
            <h3>已有个人签名图片:</h3>
            <el-dialog :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
            <el-col :span="12" v-for="(item, index) in sigins2" :key="index">
                <el-card style="margin: 20px;">
                    <el-image
                            :src="item.siginUrl"
                            @click="handlePictureCardPreview(item.siginUrl)"
                            class="image"></el-image>
                    <div style="padding: 14px;">
                    <span>
                        <span v-if="item.siginType==2">个人签名</span>
                        <span v-if="item.siginType==4">公章</span>
                        <el-tag v-if="item.status==1" type="success">发布</el-tag>
                        <el-tag v-else-if="item.status!=1" type="warning">暂存</el-tag>
                    </span>
                        <div class="bottom clearfix">

                            <el-button type="danger" class="delbutton" @click="del(item.id)">删除</el-button>
                            <el-button v-if="item.status==1" type="info" class="delbutton" @click="up(item,0)">暂存</el-button>
                            <el-button v-if="item.status!=1" type="primary" class="delbutton" @click="up(item,1)">发布</el-button>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-col>
        <el-col :span="24" v-show="this.dom.siginType==1">
            <span class="label-name">关联保姆:</span>
            <el-select
                    style="width: 70%"
                    v-model="baomuno"
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请输入保姆名称搜索选择"
                    :remote-method="remoteMethod"
                    @change="changbaomu"
                    :loading="employeeloading">
                <el-option
                        v-for="item in employeeoptions"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value">
                </el-option>
            </el-select>
            <el-button type="primary" @click="getbaomu">查询</el-button>
            <h3 style="padding-top: 20px">已有保姆签名图片:</h3>
            <el-dialog :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
            <el-col :span="12" v-for="(item, index) in sigins1" :key="index">
                <el-card style="margin: 20px;">
                    <el-image
                            :src="item.siginUrl"
                            @click="handlePictureCardPreview(item.siginUrl)"
                            class="image"></el-image>
                    <div style="padding: 14px;">
                    <span>
                        <span v-if="item.siginType==2">法人签名</span>
                        <span v-if="item.siginType==4">公章</span>
                        <span v-if="item.siginType==1">保姆签名</span>
                        <el-tag v-if="item.status==1" type="success">发布</el-tag>
                        <el-tag v-else-if="item.status!=1" type="warning">暂存</el-tag>
                    </span>
                        <div class="bottom clearfix">

                            <el-button type="danger" class="delbutton" @click="del(item.id)">删除</el-button>
                            <el-button v-if="item.status==1" type="info" class="delbutton" @click="up(item,0)">暂存</el-button>
                            <el-button v-if="item.status!=1" type="primary" class="delbutton" @click="up(item,1)">发布</el-button>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-col>
    </el-row>
</template>

<script>
    export default {
        name: "contractSiginCommom",
        props:['model'],
        data() {
            return {
                showbtn:true,
                employeeloading:false,
                baomuno:null,
                employeeId:null,
                employeeoptions:[],
                sigins:[],
                sigins1:[],
                sigins2:[],
                sigins4:[],
                form:{
                    employeeId:null,
                    status:0,
                    siginType:null,
                    siginUrl:null,
                },
                dialogVisible:false,
                dialogImageUrl:false,
                dialogFormVisible:false,
                dom:this.model,

            }
        },
        created() {
            console.log(this.dom)
            if (this.dom.siginType==3){
                this.getData(3)
                this.getData(4)
            }if (this.dom.siginType==2){
                this.form.employeeId=localStorage.getItem("id")
                this.form.siginType='2'
                this.getData(2)

            }if (this.dom.siginType==1){
                this.form.siginType='1'
                if (this.dom.employeeId==null){
                    this.$message.success("请先选择一个保姆");
                    this.showbtn=false
                }else {
                    this.employeeId=this.dom.employeeId
                    this.getData(1)
                }


            }

        },
        methods: {
            getbaomu(){
                console.log(this.employeeId)
                this.getData(1)
            },
            remoteMethod(query) {
                this.employeeoptions = []
                if (query !== '') {
                    this.employeeloading = true;
                    this.$getData("get_baomubyno",{no:query,storeId:localStorage.getItem("storeId")}).then(res => {
                        if (res.status == 200) {
                            this.employeeloading = false;
                            res.data.forEach((item, index, arr) => {
                                console.log(item)
                                var a={}
                                a.id=item.id
                                a.value=item.id;
                                a.label=item.realName;
                                this.employeeoptions.push(a);
                            });
                            console.log(this.employeeoptions)
                        } else {
                            this.$message.error("修改失败，" + res.msg);
                        }
                    })
                } else {
                    this.employeeoptions = [];
                }
            },
            changbaomu(item){
                this.employeeId=item
                this.form.employeeId=this.employeeId
                console.log(this.form)
            },
            del(id){
                this.$postUrl("del_contractSigin", id, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.error("删除成功，" + res.msg);
                        if (this.dom.siginType==3){

                            this.getData(3)
                            this.getData(4)
                        }if (this.dom.siginType==2){

                            this.getData(2)

                        }if (this.dom.siginType==1){

                            this.getData(1)

                        }

                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })
            },
            up(row,status){
                row.status=status
                this.$postData("up_contractSigin",row ,{}).then(res => {
                    if (res.status == 200) {
                        this.$message.success("变更成功，" + res.msg);
                        this.dialogFormVisible=false;
                        if (this.dom.siginType==3){

                            this.getData(3)
                            this.getData(4)
                        }
                        if (this.dom.siginType==2){

                            this.getData(2)
                        }
                        if (this.dom.siginType==1){

                            this.getData(1)
                        }

                    } else {
                        this.$message.error("变更失败，" + res.msg);
                    }
                })
            },
            addcontractSigin(){
                this.$postData("add_contractSigin", this.form, {}).then(res => {
                    if (res.status == 200) {

                        if (this.dom.siginType==3){

                            this.getData(3)
                            this.getData(4)
                        }if (this.dom.siginType==2){

                            this.getData(2)

                        }if (this.dom.siginType==1){

                            this.getData(1)

                        }
                        this.$message.success("添加成功，" + res.msg);
                        this.dialogFormVisible=false;
                    } else {
                        this.$message.error("添加失败，" + res.msg);
                    }
                })
            },
            handlePictureCardPreview(url) {
                this.dialogImageUrl = url;
                this.dialogVisible = true;
            },
            handleAvatarSuccess(res, file){
                this.form.siginUrl=res.data;
            },
            getData(siginType) {
                let show={
                    siginType:siginType
                }
                if (siginType==2){
                    show.employeeId=localStorage.getItem("id")
                }
                if (siginType==1){
                    show.employeeId=this.employeeId
                }
                this.$postData("get_contractSigin", show, {}).then(res => {
                    if (res.status == 200) {

                        if (siginType==3){
                            this.sigins=res.data
                        }if (siginType==4){
                            this.sigins4=res.data
                        }if (siginType==2){
                            this.sigins2=res.data
                        }if (siginType==1){
                            this.showbtn=true
                            this.sigins1=res.data
                        }
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
        }
    }
</script>

<style scoped>
    .label-name{
        font-weight: bolder;
        font-size: 17px;
        padding-right: 20px;

    }
    .delbutton {
        float: right;
    }

    .image {
        width: 100%;
        display: block;
    }

    .clearfix:before,
    .clearfix:after {
        display: table;
        content: "";
    }

    .clearfix:after {
        clear: both
    }
</style>
