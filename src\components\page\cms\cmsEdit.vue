<template>
    <div class="table">

        <el-drawer
                title="素材选择"
                :visible.sync="drawer"
                direction="rtl"
                size="60%">
             <cmsMaterial type="edit"  @init-choose="initMaterial"></cmsMaterial>
        </el-drawer>
        <el-drawer
                title="内容封面"
                :visible.sync="coverDrawer"
                direction="rtl"
                size="60%">
            <cmsMaterial type="edit"  @init-choose="initCover"></cmsMaterial>
        </el-drawer>

        <div class="container">
            <el-card class="box-card">
                <el-form :model="dom" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <div class="boottm-btn" style="text-align: right">
                    <el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="submitForm('ruleForm')">立即{{dom.id==null?'创建':'保存'}}</el-button>
                            <el-button @click="resetForm('ruleForm')">重置</el-button>
                            <el-button @click="dom.status=0,submitForm('ruleForm')" type="danger" v-if="dom.id!==null&&dom.status==1">停用</el-button>
                            <el-button @click="dom.status=1,submitForm('ruleForm')" type="danger" v-if="dom.id!==null&&dom.status==0">恢复启用</el-button>
                        </el-form-item>
                    </el-form-item>
                </div>
                <el-form-item label="内容名称" prop="title">
                    <el-input v-model="dom.title"
                              style="width: 68%"
                              placeholder="请输入内容名称"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item label="文章发布人" prop="creatPerson">
                    <el-input v-model="dom.creatPerson"
                              style="width: 68%"
                              placeholder="文章发布人"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item label="排序比重" prop="orderNum">
                    <el-slider
                            style="width: 68%"
                            :min="0" :max="150"
                            v-model="dom.orderNum"
                            :marks="marks">
                    </el-slider>

                </el-form-item>
                <el-row>
                    <el-col :span="5">
                        <el-form-item label="是否全图" prop="delivery">
                            <el-switch v-model="dom.isAllImage" :active-value=1 :inactive-value=0></el-switch>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="是否可回复" prop="delivery">
                            <el-switch v-model="dom.isRevert" :active-value=1 :inactive-value=0></el-switch>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="5">
                        <el-form-item label="内容封面" >
                            <div @click="coverDrawer=true">
                                <el-image
                                        style="width: 100px; height: 150px;min-width: 150px;"
                                        :src="dom.cover" >
                                    <div slot="error" class="image-slot"  style="font-size: 25px;color: #ddd;padding: 50px;background: #f1f1f1;text-align: center;">
                                        <i class="el-icon-plus"></i>
                                    </div>
                                </el-image>
                            </div>

                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="内容摘要" prop="abstractInfo">
                            <el-input type="textarea" v-model="dom.abstractInfo" clearable  placeholder="选填，摘要会在订阅号消息、转发链接等文章外的场景显露，帮助读者快速了解内容，如不填写则默认抓取正文前54字"
                                      maxlength="120"
                                      :autosize="{ minRows: 6}"
                                      show-word-limit></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                    <el-form-item label="外部链接" prop="outLink">
                        <el-input v-model="dom.outLink"
                                  style="width: 68%"
                                  placeholder="外部链接"
                                  clearable
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="类型选择" prop="typeName">
                        <span  v-for="(item,index) in typeNameList" :key="index" style="margin: 5px">
                            <el-radio v-model="dom.typeName" :label="item" border >{{item}}</el-radio>
                        </span>
                        <el-divider></el-divider>
                        <el-input v-model="dom.typeName"
                                  style="width: 68%"
                                  placeholder="自定义类型"
                                  clearable
                        ></el-input>
                    </el-form-item>

            </el-form>
            </el-card>
            <quill-editor ref="myTextEditor" v-model="dom.cms" :options="editorOption"  ></quill-editor>
        </div>
    </div>
</template>
<script>
    import 'quill/dist/quill.core.css'
    import 'quill/dist/quill.snow.css'
    import 'quill/dist/quill.bubble.css'
    import { quillEditor } from 'vue-quill-editor';
    import cmsMaterial from "./cmsMaterial";
    //加入以下引用
    //quill编辑器的字体
    import * as Quill from 'quill'
    var fonts = ['SimSun', 'SimHei','Microsoft-YaHei','KaiTi','FangSong','Arial','Times-New-Roman','sans-serif'];
    var Font = Quill.import('formats/font');
    Font.whitelist = fonts; //将字体加入到白名单
    Quill.register(Font, true);


    export default {
        data(){
            return{
                id:this.$route.query.id,
                typeNameList:[],
                marks: {
                    0: '普通',
                    100: '热门',
                    145: '置顶'
                },
                dom:{
                    id:null,
                    title:null,
                    abstractInfo:null,
                    cover:null,
                    viewNum:null,
                    isAllImage:0,
                    isRevert:1,
                    creatDate:null,
                    putDate:null,
                    creatPerson:localStorage.getItem("realName"),
                    creatId:null,
                    type:null,
                    status:null,
                    updateTime:null,
                    cms:null,
                    orderNum:0,
                    outLink:null,
                    typeName:null,

                },
                rules: {
                    title: [
                        {required: true, message: '请输入名称', trigger: 'blur'}
                    ],
                    abstractInfo: [
                        {required: true, message: '请输入摘要', trigger: 'blur'},
                        {min: 10, max: 120, message: '长度在 10 到 120 个字符', trigger: 'blur'}
                    ],
                    url: [
                        {required: true, message: '上传信息不能为空', trigger: 'blur'}

                    ],
                },
                drawer:false,
                coverDrawer:false,
                content: '',
                editorOption: {
                    placeholder: "请在这里输入",
                    modules: {
                        imageDrop: true,
                        imageResize: {
                            displayStyles: {
                                backgroundColor: "black",
                                border: "none",
                                color: "white"
                            },
                            modules: ["Resize", "DisplaySize", "Toolbar"]
                        },
                        //其他配置，如quill-image-extend-module
                        toolbar: {
                            container: [
                                ["bold", "italic", "underline", "strike"],
                                [{ size: ["small", false, "large", "huge"] }],
                                [{'header': [1, 2, 3, 4, 5, 6, false]}],
                                [{'color': []}, {'background': []}],
                                [{ 'font': fonts }],
                                [{'indent': '-1'}, {'indent': '+1'}],
                                [{'align': []}],

                                [{'list': 'ordered'}, {'list': 'bullet'}],
                                ["blockquote", "code-block"],
                                ["link"],
                                ['image']
                            ]
                        },
                    }
                },
            }
        },
        components: {
            quillEditor,
            cmsMaterial
        },
        mounted(){
            this.$refs.myTextEditor.quill.getModule('toolbar').addHandler('image', this.imgHandler)
            this.getTypeNameList()
        },
        created(){
            if (this.id!==null && this.id!==undefined){
               this.getCmsInfo()
            }
        },
        methods:{
            getCmsInfo(){
                this.$getData("getCmsInfo", {id:this.id}).then(res => {
                    if (res.status == 200) {
                        this.dom = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getTypeNameList(){
                this.$getData("getTypeNameList", null).then(res => {
                    if (res.status == 200) {
                        this.typeNameList = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            submitForm(formName) {
                this.$refs['ruleForm'].validate((valid) => {
                    if (valid) {
                        this.$postData("saveCmsInfo", this.dom, {}).then(res => {
                            if (res.status == 200) {
                                this.$message.success("保存成功" );
                                if (this.id==null){
                                    this.resetForm('ruleForm')
                                }

                            } else {
                                this.$message.error("保存失败。请稍后重试");
                            }
                        })
                    } else {
                        this.$message.error("输入有误，请重试")
                        return false;
                    }
                });

            },
            resetForm(formName) {
                this.$refs[formName].resetFields();
                this.dom.cms=null
                this.dom.cover=null
            },
            imgHandler(state){
                this.drawer=state
                console.log(state)
            },
            initCover(material){
                this.dom.cover=material.url
                this.coverDrawer=false
            },
            initMaterial(material){

                let range = this.$refs.myTextEditor.quill.selection.savedRange.index

                this.$refs.myTextEditor.quill.insertEmbed(
                    range !== null ? range : 0,
                    "image",
                    material.url
                ); // 调用编辑器的 insertEmbed 方法，插入URL
                this.drawer=false

                console.log(material)
            },

            onEditorChange({ editor, html, text }) {
                this.dom.cms = html;
            },
        }
    }
</script>
<style lang='css' >

    .editor {
        line-height: normal !important;
        height: 500px;
    }
    .ql-snow .ql-tooltip[data-mode="link"]::before {
        content: "请输入链接地址:";
    }
    .ql-snow .ql-tooltip.ql-editing a.ql-action::after {
        border-right: 0px;
        content: "保存";
        padding-right: 0px;
    }

    .ql-snow .ql-tooltip[data-mode="video"]::before {
        content: "请输入视频地址:";
    }

    .ql-snow .ql-picker.ql-size .ql-picker-label::before,
    .ql-snow .ql-picker.ql-size .ql-picker-item::before {
        content: "14px";
    }
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {
        content: "10px";
    }
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {
        content: "18px";
    }
    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before,
    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {
        content: "32px";
    }

    .ql-snow .ql-picker.ql-header .ql-picker-label::before,
    .ql-snow .ql-picker.ql-header .ql-picker-item::before {
        content: "文本";
    }
    .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
        content: "标题1";
    }
    .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
        content: "标题2";
    }
    .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
        content: "标题3";
    }
    .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
        content: "标题4";
    }
    .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
        content: "标题5";
    }
    .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
        content: "标题6";
    }

    .ql-snow .ql-picker.ql-font .ql-picker-label::before,
    .ql-snow .ql-picker.ql-font .ql-picker-item::before {
        content: "标准字体";
    }
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="serif"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before {
        content: "衬线字体";
    }
    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="monospace"]::before,
    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="monospace"]::before {
        content: "等宽字体";
    }
</style>
