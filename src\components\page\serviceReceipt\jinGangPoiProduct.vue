<template>
  <div class="container">
    <div class="handle-box">
      <el-form :inline="true" class="demo-form-inline">

        <el-form-item label="产品id">
          <el-input v-model="dom.productId" placeholder="产品Id"></el-input>
        </el-form-item>

        <el-form-item label="产品名称">
          <el-input v-model="dom.productName" placeholder="产品名称"></el-input>
        </el-form-item>
        <el-form-item >
         <el-button type="primary" @click="searchBtn">搜索</el-button>
        </el-form-item>
      </el-form>
    </div>


    <el-table :data="dataList" v-loading="loading" style="width: 100%"
              :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
      <el-table-column
          label="序号"
          type="index"
          align="center">
      </el-table-column>
      <el-table-column
          prop="productId"
          label="产品Id"
          width="200"
          align="center">
      </el-table-column>
      <el-table-column
          prop="douyinProductId"
          label="抖音产品id"
          width="200"
          align="center">
      </el-table-column>
      <el-table-column
          prop="productName"
          label="产品名称"
          align="center">
      </el-table-column>
      <el-table-column
          prop="status"
          label="产品状态"
          align="center">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.status === 1">上架</el-tag>
          <el-tag type="warning" v-if="scope.row.status === 2">下架</el-tag>
          <el-tag type="danger" v-if="scope.row.status === 3">封禁</el-tag>
          <el-tag type="danger" v-if="scope.row.status == null">未知</el-tag>
        </template>
      </el-table-column>

      <el-table-column
          fixed="right"
          label="操作"
          width="400">
        <template slot-scope="scope">
          <el-row :gutter="20">
            <el-col :span="6"> <el-button type="primary" size="small" @click="editJson(scope.row)">编辑</el-button></el-col>
            <el-col :span="6"><el-button type="primary" size="small" @click="productStatus(scope.row,1)">上架</el-button></el-col>
            <el-col :span="6"><el-button type="primary" size="small" @click="productStatus(scope.row,2)">下架</el-button></el-col>
          </el-row>


        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChangePage"
            :current="dom.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="dom.size"/>
    </div>

    <el-dialog
        title="编辑"
        :visible.sync="editModal"
        width="50%"
    >
      <el-form label-width="180px" v-if="editRow.jsonStr" label-position="top">
        <el-form-item label="抖音侧产品id">
          <el-input v-model="editRow.jsonStr.product_id" disabled="disabled" style="width: 320px;"></el-input>
        </el-form-item>
        <el-form-item label="抖音产品名称">
          <el-input v-model="editRow.jsonStr.product.product_name" style="width: 320px;"></el-input>
        </el-form-item>
        <el-form-item label="poi门店集合(新增门店poi按格式补充)">
          <el-input
              type="textarea"
              autosize
              placeholder="请输入内容"
              v-model="jsonEdit.pois">
          </el-input>
        </el-form-item>
        <el-form-item label="封面图(填写图片地址或者上传图片自动返回地址)">
          <el-row>
            <el-col :span="24">
              <el-upload
                  action="https://biapi.xiaoyujia.com/files/uploadFiles"
                  :on-success="handleSuccess"
                  >
                <el-button size="small" type="primary">点击上传</el-button>
                <div slot="tip" class="el-upload__tip">图片不得超过5M，建议分辨率为375*280px，图片比例为4:3</div>
              </el-upload>

            </el-col>
          </el-row>
          <br>
          <el-row>
            <el-col :span="24">
              <el-input v-model="jsonEdit.img[0].url"></el-input>
            </el-col>
          </el-row>

        </el-form-item>
        <el-form-item label="使用时间">
          <el-radio v-model="jsonEdit.userDate.use_date_type" :label="1">指定日期</el-radio>
          <el-radio v-model="jsonEdit.userDate.use_date_type" :label="2">指定天数</el-radio>
          <el-date-picker
              v-if="jsonEdit.userDate.use_date_type == 1"
              v-model="jsonEdit.dateTime"
              @change="changeUserTimeRage"
              type="daterange"
              range-separator="至"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
          </el-date-picker>
          <el-input placeholder="请输入内容" v-model="jsonEdit.userDate.day_duration" v-if="jsonEdit.userDate.use_date_type == 2" style="width: 300px;">
            <template slot="prepend">自购买起日</template>
          </el-input>
        </el-form-item>
        <el-form-item label="产品售卖开始时间">
          <el-date-picker
              v-model="editRow.jsonStr.product.sold_start_time"
              type="date"
              value-format="timestamp"
              placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="产品售卖结束时间">
          <el-date-picker
              v-model="editRow.jsonStr.product.sold_end_time"
              type="date"
              value-format="timestamp"
              placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否自动延期">
          <el-row>
            <el-col :span="24">
              <el-switch
                  v-model="editRow.jsonStr.product.attr_key_value_map.auto_renew"
                  active-color="#13ce66"
                  inactive-color="#ff4949">
              </el-switch>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              售卖时间到期前一天，商品售卖日期和顾客可消费日期自动延长30天（历史已售订单不受影响，可随时关闭)
            </el-col>
          </el-row>


        </el-form-item>
        <el-form-item label="产品投放渠道">
          <el-radio v-model="editRow.jsonStr.product.attr_key_value_map.show_channel" label="1">不限渠道</el-radio>
          <el-radio v-model="editRow.jsonStr.product.attr_key_value_map.show_channel" label="2">仅直播间</el-radio>
        </el-form-item>
        <el-form-item label="sku信息(原价不可小于折扣价),sku名称若不知道那就和产品名称对应即可">
          <el-input placeholder="请输入内容" v-model="jsonEdit.skuCommodity[0].group_name">
            <template slot="prepend">sku分组名称</template>
          </el-input>
          <el-input placeholder="请输入内容" v-model="jsonEdit.skuCommodity[0].item_list[0].name">
            <template slot="prepend">sku子名称</template>
          </el-input>
          <el-form-item label="产品原价(单位为分,转换为元需 *100)">
            <el-input-number v-model="jsonEdit.skuCommodity[0].item_list[0].price"  :min="1" :max="9999999" label="描述文字"> </el-input-number>
          </el-form-item>

          <el-form-item label="产品现价(单位为分,转换为元需 *100)">
            <el-input-number v-model="editRow.jsonStr.sku.actual_amount"  :min="1" :max="9999999" label="描述文字">
            </el-input-number>
          </el-form-item>


<!--          <el-input placeholder="请输入内容" v-model="jsonEdit.skuCommodity[0].item_list[0].price">-->
<!--            <template slot="prepend">产品原价(单位为分,转换为元需 *100)</template>-->
<!--          </el-input>-->

<!--          <el-input placeholder="请输入内容" v-model="editRow.jsonStr.sku.actual_amount">-->
<!--            <template slot="prepend">产品现价(单位为分,转换为元需 *100)</template>-->
<!--          </el-input>-->
        </el-form-item>
        <el-form-item label="产品库存">
          <el-radio v-model="editRow.jsonStr.sku.stock.limit_type" :label="1">无限库存</el-radio>
          <el-radio v-model="editRow.jsonStr.sku.stock.limit_type" :label="2">有限库存</el-radio>
          <el-input placeholder="请输入内容" v-model="editRow.jsonStr.sku.stock.stock_qty"
                    v-if="editRow.jsonStr.sku.stock.limit_type == 2" style="width: 200px">
            <template slot="prepend">库存数</template>
          </el-input>
        </el-form-item>

      </el-form>
      <span slot="footer" class="dialog-footer">
    <el-button @click="editModal = false">取 消</el-button>
    <el-button type="primary" @click="saveJson">确 定</el-button>
  </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      editModal: false,
      loading: false,
      time: [],
      jsonEdit: {
        dateTime: [],
      },
      editRow: {
        id: '',
        jsonStr: null
      },
      dom: {
        size: 10,
        current: 1,
        productId: '',
        productName: '',
        status: ''
      },
      dataList: [],
      pageSizeOpts: [10],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
    }
  },
  created() {
    this.getData();

  },
  methods: {
    productStatus(row,status) {
      this.loading = true;
      let param = {
        productId: row.productId,
        status: status
      }
      this.$postData("poijgSaveStatus", param).then(res => {
        if (res.status === 200) {

          this.$message({
            type: 'success',
            message: res.data
          });
        } else  {
          this.$message({
            type: 'warning',
            message: res.msg
          });
        }
        this.loading = false;
      })
    },
    searchBtn() {
      this.dom.current = 1;
      this.pageInfo.current = 1;
      this.getData();
    },
    saveJson() {
      //开始售卖时间和结束售卖时间编辑时先 *1000，保存时在返回 /1000
      this.editRow.jsonStr.product.sold_start_time = this.editRow.jsonStr.product.sold_start_time / 1000
      this.editRow.jsonStr.product.sold_end_time = this.editRow.jsonStr.product.sold_end_time / 1000
      this.editRow.jsonStr.product.attr_key_value_map.image_list = JSON.stringify(this.jsonEdit.img)
      this.editRow.jsonStr.product.pois = this.jsonEdit.pois
      this.editRow.jsonStr.product.attr_key_value_map.use_date = JSON.stringify(this.jsonEdit.userDate)
      this.editRow.jsonStr.sku.attr_key_value_map.commodity = JSON.stringify(this.jsonEdit.skuCommodity)
      this.editRow.jsonStr.product.attr_key_value_map.auto_renew = this.editRow.jsonStr.product.attr_key_value_map.auto_renew.toString();
      this.loading = true;
      this.editModal = false;
      this.editRow.str = this.editRow.jsonStr;
      this.editRow.jsonStr.product.product_ext.display_price.low_price = this.jsonEdit.skuCommodity[0].item_list[0].price
      this.editRow.jsonStr = null;


      this.$postData("savejgJson", this.editRow).then(res => {
        if (res.status === 200) {

          this.$message({
            type: 'success',
            message: res.data
          });
        } else  {
          this.$message({
            type: 'warning',
            message: res.msg
          });
        }
        this.loading = false;
        this.getData();
      })
    },
    handleSuccess(res, file) {
      this.jsonEdit.img[0].url = res.data;
      this.jsonEdit.img[1].url = res.data;
    },
    changeUserTimeRage() {
      this.jsonEdit.userDate.use_start_date = this.jsonEdit.dateTime[0]
      this.jsonEdit.userDate.use_end_date = this.jsonEdit.dateTime[1]

    },
    editJson(data) {
      this.editModal = true;
      this.editRow = data;
      this.editRow.jsonStr = JSON.parse(data.jsonStr)
      console.log(this.editRow.jsonStr)
      //把所有多层嵌套的东西拿出来单独做处理，点击保存的时候在合并
      let img = JSON.parse(this.editRow.jsonStr.product.attr_key_value_map.image_list);
      let pois = JSON.stringify(this.editRow.jsonStr.product.pois)
      let userDate = JSON.parse(this.editRow.jsonStr.product.attr_key_value_map.use_date);
      let skuCommodity = JSON.parse(this.editRow.jsonStr.sku.attr_key_value_map.commodity)
      // let skuStock = JSON.parse(this.editRow.jsonStr)
      this.jsonEdit = {
        img: img,
        pois: pois,
        userDate: userDate,
        skuCommodity: skuCommodity,
        dateTime:[userDate.use_start_date,userDate.use_end_date]
      }
      //开始售卖时间和结束售卖时间编辑时先 *1000，保存时在返回 /1000
      this.editRow.jsonStr.product.sold_start_time = this.editRow.jsonStr.product.sold_start_time * 1000
      this.editRow.jsonStr.product.sold_end_time = this.editRow.jsonStr.product.sold_end_time * 1000
    },
    getData() {
      this.loading = true;
      this.$postData("getjgPoiList", this.dom).then(res => {
        if (res.status === 200) {
          this.loading = false;
          this.dataList = [];
          this.dataList = res.data.records;
          this.pageInfo.total = res.data.total;
        }
      })
    },
    onChangePage(index) {
      this.dom.current = index;
      this.getData();
    },
    onPageSizeChange(size) {
      this.dom.size = size;
      this.getData();
    },

  }

}
</script>

<style scoped>


</style>
