<template>
  <div class="container" id="pdfDom">

    <!-- 搜索栏目 -->
    <div class="handle-box">
      <el-form ref="form" :model="pageInfo">
        <el-row>

          <el-col :span="5">
            <el-form-item label="服务类别" style="margin-right: 20px">
              <el-select v-model="quer.typeID" placeholder="请选择服务类别" clearable>
                <el-option
                    v-for="item in categoryOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="1">
            <el-button style="margin-right: 30px;" type="primary" @click="query()"
                       icon="el-icon-search">搜索
            </el-button>
          </el-col>

          <el-col :span="1">
            <el-button style="margin-left: 60px;" type="success" icon="el-icon-circle-plus-outline"
                       @click="certModal = true">添加
            </el-button>
          </el-col>
        </el-row>

      </el-form>
    </div>


    <!-- 数据表格 -->
    <el-table :data="productScriptTypeList" v-loading="loading" border stripe
              :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
              :expand-row-keys="expands">

      <el-table-column width="140" prop="certCodeFixed" label="类别名称">
        <template slot-scope="scope">
          <span >{{ scope.row.categoryName }}</span>
        </template>
      </el-table-column>

      <el-table-column width="140" prop="typeName" label="服务话术">
        <template slot-scope="scope">
          <el-tag type="primary" @click="getServiceLanguage(scope.row.id)">点击查看</el-tag>
        </template>
      </el-table-column>

      <el-table-column width="140" prop="certContent" label="创建时间">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>

      <el-table-column width="130" prop="creater" label="创建人">
        <template slot-scope="scope">
          <span>{{ scope.row.realName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-popconfirm title="确定删除吗？" @confirm="delProductScriptType(scope.row)">
            <el-button :disabled="scope.row.id==3" type="danger" size="small" slot="reference" style="margin-left: 10px" icon="el-icon-delete">删除
            </el-button>
          </el-popconfirm>

        </template>
      </el-table-column>

    </el-table>


    <div class="pagination">
      <Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
            :show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
            :page-size="pageInfo.size"/>
    </div>

    <el-dialog :visible.sync="certModal" width="60%" title="添加类别" :mask-closable="false">
      <div style="height: 400px;overflow: hidden;overflow-y: scroll;">
        <el-form ref="ruleForm" label-width="150px" class="demo-ruleForm" size="mini">

          <el-form-item label="服务类别：">
            <el-select v-model="productScriptType.productCategoryId" placeholder="请选择服务类别">
              <el-option
                  v-for="item in categoryOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>

        <div style="margin: 0 400px;width: 100%;">
          <el-button @click="saveProductScriptType()" type="success" size="small">确定添加
          </el-button>
        </div>

      </div>
    </el-dialog>


    <el-dialog :visible.sync="replyModal" width="60%" title="添加话术" :mask-closable="false">
      <div style="height: 500px;overflow: hidden;overflow-y: scroll;">
        <el-form ref="ruleForm" label-width="150px" class="demo-ruleForm" size="mini">

          <el-form-item label="会员等级：">
            <el-select v-model="serviceLanguage.memberLevel" placeholder="请选择问答类型">
              <el-option
                  v-for="item in memberLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="对应话术：">
            <el-input v-model="serviceLanguage.serviceScript" type="textarea" class="handle-input mr10"
                      placeholder="请输入话术内容">
            </el-input>
          </el-form-item>

        </el-form>

        <div style="margin: 0 400px;width: 100%;">
          <el-button @click="saveServiceLanguage()" type="success" size="small">确定添加
          </el-button>
        </div>

      </div>
    </el-dialog>

    <el-link :href="hrefUrl" target="_blank" id="elLink"/>

    <el-dialog
        title="服务话术："
        :visible.sync="dialogFlag"
        width="80%">
      <div style="display: flex">
        <el-button style="margin-left: 10px;margin-bottom: 20px;" type="success" icon="el-icon-circle-plus-outline"
                   @click="clickAddReply">添加话术
        </el-button>
      </div>
      <el-table :data="serviceLanguageList" v-loading="replyLoading" :header-cell-style="{background:'#ddd'}" border class="table"
                ref="multipleTable">
        <el-table-column fixed="left" prop="memberLevel" label="会员等级" width="150">
          <template slot-scope="scope">
            <span v-if="!scope.row.isEdit">{{ scope.row.memberLevel==1?'白金会员':
                                              scope.row.memberLevel==2?'黄金会员':
                                              scope.row.memberLevel==3?'铂金会员':
                                              scope.row.memberLevel==4?'钻石会员':
                                              scope.row.memberLevel==5?'黑钻会员':'未知'}}</span>
            <el-select v-if="scope.row.isEdit" v-model="scope.row.levelName" placeholder="请选择问答类型">
              <el-option
                  v-for="item in memberLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="serviceScript" label="对应话术" width="350">
          <template slot-scope="scope">
            <span v-if="!scope.row.isEdit">{{ scope.row.serviceScript }}</span>
            <el-input v-model="scope.row.serviceScript" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
                      placeholder="请输入话术内容" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
          </template>
        </el-table-column>
        <el-table-column
            prop="createTime"
            label="创建时间"
            width="150"
        ></el-table-column>
        <el-table-column
            prop="createRealName"
            label="创建人"
            width="100"
        ></el-table-column>
        <el-table-column
            prop="updateTime"
            label="修改时间"
            width="150"
        ></el-table-column>
        <el-table-column
            prop="updateRealName"
            label="修改人"
            width="100"
        ></el-table-column>
        <el-table-column fixed="right" min-width="180" label="操作">
          <template slot-scope="scope">
            <el-button @click="openEdit(scope.row,1,scope.$index)" type="success" size="small" :disabled="scope.row.isEdit"
                       v-if="!scope.row.isEdit" icon="el-icon-edit">修改
            </el-button>
            <el-button @click="editServiceLanguage(scope.row)" type="primary" size="small"
                       v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
            </el-button>
            <el-popconfirm title="确定此话术信息删除吗？" @confirm="delServiceLanguage(scope.row)">
              <el-button type="danger" size="small" slot="reference" style="margin-left: 10px" icon="el-icon-delete">删除
              </el-button>
            </el-popconfirm>

          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!--图片预览-->
    <el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
      <img :src="imageUrl!=null?imageUrl:blankImg" style="width: 100%;height: auto;margin: 0 auto;"/>
    </el-dialog>
  </div>

</template>

<script>

export default {
  name: "qaLibraryType",
  components: {},
  data() {
    return {
      url: '',
      imageUrl: '',
      memberLevelOptions: [{
        value: '1',
        label: '白金会员'
      }, {
        value: '2',
        label: '黄金会员'
      }, {
        value: '3',
        label: '铂金会员'
      }, {
        value: '4',
        label: '钻石会员'
      }, {
        value: '5',
        label: '黑钻会员'
      }],
      uploadType: null,
      blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
      imgModal: false,
      replyModal: false,
      categoryOptions: [],
      serviceLanguage: {
        memberLevel: '',
        serviceScript: '',
        productScriptId: '',
        createPeople: localStorage.getItem('id')
      },
      value: '',
      replyIndex: null,
      hrefUrl: "https://biapi.xiaoyujia.com/unionQa/importQaTemplateDownload",
      uploadId: 1,
      excelUploadId: 1,
      isModal: false,
      dialogFlag: false,
      excelUploadUrl: "https://biapi.xiaoyujia.com/unionQa/importUnionQa/" + localStorage.getItem("id"),
      isEdit: false,
      certModal: false,
      fullscreenLoading: false,
      productScriptTypeList: [],
      unionQaReplyList: [],
      serviceLanguageList: [],
      loading: true,
      replyLoading: true,
      excelloading: false,
      pageSizeOpts: [5, 10, 20],
      unionQA: {
        founder: localStorage.getItem("id")
      },
      productScriptType: {
        createPeople: localStorage.getItem("id")
      },
      unionQAReply: {
        qaId: null,
        founder: localStorage.getItem("id"),
        isEdit: true
      },
      certType: {},
      pageInfo: {
        total: 10,
        size: 5,
        current: 1,
        pages: 1
      },
      expands: [],
      getRowKeys(row) {
        return row.id
      },
      quer: {
        "productCategoryId": null,
        "current": 1,
        "size": 10
      },
    }
  },
  created() {
    this.getData()
    this.getAllProductCategory()
  },
  methods: {
    clickAddReply() {
      this.uploadType = 0
      this.replyModal = true
    },
    uploadImgUrl(val){
      if(val.isEdit){
      this.fullscreenLoading = true;
      }else {
        // 打开图片预览
        if (val.pictureUrl != null && val.pictureUrl != '') {
          this.imageUrl = val.pictureUrl
        } else {
          this.imageUrl = this.blankImg
        }
        this.imgModal = true
      }
    },
    // 图片上传成功
    imgUploadSuccess(res, file) {
      if (this.uploadType === 0) {
        this.unionQAReply.pictureUrl = res.data
      }
      if (this.uploadType === 1) {
        this.unionQaReplyList[this.replyIndex].pictureUrl = res.data
      }
      this.fullscreenLoading = false;

    },
    getServiceLanguage(val) {
      this.dialogFlag = true
      this.serviceLanguage.productScriptId = val
      this.$getData("getServiceLanguage", {id: val}).then(res => {
        if (res.code == 0) {
          this.serviceLanguageList = res.data
          // 追加编辑状态位
          for (let item of this.serviceLanguageList) {
            this.$set(item, "isEdit", false)
          }
          this.replyLoading = false
        } else {
          this.$message.error('未查询到服务话术!')
        }
      })
    },
    getAllProductCategory() {
      // 获取证书列表
      this.$getData("getAllProductCategory", "").then(res => {
        if (res.code == 0) {
          this.categoryOptions = res.data
        }
      })
    },
    getData() {
      // 获取证书列表
      this.$getData("getProductScriptTypeData", this.quer).then(res => {
        this.loading = false
        if (res.code == 0) {
          this.productScriptTypeList = res.data.records
          this.pageInfo.current = res.data.current
          this.pageInfo.size = res.data.size
          this.pageInfo.total = res.data.total
        } else {
          this.productScriptTypeList = []
          this.$message.error('查询失败!')
        }
      })
    },
    // 搜索
    query() {
      this.loading = true
      this.quer.current = 1
      this.getData()
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.quer.size = size
      this.getData()
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.quer.current = index
      this.getData()
    },
    // 证书Excel模板下载
    excelDownload() {
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    // Excel表格上传成功
    excelUploadSuccess(res, file) {
      this.excelloading = false;
      this.$message.success('问答信息导入成功！')
      this.getData()
    },
    // Excel表格上传失败
    excelUploadError() {
      this.excelloading = false;
      this.$message.error('问答信息解析失败！请使用正确的Excel模板进行上传！')
    },
    // 打开修改
    openEdit(val,flag,index) {
      this.replyIndex = index
      val.isEdit = true
      if(flag===1){
        this.uploadType=1
      }
    },
    saveServiceLanguage() {
      // 数据校验
      if (!this.serviceLanguage.memberLevel) {
        this.$message.error('请选择会员等级！')
      } else if (!this.serviceLanguage.serviceScript) {
        this.$message.error('请输入话术内容！')
      } else {
        this.$postData("saveServiceLanguage", this.serviceLanguage).then(res => {
          if (res.code == 0) {
            this.$message.success('添加成功!')
            this.replyModal = false
            this.serviceLanguage.memberLevel = ''
            this.serviceLanguage.serviceScript = ''
            this.getServiceLanguage(this.serviceLanguage.productScriptId)
          } else {
            this.$message.error('添加失败！' + res.msg)
          }
        })
      }
    },
    saveProductScriptType() {
      // 数据校验
      if (!this.productScriptType.productCategoryId) {
        this.$message.error('请选择服务类别！')
      } else {
        this.$postData("saveProductScriptType", this.productScriptType).then(res => {
          if (res.code == 0) {
            this.$message.success('添加成功!')
            this.certModal = false
            this.productScriptType.productCategoryId = ''
            this.getData()
          } else {
            this.$message.error('添加失败！' + res.msg)
          }
        })
      }
    },
    delServiceLanguage(val) {
      val.updatePeople = localStorage.getItem('id')
      this.$postData("delServiceLanguage", val).then(res => {
        if (res.code == 0) {
          this.$message.success('删除成功!')
          this.getServiceLanguage(this.serviceLanguage.productScriptId)
        } else {
          this.$message.error('删除失败！' + res.msg)
        }
      })
    },
    delProductScriptType(val) {
      this.$postData("delProductScriptType", val).then(res => {
        if (res.code == 0) {
          this.$message.success('删除成功!')
          this.getData()
        } else {
          this.$message.error('删除失败！' + res.msg)
        }
      })
    },
    // 更改问答信息
    editServiceLanguage(val) {
      // 数据校验
      if (!val.levelName) {
        this.$message.error('请选择会员等级！')
      } else if (!val.serviceScript) {
        this.$message.error('请输入话术内容！')
      } else {
         if(val.levelName.indexOf("会员")==-1){
           val.memberLevel =val.levelName
         }
         val.updatePeople =localStorage.getItem('id')
        this.$postData("editServiceLanguage", val).then(res => {
          if (res.code == 0) {
            this.$message.success('更新成功!')
            this.getServiceLanguage(this.serviceLanguage.productScriptId)
            val.isEdit = false
          } else {
            this.$message.error('更新失败！' + res.msg)
          }
        })
      }
    },
  },
}
</script>

<style scoped>
.handle-box {
  /*margin-bottom: 10px;*/
  font-weight: bold !important;
}

table {
  border-collapse: collapse;
  /*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
  border-left: #C8B9AE solid 1px;
  border-top: #C8B9AE solid 1px;
}

table td {
  border-right: #C8B9AE solid 1px;
  border-bottom: #C8B9AE solid 1px;
}

th {
  background: #eee;
  font-weight: normal;
}

tr {
  background: #fff;
}

tr:hover {
  background: #cc0;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

>>> .el-upload--text {
  width: 200px;
  height: 150px;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 300px;
  height: 300px;
  display: block;
}

.detail-title {
  margin: 10px 20px;
}

.handle-input {
  width: 400px;
  display: inline-block;
}

.mr10 {
  margin-right: 10px;
}

.big-input {
  width: 200px;
}

.inline-block {
  display: inline-block;
}
</style>