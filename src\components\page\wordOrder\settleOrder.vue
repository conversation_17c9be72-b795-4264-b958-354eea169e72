<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">
                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="订单编号">
                                <el-input
                                        v-model="dto.orderNo"
                                        placeholder="订单编号"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="5">
                            <el-form-item label="服务员工">
                                <el-input
                                        v-model="dto.serviceName"
                                        placeholder="支持员工号/姓名"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="服务项目">
                                <Select filterable style="width: 110px" @on-change="changeValue"
                                        v-model="dto.productCategoryId">
                                    <Option value="0">请选择</Option>
                                    <Option v-for="item in options" :value="item.id" :key="item.id">{{item.name}}</Option>
                                </Select>
                                <Select filterable style="width: 110px" v-model="dto.productId"  >
                                    <Option value="">请选择</Option>
                                    <Option v-for="item in options_children" :value="item.value" :key="item.value">{{item.text}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="所属门店">
                                <Select filterable style="width: 150px" v-model="dto.storeId">
                                    <Option value="">请选择</Option>
                                    <Option v-for="item in storeList" :value="item.id">{{ item.storeName}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="5">
                            <el-form-item label="所属部门">
                                <Select filterable style="width: 150px" v-model="dto.departmentId">
                                    <Option value="">请选择</Option>
                                    <Option v-for="item in departList" :value="item.id">{{ item.name}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>


                        <el-col :span="6">
                            <el-form-item label="服务时间">
                                <el-date-picker
                                        v-model="dto.startTime"
                                        type="datetime"
                                        placeholder="开始时间"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" style="text-align: left">
                            <el-form-item label="至">
                                <el-date-picker
                                        v-model="dto.endTime"
                                        type="datetime"
                                        placeholder="结束时间"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="订单状态">
                                <Select filterable style="width: 200px" v-model="dto.orderState"  multiple>
                                    <Option value="">请选择</Option>
                                    <Option :value="10">已接单</Option>
                                    <Option :value="20">派单待确认</Option>
                                    <Option :value="30">拒绝接单</Option>
                                    <Option :value="40">已派单</Option>
                                    <Option :value="50">执行中</Option>
                                    <Option :value="60">开始服务</Option>
                                    <Option :value="70">服务结束</Option>
                                    <Option :value="80">已完成</Option>
                                    <Option :value="90">已评价</Option>
                                    <Option :value="99">已取消</Option>
                                </Select>
                            </el-form-item>
                        </el-col>


                        <el-col :span="5">
                            <el-form-item label="订单标签">
                                <Select filterable style="width: 150px" v-model="dto.lblId">
                                    <Option value="">请选择</Option>
                                    <Option v-for="item in orderLabelList" :value="item.name">{{ item.name}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>


                        <el-col :span="5">
                            <el-form-item label="是否结算">
                                <Select filterable style="width: 100px" v-model="dto.settlementType">
                                    <Option value="">请选择</Option>
                                    <Option :value="1">是</Option>
                                    <Option :value="0">否</Option>
                                </Select>
                            </el-form-item>
                        </el-col>


                        <el-col :span="5">
                            <el-form-item label="会员账户">
                                <el-input
                                        v-model="dto.account"
                                        placeholder="会员账户"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                    </el-row>
                    <el-row>
                        <el-col :span="24" style="text-align: right">
                            <el-form-item>
                                <el-button type="primary" style="width:8%" @click="query()">搜索</el-button>
                                <el-button type="success" style="width:8%" @click="OneClickSettlement" :loading="isSettle">批量结算</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div style="text-align:right">{{summary}}</div>
            <el-table :data="list" border class="table" ref="multipleTable"
                      v-loading="loading"
                      @selection-change="handleSelectionChange">
                <el-table-column
                        type="selection"
                        width="55">
                </el-table-column>
                <el-table-column
                        prop="order.billNo"
                        width="140"
                        label="订单编号">
                    <template slot-scope="scope">
                        <a style="text-decoration:underline;" :href="scope.row.url" target="_Blank">{{scope.row.order.billNo}}</a>
                    </template>
                </el-table-column>
                <!--<el-table-column-->
                        <!--prop="order.orderStateDesc"-->
                        <!--label="订单状态"-->
                        <!--:show-overflow-tooltip="true"-->
                <!--&gt;</el-table-column>-->
                <el-table-column
                        width="100"
                        prop="order.productName"
                        label="服务项目"
                ></el-table-column>
                <el-table-column
                        prop="orderWages.serviceNo"
                        width="110"
                        label="服务员工">
                    <template slot-scope="scope">
                        <p  style="font-size: 12px" v-for="item in scope.row.orderWagesList">
                            <span v-if="scope.row.product.serviceOption==2">
                                  {{item.no}}
                            </span>
                            <span  v-if="scope.row.product.serviceOption==1">
                            {{item.serviceNo}}
                            </span>
                            ({{item.serviceName}})
                        </p>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="order.totalAmount"
                        label="订单金额"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="order.amount"
                        label="实付金额"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="amount"
                        label="订单薪酬"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="realWage"
                        label="服务薪酬">
                    <template slot-scope="scope">
                        <p  style="font-size: 12px" v-for="item in scope.row.orderWagesList ">
                            {{item.realWage}}
                        </p>
                    </template>
                </el-table-column>

                <el-table-column
                        width="160"
                        prop="order.paySettlementTime"
                        label="结算时间"
                ></el-table-column>
                <el-table-column
                        prop="member.account"
                        label="会员账号"
                        :show-overflow-tooltip="true"
                ></el-table-column>

                <!--<el-table-column-->
                        <!--prop="store.storeName"-->
                        <!--label="所属门店"-->
                        <!--:show-overflow-tooltip="true"-->
                <!--&gt;</el-table-column>-->
                <el-table-column
                        prop="department.name"
                        label="所属部门"
                        :show-overflow-tooltip="true"
                ></el-table-column>

                <el-table-column
                        width="40"
                        prop="settlementTypeDesc"
                        label="是否结算"
                ></el-table-column>
                <el-table-column
                        prop="paySettlementPerson"
                        label="结算人"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="orderExt.lblId"
                        label="订单标签"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="overtime"
                        label="超时时间"
                        :show-overflow-tooltip="true"
                ></el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :current="this.dto.pageNum"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="10"/>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                loading:true,
                summary: null,
                list: null,
                tableData: [],
                pageSizeOpts: [10, 20, 30],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                serviceOptions: [], //服务区域
                departList: [],
                orderLabelList: [],
                dto: {
                    productCategoryId: null,
                    productId: null,
                    departmentId: null,
                    account: null,
                    lblId: null,
                    storeId: null,
                    startTime: null,
                    endTime: null,
                    serviceName: null,
                    settlementType: null,
                    orderState: [80,90],
                    pageSize: 10,
                    pageNum: 1,
                },
                options: [], //服务项目一级
                options_children: [], //服务项目二级
                storeList: [],
                startTime1: [],
                timer: null,
                orderIds: null,
                isSettle:false

            };
        },
        components: {},
        created() {
            this.getData();
            this.getProduct();
            this.getStore();
            this.getDepart();
            this.getOrderLabel();
            this.time();
        },
        mounted() {
            this.timer = setInterval(() => {
                this.getData()
            }, 300000)
        },
        beforeDestroy() {
            clearInterval(this.timer);
        },
        computed: {},
        methods: {
            time() {
                let y = new Date().getFullYear(),
                    m = new Date().getMonth() + 1,
                    d = new Date().getDate();
                this.dto.endTime =
                    y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d) + " 23:59:59";
                if (m == 0) {
                    y -= 1;
                    m = 12;
                }
                // this.dto.startTime =
                //     y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d) + " 00:00:00";
                this.dto.startTime = "2019-01-01 00:00:00"
            },
            getOrderLabel() {
                this.$getData("getOrderLabel").then(
                    res => {

                        this.orderLabelList = res.data;
                    }
                );
            },
            getData() {
                this.$postData("dispatch_selectSettlement", this.dto, {}).then(res => {
                    if (res.status == 200) {
                        this.loading=false;

                        this.summary = res.data.summary
                        this.list = res.data.list;
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    }
                });
            },

            changeValue(value) {
                if (value != null) {
                    this.$getData("getProductName", {productCategoryId: value}).then(
                        res => {
                            this.options_children = res.data;
                        }
                    );
                }
            },
            getProduct() {
                this.$getData("category_getByRoleId").then(res => {
                    this.options = res.data;
                    console.log(this.options)
                });
            },

            getDepart() {
                this.$postData("depart_list", {}).then(res => {
                    this.departList = res.data;
                    console.log(this.options)
                });
            },
            getStore() {
                this.$postData("store_getByList", {}, {}).then(res => {
                    if (res.status == 200) {

                        this.storeList = res.data;
                    }
                });
            },
            getAddress() {
                this.$getData("getCityArea").then(res => {
                    console.log(res.data)
                    this.serviceOptions = res.data;
                });
            },
            getCity(value) {
                this.$getData("getCityId", {cityId: value}).then(
                    res => {
                        this.cityList = res.data;
                    }
                );
            },
            query() {
                this.loading=true
                this.dto.pageNum = 1;
                this.getData();
            },

            // 页码大小
            onPageSizeChange(size) {
                this.loading=true
                this.dto.pageSize = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                console.log(index)
                this.loading=true
                this.dto.pageNum = index;
                this.getData();
            },
            handleSelectionChange(val) {
                console.log(val)
                let orderIds = "";
                for (let i = 0; i < val.length; i++) {
                    let res = val[i];

                    orderIds += res.order.id;
                    if (i != val.length - 1) {
                        orderIds += ","
                    }
                }
                console.log(orderIds)
                this.orderIds = orderIds;
            },
            /*
            * 一键结算
            * */
            OneClickSettlement() {
                if (this.orderIds != '' && this.orderIds != null) {
                    this.isSettle=true;
                    this.$getData("dispatch_click_settlement", {ids: this.orderIds}).then(res => {
                        if (res.Meta.State == 200) {
                            this.isSettle=false;
                            this.$message({
                                showClose: true,
                                message: res.Meta.Msg,
                                type: 'success'
                            });
                            this.getData();
                        } else {
                            this.isSettle=false;
                            this.$alert(res.Meta.Msg, '提示', {
                                confirmButtonText: '确定',
                            });
                            this.getData();
                        }
                    })
                }else {
                    this.$message.error('请选择订单');
                }
            },
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
