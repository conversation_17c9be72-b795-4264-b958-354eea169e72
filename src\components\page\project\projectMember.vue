<template>
    <div class="table">
        <!-- <div class="crumbs">
             <el-breadcrumb separator="/">
                 <el-breadcrumb-item>
                     <i class="el-icon-lx-cascades"></i> 成员管理
                 </el-breadcrumb-item>
             </el-breadcrumb>
         </div>-->
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">
                    <el-row>
                        <el-col :span="9">
                            <el-form-item label="项目编号">
                                <el-input
                                        v-model="projectMemberQuery.projectNo"
                                        placeholder="项目编号"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="成员名称">
                                <el-input
                                        v-model="projectMemberQuery.employeeName"
                                        placeholder="成员名称"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>


                        <el-col style="text-align:right" :span="6">
                            <el-form-item>
                                <el-button type="success" style="width:30%" @click="saveModal=true">添加</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>

                        <el-col :span="17">
                            <el-form-item label="加入时间">
                                <el-date-picker
                                        v-model="projectMemberQuery.createTime"
                                        type="daterange"
                                        align="right"
                                        unlink-panels
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        format="yyyy 年 MM 月 dd 日"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>

                        <el-col style="text-align:right" :span="6">
                            <el-form-item>
                                <el-button type="primary" style="width:30%" @click="query()">搜索</el-button>
                                <el-button type="success" style="width:30%" @click="exportList">导出Excel</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-table :data="data" border class="table" ref="multipleTable">
                <el-table-column
                        prop="employee.realName"
                        label="姓名"
                        width="120"
                ></el-table-column>
                <el-table-column
                        prop="project.name"
                        label="所属项目"
                        width="300"
                ></el-table-column>
                <el-table-column
                        prop="role"
                        label="角色"
                        width="200"
                ></el-table-column>
                <el-table-column
                        prop="capital"
                        label="注册资金"
                        width="150"
                ></el-table-column>
                <el-table-column
                        prop="proportion"
                        label="分润比例"
                        width="130"
                ></el-table-column>
                <el-table-column
                        prop="createTime"
                        label="加入时间"
                ></el-table-column>
                <el-table-column
                        label="操作"
                        width="240">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="edit(scope.row.id,scope.row.project.capital)" type="success">编辑</el-button>
                        <el-button size="mini" @click="update(scope.row.id)" type="danger">退出项目</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="10"/>
            </div>
        </div>

        <Modal v-model="saveModal" class="Modal" :width="screenWidth" title="添加" :mask-closable="false">
            <div class="addBody">

                <project-member-choose v-if="saveModal" @init-choose="initChooseProject"
                                       @close-modal="closeCurrModal"></project-member-choose>
            </div>
            <div slot="footer">
            </div>
        </Modal>

        <!--编辑-->
        <Modal v-model="editModal" class="Modal" :width="width" title="成员编辑" :mask-closable="false">
            <div class="addBody">

                <member-update v-if="editModal" :member="member" @init-choose="initChooseProject"
                               @close-modal="closeCurrModal"></member-update>
            </div>
            <div slot="footer">
            </div>
        </Modal>

    </div>
</template>

<script>
    import projectMemberChoose from '@/components/page/project/choose/projectMemberChoose.vue'
    import memberUpdate from '@/components/page/project/choose/memberUpdate.vue'
    import {formatDate} from '@/components/common/utils.js'

    export default {
        data() {
            return {
                member: null,
                editModal: false,
                tableData: [],
                pageSizeOpts: [10, 20, 30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                theader: "",
                screenWidth: '40%',//新增对话框 宽度
                width: '30%',//新增对话框 宽度
                saveModal: false,
                saveLoading: false,
                registerTime: [],
                lastLoginTime: [],
                projectMemberQuery: {
                    projectNo: null,
                    employeeId: null,
                    createTime: null,
                    createTime1: null,
                    pageSize: "",
                    pageIndex: "",
                    employeeName: null
                },
            };
        },
        components: {
            'projectMemberChoose': projectMemberChoose,
            "memberUpdate": memberUpdate,
        },
        created() {
            this.getData();
        },
        computed: {
            data() {
                return this.tableData.filter(d => {
                    d.createTime = d.createTime.substring(0, 10);
                    return d;
                });
            }
        },
        methods: {
            getData() {
                this.$postData("projectMember_getByList", this.projectMemberQuery, {}).then(res => {
                    if (res.meta.state == 200) {

                        this.tableData = res.data.tbody.records;
                        this.pageInfo.current = res.data.tbody.current;
                        this.pageInfo.pages = res.data.tbody.pages;
                        this.pageInfo.size = res.data.tbody.size;
                        this.pageInfo.total = res.data.tbody.total
                        this.income(this.tableData);
                        this.projectMemberQuery.createTime = null;
                        this.projectMemberQuery.createTime1 = null;
                    } else {
                        this.$message.error("查询失败，" + res.meta.msg);
                    }
                })

            },
            exportList() {
                if (this.projectMemberQuery.createTime != null) {
                    this.projectMemberQuery.createTime1 = this.projectMemberQuery.createTime[1];
                    this.projectMemberQuery.createTime = this.projectMemberQuery.createTime[0];
                }
                this.$postData("projectMember_export", this.projectMemberQuery, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tablename: "成员管理",
                        res: res
                    });
                });
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
            query() {
                this.projectMemberQuery.pageIndex=1;
                console.log(this.projectMemberQuery);
                if (this.projectMemberQuery.createTime != null) {
                    this.projectMemberQuery.createTime1 = this.projectMemberQuery.createTime[1];
                    this.projectMemberQuery.createTime = this.projectMemberQuery.createTime[0];

                }
                this.getData();
            },
            /*
            * 计算总支出收入
            * */
            income(data) {
                let income = 0;
                let expend = 0;
                if (data) {
                    for (let i = 0; i < data.length; i++) {
                        let res = data[i];
                        if (res.incomeExpends) {
                            for (let j = 0; j < res.incomeExpends.length; j++) {
                                let flag = res.incomeExpends[j];
                                if (flag.type == 1) {
                                    income += flag.income;
                                } else if (flag.type == 2) {
                                    expend += flag.expenditure;
                                }
                            }
                        }
                    }
                    this.theader = "项目总收益: " + income + " 项目总支出:" + expend;
                }

            },
            // 页码大小
            onPageSizeChange(size) {
                this.projectMemberQuery.pageSize = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                console.log(index)
                this.projectMemberQuery.pageIndex = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
                this.editModal = false
            },
            /*
            * 得到子窗口数据
            * */
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData();
            },
            update(id) {
                this.$confirm('此操作将永久性退出, 是否继续?', '提示', {
                    confirmButtonText: '退出',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let update = {
                        id: id,
                        state: 0
                    }
                    this.$postData("projectMember_update", update).then(res => {
                        if (res.status == 200) {
                            this.$message.success("退出成功");
                            this.getData();
                        } else {
                            this.$message.error("退出失败" + res.msg);
                        }
                    })

                }).catch(() => {

                });
            },
            edit(id,capital) {
                console.log(capital)
                this.member={
                    memberId:id,
                    capital:capital
                }
                this.editModal = true;
            }
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
