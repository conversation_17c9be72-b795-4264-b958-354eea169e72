<template>
    <el-row style="padding-left: 20px;padding-right: 20px">
        <div>
            <el-dialog :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
            <el-tabs v-model="activeName" @tab-click="handleClick" type="card">
                <el-tab-pane label="认证信息" name="1">
                    <img :src="dom.headimg" width="150px" style="float: right"
                         @click="handlePictureCardPreview(dom.headimg)">
                    <div id="pdfDom" style="padding: 20px">
                        <h3>
                            姓名：{{dom.realName}}
                            <el-divider direction="vertical"></el-divider>
                            手机号：{{dom.phone}}
                            <el-divider direction="vertical"></el-divider>
                            身份证：{{dom.idcard}}
                            <el-divider direction="vertical"></el-divider>
                            <el-button type="primary" @click="rz()" v-if="(roleId=='1' || roleId=='66'|| roleId=='95') "
                                       v-loading="loading">一键认证
                            </el-button>
                            <el-button type="primary" @click="getPdf()">导出PDF</el-button>
                        </h3>
                        <br>
                        <div>
                            <div class="title">照片人证比对</div>
                            <div v-for="(i,index) in rzList0021" :key="index">

                                <div class="rzbody" v-if="index==0 || more0021">
                                    <div>
                                        <div class="rzbody-1">流水号：</div>
                                        <div class="rzbody-2">{{i.outTradeNo}}</div>
                                    </div>
                                    <div>
                                        <div class="rzbody-1">操作人：</div>
                                        <span class="rzbody-2">{{i.crePerson}}</span>
                                    </div>
                                    <div>
                                        <div class="rzbody-1">查询时间：</div>
                                        <span class="rzbody-2">{{i.tranTime}}</span>
                                    </div>
                                    <div>
                                        <div class="rzbody-1">查询结果：</div>
                                        <span class="rzbody-2"><span
                                                style="color: #2d8cf0">{{i.resultMsg}}</span></span>
                                    </div>

                                </div>

                            </div>
                            <div class="moreText" v-if="rzList0021.length>1" @click="more0021=!more0021">
                                {{!more0021?'查看更多':'收起更多'}}<i
                                    :class="!more0021?'el-icon-arrow-down':'el-icon-arrow-up'"></i></div>

                        </div>
                        <div>
                            <div class="title">不良人员名单</div>
                            <div v-for="(i,index) in rzList0626" :key="index">

                                <div class="rzbody" v-if="index==0 || more0626">
                                    <div>
                                        <div class="rzbody-1">流水号：</div>
                                        <div class="rzbody-2">{{i.outTradeNo}}</div>
                                    </div>
                                    <div>
                                        <div class="rzbody-1">操作人：</div>
                                        <span class="rzbody-2">{{i.crePerson}}</span>
                                    </div>
                                    <div>
                                        <div class="rzbody-1">查询时间：</div>
                                        <span class="rzbody-2">{{i.tranTime}}</span>
                                    </div>
                                    <div>
                                        <div class="rzbody-1">查询结果：</div>
                                        <span class="rzbody-2">
                                        <span style="color: #2d8cf0" v-if="i.result=='1'">无不良记录</span>
                                        <span style="color: #f00f14" v-if="i.result=='0'">

                                            存在不良记录
                                            <el-popover
                                                    placement="top-start"

                                                    width="1200"
                                                    trigger="hover"
                                                    :content="i.data">
                                            <div slot="reference"
                                                 style="display: inline-block;padding-left: 20px;color: #2d8cf0">查看</div>
                                          </el-popover>
                                        </span>
                                    </span>
                                    </div>

                                </div>

                            </div>
                            <div class="moreText" v-if="rzList0626.length>1" @click="more0626=!more0626">
                                {{!more0626?'查看更多':'收起更多'}}<i
                                    :class="!more0626?'el-icon-arrow-down':'el-icon-arrow-up'"></i></div>

                        </div>
                        <div>
                            <div class="title">法院信息详情查询（个人版）</div>
                            <div v-for="(i,index) in rzList0627" :key="index">

                                <div class="rzbody" v-if="index==0 || more0627">
                                    <div>
                                        <div class="rzbody-1">流水号：</div>
                                        <div class="rzbody-2">{{i.outTradeNo}}</div>
                                    </div>
                                    <div>
                                        <div class="rzbody-1">操作人：</div>
                                        <span class="rzbody-2">{{i.crePerson}}</span>
                                    </div>
                                    <div>
                                        <div class="rzbody-1">查询时间：</div>
                                        <span class="rzbody-2">{{i.tranTime}}</span>
                                    </div>
                                    <div>
                                        <div class="rzbody-1">查询结果：</div>
                                        <span class="rzbody-2">
                                        <span style="color: #2d8cf0" v-if="i.result=='1'">无不良记录</span>
                                        <span style="color: #f00f14" v-if="i.result=='0'">

                                            存在不良记录
                                            <el-popover
                                                    placement="top-start"

                                                    width="1200"
                                                    trigger="hover"
                                                    :content="i.data">
                                            <div slot="reference"
                                                 style="display: inline-block;padding-left: 20px;color: #2d8cf0">查看</div>
                                          </el-popover>
                                        </span>
                                    </span>
                                    </div>

                                </div>

                            </div>
                            <div class="moreText" v-if="rzList0627.length>1" @click="more0627=!more0627">
                                {{!more0627?'查看更多':'收起更多'}}<i
                                    :class="!more0627?'el-icon-arrow-down':'el-icon-arrow-up'"></i></div>

                        </div>
                        <div>
                            <div class="title">特殊名单验证</div>
                            <div v-for="(i,index) in rzList04216" :key="index">

                                <div class="rzbody" v-if="index==0 || more04216">
                                    <div>
                                        <div class="rzbody-1">流水号：</div>
                                        <div class="rzbody-2">{{i.outTradeNo}}</div>
                                    </div>
                                    <div>
                                        <div class="rzbody-1">操作人：</div>
                                        <span class="rzbody-2">{{i.crePerson}}</span>
                                    </div>
                                    <div>
                                        <div class="rzbody-1">查询时间：</div>
                                        <span class="rzbody-2">{{i.tranTime}}</span>
                                    </div>
                                    <div>
                                        <div class="rzbody-1">查询结果：</div>
                                        <span class="rzbody-2"><span
                                                style="color: #2d8cf0">{{i.resultMsg}}</span></span>
                                    </div>

                                </div>

                            </div>
                            <div class="moreText" v-if="rzList04216.length>1" @click="more04216=!more04216">
                                {{!more04216?'查看更多':'收起更多'}}<i
                                    :class="!more04216?'el-icon-arrow-down':'el-icon-arrow-up'"></i></div>

                        </div>
                      <div>
                        <div class="title">人员涉诉信息查询[详情版]</div>
                        <div v-for="(i,index) in rzList0638" :key="index">

                          <div class="rzbody" v-if="index==0 || more0638">
                            <div>
                              <div class="rzbody-1">流水号：</div>
                              <div class="rzbody-2">{{i.outTradeNo}}</div>
                            </div>
                            <div>
                              <div class="rzbody-1">操作人：</div>
                              <span class="rzbody-2">{{i.crePerson}}</span>
                            </div>
                            <div>
                              <div class="rzbody-1">查询时间：</div>
                              <span class="rzbody-2">{{i.tranTime}}</span>
                            </div>
                            <div>
                              <div class="rzbody-1">查询结果：</div>
                              <span class="rzbody-2">
                                        <span style="color: #2d8cf0" v-if="!i.data">无不良记录</span>
                                        <span style="color: #f00f14" v-if="i.data">

                                            存在不良记录
                                            <el-popover
                                                placement="top-start"

                                                width="1200"
                                                trigger="hover"
                                                :content="i.data">
                                            <div slot="reference"
                                                 style="display: inline-block;padding-left: 20px;color: #2d8cf0">查看</div>
                                          </el-popover>
                                        </span>
                                    </span>
                            </div>
                          </div>

                        </div>
                        <div class="moreText" v-if="rzList0638.length>1" @click="more0638=!more0638">
                          {{!more0638?'查看更多':'收起更多'}}<i
                            :class="!more0638?'el-icon-arrow-down':'el-icon-arrow-up'"></i></div>

                      </div>
                      <div>
                        <div class="title">人员涉诉信息精准查询</div>
                        <div v-for="(i,index) in rzList0637" :key="index">

                          <div class="rzbody" v-if="index==0 || more0637">
                            <div>
                              <div class="rzbody-1">流水号：</div>
                              <div class="rzbody-2">{{i.outTradeNo}}</div>
                            </div>
                            <div>
                              <div class="rzbody-1">操作人：</div>
                              <span class="rzbody-2">{{i.crePerson}}</span>
                            </div>
                            <div>
                              <div class="rzbody-1">查询时间：</div>
                              <span class="rzbody-2">{{i.tranTime}}</span>
                            </div>
                            <div>
                              <div class="rzbody-1">查询结果：</div>
                              <span class="rzbody-2">
                                        <span style="color: #2d8cf0" v-if="!i.data">无不良记录</span>
                                        <span style="color: #f00f14" v-if="i.data">

                                            存在不良记录
                                            <el-popover
                                                placement="top-start"

                                                width="1200"
                                                trigger="hover"
                                                :content="i.data">
                                            <div slot="reference"
                                                 style="display: inline-block;padding-left: 20px;color: #2d8cf0">查看</div>
                                          </el-popover>
                                        </span>
                                    </span>
                            </div>

                          </div>

                        </div>
                        <div class="moreText" v-if="rzList0637.length>1" @click="more0637=!more0637">
                          {{!more0637?'查看更多':'收起更多'}}<i
                            :class="!more0637?'el-icon-arrow-down':'el-icon-arrow-up'"></i></div>

                      </div>
                    </div>


                </el-tab-pane>
                <el-tab-pane label="认证图片" name="2">
                    <el-button v-if="showHandleByRoleId()" size="mini" type="primary" @click="dialogFormVisible = true" style="float: right">添加图片
                    </el-button>
                    <div>
                        <h3>已有图片:</h3>

                        <el-col :span="8" v-for="(item,index) in cers" :key="index">
                            <el-card style="margin: 20px;">
                                <el-image
                                        :src="item.certificateImg"
                                        @click="handlePictureCardPreview(item.certificateImg)"
                                        class="image"></el-image>
                                <div style="padding: 14px;">
                                    <div style="margin-bottom: 10px">
                                        <!--                        {{item.title}}-->
                                        <el-autocomplete
                                                class="inline-input"
                                                v-model="item.title"
                                                :fetch-suggestions="querySearch"
                                                placeholder="请输入内容"
                                        ></el-autocomplete>
                                        <!--                              @select="handleSelect"-->

                                        <el-tag v-if="showHandleByRoleId() && item.state==1" type="success">发布</el-tag>
                                        <el-tag v-else-if="showHandleByRoleId() && item.state!=1" type="warning">暂存</el-tag>
                                    </div>
                                    <div class="bottom clearfix">
                                        <el-button v-if="showHandleByRoleId()" type="danger" class="delbutton" @click="del(item.id)">删除</el-button>
                                        <el-button v-if="showHandleByRoleId() && item.state==1" type="info" class="delbutton"
                                                   @click="up(item,0)">暂存
                                        </el-button>
                                        <el-button v-if="showHandleByRoleId() && item.state!=1" type="primary" class="delbutton"
                                                   @click="up(item,1)">发布
                                        </el-button>
                                        <el-button v-if="showHandleByRoleId()" type="primary" class="delbutton" @click="up(item,1)">更新</el-button>
                                        <el-button v-if="showHandleByRoleId()" type="primary" class="delbutton" @click="upHead(item)"
                                                   v-show="'人脸头像'==item.title">设为头像
                                        </el-button>
                                        <el-button v-if="showHandleByRoleId()" type="primary" class="delbutton" @click="upRzHead(item)"
                                                   v-show="'认证头像'==item.title">设为认证
                                        </el-button>
                                    </div>
                                </div>
                            </el-card>
                        </el-col>
                    </div>

                </el-tab-pane>
            </el-tabs>
            <el-dialog title="添加认证图片" :visible.sync="dialogFormVisible">
                <el-form :model="form">
                    <el-form-item label="认证图片名称">
                        <!--                        <el-input v-model="form.title" autocomplete="off"></el-input>-->
                        <el-autocomplete
                                class="inline-input"
                                v-model="form.title"
                                clearable
                                :fetch-suggestions="querySearch"
                                placeholder="请输入内容"
                        ></el-autocomplete>
                    </el-form-item>
                    <el-upload
                            :limit="1"
                            class="upload-demo"
                            action="https://biapi.xiaoyujia.com/files/uploadFiles"
                            list-type="picture"
                            :on-success="handleAvatarSuccess"><!--:show-file-list="false"-->
                        <el-button v-if="showHandleByRoleId()" size="mini" type="primary" @click="dialogFormVisible = true">选择图片</el-button>
                    </el-upload>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button v-if="showHandleByRoleId()" @click="dialogFormVisible = false">取 消</el-button>
                    <el-button v-if="showHandleByRoleId()" type="primary" @click="save">确 定</el-button>
                </div>
            </el-dialog>


        </div>

    </el-row>
</template>

<script>

    export default {
        name: "baomuImg",
        props: ['model'],
        data() {
            return {
                head: '人脸头像',
                roleId: localStorage.getItem("roleId"),
                storeId: localStorage.getItem("storeId"),
                activeName: "1",
                restaurants: [
                    {value: "人脸头像"},
                    {value: "认证头像"},
                    {value: "家政员证"},
                    {value: "月嫂证"},
                    {value: "育婴师证"},
                    {value: "护工证"},
                    {value: "催乳师证"},
                    {value: "健康证"},
                    {value: "体检表"},
                    {value: "驾驶证"},
                    {value: "身份证（正面）"},
                    {value: "身份证（反面）"},
                    {value: "保姆证"},
                    {value: "教师证"},
                    {value: "毕业证"},
                ],
                form: {
                    certificateImg: null,
                    title: null,
                    employeeId: null,
                },
                dialogVisible: false,
                dialogImageUrl: null,
                dialogFormVisible: false,
                listImg: [],
                dom: this.model,
                cers: [],
                rzList: [],
                rzList0021: [],
                rzList0626: [],
                rzList0627: [],
                rzList0637: [],
                rzList0638: [],
                rzList04216: [],
                more0021: false,
                more0626: false,
                more0627: false,
                more0637: false,
                more0638: false,
                more04216: false,
                loading: false,
                htmlTitle: null,
            }
        },
        created() {
            if (this.dom.headimg == null) {
                this.dom.headimg = this.dom.headPortrait
            }
            this.getData();
            this.jizhengyunList();
            this.htmlTitle = this.dom.realName
        },
        methods: {
            showHandleByRoleId() {
                if (this.roleId == null) {
                    return false;
                }
                return this.roleId != 85;
            },
            rz() {
                if (this.dom.realName == null || this.dom.idcard == null || this.dom.headimg == null || this.dom.phone == null) {
                    return this.$message.error("阿姨信息不完整，无法一键认证");
                }

                let jzyParam = {
                    name: this.dom.realName,
                    certNo: this.dom.idcard,
                    imgUrl: this.dom.headimg,
                    phone: this.dom.phone,
                    employeeId: this.dom.id,
                    crePerson: localStorage.getItem("realName"),
                    storeId: localStorage.getItem("storeId"),
                }
                if (jzyParam.imgUrl == null || jzyParam.imgUrl === '') {
                    jzyParam.imgUrl = this.dom.headSource;
                }
                if (jzyParam.imgUrl == null || jzyParam.imgUrl === '') {
                    jzyParam.imgUrl = this.dom.headPortrait;
                }
                if (jzyParam.imgUrl == null || jzyParam.imgUrl === '') {
                    return this.$message.error("阿姨信息不完整，无法一键认证");
                }
                console.info('jzd',jzyParam);
                // let param={
                //     jzyParam:jzyParam,
                //     employeeId:this.dom.id,
                //     crePerson:localStorage.getItem("realName"),
                // }
                this.loading = true;
                this.$postData("jzyAll", jzyParam, {}).then(res => {
                    if (res.status != 200) {
                        this.$message.error("认证失败，" + res.msg);
                    }
                    this.jizhengyunList();
                })
            },
            handleClick(tab, event) {
                // console.log(tab, event);
            },
            createFilter(queryString) {
                return (restaurant) => {
                    return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                };
            },
            querySearch(queryString, cb) {
                let restaurants = this.restaurants;
                let results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
                // 调用 callback 返回建议列表的数据
                cb(results);
            },
            handlePictureCardPreview(url) {
                this.dialogImageUrl = url;
                this.dialogVisible = true;
            },
            save() {
                this.form.employeeId = this.dom.id;
                this.$postData("add_Certificate", this.form, {}).then(res => {
                    if (res.status == 200) {
                        this.form.certificateImg = null;
                        this.form.title = null;
                        this.listImg = [];
                        this.$message.success("添加成功，" + res.msg);
                        this.dialogFormVisible = false;
                        this.getData();

                    } else {
                        this.$message.error("添加失败，" + res.msg);
                    }
                })

            },
            upHead(row) {
                let dom = {
                    id: row.employeeId,
                    headPortrait: row.certificateImg
                };

                // console.log(row)
                this.$postData("update_employee", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.$Message.success('修改成功');
                        this.chooseThisModel();
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })
            },
            upRzHead(row) {
                let dom = {
                    id: row.employeeId,
                    headSource: row.certificateImg
                };

                // console.log(row)
                this.$postData("update_employee", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.$Message.success('修改成功');
                        this.dom.headimg = row.certificateImg;
                        this.chooseThisModel();
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })
            },
            chooseThisModel(name) {
                this.dom = null;
                this.$emit('init-choose', "");
            },
            up(row, stat) {
                row.state = stat;
                this.$postData("up_Certificate", row, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.success("变更成功，" + res.msg);
                        this.dialogFormVisible = false;
                        this.getData();

                    } else {
                        this.$message.error("变更失败，" + res.msg);
                    }
                })
            },
            insertImg() {
                // console.log()
            },
            handleAvatarSuccess(res, file) {
                this.form.certificateImg = res.data;
            },
            del(id) {
                this.$postUrl("del_Certificate", id, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.error("删除成功，" + res.msg);
                        this.getData();
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })
            },
            getData() {
                this.$postUrl("list_Certificate", this.dom.id, {}).then(res => {
                    if (res.status == 200) {

                        this.cers = res.data;
                        res.data.forEach((cer, index) => {
                            this.listImg.push(
                                cer.certificateImg
                            );
                        });
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })
            },
            jizhengyunList() {
                this.$getData("jizhengyunList", {employeeId: this.dom.id}, {}).then(res => {
                    if (res.status == 200) {
                        this.loading = false;

                        this.rzList = res.data;
                        if (this.rzList.length > 0) {
                            this.rzList0021 = [];
                            this.rzList0626 = [];
                            this.rzList0627 = [];
                            this.rzList0637 = [];
                            this.rzList0638 = [];
                            this.rzList04216 = [];
                            this.rzList.forEach(v => {
                                if (v.type == '0021') {
                                    this.rzList0021.push(v)
                                }
                                if (v.type == '0626') {
                                    this.rzList0626.push(v)
                                }
                                if (v.type == '0627') {
                                    this.rzList0627.push(v)
                                }
                              if (v.type == '0637') {
                                this.rzList0637.push(v)
                              }
                              if (v.type == '0638') {
                                this.rzList0638.push(v)
                              }
                                if (v.type == '04216') {
                                    this.rzList04216.push(v)
                                }
                            })
                        }
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })
            },
        }
    }
</script>

<style scoped>
    .moreText {
        padding-left: 300px;
    }

    .rzbody-1 {
        width: 300px;
        border: 1px solid #606266;
        background: #e8eaec;
        display: inline-block;
        text-align: center;
        padding: 5px;

    }

    .rzbody-2 {
        width: 300px;
        border: 1px solid #606266;
        border-left: none;
        display: inline-block;
        text-align: center;
        padding: 5px;
    }

    .rzbody {
        padding: 10px;
        line-height: 20px;
    }

    .title {
        font-size: 15px;
        font-weight: bold;
        border-left: 5px solid #2d8cf0;
        padding-left: 10px;
    }

    .delbutton {
        float: right;
    }

    .image {
        width: 100%;
        display: block;
    }

    .clearfix:before,
    .clearfix:after {
        display: table;
        content: "";
    }

    .clearfix:after {
        clear: both
    }
</style>
