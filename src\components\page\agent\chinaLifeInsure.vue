<template>
  <div>
    <div class="handle-box">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-date-picker
              :unlink-panels="true"
              v-model="time"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
          </el-date-picker>
        </el-col>

        <el-col :span="3">
          <el-input v-model="model.employeeName" placeholder="保姆姓名"></el-input>
        </el-col>
        <el-col :span="2">
          <el-button type="primary" @click="query()">搜索</el-button>
        </el-col>
        <el-col :span="2">
          <el-button type="success" @click="doInsure()" v-if="model.status == 1">合同上险</el-button>
        </el-col>
<!--        <el-col :span="2">-->
<!--          <el-button type="danger" @click="doInsure()"  v-if="model.status == 2">合同下险</el-button>-->
<!--        </el-col>-->
        <!--        <el-col :span="2">-->
        <!--          <el-button type="danger" @click="doInsure()"  v-if="model.status == 2">合同下险</el-button>-->
        <!--        </el-col>-->
        <el-col :span="2">
          <el-button type="danger" @click="exportData()">导出</el-button>
        </el-col>
      </el-row>
      <el-divider></el-divider>
      <el-link type="warning">上户状态下搜的是合同开始服务时间区间,下户状态下搜的是合同服务结束时间区间</el-link>
      <el-divider></el-divider>
      <el-radio-group v-model="model.status" @change="query">
        <el-radio-button label='1'>上户</el-radio-button>
        <el-radio-button label='2'>下户</el-radio-button>
      </el-radio-group>
      <el-table
          :data="list"
          border
          style="width: 100%"
          v-loading="loading"
          @selection-change="handleSelectionChange">
        <el-table-column
            type="selection"
            width="55">
        </el-table-column>
        <el-table-column
            prop="no"
            label="合同号"
            width="150">
        </el-table-column>
        <el-table-column
            prop="no"
            label="合同是否被补签"
            width="120">
          <template slot-scope="scope">
            <el-tag type="danger" v-if="scope.row.semployeeName">补签</el-tag>
            <el-tag type="success" v-else>未补签</el-tag>
          </template>
        </el-table-column>
        <el-table-column
            prop="no"
            label="订单是否欠款"
            width="120">
          <template slot-scope="scope">
            <el-tag type="danger" v-if="scope.row.arrears">欠款</el-tag>
            <el-tag type="success" v-else>未欠款</el-tag>
          </template>
        </el-table-column>
        <el-table-column
            label="现阿姨"
            width="100">
          <template slot-scope="scope">
              <span>{{ scope.row.semployeeName == null ? scope.row.employeeName : scope.row.semployeeName }}</span>
          </template>
        </el-table-column>
        <el-table-column
            label="现阿姨身份证"
            width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.semployeeName == null ? scope.row.employeeCarId : scope.row.semployeeCarId }}</span>
          </template>
        </el-table-column>
        <el-table-column
            label="员工签名时间"
            width="150">
          <template slot-scope="scope">
            <span>{{scope.row.semployeeName !== null ? scope.row.semployeeSignDate : scope.row.employeeSignDate}}</span>
          </template>
        </el-table-column>
        <el-table-column
            label="客户签名时间"
            width="150">
          <template slot-scope="scope">
            <span>{{scope.row.semployeeName !== null ? scope.row.smemberSignDate : scope.row.memberSignDate}}</span>
          </template>
        </el-table-column>
        <el-table-column
            label="员工上保时间"
            width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.supplyInsureDate == null ? scope.row.insureDate : scope.row.supplyInsureDate }}</span>
          </template>
        </el-table-column>
        <el-table-column
            label="原阿姨"
            width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.oldEmployeeName == null ? '无' : scope.row.oldEmployeeName }}</span>
          </template>
        </el-table-column>
        <el-table-column
            label="原阿姨身份证"
            width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.oldEmployeeName == null ? '无' : scope.row.oldEmployeeCarId }}</span>
          </template>
        </el-table-column>
        <el-table-column
            label="合同开始时间"
            width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.sstartDate == null ? scope.row.serviceStarDate : scope.row.sstartDate}}</span>
          </template>
        </el-table-column>
        <el-table-column
            prop="serviceEndDate"
            label="合同结束时间"
            width="180">
        </el-table-column>
        <el-table-column
            prop="servicePay"
            label="合同金额"
            width="180">
        </el-table-column>
        <el-table-column
            prop="orderAmount"
            label="客户已付金额"
            width="180">
        </el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="pageInfo.total"
              @on-change="onChange"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onPageSizeChange"
              :page-size="pageInfo.size"/>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  name: "chinaLifeInsure",
  data() {
    return {
      time:[],
      list: [],
      loading:false,
      multipleSelection: [],
      pageSizeOpts: [10, 20, 30, 50, 100],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      model: {
        current: 1,
        size: 10,
        status:1,
        startDate:null,
        endDate:null,
        employeeName:null

      },

    }
  },created() {
    this.getData();
  },
  methods:{
    doInsure(){
    let param = {
      insureType:this.model.status,
      contracts:this.multipleSelection
    }
    console.log(JSON.stringify(param))
      this.$postData("doInsure", param, {}).then(res => {
        if (res.status == 200) {
          this.$message.success(res.data);
          // this.getData();
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })

    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      console.log(this.multipleSelection)
    },
    query(){

      this.loading = true;
      if (this.time){
        this.model.startDate = this.time[0];
        this.model.endDate = this.time[1];
      } else {
        this.model.startDate = null;
        this.model.endDate = null;
      }
      this.getData();
    },
    exportData(){
      let name = "人寿保险-下保清单";
    if (this.model.status == 1){
      name = "人寿保险-上保清单";
    }
      this.loading = true;
      this.$postData("downloadChinaLife", this.model, {
        responseType: "arraybuffer"
      }).then(res => {
        this.blobExport({
          tablename: name,
          res: res
        });
      })
      this.loading = false;
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
    getData(){
      this.$postData("chinaLifeContractBxPage", this.model, {}).then(res => {
        if (res.status == 200) {

          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
          this.loading = false;
        } else {
          this.$message.error("查询失败，" + res.msg);
          this.loading = false;
        }
      })
    },
    // 跳转页码
    onChange(index) {
      console.log(index)
      this.model.current = index;
      this.getData();
    },
    // 页码大小
    onPageSizeChange(size) {
      console.log(size)
      this.model.size = size;
      this.getData();
    },
  }
}
</script>

<style scoped>

</style>
