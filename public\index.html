<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge, chrome=1" />
  <meta name="renderer" content="webkit|ie-comp|ie-stand" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="format-detection" content="telephone=no, email=no, address=no" />
  <meta name="robots" Content="none" />
  <meta name="author" content="kejk" />
  <link rel="icon" type="image/x-icon" href="/favicon.ico" />
  <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico" />
  <link rel="bookmark" type="image/x-icon" href="/favicon.ico" />
  <link rel="stylesheet" href="//at.alicdn.com/t/font_830376_qzecyukz0s.css">
  <meta name="ProgId" content="Excel.Sheet"/>
  <meta name="Generator" content="WPS Office ET"/>
<!--  <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=b81093df27d6801461e9bf0151210b09&plugin=AMap.Geocoder"></script>-->
  <script src="http://api.map.baidu.com/api?v=3.0&ak=ukhfLmTzcpM3XjImQ1q90jlBIPO5ajMW&s=1"></script>
  <script type="text/javascript" src="//api.map.baidu.com/library/Heatmap/2.0/src/Heatmap_min.js"></script>
  <title>小羽佳家政 大数据系统</title>
</head>
<script>
  // ==UserScript==
  // @name           使用阿里巴巴普惠体作为网页默认字体
  // @description    网页字体替换为阿里巴巴普惠体(优先)或微软雅黑
  // @include        *:*
  // <AUTHOR>
  // @version        1.08
  // @namespace https://greasyfork.org/users/704275
  // ==/UserScript==
  (function() {
    function addStyle(rules) {
      var styleElement = document.createElement('style');
      styleElement.type = 'text/css';
      document.getElementsByTagName('head')[0].appendChild(styleElement);
      styleElement.appendChild(document.createTextNode(rules));
    }

    addStyle('body,form,div,ul,ol,li,h1,h2,h3,h4,h5,h6,span,table,tr,th,td,p,input,dl,dt,dd,ul,ol,li,input,textarea,a,label,b {font-family : "阿里巴巴普惠体","Alibaba Sans","微软雅黑","Microsoft YaHei" !important;}');
  })();
</script>
<body>

  <noscript>
    <strong>We're sorry but vms doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
  </noscript>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>
