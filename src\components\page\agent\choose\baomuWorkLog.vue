<template>
    <div style="padding: 20px">
        <el-row>
            <el-col :span="12">
                <el-collapse v-model="activeNames">

                    <el-collapse-item title="新增保姆日志" name="1">
                        <template slot="title">
                            <el-button type="primary">添加保姆日志</el-button>

                        </template>
                        <el-form ref="form" :model="baomuWorkLog">
                            <el-form-item prop="remark">
                                <div class="label-name">标题:&nbsp
                                    <el-button size="mini" v-for="(i, index) in titleList" :key="index"
                                               @click="baomuWorkLog.title=i">{{i}}
                                    </el-button>
                                </div>
                                <el-input placeholder="请输入标题" v-model="baomuWorkLog.title" style="width: 70%">
                                </el-input>
                            </el-form-item>
                            <el-form-item prop="workContent">
                                <div class="label-name">内容:</div>
                                <el-input placeholder="请输入工作内容" type="textarea" v-model="baomuWorkLog.workContent"
                                          style="width: 70%">
                                </el-input>
                            </el-form-item>

                            <el-form-item prop="remark">
                                <div class="label-name">备注:</div>
                                <el-input placeholder="请输入备注" v-model="baomuWorkLog.remark" style="width: 70%">
                                </el-input>
                            </el-form-item>
                            <el-form-item prop="startWorkTime">
                                <div class="label-name">开始时间:</div>
                                <el-date-picker
                                        style="width: 70%"
                                        v-model="baomuWorkLog.startTime"
                                        type="date"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        placeholder="开始时间">
                                </el-date-picker>
                            </el-form-item>
                            <el-form-item prop="endWorkTime" label="">
                                <div class="label-name">结束时间:</div>
                                <el-date-picker
                                        style="width: 70%"
                                        v-model="baomuWorkLog.endTime"
                                        type="date"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        placeholder="结束时间">
                                </el-date-picker>
                            </el-form-item>


                            <div style="margin-right: 100px;float: right">
                                <Button type="primary" @click="save('form')">确定</Button>
                            </div>
                        </el-form>
                    </el-collapse-item>
                </el-collapse>
                <br>
                <el-timeline :reverse="true">
                    <el-timeline-item v-for="(activity, index) in list"
                                      :key="index"
                                      :timestamp="activity.startTime" placement="top">
                        <el-card>
                            <h4> {{activity.title}}-{{activity.contractNo}}
                                <el-button type="primary" plain v-if="activity.title=='订单合同服务'"
                                           @click="showPhoneContract(activity.id)">查看合同
                                </el-button>
                            </h4>
                            <p>内容：{{activity.workContent}}</p>
                            <p>操作人：{{activity.crePerson}}</p>
                            <p>时间：{{activity.startTime}} -- {{activity.endTime}}</p>
                            <p>备注：{{activity.remark}}</p>
                            <br>
                            <!--                            <el-button type="danger" plain v-if="activity.id!==null" @click="del(activity.id)">删除</el-button>-->
                        </el-card>
                    </el-timeline-item>

                </el-timeline>
            </el-col>
            <el-col :span="12">
                <center>

                    <iframe
                            id="iframeId" :src="url" frameborder="0" class="pc iframe" scrolling="auto"
                            v-if="url!==null">
                    </iframe>

                </center>
            </el-col>

        </el-row>


    </div>

</template>

<script>
    export default {
        name: "baomuWorkLog",
        props: ['employeeId'],
        data() {
            return {
                url: null,
                id: this.employeeId,
                list: [],
                baomuWorkLog: {
                    id: null,
                    employeeId: this.employeeId,
                    workContent: null,
                    remark: null,
                    status: 1,
                    billNo: null,
                    contractNo: null,
                    startTime: null,
                    endTime: null,
                    creatTime: null,
                    crePerson: localStorage.getItem("realName"),
                    type: 0,
                    title: null,


                },
                activeNames: [],
                titleList: ["上户面试", "恶意行为", "保姆培训"]

            }
        },
        created: function () {
            this.getData()
        },
        methods: {
            showPhoneContract(id) {
                this.url = "https://agent.xiaoyujia.com/upbaomu/contractInfo/" + id
            },
            save(name) {
                if (this.baomuWorkLog.title == null || this.baomuWorkLog.workContent == null || this.baomuWorkLog.startTime == null || this.baomuWorkLog.endTime == null) {
                    return this.$Message.error('请将内容填写完整');

                }
                this.$postData("saveBaomuWorkLog", this.baomuWorkLog, {}).then(res => {
                    if (res.status == 200) {
                        this.$Message.success('保存成功');
                        this.activeNames = []
                        this.getData()
                    } else {
                        this.$message.error("保存失败，" + res.msg);
                    }
                })
            },
            del(id) {
                this.$getData("delBaomuWorkLog", {id: id}).then(res => {
                    if (res.status == 200) {

                        this.getData()

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getData() {
                this.$getData("getBaomuWorkLog", {employeeId: this.id}).then(res => {
                    if (res.status == 200) {

                        this.list = res.data;

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            /*
        * 关闭当前窗口
        * */
            chooseThisModel(name) {
                this.id = null;
                this.$emit('init-choose', "");
            },
        }
    }
</script>

<style scoped>
    .iframe {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        border: 1px solid #ddd;
        padding: 10px;
        top: 200px;
        right: 200px;
        width: 375px;
        height: 667px;
        background: #fff;
        overflow-y: hidden;
    }
</style>
