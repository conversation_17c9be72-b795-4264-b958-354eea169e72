<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv" style="margin: 0 auto;width: 85%">
            <el-upload
                    class="upload-demo"
                    drag
                    action="https://biapi.xiaoyujia.com/contract/insertInformation"
                    :on-success="handlePreview"
                    :on-progress="progress"
                    :headers="headers"
                    :on-error="handleError"
                    :on-change="onChange"
                    :on-remove="handleRemove"
                    :before-upload="before"
                    :on-exceed="onExcees"
                    :file-list="fileList"
                    :limit="1"
                    list-type="xlsx">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip">只能上传xlsx文件</div>
                <div class="el-upload__tip" slot="tip">批量导入时确定列表有该身份证信息且操作日期是2022-03-01(包括)后的</div>
            </el-upload>
            <div>
                <el-button size="small" style="margin-top: 20px;" type="success"
                           @click="handlePictureCardPreview">
                    下载模版
                </el-button>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                headers: {token:localStorage.getItem('token')},
                fileList: [],
                loading: null,
                logs: []
            }
        },
        components: {},
        created: function () {
        },
        methods: {
            //可视底部
            scrollBottom() {
                let ele = document.getElementById("div");
                //判断元素是否出现了滚动条
                if (ele.scrollHeight > ele.clientHeight) {
                    //设置滚动条到最底部
                    ele.scrollTop = ele.scrollHeight;
                }
            },
            submitUpload() {
                if (this.fileList.length === 0) {
                    this.$message({
                        type: 'error',
                        message: '请选择文件!'
                    });
                    return
                }
                this.$refs.upload.submit();
            },
            onChange(file, fileList) {
                console.log(fileList);
                this.fileList = fileList
            },
            handleError(file, fileList) {
                console.log(file);
                this.$message({
                    type: 'error',
                    message: '导入失败!'
                });
                this.loading.close();
            },
            handlePictureCardPreview() {
                this.download("http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/qiye1646183269238example.xlsx");
            },
            onExcees() {
                this.$Message.success('只可上传一个文件。');
            },
            download(src) {
                let x = new XMLHttpRequest();
                x.open("GET", src, true);
                x.responseType = 'blob';
                x.onload = function (e) {
                    let url = window.URL.createObjectURL(x.response);
                    let a = document.createElement('a');
                    a.href = url;
                    a.download = "人员信息模版.xlsx";
                    a.click();
                };
                x.send();
            },
            handleRemove(file) {
                this.fileList = [];
            },
            before() {
                if (this.fileList.length > 1) {
                    this.$Notice.warning({
                        title: '最多可上传1个附件'
                    });
                }
            },
            progress(){
                this.loading = this.$loading({
                    lock: true,
                    text: '拼命加载中',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
            },

            handlePreview(file) {
                console.log(file);
                this.$message({
                    type: 'success',
                    message: '导入成功!'
                });
                this.loading.close();
                this.$emit('init-choose', "");
            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                console.log(322323);
                this.$emit('init-choose', "");
            },

        },

    }
</script>


<style>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;
        height: 300px;

    }

    .el-upload--text {
        background-color: #fff;
        border: none !important;
        border-radius: 0px;
        box-sizing: border-box;
        width: auto !important;
        height: auto !important;
        text-align: center;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

</style>

