<template>
<div>
  <div class="handle-box">
    <el-form ref="form" :model="pageInfo">
      <el-row>
        <el-col :span="4">
          <el-form-item label="月份">
            <el-date-picker
                v-model="quer.month"
                type="month"
                value-format="yyyy-MM"
                style="width: 200px"
                placeholder="选择月"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="姓名">
            <el-input v-model="quer.realName" placeholder="请输入姓名"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="状态">
            <el-select v-model="quer.type">
              <el-option label="全部" value=""></el-option>
              <el-option label="超时强派" value="1"></el-option>
              <el-option label="超时未接单" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4">
          <el-button type="primary" @click="query()" icon="el-icon-search" >搜索</el-button>
        </el-col>
      </el-row>

    </el-form>
  </div>
  <el-table :data="list" height="500px" v-loading="loading" border
            :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
    <el-table-column
        prop="month"
        width="200"
        label="月份">
    </el-table-column>
    <el-table-column
        label="经纪人">
      <template slot-scope="scope">
       <span> ({{scope.row.no}}){{scope.row.realName}}</span>
      </template>
    </el-table-column>
    <el-table-column
        label="类型">
      <template slot-scope="scope">
        <div v-if="scope.row.type ===1">超时强派</div>
        <div v-if="scope.row.type ===2">超时未接单</div>
      </template>
    </el-table-column>
    <el-table-column
        prop="count"
        label="次数">
    </el-table-column>
    <el-table-column
        prop="needsCount"
        label="分配线索数">
    </el-table-column>
    <el-table-column
        label="未接单率/强派率">
      <template slot-scope="scope">
        <span>{{getnumber(scope.row)}}</span>
      </template>
    </el-table-column>

  </el-table>
  <div class="pagination">
    <Page :total="pageInfo.total"
          @on-change="onChange"
          :current="this.dto.current"
          :show-total="true"
          :show-sizer="true"
          :page-size-opts="pageSizeOpts"
          @on-page-size-change="onPageSizeChange"
          :page-size="10"/>
  </div>




</div>
</template>

<script>
export default {
  name: "delayOrder",
  data() {
    return {
      list: null,
      loading: false,
      pageSizeOpts: [10, 20, 40],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      dto: [],
      quer:{
        size: 10,
        current: 1,
        month:null,
        type:null,
        realName:null,
      },

    }
  },
  created() {
    this.getData();
  },
  methods:{
    query(){
      this.getData();
    },
    getData() {
      this.$postData("selectDelayOrder", this.quer).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.quer.current = index;
      this.getData();
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.quer.size = size;
      this.getData();
    },
    getnumber(data){
      if (data.needsCount == 0){
        return 0;
      } else {
        let num = data.count/data.needsCount;
        let data1 = (num*100).toFixed(2)+"%"
        return data1;
      }
    }
  }
}
</script>

<style scoped>

</style>