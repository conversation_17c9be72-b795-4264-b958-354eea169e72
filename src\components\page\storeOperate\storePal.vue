<template>
    <div class="container">
        <div class="handle-box">
            <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="选择门店">
                    <el-select ref="storeNameSel" filterable v-model="dom.storeId" placeholder="请选择门店" clearable>
                        <el-option
                                v-for="(item,index) in storeList"
                                :key="index"
                                :label="item.text"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="时间">
                    <el-date-picker
                            v-model="time"
                            type="month"
                            :picker-options="setDisabled"
                            placeholder="选择日期时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="盈亏">
                    <el-select ref="storeNameSel" filterable v-model="dom.isPal" placeholder="盈亏情况" clearable>
                        <el-option
                                v-for="(item,index) in isPalList"
                                :key="index"
                                :label="item.text"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-search" type="primary" @click="onQuery" v-loading="loading">
                        查询
                    </el-button>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-refresh" @click="reset()" v-loading="loading">
                        重置
                    </el-button>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-download" type="primary" plain @click="exportExcel" v-loading="loading">
                        导出
                    </el-button>
                </el-form-item>
            </el-form>
        </div>

        <el-table :data="dataList" height="550px" v-loading="loading"
                  :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
            <el-table-column
                    label="序号"
                    type="index"
                    width="80"
                    align="center">
            </el-table-column>
            <el-table-column
                    prop="createTime"
                    label="经营月份"
            ></el-table-column>
            <el-table-column
                    prop="storeName"
                    label="门店名称"
                    width="200"
                    align="center"
            ></el-table-column>
            <el-table-column
                    prop="realName"
                    label="门店店长"
                    align="center">
            </el-table-column>
            <el-table-column
                    prop="openTime"
                    label="开业时间"
                    align="center"
            >
            </el-table-column>
            <el-table-column
                    prop="days"
                    label="开业周期"
                    align="right"
            >
                <template slot-scope="scope">
                    {{ scope.row.days }}天
                </template>
            </el-table-column>
            <el-table-column
                    prop="intermediary"
                    label="中介收入"
                    align="right"
            >
                <template slot-scope="scope">
                    <span v-if="scope.row.intermediary != null">
                        {{ scope.row.intermediary.toFixed(2) }}
                    </span>
                    <span v-else>{{ scope.row.intermediary }}</span>
                </template>
            </el-table-column>
            <el-table-column
                    prop="abc"
                    label="开发收入"
                    align="right"
            >
                <template slot-scope="scope">
                    <span v-if="scope.row.abc != null">
                        {{ scope.row.abc.toFixed(2) }}
                    </span>
                    <span v-else>{{ scope.row.abc }}</span>
                </template>
            </el-table-column>
            <el-table-column
                    prop="storeCost"
                    label="门店成本"
                    align="right"
            >
                <template slot-scope="scope">
                    <span v-if="scope.row.storeCost != null">
                        {{ scope.row.storeCost.toFixed(2) }}
                    </span>
                    <span v-else>{{ scope.row.storeCost }}</span>
                </template>
            </el-table-column>
            <el-table-column
                    prop="storeGross"
                    label="门店毛利"
                    align="right"
            >
                <template slot-scope="scope">
                    <span v-if="scope.row.storeGross != null">
                        {{ scope.row.storeGross.toFixed(2) }}
                    </span>
                    <span v-else>{{ scope.row.storeGross }}</span>
                </template>
            </el-table-column>
            <el-table-column
                    prop="storePal"
                    label="门店盈亏"
                    align="right"
            >
                <template slot-scope="scope">
                    <span v-if="scope.row.storePal != null">
                        {{ scope.row.storePal.toFixed(2) }}
                    </span>
                    <span v-else>{{ scope.row.storePal }}</span>
                </template>
            </el-table-column>
        </el-table>
        <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChangePage"
                  :current="dom.current"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="dom.size"/>
        </div>
    </div>
</template>

<script>
    export default {
        name: "",
        data() {
            return {
                loading: false,
                storeId: null,
                storeList: [],
                isPalList: [
                    {"text": "全部", "value": null},
                    {"text": "盈利", "value": 1},
                    {"text": "亏损", "value": -1}
                ],
                time: new Date(),
                dom: {
                    storeId: null,
                    time: null,
                    isPal: null,
                    eId: localStorage.getItem('id'),
                    size: 15,
                    current: 1
                },
                dataList: [],
                pageSizeOpts: [10, 15, 20, 30, 50, 100],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                setDisabled: {
                    disabledDate(time) {
                        return time.getTime() >= Date.now();  // 可选历史天、可选当前天、不可选未来天
                        // return time.getTime() > Date.now() - 8.64e7;  // 可选历史天、不可选当前天、不可选未来天
                        // return time.getTime() < Date.now() - 8.64e7;  // 不可选历史天、可选当前天、可选未来天
                        // return time.getTime() < Date.now(); // 不可选历史天、不可选当前天、可选未来天
                    },
                },
            }
        },
        async created() {
            await this.getStoreData();
            await this.getData();
        },
        mounted() {
        },
        methods: {
            async getStoreData() {
                this.loading = true;
                await this.$postData("getStoreDataByEid", JSON.stringify({id: this.dom.eId}), {}).then((res) => {
                    if (res.status === 200) {
                        this.storeList = res.data;
                    } else {
                        this.$message.error(res.msg);
                    }
                }).finally(() => {
                    this.loading = false;
                });
            },
            onQuery() {
                this.dom.size = 15;
                this.dom.current = 1;
                this.getData();
            },
            async getData() {
                this.loading = true;
                if (this.time == null) {
                    this.loading = false;
                    return this.$message.error("请选择时间后再查询");
                }
                const date = new Date(this.time);
                const year = date.getFullYear();
                let month = date.getMonth() + 1;

                if (month < 10) {
                    month = "0" + month;
                } else {
                    month = month + "";
                }
                this.dom.time = year + month;
                await this.$postData("getStorePalPageData", JSON.stringify(this.dom), {}).then((res) => {
                    if (res.status === 200) {
                        this.dataList = [];
                        this.dataList = res.data.records;
                        this.pageInfo.total = res.data.total;
                    } else {
                        this.$message.error(res.msg);
                    }
                }).finally(() => {
                    this.loading = false;
                });

            },
            reset() {
                this.dom.size = 15;
                this.dom.current = 1;
                this.dom.storeId = null;
                this.dom.isPal = null;
                this.time = new Date();
                this.getData();
            },
            onChangePage(index) {
                this.dom.current = index;
                this.getData();
            },
            onPageSizeChange(size) {
                this.dom.size = size;
                this.getData();
            },
            exportExcel() {
                this.loading = true;
                this.$postData("exportStorePal", JSON.stringify(this.dom), {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tableName: "门店盈亏报表",
                        res: res
                    });
                });
            },

            blobExport({tableName, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tableName + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
                const that = this;
                setTimeout(function () {
                    that.loading = false;
                }, 2500);

            },
        }
    }
</script>

<style scoped>
    /*滚动条的宽度*/
    /deep/ .el-table__body-wrapper::-webkit-scrollbar {
        /*横向滚动条*/
        width: 6px;
        /*纵向滚动条 必写*/
        height: 6px;

    }

    /*滚动条的滑块*/
    /deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 3px;
    }

    .el-row {
        margin-bottom: 20px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .el-col {
        border-radius: 4px;
    }

    .bg-purple-dark {
        background: #99a9bf;
    }

    .bg-purple {
        background: #d3dce6;
    }

    .bg-purple-light {
        background: #e5e9f2;
    }

    .grid-content {
        border-radius: 4px;
        min-height: 36px;
    }

    .row-bg {
        padding: 10px 0;
        background-color: #f9fafc;
    }
</style>
