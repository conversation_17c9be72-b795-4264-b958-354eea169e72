<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">

                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="名称">
                                <el-input
                                        v-model="model.name"
                                        placeholder="名称"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="手机号">
                                <el-input
                                        v-model="model.bindTel"
                                        placeholder="手机号"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" >

                            <el-form-item>
                                <el-button type="success"  round @click="query()">搜索</el-button>
                                <el-button type="info"  round @click="re()">重置</el-button>
                                <el-button type="success" round  @click="addModal=true">添加</el-button>
                            </el-form-item>

                        </el-col>
                    </el-row>

                </el-form>
            </div>
            <el-table :data="list" :header-cell-style="{background:'#ddd'}" @sort-change="sortChange" border class="table" ref="multipleTable" >
                <el-table-column
                        type="selection"
                        width="35">
                </el-table-column>
<!--                <el-table-column-->
<!--                        prop="remark"-->
<!--                        fixed="left"-->
<!--                        label="备注信息"-->
<!--                        -->
<!--                        width="120"-->
<!--                ></el-table-column>-->

                <el-table-column
                        prop="bindTel"
                        label="手机号"

                        width="130"
                ></el-table-column>
                <el-table-column
                        prop="name"
                        label="名称"

                        width="130"
                ></el-table-column>
                <el-table-column

                        prop="account"
                        label="会员账号"
                        width="130"
                ></el-table-column>
<!--                <el-table-column-->
<!--                        prop="workRemark"-->
<!--                        label="工作记录"-->
<!--                        width="150"-->
<!--                >-->
<!--                </el-table-column>-->

                <el-table-column

                        prop="sex"
                        label="性别"
                        width="130"
                >
                    <template slot-scope="scope">
                        <span v-if="scope.row.sex==1">{{scope.row.orderState}}男</span>
                        <span v-if="scope.row.sex==2">{{scope.row.orderState}}女</span>
                        <span v-if="scope.row.sex==3">{{scope.row.orderState}}未知</span>
						<span v-if="scope.row.sex==0">{{scope.row.orderState}}未知</span>
                    </template>
                </el-table-column>
                <el-table-column

                        prop="remark"
                        label="备注"
                        :show-overflow-tooltip="true"
                ></el-table-column>


                <el-table-column
                        label="操作"
                        fixed="right"
                        min-width="20">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="edit(scope.row.id)" type="primary">日志</el-button>
<!--                        <el-button size="mini" @click="doSet(scope.row.id)" type="primary">结算</el-button>-->
                   </template>

                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>
        <el-dialog :visible.sync="addModal" class="Modal" width="80%"   title="添加客户" >
            <el-form ref="form" :model="dom" >
                <Row>

                    <Col span="12">
                        <el-form-item   prop="BindTel">
                            <div class="label-name">手机号:</div>
                            <el-input placeholder="请输入" v-model="dom.bindTel" style="width: 70%">

                            </el-input>
                        </el-form-item >
                        <el-form-item label="性别">
                            <el-radio-group v-model="dom.sex">
                                <el-radio label="1">未知</el-radio>
                                <el-radio label="2">男</el-radio>
                                <el-radio label="3">女</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="获客渠道">
                            <el-select v-model="dom.source"  placeholder="请选择">
                                <el-option
                                        v-for="item in options"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </Col>
                    <Col span="12">
                        <el-form-item   prop="Name">
                            <div class="label-name">姓名:</div>
                            <el-input placeholder="姓名" v-model="dom.name" style="width: 70%">
                            </el-input>
                        </el-form-item >
                        <el-form-item   prop="remark">
                            <div class="label-name">备注:</div>
                            <el-input placeholder="备注" v-model="dom.remark" style="width: 70%">
                            </el-input>
                        </el-form-item >


                    </Col>


                </Row>

                <div style="margin-right: 100px;    text-align: right;">
                    <Button type="primary" @click="save('dom')">确定</Button>
                    <Button @click="addModal=false" style="margin-left: 15px">取消</Button>
                </div>
            </el-form>
        </el-dialog>



    </div>
</template>

<script>

    import {formatDate} from '@/components/common/utils.js'
    export default {
        data() {
            return {
                options:[
                {label:'小区门店',
                    value:1
                },{label:'框架广告',
                    value:2
                },{label:'短信',
                    value:3
                },{label:'DM单',
                    value:4
                },{label:'其他',
                    value:5
                },{label:'邀请活动',
                    value:6
                },{label:'第三方糯米',
                    value:7
                },{label:'第三方淘宝',
                    value:8
                },{label:'第三方大众',
                    value:9
                },{label:'第三方美团',
                    value:10
                },{label:'第三方京东到家',
                    value:11
                },{label:'第三方葡萄社区',
                    value:12
                },{label:'第三方到位',
                    value:13
                },{label:'第三方蓝店',
                    value:14
                },{label:'民生银行',
                    value:15
                },{label:'招商银行',
                    value:16
                },{label:'建发汽车',
                    value:17
                },{label:'永辉生活',
                    value:18
                },{label:'一搬小程序',
                    value:19
                },{label:'小羽佳U小程序',
                    value:20
                },{label:'小羽佳小程序',
                    value:21
                },{label:'汽车广播',
                    value:50
                },{label:'公交站牌',
                    value:51
                },{label:'微信公众号',
                    value:52
                },{label:'微信朋友圈',
                    value:53
                },{label:'小羽佳服务员工介绍',
                    value:54
                },{label:'好朋友介绍',
                    value:55
                },{label:'搬家车厢广告',
                    value:56
                },{label:'百度搜索',
                    value:57
                },{label:'官网',
                    value:58
                },{label:'抖音',
                    value:59
                },{label:'其它',
                    value:60
                }
                ],
                dom:{
                    source:60,
                    bindTel:null,
                    name:null,
                    sex:'1',
                    remark:null,
                },
                doid:null,
                doModal:false,
                tableData: [],
                pageSizeOpts:[10,20,30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                screenWidth: '80%',//新增对话框 宽度
                doscreenWidth:'50%',
                saveModal: false,
                addModal:false,
                show:{},
                model: {
                    name:null,
                    bindTel:null,
                    current: 1,
                    size: 10
                },
                update:{
                    id:null,
                    realName:null,

                },
                list:null
            };
        },
        created() {
            this.getData();
        },
        computed: {
        },
        methods: {
            save(){
                if (this.dom.bindTel==null){
                    return  this.$message.error("手机号不能为空，" );
                }
                this.$postData("insertMember", this.dom, {}).then(res => {
                    if (res.status == 200) {
                        this.dom.BindTel=null
                        this.dom.Name=null
                        this.addModal=false
                        this.getData();
                    } else {
                        this.$message.error("保存失败，" + res.msg);
                    }
                })
            },
            sortChange: function(column, prop, order) {

                if (column == null) {
                    this.model.orderBy = "";
                } else {
                    if (column.order == "ascending") {
                        this.model.orderBy = column.prop + " ASC";
                    }
                    if (column.order == "descending") {
                        this.model.orderBy = column.prop + " DESC";
                    }
                }
                this.getData();
            },
            getData() {
                this.$postData("memberPage", this.model, {}).then(res => {
                    if (res.status == 200) {

                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            query() {
                this.getData();
            },
            re(){
                this.model.name=null,
                this.model.bindTel=null
                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.model.size = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                console.log(index)
                this.model.current = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
                this.updateModal = false;
                this.addModal=false;
                this.doModal=false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },
            edit(id){
                // this.show=id;
                // console.log(this.model)
                // this.saveModal=true;
                let url=window.location.href
                window.open(url.substring(0,url.length-11)+'memberInfo?id='+id)
            },
            doSet(id){
                this.doid=id;
                this.doModal=true;
            }
        }
    };
</script>

<style scoped>

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
