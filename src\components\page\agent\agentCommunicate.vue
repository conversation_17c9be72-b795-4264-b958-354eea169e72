<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="经理名">
                                <el-input
                                        v-model="model.agentName"
                                        placeholder="经理名"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="客户名">
                                <el-input
                                        v-model="model.memberName"
                                        placeholder="客户名"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="保姆名">
                                <el-input
                                        v-model="model.baomuName"
                                        placeholder="保姆名"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8" >

                            <el-form-item>
                                <el-button type="success"  round @click="query()">搜索</el-button>
                                <el-button type="info"  round @click="re()">重置</el-button>
<!--                                <el-button type="success" round  @click="addModal=true">添加</el-button>-->
                            </el-form-item>

                        </el-col>
                    </el-row>

                </el-form>
            </div>
            <el-table :data="list" :header-cell-style="{background:'#ddd'}" @sort-change="sortChange" border class="table" ref="multipleTable" @cell-dblclick="edit">
                <el-table-column
                        type="selection"
                        width="35">
                </el-table-column>
                <el-table-column
                        fixed="left"
                        prop="memberName"
                        label="客户名称"
                        width="120"
                        sortable="custom"
                ></el-table-column>
                <el-table-column
                        prop="agentName"
                        label="经理名称"
                        width="120"
                        sortable="custom"
                ></el-table-column>
                <el-table-column
                        prop="baomuName"
                        label="保姆名称"
                        width="120"
                        sortable="custom"
                ></el-table-column>
                <el-table-column
                        prop="communicateTime"
                        label="咨询时间"
                        sortable="custom"
                        width="180"
                ></el-table-column>

                <el-table-column
                        prop="content"
                        label="咨询内容"
                        sortable="custom"
                        show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                        prop="returnId"
                        label="回复状态"
                        show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.returnContent!=null" type="success">已回复</el-tag>
                        <el-tag v-else-if="scope.row.returnContent==null" type="warning">未回复</el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="remarks"
                        label="备注"
                        sortable="custom"
                        show-overflow-tooltip
                ></el-table-column>




                <el-table-column
                        label="操作"
                        min-width="180">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="edit(scope.row)" type="primary">编辑</el-button>
                        <el-button size="mini" @click="returnCon(scope.row)" type="primary" v-if="scope.row.returnContent==null">回复</el-button>
                        <el-button size="mini" @click="returnCon(scope.row)" type="info" v-if="scope.row.returnContent!=null">查看回复</el-button>
                   </template>

                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>


        <el-dialog title="回复留言" :visible.sync="dialogFormVisible">
            <el-form ref="form" :model="dialogForm" >
                <Row>
                    <Col span="12">
                        <el-form-item   prop="memberName">
                            <div class="label-name">客户名:</div>
                            <el-input placeholder="请输入客户名" v-model="dialogForm.memberName"  :disabled="true" style="width: 70%">

                            </el-input>
                        </el-form-item >
                        <el-form-item   prop="communicateTime">
                            <div class="label-name">咨询时间:</div>
                            <el-input placeholder="请输入咨询时间" v-model="dialogForm.communicateTime"  :disabled="true" style="width: 70%">
                            </el-input>
                        </el-form-item >
                    </Col>
                    <Col span="12">
                        <el-form-item   prop="baomuName">
                            <div class="label-name">保姆名:</div>
                            <el-input placeholder="请输入保姆名" v-model="dialogForm.baomuName"  :disabled="true" style="width: 70%">

                            </el-input>
                        </el-form-item >


                        <el-form-item   prop="communicateTime" >
                            <div class="label-name">留言内容:</div>
                            <el-input
                                    style="width: 70%"
                                    :disabled="true"
                                    type="textarea"
                                    :autosize="{ minRows: 1}"
                                    placeholder="请输入内容"
                                    v-model="dialogForm.content">

                            </el-input>
                        </el-form-item>
                    </Col>
                    <Col span="24">


                    </Col>

                </Row>

            </el-form><hr>
            <el-form>
                <el-form-item label="回复内容">
                    <el-input
                            type="textarea"
                            :disabled="isReturn"
                            :autosize="{ minRows: 2}"
                            placeholder="请输入内容"
                            v-model="returnContent">
                    </el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取 消</el-button>
                <el-button type="primary" @click="doReturn" v-if="!isReturn">回 复</el-button>
            </div>
        </el-dialog>

        <Modal v-model="saveModal" class="Modal" :width="screenWidth"  title="添加" :mask-closable="false" >
            <div class="addBody">

                <CommunicateUpdate v-if="saveModal" @init-choose="initChooseProject"
                                @close-modal="closeCurrModal" :model="show"></CommunicateUpdate>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="addModal" class="Modal" :width="screenWidth"   title="添加" :mask-closable="false" >
            <div class="addBody" style="min-height:400px">
                <communicateAdd v-if="addModal" @init-choose="initChooseProject"
                         @close-modal="closeCurrModal" ></communicateAdd>
            </div>
            <div slot="footer">
            </div>
        </Modal>

    </div>
</template>

<script>
    import communicateAdd from '@/components/page/agent/choose/communicateAdd.vue'
    import CommunicateUpdate from '@/components/page/agent/choose/CommunicateUpdate.vue'
    import {formatDate} from '@/components/common/utils.js'
    export default {
        data() {
            return {
                isReturn:false,
                returnContent:null,
                dialogFormVisible:false,
                dialogForm:{
                    memberName:null,
                    baomuName:null,
                    communicateTime:null,
                    content:null,
                },
                tableData: [],
                pageSizeOpts:[10,20,30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                screenWidth: '80%',//新增对话框 宽度
                saveModal: false,
                addModal:false,
                show:{},
                model: {
                    memberId:null,
                    agentName: null,
                    memberName:null,
                    baomuName:null,
                    current: 1,
                    size: 10
                },
                update:{
                    id:null,
                    realName:null,

                },
                list:null
            };
        },
        components: {
            'communicateAdd': communicateAdd,
            'CommunicateUpdate': CommunicateUpdate,
        },
        created() {
            this.getData();
        },
        computed: {
        },
        methods: {
            returnCon(dom){
                this.isReturn=false
                this.returnContent=null
                if (dom.returnContent!=null){
                    this.isReturn=true
                    this.returnContent=dom.returnContent
                }
                this.dialogForm=dom
                this.dialogForm.returnId= this.dialogForm.id;
                this.dialogForm.id=null;
                this.dialogForm.type=15;
                this.dialogFormVisible=true

            },
            doReturn(){
                if(this.returnContent==null){
                   return  this.$message.error("请先添加回复内容!");
                }
                this.dialogForm.content=this.returnContent
                this.$postData("add_communicate", this.dialogForm, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.error("回复成功，" + res.msg);
                        this.getData()
                        this.dialogFormVisible=false
                    } else {
                        this.$message.error("回复失败，" + res.msg);
                    }
                })
            },
            sortChange: function(column, prop, order) {

                if (column == null) {
                    this.model.orderBy = "";
                } else {
                    if (column.order == "ascending") {
                        this.model.orderBy = column.prop + " ASC";
                    }
                    if (column.order == "descending") {
                        this.model.orderBy = column.prop + " DESC";
                    }
                }
                this.getData();
            },
            getData() {
                this.$postData("communicate_page", this.model, {}).then(res => {
                    if (res.status == 200) {

                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            query() {
                this.getData();
            },
            re(){

                this.model.baomuName=null,
                this.model.agentName=null,
                this.model.memberName=null
                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.model.size = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                console.log(index)
                this.model.current = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
                this.updateModal = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },
            edit(id){
                this.show=id;
                console.log(this.model)
                this.saveModal=true;
            }
        }
    };
</script>

<style scoped>
    .label-name{
        float: left;
        text-align: center;
        width: 20%;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
