<template>
    <div>
        <el-table
                :data="rateList"
                style="width: 100%">
            <el-table-column
                    type="selection"
                    width="35">
            </el-table-column>
            <el-table-column
                    prop="remark"
                    show-overflow-tooltip
                    label="评价内容">
            </el-table-column>
            <el-table-column
                    prop="goodtag"
                    show-overflow-tooltip
                    label="好评标签">
            </el-table-column>
            <el-table-column
                    prop="badtag"
                    show-overflow-tooltip
                    label="差评标签">
            </el-table-column>
            <el-table-column
                    prop="state"
                    label="状态"
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status==1" type="success">展示</el-tag>
                    <el-tag v-else-if="scope.row.status==0" type="warning">暂存</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    label="操作"
                    fixed="right"
                    min-width="130"
                   >
                <template slot-scope="scope">
                    <el-button size="mini" @click="del(scope.row.id)" type="info">删除</el-button>
                    <el-button v-if="scope.row.status==1" size="mini" @click="update(scope.row,0)" type="primary">设为暂存</el-button>
                    <el-button v-if="scope.row.status==0" size="mini" @click="update(scope.row,1)" type="info">设为展示</el-button>
                </template>

            </el-table-column>
        </el-table>
    </div>

</template>

<script>
    export default {
        props:['model'],
        name: "baomurate",
        data() {
            return {
                dom:this.model,
                rateList:[],
            }
        },
        created: function () {
            this.getData()
            console.log(this.dom)
        },
        methods: {
            update(dom,state) {
                dom.status=state;
                this.$postData("save_baomuRate", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.getData()
                        this.$Message.success('修改成功');
                    } else {
                        this.$message.error("修改失败，" + res.msg);
                    }
                })

            },
            del(id){
                this.$postUrl("del_baomuRate", id, null, {}).then(res => {
                    if (res.status == 200) {

                        this.getData()
                        this.$message.success("删除成功，" + res.msg);


                    } else {
                        this.$message.error("删除失败，" + res.msg);
                    }
                })
            },
            getData() {
                this.$postData("baomuRate_list", {baomuId:this.dom.id}, {}).then(res => {
                    if (res.status == 200) {

                        this.rateList=res.data;
                        // this.rateList.forEach(v=>{
                        //     v.goodtags=this.StringToArray(v.goodtag,',')
                        // })
                        console.log(this.rateList)
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            /*
       * 关闭当前窗口
       * */
            chooseThisModel(name) {
                this.dom=null;
                this.$emit('init-choose', "");
            },
        }
    }
</script>

<style scoped>

</style>
