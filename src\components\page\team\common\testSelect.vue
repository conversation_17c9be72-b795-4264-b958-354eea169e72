<template>
  <div class="el-transfer">
    <transfer-panel
        v-bind="$props"
        ref="leftPanel"
        :data="sourceData"
        :filterable = "true"
        :title="titles[0] || t('el.transfer.titles.0')"
        :default-checked="leftDefaultChecked"
        :placeholder="filterPlaceholder || t('el.transfer.filterPlaceholder')"
        @checked-change="onSourceCheckedChange">
      <slot name="left-footer"></slot>
    </transfer-panel>
    <transfer-panel
        v-bind="$props"
        ref="rightPanel"
        :data="targetData"
        :title="titles[1] || t('el.transfer.titles.1')"
        :filterable = "true"
        :default-checked="rightDefaultChecked"
        :placeholder="filterPlaceholder || t('el.transfer.filterPlaceholder')"
        @checked-change="onTargetCheckedChange">
      <slot name="right-footer"></slot>
    </transfer-panel>
    <div class="buttonStyle">
      <el-divider></el-divider>
      <el-button type="primary" @click="chooseThisModel">确认选择</el-button>
    </div>
  </div>


</template>

<script>
import ElButton from 'element-ui/packages/button';
import Emitter from 'element-ui/src/mixins/emitter';
import Locale from 'element-ui/src/mixins/locale';
import TransferPanel from 'element-ui/packages/transfer/src/transfer-panel.vue';
import Migrating from 'element-ui/src/mixins/migrating';

export default {
  name: 'testSelect',

  mixins: [Emitter, Locale, Migrating],

  components: {
    TransferPanel,
  },
  created() {
    this.getTaskPage()
    this.getEmployeeList()
  },
  props: {
    data: {
      type: Array,
      default() {
        return [];
      }
    },
    titles: {
      type: Array,
      default() {
        return ["任务列表","人员列表"];
      }
    },
    filterPlaceholder: {
      type: String,
      default: ''
    },
    filterMethod: Function,
    leftDefaultChecked: {
      type: Array,
      default() {
        return [];
      }
    },
    rightDefaultChecked: {
      type: Array,
      default() {
        return [];
      }
    },
    renderContent: Function,
    value: {
      type: Array,
      default() {
        return [];
      }
    },
    format: {
      type: Object,
      default() {
        return {};
      }
    },
    filterable: Boolean,
    props: {
      type: Object,
      default() {
        return {
          label: 'label',
          key: 'key',
          disabled: 'disabled'
        };
      }
    },
    targetOrder: {
      type: String,
      default: 'original'
    }
  },

  data() {
    return {
      leftChecked: [],
      rightChecked: [],
      sourceData:[],
      targetData:[],
      chooseMode: {
        missionList: [],
        employList: []
      },
      searchEmployee:{
        realName:null,
      },
    };
  },

  computed: {
    // dataObj() {
    //   const key = this.props.key;
    //   return this.data.reduce((o, cur) => (o[cur[key]] = cur) && o, {});
    // },

    // sourceData() {
    //   return this.data.filter(item => this.value.indexOf(item[this.props.key]) === -1);
    // },
    //
    // targetData() {
    //   if (this.targetOrder === 'original') {
    //     return this.data.filter(item => this.value.indexOf(item[this.props.key]) > -1);
    //   } else {
    //     return this.value.reduce((arr, cur) => {
    //       const val = this.dataObj[cur];
    //       if (val) {
    //         arr.push(val);
    //       }
    //       return arr;
    //     }, []);
    //   }
    // },

  },

  // watch: {
  //   value(val) {
  //     this.dispatch('ElFormItem', 'el.form.change', val);
  //   }
  // },

  methods: {
    // getMigratingConfig() {
    //   return {
    //     props: {
    //       'footer-format': 'footer-format is renamed to format.'
    //     }
    //   };
    // },
    chooseThisModel() {
      // console.log(this.chooseMode.missionList);
      // console.log(this.chooseMode.employList);
      // console.log("发送信息:"+JSON.stringify(this.chooseMode));
      this.$emit('init-flyBrid', this.chooseMode);
      this.getTaskPage();
      this.getEmployeeList();

    },
    getTaskPage() {
      this.sourceData = [];
      this.$getData("getBadMissionList", null, {}).then(res => {
        if (res.status == 200) {
          let data = res.data;
          data.forEach((i, index) => {
            this.sourceData.push({
              label: i.productName+"("+i.billNo+")" + "/订单账号:("+i.memberAccount+")",
              key: i.id,
            });
          });
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
        this.loading = false;
      })
    },
    getEmployeeList() {
      this.targetData = [];
      this.$postData("getEmployeeList", this.searchEmployee, {}).then(res => {
        if (res.status == 200) {
          let data = res.data;
          this.targetData = [];
          data.forEach((i, index) => {
            this.targetData.push({
              label: i.realName+"/工号:"+i.no,
              key: i.id,
            });
          });
        }
      });
    },
    onSourceCheckedChange(val) {
      console.info("左边点了:" + val);
      if (this.chooseMode.missionList.length != null || this.chooseMode.missionList.length !== 0) {
        this.chooseMode.missionList = [];
        val.forEach(e => {
          this.chooseMode.missionList.push(e);
        })
      } else {
        this.chooseMode.missionList.push(val);
      }
    },

    onTargetCheckedChange(val) {
      console.info("右边点了:" + val);
      if (this.chooseMode.employList.length != null || this.chooseMode.employList.length !== 0) {
        this.chooseMode.employList = [];
        val.forEach(e => {
          this.chooseMode.employList.push(e);
        })
      } else {
        this.chooseMode.employList.push(val);
      }

    },
    clearQuery(which) {
      if (which === 'left') {
        this.$refs.leftPanel.query = '';
      } else if (which === 'right') {
        this.$refs.rightPanel.query = '';
      }
    }

  }
};
</script>
<style scoped>
.el-transfer >>> .el-transfer-panel {
  width: 65%;
}
.el-transfer {
  padding-left: 5%;
}
.buttonStyle{
  text-align: center;
}
</style>