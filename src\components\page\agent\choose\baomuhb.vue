<template>
    <div>
    <div class="hbbody"  ref="imageWrapper">
        <el-image
                id="file"
                class="hbimg"
                :src="dom.headPortrait"
                fit="cover">
        </el-image>
        <div style="width: 60%;float: left">

            <div class="hbtext">
                <img src="../../../../assets/img/hb/信息.png" class="hbicon"/>
                <span class="hbt">{{this.dom.realName}}</span>
            </div>
            <div class="hbtext">
                <img src="../../../../assets/img/hb/简历.png"  class="hbicon"/>
                <span class="hbt">
                {{this.dom.hometown.substring(0,2)+this.dom.hometown.substring(3,5)}}
                {{this.dom.age}}岁
                {{this.baomuinfo.workYear}}年经验
            </span>

            </div>
            <div class="hbtext">
                <img src="../../../../assets/img/hb/一句话.png"  class="hbicon"/>
<!--                <span class="hbt">{{this.baomuinfo2.introduce}}</span>-->
                <span class="hbt"> {{this.dom.baomuWorkType}}</span>
            </div>
            <img src="../../../../assets/img/hb/bg.png"  style="width: 170%;z-index: -1;margin-bottom: -10px" />

        </div>


        <div class="hbe">
<!--            <img src="../../../../assets/img/hb/一句话.png"  class="hbeimg"/>-->
            <vue-qr :text="downloadData.url" :margin="0"  colorLight="#fff"  :logoScale="0.3" :size="100" class="hbeimg"></vue-qr>
            扫一扫查看更多
        </div>




    </div>
        <center>
            <el-button type="primary" style="margin-top: 10px" @click="makeimg">保存海报</el-button>
        </center>
<!--        <img :src="dataURL " alt="" />-->


    </div>

</template>

<script>
    import html2canvas from 'html2canvas'
    import vueQr from 'vue-qr'
    import $ from 'jquery'
    export default {
        props:['model'],
        name: "baomuhb",
        data() {
            return {
                downloadData: {
                    url: 'https://agent.xiaoyujia.com/nannyInfo/'+this.model.id,
                    icon: '../../../../assets/img/hb/一句话.png'
                },
                imgbase:null,
                workType:null,
                baomuinfo:{
                    workYear:null,
                },
                baomuinfo2:{
                    introduce:null,
                },
                dom:this.model,
                dataURL:null,
            }
        },
        components: {
            vueQr
        },
        methods:{
            showhome(str){
                if (str!=null){
                    return str.substring(0,6)
                }
            },
            makeimg(){

                html2canvas(this.$refs.imageWrapper,{
                    useCORS:true,
                    proxy:true,
                    backgroundColor: null     // 解决生成的图片有白边
                }).then((canvas) => {

                    let dataURL = canvas.toDataURL("image/png")
                    this.dataURL = dataURL
                    var a = document.createElement('a');          // 创建一个a节点插入的document
                    var event = new MouseEvent('click')           // 模拟鼠标click点击事件
                    a.download = '保姆海报-'+this.dom.realName                  // 设置a节点的download属性值
                    a.href = this.dataURL;                                 // 将图片的src赋值给a节点的href
                    a.dispatchEvent(event)                        // 触发鼠标点击事件




                })
            },
            getinfo(){
                this.$postUrl("info_baomu", this.dom.id, {}).then(res => {
                    if (res.status == 200) {
                        this.baomuinfo=res.data;
                        console.log(this.baomuinfo)
                    } else {
                        this.$message.error("失败，" + res.msg);
                    }
                })

            },
            getinfo2(){
                this.$postUrl("get_baomuinfo",this.dom.id, {}).then(res => {
                    if (res.status == 200) {
                        this.baomuinfo2=res.data;
                        if (this.baomuinfo2.workType!=null ){
                            this.workType=this.StringToArray(this.baomuinfo2.workType,',')
                        }

                        console.log( this.baomuinfo2)

                    } else {
                        this.$message.error("失败，" + res.msg);
                    }
                })

            },
            chooseThisModel(name) {
                this.dom=null;
                this.$emit('init-choose', "");
            },

            StringToArray(str,substr) {

                var arrTmp = new Array();
                if(substr=="") {
                    arrTmp.push(str);
                    return arrTmp;
                }
                var i=0, j=0, k=str.length;
                while(i<k) {
                    j = str.indexOf(substr,i);
                    if(j!=-1) {
                        if(str.substring(i,j)!="") { arrTmp.push(str.substring(i,j)); }
                        i = j+1;
                    } else {
                        if(str.substring(i,k)!="") { arrTmp.push(str.substring(i,k)); }
                        i = k;
                    }
                }
                return arrTmp;
            },

        },
        created: function () {

            var imgurl=this.dom.headimg
            // if (imgurl!=null){
            //     if (imgurl.substring(0,1)=='e'){
            //         this.dom.headPortrait= "http://xyj-pic.oss-cn-shenzhen.aliyuncs.com/"+this.dom.headimg
            //     }else if (imgurl.substring(0,1)=='h'){
            //         this.dom.headPortrait= this.dom.headimg
            //     }
            // }
            this.getinfo()
            this.getinfo2()
            console.log(this.dom)

            // console.log(this.getBase64Image(this.dom.headPortrait))



        },
    }
</script>

<style scoped>
    .hbeimg{
        width: 100%;
        padding: 10px 15px 0px 15px;
    }
    .hbe{
        width: 40%;
        float: right;
        text-align: center
    }
    .hbimg{
        width: 100%;
        height: 350px;
    }
    .hbicon{
        width: 25px;
        margin: 15px 5px -10px 5px;
    }
.hbbody{
    border: 5px solid #ebeef5;
    min-height: 500px;
    background: #fff;
    padding: 5px;
}
</style>
