<template>
  <div>
    <div class="handle-box">
      <el-form ref="form" :model="pageInfo">
        <el-row>
          <el-col :span="4">
            <el-form-item label="处理人工号">
              <el-input v-model="quer.dealNo" placeholder="工号"></el-input>
            </el-form-item>
          </el-col>
<!--          <el-col :span="4">-->
<!--            <el-form-item label="工单类型">-->
<!--              <el-select v-model="quer.workOrderType">-->
<!--                <el-option label="技术工单" value="1"></el-option>-->
<!--                <el-option label="客服工单" value="2"></el-option>-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="6">
            <el-button type="primary" @click="query()" icon="el-icon-search" >搜索</el-button>
            <el-button type="primary" @click="clear()" icon="el-icon-search" >重置</el-button>
            <el-button type="success"  :loading="loadingExcel" icon="el-icon-download" @click="exportExcel()" >导出</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="创建时间">
              <el-date-picker
                  v-model="date1"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
    </div>
    <el-table :data="list" height="500px" v-loading="loading" border
              :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
      <el-table-column
          prop="month"
          width="200"
          label="工单类型">
        <template slot-scope="scope">
          <span v-if="scope.row.workOrderType == 1">技术工单</span>
          <span v-if="scope.row.workOrderType == 2">客服工单</span>
        </template>
      </el-table-column>
      <el-table-column
          label="处理人">
        <template slot-scope="scope">
          <span> ({{scope.row.dealNo}}){{scope.row.realName}}</span>
        </template>
      </el-table-column>
      <el-table-column
          prop="orderCount"
          label="工单数量">
      </el-table-column>
      <el-table-column
          prop="avgReceiveTime"
          label="平均处理/回复时长">
      </el-table-column>
      <el-table-column
          prop="delayOrderCount"
          label="超时响应工单数（15分钟未回复）">
      </el-table-column>
      <el-table-column
          prop="evaluateCount"
          label="满意工单数">
      </el-table-column>
      <el-table-column
          prop="evaluatePercent"
          label="满意率">
      </el-table-column>
      <el-table-column
          prop="badEvaluateCount"
          label="差评工单数">
      </el-table-column>
      <el-table-column
          prop="badEvaluatePercent"
          label="差评率">
      </el-table-column>

    </el-table>
    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChange"
            :current="this.dto.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="10"/>
    </div>




  </div>
</template>

<script>
export default {
  name: "workOrderReportByCustomer",
  data() {
    return {
      list: null,
      loading: false,
      date1:null,
      loadingExcel:false,
      pageSizeOpts: [10, 20, 40],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      dto: [],
      quer:{
        size: 500,
        current: 1,
        workOrderType:2,
        dealNo:null,
        realName:null,
        startTime:null,
        endTime:null,
      },

    }
  },
  created() {
    // this.getData();
  },
  methods:{
    query(){
      this.getData();
    },
    exportExcel(){
      this.loadingExcel = true;
      this.$postData("downloadWorkOrderReport", this.quer, {
        responseType: "arraybuffer"
      }).then(res => {
        this.loadingExcel = false;
        this.blobExport({
          tablename: "工单报表",
          res: res
        });
      })
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
    clear(){
      this.quer.dealNo = null;
      // this.quer.workOrderType = null;
      this.quer.date1 = null;
    },
    getData() {
      if (this.date1 !=null){
        this.quer.startTime = this.date1[0];
        this.quer.endTime = this.date1[1];
      } else {
        this.quer.startTime = null;
        this.quer.endTime = null;
      }


      if (this.quer.workOrderType == null){
        return  this.$message({
          message: '选择工单类型',
          type: 'warning'
        });
      }
      this.$postData("workOrderReport", this.quer).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      })
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.quer.current = index;
      this.getData();
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.quer.size = size;
      this.getData();
    },

  }
}
</script>

<style scoped>

</style>