<script>
export default {
  name: 'StoreWallet',
  data() {
    return {
      // 门店类型枚举
      StoreType: {
        DIRECT: 'direct',
        FRANCHISE: 'franchise',
        PARTNER: 'partner'
      },

      // 门店类型标签映射
      storeTypeLabels: {
        'direct': '直营店',
        'franchise': '加盟店',
        'partner': '合作店'
      },

      // 响应式数据
      loading: false,
      tableData: [],
      total: 0,

      // 查询表单
      queryForm: {
        storeId: '',
        storeType: '',
        pageNum: 1,
        pageSize: 10
      },

      // 分页配置
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },

      // 门店类型选项
      storeTypeOptions: [
        { label: '全部', value: '' },
        { label: '直营店', value: '1' },
        { label: '加盟店', value: '2' },
        { label: '承包店', value: '5' }
      ],

      // 门店选项
      storeOptions: []
    }
  },

  mounted() {
    this.getAllFranchiseStore()
    this.getStoreWalletList()
  },

  methods: {
    // 获取门店钱包列表
    async getStoreWalletList() {
      this.loading = true
      try {
        // 模拟API调用
        const response = await this.mockApiCall()
        this.tableData = response.data
        this.total = response.total
        this.pagination.total = response.total
      } catch (error) {
        this.$message.error('获取门店钱包列表失败')
        console.error('获取门店钱包列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 模拟API调用
    mockApiCall() {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockData = [
            {
              storeId: 'ST001',
              storeName: '北京朝阳店',
              storeType: this.StoreType.DIRECT,
              balance: 15680.50,
              createTime: '2024-01-15',
              updateTime: '2024-08-20'
            },
            {
              storeId: 'ST002',
              storeName: '上海浦东店',
              storeType: this.StoreType.FRANCHISE,
              balance: 23450.80,
              createTime: '2024-02-10',
              updateTime: '2024-08-19'
            },
            {
              storeId: 'ST003',
              storeName: '广州天河店',
              storeType: this.StoreType.PARTNER,
              balance: 8920.30,
              createTime: '2024-03-05',
              updateTime: '2024-08-18'
            },
            {
              storeId: 'ST004',
              storeName: '深圳南山店',
              storeType: this.StoreType.DIRECT,
              balance: 31200.00,
              createTime: '2024-01-20',
              updateTime: '2024-08-20'
            },
            {
              storeId: 'ST005',
              storeName: '杭州西湖店',
              storeType: this.StoreType.FRANCHISE,
              balance: 12750.60,
              createTime: '2024-04-12',
              updateTime: '2024-08-17'
            }
          ]

          // 根据查询条件过滤数据
          let filteredData = mockData
          if (this.queryForm.storeId) {
            filteredData = filteredData.filter(item =>
              item.storeId === this.queryForm.storeId
            )
          }
          if (this.queryForm.storeType) {
            filteredData = filteredData.filter(item =>
              item.storeType === this.queryForm.storeType
            )
          }

          // 分页处理
          const start = (this.queryForm.pageNum - 1) * this.queryForm.pageSize
          const end = start + this.queryForm.pageSize
          const pageData = filteredData.slice(start, end)

          resolve({
            data: pageData,
            total: filteredData.length
          })
        }, 500)
      })
    },

    // 获取所有门店
    getAllFranchiseStore() {
      this.$getData("getAllFranchiseStore", {type: 5}, {}).then(res => {
        if (res.code == 0) {
          this.storeOptions = res.data
        } else {
          this.$message.error("查询筛选门店失败!")
        }
      })
    },

    // 搜索
    handleSearch() {
      this.queryForm.pageNum = 1
      this.pagination.currentPage = 1
      this.getStoreWalletList()
    },

    // 重置搜索
    handleReset() {
      this.queryForm.storeId = ''
      this.queryForm.storeType = ''
      this.queryForm.pageNum = 1
      this.pagination.currentPage = 1
      this.getStoreWalletList()
    },

    // 分页变化
    handlePageChange(page) {
      this.queryForm.pageNum = page
      this.pagination.currentPage = page
      this.getStoreWalletList()
    },

    // 页面大小变化
    handleSizeChange(size) {
      this.queryForm.pageSize = size
      this.queryForm.pageNum = 1
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.getStoreWalletList()
    },

    // 格式化金额
    formatAmount(amount) {
      return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    },

    // 获取门店类型标签
    getStoreTypeLabel(type) {
      return this.storeTypeLabels[type] || '未知'
    },

    // 获取门店类型标签样式
    getStoreTypeTagType(type) {
      switch (type) {
        case this.StoreType.DIRECT:
          return 'success'
        case this.StoreType.FRANCHISE:
          return 'warning'
        case this.StoreType.PARTNER:
          return 'info'
        default:
          return ''
      }
    },

    // 查看详情
    handleViewDetail(row) {
      this.$message.info(`查看门店 ${row.storeName} 的详细信息`)
      // 这里可以跳转到详情页面或打开详情弹窗
    }
  }
}
</script>

<template>
  <div class="store-wallet-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>门店钱包管理</h2>
      <p class="page-description">管理各门店的钱包余额信息</p>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-container">
      <el-form :model="queryForm" inline class="search-form">
        <el-form-item label="所属门店">
          <el-select
            v-model="queryForm.storeId"
            filterable
            clearable
            placeholder="请选择所属门店"
            style="width: 200px"
          >
            <el-option
              v-for="item in storeOptions"
              :key="item.id"
              :label="item.storeName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="门店类型">
          <el-select
            v-model="queryForm.storeType"
            placeholder="请选择门店类型"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in storeTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <i class="el-icon-search"></i>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        empty-text="暂无数据"
      >
        <el-table-column
          prop="storeId"
          label="门店ID"
          width="120"
          show-overflow-tooltip
        />

        <el-table-column
          prop="storeName"
          label="门店名称"
          min-width="150"
          show-overflow-tooltip
        />

        <el-table-column
          prop="storeType"
          label="门店类型"
          width="120"
          align="center"
        >
          <template slot-scope="{ row }">
            <el-tag
              :type="getStoreTypeTagType(row.storeType)"
              size="small"
            >
              {{ getStoreTypeLabel(row.storeType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="balance"
          label="门店余额"
          width="150"
          align="right"
        >
          <template slot-scope="{ row }">
            <span class="amount-text" :class="{ 'low-balance': row.balance < 10000 }">
              {{ formatAmount(row.balance) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          prop="updateTime"
          label="更新时间"
          width="120"
          align="center"
        />

        <el-table-column
          label="操作"
          width="120"
          align="center"
          fixed="right"
        >
          <template slot-scope="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<style scoped>
.store-wallet-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.amount-text {
  font-weight: 600;
  color: #67c23a;
}

.amount-text.low-balance {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .store-wallet-container {
    padding: 10px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
</style>