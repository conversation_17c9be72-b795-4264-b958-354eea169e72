<script>
export default {
  name: 'StoreWallet',
  data() {
    return {
      // 门店类型枚举
      StoreType: {
        DIRECT: 1,
        FRANCHISE: 2,
        PARTNER: 5
      },

      // 门店类型标签映射
      storeTypeLabels: {
        1: '直营店',
        2: '加盟店',
        5: '承包店'
      },

      // 响应式数据
      loading: false,
      tableData: [],
      total: 0,

      // 查询表单
      queryForm: {
        storeId: '',
        storeType: '',
        ifActivateClue: '',
        ifArrears: '',
        pageNum: 1,
        pageSize: 10
      },

      // 分页配置
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },

      // 门店类型选项
      storeTypeOptions: [
        { label: '全部', value: '' },
        { label: '直营店', value: '1' },
        { label: '加盟店', value: '2' },
        { label: '承包店', value: '5' }
      ],

      arrearsOptions: [
        { label: '全部', value: '' },
        { label: '否', value: '1' },
        { label: '是', value: '2' },
      ],

      activateClueOptions: [
        { label: '全部', value: '' },
        { label: '开启', value: '1' },
        { label: '关闭', value: '2' },
      ],

      // 门店选项
      storeOptions: [],

      // 弹窗控制
      increaseDialogVisible: false,
      deductDialogVisible: false,

      // 当前操作的门店信息
      currentStore: {
        storeId: '',
        storeName: '',
        balance: 0
      },

      // 增加表单
      increaseForm: {
        amount: '',
        type: '',
        changeType: '',
        voucher: '',
        remark: ''
      },

      // 扣减表单
      deductForm: {
        amount: '',
        type: '',
        voucher: '',
        remark: ''
      },

      // 增加类型选项
      increaseTypeOptions: [
        { label: '返还', value: 'refund' },
        { label: '对公充值', value: 'public_recharge' }
      ],

      // 扣减类型选项
      deductTypeOptions: [
        { label: '品牌费', value: 'brand_fee' },
        { label: '线索费', value: 'clue_fee' },
        { label: '合同保险', value: 'contract_insurance' }
      ],

      // 变更类型选项（增加时使用）
      changeTypeOptions: [
        { label: '合同保险', value: 'contract_insurance' },
        { label: '线索费', value: 'clue_fee' },
        { label: '品牌费', value: 'brand_fee' },
        { label: '对公充值', value: 'public_recharge' }
      ],

      // 表单验证规则
      increaseRules: {
        amount: [
          { required: true, message: '请输入增加金额', trigger: 'blur' },
          { pattern: /^[0-9]+(\.[0-9]{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择增加类型', trigger: 'change' }
        ],
        changeType: [
          { required: true, message: '请选择变更类型', trigger: 'change' }
        ],
        voucher: [
          { required: true, message: '请输入凭证信息', trigger: 'blur' }
        ]
      },

      deductRules: {
        amount: [
          { required: true, message: '请输入扣减金额', trigger: 'blur' },
          { pattern: /^[0-9]+(\.[0-9]{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择扣减类型', trigger: 'change' }
        ],
        voucher: [
          { required: true, message: '请输入凭证信息', trigger: 'blur' }
        ]
      }
    }
  },

  computed: {
    // 增加凭证提示
    increaseVoucherPlaceholder() {
      if (this.increaseForm.type === 'refund') {
        return '请输入合同编号'
      } else if (this.increaseForm.changeType === 'clue_fee') {
        return '请输入线索ID'
      } else if (this.increaseForm.changeType === 'contract_insurance') {
        return '请输入合同编号'
      }
      return '请输入凭证信息'
    },

    // 扣减凭证提示
    deductVoucherPlaceholder() {
      if (this.deductForm.type === 'clue_fee') {
        return '请输入线索ID'
      } else if (this.deductForm.type === 'contract_insurance') {
        return '请输入合同编号'
      }
      return '请输入凭证信息'
    }
  },

  mounted() {
    this.getAllFranchiseStore()
    this.getStoreWalletList()
  },

  methods: {
    // 获取门店钱包列表
    async getStoreWalletList() {
      this.loading = true
      try {
        // 构建请求参数
        const params = {
          storeId: this.queryForm.storeId || '',
          storeType: this.queryForm.storeType || '',
          current: this.queryForm.pageNum,
          size: this.queryForm.pageSize,
          ifActivateClue: this.queryForm.ifActivateClue || '',
          ifArrears: this.queryForm.ifArrears || ''
        }

        this.$getData("getStoreWallet", params, {}).then(response => {
          if (response.code === 0) {
            this.tableData = response.data.records
            this.total = response.data.total
            this.pagination.total = response.data.total
            this.pagination.currentPage = response.data.current
          } else {
            this.$message.error(response.msg || '获取门店钱包列表失败')
          }
        })
      } catch (error) {
        this.$message.error('获取门店钱包列表失败')
        console.error('获取门店钱包列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 打开增加弹窗
    increase(row) {
      this.currentStore = {
        storeId: row.storeId,
        storeName: row.storeName,
        balance: row.balance
      }
      this.resetIncreaseForm()
      this.increaseDialogVisible = true
    },

    // 打开扣减弹窗
    deduct(row) {
      this.currentStore = {
        storeId: row.storeId,
        storeName: row.storeName,
        balance: row.balance
      }
      this.resetDeductForm()
      this.deductDialogVisible = true
    },

    // 重置增加表单
    resetIncreaseForm() {
      this.increaseForm = {
        amount: '',
        type: '',
        changeType: '',
        voucher: '',
        remark: ''
      }
      if (this.$refs.increaseForm) {
        this.$refs.increaseForm.clearValidate()
      }
    },

    // 重置扣减表单
    resetDeductForm() {
      this.deductForm = {
        amount: '',
        type: '',
        voucher: '',
        remark: ''
      }
      if (this.$refs.deductForm) {
        this.$refs.deductForm.clearValidate()
      }
    },

    // 提交增加
    submitIncrease() {
      this.$refs.increaseForm.validate((valid) => {
        if (valid) {
          const params = {
            storeId: this.currentStore.storeId,
            amount: parseFloat(this.increaseForm.amount),
            type: this.increaseForm.type,
            changeType: this.increaseForm.changeType,
            voucher: this.increaseForm.voucher,
            remark: this.increaseForm.remark,
            operationType: 'increase'
          }

          // 调用增加接口
          this.processBalanceChange(params, '增加')
        }
      })
    },

    // 提交扣减
    submitDeduct() {
      this.$refs.deductForm.validate((valid) => {
        if (valid) {
          const params = {
            storeId: this.currentStore.storeId,
            amount: parseFloat(this.deductForm.amount),
            type: this.deductForm.type,
            voucher: this.deductForm.voucher,
            remark: this.deductForm.remark,
            operationType: 'deduct'
          }

          // 调用扣减接口
          this.processBalanceChange(params, '扣减')
        }
      })
    },

    // 处理余额变更
    processBalanceChange(params, operationType) {
      this.$message.success(`${operationType}操作提交成功，功能开发中...`)
      // 实际项目中调用真实接口
      // this.$postData("updateStoreBalance", params, {}).then(res => {
      //   if (res.code === 0) {
      //     this.$message.success(`${operationType}操作成功`)
      //     this.closeDialogs()
      //     this.getStoreWalletList() // 刷新列表
      //   } else {
      //     this.$message.error(res.msg || `${operationType}操作失败`)
      //   }
      // }).catch(error => {
      //   this.$message.error(`${operationType}操作失败`)
      //   console.error(`${operationType}操作失败:`, error)
      // })

      // 模拟成功后关闭弹窗
      setTimeout(() => {
        this.closeDialogs()
      }, 1000)
    },

    // 关闭弹窗
    closeDialogs() {
      this.increaseDialogVisible = false
      this.deductDialogVisible = false
      this.resetIncreaseForm()
      this.resetDeductForm()
    },

    // 获取所有门店
    getAllFranchiseStore() {
      this.$getData("getAllFranchiseStore", {type: 5}, {}).then(res => {
        if (res.code == 0) {
          this.storeOptions = res.data
        } else {
          this.$message.error("查询筛选门店失败!")
        }
      })
    },

    // 搜索
    handleSearch() {
      this.queryForm.pageNum = 1
      this.pagination.currentPage = 1
      this.getStoreWalletList()
    },

    // 重置搜索
    handleReset() {
      this.queryForm.storeId = ''
      this.queryForm.storeType = ''
      this.queryForm.pageNum = 1
      this.pagination.currentPage = 1
      this.getStoreWalletList()
    },

    // 分页变化
    handlePageChange(page) {
      this.queryForm.pageNum = page
      this.pagination.currentPage = page
      this.getStoreWalletList()
    },

    // 页面大小变化
    handleSizeChange(size) {
      this.queryForm.pageSize = size
      this.queryForm.pageNum = 1
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.getStoreWalletList()
    },

    // 格式化金额
    formatAmount(amount) {
      return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    },

    // 获取门店类型标签
    getStoreTypeLabel(type) {
      return this.storeTypeLabels[type] || '未知'
    },

    // 获取门店类型标签样式
    getStoreTypeTagType(type) {
      switch (type) {
        case 1: // 直营店
          return 'success'
        case 2: // 加盟店
          return 'warning'
        case 5: // 承包店
          return 'info'
        default:
          return ''
      }
    },

    // 查看详情
    handleViewDetail(row) {
      this.$message.info(`查看门店 ${row.storeName} 的详细信息`)
      // 这里可以跳转到详情页面或打开详情弹窗
    }
  }
}
</script>

<template>
  <div class="store-wallet-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>门店钱包管理</h2>
      <p class="page-description">管理各门店的钱包余额信息</p>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-container">
      <el-form :model="queryForm" inline class="search-form">
        <el-form-item label="所属门店">
          <el-select
            v-model="queryForm.storeId"
            filterable
            clearable
            placeholder="请选择所属门店"
            style="width: 200px"
          >
            <el-option
              v-for="item in storeOptions"
              :key="item.id"
              :label="item.storeName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="门店类型">
          <el-select
            v-model="queryForm.storeType"
            placeholder="请选择门店类型"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in storeTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="是否欠费">
          <el-select
              v-model="queryForm.ifArrears"
              placeholder="请选择门店类型"
              clearable
              style="width: 150px"
          >
            <el-option
                v-for="option in arrearsOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
            />
          </el-select>
        </el-form-item>


        <el-form-item label="收费线索">
          <el-select
              v-model="queryForm.ifActivateClue"
              placeholder="请选择门店类型"
              clearable
              style="width: 150px"
          >
            <el-option
                v-for="option in activateClueOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <i class="el-icon-search"></i>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        empty-text="暂无数据"
      >
        <el-table-column
          prop="storeId"
          label="门店ID"
          width="120"
          show-overflow-tooltip
        />

        <el-table-column
          prop="storeName"
          label="门店名称"
          min-width="150"
          show-overflow-tooltip
        />

        <el-table-column
          prop="storeType"
          label="门店类型"
          width="120"
          align="center"
        >
          <template slot-scope="{ row }">
            <el-tag
              :type="getStoreTypeTagType(row.storeType)"
              size="small"
            >
              {{ getStoreTypeLabel(row.storeType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
            prop="storeType"
            label="接收扣费线索"
            width="120"
            align="center"
        >
          <template slot-scope="{ row }">
            <el-tag
                :type="getStoreTypeTagType(row.state)"
                size="small"
            >
              {{ row.state==0?'未开启':'已开启' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="balance"
          label="门店余额"
          width="150"
          align="right"
        >
          <template slot-scope="{ row }">
            <span class="amount-text" :class="{ 'low-balance': row.balance < 10000 }">
              {{ formatAmount(row.balance) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column fixed="right" min-width="80" label="操作" align="center">
          <template slot-scope="scope">
            <el-button @click="increase(scope.row)" type="info" size="small" icon="el-icon-plus">增加
            </el-button>
            <el-button @click="deduct(scope.row)" type="warning" size="small" icon="el-icon-minus">扣减
            </el-button>
          </template>
        </el-table-column>

      </el-table>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 增加余额弹窗 -->
    <el-dialog
      title="增加门店余额"
      :visible.sync="increaseDialogVisible"
      width="500px"
      @close="closeDialogs"
    >
      <div class="dialog-header">
        <p><strong>门店名称：</strong>{{ currentStore.storeName }}</p>
        <p><strong>当前余额：</strong>
          <span :class="{ 'negative-balance': currentStore.balance < 0 }">
            {{ formatAmount(currentStore.balance) }}
          </span>
        </p>
      </div>

      <el-form
        ref="increaseForm"
        :model="increaseForm"
        :rules="increaseRules"
        label-width="100px"
        class="balance-form"
      >
        <el-form-item label="增加金额" prop="amount">
          <el-input
            v-model="increaseForm.amount"
            placeholder="请输入增加金额"
            type="number"
            step="0.01"
          >
            <template slot="append">元</template>
          </el-input>
        </el-form-item>

        <el-form-item label="增加类型" prop="type">
          <el-select
            v-model="increaseForm.type"
            placeholder="请选择增加类型"
            style="width: 100%"
          >
            <el-option
              v-for="option in increaseTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="变更类型" prop="changeType">
          <el-select
            v-model="increaseForm.changeType"
            placeholder="请选择变更类型"
            style="width: 100%"
          >
            <el-option
              v-for="option in changeTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="凭证" prop="voucher">
          <el-input
            v-model="increaseForm.voucher"
            :placeholder="increaseVoucherPlaceholder"
          />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="increaseForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialogs">取消</el-button>
        <el-button type="primary" @click="submitIncrease">确定增加</el-button>
      </div>
    </el-dialog>

    <!-- 扣减余额弹窗 -->
    <el-dialog
      title="扣减门店余额"
      :visible.sync="deductDialogVisible"
      width="500px"
      @close="closeDialogs"
    >
      <div class="dialog-header">
        <p><strong>门店名称：</strong>{{ currentStore.storeName }}</p>
        <p><strong>当前余额：</strong>
          <span :class="{ 'negative-balance': currentStore.balance < 0 }">
            {{ formatAmount(currentStore.balance) }}
          </span>
        </p>
      </div>

      <el-form
        ref="deductForm"
        :model="deductForm"
        :rules="deductRules"
        label-width="100px"
        class="balance-form"
      >
        <el-form-item label="扣减金额" prop="amount">
          <el-input
            v-model="deductForm.amount"
            placeholder="请输入扣减金额"
            type="number"
            step="0.01"
          >
            <template slot="append">元</template>
          </el-input>
        </el-form-item>

        <el-form-item label="扣减类型" prop="type">
          <el-select
            v-model="deductForm.type"
            placeholder="请选择扣减类型"
            style="width: 100%"
          >
            <el-option
              v-for="option in deductTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="凭证" prop="voucher">
          <el-input
            v-model="deductForm.voucher"
            :placeholder="deductVoucherPlaceholder"
          />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="deductForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialogs">取消</el-button>
        <el-button type="danger" @click="submitDeduct">确定扣减</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.store-wallet-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.amount-text {
  font-weight: 600;
  color: #67c23a;
}

.amount-text.low-balance {
  color: #f56c6c;
}

/* 弹窗样式 */
.dialog-header {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.dialog-header p {
  margin: 5px 0;
  font-size: 14px;
  color: #606266;
}

.negative-balance {
  color: #f56c6c;
  font-weight: 600;
}

.balance-form {
  margin-top: 20px;
}

.balance-form .el-form-item {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .store-wallet-container {
    padding: 10px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .el-dialog {
    width: 90% !important;
  }
}
</style>