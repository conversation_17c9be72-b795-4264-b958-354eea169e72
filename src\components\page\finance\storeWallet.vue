<script>
export default {
  name: 'StoreWallet',
  data() {
    return {
      // 门店类型枚举
      StoreType: {
        DIRECT: 1,
        FRANCHISE: 2,
        PARTNER: 5
      },

      // 门店类型标签映射
      storeTypeLabels: {
        1: '直营店',
        2: '加盟店',
        5: '承包店'
      },

      // 响应式数据
      loading: false,
      tableData: [],
      total: 0,

      // 查询表单
      queryForm: {
        storeId: '',
        storeType: '',
        ifActivateClue: '',
        ifArrears: '',
        pageNum: 1,
        pageSize: 10
      },

      // 分页配置
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },

      // 门店类型选项
      storeTypeOptions: [
        { label: '全部', value: '' },
        { label: '直营店', value: '1' },
        { label: '加盟店', value: '2' },
        { label: '承包店', value: '5' }
      ],

      arrearsOptions: [
        { label: '全部', value: '' },
        { label: '否', value: '1' },
        { label: '是', value: '2' },
      ],

      activateClueOptions: [
        { label: '全部', value: '' },
        { label: '开启', value: '1' },
        { label: '关闭', value: '2' },
      ],

      // 门店选项
      storeOptions: []
    }
  },

  mounted() {
    this.getAllFranchiseStore()
    this.getStoreWalletList()
  },

  methods: {
    // 获取门店钱包列表
    async getStoreWalletList() {
      this.loading = true
      try {
        // 构建请求参数
        const params = {
          storeId: this.queryForm.storeId || '',
          storeType: this.queryForm.storeType || '',
          current: this.queryForm.pageNum,
          size: this.queryForm.pageSize,
          ifActivateClue: this.queryForm.ifActivateClue || '',
          ifArrears: this.queryForm.ifArrears || ''
        }

        this.$getData("getStoreWallet", params, {}).then(response => {
          if (response.code === 0) {
            this.tableData = response.data.records
            this.total = response.data.total
            this.pagination.total = response.data.total
            this.pagination.currentPage = response.data.current
          } else {
            this.$message.error(response.msg || '获取门店钱包列表失败')
          }
        })
      } catch (error) {
        this.$message.error('获取门店钱包列表失败')
        console.error('获取门店钱包列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 获取所有门店
    getAllFranchiseStore() {
      this.$getData("getAllFranchiseStore", {type: 5}, {}).then(res => {
        if (res.code == 0) {
          this.storeOptions = res.data
        } else {
          this.$message.error("查询筛选门店失败!")
        }
      })
    },

    // 搜索
    handleSearch() {
      this.queryForm.pageNum = 1
      this.pagination.currentPage = 1
      this.getStoreWalletList()
    },

    // 重置搜索
    handleReset() {
      this.queryForm.storeId = ''
      this.queryForm.storeType = ''
      this.queryForm.pageNum = 1
      this.pagination.currentPage = 1
      this.getStoreWalletList()
    },

    // 分页变化
    handlePageChange(page) {
      this.queryForm.pageNum = page
      this.pagination.currentPage = page
      this.getStoreWalletList()
    },

    // 页面大小变化
    handleSizeChange(size) {
      this.queryForm.pageSize = size
      this.queryForm.pageNum = 1
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.getStoreWalletList()
    },

    // 格式化金额
    formatAmount(amount) {
      return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    },

    // 获取门店类型标签
    getStoreTypeLabel(type) {
      return this.storeTypeLabels[type] || '未知'
    },

    // 获取门店类型标签样式
    getStoreTypeTagType(type) {
      switch (type) {
        case 1: // 直营店
          return 'success'
        case 2: // 加盟店
          return 'warning'
        case 5: // 承包店
          return 'info'
        default:
          return ''
      }
    },

    // 查看详情
    handleViewDetail(row) {
      this.$message.info(`查看门店 ${row.storeName} 的详细信息`)
      // 这里可以跳转到详情页面或打开详情弹窗
    }
  }
}
</script>

<template>
  <div class="store-wallet-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>门店钱包管理</h2>
      <p class="page-description">管理各门店的钱包余额信息</p>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-container">
      <el-form :model="queryForm" inline class="search-form">
        <el-form-item label="所属门店">
          <el-select
            v-model="queryForm.storeId"
            filterable
            clearable
            placeholder="请选择所属门店"
            style="width: 200px"
          >
            <el-option
              v-for="item in storeOptions"
              :key="item.id"
              :label="item.storeName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="门店类型">
          <el-select
            v-model="queryForm.storeType"
            placeholder="请选择门店类型"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in storeTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="是否欠费">
          <el-select
              v-model="queryForm.ifArrears"
              placeholder="请选择门店类型"
              clearable
              style="width: 150px"
          >
            <el-option
                v-for="option in arrearsOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
            />
          </el-select>
        </el-form-item>


        <el-form-item label="收费线索">
          <el-select
              v-model="queryForm.ifActivateClue"
              placeholder="请选择门店类型"
              clearable
              style="width: 150px"
          >
            <el-option
                v-for="option in activateClueOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <i class="el-icon-search"></i>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        empty-text="暂无数据"
      >
        <el-table-column
          prop="storeId"
          label="门店ID"
          width="120"
          show-overflow-tooltip
        />

        <el-table-column
          prop="storeName"
          label="门店名称"
          min-width="150"
          show-overflow-tooltip
        />

        <el-table-column
          prop="storeType"
          label="门店类型"
          width="120"
          align="center"
        >
          <template slot-scope="{ row }">
            <el-tag
              :type="getStoreTypeTagType(row.storeType)"
              size="small"
            >
              {{ getStoreTypeLabel(row.storeType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
            prop="storeType"
            label="接收扣费线索"
            width="120"
            align="center"
        >
          <template slot-scope="{ row }">
            <el-tag
                :type="getStoreTypeTagType(row.state)"
                size="small"
            >
              {{ row.state==0?'未开启':'已开启' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="balance"
          label="门店余额"
          width="150"
          align="right"
        >
          <template slot-scope="{ row }">
            <span class="amount-text" :class="{ 'low-balance': row.balance < 10000 }">
              {{ formatAmount(row.balance) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column fixed="right" min-width="80" label="操作" align="center">
          <template slot-scope="scope">
            <el-button @click="openModal(3,scope.$index)" type="info" size="small" icon="el-icon-plus">增加
            </el-button>
            <el-button @click="openView(scope.row)" type="warning" size="small" icon="el-icon-minus">扣减
            </el-button>
          </template>
        </el-table-column>

      </el-table>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<style scoped>
.store-wallet-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.amount-text {
  font-weight: 600;
  color: #67c23a;
}

.amount-text.low-balance {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .store-wallet-container {
    padding: 10px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
</style>