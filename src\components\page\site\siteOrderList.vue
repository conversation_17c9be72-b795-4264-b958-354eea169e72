<template>
  <div class="table">
    <div class="container">
      <div class="handle-box">
        <el-form ref="form" :model="pageInfo">
          <el-row>
            <el-col :span="6">
              <el-form-item label="项目名称">
                <el-input
                    clearable
                    v-model="dto.name"
                    placeholder="名称/订单号"
                    style="width:190px"
                    class="handle-input mr10"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="9" style="padding-left: 1rem">
              <el-form-item label="服务时间">
                <el-date-picker
                    v-model="dto.days"
                    type="daterange"
                    :picker-options="pickerOptions"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="是否开票">
                <Select filterable clearable style="width: 120px" v-model="dto.whether"
                        @on-change="query()">
                  <Option value="">请选择</Option>
                  <Option :value="1">是</Option>
                  <Option :value="2">否</Option>
                  <!--<Option v-for="item in stateList" :value="item.id">{{ item.text}}</Option>-->
                </Select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="开票状态">
                <Select filterable clearable style="width: 120px" v-model="dto.invoiceState"
                        @on-change="query()">
                  <Option value="">请选择</Option>
                  <Option :value="0">已作废</Option>
                  <Option :value="1">已开票</Option>
                  <Option :value="2">申请中</Option>
                  <Option :value="3">申请失败</Option>
                  <!--<Option v-for="item in stateList" :value="item.id">{{ item.text}}</Option>-->
                </Select>
                <el-button type="primary"
                           @click="query()"
                           icon="el-icon-search"
                           style="margin-left: 20px">搜索
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row style="margin-top: 10px">
            <el-col :span="24">
              <el-form-item>
                <el-button type="primary" :loading="bindLoading" icon="el-icon-connection"
                           @click="bind">一键绑定
                </el-button>
<!--                <el-button type="warning"-->
<!--                           @click="openInvoice"-->
<!--                           :loading="bindLoading2"-->
<!--                           icon="el-icon-position"-->
<!--                >一键开票-->
<!--                </el-button>-->
                <el-button type="warning" icon="el-icon-upload2" @click="isModal=true">导入开票信息
                </el-button>
                <el-button icon="el-icon-refresh" @click="getData">刷新
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-table :data="list" class="table" ref="multipleTable"
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000',textAlign: 'center'}"
                :cell-style="{textAlign: 'center'}"
                @selection-change="handleSelectionChange"
                v-loading="loading">
        <el-table-column
            type="selection"
            width="50">
        </el-table-column>
        <el-table-column
            width="140"
            prop="siteProject.name"
            label="项目名称">
        </el-table-column>
        <el-table-column
            width="145"
            prop="billNo"
            label="订单号">
        </el-table-column>
        <el-table-column
            width="90"
            prop="order.productName"
            label="服务项目"
        ></el-table-column>
        <el-table-column
            width="150"
            prop="order.startTime"
            label="服务时间"
        ></el-table-column>
        <el-table-column
            width="80"
            prop="order.realTotalAmount"
            label="订单金额"
        ></el-table-column>
        <!--<el-table-column-->
        <!--width="80"-->
        <!--prop="order.orderStateDesc"-->
        <!--label="订单状态"-->
        <!--&gt;</el-table-column>-->
        <el-table-column
            width="100"
            prop="invoiceType"
            label="开票类型">
          <template slot-scope="scope">
            <el-select filterable @change="update(scope.row)"
                       style="width: 90px"
                       v-model="scope.row.invoiceType">
              <el-option :value="1" label="纸质">纸质</el-option>
              <el-option :value="2" label="电子"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
            width="130"
            prop="invoiceType"
            label="开票项目">
          <template slot-scope="scope">
            <el-select filterable clearable @change="update(scope.row)"
                       style="width: 120px"
                       v-model="scope.row.categoryId">
              <el-option v-for="(item,index) in scope.row.categories" :key="index"
                         :value="item.id" :label="item.name"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
            width="100"
            prop="billingAmount"
            label="开票金额">
          <template slot-scope="scope">
            <el-input
                @blur="update(scope.row)"
                style="width: 90px"
                v-model="scope.row.billingAmount"
                placeholder="开票金额"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column
            width="80"
            prop="remark"
            label="发票备注">
        </el-table-column>
        <el-table-column
            width="80"
            prop="invoice.invoiceCode"
            label="发票号码">
        </el-table-column>
        <el-table-column
            width="80"
            prop="deductionAmount"
            label="扣款金额">
        </el-table-column>
        <el-table-column
            width="150"
            prop="deductionReason"
            label="扣款原因">
        </el-table-column>
        <el-table-column
            fixed="right"
            width="100"
            label="操作">
          <template slot-scope="scope">
            <el-row>
              <el-col :span="24">
                <div style="display: flex">
                  <el-button
                      size="small "
                      type="text"
                      @click="goProject(scope.row.projectId)"
                      icon="el-icon-thumb">
                    详情
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="pageInfo.total"
              @on-change="onChange"
              :current="this.dto.pageNum"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onPageSizeChange"
              :page-size="10"/>
      </div>
    </div>
    <Modal v-model="isModal" class="Modal" :width="screenWidth" title="批量导入"
           :mask-closable="false"
           @on-cancel="getData()">
      <div class="addBody">
        <site-invoice-excel v-if="isModal" @init-choose="initChooseProject"
                            @close-modal="closeCurrModal"></site-invoice-excel>
      </div>
      <div slot="footer">
      </div>
    </Modal>
  </div>
</template>
<script>
import siteInvoiceExcel from '@/components/page/site/siteInvoiceExcel.vue'

export default {
  data() {
    return {
      screenWidth: "35%",
      isModal: false,
      pickerOptions: {
        onPick: obj => {
          this.pickerMinDate = new Date(obj.minDate).getTime();
        },
        disabledDate: time => {
          if (this.pickerMinDate) {
            const day1 = 366 * 10 * 24 * 3600 * 1000;
            let maxTime = this.pickerMinDate + day1;
            let minTime = this.pickerMinDate - day1;
            return time.getTime() > maxTime || time.getTime() < minTime;
          }
        }
      },
      loading: true,
      bindLoading: false,
      siteOrders: [],
      bindLoading2: false,
      loadingExcel: false,
      list: null,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      dto: {
        name: null,
        days: [],
        startTime: null,
        endTime: null,
        invoiceState: null,
        pageSize: 10,
        pageNum: 1,
        whether: null
      },
    };
  },
  components: {
    "siteInvoiceExcel": siteInvoiceExcel
  },
  created() {
    this.getData();
  },
  computed: {},
  methods: {
    update(item) {
      this.$postData("siteOrder_update", item).then(res => {
      })
    },
    openInvoice() {
      if (this.siteOrders.length <= 0) {
        this.$message.error("请勾选订单");
        return
      }
      this.bindLoading2 = true;
      this.$postData("siteOrder_openInvoice", {"siteOrders": this.siteOrders}).then(res => {
        this.bindLoading2 = false;
        if (res.status === 200) {
          if (res.data != null && res.data.length > 0) {
            this.$confirm(res.data, '提示', {
              confirmButtonText: '确定',
              type: 'warning'
            }).then(() => {
              this.getData()
            }).catch(() => {
            });
          } else {
            this.$message({
              type: 'success',
              message: '开票成功!'
            });
            this.getData()
          }
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    goProject(id) {
      //this.$router.push({path:'/siteDetails',query:{"id":id,tabName:"first"}})
      let url = window.location.href.split("/")[0];
      window.open(url + "/siteDetails?id=" + id + "&tabName=first")
    },
    bind() {
      this.bindLoading = true;
      this.$postData("siteOrder_getByBindAll").then(res => {
        this.bindLoading = false;
        if (res.status === 200) {
          this.$message({
            type: 'success',
            message: '绑定成功!'
          });
          this.getData()
        }
      });
    },
    handleSelectionChange(val) {
      this.siteOrders = val;
    },
    deleteOrder(id) {
      this.$confirm('此操作将永久删除该订单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$postUrl("siteOrder_delete", id).then(res => {
          this.getData()
        });
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },

    getData() {
      this.loading = true;
      this.dto.startTime = null;
      this.dto.endTime = null;
      if (this.dto.days != null && this.dto.days.length > 0) {
        this.dto.startTime = this.dto.days[0];
        this.dto.endTime = this.dto.days[1]+" 23:59:59"
      }
      this.$postData("siteOrder_selectOrderList", this.dto).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.list = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        }
      });
    },
    edit(id) {
      this.$router.push({path: '/siteDetails', query: {"id": id}})
    },
    /**
     * 关闭当前界面弹出的窗口
     * */
    closeCurrModal(data) {
      this.isModal = false;
    },
    initChooseProject(data) {
      this.closeCurrModal();
      this.getData()
    },
    query() {
      this.loading = true;
      this.dto.pageNum = 1;
      this.getData();
    },

    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.dto.pageSize = size;
      this.getData();
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.dto.pageNum = index;
      this.getData();
    },
    exportExcel() {
      this.loadingExcel = true;
      this.$postData("tsExportList", this.dto, {
        responseType: "arraybuffer"
      }).then(res => {
        ;
        this.loadingExcel = false;
        this.blobExport({
          tablename: "投诉工单",
          res: res
        });
      });
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
  }
};
</script>

<style scoped>
.handle-box {
  /*margin-bottom: 10px;*/
  font-weight: bold !important;
}

.el-form-item__label {
  font-weight: bold !important;
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}

.del-dialog-cnt {
  font-size: 16px;
  text-align: center;
}

.table {
  width: 100%;
  font-size: 13px;

}

.red {
  color: #ff0000;
}

.mr10 {
  margin-right: 10px;
}

.container {
  padding: 20px !important;
  background: #fff;
  border: none !important;
  border-radius: 5px;
}

.el-date-editor .el-range-separator {
  padding: 0 5px;
  line-height: 32px;
  width: 7%;
  color: #303133;
}
</style>
