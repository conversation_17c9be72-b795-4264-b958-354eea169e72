<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">
                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="订单编号">
                                <el-input
                                        clearable
                                        v-model="dto.billNo"
                                        placeholder="订单编号"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="4">
                            <el-form-item label="投诉类型">
                                <Select filterable clearable style="width: 120px" v-model="dto.tsType">
                                    <Option value="">请选择</Option>
                                    <Option v-for="item in typeList" :value="item.id">{{ item.text}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="5">
                            <el-form-item label="培训日期">
                                <el-date-picker
                                        v-model="dto.tsStartTime"
                                        type="datetime"
                                        placeholder="开始时间"
                                        style="width:180px"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" style="text-align: left">
                            <el-form-item label="至">
                                <el-date-picker
                                        v-model="dto.tsEndTime"
                                        type="datetime"
                                        style="width:180px"
                                        placeholder="结束时间"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>

                        <el-col :span="5">
                            <el-form-item label="员工工号">
                                <el-input
                                        clearable
                                        v-model="dto.no"
                                        placeholder="支持工号"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="4">
                            <el-form-item label="考核情况">
                                <Select filterable clearable style="width: 120px" v-model="dto.trainSituation">
                                    <Option value="">请选择</Option>
                                    <Option value="1">合格</Option>
                                    <Option value="2">不合格</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24" style="text-align: left">
                            <el-form-item style="float: right">
                                <el-button type="primary" @click="query()">搜索</el-button>
                                <el-button type="success" :loading="loadingExcel" @click="exportExcel">导出Excel</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-table :data="list" border class="table" ref="multipleTable"
                      v-loading="loading">
                <!--<el-table-column-->
                <!--type="selection"-->
                <!--width="55">-->
                <!--</el-table-column>-->
                <el-table-column
                        prop="billNo"
                        width="140"
                        label="订单编号">
                    <template slot-scope="scope">
                        <a style="text-decoration:underline;"
                           :href="url+scope.row.billNo" target="_Blank">{{scope.row.billNo}}</a>
                    </template>
                </el-table-column>
                <el-table-column
                        width="100"
                        prop="department.name"
                        :show-overflow-tooltip="true"
                        label="员工部门"
                ></el-table-column>
                <el-table-column
                        width="110"
                        prop="employeeNo"
                        label="员工工号">
                    <template slot-scope="scope">
                        <p style="font-size: 12px" v-for="item in scope.row.orderWaiters">
                            <span v-if="item.serviceNo!=null">
                                 {{item.serviceNo}} ({{item.serviceName}})
                            </span>
                            <span v-else-if="item.carCode!=null">
                                 {{item.carCode}} ({{item.serviceName}})
                            </span>
                        </p>
                    </template>
                </el-table-column>
                <el-table-column
                        width="80"
                        prop="typeDesc"
                        label="投诉类型"
                ></el-table-column>
                <el-table-column
                        width="80"
                        prop="scoreDetails.changeScore"
                        label="投诉扣分"
                ></el-table-column>
                <el-table-column
                        width="150"
                        prop="trainTime"
                        label="培训日期">
                    <template slot-scope="scope">
                        <span>{{scope.row.trainTime!=null?scope.row.trainTime.substring(0,11):""}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        width="100"
                        prop="trainPeriod"
                        label="培训课时"
                ></el-table-column>
                <el-table-column
                        width="90"
                        prop="trainResult"
                        label="考核成绩"
                ></el-table-column>

                <el-table-column
                        width="100"
                        prop="trainSituationDesc"
                        label="考核情况"
                ></el-table-column>
                <el-table-column
                        width="100"
                        prop="trainScoreDetails.changeScore"
                        label="培训补分"
                ></el-table-column>
                <el-table-column
                        width="110"
                        prop="trainScoreDetails.reamke"
                        label="备注"
                ></el-table-column>
                <el-table-column
                        width="100"
                        label="操作">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="edit(scope.row)" type="primary">查看</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :current="this.dto.pageNum"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="10"/>
            </div>
        </div>

        <Modal v-model="appeal" class="Modal" :width="screenWidth" title="查看"
               :mask-closable="false"
               @on-cancel="getData()">
            <div class="addBody">
                <ts-order-train v-if="appeal" @init-choose="initChooseProject" :stage="stage"
                                 @close-modal="closeCurrModal"></ts-order-train>
            </div>
            <div slot="footer">
            </div>
        </Modal>
    </div>
</template>
<script>
    import tsOrderTrain from '@/components/page/wordOrder/choose/tsOrderTrain.vue'
    export default {
        data() {
            return {
                loading: true,
                loadingExcel: false,
                stage: null,
                screenWidth: '50%',
                appeal:false,
                list: null,
                url: "https://yun.xiaoyujia.com/Account/TokenLogin?Token=" + localStorage.getItem('token') + "&returnUrl=/order/baseinfo?BillNo=",
                pageSizeOpts: [10, 20, 30],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                dto: {
                    billNo: null,
                    no: null,
                    tsStartTime: null,
                    tsEndTime: null,
                    tsType: null,
                    trainSituation: null,
                    pageSize: 10,
                    pageNum: 1,
                },
                typeList: [],
            };
        },
        components: {
            'tsOrderTrain':tsOrderTrain
        },
        created() {
            this.getData();
            this.getTypeList();
        },
        computed: {},
        methods: {
            getTypeList() {
                this.$postUrl("get_dict", 112, null, {}).then(res => {
                    if (res.status === 200) {
                       ;
                        this.typeList = res.data
                    }
                })
            },
            getData(data) {
                this.$postData("tsTrainOrderList", this.dto, {}).then(res => {
                    this.loading = false;
                    if (res.status === 200) {
                       ;
                        this.list = res.data.list;
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    }
                });
            },
            edit(stage) {
                this.stage = stage;
                this.appeal = true;
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal(data) {
                this.appeal = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },
            query() {
                this.loading = true;
                this.dto.pageNum = 1;
                this.getData();
            },

            // 页码大小
            onPageSizeChange(size) {
                this.loading = true;
                this.dto.pageSize = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                this.loading = true;
                this.dto.pageNum = index;
                this.getData();
            },
            exportExcel() {
                this.loadingExcel=true;
                this.$postData("trainExportList", this.dto, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.loadingExcel=false;
                    this.blobExport({
                        tablename: "回炉工单",
                        res: res
                    });
                });
            },
            blobExport({ tablename, res }) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], { type: "application/vnd.ms-excel" });
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
