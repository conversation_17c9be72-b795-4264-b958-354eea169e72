<template>
	<div class="container">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">录播列表</el-menu-item>
		</el-menu>

		<!-- 搜索栏目 -->
		<div v-if="choiceIndexNow==0">
			<div class="handle-box" style="margin: 20px 20px;">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="录播标题、内容"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="录播类型" style="margin-right: 20px">
								<el-select v-model="searchType" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.typeName"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="房间状态" style="margin-right: 20px">
								<el-select v-model="searchState" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(0,0)">添加
							</el-button>

							<el-button type="danger" @click="openModal(2,0)">当前显示
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">

				<el-table-column prop="id" width="60" label="编号">
				</el-table-column>

				<el-table-column width="140" prop="roomTitle" label="录播标题">
				</el-table-column>

				<el-table-column width="140" prop="roomContent" label="录播内容">
				</el-table-column>

				<el-table-column width="122" prop="roomType" label="录播类型" :render-header="renderHeader">
					<template slot-scope="scope">
						<el-tag v-if="item.id == scope.row.roomType" v-for="(item,index) in typeList" :key="index"
							:type="typeStyleList[index%4].type">{{item.typeName}}</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="150" prop="roomImg" label="录播封面" :render-header="renderHeader">
					<template slot-scope="scope">
						<img :src="scope.row.roomImg||blankImg" style="width: 120px;height: 72px;"
							@click="openImg(scope.row.roomImg||blankImg)">
					</template>
				</el-table-column>

				<el-table-column width="120" prop="roomContent" label="录播链接">
					<template slot-scope="scope">
						<el-button @click="openLiveRoom(scope.row.videoUrl)" type="primary" size="small"
							:disabled="!scope.row.videoUrl" icon="el-icon-zoom-in">预览
						</el-button>
					</template>
				</el-table-column>

				<el-table-column prop="videoDuration" label="录播时长" width="120" sortable>
					<template slot-scope="scope">
						<span>{{formatDuration(scope.row.videoDuration)}}</span>
					</template>
				</el-table-column>

				<el-table-column prop="viewNum" label="观看数" width="120" sortable />
				<el-table-column prop="likeCount" label="点赞数" width="120" sortable />

				<el-table-column width="122" label="关联课程" prop="courseId" :render-header="renderHeader">
					<template slot-scope="scope">
						<span v-if="scope.row.courseId==item.id" v-for="(item,index) in courseList" :key="index">
							{{item.courseTitle}}
						</span>
						<span v-if="!scope.row.courseId">
							暂未设置
						</span>
					</template>
				</el-table-column>

				<el-table-column width="122" label="关联导师" prop="teacherId" :render-header="renderHeader">
					<template slot-scope="scope">
						<span v-if="scope.row.teacherId==item.id" v-for="(item,index) in teacherList" :key="index">
							{{item.realName}}
						</span>
						<span v-if="!scope.row.teacherId">
							暂未设置
						</span>
					</template>
				</el-table-column>

				<el-table-column width="102" label="状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.roomState == 0" type="info">下架</el-tag>
						<el-tag v-if="scope.row.roomState == 1" type="success">上架</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="startTime" label="开始时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.startTime}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="endTime" label="结束时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.endTime}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creator" label="创建人">
					<template slot-scope="scope">
						<span
							v-if="scope.row.createEmployeeName">{{scope.row.createEmployeeName}}({{scope.row.createEmployeeId}})</span>
						<span v-else>{{scope.row.createEmployeeId||''}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="200" label="操作">
					<template slot-scope="scope">
						<el-button @click="openModal(1,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--添加录播-->
		<el-drawer size="60%" :with-header="false" :visible.sync="liveRoomModal" direction="rtl">
			<el-menu :default-active="choiceIndexRoom" class="el-menu-demo" mode="horizontal" @select="handleSelectRoom"
				style="margin: 0 0 30px 0;">
				<el-menu-item index="0">基础信息</el-menu-item>
				<el-menu-item index="1" v-if="detailType==1">录播产品</el-menu-item>
			</el-menu>

			<div style="margin: 0 40px;" v-if="choiceItem">
				<transition name="el-zoom-in-top">
					<div v-show="choiceIndexRoom == '0'">
						<el-row style="width:100%;height: auto;margin: 20px 20px;">
							<h2 class="detail-title">录播信息</h2>
							<el-col :span="12">
								<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
									<el-form-item label="* 录播标题：">
										<el-input v-model="choiceItem.roomTitle" type="textarea"
											class="handle-input mr10" placeholder="请输入录播标题"
											:autosize="{ minRows: 4, maxRows: 10}">
										</el-input>
									</el-form-item>

									<el-form-item label="* 录播内容：">
										<el-input v-model="choiceItem.roomContent" type="textarea"
											class="handle-input mr10" placeholder="请输入录播内容描述"
											:autosize="{ minRows: 4, maxRows: 10}">
										</el-input>
										<el-button type="primary"
											@click="roomContentText=choiceItem.roomContent;openContentText=true" plain
											icon="el-icon-edit">编辑</el-button>
										<el-tooltip class="item" effect="dark" content="支持图文形式（优先展示录播详情图，推荐用详情图代替）"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="* 录播链接：">
										<el-input v-model="choiceItem.videoUrl" type="textarea"
											class="handle-input mr10" placeholder="请输入录播链接"
											:autosize="{ minRows: 4, maxRows: 10}">
										</el-input>
									</el-form-item>

									<el-form-item>
										<el-button @click="openLiveRoom(choiceItem.videoUrl)" type="primary"
											size="small" :disabled="!choiceItem.videoUrl" icon="el-icon-zoom-in">预览
										</el-button>
										<el-upload :action="fileUploadUrl" list-type="picture"
											:before-upload="beforeFileUpload" :on-success="fileUploadSuccess"
											:on-error="fileUploadError" :show-file-list="false" :data="fileUploadData"
											class="upload-demo inline-block">
											<el-button @click="fileUpload(0)" type="warning" size="small"
												style="margin-left: 10px" icon="el-icon-upload2">普通上传</el-button>
										</el-upload>
										<el-button @click="openModal(3,0)" type="info" size="small"
											style="margin-left: 10px" icon="el-icon-upload2">阿里云点播</el-button>
										<el-tooltip class="item" effect="dark"
											content="推荐上传到阿里云点播控制台，再将链接复制到这，以获得更流畅的视频播放体验" placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="录播封面：">
										<el-upload class="upload-demo"
											action="https://biapi.xiaoyujia.com/files/uploadFiles" list-type="picture"
											:on-success="imgUploadSuccess" :show-file-list="false">
											<img :src="choiceItem.roomImg||blankImg" style="width: 150px;height: 150px;"
												@click="openImgUpload(0,0)">
										</el-upload>
									</el-form-item>

									<el-form-item label="录播详情：">
										<el-upload class="upload-demo"
											action="https://biapi.xiaoyujia.com/files/uploadFiles" list-type="picture"
											:on-success="imgUploadSuccess" :show-file-list="false">
											<img :src="choiceItem.roomImgDetail||blankImg"
												style="width: 150px;height: 150px;" @click="openImgUpload(1,0)">
										</el-upload>
									</el-form-item>
								</el-form>
							</el-col>

							<el-col :span="12">
								<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
									<el-form-item label="录播类型：">
										<el-select v-model="choiceItem.roomType" placeholder="请选择"
											style="width: 100px;">
											<el-option v-for="(item,index) in typeList" :key="index"
												:label="item.typeName" :value="item.id"></el-option>
										</el-select>
										<el-tooltip class="item" effect="dark" content="可根据类型进行分类"
											placement="top-start">
											<i class="el-icon-group" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="关联课程：">
										<el-select v-model="choiceItem.courseId" placeholder="请选择" filterable>
											<el-option v-for="(item,index) in courseList" :key="index"
												:label="item.id+'：'+item.courseTitle+'(￥'+item.courseAmount+')'"
												:value="item.id"></el-option>
										</el-select>
										<el-button type="success" icon="el-icon-circle-plus-outline"
											@click="openPage(0)" style="margin-left: 20px">添加
										</el-button>
										<el-tooltip class="item" effect="dark" content="在录播下方显示相关课程，并可在详情页通过点击卡片查看课程详情"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="关联导师：">
										<el-select v-model="choiceItem.teacherId" placeholder="请选择" filterable>
											<el-option v-for="(item,index) in teacherList" :key="index"
												:label="item.id+'：'+item.realName" :value="item.id"></el-option>
										</el-select>
										<el-button type="success" icon="el-icon-circle-plus-outline"
											@click="openPage(1)" style="margin-left: 20px">添加
										</el-button>
										<el-tooltip class="item" effect="dark"
											content="在录播下方显示导师课程报名数，并可在详情页通过点击头像查看导师详情" placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="开始日期：">
										<el-date-picker v-model="choiceItem.startTime" type="datetime"
											value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00']"
											placeholder="请选择考试开始日期" :picker-options="pickerOptions">
										</el-date-picker>
									</el-form-item>

									<el-form-item label="结束日期：">
										<el-date-picker v-model="choiceItem.endTime" type="datetime"
											value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00']"
											placeholder="请选择考试结束日期" :picker-options="pickerOptions">
										</el-date-picker>
									</el-form-item>

									<el-form-item label="房间状态：">
										<el-select v-model="choiceItem.roomState" placeholder="请选择"
											style="width: 100px;">
											<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
												:value="item.value"></el-option>
										</el-select>
										<el-tooltip class="item" effect="dark" content="只有上架且日期范围设置正确，才会显示在家姐课堂首页的直播浮窗中"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="录播时长：" v-if="detailType==1">
										<span>{{formatDuration(choiceItem.videoDuration)}}</span>
										<el-tooltip class="item" effect="dark" content="无需手动填写，在保存录播链接后将自动计算时长"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="主播入口：" v-if="detailType==1">
										<el-tag @click="openEntry(0)">入口二维码</el-tag>
										<el-tooltip class="item" effect="dark" content="由该入口进入后开启主播身份，可以使用发布公告/推荐课程等功能"
											placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="直播状态：" v-if="detailType==1">
										<el-select v-model="choiceItem.liveState" placeholder="请选择"
											style="width: 100px;">
											<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
												:value="item.value"></el-option>
										</el-select>
										<el-tooltip class="item" effect="dark"
											content="直播时自动打开，不直播时需【手动关闭】，关闭后会优先展示配置好的录播视频" placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="推流地址：" v-if="detailType==1">
										<div>
											<span @click="copyDatas(choiceItem.pushUrl)">{{choiceItem.pushUrl}}</span>
										</div>
										<el-button @click="refreshPushUrl()" type="primary" size="small"
											icon="el-icon-zoom-in">刷新
										</el-button>
										<el-tooltip class="item" effect="dark"
											content="链接具有时效性，开始直播之前最好刷新一次，刷新后会直播状态自动变更为【上架】" placement="top-start">
											<i class="el-icon-question" style="margin-left:10px;"></i>
										</el-tooltip>
									</el-form-item>

									<el-form-item label="创建人：" v-if="detailType==1">
										<span
											v-if="choiceItem.createEmployeeName">{{choiceItem.createEmployeeName}}({{choiceItem.createEmployeeId}})</span>
										<span v-else>{{choiceItem.createEmployeeId||''}}</span>
									</el-form-item>

									<el-form-item label="创建时间：" v-if="detailType==1">
										<span>{{choiceItem.createTime}}</span>
									</el-form-item>
								</el-form>
							</el-col>
						</el-row>

						<div style="margin: 0 400px;width: 100%;">
							<el-button @click="liveRoomModal=false" type="primary" size="small">取消
							</el-button>
							<el-button @click="insertLiveRoom()" type="success" size="small" v-if="detailType==0">确定添加
							</el-button>
							<el-button @click="updateLiveRoom(choiceItem)" type="success" size="small"
								v-if="detailType==1">保存
							</el-button>
						</div>
					</div>
				</transition>

				<!-- 展开列-录播产品列表 -->
				<transition name="el-zoom-in-top">
					<div v-show="choiceIndexRoom == '1'">
						<h2 class="detail-title">录播产品列表</h2>
						<el-col :span="24" style="margin: 20px 0 40px 0;">
							<el-button @click="addProduct()" type="success" size="small"
								icon="el-icon-circle-plus-outline">
								添加
							</el-button>
							<el-button @click="isEdit=true" type="success" size="small" :disabled="isEdit"
								v-if="!isEdit" icon="el-icon-edit">修改
							</el-button>
							<el-button @click="updateLiveRoomProductList(liveRoomProductList)" type="primary"
								size="small" v-if="isEdit" icon="el-icon-circle-check">保存
							</el-button>
						</el-col>

						<el-table :data="liveRoomProductList" v-loading="loading" ref="table"
							style="width: 1800px;margin-top: 20px;" border stripe
							:header-cell-style="{background:'#f8f8f9',fontWeight:400,color:'#000000'}">

							<el-table-column width="60">
								<template :slot-scope="scope">
									<i class="el-icon-s-operation cursor-pointer"></i>
								</template>
							</el-table-column>
							<el-table-column prop="id" width="90" label="编号" sortable>
								<template slot-scope="scope">
									<span>{{scope.row.id}}</span>
								</template>
							</el-table-column>

							<el-table-column width="180" prop="productTitle" label="产品名称">
								<template slot-scope="scope">
									<span v-if="!isEdit">{{scope.row.productTitle}}</span>

									<el-input v-model="scope.row.productTitle" style="width: 100%;" type="textarea"
										v-if="isEdit" placeholder="请输入产品名称" :autosize="{ minRows: 4, maxRows: 10}">
									</el-input>
								</template>
							</el-table-column>

							<el-table-column width="122" label="关联课程" prop="courseId">
								<template slot-scope="scope">
									<div v-if="!isEdit">
										<span v-if="scope.row.courseId==item.id" v-for="(item,index) in courseList"
											:key="index">
											{{item.courseTitle}}
										</span>
										<span v-if="!scope.row.courseId">
											暂未设置
										</span>
									</div>

									<el-select v-model="scope.row.courseId" placeholder="请选择" filterable v-if="isEdit">
										<el-option v-for="(item,index) in courseList" :key="index"
											:label="item.id+'：'+item.courseTitle+'(￥'+item.courseAmount+')'"
											:value="item.id"></el-option>
									</el-select>
								</template>
							</el-table-column>

							<el-table-column width="180" prop="productState" label="产品状态">
								<template slot-scope="scope">
									<div v-if="!isEdit">
										<el-tag v-for="(item,index) in stateList" :key="index"
											v-if="scope.row.productState == item.value"
											:type="item.type">{{item.text}}</el-tag>
									</div>

									<el-select v-model="scope.row.productState" placeholder="请选择" style="width: 100px;"
										v-if="isEdit">
										<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
											:value="item.value"></el-option>
									</el-select>
								</template>
							</el-table-column>
						</el-table>

					</div>
				</transition>

			</div>
		</el-drawer>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--视频预览-->
		<el-dialog :visible.sync="videoModal" width="40%" title="视频预览" :mask-closable="false">
			<video style="width: 100%;height: auto;margin: 0 auto;" :controls="true" :enable-progress-gesture="true"
				:initial-time="0" :show-center-play-btn="true" autoplay :src="videoUrl" custom-cache="false">
			</video>
		</el-dialog>

		<!-- 入口二维码 -->
		<el-dialog :visible.sync="entryModal" width="30%" title="主播入口" :mask-closable="false">
			<img :src="entryUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!-- 当前录播 -->
		<el-dialog :visible.sync="liveRoomNowModal" width="30%" title="当前录播房间" :mask-closable="false" v-if="liveRoomNow">
			<div><span style="font-size: 20px;font-weight: bold;">正在生效的录播房间</span></div>
			<div><span>录播编号：{{liveRoomNow.id}}</span></div>
			<div><span>录播标题：{{liveRoomNow.roomTitle}}</span></div>
			<div><span>录播内容：{{liveRoomNow.roomContent||'-'}}</span></div>
			<div>
				<div><span>开始日期：{{liveRoomNow.startTime||'-'}}</span></div>
				<div><span>结束日期：{{liveRoomNow.endTime||'-'}}</span></div>
			</div>
			<div>创建信息：<span
					v-if="liveRoomNow.createEmployeeName">{{liveRoomNow.createEmployeeName}}({{liveRoomNow.createEmployeeId}})</span>
				<span v-if="!liveRoomNow.createEmployeeName">{{liveRoomNow.createEmployeeId}}</span>
				<span>-{{liveRoomNow.createTime||'-'}}创建</span>
			</div>
			<div><span style="color: red;">*将自动选取最新且上架中的录播回放，与小程序直播浮窗中的显示同步</span>
			</div>
			<br>
		</el-dialog>

		<el-dialog title="章节内容详情" :visible.sync="openContentText" width="80%">
			<div style="display: flex">
				<el-button @click="toUpdateContent" type="success" icon="Check">确定
				</el-button>
				<el-button @click="openContentText=false">取消
				</el-button>
			</div>
			<Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig"
				mode="default" />
			<Editor style="height: 500px; overflow-y: hidden;" v-model="roomContentText" :defaultConfig="editorConfig"
				mode="default" @onCreated="onCreated" />
		</el-dialog>
	</div>
</template>

<script>
	import Vue from 'vue';
	import Sortable from "sortablejs";
	import {
		Editor,
		Toolbar
	} from '@wangeditor/editor-for-vue'
	export default {
		name: "liveRoom",
		components: {
			Editor,
			Toolbar
		},
		data() {
			return {
				// 可设置
				// 是否超级管理员（可执行删除等敏感操作）
				isAdmin: false,

				url: '',
				imageUrl: '',
				videoUrl: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				// 文件上传地址
				fileUploadUrl: 'https://api.xiaoyujia.com/system/uploadFile',
				fileUploadData: {
					route: 'liveRoom'
				},
				uploadFileType: 0,

				// baseUrl: 'http://**************:9999/',
				baseUrl: 'https://api.xiaoyujia.com/',
				uploadUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
				deleteTips: "确定删除选中录播吗？该操作无法恢复，请谨慎操作!",
				isEdit: false,
				showEdit: true,
				imgModal: false,
				entryUrl: false,
				videoModal: false,
				entryModal: false,
				liveRoomModal: false,
				liveRoomGroupModal: false,
				groupIdListModal: false,
				liveRoomRecordModal: false,
				keyListModal: false,
				authIdListModal: false,
				liveRoomNowModal: false,
				openContentText: false,
				roomContentText: '',
				detailType: 0,
				editor: null,
				toolbarConfig: {},
				editorConfig: {
					placeholder: "请输入内容...",
					readOnly: false,
					MENU_CONF: {
						//上传参考https://www.wangeditor.com/v5/menu-config.html#%E5%9F%BA%E6%9C%AC%E9%85%8D%E7%BD%AE
						uploadImage: {
							server: `https://api.xiaoyujia.com/system/imageUpload`,
							// 超时时间，默认为 10 秒
							timeout: 30 * 1000, // 5s
							fieldName: "file",
							// 将 meta 拼接到 url 参数中，默认 false
							metaWithUrl: false, // join params to url
							// 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
							meta: {
								watermark: 1,
							},
							// 自定义增加 http  header
							// headers: {
							//   Accept: "text/x-json",
							//   otherKey: "xxx"
							// },
							// 跨域是否传递 cookie ，默认为 false
							withCredentials: true,
							// 自定义增加 http  header
							// headers: {
							//   Accept: "text/x-json",
							//   otherKey: "xxx"
							// },
							maxFileSize: 10 * 1024 * 1024, // 10M
							base64LimitSize: 5 * 1024, // insert base64 format, if file's size less than 5kb
							// 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
							allowedFileTypes: ["image/*"],
							// 最多可上传几个文件，默认为 100
							maxNumberOfFiles: 30,
							onBeforeUpload(file) {
								console.log("onBeforeUpload", file);
								return file; // will upload this file
								// return false // prevent upload
							},
							onProgress(progress) {
								console.log("onProgress", progress);
							},
							onSuccess(file, res) {
								console.log("onSuccess", file, res);
							},
							onFailed(file, res) {
								alert(res.message);
								console.log("onFailed", file, res);
							},
							onError(file, err, res) {
								alert(err.message);
								console.error("onError", file, err, res);
							},
							// 自定义插入图片
							customInsert(res, insertFn) {
								insertFn(res.data, '消息图片', '消息图片')
							},
						},
						uploadVideo: {
							server: "https://api.xiaoyujia.com/system/uploadFile",
							fieldName: "file",
							// 单个文件的最大体积限制，默认为 10M
							maxFileSize: 100 * 1024 * 1024, // 100M
							// 最多可上传几个文件，默认为 5
							maxNumberOfFiles: 5,
							// 选择文件时的类型限制，默认为 ['video/*'] 。如不想限制，则设置为 []
							allowedFileTypes: ["video/*"],
							// 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
							meta: {

							},
							// 将 meta 拼接到 url 参数中，默认 false
							metaWithUrl: false,
							// 自定义增加 http  header
							// headers: {
							//   Accept: "text/x-json",
							//   otherKey: "xxx"
							// },
							// 跨域是否传递 cookie ，默认为 false
							withCredentials: true,
							// 超时时间，默认为 30 秒
							timeout: 30 * 1000, // 15 秒
							// 上传进度的回调函数
							onProgress(progress) {
								console.log("progress", progress);
							},
							// 单个文件上传成功之后
							onSuccess(file, res) {
								console.log(`${file.name} 上传成功`, res);
							},
							// 单个文件上传失败
							onFailed(file, res) {
								console.log(`${file.name} 上传失败`, res);
							},
							// 上传错误，或者触发 timeout 超时
							onError(file, err, res) {
								console.log(`${file.name} 上传出错`, err, res);
							},
							// 自定义插入视频
							customInsert(res, insertFn) {
								insertFn(res.data, '消息视频', '消息视频')
							},
						}
					}
				},

				loading: true,
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				pageSizeOpts: [20, 50, 100],
				list: [],
				courseList: [],
				teacherList: [],
				liveRoomList: [],
				groupList: [],
				liveRoomRecordList: [],
				liveRoomAnswerRecordList: [],
				liveRoomProductList: [],
				activeRows: [],
				keyList: [],
				liveRoomNow: null,
				choiceIndex: '0',
				choiceIndexNow: '0',
				choiceIndexRoom: '0',
				choiceItemIndex: 0,
				choiceItem: null,
				choiceRecordId: 0,
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				signInTips: '',
				searchText: '',
				searchType: null,
				searchState: null,
				searchRequired: null,
				quer: {
					search: "",
					groupIdList: null,
					roomState: null,
					typeState: null,
					orderBy: "t.id ASC",
					current: 1,
					size: 20
				},
				switchStateList: [{
						id: 0,
						typeName: "关闭",
						type: "info"
					},
					{
						id: 1,
						typeName: "开启",
						type: "success"
					}
				],
				typeList: [{
					id: 0,
					typeName: "普通类型"
				}],
				stateList: [{
					value: 0,
					text: "下架",
					type: "info"
				}, {
					value: 1,
					text: "上架",
					type: "success"
				}],
				typeStyleList: [{
						id: 0,
						type: "success"
					},
					{
						id: 1,
						type: "info"
					},
					{
						id: 2,
						type: "warning"
					}, {
						id: 3,
						type: "primary"
					}
				],
				searchTime: [],
				pickerOptions: {
					shortcuts: [{
						text: '昨天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '今天',
						onClick(picker) {
							picker.$emit('pick', new Date());
						}
					}, {
						text: '明天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周前',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周后',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}]
				},
				pickerOptions1: {
					shortcuts: [{
						text: '今天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '昨天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}]
				}
			}
		},
		created() {
			if (this.$route.query.isAdmin) {
				this.isAdmin = true
			}
			this.getData()
		},
		methods: {
			onCreated(editor) {
				this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
			},
			// 初始化拖动排序功能
			initSort() {
				const tbody = this.$refs.table.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
				Sortable.create(tbody, {
					animation: 300,
					onMove: () => {
						this.isEdit = true
						if (!this.activeRows.length) {
							this.activeRows = this.liveRoomProductList
						}
					},
					onEnd: e => {
						if (e.oldIndex !== e.newIndex) { // 根据自己项目需求增添条件限制
							let arr = JSON.parse(JSON.stringify(this.activeRows))
							const oldRow = this.activeRows[e.oldIndex] // 移动的那个元素
							const newRow = this.activeRows[e.newIndex] // 新的元素
							arr.splice(e.newIndex, 0, arr.splice(e.oldIndex, 1)[0]);
							this.activeRows = arr
						}
					}
				})
			},
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceItem = null
				this.choiceIndex = key
				this.expands = []
				this.showEdit = true
				this.reset()
			},
			// 切换栏目
			handleSelectRoom(key, keyPath) {
				this.choiceIndexRoom = key
				if (this.choiceIndexRoom == '1') {
					this.liveRoomProductList = []
					this.listLiveRoomProduct(this.choiceItem.id)
				}
			},
			copyDatas(data) {
				let url = data;
				let oInput = document.createElement('input');
				oInput.value = url;
				document.body.appendChild(oInput);
				oInput.select(); // 选择对象;
				// console.log(oInput.value);
				document.execCommand("Copy"); // 执行浏览器复制命令
				this.$message({
					message: '链接复制成功',
					type: 'success'
				});
				oInput.remove();
			},
			getData() {
				this.choiceIndexNow = 2
				if (this.choiceIndex == 0) {
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'course/pageLiveRoom',
						data: JSON.stringify(this.quer),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							this.loading = false
							if (res.code == 0) {
								this.list = res.data.records
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
								this.choiceIndexNow = this.choiceIndex
							} else {
								this.list = []
								this.$message.error('未查询到相关录播!')
								this.choiceIndexNow = this.choiceIndex
							}
						},
					})
					this.listCourse()
					this.listCourseTeacher()
				} else if (this.choiceIndex == 1) {
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'course/pageLiveRoomGroup',
						data: JSON.stringify(this.quer),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							this.loading = false
							if (res.code == 0) {
								this.list = res.data.records
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
								this.choiceIndexNow = this.choiceIndex
							} else {
								this.list = []
								this.$message.error('未查询到相关分组!')
								this.choiceIndexNow = this.choiceIndex
							}
						},
					})
				}
				this.getDict()
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.quer.roomState = this.searchState
				this.quer.groupState = this.searchState
				this.quer.roomType = this.searchType
				this.quer.required = this.searchRequired
				this.getData()
			},
			// 重置
			reset() {
				this.loading = true
				this.quer.search = ""
				this.quer.roomState = null
				this.quer.roomType = null
				this.quer.groupState = null
				this.quer.required = null

				this.quer.orderBy = "t.id ASC"
				this.quer.current = 1

				this.searchText = ''
				this.searchState = null
				this.searchType = null
				this.searchRequired = null
				this.isEdit = false
				this.getData()
			},
			// 获取课程列表
			listCourse() {
				this.$postData("listCourse", {}).then(res => {
					if (res.status == 200) {
						this.courseList = res.data
					}
				})
			},
			// 获取导师列表
			listCourseTeacher() {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'course/listCourseTeacher',
					data: JSON.stringify({
						state: null
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.teacherList = res.data
						}
					},
				})
			},
			getDict() {
				this.$postUrl("get_dict", 250, null, {}).then(res => {
					if (res.status == 200) {
						this.keyList = res.data
					} else {
						this.keyList = []
					}
				})
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
						this.choiceRecordId = row.id
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "分组图片") {
					tips = "推荐尺寸：250*250"
				} else if (column.label == "录播类型") {
					tips = "可根据类型进行分类"
				} else if (column.label == "录播封面") {
					tips = "推荐尺寸：1500*900（5:3）作为首页浮窗展示"
				} else if (column.label == "录播详情") {
					tips = "作为详情页展示"
				} else if (column.label == "关联课程") {
					tips = "在录播右下角浮窗展示，重点售卖"
				} else if (column.label == "关联导师") {
					tips = "在录播详情页中展示"
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			// 将字符串转化为数组
			strToArray(val) {
				let arrayString = []
				let array = []
				arrayString = val.split(',')
				arrayString.forEach(it => {
					let value = parseInt(it)
					if (value != null && value != 0) {
						array.push(value)
					}
				})
				return array
			},
			formatDuration(duration, defaultStr) {
				if (!defaultStr) {
					defaultStr = '暂未录入'
				}
				let result = ""
				let sencond = 0
				let min = 0
				let hour = 0
				let day = 0
				// 小于一分钟
				if (duration <= 60) {
					sencond = Math.floor(parseFloat(duration / 1.0))
					if (sencond == 0) {
						result = defaultStr
					} else {
						result = sencond + "秒"
					}
				}

				// 小于二小时
				if (duration > 60 && duration <= 3600) {
					min = Math.floor(parseFloat(duration / 60.0))
					sencond = Math.floor(duration - (min * 60)).toFixed(0)
					if (min == 0) {
						result = defaultStr
					} else {
						result = min + "分钟" + sencond + "秒"
					}
				}
				// 大于二小时 小于一天
				if (duration > 3600 && duration <= 86400) {
					hour = Math.floor(parseFloat(duration / 3600))
					min = parseFloat((duration - (hour * 3600)) / 60).toFixed(0)
					if (min == 0) {
						result = hour + "小时"
					} else {
						result = hour + "小时" + min + "分钟"
					}
				}
				// 大于一天
				else if (duration > 86400) {
					day = Math.floor(parseFloat(duration / 86400))
					hour = parseFloat((duration - (day * 86400)) / 3600).toFixed(0)
					if (hour == 0) {
						result = day + "天"
					} else {
						result = day + "天" + hour + "小时"
					}
				}
				return result
			},
			formatTypeStyle(type) {
				return this.typeStyleList[type % 4].type
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 录播Excel模板下载
			excelDownload() {
				this.$postData("excelDownload", null, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('录播Excel模板下载成功!')
					this.blobExport({
						tablename: "录播Excel模板",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.$set(this.choiceItem, "roomImg", res.data)
				} else if (this.uploadImgType == 1) {
					this.$set(this.choiceItem, "roomImgDetail", res.data)
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadId = index
				let tips = ''
				if (this.uploadImgType == 0) {
					tips = "推荐尺寸：1500*900（5:3）"
				} else if (this.uploadImgType == 1) {
					tips = '推荐上传尺寸：宽度为500的长图'
				}
				this.$message.info(tips)
			},
			// 打开图片预览
			openImg(url) {
				this.imageUrl = url || this.blankImg
				this.imgModal = true
			},
			// 打开视频预览
			openLiveRoom(url) {
				if (url != null && url != '') {
					this.videoUrl = url
				}
				this.videoModal = true
			},
			// 打开入口
			openEntry(value, index) {
				let id = this.choiceItem.id
				if (value == 0) {
					$.ajax({
						type: "POST",
						url: 'https://biapi.xiaoyujia.com/image/getWxShareImg',
						data: JSON.stringify({
							textList: [{
								"text": this.choiceItem.roomTitle,
								"fontSize": 50,
								"isCenter": true,
								"color": "0xfed472",
								"isBold": true,
								"x": 0,
								"y": 1020
							}],
							qrCodeStyle: {
								"width": 350,
								"height": 350,
								"x": 200,
								"y": 250
							},
							maxWidth: 600,
							img: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/QR_img/QR_examEntry.png',
							path: 'pages-main/liveRoom/index',
							scene: "id/" + id + '*t/1',
							source: "xyjCourse",
							type: 1
						}),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.status == 200) {
								this.entryUrl = res.data
								this.entryModal = true
							} else {
								this.$message.error(res.msg)
							}
						},
						error: res => {
							this.$message.error('当前暂无回放录播哦！')
						}
					})

				}
			},
			// 将字符串转化为数组
			strToArray(val) {
				let arrayString = []
				let array = []
				arrayString = val.split(',')
				arrayString.forEach(it => {
					let value = parseInt(it)
					if (value != null && value != 0) {
						array.push(value)
					}
				})
				return array
			},
			openPage(value) {
				let path = '/courseList'
				if (value == 1) {
					path = '/courseTeacher'
				} else if (value == 2) {
					path = '/survey'
				}
				window.open(path, '_blank')
			},
			openModal(index, index1) {
				if (index == 0) {
					this.detailType = 0
					this.choiceItem = {}
					this.choiceIndexRoom = '0'
					this.liveRoomModal = true
				} else if (index == 1) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.listLiveRoomProduct(this.list[index1].id)
					this.liveRoomModal = true
				} else if (index == 2) {
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'course/getLiveRoomNow',
						data: JSON.stringify({
							source: 'xyjCourse'
						}),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.code == 0) {
								this.liveRoomNow = res.data
								this.liveRoomNowModal = true
							} else {
								this.$message.error('当前暂无回放录播哦！')
							}
						},
						error: res => {
							this.$message.error('当前暂无回放录播哦！')
						}
					})
				} else if (index == 3) {
					window.open(
						'https://vod.console.aliyun.com/?spm=5176.12672711.categories-n-products.dvod.8f791fa37JVfKI#/media/liveRoom/list'
					)
				}
			},
			//打开员工信息详情
			rowClick(id) {

			},
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			// 添加录播
			insertLiveRoom() {
				let liveRoom = this.choiceItem
				if (!liveRoom.roomTitle) {
					this.$message.error('请填写录播标题！')
				} else if (!liveRoom.roomContent) {
					this.$message.error('请填写录播内容！')
				} else if (!liveRoom.videoUrl) {
					this.$message.error('请填写录播链接！')
				} else {
					let no = localStorage.getItem('id') || '1'
					this.$set(liveRoom, "createEmployeeId", no)

					$.ajax({
						type: "POST",
						url: this.baseUrl + 'course/insertLiveRoom',
						data: JSON.stringify(liveRoom),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.code == 0) {
								this.$message.success('录播添加成功!')
								this.liveRoomModal = false
								this.list.push(res.data)
							} else {
								this.$message.error('录播添加失败！' + res.msg)
							}
						},
					})
				}
			},
			refreshPushUrl() {
				$.ajax({
					type: "GET",
					url: this.baseUrl + 'course/updateLiveRoomUrl/' + this.choiceItem.id,
					success: res => {
						if (res.code == 0) {
							this.$message.success('直播链接更新成功！开始直播吧！')
							this.getLiveRoomById()
						} else {
							this.$message.error(res.msg)
						}
					},
					error: res => {
						this.$message.error(res.msg)
					}
				})
			},
			getLiveRoomById() {
				$.ajax({
					type: "GET",
					url: this.baseUrl + 'course/getLiveRoomById/' + this.choiceItem.id,
					success: res => {
						if (res.code == 0) {
							this.choiceItem = res.data
						}
					},
				})
			},
			// 更改录播
			updateLiveRoom(val) {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'course/updateLiveRoom',
					data: JSON.stringify(val),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.$message.success('录播更新成功!')
							this.list[this.choiceItemIndex] = this.choiceItem
						} else {
							this.$message.error('录播更新失败！' + res.msg)
						}
					},
				})
			},
			listLiveRoomProduct(id) {
				this.liveRoomProductList = []
				this.activeRows = []
				$.ajax({
					type: "POST",
					url: this.baseUrl + "course/listLiveRoomProduct",
					data: JSON.stringify({
						roomId: id
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.liveRoomProductList = res.data
							this.initSort()
						} else {
							this.liveRoomProductList = []
							this.activeRows = []
						}
					},
				})
			},
			updateLiveRoomProductList() {
				let val = this.liveRoomProductList
				if (this.activeRows.length) {
					let count = 1
					this.activeRows.forEach(item => {
						item.sort = count
						count++
					})
					val = this.activeRows
				}
				$.ajax({
					type: "POST",
					url: this.baseUrl + "course/updateLiveRoomProductList",
					data: JSON.stringify(val),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.$message.success('章节分组更新成功!')
							this.listLiveRoomProduct(this.choiceItem.id)
							this.isEdit = false
						}
					},
				})
			},
			addProduct() {
				let data = {
					id: null,
					roomId: this.choiceItem.id,
					productTitle: '',
					productIntroduce: '',
					courseId: '',
					sort: 0,
					crePerson: localStorage.getItem('no') || 'admin'
				}
				this.isEdit = true
				this.liveRoomProductList.push(data)
			},
			beforeFileUpload() {
				this.loading = true
			},
			// 章节素材文件上传成功
			fileUploadSuccess(res, file) {
				this.loading = false
				if (this.uploadFileType == 0) {
					this.choiceItem.videoUrl = res.data
				}
				this.$message.success('录播文件上传成功！')
			},
			// 章节素材文件上传失败
			fileUploadError(err) {
				this.loading = false
				this.$message.error('录播文件上传失败！请重试！' + err.msg)
			},
			// 课程章节素材文件上传
			fileUpload(value) {
				this.uploadFileType = value
				this.fileUploadUrl = fileUploadUrl
			},
			beforeImgUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!');
				}
				return isLt2M;
			},
			toUpdateContent() {
				if (this.roomContentText == '<p><br></p>') {
					this.roomContentText = ''
				}
				this.$set(this.list[this.choiceItemIndex],
					'roomContent', this.roomContentText || '')
				this.openContentText = false
			},

		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>

<style src="@wangeditor/editor/dist/css/style.css"></style>