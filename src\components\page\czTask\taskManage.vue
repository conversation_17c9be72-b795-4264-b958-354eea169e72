<template>
	<div class="container" id="pdfDom">

		<!-- 搜索栏目 -->
		<div class="handle-box">
			<el-form ref="form" :model="pageInfo">
				<el-row>
					<el-col :span="6">
						<el-form-item label="任务标题" style="margin-right: 20px">
							<el-input v-model="quer.taskTitle" placeholder="请输入任务标题"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="任务介绍" style="margin-right: 20px">
							<el-input v-model="quer.taskIntroduce" placeholder="请输入任务介绍"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="跳转链接" style="margin-right: 20px">
							<el-input v-model="quer.linkUrl" placeholder="请输入跳转链接"></el-input>
						</el-form-item>
					</el-col>

					<el-col :span="6">
						<el-form-item label="跳转小程序AppID" style="margin-right: 20px">
							<el-input v-model="quer.appid" placeholder="请输入跳转小程序AppID"></el-input>
						</el-form-item>
					</el-col>

					<el-col :span="6">
						<el-form-item label="奖励类型" style="margin-right: 20px">
							<el-select v-model="quer.reward" placeholder="请选择奖励类型" clearable>
								<el-option v-for="item in rewardOptions" :key="item.value" :label="item.label"
									:value="item.value">
								</el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :span="6">
						<el-form-item label="任务类型" style="margin-right: 20px">
							<el-select v-model="quer.taskType" placeholder="请选择任务类型" clearable>
								<el-option v-for="item in options" :key="item.value" :label="item.label"
									:value="item.value">
								</el-option>
							</el-select>
						</el-form-item>
					</el-col>


					<el-col :span="6" style="margin: 32px 0;">
						<el-button type="primary" @click="query()" icon="el-icon-search">搜索
						</el-button>

						<el-button style="margin-left: 10px" type="success" icon="el-icon-circle-plus-outline"
							@click="certModal = true">添加
						</el-button>
					</el-col>
				</el-row>

			</el-form>
		</div>




		<!-- 数据表格 -->
		<el-table :data="list" v-loading="loading" border stripe
			:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
			:expand-row-keys="expands" @expand-change="expandSelect">

			<el-table-column width="140" prop="taskTitle" label="任务标题">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ scope.row.taskTitle }}</span>

					<el-input v-model="scope.row.taskTitle" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
						placeholder="请输入任务标题" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="taskIntroduce" label="任务介绍">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ scope.row.taskIntroduce }}</span>

					<el-input v-model="scope.row.taskIntroduce" style="width: 100%;" type="textarea"
						v-if="scope.row.isEdit" placeholder="请输入任务介绍" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>

			<el-table-column width="140" label="绑定表单">
				<template slot-scope="scope">
					<el-tag type="primary" @click="selectBingDingForm(scope.row)">点击配置</el-tag>
				</template>
			</el-table-column>

			<el-table-column width="140" label="流程演示">
				<template slot-scope="scope">
					<el-tag type="primary" @click="setTaskImgFlow(scope.row)">点击配置</el-tag>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="timeLimit" label="任务时限(小时)">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ scope.row.timeLimit }}</span>

					<el-input v-model="scope.row.timeLimit" style="width: 100%;" type="number" v-if="scope.row.isEdit"
						placeholder="请输入任务时限" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="timeLimit" label="任务提醒">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ scope.row.remind }}</span>

					<el-input v-model="scope.row.remind" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
						placeholder="请输入任务提醒" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="timeLimit" label="提醒类型">
				<template slot-scope="scope">
					<span>{{scope.row.remindType}}</span>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="remindDate" label="提醒日期">
				<template slot-scope="scope">
					<span>{{scope.row.remindDate}}</span>
				</template>
			</el-table-column>

			<el-table-column width="140" prop="reminTime" label="提醒时间">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ scope.row.reminTime }}</span>
					<el-time-select v-if="scope.row.isEdit" class="handle-input mr10" v-model="scope.row.remindDate"
						placeholder="请选择提醒时间" :picker-options="{
                          start: '05:00',
                          step: '00:15',
                          end: '23:00'
                          }">
					</el-time-select>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="taskType" label="任务类型">
				<template slot-scope="scope">
					<span
						v-if="!scope.row.isEdit">{{scope.row.taskType==1?'链接':scope.row.taskType==0?'表单':'系统校验'}}</span>

					<el-select v-if="scope.row.isEdit" v-model="scope.row.taskType" placeholder="请选择任务类型">
						<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
						</el-option>
					</el-select>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="taskType" label="成立条件">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{scope.row.establishWhere==0?'<':scope.row.establishWhere==1?'=':
                                           scope.row.establishWhere==2?'>':scope.row.establishWhere==3?'<=':
                                           scope.row.establishWhere==4?'>=':scope.row.establishWhere==5?'is not null':
                                           scope.row.establishWhere==6?'is null':'未知'}}</span>

					<el-select v-if="scope.row.isEdit" v-model="scope.row.establishWhere" placeholder="请选择成立条件">
						<el-option v-for="item in establishOptions" :key="item.value" :label="item.label"
							:value="item.value">
						</el-option>
					</el-select>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="inspectType" label="检验类型">
				<template slot-scope="scope">
					<span
						v-if="!scope.row.isEdit">{{scope.row.inspectType==1?'sql校验':scope.row.inspectType==0?'字段校验':'未知'}}</span>

					<el-select v-if="scope.row.isEdit" v-model="scope.row.inspectType" placeholder="请选择校验类型">
						<el-option v-for="item in inspectOptions" :key="item.value" :label="item.label"
							:value="item.value">
						</el-option>
					</el-select>
				</template>
			</el-table-column>

			<el-table-column width="140" prop="checkTable" label="校验表名">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ scope.row.checkTable }}</span>

					<el-input v-model="scope.row.checkTable" style="width: 100%;" type="textarea"
						v-if="scope.row.isEdit&&scope.row.inspectType==0" placeholder="请输入校验表名"
						:autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="checkField" label="校验字段">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ scope.row.checkField }}</span>

					<el-input v-model="scope.row.checkField" style="width: 100%;" type="textarea"
						v-if="scope.row.isEdit&&scope.row.inspectType==0" placeholder="请输入校验字段"
						:autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="checkWhere" label="校验条件">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ scope.row.checkWhere }}</span>

					<el-input v-model="scope.row.checkWhere" style="width: 100%;" type="textarea"
						v-if="scope.row.isEdit&&scope.row.inspectType==0" placeholder="请输入校验条件"
						:autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>

			<el-table-column width="140" prop="whereField" label="校验参数">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ scope.row.whereField }}</span>

					<el-input v-model="scope.row.whereField" style="width: 100%;" type="textarea"
						v-if="scope.row.isEdit&&scope.row.inspectType==0" placeholder="请输入校验参数"
						:autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="checkSql" label="校验sql">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ formatLongStr(scope.row.checkSql) }}</span>

					<el-input v-model="scope.row.checkSql" style="width: 100%;" type="textarea"
						v-if="scope.row.isEdit&&scope.row.inspectType==1" placeholder="请输入校验sql"
						:autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="reward" label="奖励类型">
				<template slot-scope="scope">
					<span
						v-if="!scope.row.isEdit">{{scope.row.reward==1?'实物':scope.row.reward==0?'佳币':scope.row.reward==2?'奖金':'未知'}}</span>
					<el-select v-if="scope.row.isEdit" v-model="scope.row.reward" placeholder="请选择奖励类型">
						<el-option v-for="item in rewardOptions" :key="item.value" :label="item.label"
							:value="item.value">
						</el-option>
					</el-select>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="rewardNumber" label="达标数量">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ scope.row.reachFlag }}</span>

					<el-input v-model="scope.row.reachFlag" style="width: 100%;" type="number" v-if="scope.row.isEdit"
						placeholder="请输入达标数量" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="rewardNumber" label="奖励数量">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ scope.row.rewardNumber }}</span>

					<el-input v-model="scope.row.rewardNumber" style="width: 100%;" type="number"
						v-if="scope.row.isEdit" placeholder="请输入奖励数量" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>

			<el-table-column width="140" prop="rewardThing" label="实物奖励">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ scope.row.rewardThing }}</span>

					<el-input v-model="scope.row.rewardThing" style="width: 100%;" type="textarea"
						v-if="scope.row.isEdit" placeholder="请输入实物奖励" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="appid" label="跳转小程序AppID">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ scope.row.appid ||'未知'}}</span>

					<el-input v-model="scope.row.appid" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
						placeholder="请输入跳转小程序AppID" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>


			<el-table-column width="140" prop="linkUrl" label="跳转链接">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ scope.row.linkUrl }}</span>

					<el-input v-model="scope.row.linkUrl" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
						placeholder="请输入跳转链接" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>

			<el-table-column width="140" prop="createTime" label="创建时间">
				<template slot-scope="scope">
					<span>{{scope.row.createTime}}</span>
				</template>
			</el-table-column>

			<el-table-column width="130" prop="creater" label="创建人">
				<template slot-scope="scope">
					<span>{{scope.row.creater}}</span>
				</template>
			</el-table-column>

			<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
				<template slot-scope="scope">
					<el-button @click="openEdit(scope.row)" type="success" size="small" :disabled="scope.row.isEdit"
						v-if="!scope.row.isEdit" icon="el-icon-edit">修改
					</el-button>
					<el-button @click="updateUnionTask(scope.row)" type="primary" size="small" v-if="scope.row.isEdit"
						icon="el-icon-circle-check">保存
					</el-button>
					<el-popconfirm title="确定删除吗？" @confirm="deleteUnionTask(scope.row)">
						<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
							icon="el-icon-delete">删除
						</el-button>
					</el-popconfirm>

				</template>
			</el-table-column>

		</el-table>


		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--添加成长任务-->
		<el-dialog :visible.sync="certModal" width="85%" title="添加成长任务" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="140px" class="demo-ruleForm" size="mini">

							<el-form-item label="任务标题：" required>
								<el-input v-model="unionTask.taskTitle" type="text" class="handle-input mr10"
									placeholder="请输入任务标题">
								</el-input>
							</el-form-item>

							<el-form-item label="任务介绍：" required>
								<el-input v-model="unionTask.taskIntroduce" type="textarea" class="handle-input mr10"
									placeholder="请输入任务介绍">
								</el-input>
							</el-form-item>

							<el-form-item label="跳转小程序AppID：" v-if="unionTask.taskType==1">
								<el-input v-model="unionTask.appid" type="textarea" class="handle-input mr10"
									placeholder="请输入跳转小程序AppID">
								</el-input>
							</el-form-item>


							<el-form-item label="实物奖励：" v-if="unionTask.reward==1">
								<el-input v-model="unionTask.rewardThing" type="textarea" class="handle-input mr10"
									placeholder="请输入实物奖励">
								</el-input>
							</el-form-item>

							<el-form-item label="任务时限(小时)：">
								<el-input v-model="unionTask.timeLimit" type="number" class="handle-input mr10"
									placeholder="请输入任务时限">
								</el-input>
							</el-form-item>

							<el-form-item label="任务提醒：">
								<el-input v-model="unionTask.remind" type="textarea" class="handle-input mr10"
									placeholder="请输入任务提醒">
								</el-input>
							</el-form-item>


							<el-form-item label="校验表名：" required v-if="unionTask.inspectType==0">
								<el-input v-model="unionTask.checkTable" type="text" class="handle-input mr10"
									placeholder="请输入校验表名">
								</el-input>
							</el-form-item>

							<el-form-item label="校验字段：" required v-if="unionTask.inspectType==0">
								<el-input v-model="unionTask.checkField" type="text" class="handle-input mr10"
									placeholder="请输入校验字段">
								</el-input>
							</el-form-item>


							<el-form-item label="校验条件：" required v-if="unionTask.inspectType==0">
								<el-input v-model="unionTask.checkWhere" type="text" class="handle-input mr10"
									placeholder="请输入校验条件">
								</el-input>
							</el-form-item>


							<el-form-item label="校验参数：" required v-if="unionTask.inspectType==0">
								<el-input v-model="unionTask.whereField" type="text" class="handle-input mr10"
									placeholder="请输入校验参数">
								</el-input>
							</el-form-item>


							<el-form-item label="校验sql：" required v-if="unionTask.inspectType==1">
								<el-input v-model="unionTask.checkSql" type="textarea" class="handle-input mr10"
									placeholder="请输入校验sql" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>


							<el-form-item label="提醒日期：">
								<el-checkbox-group v-model="remindDateList" size="large" v-if="unionTask.remindType==2">
									<el-checkbox-button :label="1">周一</el-checkbox-button>
									<el-checkbox-button :label="2">周二</el-checkbox-button>
									<el-checkbox-button :label="3">周三</el-checkbox-button>
									<el-checkbox-button :label="4">周四</el-checkbox-button>
									<el-checkbox-button :label="5">周五</el-checkbox-button>
									<el-checkbox-button :label="6">周六</el-checkbox-button>
									<el-checkbox-button :label="7">周日</el-checkbox-button>
								</el-checkbox-group>
								<el-checkbox-group v-model="remindDateList" size="large" v-if="unionTask.remindType==3">
									<el-checkbox-button :label="1">01</el-checkbox-button>
									<el-checkbox-button :label="2">02</el-checkbox-button>
									<el-checkbox-button :label="3">03</el-checkbox-button>
									<el-checkbox-button :label="4">04</el-checkbox-button>
									<el-checkbox-button :label="5">05</el-checkbox-button>
									<el-checkbox-button :label="6">06</el-checkbox-button>
									<el-checkbox-button :label="7">07</el-checkbox-button>
									<br />
									<el-checkbox-button :label="8">08</el-checkbox-button>
									<el-checkbox-button :label="9">09</el-checkbox-button>
									<el-checkbox-button :label="10">10</el-checkbox-button>
									<el-checkbox-button :label="11">11</el-checkbox-button>
									<el-checkbox-button :label="12">12</el-checkbox-button>
									<el-checkbox-button :label="13">13</el-checkbox-button>
									<el-checkbox-button :label="14">14</el-checkbox-button>
									<br />
									<el-checkbox-button :label="15">15</el-checkbox-button>
									<el-checkbox-button :label="16">16</el-checkbox-button>
									<el-checkbox-button :label="17">17</el-checkbox-button>
									<el-checkbox-button :label="18">18</el-checkbox-button>
									<el-checkbox-button :label="19">19</el-checkbox-button>
									<el-checkbox-button :label="20">20</el-checkbox-button>
									<el-checkbox-button :label="21">21</el-checkbox-button>
									<br />
									<el-checkbox-button :label="22">22</el-checkbox-button>
									<el-checkbox-button :label="23">23</el-checkbox-button>
									<el-checkbox-button :label="24">24</el-checkbox-button>
									<el-checkbox-button :label="25">25</el-checkbox-button>
									<el-checkbox-button :label="26">26</el-checkbox-button>
									<el-checkbox-button :label="27">27</el-checkbox-button>
									<el-checkbox-button :label="28">28</el-checkbox-button>
									<br />
									<el-checkbox-button :label="29">29</el-checkbox-button>
									<el-checkbox-button :label="30">30</el-checkbox-button>
									<el-checkbox-button :label="31">31</el-checkbox-button>
								</el-checkbox-group>
							</el-form-item>

						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="140px" class="demo-ruleForm" size="mini">


							<el-form-item label="提醒时间">
								<el-time-select class="handle-input mr10" v-model="unionTask.reminTime"
									placeholder="请选择提醒时间" :picker-options="{
                          start: '05:00',
                          step: '00:15',
                          end: '23:00'
                          }">
								</el-time-select>
							</el-form-item>

							<el-form-item label="提醒类型：">
								<el-select v-model="unionTask.remindType" placeholder="请选择提醒类型" clearable>
									<el-option v-for="item in remindTypeOptions" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="任务类型：" required>
								<el-select v-model="unionTask.taskType" placeholder="请选择任务类型" clearable>
									<el-option v-for="item in options" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>


							<el-form-item label="成立条件：" required v-if="!unionTask.taskType==0">
								<el-select v-model="unionTask.establishWhere" placeholder="请选择成立条件" clearable>
									<el-option v-for="item in establishOptions" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="校验类型：" required v-if="!unionTask.taskType==0">
								<el-select v-model="unionTask.inspectType" placeholder="请选择校验类型" clearable>
									<el-option v-for="item in inspectOptions" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>


							<el-form-item label="达标数量：">
								<el-input v-model="unionTask.reachFlag" type="number" class="handle-input mr10"
									placeholder="请输入达标数量">
								</el-input>
							</el-form-item>


							<el-form-item label="奖励类型：">
								<el-select v-model="unionTask.reward" placeholder="请选择奖励类型" clearable>
									<el-option v-for="item in rewardOptions" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="奖励数量：">
								<el-input v-model="unionTask.rewardNumber" type="number" class="handle-input mr10"
									placeholder="请输入奖励数量">
								</el-input>
							</el-form-item>

							<el-form-item label="跳转链接：" v-if="unionTask.taskType==1">
								<el-input v-model="unionTask.linkUrl" type="textarea" class="handle-input mr10"
									placeholder="请输入跳转链接">
								</el-input>
							</el-form-item>

						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="saveUnionTask()" type="success" size="small">确定添加
					</el-button>
				</div>

			</div>
		</el-dialog>


		<el-dialog title="绑定表单：" :visible.sync="dialogFlag" width="80%">
			<div style="display: flex">
				<el-button style="margin-left: 10px;margin-bottom: 20px;" type="success"
					icon="el-icon-circle-plus-outline" @click="formModal=true">添加绑定表单
				</el-button>
			</div>
			<el-table :data="formList" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column width="140" prop="formTitle" label="表单标题">
					<template slot-scope="scope">
						<span>{{ scope.row.formTitle }}</span>
					</template>
				</el-table-column>


				<el-table-column width="140" prop="formIntroduce" label="表单介绍">
					<template slot-scope="scope">
						<span>{{ scope.row.formIntroduce }}</span>
					</template>
				</el-table-column>


				<el-table-column width="140" prop="sort" label="表单排序">
					<template slot-scope="scope">
						<span v-if="!scope.row.isEdit">{{ scope.row.sort }}</span>
						<el-input v-model="scope.row.sort" style="width: 100%;" type="number" v-if="scope.row.isEdit"
							placeholder="请输入表单排序" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="formType" label="表单类型">
					<template slot-scope="scope">
						<span>{{scope.row.formType==1?'图片':scope.row.formType==0?'文本':'未知'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="mappingTable" label="映射表名">
					<template slot-scope="scope">
						<span>{{ scope.row.mappingTable }}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="mappingField" label="映射字段">
					<template slot-scope="scope">
						<span>{{ scope.row.mappingField }}</span>
					</template>
				</el-table-column>


				<el-table-column width="140" prop="createTime" label="创建时间">
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creater" label="创建人">
					<template slot-scope="scope">
						<span>{{scope.row.creater}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
					<template slot-scope="scope">
						<el-button @click="openEdit(scope.row)" type="success" size="small" :disabled="scope.row.isEdit"
							v-if="!scope.row.isEdit" icon="el-icon-edit">修改
						</el-button>
						<el-button @click="updateTaskForm(scope.row)" type="primary" size="small"
							v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
						</el-button>
						<el-popconfirm title="确定删除吗？" @confirm="deleteTaskForm(scope.row)">
							<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
								icon="el-icon-delete">删除
							</el-button>
						</el-popconfirm>

					</template>
				</el-table-column>

			</el-table>
		</el-dialog>


		<el-dialog title="流程演示：" :visible.sync="imgFlowFlag" width="80%">
			<div style="display: flex">
				<el-button style="margin-left: 10px;margin-bottom: 20px;" type="success"
					icon="el-icon-circle-plus-outline" @click="clickAddImgFlow">添加流程演示
				</el-button>
			</div>
			<el-table :data="taskImgFlowList" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
				:expand-row-keys="expands" @expand-change="expandSelect">

				<el-table-column width="300" prop="imgUrl" label="演示图片">
					<template slot-scope="scope">
						<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
							:disabled="!scope.row.isEdit" list-type="picture" :on-success="imgUploadSuccess"
							:show-file-list="false">
							<img :src="scope.row.imgUrl!=null?scope.row.imgUrl:blankImg"
								v-loading.fullscreen.lock="fullscreenLoading" @click="uploadImgUrl(scope.row)"
								style="width: 180px;height: 210px;">
						</el-upload>
					</template>
				</el-table-column>


				<el-table-column width="140" prop="sort" label="演示排序">
					<template slot-scope="scope">
						<span v-if="!scope.row.isEdit">{{ scope.row.sort }}</span>
						<el-input v-model="scope.row.sort" style="width: 100%;" type="number" v-if="scope.row.isEdit"
							placeholder="请输入演示排序" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
					</template>
				</el-table-column>


				<el-table-column width="140" prop="createTime" label="创建时间">
					<template slot-scope="scope">
						<span>{{scope.row.createTime}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creater" label="创建人">
					<template slot-scope="scope">
						<span>{{scope.row.creater}}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
					<template slot-scope="scope">
						<el-button @click="openEdit(scope.row,1,scope.$index)" type="success" size="small"
							:disabled="scope.row.isEdit" v-if="!scope.row.isEdit" icon="el-icon-edit">修改
						</el-button>
						<el-button @click="updateImgFlow(scope.row)" type="primary" size="small" v-if="scope.row.isEdit"
							icon="el-icon-circle-check">保存
						</el-button>
						<el-popconfirm title="确定删除吗？" @confirm="deleteImgFlow(scope.row)">
							<el-button type="danger" size="small" slot="reference" style="margin-left: 10px"
								icon="el-icon-delete">删除
							</el-button>
						</el-popconfirm>

					</template>
				</el-table-column>

			</el-table>
		</el-dialog>


		<!--添加绑定表单-->
		<el-dialog :visible.sync="formModal" width="60%" title="添加绑定表单" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">

							<el-form-item label="表单排序：">
								<el-input v-model="unionTaskForm.sort" type="number" class="handle-input mr10"
									placeholder="请输入表单排序">
								</el-input>
							</el-form-item>

							<el-form-item label="成长表单：">
								<el-select v-model="unionTaskForm.formId" placeholder="请选择成长表单" clearable>
									<el-option v-for="item in formOptions" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</el-form-item>


						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="saveTaskForm()" type="success" size="small">确定添加
					</el-button>
				</div>

			</div>
		</el-dialog>


		<el-dialog :visible.sync="imgFlowModal" width="60%" title="添加流程演示" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">

							<el-form-item label="演示排序：">
								<el-input v-model="taskImgFlow.sort" type="number" class="handle-input mr10"
									placeholder="请输入演示排序">
								</el-input>
							</el-form-item>

							<el-form-item label="演示图片：">
								<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
									list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="taskImgFlow.imgUrl!=null?taskImgFlow.imgUrl:blankImg"
										style="width: 250px;height: 250px;"
										v-loading.fullscreen.lock="fullscreenLoading"
										@click="uploadImgUrl(taskImgFlow)">
								</el-upload>
							</el-form-item>


						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="saveImgFlow()" type="success" size="small">确定添加
					</el-button>
				</div>

			</div>
		</el-dialog>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl!=null?imageUrl:blankImg" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

	</div>

</template>

<script>
	export default {
		name: "qaLibraryType",
		components: {},
		data() {
			return {
				url: '',
				uploadId: 1,
				imgModal: false,
				imageUrl: '',
				remindDateList: [],
				dialogFlag: false,
				imgFlowIndex: null,
				imgFlowFlag: false,
				formModal: false,
				imgFlowModal: false,
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				excelUploadId: 1,
				isModal: false,
				isEdit: false,
				showEdit: true,
				certModal: false,
				remindTypeOptions: [{
					value: 0,
					label: '每天重复'
				}, {
					value: 1,
					label: '工作日重复'
				}, {
					value: 2,
					label: '按周重复'
				}, {
					value: 3,
					label: '按月重复'
				}],
				options: [{
					value: 0,
					label: '表单'
				}, {
					value: 1,
					label: '链接'
				}, {
					value: 2,
					label: '系统校验'
				}],
				establishOptions: [{
					value: 0,
					label: '<'
				}, {
					value: 1,
					label: '='
				}, {
					value: 2,
					label: '>'
				}, {
					value: 3,
					label: '<='
				}, {
					value: 4,
					label: '>='
				}, {
					value: 5,
					label: 'is not null'
				}, {
					value: 6,
					label: 'is null'
				}],
				inspectOptions: [{
					value: 0,
					label: '字段校验'
				}, {
					value: 1,
					label: 'sql校验'
				}],
				rewardOptions: [{
					value: 0,
					label: '佳币'
				}, {
					value: 1,
					label: '实物'
				}, {
					value: 2,
					label: '奖金'
				}, ],
				list: [],
				uploadType: null,
				formList: [],
				taskImgFlowList: [],
				formOptions: [],
				fullscreenLoading: false,
				loading: true,
				pageSizeOpts: [5, 10, 20],
				unionTask: {
					creater: localStorage.getItem("id"),
					remindDate: ''
				},
				certType: {},
				unionTaskForm: {
					creater: localStorage.getItem("id"),
					taskId: null
				},
				taskImgFlow: {
					creater: localStorage.getItem("id"),
					taskId: null,
					isEdit: true
				},
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				expands: [],
				getRowKeys(row) {
					return row.id
				},
				quer: {
					"taskTitle": "",
					"taskIntroduce": "",
					"flowId": null,
					// "taskType": null,
					"reward": null,
					"linkUrl": "",
					"current": 1,
					"size": 10
				},
			}
		},
		created() {
			this.getData()
			this.getAllUnionForm()
		},
		methods: {
			formatLongStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					let long = 240
					if (str.length > long) {
						str = str.substring(0, long) + "..."
					}
					return str
				}
			},
			clickAddImgFlow() {
				this.imgFlowModal = true
				this.uploadType = 0
			},
			uploadImgUrl(val) {
				if (val.isEdit) {
					this.fullscreenLoading = true;
				} else {
					// 打开图片预览
					if (val.imgUrl != null && val.imgUrl != '') {
						this.imageUrl = val.imgUrl
					} else {
						this.imageUrl = this.blankImg
					}
					this.imgModal = true
				}
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadType === 0) {
					this.taskImgFlow.imgUrl = res.data
				}
				if (this.uploadType === 1) {
					this.taskImgFlowList[this.imgFlowIndex].imgUrl = res.data
				}
				this.fullscreenLoading = false;
			},
			getAllUnionForm() {
				// 获取所有成长表单
				this.$getData("getAllUnionForm", null).then(res => {
					if (res.code == 0) {
						this.formOptions = res.data
					} else {
						this.formOptions = []
						this.$message.error('未查询到成长表单!')
					}
				})
			},
			selectBingDingForm(val) {
				if (val.taskType != 0) {
					return this.$message.error('此任务类型不支持表单配置!')
				}
				this.unionTaskForm.taskId = val.id
				this.dialogFlag = true
				this.getTaskForm()
			},
			setTaskImgFlow(val) {
				if (val.taskType != 1) {
					return this.$message.error('此任务类型不支持流程演示配置!')
				}
				this.taskImgFlow.taskId = val.id
				this.imgFlowFlag = true
				this.getImgFlow()
			},
			getImgFlow() {
				// 获取任务表单
				this.$getData("getImgFlow", this.taskImgFlow).then(res => {
					if (res.code == 0) {
						this.taskImgFlowList = res.data
						// 追加编辑状态位
						for (let item of this.taskImgFlowList) {
							this.$set(item, "isEdit", false)
						}
					} else {
						this.$message.error('未查询到任务表单!')
					}
				})
			},
			getTaskForm() {
				// 获取任务表单
				this.$getData("getTaskForm", this.unionTaskForm).then(res => {
					if (res.code == 0) {
						this.formList = res.data
						// 追加编辑状态位
						for (let item of this.formList) {
							this.$set(item, "isEdit", false)
						}
					} else {
						this.$message.error('未查询到任务表单!')
					}
				})
			},
			getData() {
				// 获取成长任务
				this.$getData("getUnionTask", this.quer).then(res => {
					this.loading = false
					if (res.code == 0) {
						this.list = res.data.records
						this.pageInfo.current = res.data.current
						this.pageInfo.size = res.data.size
						this.pageInfo.total = res.data.total
						// 追加编辑状态位
						for (let item of this.list) {
							this.$set(item, "isEdit", false)
						}
					} else {
						this.list = []
						this.$message.error('未查询到成长任务!')
					}
				})
				this.getAllUnionForm()
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.getData()
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			// 打开修改
			openEdit(val, flag, index) {
				val.isEdit = true
				if (flag === 1) {
					this.uploadType = 1
					this.imgFlowIndex = index
				}
			},
			saveImgFlow() {
				// 数据校验
				if (!this.taskImgFlow.imgUrl) {
					this.$message.error('请上传演示图片！')
				} else if (!this.taskImgFlow.sort) {
					this.$message.error('请输入演示排序！')
				} else if (this.taskImgFlow.sort <= 0) {
					this.$message.error('演示排序不可为负数或0！')
				} else {
					this.$postData("saveImgFlow", this.taskImgFlow).then(res => {
						if (res.code == 0) {
							this.$message.success('添加成功!')
							this.imgFlowModal = false
							this.taskImgFlow.imgUrl = ''
							this.taskImgFlow.sort = null
							this.getImgFlow()
						} else {
							this.$message.error('添加失败！' + res.msg)
						}
					})
				}
			},
			// 添加成长任务
			saveUnionTask() {
				console.log("this.unionTask", this.unionTask)
				this.unionTask.remindDate = this.remindDateList.join(",");
				// 数据校验
				if (!this.unionTask.taskTitle) {
					this.$message.error('请输入任务标题！')
				} else if (!this.unionTask.taskIntroduce) {
					this.$message.error('请输入任务介绍！')
				} else if (this.unionTask.taskType == null) {
					this.$message.error('请选择任务类型！')
				} else if (!this.unionTask.taskType == 0 && this.unionTask.inspectType == null) {
					this.$message.error('请选择校验类型！')
				} else if (!this.unionTask.taskType == 0 && this.unionTask.establishWhere == null) {
					this.$message.error('请选择成立条件！')
				} else if (this.unionTask.inspectType == 0 && !this.unionTask.checkTable) {
					this.$message.error('请输入校验表名！')
				} else if (this.unionTask.inspectType == 0 && !this.unionTask.checkField) {
					this.$message.error('请输入校验字段！')
				} else if (this.unionTask.inspectType == 0 && !this.unionTask.checkWhere) {
					this.$message.error('请输入校验条件！')
				} else if (this.unionTask.inspectType == 0 && !this.unionTask.whereField) {
					this.$message.error('请输入校验参数！')
				} else if (this.unionTask.inspectType == 1 && !this.unionTask.checkSql) {
					this.$message.error('请输入校验sql！')
				} else if (this.unionTask.rewardNumber != null && this.unionTask.rewardNumber <= 0) {
					this.$message.error('奖励数量不可为负数或0！')
				} else if (this.unionTask.reachFlag != null && this.unionTask.reachFlag <= 0) {
					this.$message.error('达标数量不可为负数或0！')
				} else if (this.unionTask.timeLimit != null && this.unionTask.timeLimit <= 0) {
					this.$message.error('任务时限不可为负数或0！')
				} else {
					this.$postData("saveUnionTask", this.unionTask).then(res => {
						if (res.code == 0) {
							this.$message.success('添加成功!')
							this.certModal = false
							this.unionTask.taskTitle = ''
							this.unionTask.taskIntroduce = ''
							this.unionTask.appid = ''
							this.unionTask.remind = ''
							this.unionTask.remindDate = ''
							this.unionTask.taskType = null
							this.unionTask.reachFlag = null
							this.remindDateList = []
							this.unionTask.timeLimit = null
							this.unionTask.reminTime = null
							this.unionTask.remindType = null
							this.unionTask.reward = null
							this.unionTask.rewardNumber = null
							this.unionTask.inspectType = null
							this.unionTask.establishWhere = null
							this.unionTask.checkField = ''
							this.unionTask.checkTable = ''
							this.unionTask.checkWhere = ''
							this.unionTask.whereField = ''
							this.unionTask.checkSql = ''
							this.unionTask.rewardThing = ''
							this.unionTask.linkUrl = ''
							this.getData()
						} else {
							this.$message.error('添加失败！' + res.msg)
						}
					})
				}
			},
			// 添加表单任务
			saveTaskForm() {
				// 数据校验
				if (!this.unionTaskForm.sort) {
					this.$message.error('请输入表单排序！')
				} else if (this.unionTaskForm.sort <= 0) {
					this.$message.error('表单排序不可为负数或0！')
				} else if (this.unionTaskForm.formId == null) {
					this.$message.error('请选择成长表单！')
				} else {
					this.$postData("saveTaskForm", this.unionTaskForm).then(res => {
						if (res.code == 0) {
							this.$message.success('添加成功!')
							this.formModal = false
							this.unionTaskForm.sort = ''
							this.unionTaskForm.formId = null
							this.getTaskForm()
						} else {
							this.$message.error('添加失败！' + res.msg)
						}
					})
				}
			},
			// 删除成长任务
			deleteUnionTask(val) {
				this.$postData("deleteUnionTask", val).then(res => {
					if (res.code == 0) {
						this.$message.success('删除成功!')
						this.getData()
					} else {
						this.$message.error('删除失败！' + res.msg)
					}
				})
			},
			deleteImgFlow(val) {
				this.$postData("deleteImgFlow", val).then(res => {
					if (res.code == 0) {
						this.$message.success('删除成功!')
						this.getImgFlow()
					} else {
						this.$message.error('删除失败！' + res.msg)
					}
				})
			},
			// 删除任务任务
			deleteTaskForm(val) {
				this.$postData("deleteTaskForm", val).then(res => {
					if (res.code == 0) {
						this.$message.success('删除成功!')
						this.getTaskForm()
					} else {
						this.$message.error('删除失败！' + res.msg)
					}
				})
			},
			// 更改成长任务
			updateUnionTask(val) {
				// 数据校验
				if (!val.taskTitle) {
					this.$message.error('请输入任务标题！')
				} else if (!val.taskIntroduce) {
					this.$message.error('请输入任务介绍！')
				} else if (val.taskType == null) {
					this.$message.error('请选择任务类型！')
				} else if (!val.taskType == 0 && val.inspectType == null) {
					this.$message.error('请选择校验类型！')
				} else if (!val.taskType == 0 && val.establishWhere == null) {
					this.$message.error('请选择成立条件！')
				} else if (val.inspectType == 0 && !val.checkTable) {
					this.$message.error('请输入校验表名！')
				} else if (val.inspectType == 0 && !val.checkField) {
					this.$message.error('请输入校验字段！')
				} else if (val.inspectType == 0 && !val.checkWhere) {
					this.$message.error('请输入校验条件！')
				} else if (val.inspectType == 0 && !val.whereField) {
					this.$message.error('请输入校验参数！')
				} else if (val.inspectType == 1 && !val.checkSql) {
					this.$message.error('请输入校验sql！')
				} else if (val.rewardNumber != null && val.rewardNumber <= 0) {
					this.$message.error('奖励数量不可为负数或0！')
				} else if (val.reachFlag != null && val.reachFlag <= 0) {
					this.$message.error('达标数量不可为负数或0！')
				} else if (val.timeLimit != null && val.timeLimit <= 0) {
					this.$message.error('任务时限不可为负数或0！')
				} else {
					val.creater = localStorage.getItem("id")
					this.$postData("updateUnionTask", val).then(res => {
						if (res.code == 0) {
							this.$message.success('更新成功!')
							this.getData()
							val.isEdit = false
						} else {
							this.$message.error('更新失败！' + res.msg)
						}
					})
				}
			},
			updateImgFlow(val) {
				// 数据校验
				if (!val.sort) {
					this.$message.error('请输入演示排序！')
				} else if (val.sort <= 0) {
					this.$message.error('演示排序不可为负数或0！')
				} else {
					val.creater = localStorage.getItem("id")
					this.$postData("updateImgFlow", val).then(res => {
						if (res.code == 0) {
							this.$message.success('更新成功!')
							this.getImgFlow()
							val.isEdit = false
						} else {
							this.$message.error('更新失败！' + res.msg)
						}
					})
				}
			},
			// 更改任务表单
			updateTaskForm(val) {
				// 数据校验
				if (!val.sort) {
					this.$message.error('请输入表单排序！')
				} else if (val.sort <= 0) {
					this.$message.error('表单排序不可为负数或0！')
				} else {
					val.creater = localStorage.getItem("id")
					this.$postData("updateTaskForm", val).then(res => {
						if (res.code == 0) {
							this.$message.success('更新成功!')
							this.getTaskForm()
							val.isEdit = false
						} else {
							this.$message.error('更新失败！' + res.msg)
						}
					})
				}
			},
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>