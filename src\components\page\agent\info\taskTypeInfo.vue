<template>
    <div style="background:#fff;padding: 20px">

        <div class="block">
            <span class="demonstration">查询区间</span>&nbsp;
            <el-input v-model="taskType.no" placeholder="请输入员工工号" style="width: 150px" clearable></el-input>
            <el-date-picker
                    v-model="showDate"
                    type="datetimerange"
                    :picker-options="pickerOptions"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    align="right">
            </el-date-picker>&nbsp;
            <el-button type="primary" @click="searchDate()">区间分析</el-button>
        </div><br>
        <el-card shadow="hover" body-style="padding: 5px 20px " >
            <h1>{{dom.title}}</h1><br>
            <span v-for="(item,index) in numByStatus" :key="index">
                <span class="numByStatus">{{item.status==0?'未处理':item.status==1?'已处理':'无人接听'}}: <span style="color: #f00f14"><b>{{item.num}}</b></span></span>
            </span>
            <br>
            <br>
        </el-card><br>
        <el-card shadow="hover" body-style="padding: 5px 20px " v-if="getDateShow">
            <br>
            <el-row>

                <el-col :span="24" v-for="(item,index) in formByTaskTypeId" :key="index" style="padding: 10px">

                    <h2>{{index+1}}.{{item.title}}<span class="numByStatus">{{item.type==1?'文本':item.type==2?'单选':'多选'}}</span></h2><br>

                    <div>
                        {{numByResponses[index]?'':getNumByResponse(item.id,index)}}
                        <el-row>
                            <el-col :span="8">

                                    <!-- {{formByTaskTypeId[index].answers}}-->
                                    <g2char :setCharData="formByTaskTypeId[index].answers" v-if="showChar"></g2char>
                            </el-col>
                            <el-col :span="16">
                                <div v-for="anser in formByTaskTypeId[index].answers">
                                    <el-card shadow="hover">
                                        <el-tag type="danger"><b>{{anser.num}}</b></el-tag>
                                        {{anser.response}}
                                    </el-card>
                                </div>
                            </el-col>



                        </el-row>

                    </div>

                    <el-divider></el-divider>
                </el-col>
            </el-row>

        </el-card>
    </div>

</template>

<script>
    import g2char from "../char/g2char";
    export default {
        components: {"g2char":g2char},

        name: "taskTypeInfo",
        data() {
            return {
                showChar:false,
                getDateShow:true,
                numByResponses:[],
                numByStatus:[],
                taskType:{
                    no:null,
                    startTime:null,
                    endTime:null,
                    id:this.$route.query.id,
                },
                taskTypeList:[],
                formByTaskTypeId:[],
                dom:{},
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                showDate: ''
            }
        },
        created(){
            this.getTaskType()
            this.getNumByStatus()
        },
        methods:{
            searchDate(){
                this.getDateShow=false
                this.numByResponses=[]
                console.log(this.showDate)
                if (this.showDate!=null){
                    this.taskType.startTime=this.showDate[0]
                    this.taskType.endTime=this.showDate[1]
                }

                this.getNumByStatus()
                this.getDateShow=true

            },
            getFormByTaskTypeId(id) {
                this.$postUrl("getFormByTaskTypeId",id,null,null).then(res => {
                    if (res.status == 200) {
                        this.formByTaskTypeId=res.data
                        this.formByTaskTypeId.forEach(v=>{
                            this.numByResponses.push(false)
                        })
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getNumByStatus(){
                this.$postData("getNumByStatus", this.taskType, {}).then(res => {
                    if (res.status == 200) {
                        this.numByStatus=res.data
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getNumByResponse(id,index){
                if (this.numByResponses[index]){
                    return
                }
                this.numByResponses[index]=true
                let dom={
                    id:id,
                    no:this.taskType.no,
                    startTime:this.taskType.startTime,
                    endTime:this.taskType.endTime
                }
                this.showChar=false
                this.$postData("getNumByResponse",dom, {}).then(res => {
                    this.showChar=true
                    if (res.status == 200) {
                        this.formByTaskTypeId[index].answers=res.data

                        return this.formByTaskTypeId[index].answers
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getTaskType() {
                this.$postData("taskTypeList", this.taskType, {}).then(res => {
                    if (res.status == 200) {
                        this.taskTypeList=res.data
                        this.dom=this.taskTypeList[0]
                        this.getFormByTaskTypeId(this.dom.id)
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })

            },
        }
    }
</script>

<style scoped>
    .numByStatus{
        font-size: 14px;
        padding: 10px;
        line-height: 30px;
        border: 1px solid #ddd;
        border-radius: 8px;
        margin-right: 10px;
    }
</style>
