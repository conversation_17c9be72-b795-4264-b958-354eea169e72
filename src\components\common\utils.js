/**
 * 格式化日期
 * 兼容ie ios 日期nan问题
 *
 * @param date
 * @param fmt 默认格式为 yyyy-MM-dd
 * @return {*}
 */
export function formatDate(date) {

    if (date == null || date == ''){
        return null;
    }
    let date1;
    if(date.indexOf("T")>=0){
        let num=date.indexOf("T");
        console.log(num)
        date1=date.substring(0,num)+" "
        date1+=date.substring(num+1,19)
    }
    console.log(date1)
    return date1
}



