<template>
    <div class="table">
        <el-dialog title="展示地址" :visible.sync="dialogTableVisible">
            <vue-qr :text="showUrl" :margin="0"  colorLight="#fff"  :logoScale="0.3" :size="100" class="hbeimg"></vue-qr>

        </el-dialog>

      <el-dialog title="首页弹窗" :visible.sync="dialogTipTableVisible">
        <el-form>
          <el-form-item label="当前弹窗图片" v-if="dic.img">
            <div style="float: left">
              <el-image :src="dic.img" style="width: 220px; height: 220px"></el-image>
            </div>
            <div style="float: left">
              <el-upload
                  class="avatar-uploader"
                  action="https://api.xiaoyujia.com/system/imageUpload"
                  :show-file-list="false"
                  :on-success="handleUploadSuccess">
                <i class="el-icon-plus avatar-uploader-icon">点击上传</i>
              </el-upload>
            </div>

          </el-form-item>
          <el-form-item label="跳转地址(路径/开头)">
            <el-input v-model="dic.text"></el-input>
          </el-form-item>
          <el-form-item label="是否开启弹窗">
            <el-switch
                v-model="dic.isHide"
                active-text="开启"
                inactive-text="关闭"></el-switch>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
         <el-button @click="dialogTipTableVisible = false">取 消</el-button>
        <el-button type="primary" @click="updateDict">确 定</el-button>
        </span>
      </el-dialog>
        <div class="container">
            <el-form :model="dom"  label-width="100px" class="demo-ruleForm">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="名称" prop="lng">
                            <el-input v-model="dom.name"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="状态" prop="lat">
                            <el-radio v-model="dom.status" :label="1" border>启用中</el-radio>
                            <el-radio v-model="dom.status" :label="0" border>停用中</el-radio>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item>

                            <el-button type="primary"  @click="getMarketingList()" icon="el-icon-search" round plain>查询</el-button>
                            <el-button type="info"  @click="toEdit(null)" icon="el-icon-circle-plus-outline" round plain>新增</el-button>
                          <el-button type="success" @click="bannerTip()" icon="el-icon-circle-plus-outline" round plain>
                            首页弹窗设置
                          </el-button>
                        </el-form-item>
                    </el-col>
                </el-row>

            </el-form>
        </div>
        <el-row :gutter="20">



        <div v-for="(item,index) in showList" :key="index" style="margin: 20px;line-height: 24px"   >
            <el-col :span="12" style="margin-bottom: 20px">
            <el-card shadow="hover">
                <div >
                    <h2>
                        <i class="el-icon-brush"/>  &nbsp;{{item.name}}
                        <el-tag :type="item.status==0?'danger':'success'">{{item.status==0?'停用':'启用'}}</el-tag>
                        <el-button type="primary" icon="el-icon-edit" circle style="float: right;margin-left:  20px" @click="toEdit(item.id)"></el-button>
                        <el-button type="warning" icon="el-icon-view" circle  style="float: right;margin-left:  20px" @click="showQr(item.code)"></el-button>
                        <el-switch
                                @change="saveMarketing(item)"
                                style="float: right"
                                v-model="item.status"
                                :active-value="1"
                                :inactive-value="0"
                                active-color="#13ce66"
                                inactive-color="#ff4949">
                        </el-switch>
                    </h2>
                    <el-row>
                        <el-col :span="24">
                            <div><i class="el-icon-collection-tag"></i>活动编号：{{item.code}}</div>
                        </el-col>
                        <el-col :span="12">
                            <div><i class="el-icon-date"></i>创建时间：{{item.creatTime}}</div>
                        </el-col>
                        <el-col :span="12">
                            <div><i class="el-icon-view"/>访问量：{{item.viewNum}}</div>
                        </el-col>
                      <el-col :span="12">
<!--                        <el-button type="warning"  @click="showDouyinQr(item.code)">生成抖音分享码</el-button>-->
                      </el-col>
                    </el-row>





                </div>
            </el-card>
            </el-col>
        </div>
        </el-row>
    </div>

</template>

<script>
    import vueQr from 'vue-qr'
    export default {
        name: "activityPage",
        components: {
            vueQr
        },
        data () {
            return {
                dom:{
                    name:null,
                    status:1,
                },
              dic: {},
                showList:[],
                dialogTableVisible: false,
              dialogTipTableVisible: false,
                showUrl: null,
              douyinImage:null,
            }
        },
        created(){
            this.getMarketingList()
        },
        methods:{
            showQr(code){
                this.showUrl="https://m.xiaoyujia.com/pages/product/activity?code="+code
                this.dialogTableVisible=true
            },
            toEdit(id){
                this.$router.push({path:'/creatActivity',query:{"id":id}})
            },
            getMarketingList(){
                this.$postData("getMarketingList", this.dom,null).then(res => {
                    if (res.status == 200) {
                        this.showList=res.data
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }

                })
            },
            saveMarketing(item){
                this.$postData("saveMarketing", item,null).then(res => {
                    if (res.status == 200) {
                        this.$message.success("更新成功")
                    } else {
                        this.$message.error("更新失败，");
                    }

                })
            },
          // showDouyinQr(code) {
          //     let param = {
          //       code:code
          //     }
          //   this.$postData("getDouYinCode", param,null).then(res => {
          //     if (res.status == 200) {
          //       this.douyinImage=res.data;
          //       this.dialogTableVisible=true
          //     } else {
          //       this.$message.error("生成失败");
          //     }
          //
          //   })
          // }
          bannerTip() {
            this.$postUrl("get_dict", 252, null, {}).then(res => {
              if (res.status == 200) {
                this.dic = res.data[0]
                this.dialogTipTableVisible = true;
              } else {
                this.$message.error("查询失败，" + res.msg);
              }
            })

          },
          handleUploadSuccess(res) {
            if (res.code === 0) {
              this.dic.img = res.data;
            } else {
              this.$message.error(res.msg || '上传失败');
            }
          },
          updateDict() {
            this.$postData("updateDict" , this.dic).then(res => {
              if (res.status == 200) {
                this.$message.success("更新成功");
                this.dialogTipTableVisible = false;
              } else {
                this.$message.error("更新失败，" + res.msg);
              }
            })

          }
        }
    }
</script>

<style scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

</style>
