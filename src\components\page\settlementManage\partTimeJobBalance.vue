<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="8">
            <el-form-item label="提现时间">
              <el-date-picker v-model="days" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="门店名称">
              <el-input
                  clearable
                  v-model="form.storeName"
                  placeholder="请输入门店名称"
                  style="width:150px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item  label="审批进度">
              <el-select style="width:160px" v-model="form.auditingSchedule" clearable placeholder="请选择审批进度">
                <el-option v-for="item in typeOptions"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
            <el-button type="info"
                       style="margin-bottom: 15px"
                       @click="exportBasisPartTimeJobWithdrawal"
                       icon="el-icon-download">导出
            </el-button>
            <el-button type="info"
                       style="margin-bottom: 15px"
                       @click="exportPartTimeJobWithdrawal"
                       icon="el-icon-download">导出(含订单详情)
            </el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="到账时间">
              <el-date-picker v-model="queryDays" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="交易流水">
              <el-input
                  clearable
                  v-model="form.serialNumber"
                  placeholder="请输入交易流水"
                  style="width:150px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="提现人">
              <el-input
                  clearable
                  v-model="form.realName"
                  placeholder="请输入提现人"
                  style="width:160px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item  label="状态">
              <el-select style="width:160px" v-model="form.state" clearable placeholder="请选择状态">
                <el-option v-for="item in options"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
          <el-row>
          <el-col :span="6">
            <el-button type="warning"
                       style="margin-bottom: 15px"
                       @click="dialogBalance = true"
                       icon="el-icon-question">异地产品结算说明
            </el-button>
          </el-col>

          <el-col :span="6">
            <el-button type="primary"
                       style="margin-bottom: 15px"
                       @click="getDialogPrecharge"
                       icon="el-icon-folder-remove">人力窝预充
            </el-button>
          </el-col>

          <el-col :span="6">
            <el-button type="info"
                       style="margin-bottom: 15px"
                       @click="dcczmx"
                       icon="el-icon-download">导出出账明细
            </el-button>
          </el-col>
          <el-col :span="3">
           <div style="font-size: 15px;font-weight: bold;color: red">当日已出账总额：￥{{toDayReceivedSumMoney}}</div>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="提现记录">
          <el-table :data="logList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column prop="createTime" label="提现时间" width="90"></el-table-column>
            <el-table-column prop="receivedTime" label="到账时间" width="90"></el-table-column>
            <el-table-column prop="state" label="状态" width="150">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.state===1" type="warning">待审核</el-tag>
                <el-tag v-if="scope.row.state===2" type="success">通过</el-tag>
                <el-tag v-if="scope.row.state===3" type="danger">不通过</el-tag>
                <el-tag v-if="scope.row.state===4" type="warning">人力窝审核中</el-tag>
                <el-tag v-if="scope.row.state===5" type="warning">人力窝结算中</el-tag>
                <el-tag v-if="scope.row.state===6" type="success">结算成功</el-tag>
                <el-tag v-if="scope.row.state===7" type="danger">人力窝审核驳回</el-tag>
                <el-tag v-if="scope.row.state===8" type="danger">人力窝结算异常</el-tag>
                <el-tag v-if="scope.row.state===9" type="warning">冻结</el-tag>
                <el-tag v-if="scope.row.state===10" type="warning">解冻</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="auditingSchedule" label="当前审批流程" width="100">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.auditingSchedule===1" type="danger">交付</el-tag>
                <el-tag v-if="scope.row.auditingSchedule===2" type="danger">会计</el-tag>
                <el-tag v-if="scope.row.auditingSchedule===3" type="danger">出纳</el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="storeName" label="门店名称" width="160"></el-table-column>
            <el-table-column prop="withdrawalAmount" label="提现金额" width="100"></el-table-column>
            <el-table-column prop="serviceCharge" label="服务费" width="100"></el-table-column>
            <el-table-column prop="sumServiceMoney" label="合计" width="100"></el-table-column>
            <el-table-column prop="realName" label="提现人" width="80"></el-table-column>
            <el-table-column prop="serialNumber" label="交易流水号" width="140"></el-table-column>
            <el-table-column label="提现金额来源" width="140">
              <template slot-scope="scope">
              <el-tag type="primary" @click="selectOrderList(scope.row.id)">点击查看</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="人力窝打款状态查询" width="140">
              <template slot-scope="scope">
                <el-tag type="primary" @click="getRlwQuestLog(scope.row.id)">点击查看</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="operatorPeopleRealName" label="处理人" width="80"></el-table-column>
            <el-table-column prop="remark" label="备注" width="100">
              <template slot-scope="scope">
                <div style="color: red">{{scope.row.remark}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="financeRemark" label="财务备注" width="100">
              <template slot-scope="scope">
                <div style="color: red">{{scope.row.financeRemark}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="financeRemark" label="交付备注" width="100">
              <template slot-scope="scope">
                <div style="color: red">{{scope.row.deliverRemark}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="operateTime" label="处理时间" width="90"></el-table-column>
            <el-table-column prop="refusalReason" label="未通过原由" width="100">
              <template slot-scope="scope">
                <div style="color: red">{{scope.row.refusalReason}}</div>
              </template>
            </el-table-column>

            <el-table-column label="操作" fixed="right" width="100" v-if="roleId==2||roleId==79">
              <template slot-scope="scope">
                <div v-if="roleId==2||roleId==57||roleId==1||roleId==79">
                  <el-button v-if="roleId==79" :disabled="(scope.row.state!=1&&scope.row.state!=3)||scope.row.auditingSchedule!=1" @click.native.prevent="showRow(scope.row.id,scope.row.state)" type="text" size="small">操作</el-button>
                  <el-button  v-if="roleId==2" :disabled="scope.row.auditingSchedule==1||scope.row.state!=2" @click.native.prevent="showRow(scope.row.id,scope.row.state)" type="text" size="small">操作</el-button>
<!--                  <el-button  v-if="roleId==57" :disabled="scope.row.auditingSchedule!=3||scope.row.state!=2" @click.native.prevent="showRow(scope.row.id,scope.row.state)" type="text" size="small">操作</el-button>-->
                </div>

              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
        <el-dialog :visible.sync="dialogVisible">
          <el-form>
            <el-form-item label="处理结果">
              <el-tag v-if="dom.state===1" type="warning">待审核</el-tag>
              <el-tag v-if="dom.state===2" type="success">通过</el-tag>
              <el-tag v-if="dom.state===3" type="danger">不通过</el-tag>
              <el-tag v-if="dom.state===4" type="success">人力窝审核中</el-tag>
              <el-tag v-if="dom.state===5" type="danger">人力窝结算中</el-tag>
              <el-tag v-if="dom.state===6" type="danger">结算成功</el-tag>
              <el-tag v-if="dom.state===7" type="danger">人力窝审核驳回</el-tag>
              <el-tag v-if="dom.state===8" type="danger">人力窝结算异常</el-tag>
              <el-tag v-if="dom.state===9" type="danger">冻结</el-tag>
              <el-tag v-if="dom.state===10" type="warning">解冻</el-tag>
            </el-form-item>
          </el-form>
          <el-button type="primary" @click="innerVisible = true">更新</el-button>
          <br><br>
        </el-dialog>
        <el-dialog title="处理" :visible.sync="innerVisible" append-to-body center>
          <el-form>
            <el-form-item label="状态">
              <el-radio-group v-model="dom.state">
                <el-radio v-if="roleId==79" label="2">通过</el-radio>
                <el-radio v-if="roleId==2||roleId==79" label="3">不通过</el-radio>
                <el-radio v-if="roleId==2" label="4">人力窝结算</el-radio>
                <el-radio v-if="roleId==1" label="9">冻结</el-radio>
                <el-radio v-if="roleId==1" label="10">解冻</el-radio>
              </el-radio-group>
            </el-form-item>
            <br>
            <el-form-item v-if="dom.state==='2'" label="备注">
              <el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="dom.remark"> </el-input>
            </el-form-item>
            <el-form-item v-if="dom.state==='3'" label="未通过原由">
              <el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="dom.remark"> </el-input>
            </el-form-item>
            <el-form-item v-if="dom.state==='9'" label="冻结原由">
              <el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="dom.remark"> </el-input>
            </el-form-item>
            <el-form-item v-if="dom.state==='4'" label="备注">
              <el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="dom.remark"> </el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="throttledClick">确定</el-button>
          </div>
        </el-dialog>
      </el-tabs>
    </div>
    <el-dialog
        title="导出出账明细："
        :visible.sync="czmxDialogFlag"
        width="80%">
      <el-form ref="form">
        <el-row>
          <el-col :span="9">
            <el-form-item label="提现时间">
              <el-date-picker v-model="dcmxDays" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="到账时间">
              <el-date-picker v-model="dzDays" type="daterange" unlink-panels :picker-options="dzPickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item  label="所属门店" >
              <el-select style="width:220px" filterable v-model="dcmxQuery.storeId" clearable placeholder="请选择所属门店">
                <el-option v-for="item in storeOptions"
                           :key="item.id" :label="item.storeName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3" style="margin-top: 30px;margin-left: 50px">
            <el-button type="info"
                       style="margin-bottom: 15px"
                       @click="exportPtjWithdrawalPaymentOut"
                       icon="el-icon-download">导出
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
    <el-dialog
        title="提现来源："
        :visible.sync="dialog"
        width="80%">
      <div style="display: flex">
        <div>订单总金额：{{sumAmount||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <div>订单实付总金额：{{sumRealPay||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <div>结算总金额：{{sumSettle||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <div>总服务费：{{sumServiceCharge||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <div>总计：{{sumServiceMoney||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <el-button type="info"
                   style="margin-bottom: 15px"
                   @click="download"
                   icon="el-icon-download">导出
        </el-button>
      </div>
      <el-table  :data="orderList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">

        <el-table-column
            prop="billNo"
            label="订单编号"
            width="200"
        ></el-table-column>
        <el-table-column
            prop="productName"
            label="服务项目"
            width="189"
        ></el-table-column>
        <el-table-column
            prop="account"
            label="会员账号"
            width="200"
        ></el-table-column>
        <el-table-column
            prop="paySettlementTime"
            label="结算时间"
            width="200"
        ></el-table-column>
        <el-table-column
            prop="realTotalAmount"
            label="订单金额"
            width="150"
        ></el-table-column>
        <el-table-column
            prop="amount"
            label="实付金额"
            width="150"
        ></el-table-column>
        <el-table-column
            prop="realWage"
            label="结算金额"
            width="150"
        ></el-table-column>
        <el-table-column
            prop="serviceCharge"
            label="服务费"
            width="150"
        ></el-table-column>
        <el-table-column
            prop="sumServiceMoney"
            label="总计"
            width="150"
        ></el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
        title="人力窝预充："
        :visible.sync="dialogPrecharge"
        width="80%">
      <div style="display: flex">
        <div>累计预充金额：{{prechargeData.sumPrechargeMoeny||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <div>预充余额：{{prechargeData.surplusPrechargeMoeny||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <el-input style="width: 10%" v-model="addPrechargeMoeny" placeholder="请输入预充金额" type="number"></el-input>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <template v-if="roleId==57">
        <el-popconfirm title="确定增加预充金额？" @confirm="addRlwPrechargeMoney">
        <el-button type="primary"
                   slot="reference"
                   style="margin-bottom: 15px"
                   icon="el-icon-circle-plus-outline">增加
        </el-button>
        </el-popconfirm>
        </template>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <el-button type="success"
                   style="margin-bottom: 15px"
                   @click="getRlwPrechargeLog"
                   icon="el-icon-search">刷新
        </el-button>
        <el-button type="info"
                   style="margin-bottom: 15px"
                   @click="exportRlwPrechargeLog"
                   icon="el-icon-download">导出
        </el-button>
      </div>
      <el-table  :data="prechargeList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
        <el-table-column
            prop="createTime"
            label="操作时间"
            width="200"
        ></el-table-column>
        <el-table-column
            prop="oldMoney"
            label="原金额"
            width="170"
        ></el-table-column>
        <el-table-column
            prop="changeMoney"
            label="变更金额"
            width="160"
        ></el-table-column>
        <el-table-column
            prop="nowMoney"
            label="变更后金额"
            width="170"
        ></el-table-column>
        <el-table-column
            prop="serialNumber"
            label="交易流水号"
            width="150"
        ></el-table-column>
        <el-table-column
            prop="operatorPeople"
            label="操作人"
            width="150"
        ></el-table-column>
        <el-table-column
            prop="type"
            label="类型"
            width="120"
        >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.type===1" type="success">充值</el-tag>
            <el-tag v-if="scope.row.type===2" type="danger">扣减</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="bPageInfo.total"
              @on-change="bOnChange"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="bOnPageSizeChange"
              :page-size="bPageInfo.size"/>
      </div>
    </el-dialog>


    <el-dialog
        title="异地产品结算说明："
        :visible.sync="dialogBalance"
        width="80%">
      <div style="display: flex">
        <el-button type="info"
                   style="margin-bottom: 15px"
                   @click="excelDownload"
                   icon="el-icon-download">下载异地产品结算xls
        </el-button>
      </div>
      <el-image
          style="width: 900px; height: 600px;margin-left: 15%"
          src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/17140341669721714034103716.jpg"
          :preview-src-list="srcList">
      </el-image>
      <el-image
          style="width: 900px; height: 600px;margin-left: 15%"
          src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1734072322603企业微信截图_17340721291863.png"
          :preview-src-list="srcList">
      </el-image>

    </el-dialog>

    <el-link :href="hrefUrl" target="_blank" id="elLink"/>

    <el-dialog
        title="人力窝打款状态查询："
        :visible.sync="dialogCCB"
        width="80%">
      <div style="display: flex">
       <span style="color:red">系统会半小时轮询查询打款进度,如需手动查询，请点击“手动查询”按钮。</span>
        <el-button type="primary" @click="queryRlwPayment">手动查询</el-button>
      </div>
      <el-table  :data="ccbCallBackList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
        <el-table-column
            prop="createTime"
            label="请求时间"
        ></el-table-column>
        <el-table-column
            prop="respCode"
            label="响应码"
        ></el-table-column>
        <el-table-column
            prop="respDesc"
            label="响应结果描述"
        ></el-table-column>
        <el-table-column
            prop="state"
            label="响应状态"
        >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.state===0" type="warning">审核中</el-tag>
            <el-tag v-if="scope.row.state===1" type="success">结算中</el-tag>
            <el-tag v-if="scope.row.state===2" type="success">结算成功</el-tag>
            <el-tag v-if="scope.row.state===3" type="success">结算驳回</el-tag>
            <el-tag v-if="scope.row.state===4" type="success">结算异常</el-tag>
          </template>
        </el-table-column>
        <el-table-column
            prop="settlementStatusDesc"
            label="异常说明"
        ></el-table-column>
        <el-table-column
            prop="rejectReason"
            label="驳回原因"
        ></el-table-column>

      </el-table>
    </el-dialog>


  </div>
</template>

<script>
import moment from "moment";
import log from "echarts/src/scale/Log";
export default {
  name: "register",
  data() {
    return {
      addPrechargeMoeny: 0.00,
      srcList: [
        'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/17140341669721714034103716.jpg',
      ],
      employeeId: localStorage.getItem('id'),
      toDayReceivedSumMoney: 0.00,
      dialogCCB:false,
      dialogPrecharge:false,
      dialogBalance:false,
      ccbCallBackList:[],
      logList: [],
      imgModal: false,
      handler: [],
      txData: [],
      invoiceDialog: false,
      czmxDialogFlag: false,
      orderList: [],
      imageUrl: '',
      roleId: localStorage.getItem('roleId'),
      hrefUrl: "",
      blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
      dialog: false,
      getOrderObj: {
        storeId: null,
        id: null,
      },
      sumSettle: 0.00,
      sumAmount: 0.00,
      sumRealPay: 0.00,
      sumServiceCharge: 0.00,
      sumServiceMoney: 0.00,
      dialogVisible: false,
      withdrawalId: null,
      innerVisible: false,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      bPageInfo: {total: 10, size: 10, current: 1, pages: 1},
      days: [],
      queryDays: [],
      dcmxDays: [],
      dzDays: [],
      prechargeData: {},
      prechargeList: [],
      form: {
        state: '',
        startTime: '',
        endTime: '',
        dzStartTime: '',
        dzEndTime: '',
        storeName: '',
        auditingSchedule: '',
        roleId: localStorage.getItem('roleId'),
        employeeId: localStorage.getItem('id'),
        realName: '',
        serialNumber: '',
        current: 1,
        size: 10
      },
      bForm: {
        current: 1,
        size: 10
      },
      dom: {
        state: null,
        employeeId: localStorage.getItem("id"),
        id: null,
        remark: null,
      },
      options: [],
      dcmxQuery: {
        storeId: null,
        startTime: '',
        endTime: '',
        dzStartTime: '',
        dzEndTime: '',
      },
      typeOptions: [{
        value:  '1',
        label: '交付'
      },{
        value:  '2',
        label: '会计'
      },{
        value:  '3',
        label: '出纳'
      }],
      storeOptions: [],
      dzPickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },

  created() {
    let roleId = localStorage.getItem("roleId")
    let employeeId = localStorage.getItem("id")
    let obj = {
      value:  '1',
      label: '待审核'
    }
      this.options.push(obj)
      obj = {}
      obj.value = '2'
      obj.label='通过'
      this.options.push(obj)
      obj = {}
      obj.value = '3'
      obj.label='不通过'
      this.options.push(obj)
      // obj = {}
      // obj.value = '2'
      // obj.label='通过'
      // this.options.push(obj)
      obj = {}
      obj.value = '4'
      obj.label='人力窝审核中'
      this.options.push(obj)
      obj = {}
      obj.value = '5'
      obj.label='人力窝结算中'
      this.options.push(obj)
      obj = {}
      obj.value = '6'
      obj.label='人力窝结算成功'
      this.options.push(obj)
      obj = {}
      obj.value = '7'
      obj.label='人力窝审核驳回'
      this.options.push(obj)
      obj = {}
      obj.value = '8'
      obj.label='人力窝结算异常'
      this.options.push(obj)
    obj = {}
    obj.value = '9'
    obj.label='冻结'
    this.options.push(obj)
    obj = {}
    obj.value = '10'
    obj.label='解冻'
    this.options.push(obj)
    this.getData();
    // 创建节流后的函数，并存储在另一个属性中
    this.throttledClick = this.createThrottledFunction(this.auditingPartTimeJobWithdrawal, 500);
  },
  methods: {
    throttle(func, wait) {
      let timeout;
      return function() {
        const context = this, args = arguments;
        if (!timeout) {
          func.apply(context, args);
          timeout = setTimeout(() => {
            timeout = null;
          }, wait);
        }
      };
    },
    createThrottledFunction(fn, wait) {
      return this.throttle(fn.bind(this), wait);
    },
    addRlwPrechargeMoney(){
      if(this.addPrechargeMoeny<=0){
        return  this.$message({message: '请输入正确的金额', type: 'error'});
      }
      let obj = {
        changeMoney: this.addPrechargeMoeny,
        operatorPeople: localStorage.getItem('id'),
        type:1
      }
      this.$postData("addRlwPrechargeMoney", obj).then(res => {
        if (res.code === 0) {
          this.$message({message: res.data, type: 'success'});
        } else {
          this.$message({message: res.msg, type: 'warning'});
        }
        this.addPrechargeMoeny = 0.00
        this.getRlwPrechargeLog();
      })
    },
    getDialogPrecharge(){
      this.getRlwPrechargeLog()
      this.dialogPrecharge = true
    },
    getRlwPrechargeLog(){
      this.$getData("getRlwPrechargeLog", this.bForm, {}).then(res => {
        if (res.code == 0) {
          this.prechargeData = res.data
          this.prechargeList = res.data.pageList.records
          this.bPageInfo.current = res.data.pageList.current;
          this.bPageInfo.size = res.data.pageList.size;
          this.bPageInfo.total = res.data.pageList.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    },
    excelDownload(){
      this.hrefUrl = "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1714110579632外地产品结算价格表2024.04.25.xlsx"
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    exportRlwPrechargeLog(){
      this.hrefUrl = "https://biapi.xiaoyujia.com/partTimeJobBalance/exportRlwPrechargeLog"
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    exportPtjWithdrawalPaymentOut(){
      if (this.dcmxDays != null && this.dcmxDays.length > 0) {
        this.dcmxQuery.startTime = moment(this.dcmxDays[0]).format("YYYY-MM-DD");
        this.dcmxQuery.endTime = moment(this.dcmxDays[1]).format("YYYY-MM-DD")
      }else{
        this.dcmxQuery.startTime = ""
        this.dcmxQuery.endTime = ""
      }
      if (this.dzDays != null && this.dzDays.length > 0) {
        this.dcmxQuery.dzStartTime = moment(this.dzDays[0]).format("YYYY-MM-DD");
        this.dcmxQuery.dzEndTime = moment(this.dzDays[1]).format("YYYY-MM-DD")
      }else{
        this.dcmxQuery.dzStartTime = ""
        this.dcmxQuery.dzEndTime = ""
      }
      let url = "https://biapi.xiaoyujia.com/partTimeJobBalance/exportPtjWithdrawalPaymentOut"
      if (this.dcmxQuery.storeId){
        url = url+"?storeId="+this.dcmxQuery.storeId
      }
      if (this.dcmxQuery.startTime&&!this.dcmxQuery.storeId){
        url = url+"?startTime="+this.dcmxQuery.startTime+"&endTime="+ this.dcmxQuery.endTime
      }else if(this.dcmxQuery.startTime&&this.dcmxQuery.storeId){
        url = url+"&startTime="+this.dcmxQuery.startTime+"&endTime="+ this.dcmxQuery.endTime
      }
      if (this.dcmxQuery.dzStartTime&&!this.dcmxQuery.storeId&&!this.dcmxQuery.startTime){
        url = url+"?dzStartTime="+this.dcmxQuery.dzStartTime+"&dzEndTime="+ this.dcmxQuery.dzEndTime
      }else if(this.dcmxQuery.dzStartTime&&(this.dcmxQuery.storeId||this.dcmxQuery.startTime)){
        url = url+"&dzStartTime="+this.dcmxQuery.dzStartTime+"&dzEndTime="+ this.dcmxQuery.dzEndTime
      }
      this.hrefUrl = url
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    dcczmx(){
      this.$getData("getAllFranchiseStore", {type: 1}, {}).then(res => {
        if (res.code == 0) {
          this.storeOptions = res.data
        } else {
          this.$message.error("查询筛选门店失败!");
        }
      })
      this.czmxDialogFlag = true
    },
    queryRlwPayment() {
      this.$getData("queryRlwPayment", {withdrawalId:this.withdrawalId}, {}).then(res => {
        if (res.code == 0) {
          this.ccbCallBackList = res.data
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    },
    exportBasisPartTimeJobWithdrawal(){
      this.form.startTime = '';
      this.form.endTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      if (this.queryDays != null && this.queryDays.length > 0) {
        this.form.dzStartTime = moment(this.queryDays[0]).format("YYYY-MM-DD");
        this.form.dzEndTime = moment(this.queryDays[1]).format("YYYY-MM-DD")
      }
      this.hrefUrl = "https://biapi.xiaoyujia.com/partTimeJobBalance/exportBasisPartTimeJobWithdrawal?state="+this.form.state+
          "&startTime="+this.form.startTime+"&endTime="+this.form.endTime+"&storeName="+this.form.storeName+
          "&roleId="+this.roleId+"&realName="+this.form.realName+"&serialNumber="+this.form.serialNumber+
          "&dzStartTime=" + this.form.dzStartTime+"&dzEndTime=" + this.form.dzEndTime
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    exportPartTimeJobWithdrawal(){
      this.form.startTime = '';
      this.form.endTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      if (this.queryDays != null && this.queryDays.length > 0) {
        this.form.dzStartTime = moment(this.queryDays[0]).format("YYYY-MM-DD");
        this.form.dzEndTime = moment(this.queryDays[1]).format("YYYY-MM-DD")
      }
      this.hrefUrl = "https://biapi.xiaoyujia.com/partTimeJobBalance/exportPartTimeJobWithdrawal?state="+this.form.state+
          "&startTime="+this.form.startTime+"&endTime="+this.form.endTime+"&storeName="+this.form.storeName+
          "&roleId="+this.roleId+"&realName="+this.form.realName+"&serialNumber="+this.form.serialNumber+
          "&dzStartTime=" + this.form.dzStartTime+"&dzEndTime=" + this.form.dzEndTime
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    download() {
      if(this.orderList.length<=0){
        return this.$message.error("未获取到提现来源信息，无法导出！");
      }
      this.hrefUrl = "https://biapi.xiaoyujia.com/partTimeJobBalance/exportWithdrawalOrderLog?withdrawalId="+this.withdrawalId
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    selectOrderList(id){
      this.dialog = true;
      this.withdrawalId = id
      this.$getData("getPartTimeJobWithdrawalLogData", {withdrawalId: id}, {}).then(res => {
        if (res.code == 0) {
          this.orderList = res.data.balanceVoList
          this.sumSettle = res.data.sumSettle
          this.sumRealPay = res.data.sumRealPay
          this.sumAmount = res.data.sumAmount
          this.sumServiceMoney = res.data.sumServiceMoney
          this.sumServiceCharge = res.data.sumServiceCharge
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    },
    getRlwQuestLog(id) {
      this.dialogCCB = true;
      this.withdrawalId = id
      this.$getData("getRlwQuestLog", {withdrawalId:id}, {}).then(res => {
        if (res.code == 0) {
          this.ccbCallBackList = res.data
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    },
    showRow(id, state) {
      this.dialogVisible = true;
      this.dom.state = state;
      this.dom.id = id;
    },
    auditingPartTimeJobWithdrawal(){
      if (this.dom.state==3&&!this.dom.remark){
        return this.$message({message: "请填写缘由", type: 'error'});
      }
      this.innerVisible = false;
      this.$postData("auditingPartTimeJobWithdrawal", this.dom).then(res => {
        if (res.code === 0) {
          this.$message({message: res.data, type: 'success'});
        } else {
          this.$message({message: res.msg, type: 'warning'});
        }
        this.dom.remark = ""
        this.getData();
      })
      this.dialogVisible = false;
    },
    // 页码大小
    onPageSizeChange(size) {
      this.form.size = size;
      this.getData();
    },
    // 页码大小
    bOnPageSizeChange(size) {
      this.bForm.size = size;
      this.getRlwPrechargeLog();
    },
    onChange(index) {
      this.form.current = index;
      this.getData();
    },
    bOnChange(index) {
      this.bForm.current = index;
      this.getRlwPrechargeLog();
    },
    getData() {
      this.form.startTime = '';
      this.form.endTime = '';
      this.form.dzStartTime = '';
      this.form.dzEndTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      if (this.queryDays != null && this.queryDays.length > 0) {
        this.form.dzStartTime = moment(this.queryDays[0]).format("YYYY-MM-DD");
        this.form.dzEndTime = moment(this.queryDays[1]).format("YYYY-MM-DD")
      }
      this.$getData("getPartTimeJobWithdrawalData", this.form, {}).then(res => {
        if (res.code == 0) {
          this.logList = res.data.voList.records;
          this.toDayReceivedSumMoney = res.data.toDayReceivedSumMoney;
          this.pageInfo.current = res.data.voList.current;
          this.pageInfo.size = res.data.voList.size;
          this.pageInfo.total = res.data.voList.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    }
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
