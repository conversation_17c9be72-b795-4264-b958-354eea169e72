<template style="background-color: #000">

    <!--            <el-tabs type="card" :stretch="true" >-->
    <!--                <el-tab-pane label="基本信息">-->
    <Row>
        <Col span="24">
            <el-form ref="dom" :model="dom" :rules="ruleValidate">
                <Row>
                    <Col span="24">
                        <el-collapse v-model="activeNames">
                            <el-collapse-item title="合同人员信息" name="1">
                                <template slot="title">
                                    <i class="header-icon el-icon-user"></i> - 合同人员信息 -
                                </template>
                                <Col span="12">
                                    <el-form-item prop="memberName">
                                        <div class="label-name">合同编号:</div>
                                        <el-input placeholder="请输入合同编号" v-model="dom.no" style="width: 70%">
                                        </el-input>
                                    </el-form-item>

                                    <el-form-item prop="agentName">
                                        <div class="label-name">所属经纪人:</div>
                                        <el-autocomplete
                                                style="width: 70%"
                                                v-model="dom.agentName"
                                                :fetch-suggestions="querySearchAsync"
                                                placeholder="请输入内容"
                                                @select="handleSelect"
                                                :trigger-on-focus="false">
                                        </el-autocomplete>
                                    </el-form-item>

                                    <el-form-item prop="memberName">
                                        <div class="label-name">客户名:</div>
                                        <el-input placeholder="请输入客户真实姓名" v-model="dom.memberName" style="width: 70%">
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item prop="status">
                                        <div class="label-name">合同状态:</div>
                                        <el-select v-model="dom.status" clearable placeholder="请选择"
                                                   v-show="dom.status!='2'" style="width: 70%;">
                                            <el-option
                                                    v-for="item in states"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value">
                                            </el-option>
                                        </el-select>
                                        <span v-show="dom.status=='2'">已完成</span>
                                    </el-form-item>
                                </Col>
                                <Col span="12">
                                    <el-form-item prop="baomuName">
                                        <div class="label-name">关联保姆:</div>
                                        <el-select
                                                style="width: 70%"
                                                v-model="baomuno"
                                                filterable
                                                remote
                                                reserve-keyword
                                                placeholder="请输入保姆姓名"
                                                :remote-method="remoteMethod"
                                                @change="changbaomu"
                                                :loading="employeeloading">
                                            <el-option
                                                    v-for="item in employeeoptions"
                                                    :key="item.id"
                                                    :label="item.label"
                                                    :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item prop="baomuName">
                                        <div class="label-name">关联用户:</div>
                                        <el-select
                                                style="width: 70%"
                                                v-model="menberp"
                                                filterable
                                                remote
                                                reserve-keyword
                                                placeholder="输入用户手机查找用户"
                                                :remote-method="memberremoteMethod"
                                                @change="changmember"
                                                :loading="memberloading">
                                            <el-option
                                                    v-for="item in memberoptions"
                                                    :key="item.id"
                                                    :label="item.label"
                                                    :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item prop="baomuName">
                                        <div class="label-name">保姆名:</div>
                                        <el-input placeholder="请输入保姆名查找保姆" v-model="dom.employeeName"
                                                  style="width: 70%">
                                        </el-input>
                                    </el-form-item>


                                    <el-form-item prop="client">
                                        <div class="label-name">委托人:</div>
                                        <el-input placeholder="委托人" v-model="dom.client" style="width: 70%">

                                        </el-input>
                                    </el-form-item>
                                </Col>
                            </el-collapse-item>
                            <el-collapse-item title="" name="2">
                                <template slot="title">
                                    <i class="el-icon-bank-card"></i> - 合同信息 -
                                </template>
                                <Col span="12">

                                    <el-form-item prop="servicePay">
                                        <div class="label-name">劳动报酬:</div>
                                        <el-input placeholder="劳动报酬" v-model="dom.servicePay" style="width: 70%">

                                            <template slot="append">元</template>
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item prop="inPay">
                                        <div class="label-name">中介费（大写）:</div>
                                        <el-input placeholder="中介服务费（大写）" v-model="dom.inPay" style="width: 70%">

                                        </el-input>
                                    </el-form-item>
                                    <el-form-item prop="serviceType">
                                        <div class="label-name">服务方式:</div>
                                        <el-input placeholder="服务方式" v-model="dom.serviceType" style="width: 70%">
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item prop="insure">
                                        <div class="label-name">保险:</div>
                                        <!--                                                <el-input placeholder="保险" v-model="dom.insure" style="width: 70%" >-->
                                        <!--                                                </el-input>-->
                                        <el-select v-model="insures" multiple placeholder="请选择" style="width: 70%">
                                            <el-option
                                                    v-for="item in insureOptions"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item prop="insureRemark">
                                        <div class="label-name">保险（其他）:</div>
                                        <el-input placeholder="保险（其他）" v-model="dom.insureRemark" style="width: 70%">
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item prop="insureRemark">
                                        <div class="label-name">保费承担人:</div>
                                        <el-input placeholder="insureBy" v-model="dom.insureBy" style="width: 70%">
                                        </el-input>
                                    </el-form-item>


                                </Col>
                                <Col span="12">

                                    <el-form-item prop="serviceContent">
                                        <div class="label-name">服务内容:</div>
                                        <!--                                                <el-input placeholder="服务内容" v-model="dom.serviceContent"  style="width: 70%">-->
                                        <!--                                                </el-input>-->
                                        <el-select v-model="serviceTypes" multiple placeholder="请选择" style="width: 70%">
                                            <el-option
                                                    v-for="item in serviceTypeOptions"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item prop="serviceContentRemark">
                                        <div class="label-name">服务内容（其他）:</div>
                                        <el-input placeholder="服务内容（其他）" v-model="dom.serviceContentRemark"
                                                  style="width: 70%">

                                        </el-input>
                                    </el-form-item>
                                    <el-form-item prop="serviceNum">
                                        <div class="label-name">服务次数:</div>
                                        <el-input placeholder="服务次数（/年）" v-model="dom.serviceNum" style="width: 70%">
                                            <template slot="append">/年</template>
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item prop="serviceMax">
                                        <div class="label-name">服务最大天数:</div>
                                        <el-input placeholder="服务最大天数" v-model="dom.serviceMax" style="width: 70%">
                                            <template slot="append">/天</template>
                                        </el-input>
                                    </el-form-item>


                                    <el-form-item prop="clientDate">
                                        <div class="label-name">服务开始时间:</div>
                                        <el-date-picker
                                                style="width: 70%"
                                                v-model="dom.serviceStarDate"
                                                type="date"
                                                value-format="yyyy-MM-dd HH:mm:ss"
                                                placeholder="服务开始时间">
                                        </el-date-picker>
                                    </el-form-item>


                                    <el-form-item prop="clientDate">
                                        <div class="label-name">服务结束时间:</div>
                                        <el-date-picker
                                                style="width: 70%"
                                                v-model="dom.serviceEndDate"
                                                type="date"
                                                value-format="yyyy-MM-dd HH:mm:ss"
                                                placeholder="服务结束时间">
                                        </el-date-picker>
                                    </el-form-item>


                                </Col>
                            </el-collapse-item>
                            <el-collapse-item title="效率 Efficiency" name="3">
                                <template slot="title">
                                    <i class="el-icon-office-building"></i> - 服务对象基本情况 -
                                </template>
                                <Col span="12">

                                    <el-form-item prop="homeDetail">
                                        <div class="label-name">服务对象户型:</div>
                                        <el-input placeholder="服务对象户型" v-model="dom.homeDetail" style="width: 70%">
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item prop="homeArea">
                                        <div class="label-name">面积:</div>
                                        <el-input placeholder="面积" v-model="dom.homeArea" style="width: 70%">
                                            <template slot="append">平方</template>
                                        </el-input>
                                    </el-form-item>

                                    <el-form-item prop="homeHealth">
                                        <div class="label-name">健康情况:</div>
                                        <el-input placeholder="健康情况" v-model="dom.homeHealth" style="width: 70%">
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item prop="homeRemark">
                                        <div class="label-name">其他:</div>
                                        <el-input placeholder="其他" v-model="dom.homeRemark" style="width: 70%">
                                        </el-input>
                                    </el-form-item>
                                </Col>
                                <Col span="12">

                                    <el-form-item prop="homeAllPerson">
                                        <div class="label-name">总家庭成员:</div>
                                        <el-input placeholder="总家庭成员" v-model="dom.homeAllPerson" style="width: 70%"
                                                  type="number">
                                            <template slot="append">人</template>
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item prop="homePerson">
                                        <div class="label-name">家庭成员（成年）:</div>
                                        <el-input placeholder="家庭成员（成年）" v-model="dom.homePerson" style="width: 70%"
                                                  type="number">
                                            <template slot="append">人</template>
                                        </el-input>
                                    </el-form-item>

                                    <el-form-item prop="homeChildren">
                                        <div class="label-name">家庭成员（孩童）:</div>
                                        <el-input placeholder="家庭成员（孩童）" v-model="dom.homeChildren" style="width: 70%"
                                                  type="number">
                                            <template slot="append">人</template>
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item prop="homePets">
                                        <div class="label-name">宠物:</div>
                                        <el-input placeholder="宠物" v-model="dom.homePets" style="width: 70%"
                                                  type="number">
                                            <template slot="append">只</template>
                                        </el-input>
                                    </el-form-item>

                                </Col>
                            </el-collapse-item>
                            <el-collapse-item title="其他合同信息" name="4">
                                <template slot="title">
                                    <i class="el-icon-guide"></i> - 其他合同信息 -
                                </template>

                                <Col span="12">

                                    <el-form-item prop="clientDate">
                                        <div class="label-name">甲方签署日期:</div>
                                        <el-date-picker
                                                readonly
                                                style="width: 70%"
                                                v-model="dom.memberSignDate"
                                                type="date"
                                                value-format="yyyy-MM-dd HH:mm:ss"
                                                placeholder="甲方（客户）签署日期">
                                        </el-date-picker>
                                    </el-form-item>
                                    <el-form-item prop="memberCardId">
                                        <div class="label-name">甲方身份证:</div>
                                        <el-input placeholder="甲方（客户）身份证" v-model="dom.memberCardId" style="width: 70%">
                                        </el-input>
                                    </el-form-item>


                                    <el-form-item prop="memberPhone">
                                        <div class="label-name">甲方（客户）电话:</div>
                                        <el-input placeholder="甲方（客户）电话" v-model="dom.memberPhone" style="width: 70%"
                                                  maxlength="11">
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item prop="memberAdress">
                                        <div class="label-name">甲方（客户）地址:</div>
                                        <el-input placeholder="甲方（客户）地址" v-model="dom.memberAdress" style="width: 70%">
                                        </el-input>
                                    </el-form-item>


                                </Col>
                                <Col span="12">
                                    <el-form-item prop="employeeSignDate">
                                        <div class="label-name">乙方签署日期:</div>
                                        <el-date-picker
                                                readonly
                                                style="width: 70%"
                                                v-model="dom.employeeSignDate"
                                                type="date"
                                                value-format="yyyy-MM-dd HH:mm:ss"
                                                placeholder="乙方（保姆）签署日期">
                                        </el-date-picker>
                                    </el-form-item>
                                    <el-form-item prop="employeeCarId">
                                        <div class="label-name">乙方身份证:</div>
                                        <el-input placeholder="乙方（保姆）身份证" v-model="dom.employeeCarId"
                                                  style="width: 70%">
                                        </el-input>
                                    </el-form-item>

                                    <el-form-item prop="employeePhone">
                                        <div class="label-name">乙方（保姆）电话:</div>
                                        <el-input placeholder="乙方（保姆）电话" v-model="dom.employeePhone" style="width: 70%"
                                                  maxlength="11">
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item prop="employeeAdress">
                                        <div class="label-name">乙方（保姆）地址:</div>
                                        <el-input placeholder="乙方（保姆）地址" v-model="dom.employeeAdress"
                                                  style="width: 70%">
                                        </el-input>
                                    </el-form-item>


                                    <el-form-item prop="clientDate">
                                        <div class="label-name">委托代理人签字日期:</div>
                                        <el-date-picker
                                                style="width: 70%"
                                                v-model="dom.clientDate"
                                                type="date"
                                                value-format="yyyy-MM-dd HH:mm:ss"
                                                placeholder="委托代理人签字日期">
                                        </el-date-picker>
                                    </el-form-item>


                                </Col>
                            </el-collapse-item>
                        </el-collapse>
                    </Col>


                </Row>

                <div style="margin-right: 100px;float: right">
                    <Button type="primary" @click="save('dom')">确定</Button>
                    <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                </div>
            </el-form>
        </Col>
    </Row>
    <!--                </el-tab-pane>-->
    <!--                <el-tab-pane label="配置管理">配置管理</el-tab-pane>-->
    <!--            </el-tabs>-->

</template>


<script>

    export default {
        props: ['model'],
        data() {
            return {
                states: [{
                    value: '0',
                    label: '暂存'
                }, {
                    value: '1',
                    label: '生效中'
                }, {
                    value: '2',
                    label: '已完成'
                }, {
                    value: '3',
                    label: '补签单'
                }, {
                    value: '4',
                    label: '已过期(未续签)'
                }, {
                    value: '5',
                    label: '合同终止'
                }, {
                    value: '99',
                    label: '作废单'
                }, {
                    value: '100',
                    label: '暂存下户'
                }],
                insures: [],
                // insureOptions: [{
                //     value: '《家政人员人身意外险》',
                //     label: '《家政人员人身意外险》'
                // }, {
                //     value: '《家政责任险》',
                //     label: '《家政责任险》'
                // },],
                insureOptions:[{
                    value:'《家政人员雇主责任险》',
                    label: '《家政人员雇主责任险》'
                },{
                    value:'《家政服务公众责任险》',
                    label: '《家政服务公众责任险》'
                },],
                serviceTypes: [],
                serviceTypeOptions: [{
                    value: '家庭卫生打扫',
                    label: '家庭卫生打扫'
                }, {
                    value: '婴、幼儿照护',
                    label: '婴、幼儿照护'
                }, {
                    value: '婴幼儿教育',
                    label: '婴幼儿教育'
                }, {
                    value: '产妇与新生儿护理',
                    label: '产妇与新生儿护理'
                }, {
                    value: '病人陪护',
                    label: '病人陪护'
                }, {
                    value: '计时服务',
                    label: '计时服务'
                }, {
                    value: '家庭餐制作',
                    label: '家庭餐制作'
                },],
                menberp: null,
                memberloading: false,
                memberoptions: [],
                baomuno: null,
                employeeloading: false,
                employeeoptions: [],
                activeNames: ['1', '2'],
                dom: this.model,
                restaurants: [],
                list: [],
                baomuModel: {
                    RealName: null,
                    No: null,
                },
                formItem: {
                    No: null,
                    RealName: null,
                    Phone: null,
                    Address: null,
                },

                ruleValidate: {
                    employeename: [
                        {required: true, message: '请输入', trigger: 'change'}
                    ],
                    membername: [
                        {required: true, message: '请选择', trigger: 'change'}
                    ],
                    agentname: [
                        {required: true, message: '请输入', trigger: 'change'}
                    ],
                    no: [
                        {required: true, message: '请输入', trigger: 'change'}
                    ],

                },
            }
        },
        components: {},
        created: function () {
            this.baomuno = this.dom.realName;
            this.menberp = this.dom.name;
            if (this.dom.insure != null) {
                this.insures = this.StringToArray(this.dom.insure, ',')
            }
            if (this.dom.serviceContent != null) {
                this.serviceTypes = this.StringToArray(this.dom.serviceContent, ',')
            }
        },
        methods: {
            changbaomu(item) {
                this.dom.employeeId = item;
                this.employeeoptions.forEach(v => {
                    if (v.value == item) {
                        this.dom.employeeName = v.label;
                        this.dom.employeeCarId = v.idcard;
                        this.dom.employeePhone = v.employeephone;
                        this.dom.employeeAdress = v.hometown;
                    }
                })

            },
            changmember(item) {
                this.dom.memberId = item;
                this.memberoptions.forEach(v => {
                    if (v.value == item) {
                        this.dom.memberPhone = v.bindTel;
                    }
                })
            },
            remoteMethod(query) {
                this.employeeoptions = [];

                if (query !== '') {
                    this.employeeloading = true;
                    this.$getData("get_baomubyno", {no: query, storeId: localStorage.getItem("storeId")}).then(res => {
                        if (res.status == 200) {
                            this.employeeloading = false;
                            res.data.forEach((item, index, arr) => {
                                var a = {};
                                a.id = item.id;
                                a.value = item.id;
                                a.label = item.realName;
                                a.idcard = item.idcard;
                                a.employeephone = item.phone;
                                a.hometown = item.hometown;
                                this.employeeoptions.push(a);
                            });
                        } else {
                            this.$message.error("修改失败，" + res.msg);
                        }
                    })
                } else {
                    this.employeeoptions = [];
                }
            },
            memberremoteMethod(query) {
                this.memberoptions = [];
                if (query.length < 8) {
                    return
                }
                if (query !== '') {
                    this.memberloading = true;
                    this.$postUrl("get_memberbyBindTel", query, null, {}).then(res => {
                        if (res.status == 200) {
                            this.memberloading = false;
                            res.data.forEach((item, index, arr) => {
                                var a = {};
                                a.id = item.id;
                                a.value = item.id;
                                a.label = item.name;
                                a.bindTel = item.bindTel;
                                this.memberoptions.push(a);
                            });
                        } else {
                            this.$message.error("修改失败，" + res.msg);
                        }
                    })
                } else {
                    this.memberoptions = [];
                }
            },
            save(name) {
                this.dom.serviceContent = null;
                    this.serviceTypes.forEach(v => {
                        if (this.dom.serviceContent == null) {
                            this.dom.serviceContent = v
                        } else {
                            this.dom.serviceContent = this.dom.serviceContent + "," + v
                        }
                    });
                this.dom.insure = null;
                    this.insures.forEach(v => {
                        if (this.dom.insure == null) {
                            this.dom.insure = v
                        } else {
                            this.dom.insure = this.dom.insure + "," + v
                        }
                    });
                this.$refs[name].validate(valid => {
                    if (valid) {
                        if (this.baomuno != null) {
                            if (this.dom.employeeId == null) {
                                return this.$message.error("请选择正确的保姆");
                            }
                        }
                        if (this.menberp != null) {
                            if (this.dom.memberId == null) {
                                return this.$message.error("请选择正确的用户");
                            }
                        }
                        this.$postData("update_contract", this.dom, {}).then(res => {
                            if (res.status == 200) {
                                this.$Message.success('修改成功');
                                this.chooseThisModel();
                            } else {
                                this.$message.error("修改失败，" + res.msg);
                            }
                        });
                        console.log("验证成功")
                    } else {
                        return false;
                    }
                });


            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.dom = null;
                this.$emit('init-choose', "");
            },

            handleSelect(item) {
                this.dom.agentId = item.name;
                console.log(this.dom.agentId)
                console.log(this.list)

            },

            querySearchAsync(queryString, cb) {
                this.restaurants = []
                this.baomuModel.RealName = this.dom.agentName;
                this.baomuModel.realName = this.dom.agentName;
                this.$postData("agent_list", this.baomuModel, {}).then(res => {
                    if (res.status == 200) {
                        this.list = []
                        this.list = res.data;

                        this.list.forEach((item, index, arr) => {
                            var a = {}
                            a.value = item.realName;
                            a.name = item.id;
                            this.restaurants.push(a);
                        });

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                var restaurants = this.restaurants;
                var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants;
                cb(restaurants);
            },
            createStateFilter(queryString) {
                return (state) => {
                    return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                };
            },

            StringToArray(str, substr) {

                var arrTmp = new Array();
                if (substr == "") {
                    arrTmp.push(str);
                    return arrTmp;
                }
                var i = 0, j = 0, k = str.length;
                while (i < k) {
                    j = str.indexOf(substr, i);
                    if (j != -1) {
                        if (str.substring(i, j) != "") {
                            arrTmp.push(str.substring(i, j));
                        }
                        i = j + 1;
                    } else {
                        if (str.substring(i, k) != "") {
                            arrTmp.push(str.substring(i, k));
                        }
                        i = k;
                    }
                }
                return arrTmp;
            },
        },

    }
</script>

<style>
     {
        background: #409eff;
        color: #fff;
    }
</style>
<style scoped>
    .label-name {
        float: left;
        text-align: center;
        width: 20%;
    }

    .ivu-col-span-9 {
        padding-top: 30px;
    }

    .el-input-group {
        width: 80%;
    }

    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

