<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="7">
            <el-form-item label="提交时间">
              <el-date-picker
                  v-model="form.date"
                  align="right"
                  type="date"
                  placeholder="提交时间"
                  :picker-options="pickerOptions">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6" >
            <el-form-item  label="所属门店" >
              <el-select  filterable v-model="form.storeId" clearable placeholder="请选择所属门店">
                <el-option v-for="item in storeOptions"
                           :key="item.id" :label="item.storeName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="提交人">
              <el-input
                  clearable
                  v-model="form.realName"
                  placeholder="请输入提交人"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="3" >
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="填报记录">
          <el-table :data="logList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column prop="createTime" label="提交时间" width="150"></el-table-column>
            <el-table-column prop="storeName" label="门店名称" width="200"></el-table-column>
            <el-table-column prop="realName" label="提交人" width="100"></el-table-column>
            <el-table-column prop="amount" label="今日门店收入" width="100"></el-table-column>
            <el-table-column prop="employeeNum" label="今日招工人数" width="100"></el-table-column>
            <el-table-column prop="needsNum" label="今日线索数" width="100"></el-table-column>
            <el-table-column prop="taskNum" label="今日上户数" width="100"></el-table-column>
            <el-table-column prop="improvesContent" label="今日复盘与感悟" width="200"></el-table-column>
            <el-table-column prop="requirements" label="明日目标与待办" width="200"></el-table-column>
            <el-table-column prop="activityImg" label="现场素材" width="150">
              <template slot-scope="scope">
                <el-tag type="primary" v-if="scope.row.activityImg" @click="seeDepositAccountImg(scope.row)">点击查看</el-tag>
                <el-tag type="danger" v-if="!scope.row.activityImg" >暂无现场素材</el-tag>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-dialog
        title="现场素材："
        :visible.sync="invoiceDialog"
        width="80%">
      <div>
        <div>图片：</div>
        <div v-for="url in dayWorkLogImgList">
          <el-col :span="3">
            <el-image
                style="width: 100px; height: 100px"
                :src="url"
                :preview-src-list="dayWorkLogImgList">
            </el-image>
          </el-col>
        </div>
        <div style="height: 150px"></div>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import moment from "moment";

export default {
  name: "register",
  data() {
    return {
      logList: [],
      invoiceDialog: false,
      storeOptions: [],
      handler: [],
      updateItem: {},
      dayWorkLogImgList: [],
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      form: {
        realName: "",
        storeId: null,
        current: 1,
        date: "",
        size: 10
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            picker.$emit('pick', new Date());
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: '一周前',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', date);
          }
        }]
      },
    }
  },
  created() {
    this.getData();
    this.getAllFranchiseStore();
  },
  methods: {
    getAllFranchiseStore(){
      this.$getData("getAllFranchiseStore", {type: 1}, {}).then(res => {
        if (res.code == 0) {
          this.storeOptions = res.data
        } else {
          this.$message.error("查询筛选门店失败!");
        }
      })
      this.czmxDialogFlag = true
    },
    // 页码大小
    onPageSizeChange(size) {
      this.form.size = size;
      this.getData();
    },
    onChange(index) {
      this.form.current = index;
      this.getData();
    },
    seeDepositAccountImg(item){
      this.dayWorkLogImgList = item.activityImg.split(",")
      this.invoiceDialog = true
    },
    getData() {
      this.$getData("getStoreDayLog", this.form, {}).then(res => {
        if (res.code == 0) {
          this.logList = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    }
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
