<template>
  <div class="table">
    <div class="container">
      <div class="handle-box" style="margin-left: 2rem">
        <Form :label-width="80" style="margin-top: 20px"
              label-position="left">
          <FormItem label="驻场员工" prop="">
            <Input size="large" v-model="dto.employeeName" @on-focus="isService=true"
                   style="width: 180px"
                   placeholder="驻场员工"/>
          </FormItem>
          <FormItem label="订单号" prop="">
            <Input size="large" v-model="dto.billNo"
                   style="width: 180px"
                   placeholder="订单号"/>
            <p>不填写时默认添加全部子订单</p>
          </FormItem>
          <FormItem label="约定金额" prop="">
            <Input size="large" v-model="dto.appointmentAmount"
                   style="width: 100px"
                   placeholder="约定金额"/>
          </FormItem>
          <FormItem label="医社保" prop="">
            <Input size="large" v-model="dto.socialAmount"
                   style="width: 100px"
                   placeholder="医社保"/>
          </FormItem>
          <FormItem label="满勤奖" prop="">
            <Input size="large" v-model="dto.fullAmount"
                   style="width: 100px"
                   placeholder="满勤奖"/>
          </FormItem>
        </Form>
        <Button type="primary" :loading="loading" @click="batchMatter">确定</Button>
      </div>
    </div>
    <Modal v-model="isService" class="Modal" :width="width" :z-index="9999"
           title="驻场员工"
           :mask-closable="false">
      <div class="addBody">
        <employee-quit v-if="isService" @init-choose="initChooseProject"
                       @close-modal="closeCurrModal"></employee-quit>
      </div>
      <div slot="footer">
      </div>
    </Modal>
  </div>
</template>

<script>
import employeeQuit from '@/components/page/site/employeeQuit.vue'

export default {
  props: ['projectId'],
  data() {
    return {
      width: "32%",
      isService: false,
      loading: false,
      dto: {
        employeeNo: null,
        billNo: null,
        name: null,
        projectId: this.projectId,
        state: 1,
        type: 1,
        employeeId: null,
        employeeName: null,
        fullAmount: 100,
        appointmentAmount: null,
        socialAmount: null,
      },
      employeeList: [],
    };
  },
  components: {
    'employeeQuit': employeeQuit
  },
  watch: {
    projectId: {
      handler(newValue, oldValue) {
        this.projectId = newValue;
        this.getProject();
      }
    }
  },
  created() {
  },
  computed: {},
  methods: {
    batchMatter() {
      if (!this.dto.employeeId) {
        this.$message.error("选择人员");
        return
      }if (null===this.dto.appointmentAmount||''===this.dto.appointmentAmount||undefined===this.dto.appointmentAmount) {
        this.$message.error("输入约定金额");
        return
      }
      this.loading = true;
      this.$postData("siteEmployee_insertFormal", this.dto).then(res => {
        this.loading = false;
        if (res.status === 200) {
          this.dto.employeeId = null;
          this.dto.name = null;
          this.dto.employeeName = null;
          this.dto.socialAmount = null;
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
        }
      })
    },
    initChooseProject(data) {
      this.dto.employeeId = data.id;
      this.dto.name = data.realName;
      this.dto.employeeNo = data.no;
      this.dto.employeeName = data.realName + data.no;
      this.closeCurrModal();
    },
    closeCurrModal(data) {
      this.isService = false;
    },
  }
};
</script>

<style scoped>
.handle-box {
  /*margin-bottom: 10px;*/
  font-weight: bold !important;
}

.el-form-item__label {
  font-weight: bold !important;
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}

.del-dialog-cnt {
  font-size: 16px;
  text-align: center;
}

.table {
  width: 100%;
  font-size: 13px;

}

.red {
  color: #ff0000;
}

.mr10 {
  margin-right: 10px;
}

.el-date-editor .el-range-separator {
  padding: 0 5px;
  line-height: 32px;
  width: 7%;
  color: #303133;
}

.container {
  padding: 0px !important;
  background: #fff;
  border: none !important;
  border-radius: 5px;
}
</style>
