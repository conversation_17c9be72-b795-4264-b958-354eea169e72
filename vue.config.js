const webpack = require('webpack');

let otherUrl = this.otherUrl;

module.exports = {
    baseUrl: './',
    productionSourceMap: false,
    devServer: {
        port: 8083,
        open: false,
        // host: '*************',
        host: 'localhost',
        https: false,
        //以上的ip和端口是我们本机的;下面为需要跨域的
        proxy: {  //配置跨域
            '/api': {
                target: 'http://localhost:8063',  //这里后台的地址模拟的;应该填写你们真实的后台接口
                ws: true,
                changOrigin: true,  //允许跨域
                pathRewrite: {
                    '^/api': ''  //请求的时候使用这个api就可以
                }
            },
            '/admins': {
                target: otherUrl + '/admins',
                changeOrigin: true,
                pathRewrite: {
                    '^/admins': 'admins'
                }
            },
        }
    },
    configureWebpack: {
        plugins: [
            new webpack.ProvidePlugin({
                $:"jquery",
                'window.Quill': 'quill/dist/quill.js',
                'Quill': 'quill/dist/quill.js',
                jQuery:"jquery",
                "windows.jQuery":"jquery"
            })
        ]
    }
};
