<template style="background-color: #000">
	<div ref="contenBox">
		<div id="bodyDiv">
			<div id="searchDiv">
				<el-steps :active="active" align-center direction="vertical">
					<el-step title="填写投诉信息" @click.native="setNum(0)" icon="el-icon-edit"></el-step>
					<el-step title="审核阶段" @click.native="setNum(1)" icon="el-icon-upload"></el-step>
					<el-step title="处理阶段" @click.native="setNum(2)" icon="el-icon-share"></el-step>
					<el-step title="已完成" @click.native="setNum(3)" icon="el-icon-s-promotion"></el-step>
					<el-step title="已结案" @click.native="setNum(4)" icon="el-icon-success"></el-step>
				</el-steps>
			</div>
			<div class="fromInput">
				<Form v-if="active===0" ref="dto" :model="dto" :rules="ruleValidate" :label-width="80"
					label-position="left">
					<FormItem label="投诉渠道" prop="channel">
						<Select filterable clearable @on-change="onChange" label-in-value style="width: 250px"
							v-model="dto.channel">
							<Option value="">请选择</Option>
							<Option v-for="item in channelList" :value="item.id" :key="index">{{ item.text}}</Option>
						</Select>
					</FormItem>
					<FormItem label="投诉内容" prop="tsContent">
						<Input v-model="dto.tsContent" type="textarea" clearable style="width: 250px"
							:autosize="{minRows: 4,maxRows: 4}" placeholder="投诉内容">
						</Input>
					</FormItem>
					<FormItem label="备注" v-if="label==='其他'">
						<Input v-model="dto.tsRemark" style="width: 250px" type="textarea"
							:autosize="{minRows: 4,maxRows: 4}" placeholder="备注">
						</Input>
					</FormItem>
					<FormItem>
						<div v-if="active===0">
							<Button type="success" @click="update( dto)">保存</Button>
							<Button type="primary" style="margin-left: 3rem" @click="save('dto')">下一步</Button>
							<Button style="margin-left: 3rem" @click="chooseThisModel('dto')">取消</Button>
						</div>
					</FormItem>
				</Form>

				<Form ref="dto1" :model="dto1" :rules="ruleValidate" :label-width="80" label-position="left">
					<div style="float: left " v-if="active===1">
						<FormItem label="投诉级别" prop="level">
							<Select filterable clearable style="width: 250px" v-model="dto1.level">
								<Option value="">请选择</Option>
								<Option v-for="item in levelList" :value="item.id" :key="index">{{ item.text}}</Option>
							</Select>
						</FormItem>
						<FormItem label="联系客户" prop="memberPhone">
							<Input id="memberPhone" v-model="dto1.memberPhone" clearable style="width: 200px"
								placeholder="联系客户">
							</Input>
							<Button class="copy-btn" type="info" v-clipboard:copy="dto1.memberPhone"
								v-clipboard:success="onCopy" v-clipboard:error="onError">复制
							</Button>
						</FormItem>
						<FormItem label="联系结果" prop="mcommContent">
							<Input v-model="dto1.mcommContent" clearable type="textarea" style="width: 250px"
								:autosize="{minRows: 3,maxRows: 3}" placeholder="联系结果">
							</Input>
						</FormItem>
						<FormItem label="上传材料">
							<Upload multiple :action="imgUrl" :on-success="onSuccess"
								:on-preview="handlePictureCardPreview" :on-remove="handleRemove" :before-upload="before"
								:on-exceed="onExcees" :default-file-list="filesList" :max-size="10240" :data="files">
								<Button icon="ios-cloud-upload-outline">点击上传</Button>
							</Upload>
						</FormItem>
						<FormItem label="是否需要交付部处理" prop="isTransfer">
							<el-radio v-model="dto1.isTransfer" label="false">否</el-radio>
							<el-radio v-model="dto1.isTransfer" label="true">是</el-radio>
						</FormItem>
						<FormItem label="处理人" prop="transferNo">
							<Select filterable clearable style="width:200px" label-in-value v-model="dto1.transferNo">
								<Option value="">请选择</Option>
								<Option v-for="item in this.employeeRoleList" :value="item.no" :key="index">
									{{item.no}}{{item.realName}}
								</Option>
							</Select>
						</FormItem>
					</div>
					<div v-if="active===1" class="link-right"></div>
					<div v-if="active===1" style="float: right;margin-left:6rem ">
						<FormItem label="是否上门">
							<el-radio v-model="dto1.isDoor" label="false">否</el-radio>
							<el-radio v-model="dto1.isDoor" label="true">是</el-radio>
						</FormItem>
						<FormItem label="上门结果">
							<Input v-model="dto1.doorContent" clearable type="textarea" style="width: 250px"
								:autosize="{minRows: 3,maxRows: 3}" placeholder="上门结果">
							</Input>
						</FormItem>
						<FormItem label="是否约谈">
							<el-radio v-model="dto1.isTalk" label="false">否</el-radio>
							<el-radio v-model="dto1.isTalk" label="true">是</el-radio>
						</FormItem>
						<FormItem label="约谈内容">
							<Input v-model="dto1.talkContent" clearable type="textarea" style="width: 250px"
								:autosize="{minRows: 3,maxRows: 3}" placeholder="约谈内容">
							</Input>
						</FormItem>
						<!--<FormItem label="投诉判定" prop="isFound">-->
						<!--<el-radio v-model="dto1.isFound" label="false">不成立</el-radio>-->
						<!--<el-radio v-model="dto1.isFound" label="true">成立</el-radio>-->
						<!--</FormItem>-->
						<FormItem label="投诉判定" prop="violation">
							<el-radio v-model="dto1.violation" :label=1>有效投诉</el-radio>
							<el-radio v-model="dto1.violation" :label=4>其他投诉</el-radio>
							<el-radio v-model="dto1.violation" :label=2>无效投诉</el-radio>
							<!--<el-radio v-model="dto1.violation" :label=3>违纪处罚</el-radio>-->
						</FormItem>
						<FormItem>
							<div class="next" v-if="active===1">
								<Button type="success" @click="update( dto1)">保存</Button>
								<Button type="primary" style="margin-left:3rem" @click="topNext('formItem')">上一步
								</Button>
								<Button v-if="dto1.violation===1||dto1.violation===3" style="margin-left:3rem"
									type="primary" @click="save('dto1')"> 下一步
								</Button>
								<Button v-if="dto1.violation===2" style="margin-left:3rem" type="success"
									@click="save('dto1')"> 完成
								</Button>
								<Button style="margin-left: 3rem" @click="chooseThisModel('dto1')">取消</Button>
							</div>
						</FormItem>
					</div>
				</Form>
				<Form v-if="active===2" ref="dto2" :model="dto2" :rules="ruleValidate" :label-width="80"
					label-position="left">
					<div style="float: left " v-if="active===2">
						<FormItem :label="this.captainName">
							<Input v-model="dto2.employeePhone" clearable placeholder="联系员工" style="width: 200px">
							</Input>
							<Button class="copy-btn" type="info" v-clipboard:copy="dto2.employeePhone"
								v-clipboard:success="onCopy" v-clipboard:error="onError">复制
							</Button>
						</FormItem>
						<FormItem label="联系结果" prop="ecommContent">
							<Input v-model="dto2.ecommContent" clearable type="textarea" style="width: 250px"
								:autosize="{minRows: 3,maxRows: 3}" placeholder="联系结果">
							</Input>
						</FormItem>

						<FormItem label="投诉类型" prop="type">
							<Select filterable clearable style="width: 250px" v-model="dto2.type">
								<Option value="">请选择</Option>
								<Option v-for="item in typeList" :value="item.id" :key="index">{{ item.text}}</Option>
							</Select>
						</FormItem>
						<FormItem label="服务质量">
							<Select filterable clearable style="width: 250px" v-model="dto2.quality">
								<Option value="">请选择</Option>
								<Option v-for="item in qualityList" :value="item.id" :key="index">{{ item.text}}
								</Option>
							</Select>
						</FormItem>
					</div>
					<div v-if="active===2" class="link-right"></div>
					<div v-if="active===2" style="float: right;margin-left:6rem ">
						<FormItem label="处理时效" prop="period">
							<Select filterable clearable style="width: 250px" v-model="dto2.period">
								<Option value="">请选择</Option>
								<Option v-for="item in periodList" :value="item.id" :key="index">{{ item.text}}</Option>
							</Select>
						</FormItem>
						<!--<FormItem label="处理建议">-->
						<!--<Input v-model="dto2.proposal"-->
						<!--type="textarea"-->
						<!--style="width: 250px"-->
						<!--:autosize="{minRows: 3,maxRows: 3}"-->
						<!--placeholder="处理建议">-->
						<!--</Input>-->
						<!--</FormItem>-->
						<FormItem label="处理结果" prop="result">
							<Input v-model="dto2.result" type="textarea" style="width: 250px"
								:autosize="{minRows: 3,maxRows: 3}" placeholder="处理结果">
							</Input>
						</FormItem>
						<FormItem>
							<div class="next" v-if="active===2">
								<Button type="success" @click="update( dto2)">保存</Button>
								<Button type="primary" style="margin-left:3rem"
									@click="topNext('formItem')">上一步</Button>
								<Button type="primary" style="margin-left:3rem" @click="save('dto2')">下一步</Button>
								<Button style="margin-left: 3rem" @click="chooseThisModel('dto2')">取消</Button>
							</div>
						</FormItem>
					</div>
				</Form>

				<Form v-if="active===3" ref="dto3" :model="dto3" :rules="ruleValidate" :label-width="100"
					label-position="left">
					<div style="float: left " v-if="active===3&&dto1.violation ===1">
						<FormItem label="客户是否同意处理方案" prop="isAgree">
							<el-radio v-model="dto3.isAgree" label="false">否</el-radio>
							<el-radio v-model="dto3.isAgree" label="true">是</el-radio>
						</FormItem>
						<FormItem label="被投诉人" prop="beEmployees">
							<!--                            <Select filterable clearable multiple-->
							<!--                                    style="width:200px"-->
							<!--                                    @on-change="beTs"-->
							<!--                                    label-in-value-->
							<!--                                    v-model="dto3.beEmployees">-->
							<!--                                <Option value="">请选择</Option>-->
							<!--                                <Option v-for="item in serviceEmployeeList" :value="item.no"  :key="index">-->
							<!--                                    {{item.no}}{{item.realName}}-->
							<!--                                </Option>-->
							<!--                            </Select>-->
							<el-select v-model="dto3.beEmployees" multiple filterable remote reserve-keyword
								@change="beTs" placeholder="请输入关键词" :remote-method="getServiceEmployee"
								:loading="loadingSelect">
								<el-option v-for="item in serviceEmployeeList" :key="item.no"
									:label="item.no +'-' + item.realName" :value="item.no">
								</el-option>
							</el-select>
							<Button type="primary" style="margin-left: 2rem" @click="isTs=true">
								详情
							</Button>
						</FormItem>
						<div style="color: #20a0ff;font-size: 13px">
							<div v-for="(item,index) in dto3.orderTsEmployees" style="margin-top: 0.5rem">
								姓名:{{item.name}} &nbsp;&nbsp;工龄:{{item.workingYear}}&nbsp;&nbsp;手机号码:{{item.phone}}
								<!--<Button class="copy-btn" type="info"-->
								<!--v-clipboard:copy="dto3.orderTsEmployees[index].phone"-->
								<!--v-clipboard:success="onCopy"-->
								<!--v-clipboard:error="onError">复制-->
								<!--</Button>-->
								<br />
							</div>
						</div>
						<FormItem label="最后处理人">
							<Input v-model="dto3.lastDealNo" clearable style="width: 250px" placeholder="填写工号即可">
							</Input>
						</FormItem>
						<!--<FormItem label="违纪罚金" prop="fine" style="margin-top: 2rem">-->
						<!--<Select filterable clearable-->
						<!--style="width: 250px" v-model="dto3.fine">-->
						<!--<Option value="">请选择</Option>-->
						<!--<Option v-for="item in finedList" :value="item.id"  :key="index">{{ item.text}}</Option>-->
						<!--</Select>-->
						<!--</FormItem>-->
						<!--<FormItem label="行政处罚">-->
						<!--<Select filterable clearable-->
						<!--style="width: 250px" v-model="dto3.adminFine">-->
						<!--<Option value="">请选择</Option>-->
						<!--<Option v-for="item in adminFinedList" :value="item.id"  :key="index">{{ item.text}}</Option>-->
						<!--</Select>-->
						<!--</FormItem>-->
						<!--<FormItem label="行政扣分">-->
						<!--<el-input-number v-model="dto3.scoreDetails.changeScore" @change="handleChange"-->
						<!--:min="-100" :max="100">-->
						<!--</el-input-number>-->
						<!--</FormItem>-->
					</div>
					<div style="float: left " v-else>
						<FormItem label="违纪人员">
							<span>{{dto3.offenceNos}}</span>
							<Button type="primary" style="margin-left: 2rem" @click="isService=true">
								选择
							</Button>
						</FormItem>
						<FormItem label="违纪行为" prop="offence">
							<Input v-model="dto3.offence" clearable style="width: 250px" type="textarea"
								:autosize="{minRows: 3,maxRows: 3}" placeholder="违纪行为">
							</Input>
						</FormItem>
						<FormItem label="违纪罚金" prop="offenceFine">
							<Input v-model="dto3.offenceFine" clearable style="width: 250px" placeholder="违纪罚金"
								@on-blur="this.offenceBlur">
							</Input>
						</FormItem>

						<FormItem label="停工处罚" prop="punishment">
							<Select filterable clearable label-in-value style="width: 250px" v-model="dto3.punishment">
								<Option value="">请选择</Option>
								<Option v-for="item in punishmentList" :value="item.id" :key="index">{{ item.text}}
								</Option>
							</Select>
						</FormItem>
						<FormItem label="行政扣分">
							<el-input-number v-model="dto3.scoreDetails.changeScore" @change="handleChange" :min="-100"
								:max="0">
							</el-input-number>
						</FormItem>
						<FormItem label="最后处理人">
							<Input v-model="dto3.lastDealNo" clearable style="width: 250px" placeholder="填写工号即可">
							</Input>
						</FormItem>
					</div>


					<div v-if="active===3" class="link-right"></div>
					<div v-if="active===3" style="float: right;margin-left:2rem ">
						<!--<FormItem label="行政人员">-->
						<!--<Select filterable clearable multiple-->
						<!--style="width:200px"-->
						<!--label-in-value-->
						<!--v-model="dto3.bePersonnelList">-->
						<!--<Option value="">请选择</Option>-->
						<!--<Option v-for="item in employeeList" :value="item.no"  :key="index">-->
						<!--{{item.no}}{{item.realName}}-->
						<!--</Option>-->
						<!--</Select>-->
						<!--</FormItem>-->
						<FormItem label="是否回炉培训">
							<el-radio v-model="dto3.isTrain" label="false">否</el-radio>
							<el-radio v-model="dto3.isTrain" label="true">是</el-radio>
						</FormItem>
						<FormItem label="推荐案例">
							<el-radio v-model="dto3.isCase" label="false">否</el-radio>
							<el-radio v-model="dto3.isCase" label="true">是</el-radio>
						</FormItem>
						<FormItem>
							<div class="next" v-if="active===3" style="float: right;margin-top:2rem ">
								<Button type="primary" @click="topNext('formItem')">上一步</Button>
								<Button type="success" style="margin-left:3rem" @click="save('dto3')">完成</Button>
							</div>
						</FormItem>
					</div>
				</Form>
				<Form v-if="active===4" :label-width="80" label-position="left">
					<div class="lastText">订单编号：{{dto3.billNo}}</div>
					<br />
					<div class="lastText">服务项目:{{dto3.order.productName}}</div>
					<br />
					<div class="lastText">投诉类型：{{this.setType(dto3.type)}}</div>
					<br />
					<div>服务质量：{{this.setQuality(dto3.quality)}}</div>
					<br />
					<div>被投诉人:{{this.setBe()}}
						<Button v-if="this.dto1.violation===1" type="primary" style="margin-left: 2rem"
							@click="isTs=true">
							详情
						</Button>
					</div>
					<div style="color: #20a0ff;font-size: 12px">
						<div v-for="(item,index) in dto3.orderTsEmployees" style="margin-top: 0.5rem">
							姓名:{{item.name}} &nbsp;&nbsp;工龄:{{item.workingYear}}&nbsp;&nbsp;手机号码:{{item.phone}}
							<!--<Button class="copy-btn" type="info"-->
							<!--v-clipboard:copy="dto3.orderTsEmployees[index].phone"-->
							<!--v-clipboard:success="onCopy"-->
							<!--v-clipboard:error="onError">复制-->
							<!--</Button>-->
							<br />
						</div>
					</div>
					<div>处理结果:{{dto3.result}}</div>
					<br />
					<!--<div>违纪罚金:{{this.setFine(dto3.fine)}}</div>-->
					<!--<br/>-->
					<!--<div>行政处罚:{{this.setAdmin(dto3.adminFine)}}</div>-->
					<div>行政扣分:{{this.dto3.scoreDetails.changeScore}}</div>
					<!--<FormItem>-->
					<!--<div class="next" v-if="active===4" style="float: right;">-->
					<!--<Button type="primary" @click="topNext('formItem')">上一步</Button>-->
					<!--<Button style="margin-left:3rem" @click="chooseThisModel()">取消</Button>-->
					<!--</div>-->
					<!--</FormItem>-->
				</Form>
			</div>
		</div>
		<Modal v-model="isService" class="Modal" :width="screenWidth1" title="服务人员" :mask-closable="false">
			<div class="addBody">
				<employee-choose v-if="isService" @init-choose="initChooseProject" :stage="stage"
					@close-modal="closeCurrModal"></employee-choose>
			</div>
			<div slot="footer">
			</div>
		</Modal>
		<Modal v-model="isTs" class="Modal" :width="screenWidth2" title="被投诉人详情" :mask-closable="false">
			<div class="addBody">
				<ts-employee-choose v-if="isTs" @init-choose="initChooseProject" :tsId="dto.id"
					@close-modal="closeCurrModal"></ts-employee-choose>
			</div>
			<div slot="footer">
			</div>
		</Modal>
	</div>
</template>
<script>
	import employeeChoose from '@/components/page/wordOrder/choose/employeeChoose.vue'
	import tsEmployeeChoose from '@/components/page/wordOrder/choose/tsEmployeeChoose.vue'

	export default {
		props: ['stage'],
		data() {
			return {
				loadingSelect: false,
				isService: false,
				isTs: false,
				label: null,
				captainName: "联系员工",
				active: this.stage.stage,
				orderExt: {
					billNo: null,
					lblId: 4,
					pdErrorRemark: "投诉罚款"
				},
				imgUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
				testUrl: "http://************:6052/files/uploadFiles",
				channelList: [],
				punishmentList: [],
				isTransfer: false,
				evaluateList: [],
				employeeService: [],
				ruleValidate: {
					// scoreType: [
					//     {required: true, message: '请选择评价类型 ', trigger: 'change', type: "number"}
					// ],
					// changeScore: [
					//     {required: true, message: '请输入积分扣减 ', trigger: 'change', type: "number"}
					// ],
					// reamke: [
					//     {required: true, message: '请填写备注', trigger: 'change'}
					// ],
					channel: [{
						required: true,
						message: '请选择投诉渠道',
						trigger: 'change',
						type: "number"
					}],
					tsContent: [{
						required: true,
						message: '请填写投诉内容',
						trigger: 'change'
					}],
					memberPhone: [{
						required: true,
						message: '会员手机号码不能为空',
						trigger: 'change'
					}],
					mcommContent: [{
						required: true,
						message: '联系结果不能为空',
						trigger: 'change'
					}],
					ecommContent: [{
						required: true,
						message: '联系结果不能为空',
						trigger: 'change'
					}],
					employeePhone: [{
						required: true,
						message: '员工手机号码不能为空',
						trigger: 'change'
					}],
					isFound: [{
						required: true,
						message: '请选择投诉是否成立',
						trigger: 'change'
					}],
					violation: [{
						required: true,
						message: '请选择投诉判断',
						trigger: 'change',
						type: "number"
					}],
					type: [{
						required: true,
						message: '请选择投诉类型',
						trigger: 'change',
						type: "number"
					}],
					level: [{
						required: true,
						message: '请选择投诉级别',
						trigger: 'change',
						type: "number"
					}],
					quality: [{
						required: true,
						message: '请选择服务质量',
						trigger: 'change',
						type: "number"
					}],
					period: [{
						required: true,
						message: '请选择处理时效',
						trigger: 'change',
						type: "number"
					}],
					result: [{
						required: true,
						message: '处理结果不能为空',
						trigger: 'change'
					}],
					isAgree: [{
						required: true,
						message: '请选择是否同意处理建议',
						trigger: 'change'
					}],
					isTransfer: [{
						required: true,
						message: '请选择是否转派',
						trigger: 'change'
					}],
					fine: [{
						required: true,
						message: '请选择违纪罚金',
						trigger: 'change',
						type: "number"
					}],
					transferNo: [{
						required: true,
						message: '转派人员不能为空',
						trigger: 'change'
					}],
					beEmployees: [{
						required: true,
						message: '被投诉人员不能为空',
						trigger: 'change',
						type: "array"
					}],
					offenceENos: [{
						required: true,
						message: '违纪人员不能为空',
						trigger: 'change',
						type: "number"
					}],
					offence: [{
						required: true,
						message: '违纪行为不能为空',
						trigger: 'change'
					}],
					offenceFine: [{
						required: true,
						message: '违纪罚金不能为空',
						trigger: 'change'
					}],
					punishment: [{
						required: true,
						message: '停工处罚不能为空',
						trigger: 'change',
						type: "number"
					}]
				},
				dto: {
					id: this.stage.id,
					channel: null,
					tsContent: null,
					tsRemark: null,
					stage: this.active,
					state: null
				},
				dto1: {
					id: this.stage.id,
					memberPhone: null,
					mcommContent: null,
					isDoor: "false",
					employeePhone: null,
					ecommContent: null,
					isTalk: "false",
					isFound: "false",
					memberFile: "",
					talkContent: null,
					employeeFile: "",
					stage: this.active,
					state: null,
					doorContent: null,
					violation: null
				},
				dto2: {
					id: this.stage.id,
					type: null,
					level: null,
					quality: null,
					period: null,
					transferNo: null,
					isTransfer: "false",
					proposal: null,
					result: null,
					stage: this.active,
					state: null,
					periodTime: new Date()
				},
				dto3: {
					order: {
						productName: null
					},
					scoreDetails: {
						changeScore: null,
						scoreType: null,
						reamke: null,
						billNo: null,
					},
					id: this.stage.id,
					stage: this.active,
					state: null,
					isAgree: "false",
					isTransfer: "false",
					fine: null,
					transferNo: null,
					adminFine: null,
					lastTime: new Date(),
					beEmployees: [],
					bePersonnelList: [],
					offenceNos: null,
					isCase: "false",
					isTrain: "false",
					scoreId: null,
					fundId: null,
					offence: null,
					offenceFine: null,
					punishment: null,
					lastDealNo: null
				},
				files: {
					fileName: "Ts/"
				},
				typeList: [],
				qualityList: [],
				levelList: [],
				periodList: [],
				finedList: [],
				adminFinedList: [],
				filesList1: [],
				filesList: [],
				employeeList: [],
				employeeRoleList: [],
				screenWidth1: '40%',
				screenWidth2: '60%',
				serviceEmployeeList: []
			}
		},
		components: {
			"employeeChoose": employeeChoose,
			"tsEmployeeChoose": tsEmployeeChoose
		},
		created: function() {
			this.getById();
			this.getChannelList();
			this.getEvaluateList();
			this.getTypeList();
			this.getQualityList();
			this.getLevelList();
			this.getPeriodList();
			this.getFine();
			this.getAdminFineList();
			this.getEmployeeList();
			this.getPunishmentList();
		},
		methods: {

			getServiceEmployee(query) {
				this.loadingSelect = true;
				this.serviceEmployeeList = [];
				this.$postData("employeeGetByNo", {
					no: query
				}).then(res => {
					if (res.status === 200 && res.data) {
						let data = res.data;
						this.serviceEmployeeList.push(data);
					}
					this.loadingSelect = false;
				})
			},
			offenceBlur() {
				if (this.dto3.offenceFine === '' && this.dto3.offenceFine == null) {
					return
				}
				if (this.dto3.offenceFine.indexOf("-") < 0) {
					this.dto3.offenceFine = "-" + this.dto3.offenceFine
				}
			},
			handleChange(value) {
				this.dto3.scoreDetails.changeScore = value
			},
			setNum(num) {
				this.active = num;
			},
			setType(id) {
				for (let i = 0; i < this.typeList.length; i++) {
					if (id === this.typeList[i].id) {
						return this.typeList[i].text
					}
				}
			},
			setQuality(id) {
				for (let i = 0; i < this.qualityList.length; i++) {
					if (id === this.qualityList[i].id) {
						return this.qualityList[i].text
					}
				}
			},
			setFine(id) {
				for (let i = 0; i < this.finedList.length; i++) {
					if (id === this.finedList[i].id) {
						return this.finedList[i].text
					}
				}
			},
			setAdmin(id) {
				for (let i = 0; i < this.adminFinedList.length; i++) {
					if (id === this.adminFinedList[i].id) {
						return this.adminFinedList[i].text
					}
				}
			},
			setBe() {
				let res;
				if (this.dto1.violation === 3) {
					res = this.dto3.offenceNos;
				} else {
					res = this.dto3.beEmployees.join(",");;
					if (this.dto3.bePersonnelList && this.dto3.bePersonnelList.length > 0) {
						res += "," + this.dto3.bePersonnelList.join(",")
					}
				}
				return res;
			},
			getById() {
				this.$getData("tsGetById", {
					id: this.dto.id
				}).then(res => {
					;
					if (res.status === 200) {
						if (res.data.isTransfer === "true") {
							this.isTransfer = true;
						}
						for (let i = 0; i < this.channelList.length; i++) {
							if (res.data.channel === this.channelList[i].id) {
								this.label = this.channelList[i].text;
								break
							}
						}
						this.getFiles(res.data);
						this.active = res.data.stage;
						this.dto = res.data;
						this.dto1 = res.data;
						this.dto2 = res.data;
						this.dto3 = res.data;
						this.dto1.isTalk = this.dto1.isTalk + "";
						this.dto1.isFound = this.dto1.isFound + "";
						this.dto1.isDoor = this.dto1.isDoor + "";
						this.dto3.isAgree = this.dto3.isAgree + "";
						this.dto3.isCase = this.dto3.isCase + "";
						this.dto3.isTrain = this.dto3.isTrain + "";
						if (this.dto3.isTransfer) {
							this.isTransfer = true
						}
						if (this.dto1.isTransfer) {
							this.isTransfer = true
						}
						this.dto3.isTransfer = this.dto3.isTransfer + "";
						this.dto1.isTransfer = this.dto1.isTransfer + "";
						if (this.dto1.employeeCaptain != null) {
							this.captainName += this.dto1.employeeCaptain.no + this.dto1.employeeCaptain.realName
						}
						this.getEmployeeRoleList();
					}
				})
			},
			insertEmployee(value) {
				this.$postData("emOrder_save", {
					tsId: this.dto3.id,
					billNo: this.dto3.billNo,
					employeeNo: value.value,
				}).then(res => {

				})
			},
			beTs(value) {
				console.log(value);
				console.log(value.length);
				console.log(this.dto3);
				if (this.dto3.orderTsEmployees == null || value.length > this.dto3.orderTsEmployees.length) {
					this.insertEmployee(value[value.length - 1]);
					this.$postData("employeeGetByNo", {
						no: value[value.length - 1]
					}).then(res => {
						;
						if (res.status === 200) {
							let list = {
								name: res.data.no + res.data.realName,
								phone: res.data.phone,
								workingYear: res.data.workingYear
							};
							this.dto3.orderTsEmployees.push(list);
							this.update(this.dto3)
						}
					})
				} else if (value.length < this.dto3.orderTsEmployees.length) {
					let flag = false;
					for (let i = 0; i < this.dto3.orderTsEmployees.length; i++) {
						let list = this.dto3.orderTsEmployees[i];
						console.log(list);
						let name = list.name + "";
						for (let j = 0; j < value.length; j++) {
							let no = value[j].value;
							console.log(name);
							console.log(no);
							console.log(name.indexOf(no));
							if (name.indexOf(no) >= 0) {
								flag = true;
								break;
							}
						}
						if (!flag) {
							console.log(flag);
							let name = this.dto3.orderTsEmployees[i].name;
							let index = escape(name).indexOf("%u"); //判断中文下标
							this.dto3.orderTsEmployees.splice(i, 1);
							this.deleteEmployee(name.substring(0, index));
							this.update(this.dto3);
							break;
						} else {
							flag = false
						}
					}
				}
			},

			deleteEmployee(no) {
				this.$postData("emOrder_delete", {
					tsId: this.dto3.id,
					billNo: this.dto3.billNo,
					employeeNo: no,
				}).then(res => {

				})
			},
			getEmployeeList() {
				this.$postData("employee_getByList", {}).then(res => {
					if (res.status === 200) {
						this.employeeList = res.data;
					} else {
						this.$message.error("查询失败，" + res.msg);
					}
				})
			},
			getEmployeeRoleList() {
				this.$getData("tsOrder_getGdNo", {}).then(res => {
					;
					if (res.status === 200) {
						this.employeeRoleList = res.data;
						if (this.dto1.transferNo == null || this.dto1.transferNo === '') {
							this.dto1.transferNo = res.data[0].no
						}
					} else {
						this.$message.error("查询失败，" + res.msg);
					}
				})
			},
			onCopy(e) {
				this.$message.success("内容已复制到剪切板！")
			},
			onError(e) {
				this.$message.error("抱歉，复制失败！")
			},
			getFiles(data) {
				if (data.memberFile) {
					let split = data.memberFile.split(",");
					for (let i = 0; i < split.length; i++) {
						let res = decodeURIComponent(split[i]);
						let file = {
							name: res.substring(res.lastIndexOf("/") + 1, res.length),
							url: res
						};
						this.filesList.push(file);
					}
				}
				if (data.employeeFile) {
					let split = data.employeeFile.split(",");
					for (let i = 0; i < split.length; i++) {
						let res = decodeURIComponent(split[i]);
						console.log(res.substring(res.lastIndexOf("/") + 1, res.length));
						let file = {
							name: res.substring(res.lastIndexOf("/") + 1, res.length),
							url: res
						};
						this.filesList1.push(file);
					}
				}
			},
			getChannelList() {
				this.$postUrl("get_dict", 114, null, {}).then(res => {
					if (res.status === 200) {
						;
						this.channelList = res.data
					}
				})
			},
			getEvaluateList() {
				this.$postUrl("get_dict", 121, null, {}).then(res => {
					if (res.status === 200) {
						;
						this.evaluateList = res.data
					}
				})
			},
			getPunishmentList() {
				this.$postUrl("get_dict", 123, null, {}).then(res => {
					if (res.status === 200) {
						;
						this.punishmentList = res.data
					}
				})
			},
			getTypeList() {
				this.$postUrl("get_dict", 112, null, {}).then(res => {
					if (res.status === 200) {
						this.typeList = res.data
					}
				})
			},
			getLevelList() {
				this.$postUrl("get_dict", 115, null, {}).then(res => {
					if (res.status === 200) {
						this.levelList = res.data
					}
				})
			},
			getQualityList() {
				this.$postUrl("get_dict", 120, null, {}).then(res => {
					if (res.status === 200) {
						this.qualityList = res.data
					}
				})
			},
			getPeriodList() {
				this.$postUrl("get_dict", 116, null, {}).then(res => {
					if (res.status === 200) {
						this.periodList = res.data;
					}
				})
			},
			getFine() {
				this.$postUrl("get_dict", 117, null, {}).then(res => {
					if (res.status === 200) {
						this.finedList = res.data;
					}
				})
			},
			getAdminFineList() {
				this.$postUrl("get_dict", 118, null, {}).then(res => {
					if (res.status === 200) {
						this.adminFinedList = res.data
					}
				})
			},
			topNext() {
				this.active--;
			},
			next() {
				this.active++;
			},
			onChange(value) {
				this.label = value.label;
				console.log(value)
			},

			getService(value) {
				this.$getData("getByService", {
					name: value
				}).then(res => {
					;
					if (res.status === 200) {
						this.employeeService = res.data
					}
				})
			},
			onChange1(value) {
				if (value.label === "是") {
					this.isTransfer = true
				} else {
					this.isTransfer = false
				}
				console.log(value);
			},
			save(name) {
				console.log(this[name]);
				this.$refs[name].validate((valid) => {
					console.log(valid);
					if (valid) {
						if (this.active === 0) {
							this.active++;
							if (this[name].stage < 1) {
								this[name].stage = 1;
								this.dto.state = 2;
							}
							this.update(this[name])
						} else if (this.active === 1) {
							this.dto1.employeeNo = this.dto1.employeePhone;
							if (this.dto1.violation === 1 || this.dto1.violation === 3) {
								if (this[name].stage < 2) {
									this[name].stage = 2;
									this.dto1.state = 2;
								}
								this.active++;
							} else {
								this[name].lastTime = new Date();
								this[name].stage = 1;
								this.dto1.state = 3;
								this.chooseThisModel(this[name])
							}
							if (this.dto1.transferNo !== '' && this.dto1.transferNo != null && !this.dto1.emPush) {
								this.pushTransfer()
							}
							this.update(this[name])
						} else if (this.active === 2) {
							this.active++;
							if (this[name].stage < 3) {
								this[name].stage = 3;
								this.dto2.state = 2;
							}
							this.dto2.periodTime = new Date();
							this.update(this[name])
						} else if (this.active === 3) {
							if (!this.dto3.push) {
								if (this.dto1.violation === 1) {
									this.push(this.dto3.beEmployees);
								} else if (this.dto1.violation === 3) {
									this.push(this.dto3.offenceNos);
								}
							}
							if (this.dto3.violation === 3) {
								if (this.dto3.fundId == null) {
									this.insetFund();
								} else {
									this.updateFund(this.dto3.fund);
								}
							}
							this.active++;
							this[name].stage = 4;
							if (!this[name].lastTime) {
								this[name].lastTime = new Date();
							}
							this.dto3.state = 7;
							// if (this.dto3.fine || this.dto3.adminFine) {
							//
							//     this.saveExt();
							// }
							if (this.dto3.isCase === 'true') {
								this.saveExt1();
							}
							if (this.dto3.violation === 3) {
								if (this.dto3.scoreId == null || this.dto3.scoreId === '') {
									this.storeDetailsInsert(this[name])
								} else {
									this.storeDetailsUpdate(this[name])
								}
							}
							this.update(this[name])
						} else if (this.active === 4) {}
					}
				});
			},
			pushTransfer() {
				let query = {
					text: "收到" + this.dto2.dealName + "投诉工单转派通知。(" + this.dto1.billNo + ")",
					agentid: "0",
					no: this.dto1.transferNo,
				};
				this.$postData("ts_push", query).then(res => {
					;
					if (res.code === 0) {
						this.dto1.dealNo = this.dto2.transferNo;
						this.update(this.dto2)
					}
					if (res.code === 500) {
						alert(res.data.msg)
					}
				})
			},
			setChangeAmount(id) {
				for (let i = 0; i < this.finedList.length; i++) {
					if (id === this.finedList[i].id) {
						return this.finedList[i].text.replace(/[^0-9]/ig, "")
					}
				}
			},
			insetFund() {
				let changeAmount = 0;
				if (this.dto1.violation === 1) {
					changeAmount = this.setChangeAmount(this.dto3.fine);
				}
				if (this.dto1.violation === 3) {
					changeAmount = this.dto3.offenceFine;
				}
				console.log(changeAmount);
				let fundDetails = {
					employeeNo: this.dto3.offenceNos,
					employeeFundDetailsType: 90,
					changeAmount: changeAmount,
					billno: this.dto3.billNo,
					productName: this.dto3.order.productName
				};
				console.log(fundDetails);
				this.$postData("fundDetails_insert", fundDetails).then(res => {
					if (res.status === 200) {
						this.dto3.fundId = res.data;
						this.update(this.dto3)
					}
				})
			},

			updateFund(fund) {
				if (this.dto1.violation === 1) {
					fund.changeAmount = this.setChangeAmount(this.dto3.fine);
				}
				if (this.dto1.violation === 3) {
					fund.changeAmount = this.dto3.offenceFine;
				}
				this.$postData("fundDetails_update", fund).then(res => {})
			},

			storeDetailsInsert(dto) {
				dto.scoreDetails.employeeNo = dto.employeeCaptain.no;
				dto.scoreDetails.scoreType = this.evaluateList[0].id;
				dto.scoreDetails.reamke = this.setType(dto.type);
				dto.scoreDetails.billNo = dto.billNo;
				this.$postData("storeDetails_insert", dto.scoreDetails).then(res => {
					if (res.status === 200) {
						this.dto3.scoreId = res.data;
						this.update(this.dto3)
					}
				})
			},
			storeDetailsUpdate(dto) {
				dto.scoreDetails.billNo = dto.billNo;
				this.$postData("storeDetails_update", dto.scoreDetails).then(res => {})
			},
			push(beEmployees) {
				if (this.dto1.violation === 3) {
					let query = {
						text: "来自订单" + this.dto.billNo + "的处罚通知,点击查看详情\n" +
							"http://aiservice.xiaoyujia.com/orderTs/" + this.dto.id + "/" + beEmployees + "/",
						agentid: "0",
						no: beEmployees
					};
					this.$postData("ts_push", query).then(res => {
						;
						if (res.code === 0) {
							this.dto3.push = true;
							this.update(this.dto3)
						}
						if (res.code === 500) {
							alert(res.data.msg)
						}
					})
				} else if (this.dto1.violation === 1) {
					for (let i = 0; i < beEmployees.length; i++) {
						alert(beEmployees[i]);
						let query = {
							text: "来自订单" + this.dto.billNo + "的处罚通知,点击查看详情\n" +
								"http://aiservice.xiaoyujia.com/orderTs/" + this.dto.id + "/" + beEmployees[i],
							agentid: "0",
							no: beEmployees[i]
						};
						this.$postData("ts_push", query).then(res => {
							;
							if (res.code === 0) {
								this.dto3.push = true;
								this.update(this.dto3)
							}
							if (res.code === 500) {
								alert(res.data.msg)
							}
						})
					}
				}
			},
			saveExt() {
				this.orderExt.billNo = this.dto3.billNo;
				this.$postData("orderExtSave", this.orderExt).then(res => {
					if (res.status === 200) {}
				});
			},
			saveExt1() {
				let list = {
					billNo: this.dto3.billNo,
					lblId: 10,
					pdErrorRemark: "推荐案例"
				};
				this.$postData("orderExtSave", list).then(res => {
					if (res.status === 200) {}
				});
			},
			update(dto) {
				if (dto.lastTime != null) {
					dto.lastTime = new Date(dto.lastTime);
				}
				if (dto.trainTime != null) {
					dto.trainTime = new Date(dto.trainTime);
				}
				this.$postData("tsUpdate", dto, {}).then(res => {
					if (res.status === 200) {
						this.$Message.success('保存成功');
					} else {
						this.$message.error("保存失败，" + res.msg);
					}
				});
			},
			/*
			 * 关闭当前窗口
			 * */
			chooseThisModel(dto) {
				this.$emit('init-choose', dto);
				//this.update(this[dto])
			},
			handleRemove(file) {
				console.log(file);
				let names = this.dto1.memberFile.split(",");
				for (let i = 0; i < names.length; i++) {
					if (file.url === decodeURIComponent(names[i])) {
						names.splice(i, 1);
						this.dto1.memberFile = names.join(",");
						break;
					}
				}
			},
			handleRemove1(file) {
				let names = this.dto1.memberFile.split(",");
				for (let i = 0; i < names.length; i++) {
					if (file.url === decodeURIComponent(names[i])) {
						names.splice(i, 1);
						this.dto1.employeeFile = names.join(",");
						break;
					}
				}
			},
			handlePictureCardPreview(file) {
				console.log(file);
				this.download(file.url, file.name);
			},
			onExcees() {
				this.$Message.success('只可上传一个文件');
			},
			download(src, fileName) {
				let x = new XMLHttpRequest();
				x.open("GET", src, true);
				x.responseType = 'blob';
				x.onload = function(e) {
					let url = window.URL.createObjectURL(x.response);
					let a = document.createElement('a');
					a.href = url;
					a.download = fileName;
					a.click();
				};
				x.send();
			},

			before() {
				if (this.dto1.memberFile) {
					let list = this.dto1.memberFile.split(",");
					const check = list.length < 2;
					if (!check) {
						this.$Notice.warning({
							title: '最多可上传2个附件,多文件请打包上传'
						});
					}
					return check;
				}
			},
			onSuccess(res) {
				;
				if (res.status === 200) {
					this.$Message.success('上传成功');
					if (this.dto1.memberFile) {
						this.dto1.memberFile = this.dto1.memberFile + "," + res.data;
					} else {
						this.dto1.memberFile = res.data;
					}
				}
			},
			onSuccess1(res) {
				;
				if (res.status === 200) {
					this.$Message.success('上传成功');
					if (this.dto1.employeeFile) {
						this.dto1.employeeFile = this.dto1.employeeFile + "," + res.data;
					} else {
						this.dto1.employeeFile = res.data;
					}
				}
			},
			initChooseProject(data) {
				this.dto3.offenceNos = data.no;
				this.closeCurrModal();
			},
			closeCurrModal(data) {
				this.isService = false;
				this.isTs = false
			},
		},
	}
</script>


<style scoped>
	.next {
		/*margin-left: 80px;*/
		/*bottom: 10rem;*/
		/*position: fixed;*/
		/*float: right;*/
	}

	.el-message-box {
		z-index: 3000 !important;
	}

	.link-right {
		width: 1px;
		height: 350px;
		float: left;
		border-right: solid #dcdfe6 1px;
		margin-left: 5rem;
	}

	/* 查询div*/
	#searchDiv {
		width: 160px;
		float: left;
		height: 550px;
		margin-left: 1rem;
	}

	.fromInput {
		float: left;
		margin-left: 2rem;
	}

	#bodyDiv {
		font-size: 15px !important;
		background-color: #fff;
		height: 500px;
	}

	.lastText {}
</style>