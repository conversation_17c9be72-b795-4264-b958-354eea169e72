<template>
<div class="app-container">
  <el-card>
    <div class="filter-container">
      <el-input
        v-model="searchForm.productName"
        placeholder="产品名称"
        style="width: 300px;"
        @keyup.enter.native="handleSearch"
      />
      <el-button type="primary" @click="handleSearch" style="margin-left:10px">搜索</el-button>
      <el-button type="success" @click="handleAddSku" style="margin-left:10px">新增SKU</el-button>
    </div>

    <el-table
      :data="tableData"
      border
      :span-method="objectSpanMethod"
      style="width: 100%;margin-top:20px;"
    >
      <el-table-column prop="productName" label="产品名称" width="180">
        <template slot-scope="{row}">
          <div class="product-group">
            {{ row.productName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="SKU图片" width="120">
        <template slot-scope="{row}">
          <el-image
            :src="row.skuImage"
            style="width:80px;height:80px"
            :preview-src-list="[row.skuImage]"
          />
        </template>
      </el-table-column>
      <el-table-column prop="skuName" label="规格名称" />
      <el-table-column prop="price" label="售价" width="120">
        <template slot-scope="scope">
          {{ scope.row.price.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="originalPrice" label="原价" width="120">
        <template slot-scope="scope">
          {{ scope.row.originalPrice.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="stockNum" label="当前库存" width="120" />
      <el-table-column prop="totalNum" label="总库存" width="120" />
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column
          label="操作"
          width="220"
          align="center">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="handleOutbound(scope.row)">出库</el-button>
            <el-button
              type="success"
              size="mini"
              @click="handleInbound(scope.row)">入库</el-button>
            <el-button
              type="info"
              size="mini"
              @click="handleLog(scope.row)">查看日志</el-button>
          </template>
        </el-table-column>
    </el-table>

    <el-dialog
      title="库存操作日志"
      :visible.sync="logDialogVisible"
      width="70%">
      <el-table
        :data="logList"
        border
        style="width:100%;margin-bottom:15px">
        <el-table-column
          prop="createTime"
          label="操作时间"
          width="180"
          align="center"/>
        <el-table-column
          prop="type"
          label="类型"
          align="center"/>
        <el-table-column
            prop="currentSku"
            label="变更前数量"
            align="center"/>
        <el-table-column
          prop="changeSku"
          label="变更数量"
          align="center">
          <template slot-scope="{row}">
            {{ row.changeSku > 0 ? '+' + row.changeSku : row.changeSku }}
          </template>
        </el-table-column>
        <el-table-column
          prop="resultSku"
          label="操作后库存"
          align="center"/>
        <el-table-column
          prop="employeeName"
          label="操作人"
          align="center"/>
      </el-table>

      <el-pagination
          @current-change="handleSkuLogCurrentChange"
          :current-page.sync="logPagination.current"
          :page-size="logPagination.size"
          layout="total, prev, pager, next"
          :total="logPagination.total"
      />
    </el-dialog>

    <el-pagination
      style="margin-top:20px;"
      @current-change="handleCurrentChange"
      :current-page.sync="pagination.current"
      :page-size="pagination.size"
      layout="total, prev, pager, next"
      :total="pagination.total"
    />
  </el-card>

  <!-- 选择产品弹窗 -->
  <el-dialog title="选择产品" :visible.sync="selectProductDialogVisible" width="70%" @close="handleSelectProductDialogClose">
    <div class="filter-container">
      <el-input
        v-model="productSearchForm.productName"
        placeholder="产品名称"
        style="width: 200px;"
        @keyup.enter.native="handleProductSearch"
      />
      <el-select
        v-model="productSearchForm.state"
        placeholder="状态"
        clearable
        style="width: 120px;margin-left:10px"
      >
        <el-option label="上架" :value="1" />
        <el-option label="下架" :value="2" />
      </el-select>
      <el-button type="primary" @click="handleProductSearch" style="margin-left:10px">搜索</el-button>
    </div>

    <el-table
      :data="productTableData"
      border
      style="width: 100%;margin-top:20px;"
    >
      <el-table-column prop="productId" label="ID" />
      <el-table-column prop="productName" label="产品名称" />
      <el-table-column label="主图">
        <template slot-scope="scope">
          <el-image
            :src="scope.row.mainImg"
            style="width:80px;height:80px"
            :preview-src-list="[scope.row.mainImg]"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button
            type="primary"
            size="mini"
            @click="handleSelectProduct(scope.row)"
          >选择</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      style="margin-top:20px;"
      @current-change="handleProductCurrentChange"
      :current-page.sync="productPagination.current"
      :page-size="productPagination.size"
      layout="total, prev, pager, next"
      :total="productPagination.total"
    />
  </el-dialog>

  <!-- 新增SKU弹窗 -->
  <el-dialog title="新增SKU" :visible.sync="addSkuDialogVisible" width="80%" @close="handleAddSkuDialogClose">
    <el-form ref="skuForm" :model="skuForm" label-width="80px">
      <div class="selected-product" v-if="selectedProduct">
        <div class="product-info">
          <span>已选产品：{{ selectedProduct.productName }}</span>
        </div>
      </div>

      <el-button class="add-attr-btn" type="primary" @click="addSkuAttrList">添加规格</el-button>
      <el-table :data="skuForm.skus" style="width: 100%" :header-cell-style="{ background: '#EEF3FF', color: '#333333' }">
        <el-table-column label="sku配图">
          <template slot-scope="scope">
            <ImageUpload :isShowTip="false" :limit="1" :model-value="scope.row.skuImage"
                         @input="val => handleSkuImageModelValueUpdate(val, scope.$index)" />
          </template>
        </el-table-column>
        <el-table-column label="规格">
          <template slot-scope="scope">
            <el-input v-model="scope.row.skuName" />
          </template>
        </el-table-column>
        <el-table-column label="售价">
          <template slot-scope="scope">
            <el-input-number v-model="scope.row.price" :controls="false" :max="999999999" :min="0" :precision="2" :step="0.01" />
          </template>
        </el-table-column>
        <el-table-column label="原价">
          <template slot-scope="scope">
            <el-input-number v-model="scope.row.originalPrice" :controls="false" :max="999999999" :min="0" :precision="2" :step="0.01" />
          </template>
        </el-table-column>
        <el-table-column label="总库存数">
          <template slot-scope="scope">
            <el-input-number v-model="scope.row.totalNum" :controls="false" :max="999999999" :min="0" :step="1" />
          </template>
        </el-table-column>
        <el-table-column label="当前库存数">
          <template slot-scope="scope">
            <el-input-number v-model="scope.row.stockNum" :controls="false" :max="999999999" :min="0" :step="1" />
          </template>
        </el-table-column>
        <el-table-column label="起购数量">
          <template slot-scope="scope">
            <el-input-number v-model="scope.row.min" :controls="false" :max="999999999" :min="0" :step="1" />
          </template>
        </el-table-column>
        <el-table-column label="单次限购数量">
          <template slot-scope="scope">
            <el-input-number v-model="scope.row.max" :controls="false" :max="999999999" :min="0" :step="1" />
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="danger" @click="removeSkuAttr(scope.$index)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="dialog-footer" style="margin-top: 20px;text-align: right;">
        <el-button @click="addSkuDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitSkuForm">确 定</el-button>
      </div>
    </el-form>
  </el-dialog>
</div>
</template>

<script>
import ImageUpload from "@/components/common/ImageUpload.vue";

export default {
  components: { ImageUpload },
  data() {
    return {
      logDialogVisible: false,
      logList: [],
      logPagination: {
        skuId:0,
        page: 1,
        limit: 10,
        total: 0
      },
      spanArr: [],
      tableData: [],
      searchForm: {
        productName: ''
      },
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      selectProductDialogVisible: false,
      addSkuDialogVisible: false,
      selectedProduct: null,
      productSearchForm: {
        productName: '',
        state: ''
      },
      productTableData: [],
      productPagination: {
        current: 1,
        size: 10,
        total: 0
      },
      skuForm: {
        product: {
          productId: null
        },
        skus: []
      }
    }
  },

  mounted() {
    this.getList()
  },

  computed: {
    processedData() {
      return this.tableData.reduce((acc, item) => {
        const existing = acc.find(i => i.productId === item.productId);
        if (existing) {
          existing.count++;
        } else {
          acc.push({...item, count: 1});
        }
        return acc;
      }, []);
    }
  },
  methods: {
    async handleOutbound(row) {
      try {
        const { value } = await this.$prompt('请输入出库数量', '提示', {
          inputPattern: /^[1-9]\d*$/,
          inputErrorMessage: '请输入正整数'
        });

        await this.$postData("mallSkuOutIn", {
          skuId: row.skuId,
          quantity: parseInt(value),
          type:2
        }.then(res =>{
          if (res.status === 200) {
            this.$message.success('出库成功');
            this.getList();
          } else {
            this.$message.error(res.msg);
          }
        }));

      } catch (e) {
        if (e !== 'cancel') {
          this.$message.error('操作失败');
        }
      }
    },
    async handleInbound(row) {
      try {
        const { value } = await this.$prompt('请输入入库数量', '提示', {
          inputPattern: /^[1-9]\d*$/,
          inputErrorMessage: '请输入正整数'
        });

        await this.$postData("mallSkuOutIn", {
          skuId: row.skuId,
          quantity: parseInt(value),
          type:1
        }.then(res =>{
          if (res.status === 200) {
            this.$message.success('入库成功');
            this.getList();
          } else {
            this.$message.error(res.msg);
          }
        }));

      } catch (e) {
        if (e !== 'cancel') {
          this.$message.error('操作失败');
        }
      }
    },
    getSpanArr(data) {
      this.spanArr = [];
      let pos = 0;
      data.forEach((item, index) => {
        if (index === 0) {
          this.spanArr.push(1);
          pos = 0;
        } else {
          if (data[index].productId === data[index - 1].productId) {
            this.spanArr[pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            pos = index;
          }
        }
      });
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        };
      }
    },
    getList() {
      const params = {
        ...this.searchForm,
        ...this.pagination
      }
      this.$postData("mallSkuList", params).then(res => {
        if (res.data) {
          this.tableData = res.data.records
          this.getSpanArr(res.data.records)
          this.pagination.total = res.data.total
        }
      })
    },

     handleLog(row) {
      this.logPagination.page = 1;
      this.logPagination.limit = 10;
      this.logPagination.total = 0;
      this.logDialogVisible = true
      this.logPagination.skuId = row.skuId
      this.getLogList();
    },
    async getLogList() {
      try {
        const res = await this.$postData('skuLogList', {
          skuId: this.logPagination.skuId,
          page: this.logPagination.page,
          limit: this.logPagination.limit
        })
        this.logList = res.data.records
        this.logPagination.total = res.data.total
      } catch (e) {
        this.$message.error(e.message)
      }
    },
    handleSearch() {
      this.pagination.current = 1
      this.getList()
    },

    handleCurrentChange(val) {
      this.pagination.current = val
      this.getList()
    },
    handleSkuLogCurrentChange(val) {
      this.logPagination.current = val
      this.getLogList()
    },
    handleAddSku() {
      this.selectProductDialogVisible = true;
      this.getProductList();
    },

    handleSelectProductDialogClose() {
      this.productSearchForm = {
        productName: '',
        state: ''
      };
      this.productTableData = [];
    },

    handleAddSkuDialogClose() {
      this.selectedProduct = null;
      this.skuForm = {
        product: {
          productId: null
        },
        skus: []
      };
    },

    getProductList() {
      const params = {
        ...this.productSearchForm,
        ...this.productPagination
      }
      this.$postData("mallProductList", params).then(res => {
        if (res.data) {
          this.productTableData = res.data.records;
          this.productPagination.total = res.data.total;
        }
      });
    },

    handleProductSearch() {
      this.productPagination.current = 1;
      this.getProductList();
    },

    handleProductCurrentChange(val) {
      this.productPagination.current = val;
      this.getProductList();
    },

    handleSelectProduct(row) {
      this.selectedProduct = row;
      this.skuForm.product.productId = row.productId;
      this.selectProductDialogVisible = false;
      this.addSkuDialogVisible = true;
    },

    addSkuAttrList() {
      this.skuForm.skus.push({
        productId: this.selectedProduct.productId,
        skuImage: '',
        skuName: '',
        price: 0,
        originalPrice: 0,
        totalNum: 0,
        stockNum: 0,
        min: 1,
        max: 999
      });
    },

    removeSkuAttr(index) {
      this.skuForm.skus.splice(index, 1);
    },

    handleSkuImageModelValueUpdate(value, index) {
      this.$set(this.skuForm.skus, index, {
        ...this.skuForm.skus[index],
        skuImage: value
      });
    },

    submitSkuForm() {
      if (this.skuForm.skus.length === 0) {
        this.$message.warning('请至少添加一个SKU规格');
        return;
      }

      // 验证必填字段
      for (let sku of this.skuForm.skus) {
        if (!sku.skuName || !sku.skuImage || !sku.price || !sku.originalPrice || !sku.totalNum || !sku.stockNum) {
          this.$message.warning('请填写完整的SKU信息');
          return;
        }
      }

      this.$postData("insertSku", this.skuForm).then(res => {
        if (res.status === 200) {
          this.$message.success('新增SKU成功');
          this.addSkuDialogVisible = false;
          this.getList();
          this.handleAddSkuDialogClose();
        } else {
          this.$message.error(res.msg || '新增失败');
        }
      });
    }
  }
}
</script>

<style scoped>
.filter-container {
  display: flex;
  gap: 10px;
}
.app-container {
  padding: 20px;
}
.product-group {
  background: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  margin: -8px -12px;
}
.selected-product {
  margin-bottom: 20px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}
.product-info {
  font-size: 14px;
  color: #606266;
}
.add-attr-btn {
  margin-bottom: 15px;
}
</style>
