<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="9">
            <el-form-item label="申请时间">
              <el-date-picker v-model="form.days" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="师傅工号">
              <el-input
                  clearable
                  v-model="form.masterNo"
                  placeholder="师傅工号"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="师傅名称">
              <el-input
                  clearable
                  v-model="form.masterName"
                  placeholder="师傅名称"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
            <!--            <el-button type="info"-->
            <!--                       :loading="loadingExcel"-->
            <!--                       @click="download"-->
            <!--                       icon="el-icon-download">导出-->
            <!--            </el-button>-->
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="徒弟名称">
              <el-input
                  clearable
                  v-model="form.apprenticeName"
                  placeholder="徒弟名称"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="徒弟工号">
              <el-input
                  clearable
                  v-model="form.apprenticeNo"
                  placeholder="徒弟工号"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="徒弟状态">
              <el-select style="width:160px" v-model="form.state" clearable placeholder="请选择徒弟状态">
                <el-option
                    v-for="item in manageTypeList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="提交类型">
              <el-select style="width:160px" v-model="form.type" clearable placeholder="请选择徒弟状态">
                <el-option
                    v-for="item in submitTypeList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item  label="状态">
              <el-select style="width:160px" v-model="form.logState" clearable placeholder="请选择状态">
                <el-option v-for="item in options"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="填报记录">
          <el-table :data="logList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column prop="createTime" label="申请时间" width="140"></el-table-column>
            <el-table-column prop="apprenticeNo" label="徒弟工号" width="70"></el-table-column>
            <el-table-column prop="apprenticeName" label="徒弟姓名" width="70"></el-table-column>
            <el-table-column prop="masterName" label="师傅姓名" width="70"></el-table-column>
            <el-table-column prop="masterNo" label="师傅工号" width="70"></el-table-column>
            <el-table-column prop="state" label="徒弟目前状态" width="95">
              <template slot-scope="scope">
                <span>{{scope.row.state==1?'未入职':scope.row.state==2?'已入职':'已上架'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="startTime" label="徒弟跟单时间" width="105"></el-table-column>
            <el-table-column prop="documentarySum" label="徒弟跟单数量" width="50"></el-table-column>
            <el-table-column prop="refuseReason" label="徒弟不打算在公司发展原因" width="100"></el-table-column>
            <el-table-column prop="logState" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.logState==1" type="warning">待处理</el-tag>
                <el-tag v-if="scope.row.logState==2" type="danger">不通过</el-tag>
                <el-tag v-else-if="scope.row.logState==3" type="success">已通过</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="entryQsA" label="徒弟跟单期间是否能主动联系师傅确认订单信息" width="100">
              <template slot-scope="scope">
                <span>{{scope.row.entryQsA==1?'是':scope.row.entryQsA==2?'否':''}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="entryQsB" label="徒弟跟单期间是否能主动帮忙和主动咨询问题" width="100">
              <template slot-scope="scope">
                <span>{{scope.row.entryQsB==1?'是':scope.row.entryQsB==2?'否':''}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="entryQsC" label="徒弟跟单期间是否熟练操作地图软件和公交路线查询" width="100">
              <template slot-scope="scope">
                <span>{{scope.row.entryQsC==1?'是':scope.row.entryQsC==2?'否':''}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="entryQsD" label="徒弟跟单期间现场沟通是否适度 (抱怨或话太多都选否)" width="100">
              <template slot-scope="scope">
                <span>{{scope.row.entryQsD==1?'是':scope.row.entryQsD==2?'否':''}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="entryQsE" label="徒弟跟单期间是否有迟到早退过" width="100">
              <template slot-scope="scope">
                <span>{{scope.row.entryQsE==1?'是':scope.row.entryQsE==2?'否':''}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="entryTime" label="入职办理时间" width="140"></el-table-column>
            <el-table-column prop="entryQsF" label="是否已告知徒弟入职需带的资料" width="100">
              <template slot-scope="scope">
                <span>{{scope.row.entryQsF==1?'已通知':scope.row.entryQsF==2?'未通知':''}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="activationQsA" label="徒弟是否熟悉员工端订单管理操作、地图查找" width="100">
              <template slot-scope="scope">
                <span>{{scope.row.activationQsA==1?'可以':scope.row.activationQsA==2?'不可以':''}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="activationQsB" label="徒弟是否熟练公司SOP流程，可以独立出单" width="100">
              <template slot-scope="scope">
                <span>{{scope.row.activationQsB==1?'可以':scope.row.activationQsB==2?'不可以':''}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="activationTime" label="上架办理时间" width="140"></el-table-column>
            <el-table-column prop="score" label="评分" width="80"></el-table-column>
            <el-table-column prop="type" label="提交类型" width="100">
              <template slot-scope="scope">
                <span>{{scope.row.type===1?'不继续发展':scope.row.type===2?'入职':'上架'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="operator" label="处理人" width="100"></el-table-column>
            <el-table-column prop="operatorTime" label="处理时间" width="140"></el-table-column>
            <el-table-column prop="remark" label="未通过原由" width="100"></el-table-column>
            <el-table-column label="操作" fixed="right" width="100">
              <template slot-scope="scope">
                <el-button :disabled="scope.row.logState===3" @click.native.prevent="showRow(scope.row.id,scope.row.type)" type="text" size="small">更新状态
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
        <el-dialog title="处理" :visible.sync="innerVisible" append-to-body center>
          <el-form>
            <el-form-item label="状态">
              <el-radio-group v-model="dom.logState">
                <el-radio label="2">不通过</el-radio>
                <el-radio label="3">通过</el-radio>
              </el-radio-group>
            </el-form-item>
            <br>
            <el-form-item v-if="dom.logState==='2'" label="未通过原由">
              <el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="dom.remark"> </el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="updateApprentice()">确定</el-button>
          </div>
        </el-dialog>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import moment from "moment";

export default {
  name: "register",
  data() {
    return {
      manageTypeList: [{id:1,name:"未入职"},{id:2,name:"已入职"},{id:3,name:"已上架"}],
      submitTypeList: [{id:1,name:"不继续发展"},{id:2,name:"入职"},{id:3,name:"上架"}],
      logList: [],
      handler: [],
      loadingExcel: false,
      innerVisible: false,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      form: {
        days: [],
        status: null,
        startTime: null,
        endTime: null,
        apprenticeNo: null,
        masterNo: null,
        apprenticeName: null,
        masterName: null,
        logState: null,
        state: null,
        type: null,
        current: 1,
        size: 10
      },
      dom: {
        logState: null,
        operator: localStorage.getItem("id"),
        id: null,
        type: null,
        remark: null,
      },
      options: [{
        value: '2',
        label: '未通过'
      }, {
        value: '1',
        label: '未处理'
      }, {
        value: '3',
        label: '已通过'
      }
      ],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },
  created() {
    this.getData();
  },
  methods: {
    download() {
      this.loadingExcel = true;
      this.$postData("downloadFranchiseRegister", this.form, {responseType: "arraybuffer"}).then(res => {
        this.loadingExcel = false;
        this.blobExport({
          tablename: "合伙人信息",
          res: res
        });
      })
    },
    blobExport({tablename, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tablename + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
    showRow(id,type) {
      this.dom.remark = ''
      this.innerVisible = true;
      this.dom.type = type
      this.dom.id = id;
    },
    updateApprentice() {
      console.log("this.dom",this.dom)
      this.$postData("updateApprentice", this.dom).then(res => {
        if (res.status === 200) {
          this.$message({message: '编辑成功', type: 'success'});
        } else {
          this.$message({message: '编辑失败', type: 'warning'});
        }
        this.getData();
      })
      this.innerVisible = false;
    },
    // 页码大小
    onPageSizeChange(size) {
      this.form.size = size;
      this.getData();
    },
    onChange(index) {
      this.form.current = index;
      this.getData();
    },
    getData() {
      this.form.startTime = null;
      this.form.endTime = null;
      if (this.form.days != null && this.form.days.length > 0) {
        this.form.startTime = moment(this.form.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.form.days[1]).format("YYYY-MM-DD")
      }
      this.$postData("getApprenticeData", this.form, {}).then(res => {
        if (res.status == 200) {
          res.data.records.forEach(v => {
            if(v.startTime){
              v.startTime = v.startTime+"至"+ v.endTime
            }
          })
          this.logList = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    }
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
