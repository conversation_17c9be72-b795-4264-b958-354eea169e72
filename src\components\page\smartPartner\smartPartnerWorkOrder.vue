<template>
  <div class="table">
    <el-tabs type="card" @tab-click="handleClick">
      <el-tab-pane label="区域商务经理"/>
      <el-tab-pane label="标准交付"/>
      <el-tab-pane label="三嫂交付"/>
      <el-tab-pane label="联盟运营"/>
      <el-tab-pane label="区域督导"/>
      <el-tab-pane label="培训老师"/>
      <el-tab-pane label="私域"/>
      <el-tab-pane label="流量"/>
      <el-tab-pane label="技术"/>
      <el-tab-pane label="设计"/>
      <el-tab-pane label="售后"/>
      <el-tab-pane label="财务"/>
      <el-tab-pane label="行政"/>
    </el-tabs>
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="8">
            <el-form-item label="会话时间">
              <el-date-picker v-model="days" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                              align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="负责人员">
              <el-input
                  clearable
                  v-model="form.adminSearch"
                  placeholder="请输入姓名、工号"
                  style="width:150px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="所属区域">
              <el-select style="width:160px" v-model="form.department" clearable placeholder="请选择所属区域">
                <el-option v-for="item in typeOptions"
                           :key="item.value" :label="item.label" :value="item.label">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="大区名称">
              <el-input
                  clearable
                  v-model="form.regionName"
                  placeholder="请输入大区名称"
                  style="width:350px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="回复时效">
              <el-select style="width:160px" v-model="timeType" placeholder="请选择" @change="changeTimeType">
                <el-option
                    v-for="item in timeTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <div style="font-size: 25px;font-weight: bold;color: red">总会话数：{{ sessionSumCount }}</div>
          </el-col>
          <el-col :span="4">
            <div style="font-size: 25px;font-weight: bold;color: red">总会话记录数：{{ sessionLogSumCount }}</div>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="会话记录">
          <el-table v-loading="loading" :data="logList" :header-cell-style="{background:'#ddd'}" border class="table"
                    ref="multipleTable">
            <el-table-column prop="department" label="所属区域" width="140"></el-table-column>
            <el-table-column prop="regionName" label="大区名称" width="180"></el-table-column>
            <el-table-column prop="realName" label="负责人员" width="180"></el-table-column>
            <el-table-column prop="employeeNo" label="人员工号" width="160"></el-table-column>
            <el-table-column prop="sessionCount" label="会话数" width="160"
                             :render-header="renderHeader"></el-table-column>
            <el-table-column prop="sessionLogCount" label="会话记录数" width="180"
                             :render-header="renderHeader"></el-table-column>
            <el-table-column v-if="timeType==1" prop="agingMinute" label="回复时效(分/次)" width="160"
                             :render-header="renderHeader"></el-table-column>
            <el-table-column v-if="timeType==2" prop="agingHour" label="回复时效(时/次)" width="160"
                             :render-header="renderHeader"></el-table-column>
            <el-table-column label="会话详情">
              <template slot-scope="scope">
                <el-tag type="primary" @click="getStoreRegionDetails(scope.row)">点击查看</el-tag>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-dialog
        title="会话详情"
        :visible.sync="detailsDialog"
        width="60%">
      <el-table v-loading="detailsLoading" :data="storeRegionDetails" :header-cell-style="{background:'#ddd'}" border
                class="table" ref="multipleTable">
        <el-table-column
            prop="storeName"
            label="门店名称"
            width="200"
        ></el-table-column>
        <el-table-column
            prop="sessionCount"
            label="会话数"
            width="189"
        ></el-table-column>
        <el-table-column
            prop="sessionLogCount"
            label="会话记录数"
            width="200"
        ></el-table-column>
        <el-table-column v-if="timeType==1" prop="agingMinute" label="回复时效(分/次)" width="160"></el-table-column>
        <el-table-column v-if="timeType==2" prop="agingHour" label="回复时效(时/次)" width="160"></el-table-column>
        <el-table-column label="会话记录">
          <template slot-scope="scope">
            <el-tag type="primary" @click="getSessionChatLog(scope.row)">点击查看</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <Page :total="detailsPageInfo.total"
              @on-change="detailsOnChange"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="detailsOnPageSizeChange"
              :page-size="detailsPageInfo.size"/>
      </div>
    </el-dialog>

    <el-dialog
        @close="closeSession"
        title="会话记录"
        :visible.sync="logDialog"
        width="60%">
      <div v-for="item in sessionChatLog">
        <div style="font-size: 20px;text-align: center;font-weight: bold;padding-bottom: 10px">
          {{ item.realName }}({{ item.employeeNo }})
        </div>
        <div v-for="val in item.partnerChatLogs" :key="val.id">
          <!-- 门店方 -->
          <div v-if="val.initiator" style="padding-bottom: 20px;margin-right: 60%">
            <div>{{ val.createTime }}</div>
            <div v-if="val.msgType==1">{{ val.messageData }}</div>
            <div v-if="val.msgType==2" v-for="url in val.imgInvoiceUrlList">
              <el-image
                  style="width: 150px; height: 200px"
                  :src="url"
                  :preview-src-list="val.imgInvoiceUrlList">
              </el-image>
            </div>
          </div>

          <!-- 管理方 -->
          <div v-if="!val.initiator" style="padding-bottom: 20px;text-align: right;margin-left: 60%">
            <div>{{ val.createTime }}</div>
            <div v-if="val.msgType==1">{{ val.messageData }}</div>
            <div v-if="val.msgType==2" v-for="url in val.imgInvoiceUrlList">
              <el-image
                  style="width: 150px; height: 200px"
                  :src="url"
                  :preview-src-list="val.imgInvoiceUrlList">
              </el-image>
            </div>
          </div>


        </div>
      </div>
    </el-dialog>

    <el-link :href="hrefUrl" target="_blank" id="elLink"/>

  </div>
</template>

<script>
import moment from "moment";
import log from "echarts/src/scale/Log";

export default {
  name: "smartPartnerWorkOrder",
  data() {
    return {
      timeType: '1',
      timeTypeOptions: [{
        value: '1',
        label: '分/次'
      }, {
        value: '2',
        label: '时/次'
      }],
      getType: 0,
      sessionLogSumCount: 0,
      sessionSumCount: 0,
      logList: [],
      storeRegionDetails: [],
      roleId: localStorage.getItem('roleId'),
      hrefUrl: "",
      blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
      detailsDialog: false,
      logDialog: false,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      detailsPageInfo: {total: 10, size: 10, current: 1, pages: 1},
      days: [],
      sessionChatLog: [],
      loading: true,
      detailsLoading: true,
      detailsForm: {
        employeeNo: '',
        id: '',
        current: 1,
        size: 10
      },
      form: {
        getType: '0',
        startTime: '',
        endTime: '',
        regionName: '',
        adminSearch: '',
        department: '',
        current: 1,
        size: 10
      },
      options: [],
      typeOptions: [{
        value: '1',
        label: '中部'
      }, {
        value: '2',
        label: '东部'
      }, {
        value: '3',
        label: '北部'
      }, {
        value: '4',
        label: '南部'
      }, {
        value: '5',
        label: '西部'
      }],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },

  created() {
    this.getData();
  },
  methods: {
    closeSession() {
      this.detailsDialog = true
    },
    getSessionChatLog(item) {
      this.logDialog = true
      this.detailsDialog = false
      this.$getData("getSessionChatLog", {employeeNo: this.detailsForm.employeeNo, id: item.id}, {}).then(res => {
        if (res.code == 0) {
          this.sessionChatLog = res.data
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    },
    changeTimeType() {
    },
    renderHeader(h, {
      column,
      index
    }) {
      let tips = "暂无提示"
      if (column.label == "会话数") {
        tips = "有效会话，发起后存在回复的次数"
      } else if (column.label == "会话记录数") {
        tips = "会话中产生的总记录数"
      } else if (column.label == "回复时效(分/次)") {
        tips = "回复间隔总时长/有效会话次数，999999: 从未回复过，0: 无会话记录或1分钟内回复"
      } else if (column.label == "回复时效(时/次)") {
        tips = "回复间隔总时长/有效会话次数，999999: 从未回复过，0: 无会话记录或1小时内回复"
      }
      return h('div', [
        h('span', column.label),
        h('el-tooltip', {
              undefined,
              props: {
                undefined,
                effect: 'dark',
                placement: 'top',
                content: tips
              },
            },
            [
              h('i', {
                undefined,
                class: 'el-icon-question',
                style: "color:#409eff;margin-left:5px;cursor:pointer;"
              })
            ],
        )
      ]);
    },
    handleClick(tab) {
      this.form.getType = tab.index
      this.getData()
    },
    getStoreRegionDetails(item) {
      this.detailsDialog = true;
      this.detailsLoading = true;
      this.detailsForm.employeeNo = item.employeeNo
      this.detailsForm.id = item.id
      this.$getData("getStoreRegionDetails", this.detailsForm, {}).then(res => {
        this.detailsLoading = false
        if (res.code == 0) {
          this.storeRegionDetails = res.data.records;
          this.detailsPageInfo.current = res.data.current;
          this.detailsPageInfo.size = res.data.size;
          this.detailsPageInfo.total = res.data.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    },
    // 页码大小
    onPageSizeChange(size) {
      this.form.size = size;
      this.getData();
    },
    onChange(index) {
      this.form.current = index;
      this.getData();
    },
    detailsOnChange(index) {
      this.detailsForm.current = index;
      this.getStoreRegionDetails();
    },
    // 页码大小
    detailsOnPageSizeChange(size) {
      this.detailsForm.size = size;
      this.getStoreRegionDetails();
    },
    getData() {
      this.loading = true
      this.form.startTime = '';
      this.form.endTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      this.$getData("getStoreRegionData", this.form, {}).then(res => {
        this.loading = false
        if (res.code == 0) {
          this.logList = res.data.storeRegionData.records;
          this.sessionSumCount = res.data.sessionSumCount
          this.sessionLogSumCount = res.data.sessionLogSumCount
          this.pageInfo.current = res.data.storeRegionData.current;
          this.pageInfo.size = res.data.storeRegionData.size;
          this.pageInfo.total = res.data.storeRegionData.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    }
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
