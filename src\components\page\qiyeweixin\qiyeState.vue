<template>
  <div>
    <div ref="imageDom">
      <el-form v-loading="loading" ref="form" :model="dom" label-width="100px" style="background:#FFFFFF"
               label-position="right">
        <el-form-item label="分组" required>
          <el-select v-model="dom.qiyeChannelGroupById" value-key="id" filterable clearable placeholder="所属分组">
            <el-option
                v-for="item in grouping"
                :key="item.id"
                :label="item.name"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型" required>
          <el-radio-group v-model="dom.type">
            <el-radio-button :label="1">单人</el-radio-button>
            <el-radio-button :label="2">多人</el-radio-button>
            <el-radio-button :label="3">批量单人</el-radio-button>
          </el-radio-group>
          注：人数最多不超过100人
        </el-form-item>
        <!--        <el-form-item v-if="dom.type===2" label="种类" required>-->
        <!--          <el-radio-group v-model="type">-->
        <!--            <el-radio-button :label="1">按人员</el-radio-button>-->
        <!--            <el-radio-button :label="2">按部门</el-radio-button>-->
        <!--          </el-radio-group>-->
        <!--          -->
        <!--        </el-form-item>-->
        <el-form-item label="活码名称" required>
          <el-input v-model="dom.name" style="width:245px"></el-input>
        </el-form-item>
        <el-form-item label="客户标签" required>
          <ul>
            <li class="tags-li" v-for="(item,index) in qiyeState.label" :key="index">
              <span id="">
                {{ item }}
              </span>
              <span class="tags-li-icon" @click="closeTags(index)"><i class="el-icon-close"></i></span>
            </li>
          </ul>
          <el-button type="primary" @click="dialogVisible = true" plain icon="el-icon-plus">添加标签</el-button>
        </el-form-item>
        <el-form-item label="部门" required>
          <el-select v-model="dom.party" value-key="id" filterable clearable @change="getPartyUser" placeholder="使用部门">
            <el-option
                v-for="item in partys"
                :key="item.id"
                :label="item.name"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="人员" v-if="type!==2" required>
          <el-select v-model="dom.user" filterable multiple placeholder="使用人员" @change="changeSele">
            <el-option v-for="item in users"
                       :key="item.id"
                       :label="item.name"
                       :value="item.userid">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="回复类型" required>
          <el-checkbox-group v-model="qiyeState.type">
            <el-checkbox-button :label="0">文本</el-checkbox-button>
            <el-checkbox-button :label="1">图片</el-checkbox-button>
            <el-checkbox-button :label="2">图文</el-checkbox-button>
            <el-checkbox-button :label="3">小程序</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="文本" v-if="qiyeState.type.indexOf(0)!==-1" required>
          <el-input type="textarea" placeholder="请输入内容"
                    :autosize="{ minRows:3,}" v-model="qiyeState.content"
                    maxlength="2000" style="width:300px"></el-input>
          <el-divider></el-divider>
        </el-form-item>
        <el-form-item label="图片" v-if="qiyeState.type.indexOf(1)!==-1" required>
          <el-card class="box-card" shadow="always">
            <div class="text item" @click="selectPicture = true">
              <i style=" font-size: 40px; " class="el-icon-plus" v-if="qiyeState.pic_url===null"></i>
              <el-image class="image" :src=qiyeState.pic_url fit="contain" v-if="qiyeState.pic_url!==null"></el-image>
            </div>
          </el-card>
          <el-divider></el-divider>
          <el-dialog title="选择图片" :visible.sync="selectPicture" width="70%" append-to-body>
            <pictureSelection :choiceImg="choiceImg" @picture="picture"></pictureSelection>
          </el-dialog>
        </el-form-item>
        <el-form-item label="图文" v-if="qiyeState.type.indexOf(2)!==-1" required>
          <el-card class="box-card" shadow="always">
            <div class="text item" @click="selectGraphics = true">
              <i style=" font-size: 40px; " class="el-icon-plus" v-if="qiyeState.picurl===null"></i>
              <el-image class="image" :src=qiyeState.picurl fit="contain"
                        v-if="qiyeState.picurl!==null"></el-image>
            </div>
          </el-card>
        </el-form-item>
        <el-form-item label="选择小程序" v-if="qiyeState.type.indexOf(3)!==-1" required>
          <el-card class="box-card" shadow="always">
            <div class="text item" @click="selectApplet = true">
              <i style=" font-size: 40px; " class="el-icon-plus" v-if="qiyeState.sparePicture===null"></i>
              <el-image class="image" :src=qiyeState.sparePicture fit="contain"
                        v-if="qiyeState.sparePicture!==null"></el-image>
            </div>
          </el-card>
        </el-form-item>
        <el-dialog title="选择标签" :visible.sync="dialogVisible" width="550px" append-to-body>
          <div v-for="item in qiyeLabelGroup">
            <el-form-item :label="item.group_name">
              <el-checkbox-group v-model="qiyeState.label" value-key="id">
                <el-checkbox-button v-for="(val,index) in qiyeLabel" v-show="val.qiyeLabelGroupId===item.id"
                                    :key="index"
                                    :label="val.name"
                                    @change="change(val.id)">{{ val.name }}
                </el-checkbox-button>
              </el-checkbox-group>
            </el-form-item>
          </div>
          <div style="margin-top: 15px;">
            <el-row type="flex" justify="center">
              <el-col :span="3">
                <el-button @click="dialogVisible = false" size="large">取 消</el-button>
              </el-col>
              <el-col :span="3"></el-col>
              <el-col :span="3">
                <el-button type="success" size="large" @click="dialogVisible = false">确定</el-button>
              </el-col>
            </el-row>
          </div>
        </el-dialog>
        <el-dialog title="选择图文" :visible.sync="selectGraphics" width="70%" append-to-body>
          <graphicsContent :choiceGraphics="choiceImg" @graphicsContent="graphicsContent"></graphicsContent>
        </el-dialog>
        <el-dialog title="选择小程序" :visible.sync="selectApplet" width="70%" append-to-body>
          <applet :choiceImg2="choiceImg" @applet="applet"></applet>
        </el-dialog>
        <br>
        <el-form-item>
          <el-button type="primary" @click="addQiyeState">建立活码</el-button>
        </el-form-item>
        <br>
      </el-form>
    </div>
  </div>
</template>

<script>
//引入组件
import pictureSelection from "./pictureSelection";
import applet from "./applet";
import graphicsContent from "./graphicsContent";

export default {
  name: "qiyeState",
  props: {
    qiyeChannelGroupById: null,
    reset2: {
      type: Function,
      default: null,
    },
  },
  // 注册组件
  components: {
    pictureSelection,
    applet,
    graphicsContent,
  },
  data() {
    return {
      choiceImg: true,
      loading: false,
      //种类
      type: null,
      dialogVisible: false,
      selectPicture: false,
      selectGraphics: false,
      selectApplet: false,
      clickUpload: true,
      //部门
      partys: [],
      //分组
      grouping: [],
      //人员
      users: [],
      dom: {
        name: null,
        //单人、多人
        type: null,
        //使用该联系方式的部门id列表，只在type为2时有效
        party: null,
        //用该联系方式的用户userID列表，在type为1时为必填，且只能有一个
        user: [],
        //1-在小程序中联系，2-通过二维码联系
        scene: 2,
        userName: [],
        partyName: null,
        creatorId: null,
        qiyeChannelGroupById: null,
      },
      pic_url: [],
      imgList: [],
      qiyeState: {
        label: [],
        labelId: [],
        type: [],
        content: null,
        pic_url: null,
        title: null,
        picurl: null,
        desc: null,
        url: null,
        name: null,
        miniprogramTitle: null,
        pic_media_id: null,
        appid: null,
        page: null,
        sparePicture: null,
        isOnce: false,
      },
      //标签组
      qiyeLabelGroup: {
        group_id: [],
        group_name: [],
      },
      //标签
      qiyeLabel: {
        id: [],
        name: [],
        qiyeLabelGroupId: [],
      },
    };
  },
  watch: {
    qiyeChannelGroupById(newValue, oldValue) {

      this.dom.qiyeChannelGroupById = newValue
    }
  },
  created() {
    this.dom.creatorId = localStorage.getItem("id")
    this.getParty()
    this.getQiyeLabelGroup()
    this.getQiyeLabel()
    this.selectQiyeChannelGroupBy()
  },
  methods: {
    // 删除单个标签
    closeTags(index) {
      this.qiyeState.label.splice(index, 1)[0];
      this.qiyeState.labelId.splice(index, 1)[0];
    },
    reset() {
      this.dom.name = null
      this.dom.party = null
      this.dom.user = []
      this.type = null
      this.qiyeState.label = []
      this.qiyeState.labelId = []
      this.qiyeState.type = null
      this.qiyeState.content = null
      this.qiyeState.pic_url = null
      this.qiyeState.title = null
      this.qiyeState.picurl = null
      this.qiyeState.desc = null
      this.qiyeState.url = null
    },
    //建立
    async addQiyeState() {
      this.loading = true;
      if (null == this.dom.qiyeChannelGroupById) {
        this.loading = false;
        return this.$message({message: '分组不能为空', type: 'warning'});
      }
      if (null == this.dom.type) {
        this.loading = false;
        return this.$message({message: '类型不能为空', type: 'warning'});
      }
      if (null == this.qiyeState.label) {
        this.loading = false;
        return this.$message({message: '标签不能为空', type: 'warning'});
      }
      if (null == this.dom.party) {
        this.loading = false;
        return this.$message({message: '部门不能为空', type: 'warning'});
      }
      if (null == this.dom.name) {
        this.loading = false;
        return this.$message({message: '名称不能为空', type: 'warning'});
      }
      if (this.dom.type === 1 && this.dom.user.length > 1) {
        this.loading = false;
        return this.$message({message: '类型与人数不一致', type: 'warning'});
      }
      if (null == this.qiyeState.type) {
        this.loading = false;
        return this.$message({message: '回复类型不能为空', type: 'warning'});
      }
      this.qiyeState.label = this.qiyeState.label.join(",");
      this.qiyeState.labelId = this.qiyeState.labelId.join(",");
      this.dom.userName = this.dom.userName.join(",");
      if (this.qiyeState.type.indexOf(3) !== -1) {
        await this.picMediaId()
      } else {
        this.insertQiyeState()
      }
    },
    picMediaId() {
      this.$getData("pic_media_id", {sparePicture: this.qiyeState.sparePicture}).then(res => {
        if (res.status === 200) {
          this.qiyeState.pic_media_id = res.data
          this.insertQiyeState()
        }
      })
    },
    //标签组
    getQiyeLabelGroup() {
      this.$getData("getQiyeLabelGroup",).then(res => {
        if (res.status === 200) {
          this.qiyeLabelGroup = res.data
        }
      })
    },
    //标签
    getQiyeLabel() {
      this.$getData("getQiyeLabel",).then(res => {
        if (res.status === 200) {
          this.qiyeLabel = res.data
        }
      })
    },
    insertQiyeState() {
      this.qiyeState.type = this.qiyeState.type.join(",");
      let qiyeStateDto = {
        "qiyeState": this.qiyeState,
        "qiyeStates": this.dom,
      }
      this.$postData("insertQiyeState", qiyeStateDto).then(res => {
        if (res.status === 200) {
          if (res.data === 1) {
            this.loading = false;
            this.$message({message: '添加成功', type: 'success'});
            this.$emit("childByvalue", false)
          } else if (res.data === 40098) {
            this.loading = false;
            this.$message({
              showClose: true,
              message: '添加失败:有成员尚未实名认证',
              type: 'error'
            });
          } else {
            this.loading = false;
            this.$message({
              showClose: true,
              message: '添加失败:错误代码' + res.data,
              type: 'error'
            });
          }
          this.loading = false;
          this.reset()
          if (this.reset2) {
            this.reset2();
          }
        }
      })
    },
    //部门
    getParty() {
      this.$getData("getParty",).then(res => {
        if (res.status === 200) {
          this.partys = res.data
        }
      })
    },
    //分组
    selectQiyeChannelGroupBy() {
      this.$getData("selectQiyeChannelGroupBy",).then(res => {
        if (res.status === 200) {
          this.grouping = res.data
        }
      })
    },
    //人员
    getPartyUser(id) {
      this.partys.find((v) => {
        if (v.id === id) {
          this.dom.partyName = v.name
        }
      });
      this.$getData("getPartyUser", {id: id}).then(res => {
        if (res.status === 200) {
          this.users = res.data
        }
      })
    },
    changeSele(val) {
      this.dom.userName = [];
      val.forEach(e => {
        this.users.find((v) => {
          if (v.userid === e) {
            this.dom.userName.push(v.name);
          }
        });
      })
    },
    //标签
    change(id) {
      if (this.qiyeState.labelId.indexOf(id) >= 0) {
        this.qiyeState.labelId.splice(this.qiyeState.labelId.indexOf(id), 1);
      } else {
        this.qiyeState.labelId.push(id);
      }
    },
    getQiyeChannelQiyeState(id) {
      this.$getData("getQiyeChannelQiyeState", {id: id}).then(res => {
        if (res.status === 200) {
          this.qiyeState = res.data
          this.dom = res.data
        }
      })
    },
    //文件上传限制
    uploadSectionFile(param) {
      let fileObj = param.file;
      const isLt2M = fileObj.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
        return;
      }
      if (fileObj.type === "image/jpg") {
        this.pic_url = new File([fileObj], new Date().getTime() + ".jpg", {
          type: "image/jpg"
        });
      } else if (fileObj.type === "image/png") {
        this.pic_url = new File([fileObj], new Date().getTime() + ".png", {
          type: "image/png"
        });
      } else if (fileObj.type === "image/jpeg") {
        this.pic_url = new File([fileObj], new Date().getTime() + ".jpeg", {
          type: "image/jpeg"
        });
      } else {
        this.$message.error("只能上传jpg/png文件");
        return;
      }
    },
    //选择图片
    picture(val) {
      this.qiyeState.pic_url = val
      this.selectPicture = false
    },
    applet(app) {
      this.qiyeState.miniprogramTitle = app.miniprogramTitle;
      this.qiyeState.pic_media_id = app.pic_media_id;
      this.qiyeState.appid = app.appid;
      this.qiyeState.page = app.page;
      this.qiyeState.sparePicture = app.sparePicture;
      this.selectApplet = false;
    },
    graphicsContent(graphics) {
      this.qiyeState.title = graphics.title;
      this.qiyeState.picurl = graphics.picurl;
      this.qiyeState.desc = graphics.desc;
      this.qiyeState.url = graphics.url;
      this.selectGraphics = false;
    },
  }
}
</script>

<style scoped>
.text {
  font-size: 10px;
  text-align: center;
}

.item {
  padding: 10px 0;
}

.box-card {
  width: 180px;
}


</style>