<template>
  <div style=" border: 1px solid #eee;width: 100%">
    <el-button type="primary" @click="dialogVisibleUpload = true">视频上传</el-button>
    <el-table
        :data="tableData"
        >
      <el-table-column
          prop="title"
          label="标题"
          width="100">
      </el-table-column>
      <el-table-column
          prop="description"
          label="描述"
          width="100">
      </el-table-column>
      <el-table-column
          prop="createTime"
          label="创建时间"
          width="160">
      </el-table-column>
      <el-table-column
          prop="cateName"
          label="视频分类"
          width="150">

      </el-table-column>
      <el-table-column
          label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.videoState==0" type="danger">禁用</el-tag>
          <el-tag v-if="scope.row.videoState==1" type="info">审核中</el-tag>
          <el-tag v-if="scope.row.videoState==2" type="success">审核成功</el-tag>
        </template>
      </el-table-column>
      <el-table-column

          label="上下架">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.showState==1" type="primary">上架</el-tag>
          <el-tag v-if="scope.row.showState==2" type="danger">下架</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          label="查看视频">
        <template slot-scope="scope">
          <el-button @click="showVideo(scope.row.videoId)" type="primary">查看视频</el-button>
        </template>
      </el-table-column>
      <el-table-column
          label="操作">
        <template slot-scope="scope">

          <el-button v-if="scope.row.showState == 2" @click="setVideoState(scope.row.id,1)" type="primary">上架</el-button>
          <el-button v-if="scope.row.showState == 1" @click="setVideoState(scope.row.id,2)" type="warning">下架</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
        title="视频上传"
        :visible.sync="dialogVisibleUpload"
        width="40%">
      <el-form ref="form" :rules="rules" :model="form" label-width="80px">
        <el-form-item label="视频标题" prop="title">
          <el-input v-model="form.title"></el-input>
        </el-form-item>
        <el-form-item label="视频描述" prop="description">
          <el-input type="textarea" v-model="form.description"></el-input>
        </el-form-item>
        <el-form-item label="分类">
          <el-cascader
              :options="items"
              @change="handleChange"></el-cascader>
        </el-form-item>
        <el-form-item label="视频">
          <el-upload
              :data="form"
              :accept="acceptFile('video')"
              ref="upload"
              action="https://api.xiaoyujia.com/employee/uploadVod"
              :on-remove="handleRemove"
              :file-list="fileList"
              :limit="1"
              :on-success="successFile"
              :auto-upload="false"></el-upload>
        </el-form-item>
      </el-form>


      <span slot="footer" class="dialog-footer">
        <el-button @click="submitForm('form')">上传</el-button>
  </span>
    </el-dialog>


<!--    <el-dialog-->
<!--        title="视频"-->
<!--        :visible.sync="dialogVisibleVideo"-->
<!--        width="40%">-->
<!--      <video>-->
<!--        <source :src="videoUrl">-->
<!--      </video>-->
<!--    </el-dialog>-->


  </div>
</template>

<script>
export default {
  name: "employeeVideo",
  data() {
    return {
      // dialogVisibleVideo:false,
      tableData: [],
      dialogVisibleUpload: false,
      cateList: [],
      fileList: [],
      items: [],
      selected: [],
      videoUrl:'',
      form: {
        title: '',
        description: '',
        employeeId: localStorage.getItem("id"),
        cateId: '',
        cateName: '',
      },
      rules: {
        title: [{required: true, message: '请输入活动名称', trigger: 'blur'},],
        description: [{required: true, message: '请输入活动名称', trigger: 'blur'},],
      }
    }
  },
  created() {
    this.getData();
    this.getCateIdList()
  },
  methods: {
    setVideoState(id,state){
      this.$postData("setVideoState", {id:id,showState:state}, {}).then(res => {
          if ( res.data && res.data > 0){
             this.$message.success('操作成功');
             this.getData();
          } else {
            this.$message.error('操作失败');
          }
      })
    },
    successFile(res, file){
      if (res.code === 0){
        this.$refs['form'].resetFields();
        this.fileList = [];
        this.dialogVisibleUpload = false;
        this.getData();
        return this.$message.success('上传成功');

      } else {
        return this.$message.error('上传失败-'+res.msg);
      }
    },
    getCascaderObj(val, opt) {
      return val.map(function (value, index, array) {
        for (var itm of opt) {
          if (itm.value == value) {
            opt = itm.children;
            return itm;
          }
        }
        return null;
      });
    },
    acceptFile(e) {
      const allowHook = {
        video: '.mp4, .ogv, .ogg, .webm',
        audio: '.wav, .mp3, .ogg, .acc, .webm, .amr',
        file: 'doc,.docx,.xlsx,.xls,.pdf',
        excel: 'xlsx,.xls',
        img: '.jpg, .jpeg, .png, .gif'
      }
      if (e) {
        return allowHook[e];
      }
      let srt = null
      for (const k in allowHook) {
        srt += allowHook[k]
      }
      return srt
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handleChange(e) {
      this.selected = this.getCascaderObj(e, this.items); //选中节点数据
      let index = this.selected.length - 1;
      this.form.cateId = this.selected[index].value;
      let text = '';
      this.selected.forEach(v => {
        text = text + '-' + v.label
      });
      text = text.substr(1);
      this.form.cateName = text;
      console.log(JSON.stringify(this.form))
    },
    getData() {
      let roleId = localStorage.getItem("roleId")
      let param = {}
      if (roleId != 1){
        param.employeeId = this.form.employeeId;
      }
      this.$postData("pvUploadVideoList", param, {}).then(res => {
        this.tableData = res.data;
      })
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (!valid) {
          console.log('error submit!!');
          return false;
        } else {

          if (!this.form.cateId) {
            return this.$message.error('请选择上传分类');
          }
          this.$refs.upload.submit();


        }
      });
    },
    showVideo(videoId) {
      this.$getData("pvVideoShow",{videoId:videoId}).then(res =>{
          if (res.code !==0){
               return this.$message.error('获取视频失败');
          } else {
            window.open(res.data.playInfoList[0].playURL)
            // this.dialogVisibleVideo = true;

          }
      })
    },
    getCateIdList() {
      this.$getData("pvVideoCateIdList", {}).then(res => {
        this.cateList = res.data;
        if (this.cateList.length > 0) {
          this.cateList.forEach(v => {
            let obj = {};
            let chi = [];
            obj.label = v.cateName,
                obj.value = v.cateId,

                v.subCategories.forEach(r => {
                  let subdata = {};
                  subdata.label = r.cateName;
                  subdata.value = r.cateId;
                  chi.push(subdata);
                })
            obj.children = chi;
            this.items.push(obj);
          })
        }
        // this.cateList = res.data;
      })
    }

  },
}
</script>

<style scoped>

</style>
