<template>
	<div class="container">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">会员列表</el-menu-item>
			<!-- <el-menu-item index="1">数据统计</el-menu-item> -->
		</el-menu>

		<!-- 搜索栏目 -->
		<div v-if="choiceIndexNow==0">
			<div class="handle-box" style="margin: 20px 20px 20px 0px;">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="会员姓名、电话等"></el-input>
							</el-form-item>
						</el-col>

						<el-col :span="3">
							<el-form-item label="注册来源" style="margin-right: 20px">
								<el-select v-model="quer.registerSource" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in registerSourceList" :key="index"
										:label="item.name" :value="item.id"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="danger" @click="recordDownload()">导出</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">

				<el-table-column prop="id" width="80" label="编号">
				</el-table-column>

				<el-table-column width="130" prop="name" label="会员昵称">
					<template slot-scope="scope">
						<span>{{scope.row.name||'匿名用户'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="realName" label="员工姓名">
					<template slot-scope="scope">
						<span>{{scope.row.realName||'-'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="storeName" label="门店名称">
					<template slot-scope="scope">
						<span>{{scope.row.storeName||'-'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="125" prop="headImg" label="会员头像">
					<template slot-scope="scope">
						<img :src="scope.row.headImg||blankImg" style="width: 100px;height: 100px;"
							@click="openImg(scope.row.headImg||blankImg)">
					</template>
				</el-table-column>

				<el-table-column width="130" prop="sexName" label="会员性别">
					<template slot-scope="scope">
						<span>{{scope.row.sexName||'-'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="remark" label="会员备注">
					<template slot-scope="scope">
						<span>{{formatLongStr(scope.row.remark)}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="jiaBiAmount" label="积分余额" sortable>
					<template slot-scope="scope">
						<span>{{scope.row.jiaBiAmount||0}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" label="注册来源" prop="registerSource">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.registerSource==item.id" v-for="(item,index) in registerSourceList"
							:key="index" :type="formatTypeStyle(item.id)">
							{{item.name}}
						</el-tag>
					</template>
				</el-table-column>

				<el-table-column fixed="right" min-width="200" label="操作">
					<template slot-scope="scope">
						<el-button @click="openModal(1,scope.$index)" type="success" size="small">积分发放
						</el-button>
						<el-button type="warning" size="small" style="margin-left: 10px"
							@click="openModal(0,scope.$index)">余额明细</el-button>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<div v-if="choiceIndexNow==1">
			<el-button @click="openData()" type="success" size="small">查看详细统计</el-button>
		</div>

		<div class="pagination" v-if="choiceIndexNow==0">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--会员基本信息-->
		<el-drawer size="60%" :with-header="false" :visible.sync="memberIntegralModal" direction="rtl">
			<div v-if="choiceItem">
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">会员基本信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="会员昵称：">
								<span>{{choiceItem.name||'匿名会员'}}</span>
							</el-form-item>
						</el-form>
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="员工姓名：">
								<span>{{choiceItem.realName||'-'}}</span>
							</el-form-item>
						</el-form>
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="手机号：">
								<span>{{choiceItem.bindTel||'-'}}</span>
							</el-form-item>
						</el-form>
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="门店名称：">
								<span>{{choiceItem.storeName||'-'}}</span>
							</el-form-item>
						</el-form>
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="会员头像：">
								<img :src="choiceItem.headImg||blankImg" style="width: 100px;height: 100px;"
									@click="openImg(choiceItem.headImg||blankImg)">
							</el-form-item>
						</el-form>
					</el-col>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="积分余额：">
								<span>{{choiceItem.jiaBiAmount||0}}</span>
							</el-form-item>
						</el-form>
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="会员性别：">
								<span>{{choiceItem.sexName||'-'}}</span>
							</el-form-item>
						</el-form>
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="会员备注：">
								<span>{{formatLongStr(choiceItem.remark)}}</span>
							</el-form-item>
						</el-form>

						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="注册来源：">
								<el-tag v-if="choiceItem.registerSource==item.id" :type="formatTypeStyle(choiceItem.id)"
									v-for="(item,index) in registerSourceList" :key="index">
									{{item.name}}
								</el-tag>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<el-button @click="openModal(1,choiceItemIndex)" type="success" size="small"
					style="margin: 0px 40px 20px 40px;">积分发放
				</el-button>

				<h2 style="margin: 0 40px;">积分余额明细-{{jiabiDetailList.length?jiabiDetailList.length+'条':'暂无'}}</h2>
				<el-form ref="form">
					<el-table :data="jiabiDetailList" style="width: 100%;margin: 0 40px;">
						<el-table-column fixed prop="changeTypeName" label="变更类型" width="120">
						</el-table-column>
						<el-table-column fixed prop="oldAmount" label="变更前" width="120">
						</el-table-column>
						<el-table-column fixed prop="changeAmount" label="变更数量" width="120" sortable>
							<template slot-scope="scope">
								<span
									:style="scope.row.changeAmount<0?'color:#ff4d4b':'color:#19be6b'">{{scope.row.changeAmount}}</span>
							</template>
						</el-table-column>
						<el-table-column fixed prop="amount" label="变更后" width="120">
						</el-table-column>
						<el-table-column fixed prop="remark" label="备注" width="140">
							<template slot-scope="scope">
								<span>{{scope.row.remark||'-'}}</span>
							</template>
						</el-table-column>
						<el-table-column fixed prop="creatorName" label="操作人" width="140">
							<template slot-scope="scope">
								<span>{{scope.row.creatorName||'-'}}</span>
							</template>
						</el-table-column>
						<el-table-column fixed prop="createTime" label="时间" width="140" sortable>
						</el-table-column>
					</el-table>
				</el-form>

				<div style="margin: 20px 400px;width: 100%;">
					<el-button @click="memberIntegralModal=false" type="primary" size="small">关闭
					</el-button>
					<!-- 		<el-button @click="updateMemberIntegral(choiceItem)" type="success" size="small"
						v-if="detailType==1">保存
					</el-button> -->
				</div>
			</div>
		</el-drawer>

		<el-dialog :visible.sync="editModal" width="40%" title="积分发放" :mask-closable="false"
			v-if="choiceIndexNow==0&&choiceItem">
			<div><span style="font-size: 20px;font-weight: bold;">会员信息</span></div>
			<div><span>会员姓名：{{choiceItem.name||'匿名会员'}}</span></div>
			<div><span>员工姓名：{{choiceItem.realName||'-'}}</span></div>
			<div><span>门店名称：{{choiceItem.storeName||'-'}}</span></div>
			<div><img :src="choiceItem.headImg||blankImg" style="width: 100px;height: 100px;"
					@click="openImg(choiceItem.headImg||blankImg)"></span></div>


			<div>
				发放事由：
				<el-input v-model="remark" style="width: 100%;" type="textarea" placeholder="请输入发放事由说明"
					:autosize="{ minRows: 4, maxRows: 10}">
				</el-input>
			</div>
			<div>
				发放额度：
				<el-input v-model="changeAmount" style="margin: 20px 0;width: 20%;" type="number"
					placeholder="请输入发放的积分额度" :min="1" :max="10000">
				</el-input>
			</div>

			<div style="margin: 20px 0;">
				<span style="color: #ff4d4b;">* 发放后立即到账，不可撤回，请检查【会员信息】及【发放额度】！</span>
			</div>
			<div>
				<el-button @click="updateJiaBi()" type="success" round size="small" icon="el-icon-edit">确定发放
				</el-button>
			</div>
		</el-dialog>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>
	</div>

</template>

<script>
	import Vue from 'vue';

	export default {
		name: "memberIntegral",

		data() {
			return {
				// 可设置
				// 是否超级管理员（可执行删除等敏感操作）
				isAdmin: false,

				url: '',
				imageUrl: '',
				videoUrl: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				// 文件上传地址
				fileUploadUrl: 'https://api.xiaoyujia.com/system/uploadFile',
				fileUploadData: {
					route: 'memberIntegral'
				},
				uploadFileType: 0,

				uploadUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
				deleteTips: "确定删除选中课程评价吗？该操作无法恢复，请谨慎操作!",
				imgModal: false,
				editModal: false,
				memberIntegralModal: false,
				detailType: 0,
				loading: true,
				pageInfo: {
					total: 10,
					size: 20,
					current: 1,
					pages: 1
				},
				pageSizeOpts: [20, 50, 100],
				list: [],
				jiabiDetailList: [],
				courseList: [],
				keyList: [],
				choiceIndex: '0',
				choiceIndexNow: '0',
				choiceItemIndex: 0,
				choiceItem: null,
				choiceRecordId: 0,
				changeAmount: 1,
				remark: '',
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				signInTips: '',
				searchText: '',
				searchState: null,
				searchRequired: null,
				quer: {
					search: "",
					courseId: null,
					commentState: null,
					orderBy: "t.id DESC",
					current: 1,
					size: 20
				},
				switchStateList: [{
						id: 0,
						typeName: "关闭",
						type: "info"
					},
					{
						id: 1,
						typeName: "开启",
						type: "success"
					}
				],
				stateList: [{
					value: 0,
					text: "隐藏",
					type: "info"
				}, {
					value: 1,
					text: "显示",
					type: "success"
				}],
				typeStyleList: [{
						id: 0,
						type: "success"
					},
					{
						id: 1,
						type: "info"
					},
					{
						id: 2,
						type: "warning"
					}, {
						id: 3,
						type: "primary"
					}
				],
				registerSourceList: [{
					id: 1,
					name: '电话'
				}, {
					id: 2,
					name: '微信'
				}, {
					id: 3,
					name: 'APP'
				}, {
					id: 4,
					name: '小程序'
				}, {
					id: 5,
					name: '水滴智店'
				}, {
					id: 6,
					name: '抖音'
				}, {
					id: 7,
					name: 'H5'
				}, {
					id: 8,
					name: '支付宝'
				}],
				searchTime: [],
				pickerOptions: {
					shortcuts: [{
						text: '昨天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '今天',
						onClick(picker) {
							picker.$emit('pick', new Date());
						}
					}, {
						text: '明天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周前',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周后',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}]
				},
				pickerOptions1: {
					shortcuts: [{
						text: '今天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '昨天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}]
				}
			}
		},
		created() {
			if (this.$route.query.isAdmin) {
				this.isAdmin = true
			}
			this.getData()
		},
		methods: {
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceItem = null
				this.choiceIndex = key
				this.expands = []
				this.reset()
			},
			openData() {
				this.$router.push({
					path: '/courseList?choiceIndex=2'
				})
			},
			getData() {
				this.loading = true
				this.choiceIndexNow = 2
				if (this.choiceIndex == 0) {
					this.$postData("pageMemberIntegral", this.quer, {}).then(res => {
						if (res.status == 200) {
							this.loading = false
							this.list = res.data.records
							this.pageInfo.current = res.data.current
							this.pageInfo.size = res.data.size
							this.pageInfo.total = res.data.total
							this.choiceIndexNow = this.choiceIndex
						} else {
							this.list = []
							this.$message.error('未查询到相关会员信息!')
							this.choiceIndexNow = this.choiceIndex
						}
					})
				} else if (this.choiceIndex == 1) {
					this.choiceIndexNow = this.choiceIndex
				}
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.quer.commentState = this.searchState
				this.getData()
			},
			// 重置
			reset() {
				this.loading = true
				this.quer.current = 1
				this.quer.search = ""
				this.quer.registerSource = null

				this.searchText = ''
				this.searchState = null
				this.getData()
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.choiceRecordId = row.id
					}
				} else {
					that.expands = [] // 默认不展开
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "状态") {
					tips = "用户是否能够查看该评价"
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			formatLongStr(str) {
				if (!str) {
					return "暂无"
				} else {
					let long = 40
					if (str.length > long) {
						str = str.substring(0, long) + "..."
					}
					return str
				}
			},
			formatDuration(duration, defaultStr) {
				if (!defaultStr) {
					defaultStr = '暂未录入'
				}
				let result = ""
				let sencond = 0
				let min = 0
				let hour = 0
				let day = 0
				// 小于一分钟
				if (duration <= 60) {
					sencond = Math.floor(parseFloat(duration / 1.0))
					if (sencond == 0) {
						result = defaultStr
					} else {
						result = sencond + "秒"
					}
				}

				// 小于二小时
				if (duration > 60 && duration <= 3600) {
					min = Math.floor(parseFloat(duration / 60.0))
					sencond = Math.floor(duration - (min * 60)).toFixed(0)
					if (min == 0) {
						result = defaultStr
					} else {
						result = min + "分钟" + sencond + "秒"
					}
				}
				// 大于二小时 小于一天
				if (duration > 3600 && duration <= 86400) {
					hour = Math.floor(parseFloat(duration / 3600))
					min = parseFloat((duration - (hour * 3600)) / 60).toFixed(0)
					if (min == 0) {
						result = hour + "小时"
					} else {
						result = hour + "小时" + min + "分钟"
					}
				}
				// 大于一天
				else if (duration > 86400) {
					day = Math.floor(parseFloat(duration / 86400))
					hour = parseFloat((duration - (day * 86400)) / 3600).toFixed(0)
					if (hour == 0) {
						result = day + "天"
					} else {
						result = day + "天" + hour + "小时"
					}
				}
				return result
			},
			formatTypeStyle(type) {
				return this.typeStyleList[type % 4].type
			},
			// 联盟试题模板下载
			recordDownload() {
				this.$postData("pageMemberIntegralDownload", this.quer, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('会员积分余额记录下载成功!')
					this.blobExport({
						tablename: "会员积分余额记录",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 课程评价Excel模板下载
			excelDownload() {
				this.$postData("excelDownload", null, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('课程评价Excel模板下载成功!')
					this.blobExport({
						tablename: "课程评价Excel模板",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.$set(this.choiceItem, "videoImg", res.data)
				} else if (this.uploadImgType == 1) {
					this.$set(this.choiceItem, "groupImg", res.data)
				} else if (this.uploadImgType == 2) {
					this.$set(this.choiceItem, "groupIcon", res.data)
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadId = index
				let tips = ''
				if (this.uploadImgType == 0) {
					tips = "推荐尺寸：750*1000（3：4）"
				} else if (this.uploadImgType == 1) {
					tips = '推荐上传尺寸：250*250（1：1）'
				} else if (this.uploadImgType == 2) {
					tips = '推荐上传尺寸：250*250（1：1）'
				}
				this.$message.info(tips)
			},
			// 打开图片预览
			openImg(url) {
				this.imageUrl = url || this.blankImg
				this.imgModal = true
			},
			// 打开视频预览
			openVideo(url) {
				if (url != null && url != '') {
					this.videoUrl = url
				}
				this.videoModal = true
			},
			openModal(index, index1) {
				if (index == 0) {
					this.detailType = 1
					this.choiceItem = this.list[index1]
					this.memberIntegralModal = true
					this.getJiaBiDetail(this.list[index1].id)
				} else if (index == 1) {
					this.detailType = 1
					this.choiceItem = this.list[index1]
					this.editModal = true
				}
			},
			//打开员工信息详情
			rowClick(id) {

			},
			updateJiaBi() {
				if (!this.remark) {
					return this.$message.error('请填写发放事由！')
				}

				$.ajax({
					type: "POST",
					url: 'https://api.xiaoyujia.com/jiabi/updateJiaBi',
					data: JSON.stringify({
						memberId: this.choiceItem.id,
						changeAmount: this.changeAmount,
						changeType: 1,
						remark: this.remark,
						creator: localStorage.getItem('account') || 'admin'
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.$message.success('积分发放成功！')
							this.editModal = false
							this.choiceItem.jiaBiAmount += this.changeAmount
							this.changeAmount = 1
						} else {
							this.$message.error(res.msg)
						}
					},
				})
			},
			getJiaBiDetail(memberId) {
				$.ajax({
					type: "POST",
					url: 'https://api.xiaoyujia.com/jiabi/getJiaBiDetail',
					data: JSON.stringify({
						memberId: memberId
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.code == 0) {
							this.jiabiDetailList = res.data
						} else {
							this.jiabiDetailList = []
						}
					},
				})
			},
			beforeFileUpload() {
				this.loading = true
			},
			// 章节素材文件上传成功
			fileUploadSuccess(res, file) {
				this.loading = false
				if (this.uploadFileType == 0) {
					this.choiceItem.videoUrl = res.data
				}
				this.$message.success('课程评价文件上传成功！')
			},
			// 章节素材文件上传失败
			fileUploadError(err) {
				this.loading = false
				this.$message.error('课程评价文件上传失败！请重试！' + err.msg)
			},
			// 课程章节素材文件上传
			fileUpload(value) {
				this.uploadFileType = value
				this.fileUploadUrl = fileUploadUrl
			},
			beforeImgUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!');
				}
				return isLt2M;
			}
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}

	/* 隐藏el-scrollbar的横向滚动条 */
	.el-scrollbar {
		.el-scrollbar__wrap {
			overflow-x: hidden;

			.el-scrollbar__view {
				height: 100%;
			}
		}
	}

	.el-select-dropdown .el-scrollbar {
		padding-bottom: 17px;
	}

	.el-select-dropdown .el-scrollbar.is-empty {
		padding-bottom: 0;
	}

	.el-cascader__suggestion-panel.el-scrollbar {
		padding-bottom: 17px;
	}

	.el-cascader__dropdown .el-cascader-panel .el-scrollbar {
		padding-bottom: 17px;
	}
</style>