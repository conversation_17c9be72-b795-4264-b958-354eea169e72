<template>
    <div style="background:#fff;padding: 20px">
        <div class="memberInfo">
            <img :src="memberInfo.headImg" width="50" height="50" style="border-radius: 50px;border: 2px solid #ddd;margin-right: 50px">

            <h2>用户名：{{memberInfo.name}}</h2>
            <h2>账号：{{memberInfo.account}}</h2>
            <h2>手机号：{{memberInfo.bindTel}}</h2>
            <h2>注册时间：{{memberInfo.registerTime}}</h2>
<!--            {{memberInfo}}-->
        </div>

        <div style="padding: 20px">
            <el-radio v-model="memberInfo.lblId" :label="item.id" border  v-for="(item,index) in lbList" :key="index" @change="changeLabel">{{item.name}}</el-radio>
        </div>
        <el-tabs type="border-card">
            <el-tab-pane label="客户日志">
                <el-collapse v-model="activeNames" >
                    <el-collapse-item title="新增保姆日志" name="1">
                        <template slot="title">
                            <el-button type="primary" >添加客户日志</el-button>
                        </template>
                        <el-form ref="form" :model="baomuWorkLog">
                            <el-form-item   prop="remark">
                                <div class="label-name">标题:&nbsp
                                    <el-button size="mini" v-for="(i,index) in titleList" @click="baomuWorkLog.title=i" :key="index">{{i}}</el-button>
                                </div>
                                <el-input placeholder="请输入标题" v-model="baomuWorkLog.title" style="width: 70%" >
                                </el-input>
                            </el-form-item >
                            <el-form-item   prop="workContent">
                                <div class="label-name">内容:</div>
                                <el-input placeholder="请输入工作内容" type="textarea" v-model="baomuWorkLog.workContent" style="width: 70%">
                                </el-input>
                            </el-form-item >

                            <el-form-item   prop="remark">
                                <div class="label-name">备注:</div>
                                <el-input placeholder="请输入备注" v-model="baomuWorkLog.remark" style="width: 70%">
                                </el-input>
                            </el-form-item >
                            <el-form-item   prop="startWorkTime">
                                <div class="label-name">开始时间:</div>
                                <el-date-picker
                                        style="width: 70%"
                                        v-model="baomuWorkLog.startTime"
                                        type="date"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        placeholder="开始时间">
                                </el-date-picker>
                            </el-form-item >
                            <el-form-item   prop="endWorkTime" label="">
                                <div class="label-name">结束时间:</div>
                                <el-date-picker
                                        style="width: 70%"
                                        v-model="baomuWorkLog.endTime"
                                        type="date"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        placeholder="结束时间">
                                </el-date-picker>
                            </el-form-item >


                            <div style="margin-right: 100px;float: right">
                                <Button type="primary" @click="save('form')">确定</Button>
                            </div>
                        </el-form>
                    </el-collapse-item>
                </el-collapse>
                <br>
                <el-timeline :reverse="true" v-show="false">
                    <el-timeline-item v-for="(activity, index) in list"
                                      :key="index"
                                      :timestamp="activity.startTime" placement="top">
                        <el-card>
                            <h4> {{activity.title}}-{{activity.contractNo}}</h4>
                            <p>内容：{{activity.workContent}}</p>
                            <p>操作人：{{activity.crePerson}}</p>
                            <p>时间：{{activity.startTime}}  --  {{activity.endTime}}</p>
                            <p>备注：{{activity.remark}}</p>
                            <br>
                            <el-button type="danger" plain v-if="activity.id!==null" @click="del(activity.id)">删除</el-button>
                        </el-card>
                    </el-timeline-item>

                </el-timeline>

                <div style="float: right">
                    <el-checkbox-group v-model="checkboxGroup1" @change="changeCheck">
                        <el-checkbox-button v-for="city in cities" :label="city.value" :key="city.value">{{city.name}}</el-checkbox-button>
                    </el-checkbox-group>
                </div>
                <el-table
                        v-loading="show"
                        :data="showList"
                        style="width: 100%">
                    <el-table-column
                            prop="title"
                            label="日志标题"
                            width="180">
                    </el-table-column>
                    <el-table-column
                            prop="workContent"
                            label="内容"
                            width="580"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="startTime"
                            label="开始时间">
                    </el-table-column>
                    <el-table-column
                            prop="endTime"
                            label="结束时间">
                    </el-table-column>
                    <el-table-column
                            prop="crePerson"
                            label="操作人">
                    </el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="服务记录">
                <h3 class="head-title">服务记录</h3>
                <el-table
                        height="400"
                        :data="serviceList"
                        style="width: 100%">
                    <el-table-column
                            prop="billNo"
                            label="订单编号">
                    </el-table-column>
                    <el-table-column
                            prop="productName"
                            label="服务产品"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="serviceName"
                            label="服务人员">
                    </el-table-column>
                    <el-table-column
                            prop="serviceNo"
                            label="服务人员工号">
                    </el-table-column>
                    <el-table-column
                            prop="startTime"
                            label="服务时间">
                    </el-table-column>

                </el-table>
            </el-tab-pane>
            <el-tab-pane label="客户标签">

                <el-button type="primary" block @click="saveMemberTags()"   :loading="btnLoading">保存客户标签</el-button>

                <div v-for="(item,index) in tags" :key="index" class="tag-box">
                    <div class="tag-title">{{item.name}}</div>
                    <el-row>
                        <div v-for="(tag,index2) in item.memberTagList">
                            <el-col :span="8" >
                                <div :class="tag.status==1?'tag-act':'tag'" @click="(tag.status==null||tag.status==0)?tag.status=1:tag.status=0">{{tag.name}}</div>
                            </el-col>
                        </div>
                    </el-row>
                </div>
            </el-tab-pane>

            <el-tab-pane label="回访记录">
                <el-collapse v-model="activeNames" >
                    <el-collapse-item :title="task.title+' ( '+ task.employeeName+' ) - '+task.creatTime" :name="task.title" :label="task.id" v-for="(task,index) in taskList" :key="index">
                        <div class="addBody" style="min-height: 400px;width: 50%;transform:scaleX(0.8);-webkit-transform:scaleX(0.8);  -moz-transform:scaleX(0.8);border: 1px solid #ddd;padding: 15px">
                            <responseInfo :id="task.id"></responseInfo>
                        </div>
                    </el-collapse-item>
                </el-collapse>
<!--                <el-tabs tab-position="left" type="border-card">-->
<!--                    <el-tab-pane :label="task.title" v-for="(task,index) in taskList" :key="index" @click="showRow(task)">-->

<!--                        <div class="addBody" style="min-height: 400px;">-->
<!--                            <responseInfo :id="task.id"></responseInfo>-->
<!--                        </div>-->
<!--                    </el-tab-pane>-->
<!--                </el-tabs>-->
            </el-tab-pane>
        </el-tabs>




    </div>

</template>

<script>
    import responseInfo from '@/components/page/agent/info/responseInfo.vue'
    const cityOptions = [
        {value:0,name:'需求线索'},
        {value:1,name:'订单记录'},
        {value:2,name:'合同记录'},
        {value:3,name:'补签合同'},
        {value:4,name:'年卡订单'},
        ];
    export default {
        props:['mid'],
        name: "memberInfo",
        components: {
            'responseInfo': responseInfo,
        },
        data() {
            return {
                getId:this.mid,
                activeNames:[],
                show:null,
                taskList:[],
                dom:{
                    employeeId:localStorage.getItem("id"),
                    memberId:this.$route.query.id,
                    tagIds:[],
                },
                btnLoading:false,
                tags:null,
                memberId:this.$route.query.id,
                show:true,
                showList:[],
                lbList:[],
                serviceList:[],
                checkboxGroup1:[0,1,2,3,4 ],
                cities:cityOptions,
                id:this.$route.query.id,
                list:[],
                baomuWorkLog:{
                    id:null,
                    memberId:this.$route.query.id,
                    workContent:null,
                    remark:null,
                    status:1,
                    billNo:null,
                    contractNo:null,
                    startTime:null,
                    endTime:null,
                    creatTime:null,
                    crePerson:localStorage.getItem("realName"),
                    type:0,
                    title:null,

                },
                memberInfo:{},
                activeNames:[],
                titleList:["上户面试","恶意行为"],
                labelId:null,
                user:{
                    id:localStorage.getItem("id"),
                    storeId:localStorage.getItem("storeId"),
                    roleId:localStorage.getItem("roleId"),
                }

            }
        },
        created: function () {
            if (this.getId!==null && this.getId!=='' && this.getId!==undefined ){
                this.memberId=this.getId
                this.id=this.getId
                this.dom.memberId=this.getId
            }
            this.getData()
            this.getMemberTags()
            this.getTaskData()
        },
        methods:{
            showRow(row){
                this.show=row.id
            },
            getTaskData() {
                let dom={
                    employeeId:null,
                    storeId:null,
                    status:1,
                    memberId:this.memberId
                }
                if (this.user.roleId!=='1'){
                    if (this.user.roleId !=='66' || this.user.roleId !=='12'){
                        dom.employeeId=this.user.id
                    }else {
                        dom.storeId=this.user.storeId
                    }
                }

                console.log(dom)
                this.$postData("taskList", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.taskList = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            saveMemberTags(){
                this.btnLoading=true
                this.dom.tagIds=[]
                this.tags.forEach(v=>{
                    v.memberTagList.forEach(h=>{
                        if (h.status==1){
                            this.dom.tagIds.push(h.id)
                        }
                    })
                })
                if (this.dom.tagIds.length==0){
                    this.btnLoading=false
                    return  this.$message.error("请至少标记一个标签");
                }
                console.log(this.dom)

                this.$postData("saveMemberTagBind", this.dom, null).then(res => {
                    if (res.status == 200) {
                        this.getMemberTags()
                        this.$message.success("更新成功");
                    } else {

                        this.$message.error("更新失败，" + res.msg);

                    }
                    this.btnLoading=false
                })
            },
            getMemberTags(){

                this.$getUrl("memberTagDtoLists", this.memberId, null).then(res => {
                    if (res.status == 200) {
                        this.tags=res.data
                    } else {

                        this.$message.error("查询失败，" + res.msg);

                    }
                })

            },
            getTime(){
                var date1=new Date();
                var year=date1.getFullYear();
                var month=date1.getMonth()+1;
                var day=date1.getDate();
                var hours=date1.getHours();
                var minutes=date1.getMinutes();
                var seconds=date1.getSeconds();
                return year+"-"+month+"-"+day+"  "+hours+":"+minutes+":"+seconds
            },
            changeLabel(label) {
                this.$confirm('此操作将更新客户级别, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let old=null
                    let newLa="'"+'无'+"'"
                    this.lbList.forEach(v=>{
                        if (v.id==label){
                            newLa="'"+v.name+"'"
                        }
                        if (v.id==this.labelId){
                            old="'"+v.name+"'"
                        }
                    })
                    let baomuWorkLog={
                            id:null,
                            memberId:this.$route.query.id,
                            workContent:"客户级别由"+old+"更改为"+newLa,
                            remark:null,
                            status:1,
                            billNo:null,
                            contractNo:null,
                            startTime:this.getTime(),
                            endTime:null,
                            creatTime:this.getTime(),
                            crePerson:localStorage.getItem("realName"),
                            type:0,
                            title:"更新客户级别",

                        }
                        let member={
                            id:this.$route.query.id,
                            lblId:label,
                        }
                    this.$postData("memberUpdateById", member, {}).then(res => {
                        if (res.status == 200) {

                        } else {
                           return  this.$Message.error('更新失败');
                        }
                    })
                    this.$postData("saveMemberLog", baomuWorkLog, {}).then(res => {
                        if (res.status == 200) {
                            this.$Message.success('更新成功');
                            this.activeNames=[]
                            this.getData()
                        } else {
                            this.$message.error("保存失败，" + res.msg);
                        }
                    })

                }).catch(() => {

                   this.memberInfo.lblId= this.labelId
                    this.$message({
                        type: 'info',
                        message: '已取消更新'
                    });
                });
            },
            changeCheck(value){
                console.log(value)
                this.showList=[]
                if (value.length>0){
                    value.forEach(v=>{
                        this.list.forEach(vl=>{
                            console.log(v)
                            if (v==vl.type){
                                this.showList.push(vl)
                            }
                            if (vl.type==null){
                                this.showList.push(vl)
                            }
                        })
                    })
                }
            },
            save(name) {
                if (this.baomuWorkLog.title==null || this.baomuWorkLog.workContent==null || this.baomuWorkLog.startTime==null ){
                    return  this.$Message.error('请将内容填写完整');
                }
                this.$postData("saveMemberLog", this.baomuWorkLog, {}).then(res => {
                    if (res.status == 200) {
                        this.$Message.success('保存成功');
                        this.activeNames=[]
                        this.getData()
                    } else {
                        this.$message.error("保存失败，" + res.msg);
                    }
                })
            },
            del(id){
                this.$getData("delMemberLog",{id : id}).then(res => {
                    if (res.status == 200) {

                        this.getData()

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getData() {
                this.show=true

                this.$getData("selectMemberLabel",null).then(res => {
                    if (res.status == 200) {
                        this.lbList=res.data



                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                this.$getData("memberById",{id : this.id}).then(res => {
                    if (res.status == 200) {


                        this.memberInfo = res.data;
                        this.labelId=res.data.lblId

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })

                this.$getData("getServiceByMemberId",{id : this.id}).then(res => {
                    if (res.status == 200) {

                        this.serviceList = res.data;

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                this.$getData("getMemberLog",{employeeId : this.id}).then(res => {
                    if (res.status == 200) {

                        this.list = res.data;
                        this.showList = this.list;
                        this.show=false
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            /*
        * 关闭当前窗口
        * */
            chooseThisModel(name) {
                this.id=null;
                this.$emit('init-choose', "");
            },
        }
    }
</script>
<style  scoped>
    .btn{

        width: 96%;
        padding: 2%;
        background: white;
        position: fixed;
        bottom: 0;
    }
    .tag-act{
        border-radius: 1.2rem;
        text-align: center;
        font-size: 12px;
        margin: 0.2rem;
        border: 1px solid #1677FF;
        padding: 0.2rem 0;
        color: #1677FF;
        background: #eef5ff;
    }
    .tag{
        border-radius: 1.2rem;
        text-align: center;
        font-size: 12px;
        margin: 0.2rem;
        border: 1px solid #ddd;
        padding: 0.2rem 0;
        color: #929292;
    }
    .tag-box{
        padding: 0.2rem;
    }
    .tag-title{
        border-left: 0.1rem solid #1677FF;
        padding-left: 0.2rem;
        background: #eef5ff;
        padding: 0.1rem;
        color: #2b2b2b;
        margin-bottom: 0.2rem;
    }
    .head-title{
        border-left: 5px solid #409eff;
        padding-left: 20px;
    }
    .memberInfo h2{
        display: inline-block;
        margin-right: 50px;
    }
</style>
