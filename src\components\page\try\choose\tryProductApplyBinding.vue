<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col span="24">
                    <div>
                        <div id="operBtnDiv">
                            <div id="searchDiv">
                                <Form ref="formItem" :model="formItem" :label-width="80" label-position="right"
                                      :rules="ruleValidate">
                                    <FormItem label="会员" >
                                        <el-input
                                                v-model="formItem.name" readonly="readonly"
                                                placeholder="会员"
                                                style="width:190px"
                                                class="handle-input mr10"
                                        ></el-input>
                                    </FormItem>
                                    <FormItem label="优惠券" prop="couponGroupId">
                                        <el-input
                                                v-model="formItem.couponName"
                                                placeholder="请选择优惠券"
                                                style="width:190px"
                                                class="handle-input mr10"
                                                @focus="couponModal=true"
                                        ></el-input>
                                    </FormItem>

                                    <div style="margin-left: 80px">
                                        <Button type="primary" @click="save('formItem')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </div>
                                </Form>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
        <Modal v-model="couponModal" class="Modal" :width="screenWidth" title="选择优惠券">
            <div class="addBody">

                <coupon-choose v-if="couponModal" @init-choose="initChooseProject"
                                  @close-modal="closeCurrModal"></coupon-choose>
            </div>
            <div slot="footer">
                <el-button @click="closeCurrModal">取消</el-button>
                <el-button type="primary"  @click="setCoupon">确定</el-button>
            </div>
        </Modal>
    </div>
</template>


<script>
    import couponChoose from '@/components/page/try/choose/couponChoose.vue'
    export default {
        props:['binds'],
        data() {
            return {
                couponModal:false,
                screenWidth: '50%',
                formItem: {
                    couponGroupId:null,
                    name:this.binds.account+"  "+this.binds.name,
                    applyId:this.binds.tryProductApplyId,
                    couponName:null,
                    publishNum:null,
                    validity:null,
                    memberId:this.binds.memberId
                },
                ruleValidate: {
                    couponGroupId: [
                        {required: true, message: '请选择优惠券', trigger: 'change',type:"number"}
                    ],
                },
                coupons:null,
            }
        },
        components: {
            "couponChoose":couponChoose
        },
        created: function () {

        },
        methods: {
            save(name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        if(this.formItem.couponGroupId){
                            this.$confirm('此操作存在风险性, 是否继续?', '提示', {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }).then(() => {
                                this.$postData("applyCoupon_binding", this.formItem, {}).then(res => {
                                    if (res.status == 200) {
                                        this.$Message.success('绑定成功');
                                        this.chooseThisModel()
                                    } else {
                                        this.$message.error("绑定失败，" + res.msg);
                                    }
                                })

                            }).catch(() => {

                            });

                        }else {
                            this.$Message.success('请联系管理员');
                        }

                    }
                })
            },
            initChooseProject(data) {
              console.log(data)
                this.coupons=data;
            },
            setCoupon(){
                this.formItem.couponGroupId=this.coupons.id;
                this.formItem.couponName=this.coupons.name;
                this.formItem.publishNum=this.coupons.publishNum;
                this.formItem.validity=this.coupons.validity;
                this.closeCurrModal();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.couponModal = false; //
            },

                /*
          * 关闭当前窗口
          * */
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            },

        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

