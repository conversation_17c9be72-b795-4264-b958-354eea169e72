<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="9">
            <el-form-item label="营业时间">
              <el-date-picker v-model="days" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="项目名称">
              <el-input
                  clearable
                  v-model="form.storeName"
                  placeholder="请输入项目名称"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="项目经理">
              <el-input
                  clearable
                  v-model="form.storeManager"
                  placeholder="请输入项目经理"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
            <el-button type="info"
                       style="margin-bottom: 15px"
                       @click="exportAllStoreSettlementData"
                       icon="el-icon-download">导出
            </el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="流量经理">
              <el-input
                  clearable
                  v-model="form.storeTraffic"
                  placeholder="请输入流量经理"
                  style="width:160px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="技术支持">
              <el-input
                  clearable
                  v-model="form.support"
                  placeholder="请输入技术支持"
                  style="width:160px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="M单交付鉴定">
              <el-input
                  clearable
                  v-model="form.storeGrader"
                  placeholder="请输入M单交付鉴定"
                  style="width:170px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="标准单交付鉴定">
              <el-input
                  clearable
                  v-model="form.gemmologist"
                  placeholder="请输入标准单交付鉴定"
                  style="width:170px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="陪跑项目列表">
          <el-table v-loading="loading" :data="logList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column prop="id" label="门店Id" width="60"></el-table-column>
            <el-table-column prop="storeName" label="项目名称" width="180"></el-table-column>
            <el-table-column prop="storeManager" label="项目经理" width="100"></el-table-column>
            <el-table-column prop="storeTraffic" label="流量经理" width="100"></el-table-column>
            <el-table-column prop="support" label="技术支持" width="100"></el-table-column>
            <el-table-column prop="storeGrader" label="M单交付鉴定" width="100"></el-table-column>
            <el-table-column prop="gemmologist" label="标准单交付鉴定" width="110"></el-table-column>
            <el-table-column prop="openingDay" label="开业时长" width="70"></el-table-column>
            <el-table-column prop="morderSumMoney" label="M单营业额" width="100"></el-table-column>
            <el-table-column prop="abcOrderSumMoney" label="标准单营业额" width="100"></el-table-column>
            <el-table-column prop="sumMoney" label="营业额" width="100"></el-table-column>
            <el-table-column prop="baoMuIdentifyNum" label="保姆鉴定上架人数" width="120"></el-table-column>
            <el-table-column prop="abcIdentifyNum" label="标准单鉴定上架人数" width="130"></el-table-column>
            <el-table-column prop="morderNum" label="M单结算单量" width="95"></el-table-column>
            <el-table-column prop="abcOrderNum" label="标准单结算单量" width="105"></el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-link :href="hrefUrl" target="_blank" id="elLink"/>
  </div>
</template>

<script>
import moment from "moment";
export default {
  name: "register",
  data() {
    return {
      logList: [],
      loading: true,
      roleId: localStorage.getItem('roleId'),
      hrefUrl: "",
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      days: [],
      form: {
        gemmologist: '',
        support: '',
        startTime: '',
        endTime: '',
        storeManager: '',
        storeName: '',
        storeGrader: '',
        storeTraffic: '',
        current: 1,
        size: 10
      },
      options: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },
  created() {
    this.days.push(moment(new Date()).startOf("month").format("YYYY-MM-DD"))
    this.days.push(moment(new Date()).endOf("month").format("YYYY-MM-DD"))
    this.getData();
  },
  methods: {
    exportAllStoreSettlementData(){
      this.form.startTime = '';
      this.form.endTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      this.hrefUrl = "https://biapi.xiaoyujia.com/aiRun/exportAllStoreSettlementData?storeName="+this.form.storeName+
          "&gemmologist="+this.form.gemmologist+"&support="+this.form.support+"&storeManager="+this.form.storeManager+
          "&storeGrader="+this.form.storeGrader+"&storeTraffic="+this.form.storeTraffic+"&startTime="+this.form.startTime+
          "&endTime="+this.form.endTime
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.form.size = size;
      this.getData();
    },
    onChange(index) {
      this.loading = true;
      this.form.current = index;
      this.getData();
    },
    getData() {
      this.loading = true
      this.form.startTime = '';
      this.form.endTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      this.$getData("getStoreSettlementData", this.form, {}).then(res => {
        this.loading = false
        if (res.code == 0) {
          this.logList = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    }
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
