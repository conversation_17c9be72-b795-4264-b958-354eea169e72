<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <Form :label-width="80" style="margin-top: 20px"
                      label-position="left">
                    <FormItem :label="name+'时间'" prop="">
                        <el-date-picker
                                v-model="dto.days"
                                type="datetimerange"
                                :picker-options="pickerOptions"
                                format="yyyy-MM-dd HH:mm:ss"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </FormItem>
                    <FormItem :label="name+'金额'" prop="">
                        <Input size="large" v-model="dto.amount" style="width: 150px"
                               :placeholder="name+'金额'"/>
                    </FormItem>
                </Form>
                <Form :label-width="80" style="margin-top: 20px" :model="dto1"
                      label-position="left">
                    <FormItem :label="name+'时间'" prop="">
                        <el-date-picker
                                v-model="dto1.days"
                                type="datetimerange"
                                :picker-options="pickerOptions1"
                                format="yyyy-MM-dd HH:mm:ss"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </FormItem>
                    <FormItem :label="name+'金额'" prop="">
                        <Input size="large" v-model="dto1.amount" style="width: 150px"
                               :placeholder="name+'金额'"/>
                    </FormItem>
                </Form>
                <Button type="primary" :loading="loading" @click="batchMatter">批量新增</Button>
                <div style="padding: 22px">
                    注：暂不支持跨月，分多次操作
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        props: ['projectId', 'employee', 'type', 'deductionType','name'],
        data() {
            return {
                loading: false,
                dto: {
                    days: [],
                    startTime: null,
                    employeeId: this.employee.id,
                    endTime: null,
                    amount: null,
                    type: this.type,
                    deductionType: this.deductionType
                },
                dto1: {
                    days: [],
                    employeeId: this.employee.id,
                    startTime: null,
                    endTime: null,
                    amount: null,
                    type: this.type,
                    deductionType: this.deductionType
                },
                pickerOptions: {
                    onPick: ({maxDate, minDate}) => {
                        this.pickerMinDate = minDate.getTime();
                        if (maxDate) {
                            this.pickerMinDate = ''
                        }
                    },
                    disabledDate: time => {
                        if (this.pickerMinDate) {
                            console.log(this.pickerMinDate);
                            const startDay = (new Date(this.pickerMinDate).getDate() - 1) * 24 * 3600 * 1000;
                            const endDay = (new Date(new Date(this.pickerMinDate).getFullYear(),
                                new Date(this.pickerMinDate).getMonth() + 1, 0).getDate() - new Date(this.pickerMinDate).getDate()) * 24 * 3600 * 1000;
                            let minTime = this.pickerMinDate - startDay;
                            let maxTime = this.pickerMinDate + endDay;
                            return time.getTime() < minTime || time.getTime() > maxTime
                        }
                    }
                },
                pickerOptions1: {
                    onPick: ({maxDate, minDate}) => {
                        this.pickerMinDate = minDate.getTime();
                        if (maxDate) {
                            this.pickerMinDate = ''
                        }
                    },
                    disabledDate: time => {
                        if (this.pickerMinDate) {
                            const startDay = (new Date(this.pickerMinDate).getDate() - 1) * 24 * 3600 * 1000;
                            const endDay = (new Date(new Date(this.pickerMinDate).getFullYear(),
                                new Date(this.pickerMinDate).getMonth() + 1, 0).getDate() - new Date(this.pickerMinDate).getDate()) * 24 * 3600 * 1000;
                            let minTime = this.pickerMinDate - startDay;
                            let maxTime = this.pickerMinDate + endDay;
                            return time.getTime() < minTime || time.getTime() > maxTime
                        }
                    }
                },
            };
        },
        components: {},
        watch: {
            projectId: {
                handler(newValue, oldValue) {
                    this.projectId = newValue;
                }
            },
            employee: {
                handler(newValue, oldValue) {
                    this.employee = newValue;
                }
            },
            type: {
                handler(newValue, oldValue) {
                    this.type = type;
                }
            },
            deductionType: {
                handler(newValue, oldValue) {
                    this.deductionType = newValue;
                }
            },
            name: {
                handler(newValue, oldValue) {
                    this.name = newValue;
                }
            },
        },
        created() {
        },
        computed: {},
        methods: {
            batchMatter() {
                if (this.dto.days.length <= 0 && this.dto1.days.length <= 0) {
                    this.$message.error("选择日期");
                    return
                }
                let wagesMatters = [];
                if (this.dto.days.length > 0) {
                    this.dto.startTime = this.dto.days[0];
                    this.dto.endTime = this.dto.days[1];
                    wagesMatters.push(this.dto)
                }
                if (this.dto1.days.length > 0) {
                    this.dto1.startTime = this.dto1.days[0];
                    this.dto1.endTime = this.dto1.days[1];
                    wagesMatters.push(this.dto1)
                }
                let query = {
                    employeeId: this.employee.id,
                    projectId: this.projectId,
                    wagesMatters: wagesMatters,
                };
                this.loading = true;
                this.$postData("siteWagesMatter_batchMatter", query).then(res => {
                    this.loading = false;
                    if (res.status === 200) {
                        this.dto.days = [];
                        this.dto1.days = [];
                        this.dto.amount = null;
                        this.dto1.amount = null;
                        this.$message({
                            type: 'success',
                            message: '操作成功!'
                        });
                    }
                })
            }
        }
    };
</script>

<style scoped>
    .handle-box {
        /*margin-bottom: 10px;*/
        font-weight: bold !important;
    }

    .el-form-item__label {
        font-weight: bold !important;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;

    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }

    .el-date-editor .el-range-separator {
        padding: 0 5px;
        line-height: 32px;
        width: 7%;
        color: #303133;
    }

    .container {
        padding: 0px !important;
        background: #fff;
        border: none !important;
        border-radius: 5px;
    }
</style>
