<template>
    <div class="table">
        <!--<div class="crumbs">-->
            <!--<el-breadcrumb separator="/">-->
                <!--<el-breadcrumb-item>-->
                    <!--<i class="el-icon-lx-cascades"></i> 项目管理-->
                <!--</el-breadcrumb-item>-->
            <!--</el-breadcrumb>-->
        <!--</div>-->
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">

                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="描述1">
                                <el-input
                                        v-model="productDetails.explain1"
                                        placeholder="描述1"
                                        style="width:190px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="描述2">
                                <el-input
                                        v-model="productDetails.explain2"
                                        placeholder="描述2"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>


                        <el-col :span="8" style="text-align:right">
                            <el-form-item>
                                <el-button type="success" style="width:40%" round @click="saveModal=true">添加</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>


                        <el-col :span="8">
                            <el-form-item label="流程">
                                <Select v-model="productDetails.speed" filterable style="width: 200px">
                                    <Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label}}
                                    </Option>
                                </Select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item label="结束时间">
                                <el-date-picker
                                        v-model="productDetails.endTime1"
                                        type="daterange"
                                        align="right"
                                        unlink-panels
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        format="yyyy 年 MM 月 dd 日"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8" style="text-align:right">
                            <el-form-item>
                                <el-button type="success" style="width:40%" round @click="query()">搜索</el-button>
                            </el-form-item>
                        </el-col>


                    </el-row>
                </el-form>
            </div>
            <el-table :data="list" border class="table" ref="multipleTable">
                <el-table-column
                        prop="explain1"
                        label="描述1"
                        width="330"

                ></el-table-column>
                <el-table-column
                        prop="explain2"
                        label="描述2"
                        width="250"
                ></el-table-column>
                <el-table-column
                        prop="explain3"
                        label="描述3"
                        width="150"
                ></el-table-column>
                <el-table-column
                        prop="speedDesc"
                        label="流程"
                        width="100"

                ></el-table-column>
                <el-table-column
                        prop="bannerUrl"
                        label="跳转链接"
                        width="200"
                ></el-table-column>
                <el-table-column
                        prop="endTime"
                        label="结束时间"
                        width="160"
                ></el-table-column>
                <el-table-column
                        label="操作"
                        width="80">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="edit(scope.row.id)" type="primary">编辑</el-button>
                    </template>

                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>

        <Modal v-model="saveModal" class="Modal" :width="screenWidth"  title="添加" :mask-closable="false" >
            <div class="addBody">

                <try-product-detail-add v-if="saveModal" @init-choose="initChooseProject"
                                @close-modal="closeCurrModal"></try-product-detail-add>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="updateModal" class="Modal" :width="screenWidth"  title="修改" :mask-closable="false" >
            <div class="addBody">
                <try-product-detail-update v-if="updateModal" @init-choose="initChooseProject" :detailId="detailId"
                                 @close-modal="closeCurrModal"></try-product-detail-update>
            </div>
            <div slot="footer">
            </div>
        </Modal>

    </div>
</template>

<script>
    import tryProductDetailAdd from '@/components/page/try/choose/tryProductDetailAdd.vue'
    import tryProductDetailUpdate from '@/components/page/try/choose/tryProductDetailUpdate.vue'
    import {formatDate} from '@/components/common/utils.js'
    export default {
        data() {
            return {
                detailId:null,
                tableData: [],
                pageSizeOpts:[10,20,30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                screenWidth: '45%',//新增对话框 宽度
                saveModal: false,
                updateModal: false,
                productDetails: {
                    explain1: null,
                    explain2: null,
                    explain3: null,
                    speed: null,
                    bannerUrl: null,
                    endTime:null,
                    endTime1:null,
                    endTime2:null,
                    pageSize:10,
                    pageNum:1,
                },
                update:{
                    id:null,
                    status:null
                },
                cityList: [
                    {
                        value: '',
                        label: '请选择'
                    },
                    {
                        value: '1',
                        label: '申请试吃'
                    },
                    {
                        value: '2',
                        label: '填写信息'
                    },
                    {
                        value: '3',
                        label: '领取下单'
                    },
                    {
                        value: '4',
                        label: '上门服务'
                    },
                    {
                        value: '5',
                        label: '试吃体验'
                    },
                    {
                        value: '6',
                        label: '提交报告'
                    },
                ],
                list:null
            };
        },
        components: {
            'tryProductDetailAdd': tryProductDetailAdd,
            'tryProductDetailUpdate': tryProductDetailUpdate,
        },
        created() {
            this.getData();
        },
        computed: {
        },
        methods: {
            getData() {
                this.$postData("detail_getByList", this.productDetails, {}).then(res => {
                    if (res.status == 200) {

                        this.gePage(res) //分页
                        this.list = res.data.list;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            gePage(res){
                this.pageInfo.current = res.data.pageNum;
                this.pageInfo.size = res.data.pageSize;
                this.pageInfo.total = res.data.total
            },
            query() {
                if(this.productDetails.endTime1){
                    this.productDetails.endTime2=this.productDetails.endTime1[1];
                    this.productDetails.endTime1=this.productDetails.endTime1[0];
                }
                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                this.productDetails.pageSize = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                console.log(index)
                this.productDetails.pageNum = index;
                this.getData();
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
                this.updateModal = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },
            edit(id){
                this.detailId=id;
                this.updateModal=true;
            },
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
