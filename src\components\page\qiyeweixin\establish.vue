<template>
  <div class="page">
    <el-container style=" border: 1px solid #eee">
      <el-aside width="200px">
        <el-menu @select="handleSelect">
          <el-menu-item-group title="企微素材">
            <el-menu-item index="1">图片</el-menu-item>
            <el-menu-item index="2">图文</el-menu-item>
            <el-menu-item index="3">小程序</el-menu-item>
            <el-menu-item index="4">视频</el-menu-item>
            <el-menu-item index="5">文件</el-menu-item>
            <!--            <el-menu-item index="4">群机器人</el-menu-item>-->
          </el-menu-item-group>
        </el-menu>
      </el-aside>
      <el-container v-if="navigation==='1'" style="background: white">
        <pictureContent></pictureContent>
      </el-container>
      <el-container v-if="navigation==='2'" style="background: white">
        <graphicsContent></graphicsContent>
      </el-container>
      <el-container v-if="navigation==='3'" style="background: white">
        <applet></applet>
      </el-container>
      <el-container v-if="navigation==='4'" style="background: white">
        <employee-video></employee-video>
      </el-container>
      <el-container v-if="navigation==='5'" style="background: white">
       <qiye-files></qiye-files>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import pictureContent from "./pictureContent";
import applet from "./applet";
import swarmRobots from "./swarmRobots";
import graphicsContent from "./graphicsContent";
import employeeVideo from "./employeeVideo";
import qiyeFiles from "@/components/page/qiyeweixin/qiyeFiles";

export default {
  name: "establish",
  // 注册组件
  components: {
    pictureContent,
    applet,
    swarmRobots,
    graphicsContent,
    employeeVideo,
    qiyeFiles
  },

  data() {
    return {
      bitSwarmRobots: false,
      navigation: null,
      dom: {
        type: null,
        operatorId: null,
      },
      swarmRobots: {
        name: null,
        webhook: null,
      },
    }
  },
  created() {
    this.dom.operatorId = localStorage.getItem("id")
  },
  methods: {
    handleSelect(key) {
      this.navigation = key
      this.dom.type = key
    },
    addSwarmRobots() {
      if (this.swarmRobots.name === '' || this.swarmRobots.name === null || this.swarmRobots.webhook === '' || this.swarmRobots.webhook === null) {
        return this.$message.error("不能放空");
      }
      this.$postData("addSwarmRobots", this.swarmRobots).then(res => {
        if (res.data > 0) {
          this.bitSwarmRobots = !this.bitSwarmRobots;
          this.$message.success("添加成功");
        } else {
          return this.$message.error("添加失败");
        }
      })
    },
  },
}
</script>

<style scoped>
  .page{
    width: 100%;
    height: 100%;
  }
</style>
