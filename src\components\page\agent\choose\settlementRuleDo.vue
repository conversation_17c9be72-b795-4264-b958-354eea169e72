<template style="background-color: #000">

            <el-tabs type="card" :stretch="true" >
                <el-tab-pane label="金额计算">
                <Row>
                    <center>
                        <div style="font-size: 25px;width: 80%; margin-bottom: 20px;padding: 0 20px;border: 1px solid #DCDFE6;background-color: #F5F7FA;border-radius: 4px;">获得金额：<span style="color: #2d8cf0"> <b>{{dosum}}</b></span></div>
                    <Col span="24">

                        <el-form ref="form" :model="dom">


                                    <el-form-item   prop="sum">
                                        <el-input placeholder="请输入" v-model="dom.sum" type="number">
                                            <template slot="prepend">输入总金额:</template>
                                            <template slot="append">元</template>
                                        </el-input>
                                    </el-form-item >
                                    <el-form-item   prop="esum">
                                        <el-input placeholder="请输入" v-model="dom.esum" type="number">
                                            <template slot="prepend">输入开发单金额:</template>
                                            <template slot="append">元</template>
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item   prop="psum">
                                        <el-input placeholder="请输入" v-model="dom.psum" type="number">
                                            <template slot="prepend">输入自有保姆金额:</template>
                                            <template slot="append">元</template>
                                        </el-input>
                                    </el-form-item>
<!--                                    <el-form-item   prop="sum">-->
<!--                                        <el-input placeholder="请输入" v-model="dom.dosum"  :disabled="true">-->
<!--                                            <template slot="prepend">因得金额:</template>-->
<!--                                            <template slot="append">元</template>-->
<!--                                        </el-input>-->
<!--                                    </el-form-item >-->


                            <div style="margin-right: 100px;float: right">
                                <Button type="primary" @click="save('dom')">计算</Button>
                                <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                            </div>
                        </el-form>
                    </Col>

                    </center>
            </Row>
                </el-tab-pane>
<!--                <el-tab-pane label="配置管理">配置管理</el-tab-pane>-->
            </el-tabs>

</template>


<script>

    export default {
        props:['model'],
        data() {
            return {
                dosum:0,
                dom:{
                    esum:0,
                    psum:0,
                    sum:0,
                    id:this.model,

                },
                doid:this.model,

            }
        },
        components: {
        },
        created: function () {
            console.log(this.doid)
        },
        methods: {

            save(name) {
                        this.$postData("do_settlementRulePage", this.dom, {}).then(res => {
                            if (res.status == 200) {
                                console.log(res.data)
                                this.dosum=res.data
                                this.$Message.success('计算成功');
                               // this.chooseThisModel();
                            } else {
                                this.$message.error("计算失败，" + res.msg);
                            }
                        })


            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.dom=null;
                this.$emit('init-choose', "");
            },

        },

    }
</script>

<style>
    {
        background: #409eff;
        color: #fff;
    }
</style>
<style scoped>
    .startWord{
        background-color: #F5F7FA;
        color: #909399;
        vertical-align: middle;

        position: relative;
        border: 1px solid #DCDFE6;
        border-radius: 4px;
        padding: 0 20px;
        width: 1px;
        white-space: nowrap;
        float: left;
        padding-right: 50px;
    }
    .ivu-col-span-9{
        padding-top: 30px;
    }
    .el-input-group{
        width: 80%;
    }
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

