<template>
	<div class="container">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">设备列表</el-menu-item>
			<el-menu-item index="1">其他</el-menu-item>
		</el-menu>

		<!-- 搜索栏目 -->
		<div v-if="choiceIndexNow==0">
			<div class="handle-box" style="margin: 20px 20px 20px 0px;">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="6">
							<el-form-item label="搜索关键词" style="margin-right: 20px">
								<el-input v-model="quer.search" placeholder="直播设备名称、备注等"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="设备状态" style="margin-right: 20px">
								<el-select v-model="searchState" filterable>
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索
							</el-button>

							<el-button icon="el-icon-refresh" @click="reset()">重置
							</el-button>

							<el-button type="success" icon="el-icon-circle-plus-outline" @click="openModal(0,0)">添加
							</el-button>

							<el-button type="info" @click="excelDownload()">导出
							</el-button>
						</el-col>
					</el-row>
				</el-form>
			</div>

			<!-- 数据表格 -->
			<el-table :data="list" v-loading="loading" border stripe
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">

				<el-table-column prop="id" width="60" label="编号">
				</el-table-column>

				<el-table-column width="130" prop="deviceId" label="设备标识">
				</el-table-column>

				<el-table-column width="130" prop="deviceName" label="设备名称">
				</el-table-column>

				<el-table-column width="130" prop="deviceRemark" label="设备备注">
					<template slot-scope="scope">
						<span>{{scope.row.deviceRemark|| '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="125" prop="deviceImg" label="设备照片">
					<template slot-scope="scope">
						<img :src="scope.row.deviceImg||blankImg" style="width: 100px;height: 100px;"
							@click="openImg(scope.row.deviceImg||blankImg)">
					</template>
				</el-table-column>

				<el-table-column width="120" prop="deviceState" label="设备状态" :renderHeader="renderHeader">
					<template slot-scope="scope">
						<el-tag v-for="(item,index) in stateList" :key="index"
							v-if="scope.row.deviceState == item.value" :type="item.type">{{item.text}}</el-tag>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="deviceAccount" label="设备抖音号">
					<template slot-scope="scope">
						<span>{{scope.row.deviceAccount|| '暂未设置'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="deviceUser" label="设备账号名">
					<template slot-scope="scope">
						<span>{{scope.row.deviceUser|| '暂未设置'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="employeeName" label="管理者">
					<template slot-scope="scope">
						<span v-if="scope.row.employeeName">{{scope.row.employeeName}}({{scope.row.employeeNo}})</span>
						<span v-if="!scope.row.employeeName">{{scope.row.employeeNo||'暂未分配'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="liveTheme" label="直播主题">
					<template slot-scope="scope">
						<span>{{scope.row.liveTheme|| '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="150" prop="liveScript" label="直播话术">
					<template slot-scope="scope">
						<span>{{scope.row.liveScript|| '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="liveHomepage" label="首页地址">
					<template slot-scope="scope">
						<span>{{scope.row.liveHomepage|| '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="localCookie" label="直播cookie文件名">
					<template slot-scope="scope">
						<span>{{scope.row.localCookie|| '暂无'}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="douYinCookie" label="抖音cookie文件名">
					<template slot-scope="scope">
						<span>{{scope.row.douYinCookie|| '暂无'}}</span>
					</template>
				</el-table-column>

				<!-- 				<el-table-column prop="videoUrl" label="直播视频" width="180">
					<template slot-scope="scope">
						<el-button @click="openVideo(scope.row.videoUrl)" type="primary" size="small"
							:disabled="!scope.row.videoUrl" icon="el-icon-zoom-in">预览
						</el-button>
					</template>
				</el-table-column> -->

				<el-table-column prop="videoDuration" label="视频时长" width="120" sortable>
					<template slot-scope="scope">
						<span>{{formatDuration(scope.row.videoDuration)}}</span>
					</template>
				</el-table-column>

				<el-table-column width="130" prop="creatorName" label="创建人">
					<template slot-scope="scope">
						<span v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
						<span v-if="!scope.row.creatorName">{{scope.row.creator}}</span>
					</template>
				</el-table-column>

				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
				</el-table-column>

				<el-table-column fixed="right" min-width="200" label="操作">
					<template slot-scope="scope">
						<el-button @click="openModal(1,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
					</template>
				</el-table-column>

			</el-table>
		</div>

		<div v-if="choiceIndexNow==1">
			<el-button type="success" size="small">敬请期待</el-button>
		</div>

		<div class="pagination" v-if="choiceIndexNow==0">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--添加直播设备-->
		<el-drawer size="60%" :with-header="false" :visible.sync="appletLiveDeviceModal" direction="rtl">
			<div v-if="choiceItem">
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">直播设备信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 设备标识：">
								<el-input v-model="choiceItem.deviceId" type="text" class="handle-input mr10"
									placeholder="请输入直播设备标识">
								</el-input>
							</el-form-item>

							<el-form-item label="* 设备名称：">
								<el-input v-model="choiceItem.deviceName" type="text" class="handle-input mr10"
									placeholder="请输入直播设备名称">
								</el-input>
							</el-form-item>

							<el-form-item label="设备账号名：">
								<el-input v-model="choiceItem.deviceUser" type="text" class="handle-input mr10"
									placeholder="请输入直播设备账号名">
								</el-input>
							</el-form-item>

							<el-form-item label="设备抖音号：">
								<el-input v-model="choiceItem.deviceAccount" type="text" class="handle-input mr10"
									placeholder="请输入直播设备抖音号">
								</el-input>
							</el-form-item>

							<el-form-item label="设备备注：">
								<el-input v-model="choiceItem.deviceRemark" type="textarea" class="handle-input mr10"
									placeholder="请输入直播设备备注" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="设备照片：">
								<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
									list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="choiceItem.deviceImg||blankImg" style="width: 150px;height: 150px;"
										@click="openImgUpload(0,0)">
								</el-upload>
							</el-form-item>
						</el-form>
					</el-col>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="员工检索：">
								<el-input v-model="searchText" placeholder="输入员工姓名/工号进行检索后再选择" @input="listEmployee"
									@change="listEmployee" style="width: 280px"></el-input>
							</el-form-item>
							<el-form-item label="管理者选择：">
								<el-select v-model="choiceItem.employeeNo" placeholder="请选择" @change="selectEmployee"
									:disabled="editType==1">
									<el-option v-for="(item,index) in employeeList" :key="index"
										:label="item.realName+'-'+item.no" :value="item.no">
									</el-option>
								</el-select>
								当前：<span>{{choiceItem.employeeName||'暂未分配'}}</span>
							</el-form-item>

							<el-form-item label="直播主题：">
								<el-input v-model="choiceItem.liveTheme" type="text" class="handle-input mr10"
									placeholder="请输入直播主题">
								</el-input>
							</el-form-item>

							<el-form-item label="直播话术：">
								<el-input v-model="choiceItem.liveScript" type="textarea" class="handle-input mr10"
									placeholder="请输入直播话术" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="首页地址：">
								<el-input v-model="choiceItem.liveHomepage" type="textarea" class="handle-input mr10"
									placeholder="请输入首页地址" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="本地直播cookie文件名：">
								<el-input v-model="choiceItem.localCookie" type="textarea" class="handle-input mr10"
									placeholder="请输入本地直播cookie文件名" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="抖音cookie文件名：">
								<el-input v-model="choiceItem.douYinCookie" type="textarea" class="handle-input mr10"
									placeholder="请输入抖音cookie文件名" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<!-- 				<el-form-item label="直播视频链接：">
								<el-input v-model="choiceItem.videoUrl" type="textarea" class="handle-input mr10"
									placeholder="请输入直播视频链接" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item> -->

							<el-form-item>
								<el-button @click="openVideo(choiceItem.videoUrl)" type="primary" size="small"
									:disabled="!choiceItem.videoUrl" icon="el-icon-zoom-in">预览
								</el-button>
								<el-upload :action="fileUploadUrl" list-type="picture" :before-upload="beforeFileUpload"
									:on-success="fileUploadSuccess" :on-error="fileUploadError" :show-file-list="false"
									:data="fileUploadData" class="upload-demo inline-block">
									<el-button @click="fileUpload(0)" type="warning" size="small"
										style="margin-left: 10px" icon="el-icon-upload2">普通上传</el-button>
								</el-upload>
								<el-button @click="openModal(3,0)" type="info" size="small" style="margin-left: 10px"
									icon="el-icon-upload2">阿里云点播</el-button>
								<el-tooltip class="item" effect="dark" content="推荐上传到阿里云点播控制台，再将链接复制到这，以获得更流畅的视频播放体验"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="直播视频时长：" v-if="detailType==1">
								<span>{{formatDuration(choiceItem.videoDuration)}}</span>
								<el-tooltip class="item" effect="dark" content="无需手动填写，在保存短视频链接后将自动计算时长"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>


							<el-form-item label="设备状态：" v-if="detailType==1">
								<el-select v-model="choiceItem.deviceState" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="appletLiveDeviceModal=false" type="primary" size="small">取消
					</el-button>
					<el-button @click="insertAppletLiveDevice()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button>
					<el-button @click="updateAppletLiveDevice(choiceItem)" type="success" size="small"
						v-if="detailType==1">保存
					</el-button>
				</div>

			</div>
		</el-drawer>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--视频预览-->
		<el-dialog :visible.sync="videoModal" width="40%" title="视频预览" :mask-closable="false">
			<video style="width: 100%;height: auto;margin: 0 auto;" :controls="true" :enable-progress-gesture="true"
				:initial-time="0" :show-center-play-btn="true" autoplay :src="videoUrl" custom-cache="false">
			</video>
		</el-dialog>
	</div>

</template>

<script>
	import Vue from 'vue';

	export default {
		name: "appletLiveDevice",

		data() {
			return {
				// 可设置
				// 是否超级管理员（可执行删除等敏感操作）
				isAdmin: false,

				url: '',
				imageUrl: '',
				videoUrl: '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				uploadId: 1,
				uploadImgType: 0,
				// 文件上传地址
				fileUploadUrl: 'https://api.xiaoyujia.com/system/uploadFile',
				fileUploadData: {
					route: 'appletLiveDevice'
				},
				uploadFileType: 0,

				// baseUrl: 'http://localhost:8063/',
				baseUrl: 'https://biapi.xiaoyujia.com/',
				uploadUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
				deleteTips: "确定删除选中直播设备吗？该操作无法恢复，请谨慎操作!",
				imgModal: false,
				videoModal: false,
				appletLiveDeviceModal: false,
				detailType: 0,
				loading: true,
				pageInfo: {
					total: 10,
					size: 20,
					current: 1,
					pages: 1
				},
				pageSizeOpts: [20, 50, 100],
				list: [],
				courseList: [],
				keyList: [],
				employeeList: [],
				choiceIndex: '0',
				choiceIndexNow: '0',
				choiceItemIndex: 0,
				choiceItem: null,
				choiceRecordId: 0,
				expands: [],
				multipleSelection: [],
				getRowKeys(row) {
					return row.id
				},
				signInTips: '',
				searchText: '',
				searchState: null,
				searchRequired: null,
				quer: {
					search: "",
					deviceState: null,
					orderBy: "t.id ASC",
					current: 1,
					size: 20
				},
				switchStateList: [{
						id: 0,
						typeName: "关闭",
						type: "info"
					},
					{
						id: 1,
						typeName: "开启",
						type: "success"
					}
				],
				stateList: [{
					value: 0,
					text: "关机停用",
					type: "info"
				}, {
					value: 1,
					text: "待机中",
					type: "primary"
				}, {
					value: 2,
					text: "直播中",
					type: "success"
				}],
				typeStyleList: [{
						id: 0,
						type: "success"
					},
					{
						id: 1,
						type: "info"
					},
					{
						id: 2,
						type: "warning"
					}, {
						id: 3,
						type: "primary"
					}
				],
				authList: [{
					id: 1,
					name: '普通会员'
				}, {
					id: 2,
					name: '行政员工'
				}, {
					id: 3,
					name: '服务员工'
				}, {
					id: 4,
					name: '保姆月嫂'
				}, {
					id: 5,
					name: '共创店'
				}, {
					id: 6,
					name: '合伙人'
				}, {
					id: 7,
					name: '门店店长'
				}, {
					id: 8,
					name: '陪跑店店长'
				}],
				searchTime: [],
				pickerOptions: {
					shortcuts: [{
						text: '昨天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '今天',
						onClick(picker) {
							picker.$emit('pick', new Date());
						}
					}, {
						text: '明天',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周前',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}, {
						text: '一周后',
						onClick(picker) {
							const date = new Date();
							date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
							picker.$emit('pick', date);
						}
					}]
				},
				pickerOptions1: {
					shortcuts: [{
						text: '今天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '昨天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					}, {
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}]
				}
			}
		},
		created() {
			if (this.$route.query.isAdmin) {
				this.isAdmin = true
			}
			this.getData()
		},
		methods: {
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceItem = null
				this.choiceIndex = key
				this.expands = []
				this.reset()
			},
			getData() {
				this.choiceIndexNow = 2
				if (this.choiceIndex == 0) {
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'appletLiveDevice/pageAppletLiveDevice',
						data: JSON.stringify(this.quer),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							this.loading = false
							if (res.status == 200) {
								this.list = res.data.records
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
								this.choiceIndexNow = this.choiceIndex
							} else {
								this.list = []
								this.$message.error('未查询到相关直播设备!')
								this.choiceIndexNow = this.choiceIndex
							}
						},
					})
				} else if (this.choiceIndex == 1) {
					this.choiceIndexNow = this.choiceIndex
				}
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.quer.deviceState = this.searchState
				this.getData()
			},
			// 重置
			reset() {
				this.loading = true
				this.quer.current = 1
				this.quer.search = ""
				this.quer.deviceState = null

				this.searchText = ''
				this.searchState = null
				this.getData()
			},
			// 获取分组
			listAppletLiveDeviceGroup() {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'appletLiveDevice/listAppletLiveDeviceGroup',
					data: JSON.stringify({
						groupState: 1
					}),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.status == 200) {
							this.groupList = res.data
						} else {
							this.groupList = []
						}
					},
				})
			},
			// 获取员工列表
			listEmployee() {
				if (!this.searchText) {
					return
				}
				this.$postData("listEmployeeDto", {
					search: this.searchText,
					employeeType: null,
					state: 1
				}).then(res => {
					if (res.status == 200) {
						this.employeeList = res.data
					} else {
						this.$message.error("查询失败，" + res.msg)
					}
				})
			},
			// 选中员工
			selectEmployee(id) {
				let index = this.getIndex(id, this.employeeList)
				let employee = this.employeeList[index]
				this.choiceItem.employeeNo = employee.no || null
				this.choiceItem.employeeName = employee.realName || ''
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.choiceRecordId = row.id
					}
				} else {
					that.expands = [] // 默认不展开
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "设备状态") {
					tips = "设备当前使用状态"
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			// 将字符串转化为数组
			strToArray(val) {
				let arrayString = []
				let array = []
				arrayString = val.split(',')
				arrayString.forEach(it => {
					let value = parseInt(it)
					if (value != null && value != 0) {
						array.push(value)
					}
				})
				return array
			},
			formatDuration(duration, defaultStr) {
				if (!defaultStr) {
					defaultStr = '暂未录入'
				}
				let result = ""
				let sencond = 0
				let min = 0
				let hour = 0
				let day = 0
				// 小于一分钟
				if (duration <= 60) {
					sencond = Math.floor(parseFloat(duration / 1.0))
					if (sencond == 0) {
						result = defaultStr
					} else {
						result = sencond + "秒"
					}
				}

				// 小于二小时
				if (duration > 60 && duration <= 3600) {
					min = Math.floor(parseFloat(duration / 60.0))
					sencond = Math.floor(duration - (min * 60)).toFixed(0)
					if (min == 0) {
						result = defaultStr
					} else {
						result = min + "分钟" + sencond + "秒"
					}
				}
				// 大于二小时 小于一天
				if (duration > 3600 && duration <= 86400) {
					hour = Math.floor(parseFloat(duration / 3600))
					min = parseFloat((duration - (hour * 3600)) / 60).toFixed(0)
					if (min == 0) {
						result = hour + "小时"
					} else {
						result = hour + "小时" + min + "分钟"
					}
				}
				// 大于一天
				else if (duration > 86400) {
					day = Math.floor(parseFloat(duration / 86400))
					hour = parseFloat((duration - (day * 86400)) / 3600).toFixed(0)
					if (hour == 0) {
						result = day + "天"
					} else {
						result = day + "天" + hour + "小时"
					}
				}
				return result
			},
			formatTypeStyle(type) {
				return this.typeStyleList[type % 4].type
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 直播设备信息下载
			excelDownload() {
				this.$postData("appletLiveDeviceExcelDownload", this.quer, {
					responseType: "arraybuffer"
				}).then(res => {
					this.$message.success('直播设备信息下载成功!')
					this.blobExport({
						tablename: "直播设备信息",
						res: res
					})
				})
			},
			// Excel下载
			blobExport({
				tablename,
				res
			}) {
				const aLink = document.createElement("a");
				let blob = new Blob([res], {
					type: "application/vnd.ms-excel"
				});
				aLink.href = URL.createObjectURL(blob);
				aLink.download = tablename + ".xlsx";
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.$set(this.choiceItem, "deviceImg", res.data)
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadId = index
				let tips = ''
				if (this.uploadImgType == 0) {
					tips = "上传设备图片，以方便识别"
				}
				this.$message.info(tips)
			},
			// 打开图片预览
			openImg(url) {
				this.imageUrl = url || this.blankImg
				this.imgModal = true
			},
			// 打开视频预览
			openVideo(url) {
				if (url != null && url != '') {
					this.videoUrl = url
				}
				this.videoModal = true
			},
			// 将字符串转化为数组
			strToArray(val) {
				let arrayString = []
				let array = []
				arrayString = val.split(',')
				arrayString.forEach(it => {
					let value = parseInt(it)
					if (value != null && value != 0) {
						array.push(value)
					}
				})
				return array
			},
			openModal(index, index1) {
				if (index == 0) {
					this.detailType = 0
					this.choiceItem = {}
					this.choiceItem.authListArray = []
					this.choiceItem.keyListArray = []
					this.appletLiveDeviceModal = true
				} else if (index == 1) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.appletLiveDeviceModal = true
				} else if (index == 3) {
					window.open(
						'https://vod.console.aliyun.com/?spm=5176.12672711.categories-n-products.dvod.8f791fa37JVfKI#/media/video/list'
					)
				}
			},
			//打开员工信息详情
			rowClick(id) {

			},
			// 添加课程权限列表
			addAuthIdList() {
				this.authIdListModal = false
			},
			// 删除直播设备权限标签
			closeAuthTags(index, index1) {
				this.choiceItem.authListArray.splice(index1, 1)
			},
			// 添加直播设备
			insertAppletLiveDevice() {
				let appletLiveDevice = this.choiceItem
				if (!appletLiveDevice.deviceId) {
					this.$message.error('请填写直播设备标识！')
				} else if (!appletLiveDevice.deviceName) {
					this.$message.error('请填写直播设备名称！')
				} else {
					this.$set(appletLiveDevice, "creator", localStorage.getItem('account') || 'admin')
					$.ajax({
						type: "POST",
						url: this.baseUrl + 'appletLiveDevice/insertAppletLiveDevice',
						data: JSON.stringify(appletLiveDevice),
						dataType: "json",
						contentType: "application/json",
						success: res => {
							if (res.status == 200) {
								this.$message.success('直播设备添加成功!')
								this.list.push(res.data)
								this.appletLiveDeviceModal = false
							} else {
								this.$message.error('直播设备添加失败！' + res.msg)
							}
						},
					})
				}
			},
			// 更改直播设备
			updateAppletLiveDevice(val) {
				$.ajax({
					type: "POST",
					url: this.baseUrl + 'appletLiveDevice/updateAppletLiveDevice',
					data: JSON.stringify(val),
					dataType: "json",
					contentType: "application/json",
					success: res => {
						if (res.status == 200) {
							this.$message.success('直播设备更新成功!')
							this.list[this.choiceItemIndex] = this.choiceItem
							this.appletLiveDeviceModal = false
						} else {
							this.$message.error('直播设备更新失败！' + res.msg)
						}
					},
				})
			},
			beforeFileUpload() {
				this.loading = true
			},
			// 章节素材文件上传成功
			fileUploadSuccess(res, file) {
				this.loading = false
				if (this.uploadFileType == 0) {
					this.choiceItem.videoUrl = res.data
				}
				this.$message.success('直播设备文件上传成功！')
			},
			// 章节素材文件上传失败
			fileUploadError(err) {
				this.loading = false
				this.$message.error('直播设备文件上传失败！请重试！' + err.msg)
			},
			// 课程章节素材文件上传
			fileUpload(value) {
				this.uploadFileType = value
				this.fileUploadUrl = fileUploadUrl
			},
			beforeImgUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!');
				}
				return isLt2M;
			}
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>