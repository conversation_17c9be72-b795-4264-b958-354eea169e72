<template xmlns:x="http://www.w3.org/1999/xhtml">
    <div style="padding: 10px">

        <el-card shadow="hover">
            <div style="color: red;font-weight: 700;font-size: 20px">￥{{dom.sumBonus}}</div>
        </el-card>
        <el-table
                :data="bonusList"
                stripe
                style="width: 100%">
            <el-table-column
                    prop="bonus"
                    label="抽成奖金"
                    width="180">
                <template slot-scope="scope">
                    <div style="color: red">￥{{scope.row.bonus}}</div>
                </template>
            </el-table-column>
            <el-table-column
                    prop="bonus"
                    label="奖金归属人"
                    width="180">
                <template slot-scope="scope">
                    <div style="color: blue">{{scope.row.realName +'('+scope.row.no+')' }}</div>
                </template>
            </el-table-column>
            <el-table-column
                    prop="typeName"
                    label="来源"
                    width="180">
            </el-table-column>
            <el-table-column
                    prop="demand"
                    label="说明"
                    width="180"

                    show-overflow-tooltip>
            </el-table-column>
            <el-table-column
                    prop="detail"
                    label="备注"
                    width="180">
            </el-table-column>
            <el-table-column
                    prop="creatDate"
                    label="创建日期">
            </el-table-column>
            <el-table-column
                    prop="status"
                    label="状态">
                <template slot-scope="scope">
                    <el-tag type="success" v-if="scope.row.status==1">正常</el-tag>
                    <el-tag type="info" v-if="scope.row.status==2">审批</el-tag>
                </template>
            </el-table-column>
        </el-table>


        <el-divider></el-divider>
        <el-tabs v-model="activeName" type="card">
            <el-tab-pane label="结算信息" name="first">
                <table width="758" border="0" cellpadding="0" cellspacing="0" style='width:568.50pt;border-collapse:collapse;table-layout:fixed;'>
                    <col width="72" style='width:54.00pt;'/>
                    <col width="127" style='mso-width-source:userset;mso-width-alt:4064;'/>
                    <col width="201" style='mso-width-source:userset;mso-width-alt:6432;'/>
                    <col width="111" style='mso-width-source:userset;mso-width-alt:3552;'/>
                    <col width="247" style='mso-width-source:userset;mso-width-alt:7904;'/>
                    <tr height="40" style='height:30.00pt;mso-height-source:userset;mso-height-alt:600;'>
                        <td height="40" width="72" style='height:30.00pt;width:54.00pt;'></td>
                        <td class="xl65" width="686" colspan="4" style='width:514.50pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' x:str>结算单基本信息</td>
                    </tr>
                    <tr height="33.33" style='height:25.00pt;mso-height-source:userset;mso-height-alt:500;'>
                        <td height="33.33" style='height:25.00pt;'></td>
                        <td class="xl67" x:str>经纪人</td>
                        <td class="xl68">{{dom.agentName}}</td>
                        <td class="xl67" x:str>合同开始时间</td>
                        <td class="xl68">{{dom.serviceStarDate}}</td>
                    </tr>
                    <tr height="33.33" style='height:25.00pt;mso-height-source:userset;mso-height-alt:500;'>
                        <td height="33.33" style='height:25.00pt;'></td>
                        <td class="xl67" x:str>上户阿姨</td>
                        <td class="xl68">{{dom.employeeName}}</td>
                        <td class="xl67" x:str>合同结束时间</td>
                        <td class="xl68">{{dom.serviceEndDate}}</td>
                    </tr>
                    <tr height="33.33" style='height:25.00pt;mso-height-source:userset;mso-height-alt:500;'>
                        <td height="33.33" style='height:25.00pt;'></td>
                        <td class="xl67" x:str>是否百优</td>
                        <td class="xl68">{{employee.baomuLevel==1?'是':'否'}}</td>
                        <td class="xl67" x:str>合同天数</td>
                        <td class="xl68">{{dom.contractDay}}（天）</td>
                    </tr>
                    <tr height="33.33" style='height:25.00pt;mso-height-source:userset;mso-height-alt:500;'>
                        <td height="33.33" style='height:25.00pt;'></td>
                        <td class="xl67" x:str>阿姨介绍人</td>
                        <td class="xl68">{{employee.introducerName==null?'无':employee.introducerName}}</td>
                        <td class="xl67" x:str>订单金额</td>
                        <td class="xl68">￥{{dom.amount}}</td>
                    </tr>
                    <tr height="33.33" style='height:25.00pt;mso-height-source:userset;mso-height-alt:500;'>
                        <td height="33.33" style='height:25.00pt;'></td>
                        <td class="xl67" x:str>业绩金额</td>
                        <td class="xl68">￥{{dom.realAmount}}</td>
                        <td class="xl67" x:str>阿姨工资</td>
                        <td class="xl68">￥{{dom.realWage}}</td>
                    </tr>
                    <tr height="33.33" style='height:25.00pt;mso-height-source:userset;mso-height-alt:500;'>
                        <td height="33.33" style='height:25.00pt;'></td>
                        <td class="xl67" x:str>阿姨归属经纪人</td>
                        <td class="xl68">{{employee.bindName==null?'无':employee.bindName}}</td>
                        <td class="xl67" x:str>服务产品</td>
                        <td class="xl68">{{dom.productName}}</td>
                    </tr>
                    <tr height="33.33" style='height:25.00pt;mso-height-source:userset;mso-height-alt:500;'>
                        <td height="33.33" style='height:25.00pt;'></td>
                        <td class="xl67" x:str>开单人（工号）</td>
                        <td class="xl68">{{dom.crePerson}}</td>
                        <td class="xl67" x:str>客户</td>
                        <td class="xl68">{{dom.memberName}}</td>
                    </tr>
                    <tr height="33.33" style='height:25.00pt;mso-height-source:userset;mso-height-alt:500;'>
                        <td height="33.33" style='height:25.00pt;'></td>
                        <td class="xl67" x:str>开发人（工号）</td>
                        <td class="xl68">{{dom.channel}}</td>
                        <td class="xl67" x:str>客户保姆单</td>
                        <td class="xl68">{{dom.memberOrderNum}}单</td>
                    </tr>
                    <![if supportMisalignedColumns]>
                    <tr width="0" style='display:none;'>
                        <td width="127" style='width:95;'></td>
                        <td width="201" style='width:151;'></td>
                        <td width="111" style='width:83;'></td>
                        <td width="247" style='width:185;'></td>
                    </tr>
                    <![endif]>
                </table>
            </el-tab-pane>
            <el-tab-pane label="规则" name="second">
                <el-dialog title="规则" :visible.sync="showImg" :fullscreen="true" :modal="false">
                    <img src="../../../../assets/img/agentBonusRules.png" style="width: 100%">
                </el-dialog>
                    <img src="../../../../assets/img/agentBonusRules.png" style="width: 100%" @click="showImg=true">

            </el-tab-pane>
        </el-tabs>

        <el-divider></el-divider>


    </div>
</template>

<script>
    export default {
        props:['dom'],
        name: "agentBonusInfo",
        data() {
            return {
                showImg:false,
                activeName: 'first',
                employee:null,
                bonusList:[],
            }
        },
        created() {
            this.agentBonusList(this.dom.billNo)
            this.getEmployeeBaomuBindDto(this.dom.employeeId)
        },
        methods:{
            agentBonusList(billNo){
                this.$postUrl("agentBonusList",billNo, {}).then(res => {
                    if (res.status == 200) {
                        this.bonusList=res.data
                    } else {
                        this.$message.error("失败，" + res.msg);
                    }
                })

            },
            getEmployeeBaomuBindDto(billNo){
                this.$postUrl("getEmployeeBaomuBindDto",billNo, {}).then(res => {
                    if (res.status == 200) {
                        this.employee=res.data
                    } else {
                        this.$message.error("失败，" + res.msg);
                    }
                })

            },
        }
    }
</script>

<style scoped>

    {margin:1.00in 0.75in 1.00in 0.75in;
        mso-header-margin:0.50in;
        mso-footer-margin:0.50in;}
    tr
    {mso-height-source:auto;
        mso-ruby-visibility:none;}
    col
    {mso-width-source:auto;
        mso-ruby-visibility:none;}
    br
    {mso-data-placement:same-cell;}
    .font0
    {color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:134;}
    .font1
    {color:#000000;
        font-size:11.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:134;}
    .font2
    {color:#7F7F7F;
        font-size:11.0pt;
        font-weight:400;
        font-style:italic;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:0;}
    .font3
    {color:#3F3F76;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:0;}
    .font4
    {color:#0000FF;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:underline;
        text-underline-style:single;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:0;}
    .font5
    {color:#800080;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:underline;
        text-underline-style:single;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:0;}
    .font6
    {color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:0;}
    .font7
    {color:#9C0006;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:0;}
    .font8
    {color:#FFFFFF;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:0;}
    .font9
    {color:#FA7D00;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:0;}
    .font10
    {color:#44546A;
        font-size:11.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:134;}
    .font11
    {color:#FF0000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:0;}
    .font12
    {color:#44546A;
        font-size:18.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:134;}
    .font13
    {color:#3F3F3F;
        font-size:11.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:0;}
    .font14
    {color:#44546A;
        font-size:15.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:134;}
    .font15
    {color:#FA7D00;
        font-size:11.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:0;}
    .font16
    {color:#44546A;
        font-size:13.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:134;}
    .font17
    {color:#FFFFFF;
        font-size:11.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:0;}
    .font18
    {color:#000000;
        font-size:11.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:0;}
    .font19
    {color:#006100;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:0;}
    .font20
    {color:#9C6500;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:"宋体";
        mso-generic-font-family:auto;
        mso-font-charset:0;}
    .style0
    {mso-number-format:"General";
        text-align:general;
        vertical-align:middle;
        white-space:nowrap;
        mso-rotate:0;
        mso-pattern:auto;
        mso-background-source:auto;
        color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:134;
        border:none;
        mso-protection:locked visible;
        mso-style-name:"常规";
        mso-style-id:0;}
    .style16
    {mso-number-format:"_ \0022\00A5\0022* \#\,\#\#0_ \;_ \0022\00A5\0022* \\-\#\,\#\#0_ \;_ \0022\00A5\0022* \0022-\0022_ \;_ \@_ ";
        mso-style-name:"货币[0]";
        mso-style-id:7;}
    .style17
    {mso-pattern:auto none;
        background:#EDEDED;
        color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"20% - 强调文字颜色 3";}
    .style18
    {mso-pattern:auto none;
        background:#FFCC99;
        color:#3F3F76;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        border:.5pt solid #7F7F7F;
        mso-style-name:"输入";}
    .style19
    {mso-number-format:"_ \0022\00A5\0022* \#\,\#\#0\.00_ \;_ \0022\00A5\0022* \\-\#\,\#\#0\.00_ \;_ \0022\00A5\0022* \0022-\0022??_ \;_ \@_ ";
        mso-style-name:"货币";
        mso-style-id:4;}
    .style20
    {mso-number-format:"_ * \#\,\#\#0_ \;_ * \\-\#\,\#\#0_ \;_ * \0022-\0022_ \;_ \@_ ";
        mso-style-name:"千位分隔[0]";
        mso-style-id:6;}
    .style21
    {mso-pattern:auto none;
        background:#DBDBDB;
        color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"40% - 强调文字颜色 3";}
    .style22
    {mso-pattern:auto none;
        background:#FFC7CE;
        color:#9C0006;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"差";}
    .style23
    {mso-number-format:"_ * \#\,\#\#0\.00_ \;_ * \\-\#\,\#\#0\.00_ \;_ * \0022-\0022??_ \;_ \@_ ";
        mso-style-name:"千位分隔";
        mso-style-id:3;}
    .style24
    {mso-pattern:auto none;
        background:#C9C9C9;
        color:#FFFFFF;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"60% - 强调文字颜色 3";}
    .style25
    {color:#0000FF;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:underline;
        text-underline-style:single;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"超链接";
        mso-style-id:8;}
    .style26
    {mso-number-format:"0%";
        mso-style-name:"百分比";
        mso-style-id:5;}
    .style27
    {color:#800080;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:underline;
        text-underline-style:single;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"已访问的超链接";
        mso-style-id:9;}
    .style28
    {mso-pattern:auto none;
        background:#FFFFCC;
        border:.5pt solid #B2B2B2;
        mso-style-name:"注释";}
    .style29
    {mso-pattern:auto none;
        background:#F4B084;
        color:#FFFFFF;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"60% - 强调文字颜色 2";}
    .style30
    {color:#44546A;
        font-size:11.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:134;
        mso-style-name:"标题 4";}
    .style31
    {color:#FF0000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"警告文本";}
    .style32
    {color:#44546A;
        font-size:18.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:134;
        mso-style-name:"标题";}
    .style33
    {color:#7F7F7F;
        font-size:11.0pt;
        font-weight:400;
        font-style:italic;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"解释性文本";}
    .style34
    {color:#44546A;
        font-size:15.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:134;
        border-bottom:1.0pt solid #5B9BD5;
        mso-style-name:"标题 1";}
    .style35
    {color:#44546A;
        font-size:13.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:134;
        border-bottom:1.0pt solid #5B9BD5;
        mso-style-name:"标题 2";}
    .style36
    {mso-pattern:auto none;
        background:#9BC2E6;
        color:#FFFFFF;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"60% - 强调文字颜色 1";}
    .style37
    {color:#44546A;
        font-size:11.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:134;
        border-bottom:1.0pt solid #ACCCEA;
        mso-style-name:"标题 3";}
    .style38
    {mso-pattern:auto none;
        background:#FFD966;
        color:#FFFFFF;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"60% - 强调文字颜色 4";}
    .style39
    {mso-pattern:auto none;
        background:#F2F2F2;
        color:#3F3F3F;
        font-size:11.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        border:.5pt solid #3F3F3F;
        mso-style-name:"输出";}
    .style40
    {mso-pattern:auto none;
        background:#F2F2F2;
        color:#FA7D00;
        font-size:11.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        border:.5pt solid #7F7F7F;
        mso-style-name:"计算";}
    .style41
    {mso-pattern:auto none;
        background:#A5A5A5;
        color:#FFFFFF;
        font-size:11.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        border:2.0pt double #3F3F3F;
        mso-style-name:"检查单元格";}
    .style42
    {mso-pattern:auto none;
        background:#E2EFDA;
        color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"20% - 强调文字颜色 6";}
    .style43
    {mso-pattern:auto none;
        background:#ED7D31;
        color:#FFFFFF;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"强调文字颜色 2";}
    .style44
    {color:#FA7D00;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        border-bottom:2.0pt double #FF8001;
        mso-style-name:"链接单元格";}
    .style45
    {color:#000000;
        font-size:11.0pt;
        font-weight:700;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        border-top:.5pt solid #5B9BD5;
        border-bottom:2.0pt double #5B9BD5;
        mso-style-name:"汇总";}
    .style46
    {mso-pattern:auto none;
        background:#C6EFCE;
        color:#006100;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"好";}
    .style47
    {mso-pattern:auto none;
        background:#FFEB9C;
        color:#9C6500;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"适中";}
    .style48
    {mso-pattern:auto none;
        background:#D9E1F2;
        color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"20% - 强调文字颜色 5";}
    .style49
    {mso-pattern:auto none;
        background:#5B9BD5;
        color:#FFFFFF;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"强调文字颜色 1";}
    .style50
    {mso-pattern:auto none;
        background:#DDEBF7;
        color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"20% - 强调文字颜色 1";}
    .style51
    {mso-pattern:auto none;
        background:#BDD7EE;
        color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"40% - 强调文字颜色 1";}
    .style52
    {mso-pattern:auto none;
        background:#FCE4D6;
        color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"20% - 强调文字颜色 2";}
    .style53
    {mso-pattern:auto none;
        background:#F8CBAD;
        color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"40% - 强调文字颜色 2";}
    .style54
    {mso-pattern:auto none;
        background:#A5A5A5;
        color:#FFFFFF;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"强调文字颜色 3";}
    .style55
    {mso-pattern:auto none;
        background:#FFC000;
        color:#FFFFFF;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"强调文字颜色 4";}
    .style56
    {mso-pattern:auto none;
        background:#FFF2CC;
        color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"20% - 强调文字颜色 4";}
    .style57
    {mso-pattern:auto none;
        background:#FFE699;
        color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"40% - 强调文字颜色 4";}
    .style58
    {mso-pattern:auto none;
        background:#4472C4;
        color:#FFFFFF;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"强调文字颜色 5";}
    .style59
    {mso-pattern:auto none;
        background:#B4C6E7;
        color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"40% - 强调文字颜色 5";}
    .style60
    {mso-pattern:auto none;
        background:#8EA9DB;
        color:#FFFFFF;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"60% - 强调文字颜色 5";}
    .style61
    {mso-pattern:auto none;
        background:#70AD47;
        color:#FFFFFF;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"强调文字颜色 6";}
    .style62
    {mso-pattern:auto none;
        background:#C6E0B4;
        color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"40% - 强调文字颜色 6";}
    .style63
    {mso-pattern:auto none;
        background:#A9D08E;
        color:#FFFFFF;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:0;
        mso-style-name:"60% - 强调文字颜色 6";}
    td
    {mso-style-parent:style0;
        padding-top:1px;
        padding-right:1px;
        padding-left:1px;
        mso-ignore:padding;
        mso-number-format:"General";
        text-align:general;
        vertical-align:middle;
        white-space:nowrap;
        mso-rotate:0;
        mso-pattern:auto;
        mso-background-source:auto;
        color:#000000;
        font-size:11.0pt;
        font-weight:400;
        font-style:normal;
        text-decoration:none;
        font-family:宋体;
        mso-generic-font-family:auto;
        mso-font-charset:134;
        border:none;
        mso-protection:locked visible;}
    .xl65
    {mso-style-parent:style0;
        text-align:center;
        mso-pattern:auto none;
        background:#F4B084;
        font-weight:700;
        mso-font-charset:134;
        border:.5pt solid windowtext;}
    .xl66
    {mso-style-parent:style0;
        text-align:center;
        mso-pattern:auto none;
        background:#F4B084;
        mso-font-charset:134;
        border:.5pt solid windowtext;}
    .xl67
    {mso-style-parent:style0;
        text-align:center;
        mso-pattern:auto none;
        background:#F8CBAD;
        mso-font-charset:134;
        border:.5pt solid windowtext;}
    .xl68
    {mso-style-parent:style0;
        text-align:center;
        mso-pattern:auto none;
        background:#FFFAF8;
        mso-font-charset:134;
        border:.5pt solid windowtext;}
</style>
