<template>
  <div class="container" id="pdfDom">

    <!-- 搜索栏目 -->
    <div class="handle-box">
      <el-form ref="form" :model="pageInfo">
        <el-row>
          <el-col :span="5">
            <el-form-item label="图标名称" style="margin-right: 20px">
              <el-input v-model="quer.title" placeholder="请输入图标名称"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="问题标签" style="margin-right: 20px">
              <el-input v-model="quer.tags" placeholder="请输入问题标签"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="问题答复" style="margin-right: 20px">
              <el-input v-model="quer.reply" placeholder="请输入问题答复"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="问答类型" style="margin-right: 20px">
              <el-select v-model="quer.typeID" placeholder="请选择问答类型" clearable>
                <el-option
                    v-for="item in qaTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="1">
            <el-button style="margin-right: 30px;margin-top: 30px;" type="primary" @click="query()"
                       icon="el-icon-search">搜索
            </el-button>
          </el-col>

          <el-col :span="10" style="margin: 32px 0;">

            <el-upload :action="excelUploadUrl" list-type="picture" :on-success="excelUploadSuccess"
                       :on-error="excelUploadError" :show-file-list="false" class="upload-demo inline-block">
              <el-button type="warning" size="small"
                         :loading="excelloading" @click="excelloading = true" style="margin-left: 10px" icon="el-icon-upload2">导入问答信息
              </el-button>
            </el-upload>

            <el-button style="margin-left: 10px" type="warning" icon="el-icon-download" @click="excelDownload">下载导入模板
            </el-button>

            <el-button style="margin-left: 10px" type="success" icon="el-icon-circle-plus-outline"
                       @click="certModal = true">添加
            </el-button>
          </el-col>
        </el-row>

      </el-form>
    </div>


    <!-- 数据表格 -->
    <el-table :data="unionQaList" v-loading="loading" border stripe
              :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
              :expand-row-keys="expands">

      <el-table-column width="140" prop="certCodeFixed" label="问题标题">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.title }}</span>

          <el-input v-model="scope.row.title" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
                    placeholder="请输入问题标题" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>

      <el-table-column width="140" prop="typeName" label="问题标签">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.tags }}</span>

          <el-input v-model="scope.row.tags" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
                    placeholder="请输入问题标签" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>

      <el-table-column width="140" prop="typeName" label="问题答复">
        <template slot-scope="scope">
          <el-tag type="primary" @click="selectQAReply(scope.row.id)">点击查看</el-tag>
        </template>
      </el-table-column>

      <el-table-column width="140" prop="typeName" label="问答类型">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.typeName }}</span>

          <el-select v-if="scope.row.isEdit" v-model="scope.row.typeID" placeholder="请选择问答类型">
            <el-option
                v-for="item in qaTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>

      <el-table-column width="140" prop="typeName" label="有用">
        <template slot-scope="scope">
          <span>{{ scope.row.ofUse }}</span>
        </template>
      </el-table-column>

      <el-table-column width="140" prop="typeName" label="无用">
        <template slot-scope="scope">
          <span>{{ scope.row.noUse }}</span>
        </template>
      </el-table-column>

      <el-table-column width="140" prop="certContent" label="创建时间">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>

      <el-table-column width="130" prop="creater" label="创建人">
        <template slot-scope="scope">
          <span>{{ scope.row.founder }}</span>
        </template>
      </el-table-column>

      <el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
        <template slot-scope="scope">
          <el-button @click="openEdit(scope.row,0)" type="success" size="small" :disabled="scope.row.isEdit"
                     v-if="!scope.row.isEdit" icon="el-icon-edit">修改
          </el-button>
          <el-button @click="updateUnionQA(scope.row)" type="primary" size="small"
                     v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
          </el-button>
          <el-popconfirm title="确定删除吗？此操作会连同该答复信息一并删除!" @confirm="deleteUnionQA(scope.row)">
            <el-button type="danger" size="small" slot="reference" style="margin-left: 10px" icon="el-icon-delete">删除
            </el-button>
          </el-popconfirm>

        </template>
      </el-table-column>

    </el-table>


    <div class="pagination">
      <Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
            :show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
            :page-size="pageInfo.size"/>
    </div>

    <!--添加问答-->
    <el-dialog :visible.sync="certModal" width="60%" title="添加问答信息" :mask-closable="false">
      <div style="height: 500px;overflow: hidden;overflow-y: scroll;">
        <el-form ref="ruleForm" label-width="150px" class="demo-ruleForm" size="mini">

          <el-form-item label="问题标题：">
            <el-input v-model="unionQA.title" type="textarea" class="handle-input mr10"
                      placeholder="请输入问题标题">
            </el-input>
          </el-form-item>

          <el-form-item label="问题标签：">
            <el-input v-model="unionQA.tags" type="textarea" class="handle-input mr10"
                      placeholder="请输入问题标签">
            </el-input>
          </el-form-item>

          <el-form-item label="问答类型：">
            <el-select v-model="unionQA.typeID" placeholder="请选择问答类型">
              <el-option
                  v-for="item in qaTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>

        <div style="margin: 0 400px;width: 100%;">
          <el-button @click="saveUnionQA()" type="success" size="small">确定添加
          </el-button>
        </div>

      </div>
    </el-dialog>


    <!--添加答复-->
    <el-dialog :visible.sync="replyModal" width="60%" title="添加答复信息" :mask-closable="false">
      <div style="height: 500px;overflow: hidden;overflow-y: scroll;">
        <el-form ref="ruleForm" label-width="150px" class="demo-ruleForm" size="mini">

          <el-form-item label="问题答复：">
            <el-input v-model="unionQAReply.reply" type="textarea" class="handle-input mr10"
                      placeholder="请输入问题答复">
            </el-input>
          </el-form-item>

          <el-form-item label="图片答复：" >
            <el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
                       list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
              <img :src="unionQAReply.pictureUrl!=null?unionQAReply.pictureUrl:blankImg"
                   style="width: 250px;height: 250px;" v-loading.fullscreen.lock="fullscreenLoading" @click="uploadImgUrl(unionQAReply)">
            </el-upload>
          </el-form-item>

          <el-form-item label="答复排序：">
            <el-input v-model="unionQAReply.sort" type="number" class="handle-input mr10"
                      placeholder="请输入答复排序">
            </el-input>
          </el-form-item>

          <el-form-item label="跳转路径：">
            <el-input v-model="unionQAReply.routeUrl" type="textarea" class="handle-input mr10"
                      placeholder="请输入跳转路径">
            </el-input>
          </el-form-item>


        </el-form>

        <div style="margin: 0 400px;width: 100%;">
          <el-button @click="saveUnionQAReply()" type="success" size="small">确定添加
          </el-button>
        </div>

      </div>
    </el-dialog>

    <el-link :href="hrefUrl" target="_blank" id="elLink"/>

    <el-dialog
        title="问题答复："
        :visible.sync="dialogFlag"
        width="80%">
      <div style="display: flex">
        <el-button style="margin-left: 10px;margin-bottom: 20px;" type="success" icon="el-icon-circle-plus-outline"
                   @click="clickAddReply">添加答复
        </el-button>
      </div>
      <el-table :data="unionQaReplyList" v-loading="replyLoading" :header-cell-style="{background:'#ddd'}" border class="table"
                ref="multipleTable">
        <el-table-column fixed="left" prop="sort" label="答复排序" width="100">
          <template slot-scope="scope">
            <span v-if="!scope.row.isEdit">{{ scope.row.sort }}</span>
            <el-input v-model="scope.row.sort" style="width: 100%;" type="number" v-if="scope.row.isEdit"
                      placeholder="请输入答复排序" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="reply" label="回复内容" width="400">
          <template slot-scope="scope">
            <span v-if="!scope.row.isEdit">{{ scope.row.reply }}</span>
            <el-input v-model="scope.row.reply" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
                      placeholder="请输入回复内容" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="pictureUrl" label="图片" width="200">
          <template slot-scope="scope">
            <el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
                       :disabled="!scope.row.isEdit" list-type="picture" :on-success="imgUploadSuccess"
                       :show-file-list="false">
              <img :src="scope.row.pictureUrl!=null?scope.row.pictureUrl:blankImg"
                   v-loading.fullscreen.lock="fullscreenLoading" @click="uploadImgUrl(scope.row)" style="width: 180px;height: 210px;">
            </el-upload>
          </template>
        </el-table-column>
        <el-table-column prop="routeUrl" label="跳转路径" width="200">
          <template slot-scope="scope">
            <span v-if="!scope.row.isEdit">{{ scope.row.routeUrl }}</span>
            <el-input v-model="scope.row.routeUrl" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
                      placeholder="请输入跳转路径" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
          </template>
        </el-table-column>
        <el-table-column
            prop="createTime"
            label="创建时间"
            width="150"
        ></el-table-column>
        <el-table-column
            prop="founder"
            label="创建人"
            width="100"
        ></el-table-column>
        <el-table-column fixed="right" min-width="180" label="操作">
          <template slot-scope="scope">
            <el-button @click="openEdit(scope.row,1,scope.$index)" type="success" size="small" :disabled="scope.row.isEdit"
                       v-if="!scope.row.isEdit" icon="el-icon-edit">修改
            </el-button>
            <el-button @click="updateUnionQAReply(scope.row)" type="primary" size="small"
                       v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
            </el-button>
            <el-popconfirm title="确定此答复信息删除吗？" @confirm="deleteUnionQAReply(scope.row)">
              <el-button type="danger" size="small" slot="reference" style="margin-left: 10px" icon="el-icon-delete">删除
              </el-button>
            </el-popconfirm>

          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!--图片预览-->
    <el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
      <img :src="imageUrl!=null?imageUrl:blankImg" style="width: 100%;height: auto;margin: 0 auto;"/>
    </el-dialog>
  </div>

</template>

<script>

export default {
  name: "qaLibraryType",
  components: {},
  data() {
    return {
      url: '',
      imageUrl: '',
      uploadType: null,
      blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
      imgModal: false,
      replyModal: false,
      qaTypeOptions: [],
      value: '',
      replyIndex: null,
      hrefUrl: "https://biapi.xiaoyujia.com/unionQa/importQaTemplateDownload",
      uploadId: 1,
      excelUploadId: 1,
      isModal: false,
      dialogFlag: false,
      excelUploadUrl: "https://biapi.xiaoyujia.com/unionQa/importUnionQa/" + localStorage.getItem("id"),
      isEdit: false,
      showEdit: true,
      certModal: false,
      fullscreenLoading: false,
      unionQaList: [],
      unionQaReplyList: [],
      loading: true,
      replyLoading: true,
      excelloading: false,
      pageSizeOpts: [5, 10, 20],
      unionQA: {
        founder: localStorage.getItem("id")
      },
      unionQAReply: {
        qaId: null,
        founder: localStorage.getItem("id"),
        isEdit: true
      },
      certType: {},
      pageInfo: {
        total: 10,
        size: 5,
        current: 1,
        pages: 1
      },
      expands: [],
      getRowKeys(row) {
        return row.id
      },
      quer: {
        "title": "",
        "tags": "",
        "reply": "",
        "typeID": null,
        "current": 1,
        "size": 10
      },
    }
  },
  created() {
    this.getData()
    this.getUnionQaAllType()
  },
  methods: {
    clickAddReply() {
      this.uploadType = 0
      this.replyModal = true
    },
    uploadImgUrl(val){
      if(val.isEdit){
      this.fullscreenLoading = true;
      }else {
        // 打开图片预览
        if (val.pictureUrl != null && val.pictureUrl != '') {
          this.imageUrl = val.pictureUrl
        } else {
          this.imageUrl = this.blankImg
        }
        this.imgModal = true
      }
    },
    // 图片上传成功
    imgUploadSuccess(res, file) {
      if (this.uploadType === 0) {
        this.unionQAReply.pictureUrl = res.data
      }
      if (this.uploadType === 1) {
        this.unionQaReplyList[this.replyIndex].pictureUrl = res.data
      }
      this.fullscreenLoading = false;

    },
    selectQAReply(val) {
      this.dialogFlag = true
      this.unionQAReply.qaId = val
      let obj = {
        qaId: val
      }
      // 获取答复列表
      this.$getData("getUnionQaReply", obj).then(res => {
        if (res.code == 0) {
          this.unionQaReplyList = res.data
          // 追加编辑状态位
          for (let item of this.unionQaReplyList) {
            this.$set(item, "isEdit", false)
          }
          this.replyLoading = false
        } else {
          this.$message.error('未查询到问答回复信息!')
        }
      })
    },
    getUnionQaAllType() {
      // 获取证书列表
      this.$getData("getUnionQaAllType", "").then(res => {
        if (res.code == 0) {
          this.qaTypeOptions = res.data
        } else {
          this.$message.error('未查询到问答类型!')
        }
      })
    },
    getData() {
      // 获取证书列表
      this.$getData("getUnionQaList", this.quer).then(res => {
        this.loading = false
        if (res.code == 0) {
          this.unionQaList = res.data.records
          this.pageInfo.current = res.data.current
          this.pageInfo.size = res.data.size
          this.pageInfo.total = res.data.total
          // 追加编辑状态位
          for (let item of this.unionQaList) {
            this.$set(item, "isEdit", false)
          }
        } else {
          this.unionQaList = []
          this.$message.error('未查询到问答类型!')
        }
      })
    },
    // 搜索
    query() {
      this.loading = true
      this.quer.current = 1
      this.getData()
    },
    // 页码大小
    onPageSizeChange(size) {
      this.loading = true;
      this.quer.size = size
      this.getData()
    },
    // 跳转页码
    onChange(index) {
      this.loading = true;
      this.quer.current = index
      this.getData()
    },
    // 证书Excel模板下载
    excelDownload() {
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    // Excel表格上传成功
    excelUploadSuccess(res, file) {
      this.excelloading = false;
      this.$message.success('问答信息导入成功！')
      this.getData()
    },
    // Excel表格上传失败
    excelUploadError() {
      this.excelloading = false;
      this.$message.error('问答信息解析失败！请使用正确的Excel模板进行上传！')
    },
    // 打开修改
    openEdit(val,flag,index) {
      this.replyIndex = index
      val.isEdit = true
      if(flag===1){
        this.uploadType=1
      }
    },
    // 添加答复信息
    saveUnionQAReply() {
      // 数据校验
      if (!this.unionQAReply.reply) {
        this.$message.error('请输入答复内容！')
      } else if (!this.unionQAReply.sort) {
        this.$message.error('请输入答复排序！')
      } else if (this.unionQAReply.sort <= 0) {
        this.$message.error('答复排序不可为负数或0！')
      } else {
        this.$postData("saveUnionQAReply", this.unionQAReply).then(res => {
          if (res.code == 0) {
            this.$message.success('添加成功!')
            this.replyModal = false
            this.unionQAReply.reply = ''
            this.unionQAReply.sort = null
            this.unionQAReply.pictureUrl = ''
            this.unionQAReply.routeUrl = ''
            this.selectQAReply(this.unionQAReply.qaId)
          } else {
            this.$message.error('添加失败！' + res.msg)
          }
        })
      }
    },
    // 添加问答信息
    saveUnionQA() {
      // 数据校验
      if (!this.unionQA.title) {
        this.$message.error('请输入问题标题！')
      } else if (!this.unionQA.tags) {
        this.$message.error('请输入问题标签！')
      } else if (!this.unionQA.typeID) {
        this.$message.error('请选择问答类型！')
      } else {
        this.$postData("saveUnionQA", this.unionQA).then(res => {
          if (res.code == 0) {
            this.$message.success('添加成功!')
            this.certModal = false
            this.unionQA.title = ''
            this.unionQA.tags = ''
            this.unionQA.typeID = ''
            this.getData()
          } else {
            this.$message.error('添加失败！' + res.msg)
          }
        })
      }
    },
    // 删除问答信息
    deleteUnionQAReply(val) {
      this.$postData("deleteUnionQAReply", val).then(res => {
        if (res.code == 0) {
          this.$message.success('删除成功!')
          this.selectQAReply(this.unionQAReply.qaId)
        } else {
          this.$message.error('删除失败！' + res.msg)
        }
      })
    },
    // 删除问答信息
    deleteUnionQA(val) {
      this.$postData("deleteUnionQA", val).then(res => {
        if (res.code == 0) {
          this.$message.success('删除成功!')
          this.getData()
        } else {
          this.$message.error('删除失败！' + res.msg)
        }
      })
    },
    // 更改问答信息
    updateUnionQA(val) {
      // 数据校验
      if (!val.title) {
        this.$message.error('请输入问题标题！')
      } else if (!val.tags) {
        this.$message.error('请输入问题标签！')
      } else if (!val.typeID) {
        this.$message.error('请选择问答类型！')
      } else {
        val.founder = localStorage.getItem("id")
        this.$postData("updateUnionQA", val).then(res => {
          if (res.code == 0) {
            this.$message.success('更新成功!')
            this.getData()
            val.isEdit = false
          } else {
            this.$message.error('更新失败！' + res.msg)
          }
        })
      }
    },
    // 更改问答信息
    updateUnionQAReply(val) {
      // 数据校验
      if (!val.reply) {
        this.$message.error('请输入答复内容！')
      } else if (!val.sort) {
        this.$message.error('请输入答复排序！')
      } else if (val.sort <= 0) {
        this.$message.error('答复排序不可为负数或0！')
      } else {
        val.founder = localStorage.getItem("id")
        this.$postData("updateUnionQAReply", val).then(res => {
          if (res.code == 0) {
            this.$message.success('更新成功!')
            this.selectQAReply(this.unionQAReply.qaId)
            val.isEdit = false
          } else {
            this.$message.error('更新失败！' + res.msg)
          }
        })
      }
    },
  },
}
</script>

<style scoped>
.handle-box {
  /*margin-bottom: 10px;*/
  font-weight: bold !important;
}

table {
  border-collapse: collapse;
  /*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
  border-left: #C8B9AE solid 1px;
  border-top: #C8B9AE solid 1px;
}

table td {
  border-right: #C8B9AE solid 1px;
  border-bottom: #C8B9AE solid 1px;
}

th {
  background: #eee;
  font-weight: normal;
}

tr {
  background: #fff;
}

tr:hover {
  background: #cc0;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

>>> .el-upload--text {
  width: 200px;
  height: 150px;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 300px;
  height: 300px;
  display: block;
}

.detail-title {
  margin: 10px 20px;
}

.handle-input {
  width: 400px;
  display: inline-block;
}

.mr10 {
  margin-right: 10px;
}

.big-input {
  width: 200px;
}

.inline-block {
  display: inline-block;
}
</style>