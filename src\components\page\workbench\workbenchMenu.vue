<template>
	<div class="container" id="pdfDom">

		<!-- 搜索栏目 -->
		<div class="handle-box">
			<el-form ref="form" :model="pageInfo">
				<el-row>
					<el-col :span="4">
						<el-form-item label="菜单名称" style="margin-right: 20px">
							<el-input v-model="quer.menuName" placeholder="请输入菜单名称"></el-input>
						</el-form-item>
					</el-col>
          <el-col :span="4">
            <el-form-item label="角色" style="margin-right: 20px">
              <el-select filterable v-model="quer.roleId" placeholder="请选择角色" clearable>
                <el-option v-for="item in roleList" :key="item.id" :label="item.name"
                           :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
					<el-col :span="6">
						<el-form-item label="工号、姓名" style="margin-right: 20px">
							<el-input v-model="quer.search" placeholder="请输入工号、姓名"></el-input>
						</el-form-item>
					</el-col>


					<el-col :span="6" style="margin: 32px 0;">
						<el-button type="primary" @click="query()" icon="el-icon-search">搜索
						</el-button>

						<el-button style="margin-left: 10px" type="success" icon="el-icon-circle-plus-outline"
							@click="certModal = true,uploadType = 1">添加
						</el-button>
					</el-col>
				</el-row>

			</el-form>
		</div>




		<!-- 数据表格 -->
		<el-table :data="list" v-loading="loading" border stripe
			:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
			:expand-row-keys="expands" @expand-change="expandSelect">

			<el-table-column width="120" prop="menuName" label="菜单名称">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{ scope.row.menuName }}</span>

					<el-input v-model="scope.row.menuName" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
						placeholder="请输入计划标题" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>

      <el-table-column prop="menuIcon" label="菜单图标" width="130">
        <template slot-scope="scope">
          <el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
                     :disabled="!scope.row.isEdit" list-type="picture" :on-success="imgUploadSuccess"
                     :show-file-list="false">
            <img :src="scope.row.menuIcon?scope.row.menuIcon:blankImg" style="width: 90px;height: 90px;">
          </el-upload>
        </template>
      </el-table-column>


			<el-table-column width="200" prop="roleNames" label="可见权限">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">
						{{scope.row.roleNames}}
					</span>

					<el-select filterable  multiple v-if="scope.row.isEdit" v-model="scope.row.selectRoles" placeholder="请选择可见权限">
						<el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.name">
						</el-option>
					</el-select>
				</template>
			</el-table-column>

      <el-table-column width="250" prop="jumpUrl" label="跳转路径">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.jumpUrl }}</span>

          <el-input v-model="scope.row.jumpUrl" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
                    placeholder="请输入跳转路径" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>


      <el-table-column width="140" prop="appointPersonnelName" label="指定可用人员">
        <template slot-scope="scope">
          <span v-if="!scope.row.isEdit">{{ scope.row.appointPersonnelName }}</span>

          <el-input v-model="scope.row.appointPersonnel" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
                    placeholder="请输入指定可用人员id" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
        </template>
      </el-table-column>


			<el-table-column width="140" prop="createTime" label="创建时间">
				<template slot-scope="scope">
					<span>{{scope.row.createTime}}</span>
				</template>
			</el-table-column>

			<el-table-column fixed="right" min-width="180" label="操作" v-if="showEdit">
				<template slot-scope="scope">
					<el-button @click="openEdit(scope.row,scope.$index)" type="success" size="small" :disabled="scope.row.isEdit"
						v-if="!scope.row.isEdit" icon="el-icon-edit">修改
					</el-button>
					<el-button @click="updateWorkbench(scope.row)" type="primary" size="small" v-if="scope.row.isEdit"
						icon="el-icon-circle-check">保存
					</el-button>

				</template>
			</el-table-column>

		</el-table>


		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--添加计划-->
		<el-dialog :visible.sync="certModal" width="60%" title="添加菜单" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-col :span="12">
						<el-form ref="ruleForm"  class="demo-ruleForm" size="mini">

							<el-form-item label="菜单名称：">
								<el-input v-model="workbenchMenu.menuName" type="text" class="handle-input mr10"
									placeholder="请输入菜单名称">
								</el-input>
							</el-form-item>

              <el-form-item label="菜单图标：" >
                <el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
                           list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
                  <img :src="workbenchMenu.menuIcon?workbenchMenu.menuIcon:blankImg"
                       style="width: 150px;height: 150px;" >
                </el-upload>
              </el-form-item>

							<el-form-item label="可见权限：">
								<el-select filterable  multiple v-model="workbenchMenu.selectRoles" placeholder="请选择可见权限" clearable>
									<el-option v-for="item in roleList" :key="item.id" :label="item.name"
										:value="item.id">
									</el-option>
								</el-select>
							</el-form-item>


              <el-form-item label="跳转路径：">
                <el-input v-model="workbenchMenu.jumpUrl" style="width: 290px" type="text" class="handle-input mr10"
                          placeholder="请输入跳转路径">
                </el-input>
              </el-form-item>


              <el-form-item label="指定可用人员(employeeId)：">
                <el-input v-model="workbenchMenu.appointPersonnel" style="width: 290px" type="textarea" class="handle-input mr10"
                          placeholder="请输入指定可用人员">
                </el-input>
              </el-form-item>


						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="saveWorkbench()" type="success" size="small">确定添加
					</el-button>
				</div>

			</div>
		</el-dialog>

	</div>

</template>

<script>
	export default {
		name: "qaLibraryType",
		components: {},
		data() {
			return {
        roleList: [],
        blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				url: '',
				uploadType: 1,
        itemIndex: null,
				excelUploadId: 1,
				isModal: false,
				isEdit: false,
				showEdit: true,
				certModal: false,
				list: [],
				loading: true,
				pageSizeOpts: [5, 10, 20],
        workbenchMenu: {
          menuName: "",
          menuIcon: "",
          roleIds: "",
          jumpUrl: "",
          appointPersonnel: "",
          operator: localStorage.getItem("id"),
          selectRoles: [],
        },
				unionPlan: {
          operator: localStorage.getItem("id"),
          selectRoles: [],
				},
				certType: {},
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				expands: [],
				getRowKeys(row) {
					return row.id
				},
				quer: {
					"menuName": "",
					"menuIcon": "",
					"planType": null,
					"current": 1,
					"size": 10
				},
			}
		},
		created() {
			this.getData()
			this.getRoleList()
		},
		methods: {
      // 图片上传成功
      imgUploadSuccess(res, file) {
        if(this.uploadType==1){
          this.workbenchMenu.menuIcon = res.data
        }else{
          this.list[this.itemIndex].menuIcon = res.data
        }
      },
      getRoleList(){
        this.$getData("getRoleList", {}).then(res => {
          this.loading = false
          if (res.code == 0) {
            this.roleList = res.data
          } else {
            this.$message.error('获取角色列表失败!')
          }
        })
      },
			getData() {
				// 获取成长计划
				this.$getData("getWorkbench", this.quer).then(res => {
					this.loading = false
					if (res.code == 0) {
						this.list = res.data.records
						this.pageInfo.current = res.data.current
						this.pageInfo.size = res.data.size
						this.pageInfo.total = res.data.total
						// 追加编辑状态位
						for (let item of this.list) {
							this.$set(item, "isEdit", false)
						}
					} else {
						this.list = []
						this.$message.error('未查询到菜单信息!')
					}
				})
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.getData()
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			// 打开修改
			openEdit(val,index) {
				val.isEdit = true
        this.itemIndex = index
        this.uploadType = 0
			},
      saveWorkbench() {
				// 数据校验
				if (!this.workbenchMenu.menuName) {
					this.$message.error('请输入菜单名称！')
				} else if (!this.workbenchMenu.menuIcon) {
					this.$message.error('请上传菜单图标！')
				} else if (this.workbenchMenu.selectRoles.length<=0&&!this.workbenchMenu.appointPersonnel) {
					this.$message.error('可见权限和指定可用人员至少一个不为空！')
				} else if (!this.workbenchMenu.jumpUrl) {
          this.$message.error('请输入跳转路径！')
        } else {
          let roleIds = ""
          for (let i = 0; i < this.workbenchMenu.selectRoles.length; i++) {
            roleIds+=this.workbenchMenu.selectRoles[i]+","
          }
          this.workbenchMenu.roleIds = roleIds.substring(0,roleIds.length-1)
					this.$postData("saveWorkbench", this.workbenchMenu).then(res => {
						if (res.code == 0) {
							this.$message.success('添加成功!')
							this.certModal = false
							this.workbenchMenu.menuName = ''
							this.workbenchMenu.menuIcon = ''
							this.workbenchMenu.selectRoles = []
							this.workbenchMenu.appointPersonnel = ''
							this.workbenchMenu.jumpUrl = ''
							this.getData()
						} else {
							this.$message.error('添加失败！' + res.msg)
						}
					})
				}
			},
      updateWorkbench(val) {
        // 数据校验
        if (!val.menuName) {
          this.$message.error('请输入菜单名称！')
        } else if (!val.menuIcon) {
          this.$message.error('请上传菜单图标！')
        } else if (val.selectRoles.length<=0&&!val.appointPersonnel) {
          this.$message.error('可见权限和指定可用人员至少一个不为空！')
        } else if (!val.jumpUrl) {
          this.$message.error('请输入跳转路径！')
        } else {
          let roleIds = ""
          for (let i = 0; i < val.selectRoles.length; i++) {
            for (let j = 0; j < this.roleList.length; j++) {
              if (val.selectRoles[i]===this.roleList[j].name){
                roleIds+=this.roleList[j].id+","
              }
            }
          }
          val.roleIds = roleIds.substring(0,roleIds.length-1)
          this.$postData("updateWorkbench", val).then(res => {
            if (res.code == 0) {
              this.$message.success('更新成功!')
              this.getData()
              val.isEdit = false
            } else {
              this.$message.error('更新失败！' + res.msg)
            }
          })
        }
			},
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>