<template>
    <div id="load" class="load">
        <!--存放百度地图容器-->
        <div id="container" class="container"></div>
    </div>
</template>

<script>

    export default {
        props:{
            points:{
                type: Array,
                default:  () => []
            },

        },
        data() {
            return {
                // 热力图的点数据，lng：点的经度 点的lat纬度 count：点的热力程度
                map: '',// 保存地图实例
                centerLng: '24.477308', // 经度118.08243, "lat": 24.44579
                centerLat: '118.16656', // 纬度
                heatmapOverlay: {},// 热力图覆盖物
            }
        },
        methods: {
            // 创建和初始化地图函数
            initMap(){
                this.createMap();// 创建地图
                this.initHeatMap();// 添加热力图覆盖物
            },
            // 方法 - 创建地图
            createMap(){
                // 在百度地图容器中创建地图实例
                let map = new BMap.Map("container");
                // 将map变量存储在全局
                this.map = map;
                // 设定地图的中心点和坐标
                let point = new BMap.Point(this.centerLng, this.centerLat);
                // 设置中心点坐标和地图级别
                this.map.centerAndZoom(point, 13);
                // 允许滚轮缩放
                this.map.enableScrollWheelZoom(point, 13);
                // 添加左上角缩放比例尺(offset: new BMap.Size(0, 0)为比例尺的坐标位置)
                var opts = {offset: new BMap.Size(10, 10)}
                this.map.addControl(new BMap.NavigationControl(opts));
                // 设置地图默认缩放比例
                this.map.setZoom(13);
            },
            // 方法 - 添加热力图覆盖物
            initHeatMap () {
                this.heatmapOverlay = new BMapLib.HeatmapOverlay({
                    // 热力图的每个点的半径大小
                    'radius': BMAP_POINT_SIZE_SMALL * 10,
                    // 热力的透明度0~1
                    'opacity': 0.8,
                    // 其中 key 表示插值的位置0~1，value 为颜色值
                    'gradient': {
                        0:'rgb(0,255,1)',
                        .5:'rgb(255, 170, 0)',
                        1:'rgb(255, 0, 0)'
                    }
                })
                // 清除图层(每次重新调用需要清除上一个覆盖物图层)
                this.map.clearOverlays();
                // 添加热力覆盖物
                this.map.addOverlay(this.heatmapOverlay);
                console.log( this.points)
                this.heatmapOverlay.setDataSet({data: this.points, max: 5});
                // 显示热力图，隐藏为this.heatmapOverlay.hide();
                this.heatmapOverlay.show();
            }
        },
        mounted() {
            // 创建和初始化地图函数

            this.initMap();
            let self=this
            setTimeout(function (){
                self.initHeatMap()

            }, 2000);
        }
    }
</script>

<style  scoped>
    .load {
        width: 100%;
        height: 100%;
    }
    .container {
        width: 100%;
        height: 100%;
        border: #ccc solid 1px;
    }
</style>
