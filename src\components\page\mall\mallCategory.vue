<template>
<div class="app-container">
  <el-card>
    <div class="filter-container">
      <el-input
        v-model="searchForm.name"
        placeholder="分类名称"
        style="width: 200px;"
        @keyup.enter.native="handleSearch"
      />
      <el-select
        v-model="searchForm.state"
        placeholder="状态"
        clearable
        style="width: 120px;margin-left:10px"
      >
        <el-option label="启用" :value="1" />
        <el-option label="禁用" :value="2" />
      </el-select>
      <el-button type="primary" @click="handleSearch" style="margin-left:10px">搜索</el-button>
      <el-button type="success" @click="addCategory" style="margin-left:10px">新增分类</el-button>
    </div>

    <el-table
      :data="tableData"
      border
      style="width: 100%;margin-top:20px;"
    >
      <el-table-column prop="id" label="ID"  />
      <el-table-column prop="name" label="分类名称" />
      <el-table-column label="图标">
        <template slot-scope="scope">
          <el-image
            :src="scope.row.icon"
            style="width:50px;height:50px"
            :preview-src-list="[scope.row.icon]"
          />
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.state === 1 ? 'success' : 'danger'">
            {{ scope.row.state | statusFilter }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column label="操作" width="220">
        <template slot-scope="scope">
          <el-button
            size="mini"
            :type="scope.row.state === 2 ? 'success' : 'info'"
            @click="handleStateChange(scope.row, scope.row.state === 1 ? 2 : 1)"
          >
            {{ scope.row.state === 1 ? '禁用' : '启用' }}
          </el-button>
          <el-button
            type="primary"
            size="mini"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            size="mini"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>


  </el-card>

  <el-dialog :title="form.id ? '编辑分类' : '新增分类'" :visible.sync="dialogVisible" width="500px">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="分类图标" prop="icon">
        <ImageUpload :limit="1" v-model="form.icon" />
      </el-form-item>
      <el-form-item label="状态" prop="state">
        <el-radio-group v-model="form.state">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="2">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </template>
  </el-dialog>
</div>
</template>

<script>
import ImageUpload from "@/components/common/ImageUpload.vue";

export default {
  components: { ImageUpload },
  filters: {
    statusFilter(state) {
      const statusMap = {
        1: '启用',
        2: '禁用'
      }
      return statusMap[state] || '未知'
    }
  },
  data() {
    return {
      dialogVisible: false,
      searchForm: {
        name: '',
        state: ''
      },
      form: {
        id: null,
        name: '',
        icon: '',
        state: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' }
        ],
        icon: [
          { required: true, message: '请上传分类图标', trigger: 'blur' }
        ],
        state: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      tableData: [],
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      this.$postData('mallCategoryList', {
        name: this.searchForm.name,
        state: this.searchForm.state
      }).then(res => {
        this.tableData = res.data // 直接使用接口返回数据
      })
    },
    handleSearch() {
      this.getList()
    },
    addCategory() {
      this.form = {
        id: null,
        name: '',
        icon: '',
        state: 1
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.formRef && this.$refs.formRef.clearValidate()
      })
    },
    handleEdit(row) {
      this.form = {
        id: row.id,
        name: row.name,
        icon: row.icon,
        state: row.state
      }
      this.dialogVisible = true
    },
    handleDelete(row) {
      this.$confirm('确认删除该分类?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$postData("mallCategoryDelete", { id: row.id }).then(res => {
          if (res.status === 200) {
            this.$message.success('删除成功')
            this.getList()
          }
        })
      })
    },
    handleStateChange(row, newState) {
      this.$postData("mallCategoryUpdate", {
        ...row,
        state: newState
      }).then(res => {
        if (res.status === 200) {
          this.$message.success('状态更新成功')
          this.getList()
        }
      })
    },
    submitForm() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          const api = this.form.id ? "mallCategoryUpdate" : "mallCategoryadd"
          this.$postData(api, this.form).then(res => {
            if (res.status === 200) {
              this.$message.success(this.form.id ? '修改成功' : '新增成功')
              this.dialogVisible = false
              this.getList()
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.filter-container {
  display: flex;
  gap: 10px;
}
.app-container {
  padding: 20px;
}
</style>
