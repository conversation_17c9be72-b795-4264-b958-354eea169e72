<template>
    <div>
        <!--        {{contract}}-->
        <el-collapse v-model="activeNames">

            <el-collapse-item title="新增合同补充说明" name="1">
                <template slot="title">
                    <el-button type="primary">添加合同补充说明</el-button>

                </template>
                <!--                :rules="ruleValidate"-->
                <el-form ref="form" :model="contractSupply" class="addContractSupply">

                    <row>
                        <el-col :span="12">
                            <el-form-item prop="weekDay">
                                <div class="label-name">原合同编号:</div>
                                <el-input placeholder="请输入原合同编号" v-model="contractSupply.oldNo" style="width: 70%">
                                </el-input>
                            </el-form-item>
                            <el-form-item prop="weekDay">
                                <div class="label-name">甲方(雇主):</div>
                                <el-input placeholder="请输入甲方(雇主)" v-model="contractSupply.memberName"
                                          style="width: 70%">
                                </el-input>
                            </el-form-item>
                            <el-form-item prop="weekDay">
                                <div class="label-name">甲方(电话):</div>
                                <el-input placeholder="请输入甲方(电话)" v-model="contractSupply.memberPhone"
                                          style="width: 70%" maxlength="11">
                                </el-input>
                            </el-form-item>
                            <el-form-item prop="weekDay">
                                <div class="label-name">甲方(地址):</div>
                                <el-input placeholder="请输入甲方(地址)" v-model="contractSupply.memberAdress"
                                          style="width: 70%">
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item prop="baomuName">
                                <div class="label-name">关联原保姆:</div>
                                <el-select
                                        style="width: 70%"
                                        v-model="oldBaomuno"
                                        filterable
                                        remote
                                        reserve-keyword
                                        placeholder="请输入保姆姓名"
                                        :remote-method="remoteMethod"
                                        @change="changbaomu"
                                        :loading="oldEmployeeloading">
                                    <el-option
                                            v-for="item in oldEmployeeoptions"
                                            :key="item.id"
                                            :label="item.label"
                                            :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item prop="weekDay">
                                <div class="label-name">原乙方(名字):</div>
                                <el-input placeholder="请输入原乙方(名字)" v-model="contractSupply.oldEmployeeName"
                                          style="width: 70%">
                                </el-input>
                            </el-form-item>
                            <el-form-item prop="weekDay">
                                <div class="label-name">原乙方(电话):</div>
                                <el-input placeholder="请输入原乙方(电话)" v-model="contractSupply.oldEmployeePhone"
                                          style="width: 70%" maxlength="11">
                                </el-input>
                            </el-form-item>
                            <el-form-item prop="weekDay">
                                <div class="label-name">原乙身份证:</div>
                                <el-input placeholder="请输入原乙方(身份证)" v-model="contractSupply.oldEmployeeCarId"
                                          style="width: 70%" maxlength="18">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </row>
                    <hr>
                    <br>
                    <!--                    <row>-->
                    <!--                        <el-col :span="12">-->
                    <!--                            <el-form-item   prop="employeeSignDate">-->
                    <!--                                <div class="label-name">离职日期:</div>-->
                    <!--                                <el-date-picker-->
                    <!--                                        style="width: 70%"-->
                    <!--                                        v-model="contractSupply.quitDate"-->
                    <!--                                        type="date"-->
                    <!--                                        value-format="yyyy-MM-dd HH:mm:ss"-->
                    <!--                                        placeholder="离职日期">-->
                    <!--                                </el-date-picker>-->
                    <!--                            </el-form-item>-->
                    <!--                        </el-col>-->
                    <!--                        <el-col :span="12">-->
                    <!--                            <el-form-item   prop="weekDay">-->
                    <!--                                <div class="label-name">更换原因:</div>-->
                    <!--                                <el-input placeholder="请输入更换原因" v-model="contractSupply.quitText" style="width: 70%">-->
                    <!--                                </el-input>-->
                    <!--                            </el-form-item >-->
                    <!--                        </el-col>-->
                    <!--                    </row>-->
                    <!--                    <hr>-->
                    <!--                    <br>-->
                    <el-row>
                        <el-col :span="12">
                            <el-form-item prop="weekDay">
                                <div class="label-name">代表人:</div>
                                <el-input placeholder="请输入代表人" v-model="contractSupply.agentName" style="width: 70%">
                                </el-input>
                            </el-form-item>
                          <el-form-item prop="serviceType">
                            <div class="label-name">服务类型:</div>
                            <el-input placeholder="请输入服务类型" v-model="contractSupply.serviceType" style="width: 70%">
                            </el-input>
                          </el-form-item>
                          <el-form-item prop="serviceType">
                            <div class="label-name">服务类型(其他):</div>
                            <el-input placeholder="请输入服务类型(其他)" v-model="contractSupply.serviceContentRemark" style="width: 70%">
                            </el-input>
                          </el-form-item>
                            <el-form-item prop="employeeSignDate">
                                <div class="label-name">离职日期:</div>
                                <el-date-picker
                                        style="width: 70%"
                                        v-model="contractSupply.quitDate"
                                        type="date"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        placeholder="原保姆离职日期">
                                </el-date-picker>
                            </el-form-item>
                          <el-form-item prop="employeeSignDate">
                            <div class="label-name">上户日期:</div>
                            <el-date-picker
                                style="width: 70%"
                                v-model="contractSupply.startDate"
                                type="date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                placeholder="上户日期">
                            </el-date-picker>
                          </el-form-item>
                            <el-form-item prop="employeeSignDate">
                                <div class="label-name">合同日期:</div>
                                <el-date-picker
                                        style="width: 70%"
                                        v-model="contractSupply.clientDate"
                                        type="date"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        placeholder="合同日期">
                                </el-date-picker>
                            </el-form-item>
                          <el-form-item prop="employeeSignDate">
                            <div class="label-name">截止日期:</div>
                            <el-date-picker
                                style="width: 70%"
                                v-model="contractSupply.endDate"
                                type="date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                placeholder="截止日期">
                            </el-date-picker>
                          </el-form-item>

<!--                            <el-form-item prop="employeeSignDate">-->
<!--                                <div class="label-name">截止日期:</div>-->
<!--                                <el-date-picker-->
<!--                                        style="width: 70%"-->
<!--                                        v-model="contractSupply.endDate"-->
<!--                                        type="date"-->
<!--                                        value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                                        placeholder="截止日期">-->
<!--                                </el-date-picker>-->
<!--                            </el-form-item>-->
                        </el-col>
                        <el-col :span="12">
                            <el-form-item prop="baomuName">
                                <div class="label-name">关联新保姆:</div>
                                <el-select
                                        style="width: 70%"
                                        v-model="newBaomuno"
                                        filterable
                                        remote
                                        reserve-keyword
                                        placeholder="请输入保姆姓名"
                                        :remote-method="newRemoteMethod"
                                        @change="newChangbaomu"
                                        :loading="newEmployeeloading">
                                    <el-option
                                            v-for="item in newEmployeeoptions"
                                            :key="item.id"
                                            :label="item.label"
                                            :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item prop="weekDay">
                                <div class="label-name">新乙方(名字):</div>
                                <el-input placeholder="请输入新乙方(名字)" v-model="contractSupply.employeeName"
                                          style="width: 70%">
                                </el-input>
                            </el-form-item>
                            <el-form-item prop="weekDay">
                                <div class="label-name">新乙方(电话):</div>
                                <el-input placeholder="请输入新乙方(电话)" v-model="contractSupply.employeePhone"
                                          style="width: 70%" maxlength="11">
                                </el-input>
                            </el-form-item>
                            <el-form-item prop="weekDay">
                                <div class="label-name">新乙身份证:</div>
                                <el-input placeholder="请输入新乙方(身份证)" v-model="contractSupply.employeeCarId"
                                          style="width: 70%">
                                </el-input>
                            </el-form-item>
                            <el-form-item prop="weekDay">
                                <div class="label-name">新劳务报酬:</div>
                                <el-input placeholder="请新劳务报酬" v-model="contractSupply.servicePay" type="number"
                                          style="width: 70%" maxlength="8">
                                </el-input>
                            </el-form-item>
                        </el-col>

                    </el-row>

                    <div style="text-align: right">
                        <Button type="primary" @click="save('form')">保存</Button>
                    </div>
                </el-form>
            </el-collapse-item>

        </el-collapse>
        <el-table
                :data="contractSupplyList"
                style="width: 100%">
            <el-table-column
                    type="selection"
                    width="35">
            </el-table-column>
            <el-table-column
                    prop="oldNo"
                    label="原合同编号"
                    width="80">
            </el-table-column>
            <el-table-column
                    prop="memberName"
                    label="客户名称">
            </el-table-column>
            <el-table-column
                    prop="createDate"
                    label="创建日期">
            </el-table-column>
            <el-table-column
                    prop="quitDate"
                    label="离职日期">
            </el-table-column>
            <el-table-column
                    prop="oldEmployeeName"
                    label="原阿姨名称">
            </el-table-column>
            <el-table-column
                    prop="employeeName"
                    label="新阿姨名称">
            </el-table-column>
            <el-table-column
                    prop="state"
                    label="状态"
                    width="120">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status==2" type="success">完成</el-tag>
                    <el-tag v-if="scope.row.status==1" type="success">展示</el-tag>
                    <el-tag v-else-if="scope.row.status==0" type="warning">暂存</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                    label="操作"
                    fixed="right"
                    min-width="180">
                <template slot-scope="scope">
                    <!--                    <el-button size="mini" @click="delbaomuExp(scope.row.id)" type="info">删除</el-button>-->
                    <!--                    <el-button size="mini" @click="upbaomuExp(scope.row)" type="primary">编辑</el-button>-->
                    <el-button size="mini" @click="getUrl(scope.row)" type="primary">签名</el-button>
                    <el-button v-if="scope.row.status==1" size="mini" @click="update(scope.row,0)" type="primary">设为暂存
                    </el-button>
                    <el-button v-if="scope.row.status==0" size="mini" @click="update(scope.row,1)" type="info">设为生效
                    </el-button>
                </template>

            </el-table-column>
        </el-table>
        <el-dialog
                title="签名Url地址"
                :visible.sync="dialogVisible"
                :modal="false"
                width="50%"
        >
            <!--                    <el-input placeholder="请输入技能说明" v-model="url"  id="urlinput">-->
            <!--                    </el-input>-->
            <h2>阿姨签名地址</h2>
            <img width="50%" :src="eImageUrl" alt="">
            <div>点击复制Url,并把地址发送给指定的阿姨。完成合同的阿姨签名。</div>
            <hr>
            <article id="article">
                {{url}}
            </article>
            <br>
            <br>
            <br>
            <h2>客户签名地址</h2>
            <img width="50%" :src="mImageUrl" alt="">
            <div>点击复制Url,并把地址发送给指定的客户。完成合同的客户签名。</div>
            <hr>
            <article id="article2">
                {{url2}}
            </article>
            <span slot="footer" class="dialog-footer">
                        <el-button @click="dialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="dialogVisible = false,copyurl()">复制阿姨Url</el-button>
                        <el-button type="primary" @click="dialogVisible = false,copyurl2()">复制客户Url</el-button>
            </span>
        </el-dialog>
    </div>

</template>

<script>
import moment from "moment";
    export default {
        name: "contractSupply",
        props: ['model'],
        data() {
            return {
                isSave: false,
                dialogVisible: false,
                eImageUrl: null,
                mImageUrl: null,
                oldBaomuno: null,
                oldEmployeeoptions: [],
                oldEmployeeloading: false,
                newBaomuno: null,
                newEmployeeoptions: [],
                newEmployeeloading: false,
                ruleValidate: {
                    weekDay: [
                        {required: true, message: '请输入完整的正确信息', trigger: 'change'}
                    ],
                },
                contract: this.model,
                contractSupply: {
                    id: null,
                    memberId: null,
                    employeeId: null,
                    memberName: null,
                    employeeName: null,
                    memberCardId: null,
                    employeeCarId: null,
                    memberPhone: null,
                    employeePhone: null,
                    memberAdress: null,
                    employeeAdress: null,
                    agentId: null,
                    agentName: null,
                    createDate: null,
                    memberSignDate: null,
                    employeeSignDate: null,
                    updateDate: null,
                    status: 0,
                    orderId: null,
                    oldNo: null,
                    oldEmployeeId: null,
                    oldEmployeeName: null,
                    oldEmployeeCarId: null,
                    oldEmployeePhone: null,
                    oldEmployeeAdress: null,
                    quitDate: null,
                    clientDate: null,
                    contractId: null,
                    quitText: null,
                    endDate: null,
                    startDate: null,
                    servicePay: null
                },
                activeNames: [],

                contractSupplyList: [],
                url: '',
                url2: '',
            }
        },
        created() {
            this.getContractSupplyList();
            this.contractSupply.contractId = this.contract.id;
            this.contractSupply.orderId = this.contract.orderId;
            this.contractSupply.oldNo = this.contract.no;
            this.contractSupply.memberId = this.contract.memberId;
            this.contractSupply.memberCardId = this.contract.memberCardId;
            this.contractSupply.memberName = this.contract.memberName;
            this.contractSupply.memberPhone = this.contract.memberPhone;
            this.contractSupply.memberAdress = this.contract.memberAdress;
            this.contractSupply.agentId = this.contract.agentId;
            this.contractSupply.agentName = this.contract.agentName;
            this.contractSupply.clientDate = this.contract.serviceStarDate;
            this.contractSupply.endDate = this.contract.serviceEndDate;

            this.contractSupply.oldEmployeeId = this.contract.employeeId;
            this.contractSupply.oldEmployeeName = this.contract.employeeName;
            this.contractSupply.oldEmployeeAdress = this.contract.employeeAdress;
            this.contractSupply.oldEmployeePhone = this.contract.employeePhone;
            this.contractSupply.oldEmployeeCarId = this.contract.employeeCarId;
        },
        methods: {
            getUrl(row) {
                console.log(row);
                this.eImageUrl = null;
                this.mImageUrl = null;
                this.getSigin(row.memberId, row.id);
                this.getESigin(row.employeeId, row.id);
                this.dialogVisible = true;
                this.url = "https://agent.xiaoyujia.com/upbaomu/baomuContractInfoSupply/" + row.id;
                this.url2 = "https://agent.xiaoyujia.com/upbaomu/contractInfoSupply/" + row.id;
            },
            copyurl() {
                this.copyArticle();
                return this.$message.success("复制成功。");
            },
            copyurl2() {
                this.copyArticle2();
                return this.$message.success("复制成功。");
            },
            copyArticle(event) {
                const range = document.createRange();
                range.selectNode(document.getElementById('article'));
                const selection = window.getSelection();
                if (selection.rangeCount > 0) selection.removeAllRanges();
                selection.addRange(range);
                document.execCommand('copy');
            },
            copyArticle2(event) {
                const range = document.createRange();
                range.selectNode(document.getElementById('article2'));
                const selection = window.getSelection();
                if (selection.rangeCount > 0) selection.removeAllRanges();
                selection.addRange(range);
                document.execCommand('copy');
            },
            getSigin(id, contractId) {
                if (id == null) {
                    this.$message.error("该合同还未绑定用户，");
                }
                let show = {
                    contractId: contractId,
                    status: 1,
                    // memberId:id,
                    siginType: 0
                };
                this.$postData("get_contractSigin", show, {}).then(res => {
                    if (res.status == 200) {
                        if (res.data.length == 0) {
                            return this.$message.error("客户暂未签名");
                        } else {
                            console.log(res.data[0].siginUrl);
                            // this.dialogVisible=true
                            this.mImageUrl = res.data[0].siginUrl;
                        }
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getESigin(id, contractId) {
                if (id == null) {
                    return this.$message.error("该合同还未绑定用户，");
                }
                let show = {
                    contractId: contractId,
                    status: 1,
                    employeeId: id,
                    siginType: 1
                };
                this.$postData("get_contractSigin", show, {}).then(res => {
                    if (res.status == 200) {
                        if (res.data.length == 0) {
                            return this.$message.error("阿姨暂未签名");
                        } else {
                            console.log(res.data[0].siginUrl);
                            // this.dialogVisible=true
                            this.eImageUrl = res.data[0].siginUrl;
                        }
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            update(row, status) {
                row.status = status;
                this.$postData("save_contractSupply", row, {}).then(res => {
                    if (res.status == 200) {
                        this.$Message.success('更新成功');
                        this.getContractSupplyList();
                    } else {
                        this.$message.error("更新失败，" + res.msg);
                    }
                })
            },
            save(name) {
                const sp = this.contractSupply.servicePay;
                if (sp == null) {
                    return this.$message.error("新增失败，请输入新劳务报酬");
                }
                if (this.contractSupply.quitDate == null) {
                    return this.$message.error("新增失败，请选择离职日期");
                }
                if (this.contractSupply.clientDate == null) {
                    return this.$message.error("新增失败，请选择合同日期");
                }
                if (sp.indexOf("-") > -1 || sp.indexOf("+") > -1) {
                    return this.$message.error("新增失败，错误的劳务报酬，请重新输入");
                }
                const sd = this.contractSupply.startDate;
                if (sd == null) {
                    return this.$message.error("新增失败，请选择起始日期");
                }
                const ed = this.contractSupply.endDate;
                if (ed == null) {
                    return this.$message.error("新增失败，请选择截止日期");
                }
                const sdt = new Date(sd).getTime();
                const edt = new Date(ed).getTime();
                const time = edt - sdt;
                if(time < 0) {
                    return this.$message.error("新增失败，截止日期比起始时间小");
                }
                if(sdt === edt) {
                    return this.$message.error("新增失败，截止日期和起始时间是同一天");
                }

                if (this.isSave) {
                    return;
                }
                this.isSave = true;
                this.contractSupply.status = 1;
                this.$postData("save_contractSupply", this.contractSupply, {}).then(res => {
                    if (res.status == 200) {
                        this.$Message.success('新增成功');
                        this.activeNames = [];
                        this.getContractSupplyList();
                    } else {
                        this.$message.error("新增失败，" + res.msg);
                    }
                }).finally(() => {
                    setTimeout(() => {
                        this.isSave = false;
                    }, 1500);
                });
            },
            newChangbaomu(item) {
                this.contractSupply.employeeId = item;
                this.newEmployeeoptions.forEach(v => {
                    if (v.value == item) {
                        console.log(v);
                        this.contractSupply.employeeCarId = v.idcard;
                        this.contractSupply.employeeName = v.label;
                        this.contractSupply.employeePhone = v.employeephone;
                        this.contractSupply.employeeAdress = v.hometown;
                    }
                })

            },
            changbaomu(item) {
                this.contractSupply.oldEmployeeId = item;
                this.oldEmployeeoptions.forEach(v => {
                    if (v.value == item) {
                        console.log(v);
                        this.contractSupply.oldEmployeeCarId = v.idcard;
                        this.contractSupply.oldEmployeeName = v.label;
                        this.contractSupply.oldEmployeePhone = v.employeephone;
                        this.contractSupply.oldEmployeeAdress = v.hometown;
                    }
                })

            },
            remoteMethod(query) {
                this.oldEmployeeoptions = [];

                if (query !== '') {
                    this.oldEmployeeloading = true;
                    this.$getData("get_baomubyno", {no: query, storeId: localStorage.getItem("storeId")}).then(res => {
                        if (res.status == 200) {
                            this.oldEmployeeloading = false;

                            res.data.forEach((item, index, arr) => {
                                var a = {};
                                a.id = item.id;
                                a.value = item.id;
                                a.label = item.realName;
                                a.idcard = item.idcard;
                                a.employeephone = item.phone;
                                a.hometown = item.hometown;
                                this.oldEmployeeoptions.push(a);
                            });
                        } else {
                            this.$message.error("修改失败，" + res.msg);
                        }
                    })
                } else {
                    this.oldEmployeeoptions = [];
                }
            },
            newRemoteMethod(query) {
                this.newEmployeeoptions = [];

                if (query !== '') {
                    this.newEmployeeloading = true;
                    this.$getData("get_baomubyno", {no: query, storeId: localStorage.getItem("storeId")}).then(res => {
                        if (res.status == 200) {
                            this.newEmployeeloading = false;

                            res.data.forEach((item, index, arr) => {
                                var a = {};
                                a.id = item.id;
                                a.value = item.id;
                                a.label = item.realName +'  '+item.phone;
                                a.idcard = item.idcard;
                                a.employeephone = item.phone;
                                a.hometown = item.hometown;
                                this.newEmployeeoptions.push(a);
                            });

                        } else {
                            this.$message.error("修改失败，" + res.msg);
                        }
                    })
                } else {
                    this.newEmployeeoptions = [];
                }
            },
            getContractSupplyList() {
                this.$getUrl("list_contractSupply", this.contract.id, {}).then(res => {
                    if (res.status == 200) {
                        this.contractSupplyList = res.data
                      if (this.contractSupplyList.length > 0) {
                        let supplyDto = this.contractSupplyList[this.contractSupplyList.length -1];
                        this.contractSupply.oldEmployeeId = supplyDto.employeeId;
                        this.contractSupply.oldEmployeeName = supplyDto.employeeName;
                        this.contractSupply.oldEmployeeAdress = supplyDto.employeeAdress;
                        this.contractSupply.oldEmployeePhone = supplyDto.employeePhone;
                        this.contractSupply.oldEmployeeCarId = supplyDto.employeeCarId;

                      }
                    } else {
                        this.$message.error("失败，" + res.msg);
                    }
                })
            },
        }
    }
</script>

<style scoped>
    .addContractSupply {
        background: #f5f7fa;
        padding: 20px;
        border: 1px solid #ccc;
    }

    .label-name {
        width: 100px;
    }
</style>
