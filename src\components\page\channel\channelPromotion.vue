<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">
                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="渠道码">
                                <el-input
                                        v-model="dto.code"
                                        placeholder="渠道码"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="渠道名称">
                                <el-input
                                        v-model="dto.name"
                                        placeholder="渠道名称"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24" style="text-align:right">
                            <el-form-item>
                                <el-button type="primary" style="width:100px;" @click="query()">搜索</el-button>
                                <!--&lt;!&ndash;<el-button type="success" :loading="loadingExcel" @click="exportExcel">导出Excel&ndash;&gt;-->
                                <!--</el-button>-->
                                <el-button type="success" style="width:100px;" @click="insert=true">新增</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-table :data="list" border class="table"
                      v-loading="loading"
                      ref="multipleTable"
                      tooltip-effect="dark">
                <el-table-column
                        prop="name"
                        label="渠道名称">
                </el-table-column>
                <el-table-column
                        prop="code"
                        label="渠道码"
                ></el-table-column>
                <el-table-column
                        prop="type"
                        label="渠道类型"
                ></el-table-column>
                <el-table-column
                        prop="createrName"
                        label="创建者"
                ></el-table-column>
                <el-table-column
                        prop="createTime"
                        label="创建时间">
                </el-table-column>
                <el-table-column
                        width="300"
                        label="操作">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="goUrl(scope.row)" type="primary">生成</el-button>
                        <el-button size="mini" v-if="scope.row.img!=null" @click="show(scope.row.img)" type="primary">
                            预览
                        </el-button>
                        <el-button size="mini" v-if="scope.row.img!=null" @click="download(scope.row.img)"
                                   type="primary">下载
                        </el-button>
                        <!--<el-button size="mini" @click="edit(scope.row)" type="danger">删除</el-button>-->
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      :current="this.dto.pageNum"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="10"/>
            </div>
        </div>
        <Modal v-model="insert" class="Modal" :width="screenWidth" title="新增"
               :mask-closable="false"
               @on-cancel="getData()">
            <div class="addBody">
                <insert v-if="insert" @init-choose="initChooseProject"
                        @close-modal="closeCurrModal"></insert>
            </div>
            <div slot="footer">
            </div>
        </Modal>
        <Modal v-model="img" class="Modal" :width="screenWidth" title="查看"
               :mask-closable="false">
            <div class="addBody">
                <img-choose v-if="img" @init-choose="initChooseProject" :url="url"
                            @close-modal="closeCurrModal"></img-choose>
            </div>
            <div slot="footer">
            </div>
        </Modal>
    </div>
</template>
<script>
    import insert from '@/components/page/channel/choose/insert.vue'
    import imgChoose from '@/components/page/userTrack/imgChoose.vue'

    export default {
        data() {
            return {
                insert: false,
                id: null,
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                loadingExcel: false,
                loading: false,
                loadingShow: false,
                list: [],
                screenWidth: '35%',
                img: false,
                url: "",
                dto: {
                    code: "",
                    name: "",
                    pageSize: 10,
                    pageNum: 1,
                },
            };
        },
        components: {
            'insert': insert,
            "imgChoose": imgChoose
        },
        created() {
            this.getData();
        },
        computed: {},
        methods: {
            goUrl(row) {
                this.loadingShow=true;
                this.$postData("goUrl", {
                    "scene": row.code,
                    "page": "pages/index/index"
                }).then(res => {
                    this.loadingShow=false;
                   ;
                    if (res.code === 0) {
                        row.img = res.data;
                        this.update(row);
                    }
                });
            },
            update(dto) {
                dto.createTime=new Date(dto.createTime);
                this.$postData("channel_update", dto, {}).then(res => {
                    if (res.status === 200) {
                        this.getData()
                    }
                });
            },
            getData() {
                this.loading = true;
                this.$postData("channel_selectList", this.dto, {}).then(res => {
                    if (res.status === 200) {
                        this.loading = false;
                       ;
                        this.list = res.data.list;
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.loading = false;
                    }
                });
            },
            query() {
                this.dto.pageNum = 1;
                this.loading = true;
                this.getData();
            },
            show(url) {
                this.img = true;
                this.url = url
            },

            download(src) {
                let x = new XMLHttpRequest();
                x.open("GET", src, true);
                x.responseType = 'blob';
                x.onload = function (e) {
                    let url = window.URL.createObjectURL(x.response);
                    let a = document.createElement('a');
                    a.href = url;
                    a.download = "一搬小程序渠道二维码";
                    a.click();
                };
                x.send();
            },

            // 页码大小
            onPageSizeChange(size) {
                this.loading = true;
                this.dto.pageSize = size;
                this.getData();
            }
            ,
            // 跳转页码
            onChange(index) {
                console.log(index);
                this.loading = true;
                this.dto.pageNum = index;
                this.getData();
            }
            ,
            exportExcel() {
                this.loadingExcel = true;
                this.$postData("activityYi_exportList", this.dto, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.loadingExcel = false;
                    this.blobExport({
                        tablename: "一搬用户体验报告",
                        res: res
                    });
                });
            }
            ,
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            }
            ,
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.insert = false;
            }
            ,
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            }
            ,
        }
    }
    ;
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
