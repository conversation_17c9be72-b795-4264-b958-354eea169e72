<template>
    <div class="table">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item>
                    <i class="el-icon-lx-cascades"></i> 下单详情
                </el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="container">

            <div class="place-order-title">客户信息</div>
            <!--                {{form}}-->
            <el-form :rules="rules" :model="form" ref="ruleForm" style="padding-left: 20px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="客户名称" prop="name">
                            <el-col :span="17">
                                <el-input v-model="form.name" placeholder="请输入客户名称" clearable></el-input>
                            </el-col>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="客户号码" prop="phone">
                            <el-col :span="17">
                                <el-input v-model="form.phone" placeholder="请输入客户号码" clearable></el-input>
                            </el-col>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>

                    <el-col :span="8">
                        <el-form-item label="城市区域">
                            <city-choose @init-choose="initChooseProject" :model="form"
                                         :getcity="setCity"></city-choose>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="服务地址" prop="street">
                            <el-col :span="17">
                                <el-input placeholder="选择城市区域后可填" v-model="form.street" @blur="geoCode"
                                          :disabled=" form.cityId==null && form.areaId==null"></el-input>
                            </el-col>

                        </el-form-item>
                    </el-col>

                    <Col span="24" v-if="false">
                        <p style="padding: 5px;color: #999">
                            <Button type="primary" @click="geoCode">点击获取经纬度</Button> &nbsp; &nbsp;//注意：经纬度将在输入位置与城市区域后获取
                        </p>
                        <Col span="8">
                            <el-form-item prop="lng" label="经度数值">
                                <el-col :span="17">
                                    <el-input placeholder="请输入地址" v-model="form.lng" id="lng">
                                    </el-input>
                                </el-col>
                            </el-form-item>
                        </Col>
                        <Col span="8">
                            <el-form-item prop="lat" label="纬度数值">
                                <el-col :span="17">
                                    <el-input placeholder="请输入联系方式" v-model="form.lat" id="lat">
                                    </el-input>
                                </el-col>
                            </el-form-item>
                        </Col>
                    </Col>
                </el-row>
            </el-form>


            <el-divider></el-divider>
            <!--            <div class="place-order-title"-->
            <!--                 v-loading="form.name==null || form.lng==null || form.lat==null || form.phone==null"-->
            <!--                 element-loading-text="请先完成“客户信息”的填写">-->
            <div>
                <div class="place-order-title">服务信息</div>
                <el-form :rules="rules" ref="ruleForm2" style="padding-left: 20px">
                    <el-row>
                        <!--                        <el-col :span="8">-->
                        <!--                    <el-form-item label="服务门店" prop="required">-->
                        <!--                        <el-select v-model="form.region" placeholder="请选择活动区域">-->
                        <!--                            <el-option-->
                        <!--                                    v-for="item in storeList"-->
                        <!--                                    :key="item.id"-->
                        <!--                                    :label="item.storeName"-->
                        <!--                                    :value="item.id">-->
                        <!--                            </el-option>-->
                        <!--                        </el-select>-->
                        <!--                    </el-form-item>-->
                        <!--                        </el-col>-->
                        <el-col :span="8">
                            <el-form-item label="服务产品" prop="required">
                                <el-select v-model="productKey" placeholder="输入完整的客户信息后开启" @change="setProduct"
                                           :disabled="form.areaId==null">
                                    <!--                                <el-select v-model="form.productId" placeholder="输入完整的客户信息后开启" @change="getProductInfo(),getProductTime(),changeProduct()" :disabled="form.areaId==null" >-->
                                    <el-option

                                            v-for="item in productList"
                                            :key="'id='+item.id+'id;cid='+item.cid+'cid;price='+item.price+'price;'"
                                            :label="item.name+'('+item.price+')'"
                                            :value="'id='+item.id+'id;cid='+item.cid+'cid;price='+item.price+'price;'">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="服务时间" prop="startTime">
                                <el-date-picker type="date"
                                                @change="getProductTime"
                                                placeholder="选择日期"
                                                v-model="form.startTime"
                                                value-format="yyyy-MM-dd HH:mm:ss"
                                                :picker-options="pickerOptions"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="16">
                            <el-form-item label="时间区间" prop="required" v-if="showServiceTime">
                                <el-radio-group v-model="form.timeRadio" v-for="(item,index) in productTime"
                                                @change="changeTime" :key="index">
                                    <el-radio :label="item.startTime+'-'+item.endTime" border style="margin-right: 10px"
                                              :disabled="item.isno"></el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="SKU类型" prop="required">

                                <el-radio-group v-model="form.skuRadio" v-for="(item,skuIndex) in productInfo.skulist"
                                                :key="skuIndex">
                                    <el-radio :label="skuIndex" v-for="(sku,index) in item.skuPropertylist"
                                              @change="changeSku(index)" :key="index">{{sku.propertyName}}
                                    </el-radio>
                                    <br>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="销售属性">
                                <div v-for="(sku,index) in productInfo.skulist[skuIndex].units"><br>
                                    {{sku.name}}&nbsp;&nbsp;
                                    <!--                                    <el-input-number v-model="form.skuDes[skuIndex].units[index].number"  :min="sku.minValue" :max="sku.maxValue" :label="sku.name" :step="sku.step"  @change="getPrice()"/>-->
                                    <el-input-number v-model="form.skuDes[skuIndex].units[index].number"
                                                     :max="sku.maxValue" :label="sku.name" :step="sku.step"
                                                     @change="getPrice()"/>
                                    &nbsp;&nbsp; {{sku.unitName}}
                                </div>


                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!--                    <el-row>-->
                    <!--                        <el-col :span="24"  >-->
                    <!--                            <el-form-item label="销售属性" prop="required" >-->
                    <!--                                <div v-for="item in productInfo.skulist">-->
                    <!--                                    <div v-for="sku in item.units">-->
                    <!--                                        <br>-->
                    <!--                                        {{sku.name}}&nbsp&nbsp<el-input-number v-model="form.date1"  :min="sku.minValue" :max="sku.maxValue" :label="sku.name" :step="sku.step"  />-->
                    <!--                                        &nbsp&nbsp  {{sku.unitName}}-->
                    <!--                                    </div>-->

                    <!--                                </div>-->

                    <!--                            </el-form-item>-->
                    <!--                        </el-col>-->
                    <!--                    </el-row>-->
                    <el-row>
                        <el-row v-show="form.skuDes.length>0">
                            <table>
                                <tr>
                                    <td>类型</td>
                                    <span v-for="item in form.skuDes">
                                        <span v-for="s in item.skuPropertylist">
                                            <td>{{s.propertyName}}</td>
                                        </span>
                                    </span>
                                </tr>
                                <tr>
                                    <td>数量</td>
                                    <span v-for="item in form.skuDes">
                                        <span v-for="s in item.units">
                                            <td>{{s.number}}</td>
                                        </span>
                                    </span>
                                </tr>
                            </table>
                        </el-row>
                        <el-form-item label="服务备注">
                            <el-input type="textarea" v-model="form.remarks"></el-input>
                        </el-form-item>

                    </el-row>
                </el-form>
            </div>


        </div>
        <br>
        <div class="container">
            <el-row>
                <el-col :span="24">
                    <div style="color:#ff0015;"><h3>{{errorMsg}}</h3></div>
                </el-col>
                <el-col :span="8">
                    <div style="color:#ff0015;"><h2>{{msg}}</h2></div>
                </el-col>
                <el-col :span="16">
                    <div>
                        <el-button type="primary" @click="submitForm('ruleForm')" size="medium"
                                   :loading="submitLoading" :disabled="disabled">立即开单
                        </el-button>
                        <!--                <el-button type="primary" @click="getPrice()">获取价格</el-button>-->
                        <el-button type="primary" v-show="couponNum!=null">优惠劵剩余：{{couponNum}}</el-button>

                    </div>
                </el-col>
            </el-row>

        </div>
    </div>
</template>

<script>
    import cityChoose from '@/components/page/agent/minichoose/cityChoose.vue'

    export default {
        name: "placeOrder",
        data() {
            return {
                disabled: true,
                showServiceTime: false,
                priceNum: 0,
                productKey: null,
                productDom: null,
                submitLoading: false,
                msg: '￥0',
                errorMsg: null,
                skuIndex: 0,
                storeList: [],
                productList: [],
                units: [],
                skulist: [],
                pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7
                        // Date.now()
                    },
                },
                productInfo: {
                    skulist: [
                        {
                            skuPropertylist: [],
                            units: []
                        }
                    ]
                },
                couponNum: null,
                productTime: [],
                form: {
                    cid: null,
                    price: null,
                    cityId: 1,
                    areaId: 2,
                    arearId: 2,
                    productId: null,
                    memberId: 0,
                    startTime: null,
                    startTimeSpan: null,
                    endTimeSpan: null,
                    skuDes: [],
                    surcharges: [],
                    addrs: [],
                    serviceNos: [],
                    source: 'PC',
                    name: null,
                    phone: null,
                    kfr: localStorage.getItem("id"),
                    tg: localStorage.getItem("account"),
                    lat: null,
                    lng: null,
                    street: null,
                    areaName: null,
                    cityName: null,
                    kd: localStorage.getItem("id"),
                    couponId: null,
                    remarks: null,
                    timeRadio: null,
                    skuRadio: null,
                    isNet:0,

                },
                setCity: {
                    cityId: 1,
                    areaId: 2,
                },
                rules: {
                    name: [
                        {required: true, message: '请输入客户名称', trigger: 'blur'}
                    ],
                    phone: [
                        {required: true, message: '请输入客户手机号', trigger: 'blur'},
                        {min: 11, max: 11, message: '请输入11位手机号', trigger: 'blur'}
                    ],
                    street: [
                        {required: true, message: '请输入完整街道信息', trigger: 'blur'}
                    ],
                    lng: [
                        {required: true, message: '选择城市区域，再选择街道后点击获取经纬度', trigger: 'blur'}
                    ],
                    lat: [
                        {required: true, message: '选择城市区域，再选择街道后点击获取经纬度', trigger: 'blur'}
                    ],
                    required: [
                        {required: true, message: '请输入完整信息', trigger: 'blur'}
                    ],

                }
            }
        },
        components: {
            'cityChoose': cityChoose,
        },
        created() {
            var day1 = new Date();
            day1.setDate(day1.getDate() + 1);
            // var s1 = this.format(day1, "yyyy-MM-dd hh:mm:ss");
            this.form.startTime = this.format(day1, "yyyy-MM-dd hh:mm:ss");
            this.getStore();
            this.getProduct();
        },
        methods: {
            setProduct(id) {
                this.couponNum = null;
                this.form.couponId = null;
                let pid = id.substring(id.indexOf("id=") + 3, id.indexOf("id;") + 0);

                this.form.productId = pid;
                let cid = id.substring(id.indexOf("cid=") + 4, id.indexOf("cid;") + 0);

                let price = id.substring(id.indexOf("price=") + 6, id.indexOf("price;") + 0);

                this.form.cid = cid;
                // console.info('cidd', (this.form.cid));
                this.form.price = price;

                this.getProductInfo();
                this.getProductTime();
                this.changeProduct();
            },
            format(DateTime, fmt) {
                var o = {
                    "M+": DateTime.getMonth() + 1, //月份
                    "d+": DateTime.getDate(), //日
                    "h+": DateTime.getHours(), //小时
                    "m+": DateTime.getMinutes(), //分
                    "s+": DateTime.getSeconds(), //秒
                    "q+": Math.floor((DateTime.getMonth() + 3) / 3), //季度
                    "S": DateTime.getMilliseconds() //毫秒
                };
                if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (DateTime.getFullYear() + "").substr(4 - RegExp.$1.length));
                for (var k in o)
                    if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                return fmt;
            },
            changeProduct() {
                // this.productList.forEach(v=>{
                //     if (v.id==this.form.productId){
                //         this.form.cid=v.cid
                //         this.form.price=v.price
                //     }
                // })
                this.getC();
            },
            changeTime(value) {
                this.form.startTimeSpan = value.substring(0, 5);
                this.form.endTimeSpan = value.substring(6, 11);
            },
            changeSku(index) {
                this.skuIndex = this.form.skuRadio;
                this.units = this.productInfo.skulist[this.form.skuRadio].units;

            },
            getC() {
                // http://inside.xiaoyujia.com/api/Order/getKey?cgId=1
                let self = this;
                $.ajax({
                    type: "post",//type可以为post也可以为get
                    url: "https://inside.xiaoyujia.com/api/Order/getKey?cgId=" + self.form.cid,
                    contentType: "application/json",
                    data: JSON.stringify({}),//这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                    dataType: "json",//这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                    success: function (data) {
                        if (data.Meta.State === 200) {
                            self.couponNum = data.Meta.Msg.split(",")[1];
                            self.form.couponId = data.Meta.Msg.split(",")[0];
                            self.disabled = false;
                            if(self.couponNum == null || self.couponNum === 0) {
                                self.disabled = true;
                                self.$message("该产品没有优惠券了");
                            }
                        } else {
                            self.disabled = true;
                            self.$message(data.Meta.Msg);
                        }

                    },
                    error: function (data) {
                        //alert("出现了错误！");
                    }
                })
            },
            getProduct() {
                let self = this;
                $.ajax({
                    type: "post",//type可以为post也可以为get
                    url: "https://yibanapi.xiaoyujia.com/product/getProductByNoId?id=" + parseInt(localStorage.getItem("id")),
                    contentType: "application/json",
                    data: JSON.stringify({
                        id: parseInt(localStorage.getItem("id")),
                    }),//这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                    dataType: "json",//这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                    success: function (data) {
                        if (data.meta.state == 200) {
                            self.productList = data.data;
                        } else {
                            self.$message(data.meta.msg);
                        }

                    },
                    error: function (data) {
                        //alert("出现了错误！");
                    }
                })
            },
            getPrice() {
                let self = this;
                if (this.form.startTimeSpan == null) {
                    return;
                }
                $.ajax({
                    type: "post",//type可以为post也可以为get
                    url: "https://yibanapi.xiaoyujia.com/product/price",
                    contentType: "application/json",
                    data: JSON.stringify(self.form),//这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                    dataType: "json",//这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                    success: function (data) {
                        if (data.meta.state == 200) {
                            let price = 0;
                            data.data.forEach(v => {
                                price += v.price;
                            });
                            self.msg = "￥" + price;
                            if (self.form.price > 0) {
                                self.msg = "￥" + price + "-(优惠劵)￥" + self.form.price + "=￥" + (price - self.form.price);
                                self.priceNum = price - self.form.price;
                            }
                            if (self.couponNum == 0 || self.couponNum == null) {
                                self.msg = "￥" + price;
                            }

                        } else {
                            self.getPrice();
                            self.msg = data.meta.msg;
                        }

                    },
                    error: function (data) {
                        //alert("出现了错误！");
                    }
                })
            },
            getProductInfo() {
                let self = this;
                $.ajax({
                    type: "post",//type可以为post也可以为get
                    url: "https://yibanapi.xiaoyujia.com/product/placeOrder",
                    contentType: "application/json",
                    data: JSON.stringify({
                        areaId: self.form.areaId,
                        cityId: self.form.cityId,
                        memberId: 0,
                        productId: self.form.productId,
                        // productId: self.form.productId,
                    }),//这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                    dataType: "json",//这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                    success: function (data) {
                        if (data.meta.state == 200) {
                            self.productInfo = data.data;
                            self.form.skuDes = [];
                            self.productInfo.skulist.forEach((v, index) => {
                                let sku = {
                                    skuPropertylist: [],
                                    units: []
                                };
                                sku.skuPropertylist = v.skuPropertylist;
                                sku.units = [];
                                v.units.forEach(vs => {
                                    let unit = {
                                        id: null,
                                        name: null,
                                        number: null,
                                    };
                                    unit.id = vs.id;
                                    unit.name = vs.name;
                                    unit.number = 0;
                                    if (index == 0) {
                                        unit.number = 1;
                                    }
                                    sku.units.push(unit);
                                });
                                self.form.skuDes.push(sku);
                            });
                            self.form.skuRadio = 0;
                            self.getPrice();

                        }


                    },
                    error: function (data) {
                        //alert("出现了错误！");
                    }
                })
            },
            getProductTime() {
                let dom = {
                    city: this.form.cityName,
                    area: this.form.areaName,
                    productId: this.form.productId,
                    time: this.form.startTime
                };
                let self = this;
                $.ajax({
                    type: "post",//type可以为post也可以为get
                    url: "https://yibanapi.xiaoyujia.com/water/notOrderTime",
                    contentType: "application/json",
                    data: JSON.stringify(dom),//这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                    dataType: "json",//这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                    success: function (data) {
                        if (data.code === 200) {
                            // console.info('data', data.msg);
                            if (data.msg !== "success") {
                                self.showServiceTime = false;
                                self.$message.error(data.msg);
                            } else {
                                self.showServiceTime = true;
                                let timeListModels = data.data.timeListModels;
                                // console.log(timeListModels);
                                self.productTime = timeListModels[0].timeItemModels;
                                self.form.timeRadio = self.productTime[0].startTime + '-' + self.productTime[0].endTime;
                                // console.log(self.form.timeRadio);
                                self.changeTime(self.productTime[0].startTime + '-' + self.productTime[0].endTime);
                            }

                        }
                    },
                    error: function (data) {
                        //alert("出现了错误！");
                    }
                })
                // $.ajax({
                //     type: "get",//type可以为post也可以为get
                //     url: "https://yibanapi.xiaoyujia.com/product/getSkuFiexTime?pid="+self.form.productId,
                //     contentType:"application/json",
                //     data: {},//这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                //     dataType: "json",//这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                //     success: function (data) {
                //         if (data.meta.state==200){
                //             self.productTime=data.data
                //             self.form.timeRadio=self.productTime[0].startTime+'-'+self.productTime[0].endTime
                //             self.changeTime(self.productTime[0].startTime+'-'+self.productTime[0].endTime)
                //
                //
                //         }
                //     },
                //     error: function (data) {
                //         //alert("出现了错误！");
                //     }
                // })
            },

            setCAndAdd() {
                // http://inside.xiaoyujia.com/api/Order/getKey?cgId=1
                let self = this;
                $.ajax({
                    type: "post",//type可以为post也可以为get
                    url: "https://inside.xiaoyujia.com/api/Order/getKey?cgId=" + self.form.cid,
                    contentType: "application/json",
                    data: JSON.stringify({}),//这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                    dataType: "json",//这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                    success: function (data) {
                        if (data.Meta.State === 200) {
                            self.couponNum = data.Meta.Msg.split(",")[1];
                            self.form.couponId = data.Meta.Msg.split(",")[0];
                            self.addOrderByC();
                        } else {
                            self.$message(data.Meta.Msg);
                            this.submitLoading = false;
                        }
                    },
                    error: function (data) {
                        //alert("出现了错误！");
                        console.info('e', data);
                    }
                })
            },
            addOrderByC() {
                let self = this;
                // http://localhost:9888
                // https://yibanapi.xiaoyujia.com
                $.ajax({
                    type: "post",//type可以为post也可以为get
                    url: "https://yibanapi.xiaoyujia.com/product/add",
                    contentType: "application/json",
                    data: JSON.stringify(self.form),//这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                    dataType: "json",//这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                    success: function (data) {
                        if (data.meta.state == 200) {
                            self.errorMsg = null;
                            self.$message('下单成功');
                            self.submitLoading = false;
                            location.reload()
                        } else {
                            self.submitLoading = false;
                            self.errorMsg = "下单错误：" + data.meta.msg;
                            self.$message(data.meta.msg);
                        }

                    },
                    error: function (data) {
                        self.submitLoading = false;
                        //alert("出现了错误！");
                    }
                })
            },
            submitForm(formName) {
                this.submitLoading = true;
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        this.setCAndAdd();
                    } else {
                        this.submitLoading = false;
                        return false;
                    }
                });
            },
            getStore() {
                this.$postData("store_getByList", {}, {}).then(res => {
                    if (res.status === 200) {
                        this.storeList = res.data;
                    }
                });
            },
            initChooseProject(city) {
                this.form.cityId = city.cityId;
                this.form.areaId = city.areaId;
                this.form.arearId = city.areaId;
                this.form.cityName = city.cityName;
                this.form.areaName = city.areaName;
                if (this.form.cityName == null) {
                    this.form.cityName = '厦门市';
                    this.form.areaName = '湖里区';
                }

            },
            geoCode() {
                var geocoder = new AMap.Geocoder({
                    city: "", //城市设为北京，默认：“全国”
                });
                let city = this.form.cityName + this.form.areaName;
                var address = city == null ? '厦门市湖里区' : city + this.form.street;
                geocoder.getLocation(address, (status, result) => {
                    if (status === 'complete' && result.geocodes.length) {
                        this.form.lng = result.geocodes[0].location.lng;
                        this.form.lat = result.geocodes[0].location.lat;
                        // document.getElementById('lat').value = result.geocodes[0].location.lat;
                        // document.getElementById('lng').value = result.geocodes[0].location.lng;

                    } else {
                        this.$message.error("获取经纬度失败");
                    }
                });
            },
            // getProductTime(){
            //     let self=this
            //     $.ajax({
            //         type: "post",//type可以为post也可以为get
            //         url: "https://yibanapi.xiaoyujia.com/product/placeOrderTime",
            //         contentType:"application/json",
            //         data: JSON.stringify({
            //             areaId: self.dom.areaId,
            //             cityId: self.dom.cityId,
            //             memberId: 0,
            //             productId: self.form.productId,
            //         }),//这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
            //         dataType: "json",//这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
            //         success: function (data) {
            //             if (data.meta.state==200){
            //                 self.productTime=data.data
            //             }
            //             console.log(data)
            //         },
            //         error: function (data) {
            //             //alert("出现了错误！");
            //         }
            //     })
            // },
        }

    }
</script>

<style scoped>
    td {
        text-align: center;
        padding: 10px;
        width: 150px;
    }

    table, th, td {
        border-collapse: collapse;
        border: 1px solid #b9b6b6;
    }

    .place-order-title {
        padding-left: 20px;
        border-left: 5px solid #409eff;
        font-size: 15px;
        margin-bottom: 12px;
    }
</style>
