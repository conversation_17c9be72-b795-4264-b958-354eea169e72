               <template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv" v-if="!show">
            <div id="searchDiv">
                <el-steps :active="active" align-center direction="vertical">
                    <el-step title="申诉登记" @click.native="setNum(0)" icon="el-icon-edit"></el-step>
                    <el-step title="申诉结果" @click.native="setNum(1)" icon="el-icon-upload"></el-step>
                </el-steps>
            </div>
            <div class="fromInput">
                <Form v-if="active===0" ref="dto" :model="dto"
                      :rules="ruleValidate" :label-width="80" label-position="left">
                    <FormItem label="申诉理由" prop="appealReason">
                        <Input v-model="dto.appealReason" type="textarea" clearable
                               style="width: 250px"
                               :autosize="{minRows: 4,maxRows: 4}"
                               placeholder="申诉理由">
                        </Input>
                    </FormItem>
                    <FormItem label="证据上传">
                        <Upload
                                multiple
                                :action="imgUrl"
                                :on-success="onSuccess"
                                :on-preview="handlePictureCardPreview"
                                :on-remove="handleRemove"
                                :before-upload="before"
                                :on-exceed="onExcees"
                                :default-file-list="filesList"
                                :max-size="10240"
                                :data="files">
                            <Button icon="ios-cloud-upload-outline">点击上传</Button>
                        </Upload>
                    </FormItem>

                    <FormItem label="品控核实" prop="appealVerify">
                        <Input v-model="dto.appealVerify" clearable
                               type="textarea"
                               style="width: 250px"
                               :autosize="{minRows: 4,maxRows: 4}"
                               placeholder="品控核实">
                        </Input>
                    </FormItem>

                    <FormItem label="是否申诉成功">
                        <el-radio v-model="dto.effective" :label="false">否</el-radio>
                        <el-radio v-model="dto.effective" :label="true">是</el-radio>
                    </FormItem>
                    <FormItem label="是否撤销投诉">
                        <el-radio v-model="dto.revoke" :label="false">否</el-radio>
                        <el-radio v-model="dto.revoke" :label="true">是</el-radio>
                    </FormItem>
                    <FormItem>
                        <div v-if="active===0">
                            <Button v-if="dto.effective==null||dto.effective==''" style="margin-left:3rem"
                                    type="success"
                                    @click="save('dto')"> 保存
                            </Button>
                            <Button v-else type="primary" @click="save('dto')">下一步</Button>
                            <Button style="margin-left: 3rem" @click="chooseThisModel('dto')">取消</Button>
                            <Button style="margin-left: 3rem" type="primary" @click="getByTsId()">
                                申诉日志({{dto.appealSize}})
                            </Button>
                        </div>
                    </FormItem>
                </Form>

                <div v-if="active===1">
                    <div v-if="dto.effective===true" style="width: 280px">
                        <div style="width: 120px;margin: 0 auto">
                            <img style="width: 120px;height: 120px" src="../../../../assets/img/check-circle.png"/>
                        </div>
                        <div style="text-align: center;margin-top: 1rem">申诉成功</div>
                    </div>
                    <div v-if="dto.effective===false" style="width: 280px">
                        <div style="width: 120px;margin: 0 auto">
                            <img style="width: 120px;height: 120px" src="../../../../assets/img/close-circle.png"/>
                        </div>
                        <div style="text-align: center;margin-top: 1rem">申诉失败,尽可能的收集更多证据，谢谢</div>
                    </div>
                </div>
            </div>
        </div>
        <div v-else style="height: 500px;font-size: 13px">
            <Button style="margin-left: 2rem;margin-bottom: 1rem" type="primary" @click="isShow()">返回</Button>
            <Collapse v-model="value2" accordion @on-change="change">
                <Panel :name="''+index" v-for="(item,index) in appealList" :key="index">
                    {{item.appealReason}} {{item.createTime}}
                    <p slot="content">投诉文件:
                        <a @click="goFile(item)">
                            {{item.fileName}}
                        </a>
                    </p>
                    <br/>
                    <p slot="content"> 品控核实:
                        {{item.appealVerify}}
                    </p>
                </Panel>
            </Collapse>
        </div>
    </div>
</template>
<script>
    export default {
        props: ['stage'],
        data() {
            return {
                value2: '1',
                appealList: [],
                show: false,
                label: null,
                active: this.stage.appealStage,
                imgUrl: "https://biapi.xiaoyujia.com/files/uploadFiles",
                testUrl: "http://************:6052/files/uploadFiles",
                filesList: [],
                finedList: [],
                dto: {
                    id: this.stage.id,
                    appealReason: null,
                    appealFile: null,
                    appealVerify: null,
                    effective: null,
                    revoke: "false",
                    appealStage: this.stage.appealStage
                },
                ruleValidate: {
                    appealReason: [
                        {required: true, message: '请填写申诉理由', trigger: 'change'}
                    ],
                    appealVerify: [
                        {required: true, message: '请填写品控核实', trigger: 'change'}
                    ],
                    effective: [
                        {required: true, message: '请选择投诉是否有效', trigger: 'change'}
                    ]
                },
                files: {
                    fileName: "Ts/"
                }
            }
        },
        components: {},
        created: function () {
            this.getById();
            this.getFine();
        },
        methods: {
            change(key) {
                console.log(key);
                this.value2 = key[0] + ""
            },
            isShow() {
                this.show = !this.show
            },
            getByTsId() {
                this.show = !this.show;
                this.$getData("appeal_getByTsId", {tsId: this.dto.id}).then(res => {
                    if (res.status === 200) {
                        let data = res.data;
                        console.log(data);
                        for (let i = 0; i < data.length; i++) {
                            let url = decodeURIComponent(data[i].appealFile);
                            let fileName = url.substring(url.lastIndexOf("/") + 1, url.length);
                            console.log(fileName);
                            data[i].fileName = fileName;
                            if (i === data.length - 1) {
                                console.log(data);
                                this.appealList = data
                            }
                        }
                    }
                })
            },
            setNum(num) {
                this.active = num;
            },
            getById() {
                this.$getData("tsGetById", {id: this.dto.id}).then(res => {
                    if (res.status === 200) {
                        this.dto = res.data;
                        this.getFiles(res.data);
                    }
                })
            },
            getFine() {
                this.$postUrl("get_dict", 117, null, {}).then(res => {
                    if (res.status === 200) {
                        this.finedList = res.data;
                    }
                })
            },
            getFiles(data) {
                if (data.appealFile) {
                    let split = data.appealFile.split(",");
                    for (let i = 0; i < split.length; i++) {
                        let res = decodeURIComponent(split[i]);
                        let file = {
                            name: res.substring(res.lastIndexOf("/") + 1, res.length),
                            url: res
                        };
                        this.filesList.push(file);
                    }
                }
            },
            save(name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        if (this.active === 0) {
                            if (this[name].effective == null || this[name].effective === '') {
                                this[name].state = 4;
                                this[name].appealStage = 0;
                            } else if (this.dto.effective === true) {
                                this.active++;
                                this[name].appealStage = 1;
                                this[name].state = 5;
                                let text = "工号:" + this.dto.employeeCaptain.no + "\n订单" + this.dto.billNo + "的申诉已通过,处罚将进行回退。";
                                if (!this.dto.fallback) {
                                    this.insetFund();
                                    this.goPush(text);
                                }
                            } else if (this.dto.effective === false) {
                                this.active++;
                                this[name].appealStage = 1;
                                this[name].state = 6;
                                let text = "工号:" + this.dto.employeeCaptain.no + "\n订单" + this.dto.billNo + "的申诉因证据不足，审核不通过,您可以收集更多的证据，再次申诉。";
                                this.goPush(text);
                            }
                            this.update(this[name]);
                            this.updateAppeal();
                        }
                    }
                });
            },
            updateAppeal() {
                let appeal = this.dto.orderTsAppeal;
                appeal.appealFile = this.dto.appealFile;
                appeal.appealVerify = this.dto.appealVerify;
                this.$postData("tsAppeal_update", appeal, {}).then(res => {
                    if (res.status === 200) {
                        // this.$Message.success('保存成功');
                    } else {
                        //this.$message.error("保存失败，" + res.msg);
                    }
                });
            },
            update(dto) {
                dto.lastTime = new Date(dto.lastTime);
                if (dto.trainTime != null) {
                    dto.trainTime = new Date(dto.trainTime);
                }
                this.$postData("tsUpdate", dto, {}).then(res => {
                    if (res.status === 200) {
                        // this.$Message.success('保存成功');
                    } else {
                        this.$message.error("保存失败，" + res.msg);
                    }
                });
            },
            goPush(text) {
                let query = {
                    text: text,
                    agentid: "0",
                    no: this.dto.employeeCaptain.no
                };
                this.$postData("ts_push", query).then(res => {
                    if (res.code === 0) {
                    }
                    if (res.code === 500) {
                        alert(res.data.msg)
                    }
                })
            },
            setChangeAmount(id) {
                for (let i = 0; i < this.finedList.length; i++) {
                    if (id === this.finedList[i].id) {
                        return this.finedList[i].text.replace(/[^0-9]/ig, "")
                    }
                }
            },
            insetFund() {
                if (this.dto.violation === 1) {
                    let list = this.dto.tsEmployees;
                    for (let i = 0; i < list.length; i++) {
                        if (list[i].scoreId == null) {
                            continue
                        }
                        let fundDetails = {
                            employeeNo: list[i].employeeNo,
                            employeeFundDetailsType: 14,
                            changeAmount: -list[i].fund.changeAmount,
                            billno: this.dto.billNo,
                            productName: this.dto.order.productName
                        };
                        this.saveFund(fundDetails)
                    }
                }
                if (this.dto.violation === 3) {
                    let fundDetails = {
                        employeeNo: this.dto.offenceNos,
                        employeeFundDetailsType: 14,
                        changeAmount: -this.dto.offenceFine,
                        billno: this.dto.billNo,
                        productName: this.dto.order.productName
                    };
                    this.saveFund(fundDetails)
                }
            },
            saveFund(fundDetails) {
                console.log(fundDetails);
                this.$postData("fundDetails_insert", fundDetails).then(res => {
                    if (res.status === 200) {
                        this.dto.fallback = true;
                        this.update(this.dto)
                    }
                })
            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(dto) {
                this.$emit('init-choose', dto);
                //this.update(this[dto])
            },
            handleRemove(file) {
                console.log(file);
                let names = this.dto.appealFile.split(",");
                for (let i = 0; i < names.length; i++) {
                    if (file.url === decodeURIComponent(names[i])) {
                        names.splice(i, 1);
                        this.dto.appealFile = names.join(",");
                        break;
                    }
                }
            },
            handlePictureCardPreview(file) {
                console.log(file);
                this.download(file.url, file.name);
            },
            goFile(item) {
                this.download(item.appealFile, item.fileName);
            },
            onExcees() {
                this.$Message.success('只可上传一个文件');
            },
            download(src, fileName) {
                let x = new XMLHttpRequest();
                x.open("GET", src, true);
                x.responseType = 'blob';
                x.onload = function (e) {
                    let url = window.URL.createObjectURL(x.response);
                    let a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;
                    a.click();
                };
                x.send();
            },

            before() {
                if (this.dto.appealFile) {
                    let list = this.dto.appealFile.split(",");
                    const check = list.length < 2;
                    if (!check) {
                        this.$Notice.warning({
                            title: '最多可上传2个附件,多文件请打包上传'
                        });
                    }
                    return check;
                }
            },
            onSuccess(res) {
                if (res.status === 200) {
                    this.$Message.success('上传成功');
                    if (this.dto.appealFile) {
                        this.dto.appealFile = this.dto.appealFile + "," + res.data;
                    } else {
                        this.dto.appealFile = res.data;
                    }
                }
            }
        }
    }
</script>


<style scoped>

    /* 查询div*/
    #searchDiv {
        width: 160px;
        float: left;
        height: 500px;
        margin-left: 1rem;
    }

    .fromInput {
        float: left;
        margin-left: 2rem;
    }

    #bodyDiv {
        font-size: 15px !important;
        background-color: #fff;
        height: 500px;
    }
</style>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             
