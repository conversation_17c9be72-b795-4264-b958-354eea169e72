<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col span="24">
                    <div>
                        <div id="operBtnDiv">
                            <div id="searchDiv">
                                <Form ref="formItem" :model="formItem" :label-width="75" label-position="right"
                                      :rules="ruleValidate">
                                    <FormItem label="商品" prop="tryProductId">
                                        <Select v-model="formItem.tryProductId" filterable>
                                            <Option v-for="item in tryProducts" :value="item.id"
                                                    :key="item.id">{{ item.firstTitle }}
                                            </Option>
                                        </Select>
                                    </FormItem>
                                    <Row>
                                        <Col span="12">
                                            <FormItem label="描述1" prop="explain1">
                                                <Input placeholder="请输入描述" v-model="formItem.explain1">
                                                </Input>
                                            </FormItem>
                                        </Col>
                                        <Col span="11">

                                            <FormItem label="描述2" prop="explain2">
                                                <Input placeholder="请输入描述" v-model="formItem.explain2">
                                                </Input>
                                            </FormItem>
                                        </Col>
                                    </Row>

                                    <Row>
                                        <Col span="12">
                                            <FormItem label="描述3" prop="explain3">
                                                <Input placeholder="请输入描述" v-model="formItem.explain3">
                                                </Input>
                                            </FormItem>
                                        </Col>
                                        <Col span="12">
                                            <FormItem label="跳转链接" prop="bannerUrl">
                                                <Input placeholder="请输入跳转链接" v-model="formItem.bannerUrl">
                                                </Input>
                                            </FormItem>
                                        </Col>
                                    </Row>

                                    <FormItem label="结束时间">
                                                    <el-date-picker
                                                            v-model="formItem.endTime"
                                                            type="datetime"
                                                            placeholder="选择日期时间">
                                                    </el-date-picker>

                                    </FormItem>
                                    <FormItem label="详情图" prop="banner">
                                        <el-upload
                                                :action="imgUrl"
                                                list-type="picture-card"
                                                :limit="1"
                                                :on-success="onSuccess"
                                                :on-preview="handlePictureCardPreview"
                                                :on-remove="handleRemove"
                                                :on-exceed="onExcees"
                                                :file-list="fileList"
                                                :data="files">
                                            <i class="el-icon-plus"></i>
                                        </el-upload>
                                        <el-dialog :visible.sync="dialogVisible">
                                            <img width="100%" :src="dialogImageUrl" alt="">
                                        </el-dialog>
                                    </FormItem>


                                    <FormItem label="详情图" prop="imgs">
                                        <el-upload
                                                :action="imgUrl"
                                                list-type="picture-card"
                                                :limit="1"
                                                :on-success="onSuccess1"
                                                :on-preview="handlePictureCardPreview"
                                                :on-remove="handleRemove1"
                                                :file-list="fileList1"
                                                :on-exceed="onExcees"
                                                :data="files">
                                            <i class="el-icon-plus"></i>
                                        </el-upload>
                                        <el-dialog :visible.sync="dialogVisible">
                                            <img width="100%" :src="dialogImageUrl" alt="">
                                        </el-dialog>
                                    </FormItem>


                                    <div style="margin-left: 80px">
                                        <Button type="primary" @click="update('formItem')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </div>
                                </Form>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
    </div>
</template>


<script>

    export default {
        props:['detailId'],
        data() {
            return {
                imgUrl:"https://biapi.xiaoyujia.com/files/uploadFiles",
                testUrl:"http://localhost:8069/files/uploadFiles",
                usersModal: false,
                userLoading: false,
                screenWidth: '30%',
                tryProducts: null,
                formItem: {
                    id:this.detailId,
                    tryProductId: null,
                    explain1: null,
                    explain2: null,
                    explain3: null,
                    bannerUrl: null,
                    banner: null,
                    imgs: null,
                    endTime: null,
                    endTime1: null,
                },
                files: {
                    fileName: "TryProduct/Details/"
                },
                fileList:[{
                    name: '',
                    url:''
                }],
                fileList1:[{
                    name: '',
                    url:''
                }],
                dialogImageUrl: '',
                dialogVisible: false,
                ruleValidate: {
                    explain1: [
                        {required: true, message: '请输入描述1', trigger: 'change'}
                    ],
                    tryProductId: [
                        {required: true, message: '请选择商品', trigger: 'change', type: "number"}
                    ],
                    explain2: [
                        {required: true, message: '请输入描述2', trigger: 'change'}
                    ],
                    explain3: [
                        {required: true, message: '请输入描述3', trigger: 'change'}
                    ],
                    bannerUrl: [
                        {required: true, message: '请输入跳转链接', trigger: 'change'}
                    ],
                    banner: [
                        {required: true, message: '请选择图片', trigger: 'blur'}
                    ],
                    imgs: [
                        {required: true, message: '请选择图片', trigger: 'blur'}
                    ],
                    endTime: [
                        {required: true, message: '请选择日期', trigger: 'change'}
                    ],
                },
            }
        },
        components: {},
        created: function () {
            this.getTryProduct();
            this.getData();
        },
        methods: {
            getTryProduct() {
                let product = {}
                this.$postData("product_getByList", product, {}).then(res => {
                    if (res.status == 200) {
                        this.tryProducts = res.data.list;
                    } else {
                        this.$message.error("获取失败，" + res.msg);
                    }
                })
            },
            getData(){
                let detail={
                    id:this.detailId
                }
                this.$postData("detail_getById", detail, {}).then(res => {
                    if (res.status == 200) {
                        this.formItem = res.data;
                        this.fileList[0].url=res.data.banner
                        this.fileList1[0].url=res.data.imgs
                    } else {
                        this.$message.error("获取失败，" + res.msg);
                    }
                })
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.usersModal = false;
            },
            update(name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        this.$postData("detail_update", this.formItem, {}).then(res => {
                            if (res.status == 200) {
                                this.$Message.success('修改成功');
                                this.chooseThisModel();
                            } else {
                                this.$message.error("修改失败，" + res.msg);
                            }
                        })
                    }
                })
            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            },
            handleRemove(file, fileList) {
                console.log(file, fileList);
                this.formItem.banner = null
            },
            handleRemove1(file, fileList) {
                console.log(file, fileList);
                this.formItem.imgs = null
            },
            handlePictureCardPreview(file) {
                this.dialogImageUrl = file.url;
                this.dialogVisible = true;
                console.log(file);
            },
            onExcees() {
                this.$Message.success('只可上传一张图片');
            },
            onSuccess(res, file, fileList) {
               ;
                if (res.status == 200) {
                    this.$Message.success('上传成功');
                    this.formItem.banner = res.data
                }
            },
            onSuccess1(res, file, fileList) {
               ;
                if (res.status == 200) {
                    this.$Message.success('上传成功');
                    this.formItem.imgs = res.data
                }
            }
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

