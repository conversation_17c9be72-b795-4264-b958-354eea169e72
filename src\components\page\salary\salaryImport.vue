<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col :span="8">
                    <el-upload
                            class="upload-demo"
                            ref="upload"
                            action="https://biapi.xiaoyujia.com/salaryDetails/batchInsert"
                            :on-success="handlePreview"
                            :data="data"
                            :on-error="handleError"
                            :on-preview="handlePictureCardPreview"
                            :on-change="onChange"
                            :on-remove="handleRemove"
                            :before-upload="before"
                            :on-exceed="onExcees"
                            :file-list="fileList"
                            :limit="1"
                            list-type="xlsx"
                            :auto-upload="false">
                        <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
                        <el-button style="margin-left: 20px;"
                                   size="small" type="success" :loading="loading" @click="submitUpload">
                            导入
                        </el-button>
                        <div style="float:  right">
                            <el-button size="small" style="margin-left: 20px;" type="success"
                                       @click="handlePictureCardPreview">
                                下载模版
                            </el-button>
                        </div>
                        <div slot="tip" class="el-upload__tip">只能上传xlsx文件</div>
                    </el-upload>
                </Col>
            </Row>
            <div style="margin-top: 1rem;font-weight: 600;font-size: 17px">导入日志</div>
            <div style="height: 250px;border: 1px #dcdfe6 solid;overflow-x: hidden; font-size: 16px" id="div">
                <div v-for="item in logs" style="margin-top: 5px;padding-left: 10px">
                    {{item}}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                goEasy: null,
                data: {account: localStorage.getItem("account")},
                fileList: [],
                loading: false,
                logs: []
            }
        },
        components: {},
        created: function () {
            this.subscribe();
            this.scrollBottom();
        },
        methods: {
            //可视底部
            scrollBottom() {
                let ele = document.getElementById("div");
                //判断元素是否出现了滚动条
                if (ele.scrollHeight > ele.clientHeight) {
                    //设置滚动条到最底部
                    ele.scrollTop = ele.scrollHeight;
                }
            },
            subscribe() {
                let account = localStorage.getItem("account");
                let _this = this;
                this.$goEasy.subscribe({
                    channel:account + "Salary", //替换为您自己的channel
                    onMessage: function (message) {
                        // console.log(message);
                        // console.log("Channel:" + message.channel + " content:" + message.content);
                        _this.logs.push(message.content);
                        _this.scrollBottom();
                        _this.scrollBottom();
                        _this.scrollBottom();
                        // console.log(_this.logs)
                    }
                });
            },
            submitUpload() {
                if (this.fileList.length === 0) {
                    this.$message({
                        type: 'success',
                        message: '请选择文件!'
                    });
                    return
                }
                this.$refs.upload.submit();
                this.loading = true;
            },
            onChange(file, fileList) {
                // console.log(fileList);
                this.fileList = fileList
            },
            handleError(file, fileList) {
                this.loading = false;
                // console.log(file);
                this.$message({
                    type: 'success',
                    message: '导入失败!'
                });
            },
            handlePictureCardPreview() {
                this.download("https://xyj-pic.oss-cn-shenzhen.aliyuncs.com/TryProduct/Banner/16106997349115656.xlsx");
            },
            onExcees() {
                this.$Message.success('只可上传一个文件');
            },
            download(src) {
                let x = new XMLHttpRequest();
                x.open("GET", src, true);
                x.responseType = 'blob';
                x.onload = function (e) {
                    let url = window.URL.createObjectURL(x.response);
                    let a = document.createElement('a');
                    a.href = url;
                    a.download = "工资条文件上传模版.xlsx";
                    a.click();
                };
                x.send();
            },
            handleRemove(file) {
                this.fileList = [];
            },
            before() {
                if (this.fileList.length > 1) {
                    this.$Notice.warning({
                        title: '最多可上传1个附件'
                    });
                }
            },
            handlePreview(file) {
                this.loading = false;
                // console.log(file);
                this.$message({
                    type: 'success',
                    message: '导入成功!'
                });
            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                // console.log(322323);
                this.$emit('init-choose', "");
                this.logs=[];
            },

        },

    }
</script>


<style>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;
        height: 400px;

    }

    .el-upload--text {
        background-color: #fff;
        border: none !important;
        border-radius: 0px;
        box-sizing: border-box;
        width: auto !important;
        height: auto !important;
        text-align: center;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

</style>

