<template>
  <div class="table">
    <div class="container">
      <div class="handle-box">
          <el-row>
            <el-col :span="5" style="padding-left: 1rem">
              签订时间
                <el-date-picker @change="getData()"
                    v-model="time"
                    type="month"
                    placeholder="选择月">
                </el-date-picker>
            </el-col>
            <el-col :span="1">
              <el-button type="success" v-loading="loading" @click="exportExcel">导出</el-button>
            </el-col>
          </el-row>
      </div>
      <el-table :data="list" class="table" ref="multipleTable"
                :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
        <el-table-column
            width="230"
            prop="name"
            label="名称">
        </el-table-column>
        <el-table-column
            width="160"
            prop="no"
            label="总订单号">
        </el-table-column>
        <el-table-column
            width="100"
            prop="basePay"
            label="基本工资"
        ></el-table-column>
        <el-table-column
            width="100"
            prop="contractAmount"
            label="合同工资"
        ></el-table-column>
        <el-table-column
            width="170"
            prop="signTime"
            label="签订时间">
        </el-table-column>
        <el-table-column
            width="170"
            prop="expireTime"
            label="到期时间">
        </el-table-column>
        <el-table-column
            prop="state"
            label="状态">
          <template slot-scope="scope">
                        <span style="font-size: 15px" v-if=" scope.row.state===1">
                            正常
                        </span>
            <span style="font-size: 15px;color: red" v-if=" scope.row.state===2">
                            过期
                        </span>
          </template>
        </el-table-column>
        <el-table-column
            fixed="right"
            width="120"
            label="操作">
          <template slot-scope="scope">
            <el-row>
              <el-col :span="24">
                <div style="display: flex">
                  <el-button
                      size="small "
                      icon="el-icon-thumb"
                      @click="edit(scope.row.id)"
                      type="text">查看
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import moment from "moment";

export default {
  name: "fastExpiringContract",
  data() {
    return {
      list: [],
      time: new Date(),
      loading:false,
    }
  },
  created() {
    this.getData();
  },
  methods: {
    getData() {
      console.log(moment(this.time).format('YYYY-MM-DD'))

      this.$getData("fastExpiringContract", {time: moment(this.time).format('YYYY-MM-DD')}).then(res => {
        if (res.status === 200) {
          console.log(res.data)
          this.list = res.data
        }
      });
    },
    edit(id) {
      let url = window.location.href.split("/")[0];
      window.open(url + "/siteDetails?id=" + id + "&tabName=first")
    },

    exportExcel() {
      this.loading = true;
      this.$postData("fastExpiringContractExport", JSON.stringify({time: moment(this.time).format('YYYY-MM-DD')}), {
        responseType: "arraybuffer"
      }).then(res => {
        this.blobExport({
          tableName: "当月过期合同",
          res: res
        });
      });
    },

    blobExport({tableName, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tableName + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
      const that = this;
      setTimeout(function () {
        that.loading = false;
      }, 2500);

    },
  },
}
</script>

<style scoped>
table {
  border-collapse: collapse; /*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
  border-left: #C8B9AE solid 1px;
  border-top: #C8B9AE solid 1px;
}

table td {
  border-right: #C8B9AE solid 1px;
  border-bottom: #C8B9AE solid 1px;
}

.handle-box {
  /*margin-bottom: 10px;*/
  font-weight: bold !important;
}
</style>
