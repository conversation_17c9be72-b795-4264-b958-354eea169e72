<template>
	<div class="container">
		<divider></divider>
		<div class="handle-box">
			<el-form ref="form" :model="pageInfo">
				<el-row>
					<el-col :span="3">
						<el-form-item label="姓名">
							<el-input v-model="quer.name" placeholder="请输入姓名"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="3">
						<el-form-item label="状态">
							<el-select v-model="quer.status">
								<el-option label="启用" value="0"></el-option>
								<el-option label="禁用" value="1"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-button type="primary" @click="query()" icon="el-icon-search"
							style="margin-left: 20px">搜索</el-button>
						<el-button icon="el-icon-refresh" @click="reset()" style="margin-left: 20px">重置</el-button>
						<el-button type="primary" icon="el-icon-edit" @click="createInfo()"
							style="margin-left: 20px">新增</el-button>
					</el-col>
				</el-row>

			</el-form>
		</div>
		<el-table :data="list" height="600px" v-loading="loading" border
			:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
			<el-table-column prop="id" width="60" label="编号">
			</el-table-column>
			<el-table-column prop="name" width="100" label="姓名">
			</el-table-column>
			<el-table-column width="140" prop="headPortrait" label="头像">
				<template slot-scope="scope">
					<img :src="scope.row.headPortrait" style="width: 100px;height: 100px;"
						@click="openImg(scope.row.headPortrait)">
				</template>
			</el-table-column>
			<el-table-column prop="depart" width="100" label="所属部门">
			</el-table-column>
			<el-table-column prop="rewardType" width="100" label="奖励类型">
				<template slot-scope="scope">
					<el-tag v-if="item.id == scope.row.rewardType" v-for="(item,index) in rewardTypeList" :key="index"
						:type="typeStyleList[index % 4]">{{item.typeName}}</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="rewardAmount" label="奖励积分" sortable>
			</el-table-column>
			<el-table-column prop="introduce" label="工作介绍" width="200">
				<template slot-scope="scope">
					<span>{{scope.row.introduce||'暂无内容'}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="workDeeds" label="工作事迹" width="200">
				<template slot-scope="scope">
					<span>{{scope.row.workDeeds||'暂无内容'}}</span>
				</template>
			</el-table-column>
			<el-table-column width="100" label="是否显示">
				<template slot-scope="scope">
					<el-tag v-if="scope.row.status == 1" type="success">展示</el-tag>
					<el-tag v-if="scope.row.status == 0" type="danger">隐藏</el-tag>
				</template>
			</el-table-column>
			<el-table-column width="130" prop="creator" label="创建人">
				<template slot-scope="scope">
					<span v-if="scope.row.creatorName">{{scope.row.creatorName}}({{scope.row.creator}})</span>
					<span v-else>{{scope.row.creator||''}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="createDate" label="创建时间" width="160" sortable>
			</el-table-column>
			<el-table-column fixed="right" label="操作" width="180">
				<template slot-scope="scope">
					<el-button v-if="scope.row.status == 1" @click="updateStatus(scope.$index,0)" type="danger"
						size="small">隐藏
					</el-button>
					<el-button v-if="scope.row.status == 0" @click="updateStatus(scope.$index,1)" type="primary"
						size="small">展示
					</el-button>
					<el-button @click="updateInfo(scope.row)" type="primary" size="small">修改资料
					</el-button>
				</template>
			</el-table-column>
		</el-table>
		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="this.quer.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="10" />
		</div>


		<!--编辑-->
		<el-dialog :visible.sync="editModal" width="60%" title="优秀员工创建/编辑" :mask-closable="false">
			<el-form :model="dto" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">
				<el-form-item label="员工检索">
					<el-input v-model="searchText" placeholder="输入员工姓名/工号进行检索" @input="listEmployee"
						@change="listEmployee" style="width: 280px"></el-input>
				</el-form-item>
				<el-form-item label="员工选择" prop="employeeId">
					<el-select v-model="dto.employeeId" placeholder="请选择" @change="selectEmployee"
						:disabled="editType==1">
						<el-option v-for="(item,index) in employeeList" :key="index" :label="item.realName+'-'+item.no"
							:value="item.id">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="姓名" prop="name">
					<el-input v-model="dto.name" placeholder="请在上方检索后再进行选择" disabled style="width: 180px"></el-input>
				</el-form-item>
				<el-form-item label="奖励类型" prop="rewardType">
					<el-select v-model="dto.rewardType" placeholder="请选择">
						<el-option v-for="(item,index) in rewardTypeList" :key="index" :label="item.typeName"
							:value="item.id"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="奖励积分" prop="rewardAmount">
					<el-input v-model="dto.rewardAmount" placeholder="积分汇率1:100" style="width: 180px"
						onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
						:disabled="editType!=0">
					</el-input> 积分
				</el-form-item>
				<el-form-item label="推荐人" prop="reference">
					<el-input v-model="dto.reference" placeholder="若不填写则默认后台添加者为推荐人" style="width: 180px"></el-input>
				</el-form-item>
				<el-form-item label="自荐人" prop="selfReference">
					<el-input type="age" v-model="dto.selfReference" autocomplete="off" style="width: 180px"></el-input>
				</el-form-item>
				<!-- 	<el-form-item label="奖励类型">
					<el-select v-model="dto.rewardType" placeholder="请选择">
						<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
						</el-option>
					</el-select>
				</el-form-item> -->
				<el-form-item label="头像" prop="headPortrait">
					<el-upload class="avatar-uploader"
						action="https://biapi.xiaoyujia.com/qiyeCustomer/ploadPicturesu33" :show-file-list="false"
						:on-success="handleAvatarSuccessByHeadPortrait" :before-upload="beforeAvatarUpload">
						<img v-if="dto.headPortrait" :src="dto.headPortrait" class="avatar">
						<i v-else class="el-icon-plus avatar-uploader-icon"></i>
					</el-upload>
				</el-form-item>
				<el-form-item label="工作介绍" prop="introduce">
					<el-input type="textarea" :rows="3" placeholder="请输入工作介绍" v-model="dto.introduce">
					</el-input>
				</el-form-item>
				<el-form-item label="推荐事迹" prop="workDeeds">
					<el-input type="textarea" :rows="3" placeholder="请输入推荐事迹" v-model="dto.workDeeds">
					</el-input>
				</el-form-item>
				<el-form-item label="图片展示" prop="workDeeds">
					<div style="display: flex;">
						<div v-for="(item,index) in imgList" :key="index" style="position: relative;">
							<img :src="item.imgUrl" alt="" style="width: 100px;height: 100px;margin: 0 10px;"
								@click="openImg(item.imgUrl)" />
							<i class="el-icon-error" style="position: absolute;right: -5px;"
								@click="deleteExcellentEmployeeImg(index)"></i>
						</div>
						<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
							list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
							<img :src="imgUpload" style="width: 100px;height: 100px;margin: 0 10px;">
						</el-upload>
					</div>
				</el-form-item>
				<el-form-item label="归属部门" prop="depart">
					<el-input type="age" v-model="dto.depart" autocomplete="off" style="width: 180px"></el-input>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="submitForm('ruleForm')">{{editType==0?'立即创建':'更新记录'}}</el-button>
					<el-button @click="resetForm('ruleForm')" v-if="editType==0" type="danger">重置</el-button>
					<el-button @click="editModal=false;">取消</el-button>
				</el-form-item>
			</el-form>
		</el-dialog>


		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>
	</div>

</template>

<script>
	import Vue from 'vue';

	export default {
		name: "excellentEmployee",

		data() {
			return {
				imgUpload: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669101235017img-upload.png",
				options: [{
					value: '1',
					label: '最佳招工奖'
				}, {
					value: '2',
					label: '最佳开发奖'
				}, {
					value: '3',
					label: '最佳品质奖'
				}, {
					value: '4',
					label: '最佳执行奖'
				}, {
					value: '5',
					label: '最佳业绩奖'
				}],
				typeStyleList: ['success', 'info', 'warning', 'primary'],
				imageUrl: '',
				imgModal: false,
				editModal: false,
				editType: 0,
				list: null,
				loading: true,
				pageSizeOpts: [10, 20, 30],
				pageInfo: {
					total: 10,
					size: 10,
					current: 1,
					pages: 1
				},
				quer: {
					no: null,
					name: null,
					status: null,
					size: 10,
					current: 1,
					orderBy: 't.id DESC'
				},
				searchText: '',
				dto: {
					id: null,
					reference: null,
					selfReference: null,
					headPortrait: null,
					introduce: null,
					workDeeds: null,
					status: null,
					rewardType: null,
				},
				rowData: null,
				journalInfoLoading: true,
				journalList: null,
				journalInfoSizeOpts: [10, 20, 30],
				journalInfo: {
					total: 10,
					size: 10,
					current: 1,
					pages: 1
				},
				journalInfoDrawer: false,
				rules: {
					name: [{
						required: true,
						message: '请检索并选择',
						trigger: 'blur'
					}, ],
				},
				employeeList: [],
				rewardTypeList: [],
				imgList: [],
			}
		},
		created() {
			this.getData()
			this.listExcellentEmployeeRewardType()
		},
		methods: {
			getData() {
				this.$postData("selectExcellentEmployee", this.quer).then(res => {
					this.loading = false;
					if (res.status === 200) {
						this.list = res.data.records;
						this.pageInfo.current = res.data.current;
						this.pageInfo.size = res.data.size;
						this.pageInfo.total = res.data.total
					}
				})
			},
			query() {
				this.loading = true
				this.quer.current = 1
				this.getData()
			},
			reset() {
				this.list = []
				this.quer.name = null
				this.quer.status = null
				this.quer.current = 1
				this.getData();
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.dto.size = size;
				this.getData();
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index;
				this.getData();
			},
			// 打开图片预览
			openImg(url) {
				this.imageUrl = url || this.blankImg
				this.imgModal = true
			},
			updateStatus(index, status) {
				this.list[index].status = status
				this.$postData("updateExcellentEmployee", this.list[index]).then(res => {
					if (res.status === 200) {
						this.$message({
							message: '更新成功',
							type: 'success'
						});
					} else {
						this.$message.error('更新失败');
					}
				})
			},
			updateInfo(val) {
				this.editModal = true;
				this.editType = 1
				this.dto = val
				this.listExcellentEmployeeImgById(val.id)
			},
			createInfo() {
				this.dto = {}
				this.imgList = []
				this.editModal = true
				this.editType = 0
			},
			handleAvatarSuccessByHeadPortrait(response, res, file) {
				this.dto.headPortrait = response.data;
			},
			beforeAvatarUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;

				if (!isLt2M) {
					this.$message.error('上传头像图片大小不能超过 2MB!');
				}
				return isLt2M;
			},
			submitForm(formName) {
				let no = localStorage.getItem('account') || 'admin'
				if (!this.dto.memberId) {
					this.$message.error('请注意，该员工暂时未绑定会员账号，可能导致积分奖励无法赠送，请联系员工登录小程序或请技术处理！')
					return
				}

				if (!this.dto.introduce && !this.dto.workDeeds) {
					this.$message.error('请填写工作介绍或事迹！')
					return
				}

				if (!this.dto.rewardAmount) {
					this.$message.error('请填写奖励积分！')
					return
				}
				this.$refs[formName].validate((valid) => {
					if (valid) {
						this.$set(this.dto, "creator", this.dto.id ? null : no)
						let url = this.dto.id ? 'updateExcellentEmployee' : 'insertExcellentEmployee'
						$.ajax({
							type: "post",
							url: "https://api.xiaoyujia.com/content/" + url,
							data: JSON.stringify(this.dto),
							dataType: "json",
							headers: {
								"Content-Type": "application/json"
							},
							success: res => {
								if (res.code === 0) {
									this.$message.success('操作成功！')
									this.$refs[formName].resetFields()
									this.editModal = false
									if (!this.dto.id) {
										this.rewardExcellentEmployee()
									}
									this.dto = res.data
									this.updateExcellentEmployeeImgList()
									this.getData()
								} else {
									this.$message.error('更新失败')
								}
							},
						})
					} else {
						console.log('error submit!!');
						return false;
					}
				});
			},
			resetForm(formName) {
				//   console.info(formName)
				this.searchText = ''
				this.$refs[formName].resetFields();
			},
			// 获取索引
			getIndex(val, list) {
				let index = 0
				for (let i = 0; i < list.length; i++) {
					if (val == list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 选中员工
			selectEmployee(id) {
				let index = this.getIndex(id, this.employeeList)
				let employee = this.employeeList[index]
				this.dto.name = employee.realName || ''
				this.dto.depart = employee.departName || ''
				this.dto.headPortrait = employee.headPortrait || ''
				this.dto.memberId = employee.memberId || 0
			},
			// 打赏
			rewardExcellentEmployee() {
				let employeeId = localStorage.getItem('id') || null
				$.ajax({
					type: "post",
					url: "https://api.xiaoyujia.com/content/rewardExcellentEmployee",
					data: JSON.stringify({
						memberId: 298434,
						employeeId: employeeId,
						excellentEmployeeId: this.dto.id,
						rewardAmount: this.dto.rewardAmount
					}),
					dataType: "json",
					headers: {
						"Content-Type": "application/json"
					},
					success: res => {
						if (res.code == 0) {}
					},
				})
			},
			deleteExcellentEmployeeImg(index) {
				let id = this.imgList[index].id
				if (id) {
					$.ajax({
						type: "GET",
						url: "https://api.xiaoyujia.com/content/deleteExcellentEmployeeImg/" + id,
						data: {},
						dataType: "json",
						success: res => {

						},
					})
				}
				this.$delete(this.imgList, index)
				this.$message.success('图片删除成功！')
			},
			// 更新图片
			updateExcellentEmployeeImgList() {
				this.imgList.forEach(item => {
					if (!item.id) {
						this.$set(item, 'excellentEmployeeId', this.dto.id)
					}
				})
				$.ajax({
					type: "post",
					url: "https://api.xiaoyujia.com/content/updateExcellentEmployeeImgList",
					data: JSON.stringify(this.imgList),
					dataType: "json",
					headers: {
						"Content-Type": "application/json"
					},
					success: res => {},
				})
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				let data = {
					id: 0,
					imgUrl: res.data,
					excellentEmployeeId: this.dto.id || null
				}
				this.imgList.push(data)
			},
			// 获取图片
			listExcellentEmployeeImgById(id) {
				$.ajax({
					type: "GET",
					url: "https://api.xiaoyujia.com/content/listExcellentEmployeeImgById/" + id,
					data: {},
					dataType: "json",
					success: res => {
						if (res.code == 0) {
							this.imgList = res.data
						} else {
							this.imgList = []
						}
					},
				})
			},
			// 获取奖励类型
			listExcellentEmployeeRewardType() {
				$.ajax({
					type: "GET",
					url: "https://api.xiaoyujia.com/content/listExcellentEmployeeRewardType",
					data: {},
					dataType: "json",
					success: res => {
						if (res.code == 0) {
							this.rewardTypeList = res.data
						} else {
							this.rewardTypeList = []
						}
					},
				})
			},
			listEmployee() {
				if (!this.searchText) {
					return
				}
				this.$postData("listEmployeeDto", {
					search: this.searchText,
					employeeType: 20,
					state: 1
				}).then(res => {
					if (res.status == 200) {
						this.employeeList = res.data
					} else {
						this.$message.error("查询失败，" + res.msg)
					}
				})
			}
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 100px;
		height: 100px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 100px;
		height: 100px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 100px;
		height: 100px;
		display: block;
	}
</style>