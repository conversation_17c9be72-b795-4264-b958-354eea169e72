<template>
    <div class="table">
        <el-dialog title="员工位置" :visible.sync="addr" v-if="addr" style="width: 1500px">
            <addr-map :lng="loc.loc.lon" :lat="loc.loc.lat" :width="'700px'" ></addr-map>
        </el-dialog>
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item>
                    <i class="el-icon-lx-cascades"></i> 订单统计
                </el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo" :inline="true">
                    <el-row>
                        <el-col :span="10">
                            <el-form-item label="服务项目">
                                <Select filterable style="width: 180px" @on-change="changeValue"
                                        v-model="dto.productCategoryId">
                                    <Option v-for="item in options" :value="item.value" :key="item.value">{{ item.text}}</Option>
                                </Select>
                                <Select filterable style="width: 250px" v-model="dto.productIds" multiple>
                                    <Option :value="0">全部</Option>
                                    <Option v-for="item in options_children" :value="item.value" :key="item.value">{{item.text}}
                                    </Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="10">
                            <el-form-item label="服务金额">
                                <el-input
                                        v-model="dto.consumptionTotal1"
                                        placeholder="消费范围"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                                -
                                <el-input
                                        v-model="dto.consumptionTotal2"
                                        placeholder="消费范围"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4" style="text-align:right">
                            <el-button type="primary" @click="onSubmit">搜索</el-button>
                            <el-button type="success" :loading="daochu" @click="exportExcel">导出Excel</el-button>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="10">
                            <el-form-item label="服务时间">
                                <el-date-picker
                                        v-model="realStartTime"
                                        type="daterange"
                                        align="right"
                                        unlink-panels
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        format="yyyy 年 MM 月 dd 日"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="10">
                            <el-form-item label="结算时间">
                                <el-date-picker
                                        v-model="settlementTime"
                                        type="daterange"
                                        align="right"
                                        unlink-panels
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        format="yyyy 年 MM 月 dd 日"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="10">
                            <el-form-item label="服务区域">
                            <Select filterable style="width: 180px" @on-change="getCity"
                                    v-model="dto.cityId">
                                <Option v-for="item in serviceOptions" :value="item.value" :key="item.value">{{ item.label}}</Option>
                            </Select>
                            <Select filterable style="width: 250px" v-model="dto.areaIds" multiple>
                                <Option :value="0">全部</Option>
                                <Option v-for="item in cityList" :value="item.id" :key="item.id">{{item.name}}</Option>
                            </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="服务地址">
                                <el-input
                                        v-model="dto.street"
                                        placeholder="服务地址"
                                        style="width:250px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="会员ID">
                                <el-input
                                        v-model="dto.memberId"
                                        placeholder="会员ID"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="下单时间">
                                <el-date-picker
                                        v-model="dto.createTime1"
                                        type="datetime"
                                        placeholder="开始时间"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                            <!-- </el-col>
                            <el-col :span="6">-->
                            <el-form-item label="至">
                                <el-date-picker
                                        v-model="dto.createTime2"
                                        type="datetime"
                                        placeholder="结束时间"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="服务状态">
                                <el-select
                                        v-model="dto.orderState"
                                        placeholder="请选择"
                                        style="width:180px"
                                        class="mr10"
                                >
                                    <el-option
                                            v-for="item in options_orderState"
                                            :key="item.value"
                                            :label="item.text"
                                            :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="是否管家帖下单">
                                <el-select
                                        v-model="dto.isGjt"
                                        placeholder="请选择"
                                        style="width:180px"
                                        class="mr10"
                                >
                                    <el-option
                                            v-for="item in options_isGjt"
                                            :key="item.value"
                                            :label="item.text"
                                            :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="会员账号">
                                <el-input
                                        v-model="dto.account"
                                        placeholder="会员账号"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="订单编号">
                                <el-input
                                        v-model="dto.orderNo"
                                        placeholder="订单编号"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="开发人">
                                <el-input
                                        v-model="dto.searchCode"
                                        placeholder="开发人"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div style="text-align:right">{{theader}}</div>
            <el-table
                    :data="data"
                    border
                    class="table"
                    ref="multipleTable"
                    @sort-change="sortChange"
            >
                <el-table-column
                        prop="billNo"
                        label="操作"
                        width="300"
                        sortable="custom"
                >
                    <template slot-scope="scope">

                        <el-button type="info" @click="getByBillNo(scope.row.billNo)" :disabled="scope.row.orderState=='已接单'" size="mini">服务照片</el-button>
                        <el-button type="danger" @click="backOrder(scope.row.billNo)" :disabled="scope.row.orderState!='已接单'" size="mini">取消订单</el-button>
                        <el-button type="info" @click="showAddr(scope.row.billNo)" size="mini">员工位置</el-button>

                    </template>
                </el-table-column>
                <el-table-column
                        prop="billNo"
                        label="订单号"
                        width="100"
                        sortable="custom"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="memberId"
                        label="会员Id"
                        width="100"
                        sortable="custom"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="account"
                        label="会员账户"
                        width="120"
                        sortable="custom"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="code"
                        label="开发人"
                        width="100"
                        sortable="custom"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="belongChannel"
                        label="所属渠道"
                        width="120"
                        sortable="custom"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop=""
                        label="服务状态"
                        width="120"
                        sortable="custom"
                        :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                        <span v-if="scope.row.isGroup&&scope.row.splitUserId!=null">{{scope.row.orderState}}(已拆单)</span>
                        <span v-else-if="scope.row.isGroup&&scope.row.splitUserId==null">{{scope.row.orderState}}(未拆单)</span>
                        <span v-else>{{scope.row.orderState}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="productName"
                        label="服务项目"
                        width="100"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column prop="areaName" label="服务区域" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column
                        prop="street"
                        label="服务地址"
                        width="120"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="totalAmount"
                        label="服务金额"
                        sortable="custom"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="amount"
                        label="实付金额"
                        sortable="custom"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="realStartTime"
                        label="服务时间"
                        sortable="custom"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="createTime"
                        label="下单时间"
                        sortable="custom"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="settleTime"
                        label="结算时间"
                        sortable="custom"
                        :show-overflow-tooltip="true"
                ></el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                        background
                        layout="total,sizes,prev, pager, next,jumper"
                        :total="pageInfo.total"
                        :page-size="pageInfo.size"
                        :current-page="pageInfo.current"
                        :page-sizes="[10,20,50,100]"
                        :page-count="pageInfo.pages"
                        @size-change="sizeChange"
                        @current-change="currentChange"
                ></el-pagination>
            </div>
        </div>
        <el-dialog title="服务图片" :visible.sync="showUserTrackList">
            <div v-for="(item,index) in userTrackList" :key="index">
<!--                <img :src="item.url" style="width: 100%">-->
                <el-image
                        style="width: 200px;"
                        :src="item.url"
                        :preview-src-list="userTrackList1">
                </el-image>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import addrMap from "./agent/char/addrMap";
    export default {
        name: "basetable",
        components: {
            'addrMap':addrMap,
        },
        data() {
            return {
                showUserTrackList:false,
                userTrackList:[],
                userTrackList1:[],
                addr:false,
                daochu:false,
                routeProduct:this.$route.query.routeProduct,//标识是否售卡跳转过来的
                yeji:this.$route.query.yeji,//标识是否售卡跳转过来的
                tableData: [],
                query:null,
                htmlList:null,
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                theader: "",
                registerTime: [],
                settlementTime: [],


                options: [], //服务项目一级
                options_children: [], //服务项目二级
                realStartTime: [],
                settlementTime: [],
                serviceOptions: [], //服务区域
                serviceArray: [],
                options_orderState: [],
                options_isGjt: [
                    {text: "请选择", value: "0"},
                    {text: "是", value: "1"},
                    {text: "否", value: "2"}
                ],
                dto: {
                    searchCode:null,
                    productCategoryId: null,
                    cityId:null,
                    areaId: "",
                    areaIds: null,
                    productId: "",
                    productIds:[],
                    realStartTime1: "",
                    realStartTime2: "",
                    settlementTime1: "",
                    settlementTime2: "",
                    street: "",
                    totalAmount1: "",
                    totalAmount2: "",
                    createTime1: "",
                    createTime2: "",
                    orderState: "",
                    memberId: "",
                    isGjt: "0",
                    account:null,
                    orderNo:null,
                    export: "",
                    orderBy: "",
                    pageIndex: "",
                    pageSize: ""
                },
                cityList:null,
                loc:{},
            };
        },
        created() {
            this.$getData("getProductCategory").then(res => {
                //res.data.unshift({text: "请选择", value: ""});
                this.options = res.data;
                console.log( this.options)
            });
            // 服务区域下拉框
            this.$getData("getCityArea").then(res => {
               // res.data.unshift({label: "请选择", value: ""});
                console.log(res.data)
                this.serviceOptions = res.data;
            });


            this.$getData("getOrderState", {}).then(res => {
                res.data.unshift({text: "请选择", value: ""});
                this.options_orderState = res.data;
            });

            if(this.routeProduct){
              this.localOrder();
            }else if(this.yeji){
                this.localPe();
            }else {
                let y = new Date().getFullYear(),
                    m = new Date().getMonth() + 1,
                    d = new Date().getDate();
                this.dto.createTime2 =
                    y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d) + " 23:59:59";
                m -= 1;
                if (m == 0) {
                    y -= 1;
                    m = 12;
                }
                this.dto.createTime1 =
                    y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d) + " 00:00:00";
            }
            this.getData();
        },
        computed: {
            data() {
                return this.tableData.filter(d => {
                    console.log(d);
                    d.realStartTime =
                        d.realStartTime == null
                            ? null
                            : d.realStartTime.replace("T", " ").substring(0, 19);
                    d.createTime =
                        d.createTime == null
                            ? null
                            : d.createTime.replace("T", " ").substring(0, 19);
                    d.settleTime =
                        d.settleTime == null
                            ? null
                            : d.settleTime.replace("T", " ").substring(0, 19);
                    if( d.account!=null&&d.account!=''){
                        d.account=  d.account.substring(0,3) + '****' + d.account.substring(d.account.length-4);
                    }
                    return d;
                });
            }
        },
        watch: {
            '$route' (to, from) {
                console.log(to);
                if(to.query.yeji){
                    this.localPe();
                }else if(to.query.routeProduct){
                    this.localOrder();
                }
            }
        },
        methods: {
            getByBillNo(billNo){
                //billNo=****************
                this.$getData("getByBillNo", {billNo:billNo}, {}).then(res => {
                    if (res.status == 200) {

                        if (res.data==null){
                            return this.$message.error("暂无图片")
                        }else {
                            this.userTrackList=res.data.imgFile
                            this.userTrackList.forEach(v=>{
                                this.userTrackList1.push(v.url)
                            })
                            this.showUserTrackList=true
                        }
                    }else {
                        this.$message.error(res.msg)

                    }
                })
            },
            backOrder(billNo){
                this.$confirm('此操作将取消该订单, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.openFullScreen();
                    this.$getData("backOrder", {billNo:billNo}, {}).then(res => {
                        if (res.status == 200) {
                            this.$message.success("订单已成功取消！优惠券已退回！")
                            this.getData();
                            this.closeFullScreen();
                        }else {
                            this.$message.error(res.msg)
                            this.closeFullScreen();
                        }
                    })
                }).catch(() => {
                    return this.$message({
                        type: 'info',
                        message: '已取消操作'
                    });
                });

            },
            showAddr(billNo){
                //billNo=2102220954431366
                this.$getUrl("getLocByBillNo",billNo,null).then(
                    res => {
                        if ( res.data.length==0){
                            return this.$alert("暂无信息")
                        }
                        this.loc = res.data[0];
                        this.addr=true
                    }
                );
            },
            changeValue(value) {
                if(value!=null){
                    this.$getData("getProductName", {productCategoryId: value}).then(
                        res => {
                            this.options_children = res.data;
                        }
                    );
                }
            },

            localOrder(){
                if(this.$route.query.routeProduct){
                    this.settlementTime=[];
                    this.dto.settlementTime1=null
                    this.dto.settlementTime2= null
                    this.query=this.$route.query
                    this.dto.productCategoryId= this.query.productCategoryId+""
                    this.$getData("getProductName", {productCategoryId: this.query.productCategoryId}).then(
                        res => {
                            this.options_children = res.data;
                            this.options_children .unshift({ text: "全部", value: "0" });
                            this.dto.productIds=[];
                            if(this.query.productId!=0){
                                console.log("85694")
                                this.dto.productIds.push(this.query.productId+"");
                                console.log(this.dto.productIds);
                                this.dto.createTime1 = this.query.dt+"-01 00:00:00"
                                this.$getData("getMonthLash", {date:this.dto.createTime1}).then(
                                    res => {
                                        this.dto.settlementTime1=null
                                        this.dto.settlementTime2= null

                                        let month=res.data.split("-");
                                        console.log(this.query.dt+"-"+month[2]+" 23:59:59")
                                        this.dto.createTime2=this.query.dt+"-"+month[2]+" 23:59:59"

                                        console.log(this.dto.createTime1)
                                        console.log(this.dto.createTime2)
                                        this.getData();
                                        this.routeProduct=false;
                                    }
                                );
                            }else {
                                console.log("silala")
                                console.log(this.query.productId)
                                this.dto.productIds.push(this.query.productId);
                                console.log(this.dto.productIds);
                                this.dto.createTime1 = this.query.dt+"-01 00:00:00"
                                this.$getData("getMonthLash", {date:this.dto.createTime1}).then(
                                    res => {
                                        this.dto.settlementTime1=null
                                        this.dto.settlementTime2= null

                                        let month=res.data.split("-");
                                        console.log(this.query.dt+"-"+month[2]+" 23:59:59")
                                        this.dto.createTime2=this.query.dt+"-"+month[2]+" 23:59:59"

                                        console.log(this.dto.createTime1)
                                        console.log(this.dto.createTime2)
                                        this.getData();
                                        this.routeProduct=false;
                                    }
                                );
                            }
                        }
                    );
                }
            },

            localPe(){
                if(this.$route.query.yeji){
                    this.dto.createTime1=null
                    this.dto.createTime2= null
                    this.htmlList=this.$route.query
                    console.log(this.htmlList)
                    this.dto.productCategoryId= this.htmlList.productCategoryId+""
                    console.log(this.htmlList.productCategoryId)
                    if(this.htmlList.productCategoryId!=null&&this.htmlList.productCategoryId!='') {
                        this.$getData("getProductName", {productCategoryId: this.htmlList.productCategoryId}).then(
                            res => {
                                this.options_children = res.data
                                this.dto.productIds = [];
                                if (this.htmlList.productId != 0 && this.htmlList.productId != null) {
                                    console.log("45")
                                    this.dto.productIds.push(this.htmlList.productId + "");
                                    console.log(this.dto.productIds);
                                    this.settlementTime=[];
                                    this.settlementTime.push(this.htmlList.dt+"-01 00:00:00")
                                    this.$getData("getMonthLash", {date:this.settlementTime[0]}).then(
                                        res => {

                                            if(res.status==200){
                                                let month=res.data.split("-");
                                                console.log(this.htmlList.dt+"-"+month[2]+" 23:59:59")
                                                this.settlementTime.push(this.htmlList.dt+"-"+month[2]+" 23:59:59");
                                                console.log(this.settlementTime)
                                                this.dto.settlementTime1= this.settlementTime[0]
                                                this.dto.settlementTime2= this.settlementTime[1]
                                                this.getData();
                                                this.yeji=false;
                                            }
                                        }
                                    );
                                } else if (this.htmlList.productId != null && this.htmlList.productId != '') {
                                    this.dto.productIds.push(this.htmlList.productId);
                                    console.log(this.dto.productIds);
                                    this.settlementTime=[];
                                    this.settlementTime.push(this.htmlList.dt+"-01 00:00:00")
                                    this.$getData("getMonthLash", {date:this.settlementTime[0]}).then(
                                        res => {

                                            if(res.status==200){
                                                let month=res.data.split("-");
                                                console.log(this.htmlList.dt+"-"+month[2]+" 23:59:59")
                                                this.settlementTime.push(this.htmlList.dt+"-"+month[2]+" 23:59:59");
                                                console.log(this.settlementTime)
                                                this.dto.settlementTime1= this.settlementTime[0]
                                                this.dto.settlementTime2= this.settlementTime[1]
                                                this.getData();
                                                this.yeji=false;
                                            }
                                        }
                                    );
                                }
                            }
                        );
                    }else {
                        this.dto.productIds=[];
                        console.log(this.dto.productIds);
                        this.settlementTime=[];
                        this.settlementTime.push(this.htmlList.dt+"-01 00:00:00")
                        this.$getData("getMonthLash", {date:this.settlementTime[0]}).then(
                            res => {

                                if(res.status==200){
                                    let month=res.data.split("-");
                                    console.log(this.htmlList.dt+"-"+month[2]+" 23:59:59")
                                    this.settlementTime.push(this.htmlList.dt+"-"+month[2]+" 23:59:59");
                                    console.log(this.settlementTime)
                                    this.dto.settlementTime1= this.settlementTime[0]
                                    this.dto.settlementTime2= this.settlementTime[1]
                                    this.getData();
                                    this.yeji=false;
                                }
                            }
                        );
                    }
                }
            },

            getCity(value) {
                this.$getData("getCityId", {cityId: value}).then(
                    res => {
                        this.cityList = res.data;
                    }
                );
            },
            getData() {
                this.openFullScreen();
                this.$postData("operate_ordersStatistics", this.dto, {})
                    .then(res => {
                        if (res.meta.state == 200) {

                            this.tableData = res.data.tbody.records;
                            this.pageInfo = res.data.tbody;

                            let theader = "";
                            for (let i of res.data.theader) {
                                theader += i + " ";
                            }
                            this.theader = theader;
                            this.closeFullScreen();
                        } else {
                            this.closeFullScreen();
                            this.$message.error("查询失败，" + res.meta.msg);
                        }
                    })
                    .catch(error => {
                        this.closeFullScreen();
                        this.$message.error("查询失败，系统超时");
                    });
            },
            onSubmit() {
                this.dto.realStartTime1 = this.realStartTime
                    ? this.realStartTime[0] || ""
                    : "";
                this.dto.realStartTime2 = this.realStartTime
                    ? this.realStartTime[1] || ""
                    : "";
                this.dto.settlementTime1 = this.settlementTime
                    ? this.settlementTime[0] || ""
                    : "";
                this.dto.settlementTime2 = this.settlementTime
                    ? this.settlementTime[1] || ""
                    : "";
                if(this.dto.settlementTime2!=""&&this.dto.settlementTime2!=null){
                    this.dto.settlementTime2=this.dto.settlementTime2.substring(0,10)+" 23:59:59";
                }
                this.dto.areaId = this.serviceArray[this.serviceArray.length - 1];
                this.dto.export = "";
                // this.dto.orderBy = "";
                // this.dto.pageSize = "";
                this.dto.pageIndex = "1";

                this.getData();
            },
            exportExcel() {
                this.daochu=true
                this.dto.export = "Excel";
                this.$postData("operate_ordersStatistics", this.dto, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.daochu=false
                    this.blobExport({
                        tablename: "订单统计",
                        res: res
                    });
                    this.dto.export = "";
                });
            },
            // 页码大小
            sizeChange(size) {
                this.dto.pageSize = size;
                this.dto.pageIndex = 1;
                this.getData();
            },
            // 跳转页码
            currentChange(index) {
                this.dto.pageIndex = index;
                this.getData();
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
            openFullScreen() {
                const loading = this.$loading({
                    lock: true,
                    text: "Loading",
                    spinner: "el-icon-loading",
                    background: "rgba(0, 0, 0, 0.7)"
                });
            },
            closeFullScreen() {
                const loading = this.$loading({
                    lock: true,
                    text: "Loading",
                    spinner: "el-icon-loading",
                    background: "rgba(0, 0, 0, 0.7)"
                });
                loading.close();
            },
            sortChange: function (column, prop, order) {
                if (column == null) {
                    this.dto.orderBy = "";
                } else {
                    if (column.order == "ascending") {
                        this.dto.orderBy = column.prop + " ASC";
                    }
                    if (column.order == "descending") {
                        this.dto.orderBy = column.prop + " DESC";
                    }
                }
                this.getData();
            },
            sortChange: function (column, prop, order) {
                if (column == null) {
                    this.dto.orderBy = "";
                } else {
                    if (column.order == "ascending") {
                        this.dto.orderBy = column.prop + " ASC";
                    }
                    if (column.order == "descending") {
                        this.dto.orderBy = column.prop + " DESC";
                    }
                }
                this.getData();
            }
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 14px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
