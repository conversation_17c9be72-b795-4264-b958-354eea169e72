<template>
    <el-autocomplete
            v-model="agentName"
            :fetch-suggestions="querySearchAsync"
            placeholder="请输入内容"
            @select="handleSelect"
            :trigger-on-focus="false">
        <template slot="prepend">所属经纪人:</template>
    </el-autocomplete>
</template>

<script>
    export default {

        name: "agentChoose",
        data() {
            return{
                agentName:null,
                restaurants:[],
                agentmodel:{
                    RealName:null,
                    No:null,
                }
            }
        },

        methods: {
            handleSelect(item) {
                console.log(item);
            },
            querySearchAsync(queryString, cb) {
                this.restaurants=[]
                this.agentmodel.RealName=this.agentName;
                this.$postData("agent_list", this.agentmodel, {}).then(res => {
                    if (res.status == 200) {
                        this.list = res.data;
                        this.list.forEach((item, index, arr) => {
                            var a={}
                            a.value=item.realName;
                            a.name=item.id;
                            this.restaurants.push(a);
                        });
                        this.list=[]
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                var restaurants = this.restaurants;
                var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants;
                cb(restaurants);
            },
            createStateFilter(queryString) {
                return (state) => {
                    return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                };
            },
        }
    }
</script>

<style scoped>

</style>
