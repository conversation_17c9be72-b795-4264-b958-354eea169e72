<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col span="24">
                    <div>
                        <div id="operBtnDiv">
                            <div id="searchDiv">
                                <Form ref="formItem" :model="formItem" :label-width="80" label-position="left"
                                      :rules="ruleValidate">
                                    <FormItem label="项目名称" prop="projectId">
                                        <Select v-model="formItem.projectId" @on-change="getAmount" filterable >
                                            <Option v-for="item in cityList"  :value="item.value" :key="item.value">{{ item.label }}</Option>
                                        </Select>
                                    </FormItem>

                                    <FormItem label="角色" prop="role">
                                        <Input placeholder="请输入角色" v-model="formItem.role">
                                        </Input>
                                    </FormItem>


                                    <FormItem label="所属成员" prop="employeeIds1">
                                        <!--   <Input placeholder="请选择成员" v-model="formItem.employeeNames"
                                                  @on-focus="usersModal=true" readonly="readonly">
                                           </Input>-->
                                        <Select v-model="formItem.employeeIds1" filterable multiple style="width:100%"
                                                @on-change="isMember">
                                            <Option v-for="item in employees" :value="item.id">{{ item.realName}}</Option>
                                        </Select>
                                    </FormItem>
                                    <FormItem label="项目资金" prop="capital">
                                        <Input placeholder="请输入项目资金" v-model="formItem.capital" @on-change="getProportion">
                                        </Input>
                                    </FormItem>

                                    <FormItem label="分润比例" prop="proportion">
                                        <Input placeholder="请输入分润比例" v-model="formItem.proportion">
                                        </Input>
                                    </FormItem>

                                    <FormItem>
                                        <Button type="primary" @click="saveProjectMember('formItem')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </FormItem>


                                </Form>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
        <!-- <Modal v-model="usersModal" class="Modal" :width="screenWidth" title="成员">
             <div class="addBody">

                 <employees-choose v-if="usersModal" @init-choose="initChooseProject"
                                   @close-modal="closeCurrModal"></employees-choose>
             </div>
             <div slot="footer">
                 <el-button @click="closeCurrModal">取消</el-button>
                 <el-button type="primary" :loading="userLoading" @click="closeCurrModal">确定</el-button>
             </div>
         </Modal>-->
    </div>
</template>


<script>
    import employeesChoose from '@/components/page/project/choose/employeesChoose.vue'


    export default {
        data() {
            return {
                employees: null,
                usersModal: false,
                userLoading: false,
                screenWidth: '30%',
                cityList: [],
                amount: null,
                formItem: {
                    employeeIds: "",   //负责人id
                    employeeIds1: null,
                    name: null,    //项目名称
                    employeeNames: "",
                    role: null,
                    projectId: null,
                    proportion: null,
                    capital: null,
                },
                projectManage: {
                    projectId: null,
                    name: null,
                    state: 1,
                },
                isQuery: {
                    employeeIds: "",
                    projectId: null,
                    employeeName: null
                },
                ruleValidate: {
                    projectId: [
                        {required: true, message: '请选择项目名称', trigger: 'change', type: 'number'},
                    ],
                    capital: [
                        {required: true, message: '请输入项目资金', trigger: 'blur'},
                        {required: true, pattern: /(^[\-0-9][0-9]*(.[0-9]+)?)$/, message: '请输入数字', trigger: 'change'}
                    ],
                    employeeIds1: [
                        {required: true, message: '请选择负责人', trigger: 'change', type: "array"}
                    ],
                    role: [
                        {required: true, message: '请输入角色名称', trigger: 'blur'}
                    ],
                    proportion: [
                        {required: true, message: '请输入分润比例', trigger: 'blur'},
                    ],
                }


            }
        },
        components: {
            'employeesChoose': employeesChoose
        },
        created: function () {
            this.getData();
            this.getByEmployees();
        },
        methods: {
            getByEmployees() {
                this.$postData("employee_getByList", {}, {}).then(res => {
                    if (res.status == 200) {
                       ;
                        this.employees = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.meta.msg);
                    }
                })
            },
            getData() {
                this.$postData("project_selectList", this.projectManage, {}).then(res => {
                    if (res.meta.state == 200) {

                        this.getSelect(res.data.lists);
                    } else {
                        this.$message.error("查询失败，" + res.meta.msg);
                    }
                })
            },

            getSelect(data) {
                if (data) {
                    for (let i = 0; i < data.length; i++) {
                        let line = {
                            value: data[i].id,
                            label: data[i].name
                        }
                        this.cityList.push(line)
                    }
                }
            },
            getAmount(id) {
                this.$getData("project_getById", {id: id}).then(res => {
                    if (res.status == 200) {

                        this.amount = res.data.capital
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getProportion() {
                let tempVal = parseFloat(this.formItem.capital/this.amount).toFixed(3)
                console.log(tempVal)
                this.formItem.proportion= tempVal*100+"%"
            },

            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.usersModal = false;
            },
            initChooseProject(data) {
                this.formItem.employeeNames = "";
                this.formItem.employeeIds = ""
                for (let i = 0; i < data.length; i++) {
                    this.formItem.employeeIds += data[i].id + ",";
                    this.formItem.employeeNames += data[i].realName + " ";
                }
                this.isMember();
            },
            saveProjectMember(name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        console.log(this.isQuery.employeeName)
                        if (this.isQuery.employeeName == null) {
                            this.$postData("projectMember_saveProject", this.formItem, {}).then(res => {
                                if (res.meta.state == 200) {
                                    this.$Message.success('添加成功');
                                    this.chooseThisModel();
                                } else {
                                    this.$message.error("添加失败，" + res.meta.msg);
                                }
                            })
                        } else {
                            this.open();
                        }
                    }
                })
            },
            /*
            * 关闭当前窗口
            * */
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            },
            /*
            * 判断该员工是否已存在该项目
            * */
            isMember() {
                console.log(this.isQuery.employeeName)
                this.formItem.employeeNames = "";
                this.isQuery.employeeName = null;
                console.log(this.isQuery.employeeName)
                console.log(this.formItem.employeeIds1)
                if (this.formItem.employeeIds1) {
                    this.isQuery.employeeIds = "";
                    for (let i = 0; i < this.formItem.employeeIds1.length; i++) {
                        let ids = this.formItem.employeeIds1[i];
                        this.isQuery.employeeIds += ids + ","
                    }
                    console.log(this.isQuery.employeeIds)
                    this.formItem.employeeIds = this.isQuery.employeeIds
                }
                this.isQuery.projectId = this.formItem.projectId;
                this.$postData("is_ProjectMember", this.isQuery, {}).then(res => {

                    if (res.data) {
                        this.isQuery.employeeName = res.data.realName;
                        console.log(this.isQuery.employeeName)
                        return false
                    } else {
                        return true
                    }
                })
            },
            open() {
                this.$alert(this.isQuery.employeeName + "已经在该项目中，请重新选择", '提示', {
                    confirmButtonText: '确定',
                });
                this.formItem.employeeIds1 = ""
                this.isQuery.employeeIds = ""

            }
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

