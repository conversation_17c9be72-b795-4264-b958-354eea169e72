<template>
  <div>
    <el-alert
        title="提示：填写时请确认填写信息，若要关闭活动请在编辑时把时间调整到今日日期前即可"
        type="warning"
        effect="dark">
    </el-alert>
    <divider></divider>
    <el-form :model="ruleForm" ref="ruleForm" label-width="180px" class="demo-ruleForm" :inline="false">

      <el-form-item label="活动名称" required>
        <el-col :span="8">
          <el-input v-model="ruleForm.activity.activityName"></el-input>
        </el-col>
      </el-form-item>

      <el-form-item label="活动时间" required>
        <el-col :span="8">
          <el-form-item prop="date1">
            <el-date-picker type="datetimerange" placeholder="选择日期" value-format="yyyy-MM-dd HH:mm:ss"
                            v-model="date1" style="width: 100%;"></el-date-picker>
          </el-form-item>
        </el-col>
      </el-form-item>
      <el-form-item label="每日抽奖次数" required>
        <el-col :span="8">
          <el-input-number v-model="ruleForm.activity.count"  label="描述文字"></el-input-number>
        </el-col>
      </el-form-item>
      <div class="inf1">
        <el-form-item label-width="600px" label="奖项中奖概率,如(10)%,剩余的概率即谢谢惠顾;因此设置的总概率加起来可以不用等于100%"></el-form-item>
        <el-form-item label="奖项一概率" required>
          <el-col :span="8">
            <el-input-number v-model="ruleForm.activityGradePays[0].orderPay"
                             label="描述文字"></el-input-number>
          </el-col>
        </el-form-item>
        <el-form-item label="奖项二概率" required>
          <el-col :span="8">
            <el-input-number v-model="ruleForm.activityGradePays[1].orderPay"
                             label="描述文字"></el-input-number>
          </el-col>
        </el-form-item>
        <el-form-item label="奖项三概率" required>
          <el-col :span="8">
            <el-input-number v-model="ruleForm.activityGradePays[2].orderPay"
                             label="描述文字"></el-input-number>
          </el-col>
        </el-form-item>
        <el-form-item label="奖项四概率" required>
          <el-col :span="8">
            <el-input-number v-model="ruleForm.activityGradePays[3].orderPay"
                             label="描述文字"></el-input-number>
          </el-col>
        </el-form-item>
        <el-form-item label="奖项五概率" required>
          <el-col :span="8">
            <el-input-number v-model="ruleForm.activityGradePays[4].orderPay"
                             label="描述文字"></el-input-number>
          </el-col>
        </el-form-item>
        <el-form-item label="活动规则图"  required>
            <activity-pic-content ref="zlgz"></activity-pic-content>
        </el-form-item></div>

      <div class="inf1">
        <el-form-item label="奖品对应设置(如果非实物请务必填写对应优惠券批次号)。描述图尺寸必须150*150.否则图标会异常" label-width="680px"></el-form-item>
       <el-form-item> <a href="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jz-icon/cj_zlsn.png">描述图图案例</a></el-form-item>
        <el-form-item label="奖项一名称" required>
          <el-col :span="6">
            <el-input v-model="ruleForm.activityGifts[0].winGradeName"></el-input>
          </el-col>

          <el-form-item label="奖项数量" required>
            <el-col :span="3">
              <el-input-number v-model="ruleForm.activityGifts[0].max"
                               label="描述文字"></el-input-number>
            </el-col>

            <label class="el-form-item__label">&emsp; &emsp;优惠券批次：</label>
            <el-col :span="4">
              <el-input v-model.number="ruleForm.activityGifts[0].couponId"
                        onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;描述图：</label>
            <el-col :span="4">
              <activity-pic-content ref="giftImg1"></activity-pic-content>
            </el-col>

          </el-form-item>
        </el-form-item>

        <el-form-item label="奖项二名称" required>
          <el-col :span="6">
            <el-input v-model="ruleForm.activityGifts[1].winGradeName"></el-input>
          </el-col>
          <el-form-item label="奖项数量" required>
            <el-col :span="3">
              <el-input-number v-model="ruleForm.activityGifts[1].max"
                               label="描述文字"></el-input-number>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;优惠券批次：</label>
            <el-col :span="4">
              <el-input v-model.number="ruleForm.activityGifts[1].couponId"
                        onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;描述图：</label>
            <el-col :span="4">
              <activity-pic-content ref="giftImg2"></activity-pic-content>
            </el-col>
          </el-form-item>
        </el-form-item>

        <el-form-item label="奖项三名称" required>
          <el-col :span="6">
            <el-input v-model="ruleForm.activityGifts[2].winGradeName"></el-input>
          </el-col>
          <el-form-item label="奖项数量" required>
            <el-col :span="3">
              <el-input-number v-model="ruleForm.activityGifts[2].max"
                               label="描述文字"></el-input-number>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;优惠券批次：</label>
            <el-col :span="4">
              <el-input v-model.number="ruleForm.activityGifts[2].couponId"
                        onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;描述图：</label>
            <el-col :span="4">
              <activity-pic-content ref="giftImg3"></activity-pic-content>
            </el-col>
          </el-form-item>
        </el-form-item>

        <el-form-item label="奖项四名称" required>
          <el-col :span="6">
            <el-input v-model="ruleForm.activityGifts[3].winGradeName"></el-input>
          </el-col>
          <el-form-item label="奖项数量" required>
            <el-col :span="3">
              <el-input-number v-model="ruleForm.activityGifts[3].max"
                               label="描述文字"></el-input-number>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;优惠券批次：</label>
            <el-col :span="4">
              <el-input v-model.number="ruleForm.activityGifts[3].couponId"
                        onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;描述图：</label>
            <el-col :span="4">
              <activity-pic-content ref="giftImg4"></activity-pic-content>
            </el-col>
          </el-form-item>
        </el-form-item>

        <el-form-item label="奖项五名称" required>
          <el-col :span="6">
            <el-input v-model="ruleForm.activityGifts[4].winGradeName"></el-input>
          </el-col>
          <el-form-item label="奖项数量" required>
            <el-col :span="3">
              <el-input-number v-model="ruleForm.activityGifts[4].max"
                               label="描述文字"></el-input-number>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;优惠券批次：</label>
            <el-col :span="4">
              <el-input v-model.number="ruleForm.activityGifts[4].couponId"
                        onkeyup="value=value.replace(/[^\d]/g,'')"></el-input>
            </el-col>
            <label class="el-form-item__label">&emsp; &emsp;描述图：</label>
            <el-col :span="4">
              <activity-pic-content ref="giftImg5"></activity-pic-content>
            </el-col>
          </el-form-item>
        </el-form-item>

      </div>

      <el-form-item>
        <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
        <el-button @click="resetForm('ruleForm')">重置</el-button>
      </el-form-item>
    </el-form>
  </div>

</template>

<script>
import moment from "moment";

const dataMap = {
  props: {},
  Set: function (key, value) {
    this.props[key] = value
  },
};

let giftData = {
  max: null,
  winGradeName: null,
  detail: null,
  couponId: null,
  type: 0,
}
let giftwindData = {
  orderPay: null,
}
let giftArr = [];
let windGradeArr = [];
for (let i = 0; i < 5; i++) {
  giftArr.push(JSON.parse(JSON.stringify(giftData)));
}
for (let i = 0; i < 5; i++) {
  windGradeArr.push(JSON.parse(JSON.stringify(giftwindData)));
}


import activityPicContent from "./activityPicContent.vue";
export default {
  name: "createActivityByGuessWord",
  components:{
    activityPicContent
  },
  created() {
    if (this.$route.query.id != null && this.$route.query.id !== '' && this.$route.query.id !== undefined) {
      this.ruleForm.activity.id = this.$route.query.id;
      this.getData(this.$route.query.id);
    }
  },
  data() {
    return {
      date1: [],
      ruleForm: {
        activityTypeName: 'jgg',
        activityType: 0,
        activity: {
          id: null,
          activityName: '',
          count: '',
          startTime: '',
          endTime: '',
          type: 0,
        },
        activityGradePays: windGradeArr,
        activityGifts: giftArr,
        dataMap: {},
      },

    };
  },

  methods: {
    submitForm(formName) {
      if (this.date1 != null && this.date1.length > 0) {
        this.ruleForm.activity.startTime = this.date1[0];
        this.ruleForm.activity.endTime = this.date1[1];
      } else {
        return this.$message.error("时间不能为空")
      }
      let d = this.ruleForm;
      if (d.activity.activityName === '' || d.activity.count === '') {
        return this.$message.error("请将信息补充完整-活动名称(次数)")
      }
      //活动中奖概率调整
      for (let i = 0; i < d.activityGradePays.length; i++) {

        if (d.activityGradePays[i].orderPay == null ||
            d.activityGradePays[i].orderPay === '' || d.activityGradePays[i].orderPay === undefined) {
          return this.$message.error("请将信息补充完整-中奖概率")
        }
      }
      //奖品内容校验
      for (let i = 0; i < d.activityGifts.length; i++) {
        if (!d.activityGifts[i].winGradeName  || !d.activityGifts[i].max
            || (d.activityGifts[i].type === 0 && !d.activityGifts[i].couponId)
        ) {
          return this.$message.error("请将信息补充完整-奖品内容")
        }
      }

      dataMap.Set("zl_gz",this.$refs.zlgz.pic);
      this.ruleForm.dataMap = dataMap.props;

      for (let i = 0; i < 5; i++) {
        if (this.$refs[`giftImg${i + 1}`].pic) {
          this.ruleForm.activityGifts[i].giftImg = this.$refs[`giftImg${i + 1}`].pic;
        }
      }




      console.info(JSON.stringify(this.ruleForm))

      this.$postData("createActivityByYX", this.ruleForm).then(res => {

        if (res.status === 200) {
          this.$message.success("创建成功");
          this.$router.push("/activityPageByYingXiao");
        } else {
          this.$message.error(res.data);
        }
      })

    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    getData(val) {
      this.$getData("initFriendlyData", {"id": val}).then(res => {

        if (res.status === 200) {
          this.ruleForm.activity = res.data.activity;
          this.ruleForm.activityGifts = res.data.activityGifts;
          this.ruleForm.activityGradePays = res.data.activityGradePays;
          this.date1 = [new Date(res.data.activity.startTime), new Date(res.data.activity.endTime)];
        } else {
          this.$message.error(res.data);
        }
      })
    }
  }
}
</script>

<style scoped>
.inf1 {
  border: 1px solid red;
}
</style>
