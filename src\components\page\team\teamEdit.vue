<template>
    <div class="table">

        <div class="container">
            <el-page-header @back="goBack" content="团队编辑"/>
            <el-divider></el-divider>
            <el-row :gutter="20">
                <el-col :span="20" >

                        <el-drawer
                                title="内容封面"
                                :visible.sync="coverDrawer"
                                direction="rtl"
                                size="60%">
                            <cmsMaterial type="edit"  @init-choose="initCover"></cmsMaterial>
                        </el-drawer>
                        <el-form :model="dom.team" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                            <el-row :gutter="20">
                                <el-col :span="12" >
                                    <el-form-item label="队伍名称" prop="name">
                                        <el-input v-model="dom.team.name"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12" >
                                    <el-form-item label="队长人员" prop="leaderName">
                                        <el-autocomplete
                                                style="width: 70%"
                                                v-model="dom.team.leaderName"
                                                :fetch-suggestions="querySearchAsync"
                                                placeholder="请输入内容"
                                                @select="handleSelect"
                                                :trigger-on-focus="false">
                                        </el-autocomplete>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="15" >
                                    <el-form-item label="队伍积分" prop="gradeNum">
                                        <el-slider v-model="dom.team.gradeNum"></el-slider>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24" >
                                    <el-form-item label="数据更新" prop="dayUp">
                                        <el-radio-group v-model="dom.team.dayUp">
                                            <el-radio :label=1>每日更新</el-radio>
                                            <el-radio :label=0>手动更新</el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24" >
                                    <el-form-item label="队伍头像" >
                                        <div @click="coverDrawer=true">
                                            <el-image
                                                    style="width: 100px; height: 150px;min-width: 150px;"
                                                    :src="dom.team.headImg" >
                                                <div slot="error" class="image-slot"  style="font-size: 25px;color: #ddd;padding: 50px;background: #f1f1f1;text-align: center;">
                                                    <i class="el-icon-plus"></i>
                                                </div>
                                            </el-image>
                                        </div>

                                    </el-form-item>
                                </el-col>
                                <el-col :span="24" >
                                    <el-drawer
                                            title="队员选择"
                                            :visible.sync="teamEmployeeDrawer"
                                            direction="rtl"
                                            size="60%">
                                        <employeeSelect :selectList="dom.selectList"  @init-choose="teamEmployee"></employeeSelect>
                                    </el-drawer>
                                    <el-form-item label="队员选择" >
                                        <el-badge :value="dom.selectList.length" class="item" type="primary">
                                            <el-button type="primary" @click="teamEmployeeDrawer=true">队员管理</el-button>
                                        </el-badge>

                                    </el-form-item>
                                </el-col>
                                <el-col :span="24" >
                                    <el-form-item label="追踪参数" prop="type">
                                        <el-checkbox-group v-model="dom.selectParamList">

                                            <el-checkbox :label="item.id" name="type" border v-for="(item,index) in paramList" :key="index">{{item.paramName}}</el-checkbox>

                                        </el-checkbox-group>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24" >
                                    <el-form-item label="队伍类型" prop="resource">
                                        <el-radio-group v-model="dom.team.type">
                                            <el-radio :label="item.id"  v-for="(item,index) in types" :key="index">{{item.name}}</el-radio>

                                        </el-radio-group>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24" >
                                    <el-form-item label="队伍介绍" prop="detail">
                                        <el-input v-model="dom.team.detail" type="textarea" maxlength="150"
                                                  :autosize="{ minRows: 4}"
                                                  show-word-limit></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12" >
                                    <el-form-item label="经度" prop="lng">
                                        <el-input v-model="dom.team.lng" >
                                            <template slot="append">lng</template>
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12" >
                                    <el-form-item label="纬度" prop="otherCosts">
                                        <el-input v-model="dom.team.lat"  >
                                            <template slot="append">lat</template>
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12" >
                                    <el-form-item label="面积" prop="areaNum">
                                        <el-input v-model="dom.team.areaNum" type="number" >
                                            <template slot="append">平方</template>
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12" >
                                    <el-form-item label="租金" prop="rentNum">
                                        <el-input v-model="dom.team.rentNum" type="number" >
                                            <template slot="append">元/月</template>
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12" >
                                    <el-form-item label="其他成本" prop="otherCosts">
                                        <el-input v-model="dom.team.otherCosts" type="number" >
                                            <template slot="append">元/月</template>
                                        </el-input>
                                    </el-form-item>
                                </el-col>


                            </el-row>




                            <el-form-item>
                                <el-button type="primary" @click="submitForm('ruleForm')">立即{{dom.team.id==null?'创建':'保存'}}</el-button>
                                <el-button @click="resetForm('ruleForm')">重置</el-button>
                                <el-button @click="dom.team.status=0,submitForm('ruleForm')" type="danger" v-if="dom.team.id!==null">删除</el-button>
                            </el-form-item>
                        </el-form>


                </el-col>

            </el-row>

        </div>
    </div>
</template>

<script>
    import cmsMaterial from "../cms/cmsMaterial";
    import employeeSelect from "./common/employeeSelect";
    export default {
        props:['model'],
        name: "teamEdit",
        components: {
            cmsMaterial,employeeSelect
        },
        data(){
            return{
                types:[
                    {
                        id:1,
                        name:"新零售"
                    },
                    {
                        id:2,
                        name:"消费者"
                    },
                    {
                        id:3,
                        name:"大客户"
                    },
                    {
                        id:4,
                        name:"一线"
                    },
                ],
                id:this.$route.query.id,
                paramList:[],
                filterText:null,
                coverDrawer: false,
                collapse: false,
                teamEmployeeDrawer:false,

                dom:{
                    selectList:[],
                    selectParamList:[],
                    team:{
                        id:null,
                        name:null,
                        headImg:null,
                        detail:null,
                        cityId:null,
                        areaId:null,
                        address:null,
                        leaderId:null,
                        leaderName:null,
                        lat:null,
                        lng:null,
                        dayUp:1,
                        status:null,
                        creatDate:null,
                        gradeNum:null,
                        type:1,
                        areaNum:0,
                        otherCosts:0,
                        rentNum:0,
                    }

                },
                menuTreeData:null,
                rules: {
                    name: [
                        {required: true, message: '请输入名称', trigger: 'blur'},
                        {min: 2, max: 15, message: '长度在 2 到 15 个字符', trigger: 'blur'}
                    ],
                    leaderName: [
                        {required: true, message: '请输入名称', trigger: 'blur'},
                    ],
                    detail: [
                        {required: true, message: '请输入介绍', trigger: 'blur'},
                        {min: 2, max: 150, message: '长度在 2 到 150 个字符', trigger: 'blur'}
                    ],
                }
            }
        },
        created(){
            this.teamParamList()
            if (this.id!==null && this.id!==undefined){
                this.getTeamSaveDto()
            }
        },
        methods:{
            goBack() {
                this.$router.go(-1)
            },
            getTeamSaveDto(){
                this.$getData("getTeamSaveDto", {id:this.id}).then(res => {
                    if (res.status == 200) {
                        this.dom = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            teamParamList(){
                this.$getData("teamParamList", {}).then(res => {
                    if (res.status == 200) {
                        this.paramList = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            teamEmployee(list){
                this.dom.selectList=list
                this.teamEmployeeDrawer=false
            },
            handleSelect(item) {
                this.dom.team.leaderId=item.name;


            },
            createStateFilter(queryString) {
                return (state) => {
                    return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                };
            },
            querySearchAsync(queryString, cb) {
                this.restaurants=[]

                let baomuModel={
                    realName:this.dom.team.leaderName
                }
                this.$postData("getEmployeeSelectDtoByName", baomuModel, {}).then(res => {
                    if (res.status == 200) {
                        this.list=[]
                        this.list = res.data;
                        this.list.forEach((item, index, arr) => {
                            var a={}
                            a.value=item.realName;
                            a.name=item.id;
                            this.restaurants.push(a);
                        });

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
                var restaurants = this.restaurants;
                var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants;
                cb(restaurants);
            },
            chooseThisModel(name) {
                this.$emit('init-choose', name);
            },
            initCover(material){
                this.dom.team.headImg=material.url
                this.coverDrawer=false
            },
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        console.log(this.dom)
                        this.$postData("saveTeamSaveDto", this.dom, {}).then(res => {
                            if (res.status == 200) {
                                this.$message.success("保存成功" );
                                if (this.id!==null && this.id!==undefined){

                                }else {
                                    this.resetForm('ruleForm')
                                }

                            } else {
                                this.$message.error("保存失败。请稍后重试");
                            }
                        })
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            resetForm(formName) {
                this.$refs[formName].resetFields();
            },

        }
    }
</script>

<style scoped>
    .act-node{
        color: #1677FF;
    }
</style>
