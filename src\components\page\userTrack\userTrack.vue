<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">
                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="订单编号">
                                <el-input
                                        clearable
                                        v-model="dto.billNo"
                                        placeholder="订单编号"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="5">
                            <el-form-item label="所属门店">
                                <Select filterable style="width: 150px" v-model="dto.storeId">
                                    <Option value="">请选择</Option>
                                    <Option v-for="item in storeList" :value="item.id">{{ item.storeName}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="5">
                            <el-form-item label="所属部门">
                                <Select filterable style="width: 150px" v-model="dto.departmentId">
                                    <Option value="">请选择</Option>
                                    <Option v-for="item in departList" :value="item.id">{{ item.name}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="服务项目">
                                <Select filterable style="width: 120px" @on-change="changeValue" clearable
                                        v-model="dto.productCategoryId">
                                    <Option value="0">请选择</Option>
                                    <Option v-for="item in options" :value="item.id" :key="item.id">{{item.name}}
                                    </Option>
                                </Select>
                                <Select filterable style="width: 120px" v-model="dto.productId" clearable>
                                    <Option value="">请选择</Option>
                                    <Option v-for="item in options_children" :value="item.value" :key="item.value">
                                        {{item.text}}
                                    </Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="服务员工">
                                <el-input
                                        v-model="dto.serviceName"
                                        placeholder="支持员工号/姓名"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="上传者">
                                <Select filterable style="width: 120px" @on-change="query" clearable
                                        v-model="dto.uploader">
                                    <Option value="">请选择</Option>
                                    <Option value="1">客户</Option>
                                    <Option value="2">员工</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="传播渠道">
                                <Select filterable style="width: 120px" @on-change="query" clearable
                                        v-model="dto.channel">
                                    <Option value="">请选择</Option>
                                    <Option v-for="item in channelList" :value="item.id">{{item.text}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" style="padding-left: 1rem">
                            <el-form-item label="上传时间">
                                <el-date-picker
                                        v-model="dto.startTime"
                                        type="datetime"
                                        placeholder="上传开始时间"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        style="width:180px"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" style="padding-left: 1rem">
                            <el-form-item label="至">
                                <el-date-picker
                                        v-model="dto.endTime"
                                        type="datetime"
                                        placeholder="上传结束时间"
                                        style="width:180px"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <!--<el-col :span="5">-->
                            <!--<el-form-item label="是否有抖音号">-->
                                <!--<el-radio-group v-model="dto.dyNum" @change="query()">-->
                                    <!--<el-radio-button :label="1">是</el-radio-button>-->
                                    <!--<el-radio-button :label="2">否</el-radio-button>-->
                                <!--</el-radio-group>-->
                            <!--</el-form-item>-->
                        <!--</el-col>-->

                        <!--<el-col :span="5">-->
                            <!--<el-form-item label="是否点赞">-->
                                <!--<el-radio-group v-model="dto.spot" @change="query()">-->
                                    <!--<el-radio-button :label="true">是</el-radio-button>-->
                                    <!--<el-radio-button :label="false">否</el-radio-button>-->
                                <!--</el-radio-group>-->
                            <!--</el-form-item>-->
                        <!--</el-col>-->

                        <!--<el-col :span="4">-->
                            <!--<el-form-item label="是否推荐">-->
                                <!--<el-radio-group v-model="dto.follow" @change="query()">-->
                                    <!--<el-radio-button :label="true">是</el-radio-button>-->
                                    <!--<el-radio-button :label="false">否</el-radio-button>-->
                                <!--</el-radio-group>-->
                            <!--</el-form-item>-->
                        <!--</el-col>-->
                        <!--<el-col :span="5">-->
                        <!--<el-form-item label="是否有图片">-->
                        <!--<el-radio-group v-model="dto.recommend" @change="query()">-->
                        <!--<el-radio-button :label="true">是</el-radio-button>-->
                        <!--<el-radio-button :label="false">否</el-radio-button>-->
                        <!--</el-radio-group>-->
                        <!--</el-form-item>-->
                        <!--</el-col>-->
                    </el-row>
                    <el-row>

                    </el-row>
                    <el-row>
                        <el-col :span="24" style="text-align: left">
                            <el-form-item style="float: right">
                                <el-button type="primary" @click="query()">搜索</el-button>
                                <el-button type="success" :loading="loadingExcel" @click="exportExcel">导出Excel
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-table :data="list" border class="table" ref="multipleTable"
                      v-loading="loading">
                <!--<el-table-column-->
                <!--type="selection"-->
                <!--width="55">-->
                <!--</el-table-column>-->
                <el-table-column
                        prop="billNo"
                        width="140"
                        label="订单编号">
                    <template slot-scope="scope">
                        <a style="text-decoration:underline;"
                           :href="url+scope.row.billNo" target="_Blank">{{scope.row.billNo}}</a>
                    </template>
                </el-table-column>
                <el-table-column
                        width="90"
                        prop="store.storeName"
                        :show-overflow-tooltip="true"
                        label="所属门店"
                ></el-table-column>
                <el-table-column
                        width="90"
                        prop="department.name"
                        :show-overflow-tooltip="true"
                        label="所属部门"
                ></el-table-column>
                <el-table-column
                        width="100"
                        prop="order.productName"
                        :show-overflow-tooltip="true"
                        label="服务项目"
                ></el-table-column>
                <el-table-column
                        width="130"
                        prop="employeeNo"
                        label="员工工号">
                    <template slot-scope="scope">
                        <p style="font-size: 12px" v-for="item in scope.row.orderWaiterList">
                            <span v-if="item.serviceNo!=null&&item.isFlag===true">
                                 {{item.serviceNo}} ({{item.serviceName}})
                            </span>
                            <span v-else-if="item.carCode!=null&&item.isFlag===true">
                                 {{item.carCode}} ({{item.serviceName}})
                            </span>
                        </p>
                    </template>
                </el-table-column>
                <el-table-column
                        width="140"
                        prop="dyNum"
                        label="上传者">
                    <template slot-scope="scope">
                        <span v-if="scope.row.uploader===1&&scope.row.member!=null">
                            会员：{{scope.row.member.account}}
                        </span>
                        <span v-if="scope.row.uploader===2&&scope.row.employee!=null">
                            员工：{{scope.row.employee.no}}{{scope.row.employee.realName}}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                        width="80"
                        prop="channelDesc"
                        label="传播渠道"
                ></el-table-column>
                <el-table-column
                        width="150"
                        prop="createTime"
                        label="上传时间"
                ></el-table-column>
                <el-table-column
                        width="80"
                        prop=""
                        label="是否点赞">
                    <template slot-scope="scope">
                        <span>{{scope.row.spot===true?"是":"否"}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        width="80"
                        prop=""
                        label="是否推荐">
                    <template slot-scope="scope">
                        <span>{{scope.row.recommend===true?"是":"否"}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        width="300"
                        label="操作">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="edit(scope.row)" type="primary">查看</el-button>
                        <el-button size="mini" :disabled="scope.row.orderTs" @click="tsSave(scope.row)" type="primary">
                            投诉
                        </el-button>
                        <el-button size="mini" :disabled="scope.row.recommend" @click="update(scope.row)" type="primary">
                            推荐
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :current="this.dto.pageNum"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="10"/>
            </div>
            <Modal v-model="appeal" class="Modal" :width="screenWidth" title="查看"
                   :mask-closable="false"
                   @on-cancel="getData()">
                <div class="addBody">
                    <User-track-choose v-if="appeal" @init-choose="initChooseProject" :userTrackId="userTrackId"
                                       @close-modal="closeCurrModal"></User-track-choose>
                </div>
                <div slot="footer">
                </div>
            </Modal>
        </div>
    </div>
</template>
<script>
    import UserTrackChoose from '@/components/page/userTrack/userTrackChoose.vue'

    export default {
        data() {
            return {
                loading: true,
                loadingExcel: false,
                userTrackId: null,
                screenWidth: '60%',
                appeal: false,
                list: null,
                url: "https://yun.xiaoyujia.com/Account/TokenLogin?Token=" + localStorage.getItem('token') + "&returnUrl=/order/baseinfo?BillNo=",
                pageSizeOpts: [10, 20, 30],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                dto: {
                    uploader: "",
                    billNo: null,
                    storeId: null,
                    pageSize: 10,
                    pageNum: 1,
                    departmentId: null,
                    productCategoryId: null,
                    productId: null,
                    serviceName: null,
                    dyNum: null,
                    spot: null,
                    recommend: null,
                    follow: null,
                    channel: "",
                },
                options: [], //服务项目一级
                options_children: [], //服务项目二级,
                storeList: [],
                departList: [],
                channelList:[]
            };
        },
        components: {
            'UserTrackChoose': UserTrackChoose
        },
        created() {
            this.getData();
            this.getProduct();
            this.getStore();
            this.getDepart();
            this.getChannelList();
        },
        computed: {},
        methods: {
            getChannelList() {
                this.$postUrl("get_dict", 150, null, {}).then(res => {
                    if (res.status === 200) {
                        this.channelList = res.data
                    }
                })
            },
            getDepart() {
                this.$postData("depart_list", {}).then(res => {
                    this.departList = res.data;
                });
            },
            getStore() {
                this.$postData("store_getByList", {}, {}).then(res => {
                    if (res.status === 200) {
                        this.storeList = res.data;
                    }
                });
            },

            getProduct() {
                this.$getData("category_getByRoleId").then(res => {
                    this.options = res.data;
                });
            },
            changeValue(value) {
                if (value != null) {
                    this.$getData("getProductName", {productCategoryId: value}).then(
                        res => {
                            this.options_children = res.data;
                        }
                    );
                }
            },
            getData(data) {
                this.$postData("userTrackList", this.dto, {}).then(res => {
                    this.loading = false;
                    if (res.status === 200) {
                       ;
                        this.list = res.data.list;
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    }
                });
            },
            edit(res) {
                this.userTrackId = res.id;
                this.appeal = true;
            },

            update(dto) {
                this.$confirm('是否确认推荐该订单的员工？, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    dto.recommend = true;
                    this.$postData("userTrack_updateOne", dto).then(res => {
                        if (res.status === 200) {
                            this.getData();
                        }
                    })
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消推荐'
                    });
                });

            },
            tsSave(dto) {
                this.$confirm('是否确认投诉该订单的员工？, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let tsOrder = {
                        isTs: 0,
                        billNo: dto.billNo,
                        createTime: this.dateToString(),
                        dealNo: localStorage.getItem("account"),
                        state: 1
                    };
                    this.$postData("tsSave", tsOrder).then(res => {
                        if (res.status === 200) {
                            dto.orderTs = true;
                            this.$postData("userTrack_updateOne", dto).then(res => {
                                if (res.status === 200) {
                                    this.getData();
                                }
                            })
                        }
                    })
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消投诉'
                    });
                });

            },
            dateToString() {
                let date = new Date();
                let year = date.getFullYear();
                let month = (date.getMonth() + 1).toString();
                let day = (date.getDate()).toString();
                let hours = (date.getHours()).toString(); //获取系统时，
                let min = (date.getMinutes()).toString();
                let second = (date.getSeconds()).toString();
                if (month.length === 1) {
                    month = "0" + month;
                }
                if (day.length === 1) {
                    day = "0" + day;
                }
                if (hours.length === 1) {
                    hours = "0" + hours;
                }
                if (min.length === 1) {
                    min = "0" + min;
                }
                if (second.length === 1) {
                    second = "0" + second;
                }
                return year + "-" + month + "-" + day + " " + hours + ":" + min + ":" + second;
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.appeal = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
                this.getData()
            },
            query() {
                this.loading = true;
                this.dto.pageNum = 1;
                this.getData();
            },
            // 页码大小
            onPageSizeChange(size) {
                this.loading = true;
                this.dto.pageSize = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                this.loading = true;
                this.dto.pageNum = index;
                this.getData();
            },
            exportExcel() {
                this.loadingExcel = true;
                this.$postData("userTrack_exportList", this.dto, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.loadingExcel = false;
                    this.blobExport({
                        tablename: "服务跟踪",
                        res: res
                    });
                });
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
