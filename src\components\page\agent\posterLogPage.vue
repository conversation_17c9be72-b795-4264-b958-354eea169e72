<template>
    <div class="table">
        <div class="container">
            <el-form ref="form">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="推广海报">
                            <el-input
                                    clearable
                                    v-model="form.posterName"
                                    placeholder="推广海报"
                                    style="width:200px"
                                    class="handle-input mr10"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="推广人">
                            <el-input
                                    clearable
                                    v-model="form.realName"
                                    placeholder="推广人"
                                    style="width:200px"
                                    class="handle-input mr10"
                            ></el-input>
                        </el-form-item>
                    </el-col>

                                        <el-col :span="16">
                                            <el-form-item label="操作时间">
                                                <el-date-picker
                                                        v-model="showDate"
                                                        type="datetimerange"
                                                        :picker-options="pickerOptions"
                                                        range-separator="至"
                                                        start-placeholder="开始日期"
                                                        end-placeholder="结束日期"
                                                        value-format="yyyy-MM-dd HH:mm:ss"
                                                        align="right">
                                                </el-date-picker>
                                            </el-form-item>
                                        </el-col>

                    <el-col :span="8" >

                        <el-form-item>
                            <el-button type="success"  round @click="form.current=1,getData()">搜索</el-button>
                        </el-form-item>

                    </el-col>
                </el-row>
            </el-form>
            <br>

            <el-tabs type="border-card">
                <el-tab-pane :label="'扫码记录 ( 共'+pageInfo.total+'条 )'">
                    <el-table :data="logList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable" @cell-dblclick="">
                        <el-table-column
                                type="selection"
                                width="40">
                        </el-table-column>

                        <el-table-column
                                prop="creatTime"
                                label="创建时间"
                                width="180"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="realName"
                                label="推广人"
                                width="100"
                        ></el-table-column>
                        <el-table-column
                                prop="posterName"
                                label="推广海报"
                                width="100">
                        </el-table-column>
                        <el-table-column
                                prop="toUrl"
                                label="推广链接"
                        >
                        </el-table-column>


                        <!--                <el-table-column-->
                        <!--                        label="操作"-->
                        <!--                        fixed="right"-->
                        <!--                        min-width="180">-->
                        <!--                    <template slot-scope="scope">-->
                        <!--                        <el-button-->
                        <!--                                v-if="scope.row.status==1"-->
                        <!--                                @click.native.prevent="showRow(scope.row)"-->
                        <!--                                type="text"-->
                        <!--                                size="small">-->
                        <!--                            查看详情-->
                        <!--                        </el-button>-->
                        <!--                    </template>-->

                        <!--                </el-table-column>-->
                    </el-table>
                    <div class="pagination">
                        <Page :total="pageInfo.total"
                              @on-change="onChange"
                              :show-total="true"
                              :show-sizer="true"
                              :page-size-opts="pageSizeOpts"
                              @on-page-size-change="onPageSizeChange"
                              :page-size="pageInfo.size"/>
                    </div>
                </el-tab-pane>
                <el-tab-pane :label="'扫码次数 (共 '+logNumList.length+' )'">
                    <el-row>
                    <div v-for="(item,index) in logNumList" :key="index">
                        <el-col :span="8" class="num-box">
                            <div>推广海报：{{item.posterName}}</div>
                            <div>推广人：{{item.realName}}</div>
                            <div>推广数量：<span style="color: red">{{item.num}}</span></div>
                        </el-col>
                    </div>
                    </el-row>

                </el-tab-pane>
            </el-tabs>

        </div>

    </div>
</template>

<script>
    export default {
        name: "posterLogPage",
        data() {
            return {
                showDate:null,
                responseInfoModal:false,
                show:null,
                logList:[],
                logNumList:[],
                pageSizeOpts:[10,20,30],
                multipleSelection: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                form: {
                    realName:null,
                    storeName:null,
                    posterName:null,
                    roleId:null,
                    employeeId:null,
                    title:null,
                    search:null,
                    employeeName:null,
                    status:null,
                    current: 1,
                    size: 10,
                    startTime:null,
                    endTime:null,
                },
                user:{
                    id:localStorage.getItem("id"),
                    storeId:localStorage.getItem("storeId"),
                    roleId:localStorage.getItem("roleId"),
                },
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
            }
        },

        created(){
            this.getData();
        },
        methods:{

            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.form.size = size;
                this.getData();
            },
            onChange(index) {
                console.log(index)
                this.form.current = index;
                this.getData();
            },
            getData() {
                if (this.user.roleId!=='1'){
                    if (this.user.roleId !=='66'){
                        this.form.employeeId=this.user.id
                    }else {
                        this.form.storeId=this.user.storeId
                    }
                }
                if (this.showDate!=null){
                    this.form.startTime=this.showDate[0]
                    this.form.endTime=this.showDate[1]
                }else {
                    this.form.startTime=null
                    this.form.endTime=null
                }
                this.$postData("posterLogPage", this.form, {}).then(res => {
                    if (res.status == 200) {
                        this.logList = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                        this.posterLogNum()
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            posterLogNum() {
                if (this.user.roleId!=='1'){
                    if (this.user.roleId !=='66'){
                        this.form.employeeId=this.user.id
                    }else {
                        this.form.storeId=this.user.storeId
                    }
                }
                this.$postData("posterLogNum", this.form, {}).then(res => {
                    if (res.status == 200) {
                        this.logNumList = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
        }

    }
</script>

<style scoped>
    .num-box{
        padding: 10px;
        border: 1px solid #ddd;
    }
</style>
