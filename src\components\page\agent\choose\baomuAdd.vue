<template style="background-color: #000">

            <el-tabs type="card" :stretch="true" >
                <el-tab-pane label="基本信息">
                <Row>
                <Col span="24">

                        <el-form ref="dom" :model="dom" :rules="ruleValidate">
                            <Row>
                            <Col span="6" style="padding: 50px">

                                <div style="padding-left: 30%;padding-bottom: 10px"><h3>员工头像</h3></div>

                                <el-dialog :visible.sync="dialogImg">
                                    <img width="100%" :src="dom.headPortrait" alt="" >
                                </el-dialog>
                                <el-image
                                        @click="dialogImg=true"
                                        style="width: 100%; height: 200px;min-width: 150px;"
                                        :src="dom.headPortrait"
                                >
                                </el-image>
                                <el-upload
                                        class="upload-demo"
                                        action="https://biapi.xiaoyujia.com/files/uploadFiles"
                                        list-type="picture"
                                        :on-success="handleAvatarSuccess">
                                    <el-button size="small" type="primary">更改头像</el-button>
                                </el-upload>

                            </Col>
                            <Col span="8">

                                    <el-form-item   prop="no" label="编号:">
                                        <el-input placeholder="请输入编号" v-model="dom.no" style="width: 70%">
                                        </el-input>
                                    </el-form-item >

                                    <el-form-item   prop="realName" label="名称:">
                                        <el-input placeholder="请输入名称" v-model="dom.realName" style="width: 70%">
                                        </el-input>
                                    </el-form-item >

                                    <el-form-item   prop="address" label="地址:">
                                        <el-input placeholder="请输入地址" v-model="dom.address" style="width: 70%">
                                        </el-input>
                                    </el-form-item >

                                    <el-form-item   prop="phone" label="手机:">
                                        <el-input placeholder="请输入联系方式" v-model="dom.phone" style="width: 70%">
                                        </el-input>
                                    </el-form-item >
                                <el-form-item   prop="phone" label="介绍人工号:">
                                    <el-input placeholder="请输入介绍人工号" v-model="dom.introducer" style="width: 60%">
                                    </el-input>
                                </el-form-item >

                            </Col>
                                <Col span="8">

                                <el-form-item   prop="shimingState" label="认证状态:">
                                    <el-select v-model="dom.shimingState" clearable placeholder="请选择" style="width: 70%">
                                        <el-option
                                                v-for="item in shimingStateoptions"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item >
                                <el-form-item   prop="baomuWorkType" label="工作方式:">
                                    <el-select v-model="baomuWorkType" multiple placeholder="请选择" style="width: 70%">
                                        <el-option
                                                v-for="item in workTypeOptions"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item >
                                <el-form-item   prop="state" label="员工状态:">
                                    <el-select v-model="dom.state" clearable placeholder="请选择" style="width: 70%">
                                        <el-option
                                                v-for="item in states"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item >
                                    <div class="label-name">城市区域:</div>
                                    <city-choose @init-choose="initChooseProject" :model="dom"></city-choose>
                            </Col>
                                <Col span="16">

                                </Col>

                            </Row>
                                    <div style="margin-right: 100px;float: right">
                                        <Button type="primary" @click="save('dom')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </div>
                                </el-form>

                    </Col>

            </Row>
                </el-tab-pane>

            </el-tabs>

</template>


<script>
    import cityChoose from '@/components/page/agent/minichoose/cityChoose.vue'
    export default {
         data() {
            return {
                baomuWorkType:[],
                workTypeOptions:[{
                    value:'育儿嫂',
                    label: '育儿嫂'
                }, {
                    value: '家务保姆',
                    label: '家务保姆'
                }, {
                    value: '护工',
                    label: '护工'
                }, {
                    value: '保姆',
                    label: '保姆'
                }, {
                    value: '纯打扫',
                    label: '纯打扫'
                }, {
                    value:'纯做饭',
                    label: '纯做饭'
                },],
                states:[{
                    value:null,
                    label: '全部'
                },{
                    value:1,
                    label: '上架'
                }, {
                    value: 2,
                    label: '下架'
                }, {
                    value: 3,
                    label: '离职'
                },],
                dialogImg:false,
                shimingStateoptions:[{
                    value:0,
                    label: '未通过'
                }, {
                    value: 1,
                    label: '已通过'
                },],
                dom:{
                    state:null,
                    shimingState:null,
                    cityId:null,
                    areaId:null,
                    headPortrait:null,
                    no:null,
                    realName:null,
                    address:null,
                    phone:null,
                    //introducer:localStorage.getItem("id")
                    introducer:null
                },
                formItem: {
                    No: null,
                    RealName: null,
                    Phone: null,
                    Address:null,
                },
                listCer:[],
                ruleValidate: {
                    no: [
                        {required: true, message: '请输入编号', trigger: 'change'}
                    ],
                    realName: [
                        {required: true, message: '请选择名称', trigger: 'change'}
                    ],
                    phone: [
                        {required: true, message: '请选择联系方式', trigger: 'change'}
                    ],
                    address: [
                        {required: true, message: '请输入地址', trigger: 'blur'}
                    ],

                },
            }
        },
        created: function () {
            console.log(localStorage.getItem("id"))

        },
        components: {
            'cityChoose': cityChoose,
        },
        methods: {

            handleAvatarSuccess(res, file) {
                this.dom.headPortrait = res.data;
            },
            save(name) {
                this.dom.baomuWorkType=null,
                    this.baomuWorkType.forEach(v=>{
                        if ( this.dom.baomuWorkType==null){
                            this.dom.baomuWorkType=v
                        }else {
                            this.dom.baomuWorkType=this.dom.baomuWorkType+","+v
                        }
                    });
                this.$refs[name].validate(valid => {
                    if (valid) {
                        if (this.dom.phone.length!=11){
                            return  this.$message.error("手机号长度错误");
                        }

                        this.$postData("add_baomu", this.dom, {}).then(res => {
                            if (res.status == 200) {
                                this.$Message.success('修改成功');
                                this.chooseThisModel();
                            } else {
                                this.$message.error("修改失败，" + res.msg);
                            }
                        })
                    }else{
                        console.log("error submit!!");
                        return false;
                    }
                });

            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            },
            /*
            城市选择
             */
            initChooseProject(city) {
                this.dom.cityId=city.cityId
                this.dom.areaId=city.areaId
                console.log(this.dom)
            },

        },

    }
</script>
<style>
    {
        background: #409eff;
        color: #fff;
    }
</style>

<style scoped>
    .label-name{
        float: left;
        font-size: 16px;
        text-align: center;
        width: 22%;
    }
    .startWord{
        background-color: #F5F7FA;
        color: #909399;
        vertical-align: middle;

        position: relative;
        border: 1px solid #DCDFE6;
        border-radius: 4px;
        padding: 0 20px;
        width: 1px;
        white-space: nowrap;
        float: left;
        padding-right: 50px;
    }
    .ivu-col-span-9{
        padding-top: 30px;
    }
    .el-input-group{
        width: 80%;
    }
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

