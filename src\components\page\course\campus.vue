<template>
	<div class="container">
		<el-menu :default-active="choiceIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect"
			style="margin: 0 0 30px 0;">
			<el-menu-item index="0">校区管理</el-menu-item>
			<el-menu-item index="1">校区审批</el-menu-item>
		</el-menu>

		<div v-if="choiceIndexNow==0">
			<div class="handle-box" style="margin: 20px 20px 20px 0px;">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="3">
							<el-form-item label="搜索" style="margin-right: 20px;">
								<el-input v-model="quer.search" placeholder="请输入校区名等"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="状态" style="margin-right: 20px;">
								<el-select v-model="quer.campusState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="8" style="margin: 32px 0;">
							<el-button type="primary" @click="query()" icon="el-icon-search">搜索</el-button>
							<el-button icon="el-icon-refresh" @click="reset()">重置</el-button>
							<!-- 		<el-button type="primary" icon="el-icon-edit" @click="openModal(0,0)"
							style="margin-left: 20px">新增</el-button> -->
						</el-col>
					</el-row>

				</el-form>
			</div>

			<el-table :data="list" v-loading="loading" border
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys">
				<!-- 展开列-审批内容详情 -->
				<!-- 				<el-table-column type="expand">
					<template slot-scope="scope">
						<el-col :span="48" style="margin: 0 0 20px 0">
							<h2>审批内容详情</h2>
						</el-col>
						<el-col :span="48" style="margin: 0 0 20px 20px;float: left;display: flex;">
							<div style="display: inline-block;margin-right: 20px;"
								v-for="(item,index) in scope.row.campusApprovalImgList" :key="index">
								<img :src="item.imgUrl||blankImg" @click="openImg(item.imgUrl)"
									style="width:60px;height: 60px;display: inline-block;" />
							</div>
						</el-col>
					</template>
				</el-table-column> -->

				<el-table-column prop="id" width="60" label="编号">
				</el-table-column>
				<el-table-column prop="campusName" width="100" label="校区名">
				</el-table-column>
				<el-table-column prop="campusContent" width="100" label="校区介绍">
					<template slot-scope="scope">
						<span>{{scope.row.campusContent||'暂无'}}</span>
					</template>
				</el-table-column>
				<el-table-column prop="campusRemark" width="100" label="校区备注">
					<template slot-scope="scope">
						<span>{{scope.row.campusRemark||'暂无'}}</span>
					</template>
				</el-table-column>
				<el-table-column prop="storeName" width="120" label="门店">
					<template slot-scope="scope">
						<span>{{scope.row.storeName||'暂未绑定'}}</span>
					</template>
				</el-table-column>
				<el-table-column prop="campusAddress" width="120" label="地址">
				</el-table-column>
				<el-table-column prop="campusManagerName" width="100" label="管理者">
					<template slot-scope="scope">
						<span
							v-if="scope.row.campusManagerName">{{scope.row.campusManagerName}}（{{scope.row.campusManager}}）</span>
						<span v-if="!scope.row.campusManagerName">{{scope.row.campusManager||'-'}}</span>
					</template>
				</el-table-column>
				<el-table-column width="102" label="类型">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.campusType == 0" type="success">灵活</el-tag>
						<el-tag v-if="scope.row.campusType == 1" type="info">固定</el-tag>
					</template>
				</el-table-column>
				<el-table-column width="102" label="状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.campusState == 0" type="info">下架</el-tag>
						<el-tag v-if="scope.row.campusState == 1" type="success">上架</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="principalName" width="100" label="校长姓名">
				</el-table-column>
				<el-table-column prop="principalPhone" width="100" label="校长电话">
				</el-table-column>
				<el-table-column prop="directorName" width="100" label="负责人姓名">
				</el-table-column>
				<el-table-column prop="directorPhone" width="100" label="负责人电话">
				</el-table-column>
				<el-table-column prop="lecturerName" width="100" label="讲师姓名">
				</el-table-column>
				<el-table-column prop="lecturerPhone" width="100" label="讲师电话">
				</el-table-column>
				<el-table-column width="140" prop="createTime" label="创建时间" sortable>
				</el-table-column>
				<el-table-column fixed="right" min-width="180" label="操作">
					<template slot-scope="scope">
						<el-button @click="openModal(3,scope.$index)" type="success" size="small" icon="el-icon-edit">修改
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<div v-if="choiceIndexNow==1">
			<div class="handle-box">
				<el-form ref="form" :model="pageInfo">
					<el-row>
						<el-col :span="3">
							<el-form-item label="搜索" style="margin-right: 20px;">
								<el-input v-model="quer.search" placeholder="请输入姓名等"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="状态">
								<el-select v-model="quer.approvalState">
									<el-option label="" value=""></el-option>
									<el-option v-for="(item,index) in stateList1" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="6">
							<el-button type="primary" @click="query()" icon="el-icon-search"
								style="margin-left: 20px">搜索</el-button>
							<el-button icon="el-icon-refresh" @click="reset()" style="margin-left: 20px">重置</el-button>
							<!-- 		<el-button type="primary" icon="el-icon-edit" @click="openModal(0,0)"
							style="margin-left: 20px">新增</el-button> -->
						</el-col>
					</el-row>

				</el-form>
			</div>

			<el-table :data="list" v-loading="loading" border
				:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys">
				<!-- 展开列-审批内容详情 -->
				<el-table-column type="expand">
					<template slot-scope="scope">
						<el-col :span="48" style="margin: 0 0 20px 0">
							<h2>审批内容详情</h2>
						</el-col>
						<el-col :span="48" style="margin: 0 0 20px 20px;float: left;display: flex;">
							<div style="display: inline-block;margin-right: 20px;"
								v-for="(item,index) in scope.row.campusApprovalImgList" :key="index">
								<img :src="item.imgUrl||blankImg" @click="openImg(item.imgUrl)"
									style="width:60px;height: 60px;display: inline-block;" />
							</div>
						</el-col>
					</template>
				</el-table-column>

				<el-table-column prop="id" width="60" label="编号">
				</el-table-column>
				<el-table-column prop="approvalName" width="100" label="校区名">
				</el-table-column>
				<el-table-column prop="storeName" width="100" label="门店">
				</el-table-column>
				<el-table-column prop="campusAddress" width="100" label="地址">
				</el-table-column>
				<el-table-column prop="principalName" width="100" label="校长姓名">
				</el-table-column>
				<el-table-column prop="principalPhone" width="100" label="校长电话">
				</el-table-column>
				<el-table-column prop="directorName" width="100" label="负责人姓名">
				</el-table-column>
				<el-table-column prop="directorPhone" width="100" label="负责人电话">
				</el-table-column>
				<el-table-column prop="lecturerName" width="100" label="讲师姓名">
				</el-table-column>
				<el-table-column prop="lecturerPhone" width="100" label="讲师电话">
				</el-table-column>
				<el-table-column prop="employeeName" width="100" label="提交人">
				</el-table-column>
				<el-table-column prop="createTime" label="提交时间" width="160" sortable>
				</el-table-column>
				<el-table-column prop="approvalorName" width="100" label="审批人">
				</el-table-column>
				<el-table-column prop="updateTime" label="审批时间" sortable>
					<template slot-scope="scope">
						<div>
							{{scope.row.updateTime||'-'}}
						</div>
					</template>
				</el-table-column>
				<el-table-column prop="approvalState" label="审批结果">
					<template slot-scope="scope">
						<span>
							<el-tag v-if="item.value == scope.row.approvalState" v-for="(item,index) in stateList1"
								:key="index" :type="item.type">{{item.text}}</el-tag>
						</span>
					</template>
				</el-table-column>
				<el-table-column fixed="right" label="操作" width="180">
					<template slot-scope="scope">
						<el-button @click="openModal(1,scope.$index)" type="success" round size="small"
							:disabled="scope.row.approvalState!=0" icon="el-icon-edit">审批
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!-- 校区信息 -->
		<el-drawer size="60%" :with-header="false" :visible.sync="editModal" direction="rtl">
			<div v-if="choiceItem">
				<el-row style="width:100%;height: auto;margin: 20px 20px;">
					<h2 class="detail-title">校区信息</h2>
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="* 校区名称：">
								<el-input v-model="choiceItem.campusName" type="text" class="handle-input mr10"
									placeholder="请输入校区名称">
								</el-input>
							</el-form-item>

							<el-form-item label="* 校区简介：">
								<el-input v-model="choiceItem.campusContent" type="textarea" class="handle-input mr10"
									placeholder="请输入校区简介" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="校区备注：">
								<el-input v-model="choiceItem.campusRemark" type="textarea" class="handle-input mr10"
									placeholder="请输入校区备注" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>

							<el-form-item label="校区头像：">
								<el-upload class="upload-demo" action="https://biapi.xiaoyujia.com/files/uploadFiles"
									list-type="picture" :on-success="imgUploadSuccess" :show-file-list="false">
									<img :src="choiceItem.campusImg||blankImg" style="width: 150px;height: 150px;"
										@click="openImgUpload(0,0)">
								</el-upload>
								<el-tooltip class="item" effect="dark" content="推荐尺寸：1000*1000（1：1）校区头像将对外展示:"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

							<el-form-item label="校区视频：">
								<el-button @click="openVideo(choiceItem.videoUrl)" type="primary" size="small"
									:disabled="!choiceItem.videoUrl" icon="el-icon-zoom-in">预览
								</el-button>
								<el-upload :action="fileUploadUrl" list-type="picture" :before-upload="beforeFileUpload"
									:on-success="fileUploadSuccess" :on-error="fileUploadError" :show-file-list="false"
									:data="fileUploadData" class="upload-demo inline-block">
									<el-button @click="fileUpload(0)" type="warning" size="small"
										style="margin-left: 10px" icon="el-icon-upload2">普通上传</el-button>
								</el-upload>
								<el-button @click="openModal(4,0)" type="info" size="small" style="margin-left: 10px"
									icon="el-icon-upload2">阿里云点播</el-button>
								<el-tooltip class="item" effect="dark" content="推荐上传到阿里云点播控制台，再将链接复制到这，以获得更流畅的视频播放体验"
									placement="top-start">
									<i class="el-icon-question" style="margin-left:10px;"></i>
								</el-tooltip>
							</el-form-item>

						</el-form>
					</el-col>

					<el-col :span="12">
						<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
							<el-form-item label="校区类型：">
								<el-select v-model="choiceItem.campusType" placeholder="请选择" style="width: 100px;">
									<el-option v-for="(item,index) in typeList" :key="index" :label="item.text"
										:value="item.value"></el-option>
								</el-select>
							</el-form-item>

							<el-form ref="ruleForm" label-width="120px" class="demo-ruleForm" size="mini">
								<el-form-item label="校区状态：">
									<el-select v-model="choiceItem.campusState" placeholder="请选择" style="width: 100px;">
										<el-option v-for="(item,index) in stateList" :key="index" :label="item.text"
											:value="item.value"></el-option>
									</el-select>
								</el-form-item>
							</el-form>

							<el-form-item label="* 校区地址：">
								<el-input v-model="choiceItem.campusAddress" type="textarea" class="handle-input mr10"
									placeholder="请输入校区地址" :autosize="{ minRows: 4, maxRows: 10}">
								</el-input>
							</el-form-item>


							<el-form-item label="地址纬度：">
								<el-input v-model="choiceItem.campusLng" type="number" class="handle-input mr10"
									placeholder="请输入校区地址纬度">
								</el-input>
							</el-form-item>

							<el-form-item label="地址经度：">
								<el-input v-model="choiceItem.campusLat" type="number" class="handle-input mr10"
									placeholder="请输入校区地址经度">
								</el-input>
							</el-form-item>

							<el-form-item label="员工检索：">
								<el-input v-model="searchText" placeholder="输入员工姓名/工号进行检索" @input="listEmployee"
									@change="listEmployee" style="width: 280px"></el-input>
							</el-form-item>

							<el-form-item label="管理者：">
								<el-select v-model="choiceItem.campusManagerName" placeholder="请选择"
									@change="selectEmployee">
									<el-option v-for="(item,index) in employeeList" :key="index"
										:label="item.realName+'-'+item.no" :value="item.id">
									</el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="校长名字：">
								<el-input v-model="choiceItem.principalName" type="text" class="handle-input mr10"
									placeholder="请输入校长名字">
								</el-input>
							</el-form-item>

							<el-form-item label="校长电话：">
								<el-input v-model="choiceItem.principalPhone" type="number" class="handle-input mr10"
									placeholder="请输入校长电话">
								</el-input>
							</el-form-item>

							<el-form-item label="负责人名字：">
								<el-input v-model="choiceItem.directorName" type="text" class="handle-input mr10"
									placeholder="请输入负责人名字">
								</el-input>
							</el-form-item>

							<el-form-item label="负责人电话：">
								<el-input v-model="choiceItem.directorPhone" type="number" class="handle-input mr10"
									placeholder="请输入负责人电话">
								</el-input>
							</el-form-item>

							<el-form-item label="讲师名字：">
								<el-input v-model="choiceItem.lecturerName" type="text" class="handle-input mr10"
									placeholder="请输入讲师名字">
								</el-input>
							</el-form-item>

							<el-form-item label="讲师电话：">
								<el-input v-model="choiceItem.lecturerPhone" type="number" class="handle-input mr10"
									placeholder="请输入讲师电话">
								</el-input>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="courseCommentModal=false" type="primary" size="small">取消
					</el-button>
					<!-- 	<el-button @click="insertCourseComment()" type="success" size="small" v-if="detailType==0">确定添加
					</el-button> -->
					<el-button @click="updateCampus(choiceItem)" type="success" size="small" v-if="detailType==1">保存
					</el-button>
				</div>
			</div>
		</el-drawer>

		<!-- 审批结果 -->
		<el-dialog :visible.sync="editModal1" width="40%" title="审批结果判定" :mask-closable="false"
			v-if="choiceIndexNow==1&&choiceItem">
			<el-select v-model="choiceItem.approvalState" style="margin-right: 20px;">
				<el-option v-for="(item,index) in stateList1" :key="index" :label="item.text" :value="item.value"
					:disabled='item.value==0'></el-option>
			</el-select>
			<el-input v-model="choiceItem.approvalRemark" style="width: 50%;" type="textarea"
				placeholder="请输入审批备注（不通过状态下必填）" :autosize="{ minRows: 4, maxRows: 10}">
			</el-input>
			<div style="margin: 20px 0;">
				<span>* 审批结束后不可再修改结果，校区证书立即发放</span>
			</div>
			<div>
				<el-button @click="updateCampusApproval(choiceItem)" type="success" round size="small"
					icon="el-icon-edit">确定结果
				</el-button>
			</div>
		</el-dialog>

		<!--图片预览-->
		<el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
			<img :src="imageUrl" style="width: 100%;height: auto;margin: 0 auto;" />
		</el-dialog>

		<!--视频预览-->
		<el-dialog :visible.sync="videoModal" width="40%" title="视频预览" :mask-closable="false">
			<video style="width: 100%;height: auto;margin: 0 auto;" :controls="true" :enable-progress-gesture="true"
				:initial-time="0" :show-center-play-btn="true" autoplay :src="videoUrl" custom-cache="false">
			</video>
		</el-dialog>
	</div>

</template>

<script>
	import Vue from 'vue';
	export default {
		name: "campus",
		data() {
			return {
				imgUpload: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669101235017img-upload.png",
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				typeStyleList: ['success', 'info', 'warning', 'primary'],
				imageUrl: '',
				videoUrl: '',
				imgModal: false,
				videoModal: false,
				editModal: false,
				editModal1: false,
				detailType: 0,
				choiceIndex: '0',
				choiceIndexNow: '0',
				choiceItemIndex: 0,
				uploadImgType: 0,
				uploadIndex: 0,

				uploadId: 1,
				// 文件上传地址
				fileUploadUrl: 'https://api.xiaoyujia.com/system/uploadFile',
				fileUploadData: {
					route: 'shortVideo'
				},
				uploadFileType: 0,
				list: null,
				employeeList: [],
				loading: true,
				pageSizeOpts: [20, 30, 50],
				pageInfo: {
					total: 0,
					size: 20,
					current: 1,
					pages: 1
				},
				getRowKeys(row) {
					return row.id
				},
				quer: {
					no: null,
					name: null,
					size: 20,
					current: 1,
					orderBy: 't.id ASC'
				},
				searchText: '',
				stateList: [{
					value: 0,
					text: "下架",
					type: "info"
				}, {
					value: 1,
					text: "上架",
					type: "success"
				}],
				stateList1: [{
					value: 0,
					text: "未审批",
					type: "info"
				}, {
					value: 1,
					text: "不通过",
					type: "danger"
				}, {
					value: 2,
					text: "已通过",
					type: "success"
				}],
				typeList: [{
					value: 0,
					text: "灵活",
					type: "success"
				}, {
					value: 1,
					text: "固定",
					type: "info"
				}],
				choiceItem: null,
				rowData: null,
				journalInfoLoading: true,
				journalList: null,
				journalInfoSizeOpts: [10, 20, 50],
				journalInfo: {
					total: 10,
					size: 10,
					current: 1,
					pages: 1
				},
				journalInfoDrawer: false,
				rules: {
					name: [{
						required: true,
						message: '请检索并选择',
						trigger: 'blur'
					}, ],
				},
				employeeList: [],
				imgList: [],
			}
		},
		created() {
			this.getData()
		},
		methods: {
			// 切换栏目
			handleSelect(key, keyPath) {
				this.choiceItem = null
				this.choiceIndex = key
				this.expands = []
				this.reset()
			},
			getData() {
				this.choiceIndexNow = 2
				if (this.choiceIndex == 0) {
					$.ajax({
						type: "post",
						url: "https://api.xiaoyujia.com/course/pageCampus",
						data: JSON.stringify(this.quer),
						dataType: "json",
						headers: {
							"Content-Type": "application/json"
						},
						success: res => {
							this.loading = false;
							if (res.code === 0) {
								this.list = res.data.records;
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
							} else {
								this.list = []
								this.$message.error('暂未查询到相关数据！')
							}
							this.choiceIndexNow = this.choiceIndex
						},
					})
				} else if (this.choiceIndex == 1) {
					$.ajax({
						type: "post",
						url: "https://api.xiaoyujia.com/course/pageCampusApproval",
						data: JSON.stringify(this.quer),
						dataType: "json",
						headers: {
							"Content-Type": "application/json"
						},
						success: res => {
							this.loading = false;
							if (res.code === 0) {
								this.list = res.data.records;
								this.pageInfo.current = res.data.current
								this.pageInfo.size = res.data.size
								this.pageInfo.total = res.data.total
							} else {
								this.list = []
								this.$message.error('暂未查询到相关数据！')
							}
							this.choiceIndexNow = this.choiceIndex
						},
					})
				}
			},
			query() {
				this.loading = true
				this.quer.current = 1
				this.getData()
			},
			reset() {
				this.list = []
				this.quer.search = null
				this.quer.approvalState = null
				this.quer.campusState = null
				this.quer.current = 1
				this.getData();
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size;
				this.getData();
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index;
				this.getData();
			},
			// 获取员工列表
			listEmployee() {
				if (!this.searchText) {
					return
				}
				this.$postData("listEmployeeDto", {
					search: this.searchText,
					employeeType: 20,
					state: 1
				}).then(res => {
					if (res.status == 200) {
						this.employeeList = res.data
					} else {
						this.employeeList = []
						this.$message.error("查询失败，" + res.msg)
					}
				})
			},
			// 选中员工
			selectEmployee(id) {
				let index = this.getIndex(id, this.employeeList)
				let employee = this.employeeList[index]
				this.choiceItem.campusManager = employee.no || null
				this.choiceItem.campusManagerName = employee.realName || ''
				this.searchText = employee.realName || ''
			},
			formatLongStr(str) {
				if (!str) {
					return "暂无"
				} else {
					let long = 40
					if (str.length > long) {
						str = str.substring(0, long) + "..."
					}
					return str
				}
			},
			renderHeader(h, {
				column,
				index
			}) {
				let tips = "暂无提示"
				if (column.label == "导师头像") {
					tips = "推荐尺寸：250*250（1:1）"
				} else if (column.label == "导师介绍") {
					tips = "一句话介绍，尽量控制在30字以内"
				} else if (column.label == "介绍详情图") {
					tips = "推荐尺寸：1500*高度（长图-高度自适应）"
				} else if (column.label == "导师积分") {
					tips = "通过用户评价获取的平均打分"
				} else if (column.label == "导师星级") {
					tips = "评估导师能力，根据导师收到打赏和评价自动评估，也可手动变更"
				}
				return h('div', [
					h('span', column.label),
					h('el-tooltip', {
							undefined,
							props: {
								undefined,
								effect: 'dark',
								placement: 'top',
								content: tips
							},
						},
						[
							h('i', {
								undefined,
								class: 'el-icon-question',
								style: "color:#409eff;margin-left:5px;cursor:pointer;"
							})
						],
					)
				]);
			},
			// 打开图片预览
			openImg(url) {
				this.imageUrl = url || this.blankImg
				this.imgModal = true
			},
			// 打开视频预览
			openVideo(url) {
				if (url != null && url != '') {
					this.videoUrl = url
				}
				this.videoModal = true
			},
			// 获取索引
			getIndex(val) {
				let index = 0
				for (let i = 0; i < this.list.length; i++) {
					if (val == this.list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			openModal(index, index1) {
				if (index == 0) {
					this.detailType = 0
					this.choiceItem = {
						realName: null,
						memberId: null,
						employeeId: null,
						headImg: null,
						teacherIntroduction: null,
						teacherImg: null,
					}
					this.editModal1 = true
				} else if (index == 1) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.editModal1 = true
				} else if (index == 2) {
					this.detailType = 0
					this.choiceItem = {}
					this.editModal = true
				} else if (index == 3) {
					this.detailType = 1
					this.choiceItemIndex = index1
					this.choiceItem = this.list[index1]
					this.editModal = true
				} else if (index == 4) {
					window.open(
						'https://vod.console.aliyun.com/?spm=5176.12672711.categories-n-products.dvod.8f791fa37JVfKI#/media/video/list'
					)
				}
			},
			// 打开图片上传
			openImgUpload(value, index) {
				this.uploadImgType = value
				this.uploadIndex = index
				let tips = ''
				if (this.uploadImgType == 0) {
					tips = "推荐尺寸：1000*1000（1：1）校区头像将对外展示"
				}
				this.$message.info(tips)
			},
			// 图片上传成功
			imgUploadSuccess(res, file) {
				if (this.uploadImgType == 0) {
					this.$set(this.choiceItem, "campusImg", res.data)
					this.list[this.choiceItemIndex].campusImg = res.data
				}
			},
			beforeAvatarUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传头像图片大小不能超过 2MB!');
				}
				return isLt2M;
			},
			// 获取索引
			getIndex(val, list) {
				let index = 0
				for (let i = 0; i < list.length; i++) {
					if (val == list[i].id) {
						index = i
						break
					}
				}
				return index
			},
			// 更新校区信息
			updateCampus(val) {
				if (!val.campusName) {
					this.$message.error('请填写校区名称！')
				} else if (!val.campusContent) {
					this.$message.error('请填写校区简介！')
				} else if (!val.campusAddress) {
					this.$message.error('请填写校区地址！')
				} else {
					$.ajax({
						type: "post",
						url: "https://api.xiaoyujia.com/course/updateCampus",
						data: JSON.stringify(val),
						dataType: "json",
						headers: {
							"Content-Type": "application/json"
						},
						success: res => {
							if (res.code === 0) {
								this.$message.success('更新成功！')
								this.editModal = false
							} else {
								this.$message.error(res.msg)
							}
						},
					})
				}
			},
			// 更新校区审批
			updateCampusApproval(val) {
				if (val.approvalState == 0) {
					this.$message.error('请选择审批结果！')
				} else if (val.approvalState == 1 && !val.approvalRemark) {
					this.$message.error('请补充不通过原因！')
				} else {
					let id = localStorage.getItem('id') || 12
					this.$set(val, 'approvalor', id)
					$.ajax({
						type: "post",
						url: "https://api.xiaoyujia.com/course/updateCampusApproval",
						data: JSON.stringify(val),
						dataType: "json",
						headers: {
							"Content-Type": "application/json"
						},
						success: res => {
							if (res.code === 0) {
								if (val.approvalState == 2) {
									this.$message.success('审批成功！校区已开通！')
								} else {
									this.$message.success('操作完成！')
								}
								this.editModal1 = false
							} else {
								this.$message.error(res.msg)
							}
						},
					})
				}
			},
			beforeFileUpload() {
				this.loading = true
			},
			// 章节素材文件上传成功
			fileUploadSuccess(res, file) {
				this.loading = false
				if (this.uploadFileType == 0) {
					this.choiceItem.videoUrl = res.data
				}
				this.$message.success('视频文件上传成功！')
			},
			// 章节素材文件上传失败
			fileUploadError(err) {
				this.loading = false
				this.$message.error('视频文件上传失败！请重试！' + err.msg)
			},
			// 课程章节素材文件上传
			fileUpload(value) {
				this.uploadFileType = value
				this.fileUploadUrl = fileUploadUrl
			},
			beforeImgUpload(file) {
				const isLt2M = file.size / 1024 / 1024 < 2;
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!');
				}
				return isLt2M;
			}
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 100px;
		height: 100px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 100px;
		height: 100px;
		line-height: 100px;
		text-align: center;
	}

	.avatar {
		width: 100px;
		height: 100px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>