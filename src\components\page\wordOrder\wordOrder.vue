<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form" :model="pageInfo">
                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="订单编号">
                                <el-input
                                        v-model="dto.orderNo"
                                        placeholder="订单编号"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="服务项目">
                                <Select filterable style="width: 120px" @on-change="changeValue" clearable
                                        v-model="dto.productCategoryId">
                                    <Option value="0">请选择</Option>
                                    <Option v-for="item in options" :value="item.id" :key="item.id">{{item.name}}</Option>
                                </Select>
                                <Select filterable style="width: 120px" v-model="dto.productId" clearable>
                                    <Option value="">请选择</Option>
                                    <Option v-for="item in options_children" :value="item.value" :key="item.value">{{item.text}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="7">
                            <el-form-item label="服务地址">
                                <Select filterable style="width: 100px" @on-change="getCity"
                                        v-model="dto.cityId">
                                    <Option value="">请选择</Option>
                                    <Option v-for="item in serviceOptions" :value="item.value" :key="item.value">{{
                                        item.label}}
                                    </Option>
                                </Select>
                                <Select  style="width: 100px" v-model="dto.areaId">
                                    <Option value="">请选择</Option>
                                    <Option v-for="item in cityList" :value="item.id" :key="item.id">{{item.name}}
                                    </Option>
                                </Select>
                            </el-form-item>
                        </el-col>


                    </el-row>
                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="所属门店">
                                <Select filterable style="width: 130px" v-model="dto.storeId">
                                    <Option value="">请选择</Option>
                                    <Option v-for="(item,index) in storeList" :value="item.id" :key="index">{{ item.storeName}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="服务时间">
                                <el-date-picker
                                        v-model="dto.startTime"
                                        type="datetime"
                                        placeholder="开始时间"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5" style="text-align: left">
                            <el-form-item label="至">
                                <el-date-picker
                                        v-model="dto.endTime"
                                        type="datetime"
                                        placeholder="结束时间"
                                        format="yyyy-MM-dd HH:mm"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>

                        <el-col :span="5">
                            <el-form-item label="所属部门">
                                <Select filterable style="width: 150px" v-model="dto.departmentId" clearable @on-change="query1">
                                    <Option value="">请选择</Option>
                                    <Option value="1">保洁部</Option>
                                    <Option value="2">搬家部</Option>
                                    <Option value="3">维修部</Option>
                                </Select>
                            </el-form-item>
                        </el-col>

                    </el-row>

                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="是否欠款">
                                <Select filterable style="width: 100px" v-model="dto.arrearsType">
                                    <Option value="">请选择</Option>
                                    <Option :value="1">是</Option>
                                    <Option :value="0">否</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="计人计车">
                                <Select filterable style="width: 100px" v-model="dto.serviceOption" @on-change="getData">
                                    <Option value="">请选择</Option>
                                    <Option :value="1">计人</Option>
                                    <Option :value="2">计车</Option>
                                </Select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="5">
                            <el-form-item label="会员账户">
                                <el-input
                                        v-model="dto.account"
                                        placeholder="会员账户"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                    </el-row>
                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="订单标签">
                                <Select filterable style="width: 100px" v-model="dto.lblId">
                                    <Option value="">请选择</Option>
                                    <Option v-for="(item,index) in orderLabelList" :value="item.name" :key="index">{{ item.name}}</Option>
                                </Select>
                            </el-form-item>
                        </el-col>
                        <!--<el-col :span="11">-->
                        <!--<el-radio-group v-model="dto.orderState" @change="query()">-->
                        <!--<el-radio-button label="10">已接单</el-radio-button>-->
                        <!--<el-radio-button label="20">派单待确认</el-radio-button>-->
                        <!--</el-radio-group>-->
                        <!--</el-col>-->
                        <el-col :span="5">
                            <el-form-item label="订单状态">
                                <Select filterable style="width: 100px" v-model="dto.orderState" @on-change="query">
                                    <Option value="">请选择</Option>
                                    <Option :value="10">已接单</Option>
                                    <Option :value="20">派单待确认</Option>
                                    <Option :value="30">拒绝接单</Option>
                                    <Option :value="40">已派单</Option>
                                    <Option :value="50">执行中</Option>
                                    <Option :value="60">开始服务</Option>
                                    <Option :value="70">服务结束</Option>
                                    <Option :value="80">已完成</Option>
                                    <Option :value="90">已评价</Option>
                                    <Option :value="99">已取消</Option>
                                </Select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="7">
                            <el-form-item label="服务员工">
                                <el-input
                                        v-model="dto.serviceName"
                                        placeholder="支持员工号/姓名"
                                        style="width:150px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6" style="text-align:right">
                            <el-form-item>
                                <el-button type="warning" v-if="dto.orderState==20" :loading="isSending"
                                           style="width:30%" @click="sendingSms">一键催单
                                </el-button>
                                <el-button  v-else type="success"  style="width:30%"
                                           @click="oneButtonList" :loading="isOrder">一键派单
                                </el-button>
                                <el-button type="primary" style="width:30%" @click="query(true)">车单</el-button>
                                <el-button type="primary" style="width:30%" @click="query()">搜索</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div style="text-align:right">{{summary}}</div>
            <el-table :data="list" border class="table"
                      v-loading="loading"
                      ref="multipleTable"
                      tooltip-effect="dark"
                      @selection-change="handleSelectionChange">
                <el-table-column
                        type="selection"
                        width="55">
                </el-table-column>
                <el-table-column
                        prop="order.billNo"
                        width="140"
                        label="订单编号">
                    <template slot-scope="scope">
                     <div v-if="scope.row.red" style="text-align: center;">
                         <a  style="text-decoration:underline;color: red" :href="scope.row.url" target="_Blank">{{scope.row.order.billNo}}</a>
                     </div>
                        <a v-else style="text-decoration:underline;" :href="scope.row.url" target="_Blank">{{scope.row.order.billNo}}</a>
                    </template>
                </el-table-column>
                <!--<el-table-column-->
                <!--prop="order.orderStateDesc"-->
                <!--label="订单状态"-->
                <!--:show-overflow-tooltip="true"-->
                <!--&gt;</el-table-column>-->
                <el-table-column
                        prop="order.productName"
                        label="服务项目"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        width="150"
                        prop="order.startTime"
                        label="服务时间"
                ></el-table-column>
                <el-table-column
                        prop="order.totalAmount"
                        width="75"
                        label="服务金额"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        width="110"
                        prop="orderWaiter.serviceName"
                        label="服务员工">
                    <template slot-scope="scope">
                        <p style="font-size: 12px" v-for="item in scope.row.orderWaiterList">
                            <span v-if="dto.serviceOption==1">
                                 {{item.serviceNo}}
                            </span>
                            <span v-else-if="dto.serviceOption==2">
                                 {{item.carCode}}
                            </span>
                            ({{item.serviceName}})
                        </p>
                    </template>
                </el-table-column>
                <el-table-column
                        width="200"
                        prop="address"
                        label="服务地址"
                ></el-table-column>
                <el-table-column
                        prop="member.account"
                        label="会员账号"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <!--<el-table-column-->
                <!--prop="store.storeName"-->
                <!--label="所属门店"-->
                <!--:show-overflow-tooltip="true"-->
                <!--&gt;</el-table-column>-->

                <el-table-column
                        width="40"
                        prop="arrearsTypeDesc"
                        label="是否欠款"
                ></el-table-column>
                <el-table-column
                        width="75"
                prop="makeNum"
                label="预约人数"
                :show-overflow-tooltip="true">
                </el-table-column>

                <!--<el-table-column-->
                        <!--prop="makeNum"-->
                        <!--label="预派人员"-->
                        <!--:show-overflow-tooltip="true">-->
                    <!--<template slot-scope="scope">-->
                        <!--<p style="font-size: 12px" v-for="item in scope.row.preStocks">-->
                            <!--{{item.serviceNo}}-->
                        <!--</p>-->
                    <!--</template>-->
                <!--</el-table-column>-->

                <el-table-column
                        width="200"
                        prop="order.remark"
                        label="订单备注"
                ></el-table-column>
                <el-table-column
                        prop="orderExt.lblId"
                        label="订单标签"
                        :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                        prop="overtime"
                        label="超时时间"
                        :show-overflow-tooltip="true"
                ></el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      :current="this.dto.pageNum"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="10"/>
            </div>
        </div>
    </div>
</template>

<script>


    export default {
        data() {
            return {
                loading: true,
                isOrder: false,
                isSending: false,
                summary: null,
                list: null,
                tableData: [],
                pageSizeOpts: [10, 20, 30],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                productCategoryId: null,
                serviceOptions: [], //服务区域
                cityList: [],
                orderLabelList: [],
                dto: {
                    productId: null,
                    departmentId: null,
                    lblId: null,
                    productCategoryId: null,
                    areaId: null,
                    cityId: null,
                    account: null,
                    storeId: null,
                    startTime: null,
                    serviceName: null,
                    endTime: null,
                    serviceOption: 1,
                    arrearsType: null,
                    orderState: 10,
                    pageSize: 10,
                    pageNum: 1,
                },
                options: [], //服务项目一级
                options_children: [], //服务项目二级
                storeList: [],
                startTime1: [],
                timer: null,
                billNos: null,
                tableSelect: null,

            };
        },
        components: {},
        created() {
            console.log( this.$router)
            this.getData();
            this.getProduct();
            this.getStore();
            this.getAddress();
            this.getOrderLabel();
            this.time();
        },
        mounted() {
            this.timer = setInterval(() => {
                this.getData()
            }, 300000)
        },
        beforeDestroy() {
            clearInterval(this.timer);
        },
        computed: {},
        methods: {
            time() {
                let y = new Date().getFullYear(),
                    m = new Date().getMonth() + 1,
                    d = new Date().getDate();
                this.dto.endTime =
                    y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d) + " 23:59:59";
                if (m == 0) {
                    y -= 1;
                    m = 12;
                }
                this.dto.startTime =
                    y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d) + " 00:00:00";
            },
            getData() {
                this.loading = true;
                this.$postData("dispatch_list", this.dto, {}).then(res => {
                    if (res.status == 200) {
                        this.loading = false;
                       ;
                        this.summary = res.data.summary;
                        this.list = res.data.list;
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    }else {
                        this.loading = false;
                    }
                });
            },

            changeValue(value) {
                if (value != null) {
                    this.$getData("getProductName", {productCategoryId: value}).then(
                        res => {
                            this.options_children = res.data;
                        }
                    );
                }
            },
            getProduct() {
                this.$getData("category_getByRoleId").then(res => {
                    this.options = res.data;
                    console.log(this.options)
                });
            },
            getStore() {
                this.$postData("store_getByList", {}, {}).then(res => {
                    if (res.status == 200) {
                       ;
                        this.storeList = res.data;
                    }
                });
            },
            getAddress() {
                this.$getData("getCityArea").then(res => {
                    console.log(res.data);
                    this.serviceOptions = res.data;
                });
            },
            getCity(value) {
                this.$getData("getCityId", {cityId: value}).then(
                    res => {
                        this.cityList = res.data;
                    }
                );
            },

            getOrderLabel() {
                this.$getData("getOrderLabel").then(
                    res => {

                        this.orderLabelList = res.data;
                    }
                );
            },

            query(res) {
                if (res==true) {
                    this.dto.serviceOption = 2
                    this.dto.orderState = "";
                }
                this.dto.pageNum = 1;
                this.loading = true;
                this.tableSelect = null;
                this.getData();
            },

            query1(res) {
                if (res == 1) {
                    this.dto.orderState = 10;
                } else {
                    this.dto.orderState = "";
                }
                this.getData();
            },


            // 页码大小
            onPageSizeChange(size) {
                this.loading = true
                this.dto.pageSize = size;
                this.getData();
            },
            // 跳转页码
            onChange(index) {
                console.log(index)
                this.loading = true
                this.dto.pageNum = index;
                this.getData();
            },
            handleSelectionChange(val) {
                console.log(val);
                let push = [];
                let billNos = "";
                for (let i = 0; i < val.length; i++) {
                    let res = val[i];
                   ;
                    let sending = {
                        billNo: res.order.billNo,
                        serviceOption:this.dto.serviceOption,
                        orderWaiterList: res.orderWaiterList,
                        startTime: res.order.startTime
                    };
                    push.push(sending);
                    billNos += res.order.billNo;
                    if (i != val.length - 1) {
                        billNos += ","
                    }
                }
                console.log(billNos)
                console.log(push)
                this.tableSelect = push;
                this.billNos = billNos;
            },
            /*
            * 一键派单
            * */
            oneButtonList() {
                if (this.billNos != '' && this.billNos != null) {
                    this.isOrder = true; //遮罩
                    this.$getData("dispatch_button_list", {billnos: this.billNos}).then(res => {
                        if (res.Meta.State == 200) {
                            this.isOrder = false;
                            this.$message({
                                showClose: true,
                                message: res.Meta.Msg,
                                type: 'success'
                            });
                            this.getData();
                        } else {
                            this.isOrder = false;
                            this.$alert(res.Meta.Msg, '提示', {
                                confirmButtonText: '确定',
                            });
                            this.getData();
                        }
                    })
                } else {
                    this.$message.error('请选择订单');
                }
            },
            sendingSms() {
                console.log(this.tableSelect)
                if (this.tableSelect != null && this.tableSelect.length > 0) {
                    this.isSending = true; //遮罩
                    this.$postData("dispatch_sending", {list: this.tableSelect}).then(res => {

                        if (res.status == 200) {
                            this.isSending = false;
                            this.$message({
                                showClose: true,
                                message: res.data,
                                type: 'success'
                            });
                        } else {
                            this.isSending = false;
                            this.$alert(res.msg, '提示', {
                                confirmButtonText: '确定',
                            });
                        }
                        this.getData();
                    })
                } else {
                    this.$message.error('请选择订单');
                }
            }
        }
    };
</script>

<style scoped>
    .handle-box {
        margin-bottom: 20px;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;
    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }
</style>
