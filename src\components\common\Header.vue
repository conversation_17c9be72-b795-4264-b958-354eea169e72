<template>
    <div class="header">
        <!-- 折叠按钮 -->
        <div class="collapse-btn" @click="collapseChage">
            <i class="el-icon-menu"></i>
        </div>
        <div class="logo">小羽佳{{channelName}}数据分析</div>
        <div class="header-right">
            <div class="header-user-con">
                <!-- 全屏显示 -->
                <div class="btn-fullscreen" @click="handleFullScreen">
                    <el-tooltip effect="dark" :content="fullscreen?`取消全屏`:`全屏`" placement="bottom">
                        <i class="el-icon-rank"></i>
                    </el-tooltip>
                </div>
                <el-divider direction="vertical"></el-divider>
                <el-select v-model="storeId" filterable placeholder="请选择您的门店" @change="changeStoreId">
                    <el-option
                            v-for="(item, index) in storeList"
                            :key="index"
                            :label="item.storeName"
                            :value="item.id">
                    </el-option>
                </el-select>
                <el-divider direction="vertical"></el-divider>
                <!-- 消息中心 -->
                <a style="font-size: 15px" @click="goUpdate()">
                    修改密码
                    <!--<el-tooltip-->
                    <!--effect="dark"-->
                    <!--:content="message?`有${message}条未读消息`:`消息中心`"-->
                    <!--placement="bottom"-->
                    <!--&gt;-->
                    <!--<router-link to="/tabs">-->
                    <!--<i class="el-icon-bell"></i>-->
                    <!--</router-link>-->
                    <!--</el-tooltip>-->
                    <!--<span class="btn-bell-badge" v-if="message"></span>-->
                </a>


                <!-- 用户头像 -->
                <div class="user-avator">
                    <img :src="headImg">
                </div>
                <!-- 用户名下拉菜单 -->
                <el-dropdown class="user-name" trigger="click" @command="handleCommand">
                    <span class="el-dropdown-link">
                        {{username}}
                        <i class="el-icon-caret-bottom"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                        <!-- <a href="https://github.com/wuguangnuo" target="_blank">
                            <el-dropdown-item>关于作者</el-dropdown-item>
                        </a>
                        <el-dropdown-item>
                            <router-link to="/profile">
                                个人中心
                            </router-link>
                        </el-dropdown-item>-->
                        <el-dropdown-item divided command="loginout">退出登录</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
        </div>
        <el-dialog
                title="提示"
                :visible.sync="dialogVisible"
                width="30%"
        >
            <h3>您有{{message}}个需求，请尽快处理！</h3>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="dialogVisible = false">五分钟后继续提醒</el-button>
              </span>
        </el-dialog>
        <Modal v-model="updateShow" class="Modal" :width="screenWidth" title="修改密码"
               :mask-closable="false">
            <div class="addBody">
                <update-password v-if="updateShow" @init-choose="initChooseProject"
                                 @close-modal="closeCurrModal"></update-password>
            </div>
            <div slot="footer">
            </div>
        </Modal>
    </div>
</template>
<script>
    import bus from "../common/bus";
    import updatePassword from '@/components/common/updatePassword.vue'

    export default {
        data() {
            return {
                storeId: null,
                storeList: [],
                dialogVisible: false,
                collapse: false,
                fullscreen: false,
                name: "linxin",
                message: 0,
                headImg: localStorage.getItem("headImg"),
                channelName: localStorage.getItem("channelName"),
                updateShow: false,
                screenWidth: "30%"
            };
        },
        computed: {
            username() {
                let username = localStorage.getItem("realName");
                return username ? username : this.name;
            }
        },
        created() {

            if (localStorage.getItem("storeId") === "1") {
                this.storeId = "平台"
            } else {
                this.storeId = Number(localStorage.getItem("storeId"))
            }

            this.getEmployeeStoreDto();
            this.getOrderNeeds();
            let self = this;
            setInterval(function () {
                self.$getUrl("orderNeeds_count", localStorage.getItem("id"), {}).then(res => {
                    if (res.status == 200) {
                        self.message = res.data;
                        if (self.message > 0) {
                            self.dialogVisible = true
                        }

                    } else {
                        localStorage.clear();
                        self.$message.error("失败，" + res.msg);
                    }
                })
            }, 1000 * 60 * 5);

        },
        components: {
            'updatePassword': updatePassword
        },
        methods: {
            changeStoreId() {
                if (this.storeId !== '平台') {
                    localStorage.setItem("storeId", this.storeId);
                } else {
                    localStorage.setItem("storeId", "1");
                }
                window.location.reload()
            },
            goUpdate() {
                this.updateShow = true
            },
            show() {
                // console.log("0000")
            },
            getOrderNeeds() {
                this.$getUrl("orderNeeds_count", localStorage.getItem("id"), {}).then(res => {
                    if (res.status === 200) {
                        this.message = res.data;
                        if (this.message > 0) {
                            this.dialogVisible = true
                        }
                    } else {
                        this.$message.error("失败，" + res.msg);
                    }
                })
            },
            getEmployeeStoreDto() {
                const userSid = localStorage.getItem("userStoreId");
                if (userSid != null && userSid === '1') {
                    this.storeList.push({
                        id: '平台',
                        storeName: '平台'
                    })
                }
                this.$getData("getEmployeeStoreDto", {employeeId: localStorage.getItem("id")}, {}).then(res => {
                    if (res.status === 200) {
                        res.data.forEach(e => {
                            this.storeList.push({
                                id: e.id,
                                storeName: e.storeName
                            })
                        })
                    } else {
                        this.$message.error("失败，" + res.msg);
                    }
                })
            },
            // 用户名下拉菜单选择事件
            handleCommand(command) {
                if (command === "loginout") {
                    localStorage.clear();
                    this.$router.push("/login");
                }
                if (command === "profile") {
                }
            },
            // 侧边栏折叠
            collapseChage() {
                this.collapse = !this.collapse;
                bus.$emit("collapse", this.collapse);
            },
            // 全屏事件
            handleFullScreen() {
                let element = document.documentElement;
                if (this.fullscreen) {
                    if (document.exitFullscreen) {
                        document.exitFullscreen();
                    } else if (document.webkitCancelFullScreen) {
                        document.webkitCancelFullScreen();
                    } else if (document.mozCancelFullScreen) {
                        document.mozCancelFullScreen();
                    } else if (document.msExitFullscreen) {
                        document.msExitFullscreen();
                    }
                } else {
                    if (element.requestFullscreen) {
                        element.requestFullscreen();
                    } else if (element.webkitRequestFullScreen) {
                        element.webkitRequestFullScreen();
                    } else if (element.mozRequestFullScreen) {
                        element.mozRequestFullScreen();
                    } else if (element.msRequestFullscreen) {
                        // IE11
                        element.msRequestFullscreen();
                    }
                }
                this.fullscreen = !this.fullscreen;
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.updateShow = false;
            },
            initChooseProject(data) {
                this.closeCurrModal();
            },
        },
        mounted() {
            if (document.body.clientWidth < 1500) {
                this.collapseChage();
            }

        },

    };
</script>
<style scoped>
    .header {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        height: 55px;
        font-size: 20px;
        color: #fff;
    }

    .collapse-btn {
        float: left;
        padding: 0 21px;
        cursor: pointer;
        line-height: 55px;
    }

    .header .logo {
        float: left;
        width: 250px;
        line-height: 55px;
    }

    .header-right {
        float: right;
        padding-right: 50px;
    }

    .header-user-con {
        display: flex;
        height: 55px;
        align-items: center;
    }

    .btn-fullscreen {
        transform: rotate(45deg);
        margin-right: 5px;
        font-size: 24px;
    }

    .btn-bell,
    .btn-fullscreen {
        position: relative;
        width: 30px;
        height: 30px;
        text-align: center;
        border-radius: 15px;
        cursor: pointer;
    }

    .btn-bell-badge {
        position: absolute;
        right: 0;
        top: -2px;
        width: 8px;
        height: 8px;
        border-radius: 4px;
        background: #f56c6c;
        color: #fff;
    }

    .btn-bell .el-icon-bell {
        color: #fff;
    }

    .user-name {
        margin-left: 10px;
    }

    .user-avator {
        margin-left: 20px;
    }

    .user-avator img {
        display: block;
        width: 40px;
        height: 40px;
        border-radius: 50%;
    }

    .el-dropdown-link {
        color: #fff;
        cursor: pointer;
    }

    .el-dropdown-menu__item {
        text-align: center;
    }
</style>
