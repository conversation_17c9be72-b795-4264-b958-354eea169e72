<template>
    <div>
<!--        <span class="startWord">城市 区域</span>-->
<!--        <div class="label-name">城市区域:</div>-->
    <el-select v-model="city" filterable @change="cityChange" placeholder="城市" style="width: 35%">
        <el-option
                v-for="item in citys"
                :key="item.value"
                :label="item.label"
                :value="item.value">
        </el-option>
    </el-select>

    <el-select v-model="area" placeholder="区域" @change="areaChange" style="width: 35%">
        <el-option
                v-for="item in areas"
                :key="item.value"
                :label="item.label"
                :value="item.value">
        </el-option>
    </el-select>
    </div>
</template>

<script>
    export default {
        props:['getcity'],
        name: "cityChoose",
        data() {
            return{
                model:this.getcity,
                city:null,
                area:null,
                cityName:null,
                areaName:null,
                cityAreas:[],
                citys:[],
                areas:[],

            }
        },
        created() {
            console.log("-------")
            console.log(this.model)

            if (this.getcity!=null){
                this.city=this.model.cityId
                this.area=this.model.areaId
            }
            this.getCitys();
            this.cityChange()

        },
        methods: {
            getCitys() {
                this.$postData("cityArea_list", this.model, {}).then(res => {
                    if (res.status == 200) {

                        this.cityAreas = res.data;
                        this.cityAreas.forEach((item, index, arr) => {
                            if (item.cityId==0) {
                                this.citys.push( {value: item.id, label: item.name});
                            }
                            if (item.cityId!=0) {
                                this.areas.push( {value: item.id, label: item.name});
                            }
                        });
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            cityChange(i){
                this.areas=[];
                this.citys.forEach(v=>{
                    if (v.value==this.city){
                        this.cityName=v.label
                        console.log(this.cityName)
                    }
                })
                this.cityAreas.forEach((item, index, arr) => {
                    if (item.cityId==this.city) {

                        this.areas.push( {value: item.id, label: item.name});
                    }
                });
                this.$emit('init-choose', {cityId:this.city,areaId:this.area});

            },
            areaChange(){

                this.areas.forEach(v=>{
                    if (v.value==this.area){
                        this.areaName=v.label
                        console.log(this.areaName)
                        this.$emit('init-choose', {cityId:this.city,areaId:this.area,cityName:this.cityName,areaName:this.areaName});
                    }
                })

            }

        }
    }
</script>

<style scoped>
    .label-name{
        float: left;
        text-align: center;
        width: 20%;
    }
    .startWord{
        background-color: #F5F7FA;
        color: #909399;
        vertical-align: middle;

        position: relative;
        border: 1px solid #DCDFE6;
        border-radius: 4px;

        padding: 7px 20px;
        width: 1px;
        white-space: nowrap;
        float: left;
        padding-right: 70px;

    }
</style>
