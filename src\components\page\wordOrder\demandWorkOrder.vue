<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="9">
            <el-form-item label="报名时间">
              <el-date-picker v-model="form.days" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="姓名">
              <el-input clearable v-model="form.name"
                        placeholder="姓名"
                        style="width:200px"
                        class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="手机号">
              <el-input
                  clearable
                  v-model="form.phone"
                  placeholder="手机号"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="微信号">
              <el-input
                  clearable
                  v-model="form.weChat"
                  placeholder="微信号"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="抖音号">
              <el-input
                  clearable
                  v-model="form.tiktok"
                  placeholder="抖音号"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="订单渠道">
              <el-select v-model="form.channel" clearable placeholder="请选择渠道">
                <el-option
                    v-for="item in channel"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态">
              <el-select v-model="form.state" clearable placeholder="请选择状态">
                <el-option v-for="item in options"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="填报记录">
          <el-table :data="logList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column type="expand">
              <template slot-scope="scope">
                <el-form label-position="left" inline class="demo-table-expand">
                  <el-form-item label="处理结果">
                    <el-radio-group v-model="scope.row.state">
                      <el-radio :label='0'>不需要</el-radio>
                      <el-radio :label='2'>已报价</el-radio>
                      <el-radio :label='3'>已开单</el-radio>
                      <el-radio :label='4'>联系未接听</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <br>
                  <el-form-item label="订单号" v-show="scope.row.state===3">
                    <el-input v-model="scope.row.billNo" placeholder="请输入内容"></el-input>
                  </el-form-item>
                  <br>
                  <el-button type="primary" @click="saveDemandWorkOrder(scope.row)">更新</el-button>
                </el-form>
              </template>
            </el-table-column>
            <el-table-column prop="state" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.state===0" type="danger">不需要</el-tag>
                <el-tag v-if="scope.row.state===2" type="success">已报价</el-tag>
                <el-tag v-if="scope.row.state===3" type="primary">已开单</el-tag>
                <el-tag v-if="scope.row.state===4" type="info">联系未接听</el-tag>
                <el-tag v-else-if="scope.row.state===1" type="warning">未处理</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="姓名" width="100"></el-table-column>
            <el-table-column prop="phone" label="手机号" width="100"></el-table-column>
            <el-table-column prop="weChat" label="微信号" width="100"></el-table-column>
            <el-table-column prop="tiktok" label="抖音号" width="100"></el-table-column>
            <el-table-column prop="channel" label="订单渠道" width="80"></el-table-column>
            <el-table-column prop="project" label="服务项目"></el-table-column>
            <el-table-column prop="remark" label="需求备注"></el-table-column>
            <el-table-column prop="creatDate" label="报名时间" width="150" :formatter="dateFormat"></el-table-column>
            <el-table-column prop="billNo" label="订单号" width="150"></el-table-column>
            <el-table-column prop="ordrtAmount" label="订单金额" width="150"></el-table-column>
            <el-table-column label="操作" fixed="right" width="100">
              <template slot-scope="scope">
                <el-button @click.native.prevent="showRow(scope.row)"
                           type="text" size="small">
                  操作
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
        <el-tab-pane label="渠道线索">
          <el-table :data="list" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column prop="channel" label="名称" width="100"></el-table-column>
            <el-table-column prop="number" label="线索量" width="100"></el-table-column>
            <el-table-column prop="intoOrder" label="成单量" width="100"></el-table-column>
            <el-table-column prop="amount" label="成单业绩" width="100"></el-table-column>
            <el-table-column prop="conversion" label="成单转化率" width="100"></el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="服务线索">
          <el-table :data="list2" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column prop="project" label="名称" width="100"></el-table-column>
            <el-table-column prop="number" label="线索量" width="100"></el-table-column>
            <el-table-column prop="intoOrder" label="成单量" width="100"></el-table-column>
            <el-table-column prop="amount" label="成单业绩" width="100"></el-table-column>
            <el-table-column prop="conversion" label="成单转化率" width="100"></el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import moment from 'moment';

export default {
  name: "demandWorkOrder",
  data() {
    return {
      channel: [{
        value: '0',
        label: 'H5'
      }, {
        value: '1',
        label: '百度'
      }, {
        value: '2',
        label: '抖音'
      }, {
        value: '3',
        label: '其他'
      },],
      logList: [],
      handler: [],
      loadingExcel: false,
      dialogVisible: false,
      innerVisible: false,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      list: [],
      list2: [],
      form: {
        days: [],
        state: null,
        startTime: null,
        endTime: null,
        channel: null,
        name: null,
        phone: null,
        weChat: null,
        tiktok: null,
        current: 1,
        size: 10
      },
      dom: {
        state: null,
        name: null,
        phone: null,
        weChat: null,
        tiktok: null,
        channel: null,
        project: null,
        remark: null,
        creatDate: null,
        billNo: null,
      },
      options: [{
        value: '0',
        label: '不需要'
      }, {
        value: '1',
        label: '未处理'
      }, {
        value: '2',
        label: '已报价'
      }, {
        value: '3',
        label: '已开单'
      }, {
        value: '4',
        label: '联系未接听'
      },],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },
  created() {
    this.demandWorkOrderPage();
    this.channelClue();
    this.projectClue();
  },
  methods: {
    saveDemandWorkOrder(row) {
      if (row.billNo === '' || row.billNo === null) {
        return this.$message.error("请输入订单号");
      }
      this.$getData("getOrdrtAmountByBillNo", {billNo: row.billNo}).then(res => {
        if (res.data !== null) {
          row.ordrtAmount = res.data;
          row.handlerId = localStorage.getItem("id");
          this.updateDemandWorkOrder(row);
        } else {
          return this.$message.error("找不到该订单");
        }
      })
    },
    updateDemandWorkOrder(row) {
      this.$postData("updateDemandWorkOrder", row).then(res => {
        if (res.status == 200) {
          this.$message.success("保存成功");
          this.demandWorkOrderPage()
        } else {
          this.$message.error("保存失败，" + res.msg);
        }
      })
    },
    showRow(row) {
      this.$refs.multipleTable.toggleRowExpansion(row);
    },
    dateFormat(row, column) {
      var date = row[column.property];
      if (date === undefined) {
        return ''
      }
      return moment(date).format("YYYY-MM-DD HH:mm:ss")
    },
    // 页码大小
    onPageSizeChange(size) {
      this.form.size = size;
      this.demandWorkOrderPage();
    },
    onChange(index) {
      this.form.current = index;
      this.demandWorkOrderPage();
    },
    getData() {
      this.form.startTime = null;
      this.form.endTime = null;
      if (this.form.days != null && this.form.days.length > 0) {
        this.form.startTime = this.form.days[0];
        this.form.endTime = this.form.days[1]
      }
      this.demandWorkOrderPage();
      this.channelClue();
      this.projectClue();
    },
    demandWorkOrderPage(){
      this.$postData("demandWorkOrderPage", this.form, {}).then(res => {
        if (res.status == 200) {
          this.logList = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    },
    channelClue() {
      this.$postData("channelClue", this.form).then(res => {
        if (res.status === 200) {
          this.list = res.data;
        }
      })
    },
    projectClue() {
      this.$postData("projectClue", this.form).then(res => {
        if (res.status === 200) {
          this.list2 = res.data;
        }
      })
    },
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>