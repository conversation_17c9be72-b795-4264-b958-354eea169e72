<template>
    <div>
        <div>
            <el-form :inline="true" class="demo-form-inline">
                <div style="margin: 20px 10px 10px 10px">
                    <el-form-item label="经纪人姓名">
                        <el-input v-model="dom.agentName" placeholder="请精准输入经纪人姓名" clearable
                                  @keyup.enter.native="onQuery"></el-input>
                    </el-form-item>
                    <el-form-item label="经纪人工号">
                        <el-input v-model="dom.agentNo" placeholder="请精准输入经纪人工号" clearable
                                  @keyup.enter.native="onQuery"></el-input>
                    </el-form-item>
                    <el-form-item label="门店类型">
                        <el-select ref="storeTypeSel" filterable v-model="dom.storeType"
                                   placeholder="请选择门店类型" @change="changeStoreType">
                            <el-option
                                    v-for="(item,index) in storeTypeList"
                                    :key="index"
                                    :label="item.text"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="结算时间">
                        <el-date-picker
                                style="width: 280px"
                                :clearable="false"
                                v-model="dom.settlementTime"
                                type="daterange"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                :default-time="['00:00:00', '23:59:59']"
                                range-separator="~"
                                @change="changeSettlementTime"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                </div>
                <div style="display: flex;justify-content: space-between;">
                    <div>
                    </div>
                    <div>
                        <el-form-item>
                            <el-button icon="el-icon-search" type="primary" @click="onQuery" v-loading="loading"
                                       :disabled="loading">
                                查询
                            </el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button icon="el-icon-refresh" @click="reset" v-loading="loading" :disabled="loading">
                                重置
                            </el-button>
                        </el-form-item>
                        <el-form-item v-if="roleId != null && roleId === '1'">
                            <!--<el-button icon="el-icon-download" type="success" @click="exportExcel" v-loading="loading"-->
                            <!--           :disabled="loading">-->
                            <!--    导出-->
                            <!--</el-button>-->
                            <div class="exportCls" v-if="showExcelBtn" @click="handleShowExcelBtn()">
                                <download-excel class="blueBtn"
                                                :data="list"
                                                :fields="jsonFields"
                                                worksheet="sheet1"
                                                name="二野结算数据"
                                >
                                    <i class="el-icon-download el-icon--right"></i>&nbsp;导出
                                </download-excel>
                            </div>
                            <el-button icon="el-icon-download" type="success"
                                       v-loading="loading"
                                       v-if="!showExcelBtn"
                                       :disabled="loading">
                                导出
                            </el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button icon="el-icon-money" type="primary"
                                       @click="isOpenDrawer = true"
                                       v-loading="loading"
                                       :disabled="loading">
                                分析
                            </el-button>
                        </el-form-item>
                    </div>
                </div>
            </el-form>
        </div>
        <div>
            <div>
                <el-row>
                    <center>
                        <el-col :span="8">
                            <el-card shadow="hover">
                                总营业额: <span class="allAmount">￥{{ systemSettlementAmount }}</span>
                                <span style="color: #6f7180">(均值: ￥<span style="color: #f00f14">{{ Number(systemSettlementAmount / list.length).toFixed(2) }}</span>)</span>
                            </el-card>
                        </el-col>
                        <el-col :span="8">
                            <el-card shadow="hover">
                                总分期额: <span class="allAmount">￥{{ installmentRevenue }}</span>
                                <span style="color: #6f7180">(均值: ￥<span style="color: #f00f14">{{ Number(installmentRevenue / list.length).toFixed(2) }}</span>)</span>
                            </el-card>
                        </el-col>
                        <el-col :span="8">
                            <el-card shadow="hover">
                                总退款额: <span class="allAmount">￥{{ refundAmount }}</span>
                                <span style="color: #6f7180">(均值: ￥<span style="color: #f00f14">{{ Number(refundAmount / list.length).toFixed(2) }}</span>)</span>
                            </el-card>
                        </el-col>
                    </center>
                </el-row>
            </div>
            <div>
                <el-row>
                    <center>
                        <el-col :span="8">
                            <el-card shadow="hover">
                                总提成: <span class="allAmount">￥{{ totalCommission }}</span>
                                <span style="color: #6f7180">(均值: ￥<span style="color: #f00f14">{{ Number(totalCommission / list.length).toFixed(2) }}</span>)</span>
                            </el-card>
                        </el-col>
                        <el-col :span="8">
                            <el-card shadow="hover">
                                总奖金: <span class="allAmount">￥{{ totalBonus }}</span>
                                <span style="color: #6f7180">(均值: ￥<span style="color: #f00f14">{{ Number(totalBonus / list.length).toFixed(2) }}</span>)</span>
                            </el-card>
                        </el-col>
                        <el-col :span="8">
                            <el-card shadow="hover">
                                总工资: <span class="allAmount">￥{{ totalWages }}</span>
                                <span style="color: #6f7180">(均值: ￥<span style="color: #f00f14">{{ Number(totalWages / list.length).toFixed(2) }}</span>)</span>
                            </el-card>
                        </el-col>
                    </center>
                </el-row>
            </div>
            <div>
                <el-table :data="list" :header-cell-style="{background:'#d2d3d5'}" border class="table"
                          ref="multipleTable"
                          v-loading="loading">
                    <el-table-column
                            label="序号"
                            header-align="center"
                            align="center"
                            type="index"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.$index + 1 }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="agentName"
                            header-align="center"
                            align="center"
                            label="经纪人姓名"
                    >
                        <template slot-scope="scope">
                            <!--<el-link type="primary">{{scope.row.servicePersonName }}[{{scope.row.servicePersonNo}}]</el-link>-->
                            <span>{{scope.row.agentName }}[{{scope.row.agentNo}}]</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="right"
                            prop="systemSettlementAmount"
                            label="系统结算金额"
                    >
                        <template slot-scope="scope">
                            <span><span style="color: red;">￥</span>{{ scope.row.systemSettlementAmount }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="right"
                            prop="refundAmount"
                            label="退款金额"
                    >
                    </el-table-column>
                    <el-table-column
                            align="right"
                            prop="deductionAmount"
                            label="保姆签约扣减额"
                    >
                        <template slot-scope="scope">
                            <span>{{ Number(scope.row.deductionAmount).toFixed(2) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="right"
                            prop="installmentRevenue"
                            label="分期营收"
                    >
                    </el-table-column>
                    <el-table-column
                            align="right"
                            prop="installmentCommission"
                            label="分期提成"
                    >
                        <template slot-scope="scope">
                            <span>{{ Number(scope.row.installmentCommission).toFixed(2) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="right"
                            prop="fullPaymentRevenue"
                            label="实际业绩"
                    >
                    </el-table-column>
                    <el-table-column
                            align="right"
                            prop="fullPaymentCommission"
                            label="业绩提成"
                    >
                        <template slot-scope="scope">
                            <span>{{ Number(scope.row.fullPaymentCommission).toFixed(2) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="right"
                            prop="postSaleBonus"
                            label="售后奖励">
                    </el-table-column>
                    <el-table-column
                            align="right"
                            prop="recruitEverydayBonus"
                            label="招聘奖励[每日]">
                        <template slot-scope="scope">
                            <span>{{ Number(scope.row.recruitEverydayBonus).toFixed(2) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="right"
                            prop="recruitAllBonus"
                            label="招聘总奖励[10%]">
                        <template slot-scope="scope">
                            <span>{{ Number(scope.row.recruitAllBonus).toFixed(2) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="right"
                            prop="orderNeedsBonus"
                            label="线索奖励">
                    </el-table-column>
                    <el-table-column
                            width="80"
                            align="center"
                            label="操作">
                        <template slot-scope="scope">
                            <!--<el-button size="mini" @click="" type="primary">查看明细</el-button>-->
                            <a size="mini" @click="getDetail(scope.row)">查看明细</a>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!--<el-pagination-->
            <!--        style="margin-top: 10px;"-->
            <!--        @size-change="onPageSizeChange"-->
            <!--        @current-change="onChange"-->
            <!--        :current-page="pageInfo.current"-->
            <!--        :page-sizes="pageSizeOpts"-->
            <!--        :page-size="pageInfo.size"-->
            <!--        layout="->, total, prev, pager, next, sizes"-->
            <!--        :total="pageInfo.total">-->
            <!--</el-pagination>-->
            <br/>
            <br/>
        </div>
    </div>
</template>

<script>
    import {dateFormat} from 'vux'
    import reqs from "../../../config/axios";

    export default {
        name: "erYeSettlement",
        data() {
            return {
                showExcelBtn: true,
                // 接口请求前缀地址
                requestPrefixUrl: null,
                roleId: localStorage.getItem('roleId'),
                // 全局接口请求控制 加载动画
                loading: false,
                isOpenDrawer: false,
                // 表格数据
                list: [],
                systemSettlementAmount: 0,
                installmentRevenue: 0,
                refundAmount: 0,
                totalCommission: 0,
                totalBonus: 0,
                totalWages: 0,
                storeTypeList: [
                    {"text": "全部", "value": null},
                    {"text": "平台", "value": 0},
                    {"text": "直营门店", "value": 1},
                    {"text": "加盟店", "value": 2}
                ],
                // 分页选择项
                // pageSizeOpts: [10, 15, 20, 30, 50, 100],
                // 分页初始化项
                // pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                // 接口请求条件
                dom: {
                    storeType: 1,
                    agentName: null,
                    agentNo: null,
                    settlementTime: null,
                    paySettlementStartTime: null,
                    paySettlementEndTime: null,

                    eId: Number(localStorage.getItem('id')),

                    // size: 15,
                    // current: 1
                },
                jsonFields: {
                    "经纪人姓名": "agentName",
                    "经纪人工号": "agentNo",
                    "系统结算金额": "systemSettlementAmount",
                    "退款金额": "refundAmount",
                    "保姆签约扣减额": "deductionAmount",
                    "实际业绩": "realRevenue",
                    "实际业绩提成": "realCommission",
                    "分期营收": "installmentRevenue",
                    "分期提成": "installmentCommission",
                    "售后奖励": "postSaleBonus",
                    "招聘奖励[每日]": "recruitEverydayBonus",
                    "招聘奖励[10%]": "recruitAllBonus",
                    "线索奖励": "orderNeedsBonus",
                },
            }
        },
        async created() {
        },
        async mounted() {
            this.requestPrefixUrl = reqs.otherUrl;
            await this.initSettlementTime();
            this.onQuery();
        },
        methods: {
            handleShowExcelBtn() {
                this.showExcelBtn = false;
                this.loading = true;
                setTimeout(() => {
                    this.showExcelBtn = true;
                    this.loading = false;
                }, 3000);
            },
            getDetail(data) {
            },
            initSettlementTime() {
                const date = new Date();
                date.setDate(date.getDate() - 7);
                this.dom.paySettlementStartTime = dateFormat(date, 'YYYY-MM-DD') + ' 00:00:00';
                this.dom.paySettlementEndTime = dateFormat(new Date(), 'YYYY-MM-DD') + ' 23:59:59';
                this.dom.settlementTime = [this.dom.paySettlementStartTime, this.dom.paySettlementEndTime];
            },
            onQuery() {
                // this.pageInfo.current = 1;
                // this.dom.current = 1;
                this.getData();
            },
            getData() {
              console.log(JSON.stringify(this.dom))
                this.loading = true;
                this.showExcelBtn = false;
                const url = this.requestPrefixUrl + '/admins/getErYeSettlementList';
                this.$postDataSelfUrl(url, JSON.stringify(this.dom), {}).then(res => {
                    if (res.code === 0) {
                        const datum = res.data;
                        if (datum.erYeSettlementList == null) {
                            datum.erYeSettlementList = [];
                        }
                        this.list = datum.erYeSettlementList;
                        this.systemSettlementAmount = datum.systemSettlementAmount;
                        this.installmentRevenue = datum.installmentRevenue;
                        this.refundAmount = datum.refundAmount;
                        this.totalCommission = datum.totalCommission;
                        this.totalBonus = datum.totalBonus;
                        this.totalWages = datum.totalWages;
                    } else {
                        this.$message.info('统计失败,' + res.msg);
                    }
                }).finally(() => {
                    setTimeout(() => {
                        this.loading = false;
                        this.showExcelBtn = true;
                    }, 1000);
                });
            },
            async reset() {
                this.dom.storeType = 1;
                this.dom.agentName = null;
                this.dom.agentNo = null;

                this.dom.settlementTime = null;
                this.dom.paySettlementStartTime = null;
                this.dom.paySettlementEndTime = null;

                await this.initSettlementTime();
                await this.onQuery();
            },
            /**
             * 重载分页数, pageSize 改变时会触发
             */
            // onPageSizeChange(size) {
            //     this.dom.current = 1;
            //     this.pageInfo.current = 1;
            //     this.dom.size = size;
            //     this.getData();
            // },
            /**
             * 切换其他页, currentPage 改变时会触发
             */
            // onChange(index) {
            //     this.dom.current = index;
            //     this.getData();
            // },
            changeStoreType() {
            },
            /**
             * 时间搜索条件赋值
             */
            changeSettlementTime(v, s) {
                if (v == null) {
                    this.dom.paySettlementStartTime = null;
                    this.dom.paySettlementEndTime = null;
                    return;
                }
                this.dom.paySettlementStartTime = v[0];
                this.dom.paySettlementEndTime = v[1];
            },
            exportExcel() {
                this.loading = true;
                this.showExcelBtn = false;
                const url = this.requestPrefixUrl + '/admins/exportErYeSettlement';
                this.$postDataSelfUrl(url, JSON.stringify(this.dom), {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tableName: "二野结算数据",
                        res: res
                    });
                }).finally(() => {
                    setTimeout(() => {
                        this.showExcelBtn = true;
                        this.loading = false;
                    }, 10000);
                });
            },
            blobExport({tableName, res}) {
                this.$message.success('正在导出中,可能需要1~2分钟，请稍等...');
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tableName + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
                setTimeout(() => {
                    this.loading = false;
                    this.showExcelBtn = true;
                }, 3000);
            }
        }
    }
</script>

<style scoped>

    .allAmount {
        font-weight: bold;
        /*padding-left: 10px;*/
        color: red;
        font-size: 24px;
    }

    .exportCls {
        padding: 0 15px;
        border: 1px solid #67C23A;
        border-radius: 3px;
        background: #67C23A;
        color: #fff;
        cursor: pointer;
        font-size: 12px;
    }
</style>
