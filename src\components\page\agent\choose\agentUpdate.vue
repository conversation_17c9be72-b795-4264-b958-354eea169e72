<template style="background-color: #000">

            <el-tabs type="card" :stretch="true" >
                <el-tab-pane label="基本信息">
                <Row>
                <Col span="24">
                        <el-form ref="dom" :model="dom" :rules="ruleValidate">
                            <Row>
                            <Col span="4" style="">
                                <center>
                                    <div class="imgs">
                                <div style="padding-bottom: 10px"><h3>员工头像</h3></div>

                                <el-dialog :visible.sync="dialogImg">
                                    <img width="100%" :src="dom.headPortrait" alt="" >
                                </el-dialog>
                                <el-image
                                        @click="dialogImg=true"
                                        style="width: 100px; height: 150px;min-width: 150px;"
                                        :src="dom.headPortrait"
                                >
                                </el-image>
                                <el-upload
                                        class="upload-demo"
                                        action="https://biapi.xiaoyujia.com/files/uploadFiles"
                                        list-type="picture"
                                        :on-success="handleAvatarSuccess">
                                    <el-button size="small" type="primary">更改头像</el-button>
                                </el-upload>
                                    </div>


                                </center>
                            </Col>
                                <Col span="4" style="">
                                    <center>

                                        <div class="imgs">
                                        <div style="padding-bottom: 10px"><h3>微信二维码</h3></div>

                                        <el-dialog :visible.sync="wxdialogImg">
                                            <img width="100%" :src="dom.weixinimg" alt="" >
                                        </el-dialog>
                                        <el-image
                                                @click="wxdialogImg=true"
                                                style="width: 100px; height: 150px;min-width: 150px;"
                                                :src="dom.weixinimg"
                                        >
                                        </el-image>

                                        <el-upload
                                                class="upload-demo"
                                                action="https://biapi.xiaoyujia.com/files/uploadFiles"
                                                list-type="picture"
                                                :on-success="handleWeixinSuccess"
                                               >
                                            <el-button size="small" type="primary">更改二维码</el-button>
                                        </el-upload>
                                        </div>
                                    </center>
                                </Col>

                            <Col span="8">

                                    <el-form-item   prop="no" >
                                        <div class="label-name">编号:</div>
                                        <el-input placeholder="请输入编号" v-model="dom.no" style="width: 70%;">
                                        </el-input>
                                    </el-form-item >

                                    <el-form-item   prop="realName" >
                                        <div class="label-name">名称:</div>
                                        <el-input placeholder="请输入名称" v-model="dom.realName" style="width: 70%;">
                                        </el-input>
                                    </el-form-item >

                                    <el-form-item   prop="address" >
                                        <div class="label-name">地址:</div>
                                        <el-input placeholder="请输入地址"  v-model="dom.address" style="width: 70%;">
                                        </el-input>
                                    </el-form-item >

                                <el-form-item   prop="address" >
                                    <div class="label-name">积分:</div>
                                    <el-input placeholder="请输入积分"  type="number" v-model="sortDom.sort" style="width: 70%;">
                                    </el-input>
                                </el-form-item >

                            </Col>
                                <Col span="8">
                                    <el-form-item   prop="phone">
                                        <div class="label-name">手机号:</div>
                                        <el-input placeholder="请输入联系方式" v-model="dom.phone"style="width: 70%;">
                                        </el-input>
                                    </el-form-item >

                                    <el-form-item   prop="entryTime" >
                                        <div class="label-name">入职时间:</div>
<!--                                    <span class="startWord" >入职时间：</span>-->
                                        <el-date-picker
                                                width="70%"
                                                v-model="dom.entryTime"
                                                type="datetime"
                                                value-format="yyyy-MM-dd HH:mm:ss"
                                                placeholder="入职时间">
                                        </el-date-picker>
                                    </el-form-item >

                                    <el-form-item   prop="state" >
                                        <div class="label-name">员工状态:</div>
<!--                                    <span class="startWord">员工状态：</span>-->
                                        <el-select v-model="dom.state" clearable placeholder="请选择" style="width: 70%;">
                                            <el-option
                                                    v-for="item in states"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item >
                                    <el-form-item>
                                        <div class="label-name">城市区域:</div>
                                        <city-choose @init-choose="initChooseProject" :getcity=setCity></city-choose>
                                    </el-form-item>
                                </Col>

                                    <Col span="24" style="padding-top: 10px;">

                                        <el-form-item   prop="workRemark"  label="一句话介绍自己">

                                            <el-input
                                                    style=" width: 70%"
                                                    type="textarea"
                                                    autosize
                                                    placeholder="请输入内容"
                                                    v-model="dom.workRemark">
                                            </el-input>
                                        </el-form-item >
                                    </Col>

                            </Row>

                                    <div style="margin-right: 100px;float: right">
                                        <Button type="primary" @click="save('dom')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </div>
                                </el-form>
                    </Col>


            </Row>
                </el-tab-pane>
                <el-tab-pane label="合作保姆">
                    <el-form ref="form">

                        <el-row>
                            <el-col :span="8">
                                <el-form-item >
                                    <el-input
                                            v-model="getmodel.No"
                                            placeholder="编号"
                                            style="width:190px"
                                            class="handle-input mr10"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item >
                                    <el-input
                                            v-model="getmodel.RealName"
                                            placeholder="名称"
                                            style="width:200px"
                                            class="handle-input mr10"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" >

                                <el-form-item>
                                    <el-button type="success"  round @click="query()">搜索</el-button>
                                    <el-button type="info"  round @click="re()">重置查询</el-button>
                                    <el-button type="info"  round @click="getData()">刷新</el-button>
                                </el-form-item>

                            </el-col>
                        </el-row>

                    </el-form>
                    <div class="table">
                            <el-table :data="list" border class="table" ref="multipleTable" >
                                <el-table-column
                                        type="selection"
                                        width="35">
                                </el-table-column>
                                <el-table-column
                                        prop="no"
                                        label="编号"
                                        width="120"

                                ></el-table-column>
                                <el-table-column
                                        fixed="left"
                                        prop="realName"
                                        label="名称"
                                        width="150"
                                ></el-table-column>
                                <el-table-column
                                        prop="phone"
                                        label="联系方式"
                                        width="150"
                                ></el-table-column>
                                <el-table-column
                                        prop="address"
                                        label="地址"

                                ></el-table-column>

                                <!--                <el-table-column-->
                                <!--                        prop="workRemark"-->
                                <!--                        label="工作记录"-->
                                <!--                        width="150"-->
                                <!--                >-->
                                <!--                </el-table-column>-->
                                <el-table-column
                                        prop="sort"
                                        label="排序积分"
                                        width="100"
                                ></el-table-column>
                                <el-table-column
                                        label="操作"
                                        min-width="120">
                                    <template slot-scope="scope">
                                        <el-button size="mini" @click="edit(scope.row)" type="primary">编辑</el-button>
                                        <el-button size="mini" @click="upTo(scope.row.id)" type="primary">解绑</el-button>
                                    </template>

                                </el-table-column>
                            </el-table>
                        <Modal v-model="saveModal" class="Modal" :width="screenWidth"   title="保姆详情" :mask-closable="false" >
                            <div class="addBody">
                                <baomuUpdate v-if="saveModal" @init-choose="initChooseProject"
                                             @close-modal="closeCurrModal" :model="show"></baomuUpdate>
                            </div>
                            <div slot="footer">
                            </div>
                        </Modal>
                            <div class="pagination">
                                <Page :total="pageInfo.total"
                                      @on-change="onChange"
                                      :show-total="true"
                                      :show-sizer="true"
                                      :page-size-opts="pageSizeOpts"
                                      @on-page-size-change="onPageSizeChange"
                                      :page-size="pageInfo.size"/>
                            </div>
                        </div>

                </el-tab-pane>
                <el-tab-pane label="服务站点">
                    <el-form ref="form">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item >
                                    <el-input
                                            v-model="sitemodel.name"
                                            placeholder="名称"
                                            style="width:190px"
                                            class="handle-input mr10"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item >
                                    <el-input
                                            v-model="sitemodel.street"
                                            placeholder="区域"
                                            style="width:200px"
                                            class="handle-input mr10"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" >
                                <el-form-item>
                                    <el-button type="success"  round @click="sitequery()">搜索</el-button>
                                    <el-button type="info"  round @click="sitere()">重置查询</el-button>
                                </el-form-item>

                            </el-col>
                        </el-row>

                    </el-form>
                    <el-table :data="sitelist" border class="table" ref="multipleTable">
                        <el-table-column
                                type="selection"
                                width="35">
                        </el-table-column>
                        <el-table-column

                                prop="name"
                                label="名称"
                                width="120"
                        ></el-table-column>
                        <el-table-column
                                prop="street"
                                label="区域"
                        ></el-table-column>
                        <el-table-column
                                prop="lng"
                                label="经度"
                                width="120"
                        ></el-table-column>
                        <el-table-column
                                prop="lat"
                                label="纬度"
                                width="120"
                        ></el-table-column>
                        <el-table-column
                                prop="createTime"
                                label="创建时间"
                                width="180"
                        ></el-table-column>

                    </el-table>
                    <div class="pagination">
                        <Page :total="sitepageInfo.total"
                              @on-change="onsiteChange"
                              :show-total="true"
                              :show-sizer="true"
                              :page-size-opts="pageSizeOpts"
                              @on-page-size-change="onsitePageSizeChange"
                              :page-size="sitepageInfo.size"/>
                    </div>

                </el-tab-pane>
            </el-tabs>

</template>


<script>
    import cityChoose from '@/components/page/agent/minichoose/cityChoose.vue'
    import baomuUpdate from '@/components/page/agent/choose/baomuUpdate.vue'
    export default {
        props:['model'],
        data() {
            return {
                show:{},
                sortDom:null,
                saveModal: false,
                states:[{
                    value:null,
                    label: '全部'
                },{
                    value:1,
                    label: '上架'
                }, {
                    value: 2,
                    label: '下架'
                }, {
                    value: 3,
                    label: '离职'
                },],
                wxdialogImg:false,
                dialogImg:false,
                setCity:{
                    cityId:this.model.cityId,
                    areaId:this.model.areaId,
                },
                screenWidth: '80%',//新增对话框 宽度
                sitelist:null,
                list:null,
                addModal:false,
                pageSizeOpts:[5,10,20],
                pageInfo: {total: 5, size: 5, current: 1, pages: 1},
                sitepageInfo: {total: 5, size: 5, current: 1, pages: 1},
                dom:this.model,
                formItem: {
                    No: null,
                    RealName: null,
                    Phone: null,
                    Address:null,
                },
                getmodel: {
                    id:null,
                    No: null,
                    RealName:null,
                    current: 1,
                    size: 5,
                    orderBy:'sort DESC'

                },
                sitemodel: {
                    id:null,
                    name: null,
                    street:null,
                    current: 1,
                    size: 5

                },
                weixin:{
                    certificateType:10,
                    certificateImg:null,
                    employeeId:null,
                },

                ruleValidate: {
                    no: [
                        {required: true, message: '请输入编号', trigger: 'change'}
                    ],
                    realName: [
                        {required: true, message: '请选择名称', trigger: 'change'}
                    ],
                    phone: [
                        {required: true, message: '请选择联系方式', trigger: 'change'},
                        { min: 11, max: 11, message: '请输入11位手机号', trigger: 'blur' }
                    ],
                    address: [
                        {required: true, message: '请输入地址', trigger: 'blur'}
                    ],

                },
            }
        },
        created: function () {
            this.sitemodel.id=this.dom.id;
            this.getmodel.id=this.dom.id;
            this.getEmployeeSort();
            this.getData();
            this.getsiteData();

        },
        components: {
            'cityChoose': cityChoose,
            'baomuUpdate': baomuUpdate,
        },
        methods: {
            upTo(baomuId){
                let dom= {
                    employeeId:localStorage.getItem("id"),
                    baomuId:baomuId
                }
                this.$postData("up_employeeToBaomu", dom, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.success("解绑成功，" + res.msg);
                        this.getData()
                    } else {
                        this.$message.error("解绑失败，" + res.msg);
                    }
                })
            },
            getEmployeeSort(){
                this.$getUrl("get_employeeSort",this.dom.id, {}).then(res => {
                    if (res.status == 200) {

                        this.sortDom=res.data

                    } else {
                        this.$message.error("失败，" + res.msg);
                    }
                })
            },
            edit(id){
                this.show=id;
                console.log(this.model)
                this.saveModal=true;
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.saveModal = false;
                this.addModal = false;
            },
            initChooseProject(data) {
                this.getData()
                this.closeCurrModal();

            },
            handleAvatarSuccess(res, file) {
                this.dom.headSource = res.data;
                this.dom.headPortrait = res.data;
            },
            handleWeixinSuccess(res, file) {
                if (res.status==200){
                    this.weixin.certificateImg=res.data;
                    this.weixin.employeeId=this.dom.id;
                    this.$postData("agent_weixinimg", this.weixin, {}).then(res => {
                        if (res.status == 200) {


                        } else {
                            this.$message.error("失败，" + res.msg);
                        }
                    })
                    this.dom.weixinimg = res.data;
                }

            },
            /*
            城市选择
             */
            initChooseProject(city) {
                if (city!=null) {
                    this.dom.cityId = city.cityId
                    this.dom.areaId = city.areaId
                }
            },
            query() {
                this.getData();
            },
            sitequery() {
                this.getsiteData();
            },
            re(){
                this.getmodel.No='';
                this.getmodel.RealName='';
                this.getData();
            },
            sitere(){
                this.sitemodel.name='';
                this.sitemodel.street='';
                this.getsiteData();
            },
            getData() {
                this.$postData("agentBaomu_page", this.getmodel, {}).then(res => {
                    if (res.status == 200) {

                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getsiteData() {
                this.$postData("site_page", this.sitemodel, {}).then(res => {
                    if (res.status == 200) {

                        this.sitelist = res.data.records;
                        this.sitepageInfo.current = res.data.current;
                        this.sitepageInfo.size = res.data.size;
                        this.sitepageInfo.total = res.data.total
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },

            save(name) {
                this.$refs[name].validate(valid => {
                    if (valid) {
                        this.$postData("save_employeeSort", this.sortDom, {}).then(res => {
                            if (res.status == 200) {

                            } else {
                                this.$message.error("修改失败，" + res.msg);
                            }
                        })

                        this.$postData("update_employee", this.dom, {}).then(res => {
                            if (res.status == 200) {
                                this.$Message.success('修改成功');
                                this.chooseThisModel();
                            } else {
                                this.$message.error("修改失败，" + res.msg);
                            }
                        })
                        console.log("验证成功")
                    }
                    else {
                        return false;
                    }
                });



            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.dom=null;
                this.$emit('init-choose', "");
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.getmodel.size = size;
                this.getData();
            },
            // 页码大小
            onsitePageSizeChange(size) {
                console.log(size)
                this.sitemodel.size = size;
                this.getsiteData();
            },
            // 跳转页码

            onChange(index) {
                console.log(index)
                this.getmodel.current = index;
                this.getData();
            },
            onsiteChange(index) {
                console.log(index)
                this.sitemodel.current = index;
                this.getsiteData();
            },


        },

    }
</script>
<style>
    {
        background: #409eff;
        color: #fff;
    }
</style>

<style scoped>

    .label-name{
        float: left;
        text-align: center;
        width: 20%;
    }
    .el-date-editor.el-input, .el-date-editor.el-input__inner {
        width: 70%;
    }
    .ivu-col-span-9{
        padding-top: 30px;
    }

    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

