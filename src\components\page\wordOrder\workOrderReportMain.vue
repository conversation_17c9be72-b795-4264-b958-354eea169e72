<template>
  <div>
    <el-tabs type="border-card">
      <el-tab-pane label="员工工单">
      <work-order-report-by-employee></work-order-report-by-employee>

      </el-tab-pane>
      <el-tab-pane label="技术工单">
      <work-order-report></work-order-report>

      </el-tab-pane>
      <el-tab-pane label="客服工单">
        <work-order-report-by-customer></work-order-report-by-customer>

      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import workOrderReport from "./workOrderReport.vue";
import workOrderReportByCustomer from "./workOrderReportByCustomer.vue";
import workOrderReportByEmployee from "./workOrderReportByEmployee.vue";
export default {
  name: "workOrderReportMain",
  components:{
    workOrderReport,
    workOrderReportByCustomer,
    workOrderReportByEmployee
  },
  data() {
    return {


    }
  },
  created() {
    // this.getData();
  },
  methods:{

  }
}
</script>

<style scoped>

</style>