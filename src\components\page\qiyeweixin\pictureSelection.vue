<template>
	<div>
		<el-form label-width="90px">
			<el-form-item>
				<el-row :gutter="20" type="flex">
					<el-col :span="12">
						<el-input placeholder="请输入内容" prefix-icon="el-icon-search" clearable width="100"
							v-model="dom.name">
						</el-input>
					</el-col>
					<el-col :span="8">
						<el-button type="primary" @click="selectByCondition()">查询</el-button>
						<el-button type="primary" plain @click="reset()">重置</el-button>
					</el-col>
				</el-row>
			</el-form-item>
			<el-form-item label="分类：">
				<el-tag class="j" :class="{'k':quick===index}" @click.native="addPoliteness(index,tag.id)"
					v-for="(tag,index) in dynamicTags" :closable="closable" :key="index" :disable-transitions="false"
					@close="handleClose(tag)">
					{{ tag.name }}
				</el-tag>
<!--				<el-input class="input-new-tag" v-if="inputVisible" v-model="inputValue" ref="saveTagInput" size="small"-->
<!--					@keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm">-->
<!--				</el-input>-->
			</el-form-item>
			<el-form-item label="管理模块：">
				<el-button class="button-new-tag" size="small" @click="showInput">新建分类</el-button>

				<el-button v-if=" roleId==='1'" type="info" icon="el-icon-edit-outline" @click="closable=!closable"
					plain>
					编辑分类
				</el-button>
				<el-button type="warning" v-if="!delectImg" icon="el-icon-delete" @click="delectImg=!delectImg" plain>
					删除图片</el-button>
				<el-button v-if="delectImg" @click="delectImg=!delectImg" plain>取消删除</el-button>
				<el-button type="danger" v-if="delectImg" icon="el-icon-delete" @click="determine()" plain>确定删除
				</el-button>

				<el-select v-model="dom.orderBy" style="width: 100px;margin-left:10px;" @change="changOrderBy">
					<el-option v-for="(item,index) in orderByList" :key="index" :label="item.text" :value="item.value">
					</el-option>
				</el-select>
			</el-form-item>
		</el-form>


   <el-dialog
        title="提示"
        :visible.sync="dialogVisibleTag"
        width="30%">
     <el-row>
       <el-col :span="12"><div>
       可展示的角色:  <el-select v-model="selectRoles" filterable  multiple placeholder="针对角色展示分类">
           <el-option
               v-for="item in roles"
               :key="item.value"
               :label="item.label"
               :value="item.value">
           </el-option>
         </el-select>
       </div></el-col>
     </el-row>
     <br>
     <el-row>
       <el-col :span="12"><div>
         分类名称:  <el-input v-model="inputValue" placeholder="请输入内容"></el-input>
       </div></el-col>
     </el-row>





      <span slot="footer" class="dialog-footer">
    <el-button @click="dialogVisibleTag = false">取 消</el-button>
    <el-button type="primary" @click="handleInputConfirm()" >确 定</el-button>
  </span>
    </el-dialog>



		<el-row v-loading="loading" :gutter="20">
			<el-col :span="6" v-for="(item,index) in imgList" :key="index" style="margin-left: 10px">
				<el-card :body-style="{ padding: '0px'}" shadow="hover">

					<el-row>
						<el-col :span="12">
							<div style="font-size: 15px">浏览数：{{item.views}}</div>
						</el-col>
						<el-col :span="12">
							<div style="font-size: 15px">点赞数：{{item.likes}}</div>
						</el-col>
					</el-row>

					<el-row>
						<el-col :span="12">
							<div style="font-size: 15px">使用量：{{item.useNum}}</div>
						</el-col>
						<el-col :span="12">
							<div style="font-size: 15px">订单数：{{item.orders}}</div>
						</el-col>
					</el-row>




					<el-image class="image" :src="item.img" fit="contain"></el-image>
					<div style="padding:  0 0 0 10px">
						<el-checkbox-group v-model="checkList" v-if="delectImg" size="large">
							<el-checkbox-button :label="item.id">
								<span class="itemtext"> {{ item.name }}</span>
							</el-checkbox-button>
						</el-checkbox-group>
						<span class="itemtext" v-if="!delectImg">{{ item.name }}</span>
						<el-button type="primary" v-if="choiceImg" plain round class="button"
							@click="determinePicture(item.qiyeImg)">选择图片
						</el-button>
						<div>
							<el-button type="success" style="float: left" @click="topping(item.id,1)"
								v-if="item.isTop!==1">置顶</el-button>
							<el-button type="danger" style="float: left" @click="topping(item.id,0)"
								v-if="item.isTop==1">取消置顶</el-button>
							<el-button type="primary" style="float: right" @click="showDialog(item)">查看文案</el-button>
							<el-button type="warning" style="float: right" @click="edit(item)">编辑内容</el-button>
						</div>

					</div>
				</el-card>
			</el-col>
		</el-row>
		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="this.dom.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="9" />
		</div>


		<el-dialog title="文案内容" :visible.sync="dialogVisible" width="50%" style="height: 600px" center>
			<el-input type="textarea" autosize :autosize="{ minRows: 14}" v-model="contents"></el-input>
		</el-dialog>



		<el-dialog title="素材编辑" :visible.sync="dialogVisibleSC" width="50%" center>
			<el-form label-width="80px">
				<el-form-item label="选择图片">
					(不上传则默认原图)
					<el-upload action="''" ref="upload" :limit="1" :http-request="upload" list-type="picture-card"
						:auto-upload="true">
						<i slot="default" class="el-icon-plus"></i>
					</el-upload>
				</el-form-item>
				<el-form-item label="图片标签">
					<div>
						<el-radio-group v-model="form.dataLabelId" v-for="(tag,index) in dynamicTags" :key="index"
							@change="radioChange(tag.name)">
							<el-radio-button :label="tag.id">{{ tag.name }}</el-radio-button>
						</el-radio-group>
					</div>
				</el-form-item>
				<el-form-item label="图片名称">
					<el-input v-model="form.name" placeholder="请输入内容"></el-input>
				</el-form-item>
				<el-form-item label="文案">
					<el-input v-model="form.content" type="textarea" placeholder="请输入内容"></el-input>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button type="primary" @click="submitForm('ruleForm')">保存</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
	import axios from "axios";

	export default {
		name: "pictureSelection",
		props: ['content', 'choiceImg'],
		data() {
			return {
				//标签
				dynamicTags: [],
        selectRoles:[],
        dialogVisibleTag: false,
				dialogVisible: false,
				dialogVisibleSC: false,
				contents: '',
				// inputVisible: false,
				closable: false,
				inputValue: '',
				imgList: [],
				checkList: [],
				quick: null,
				loading: false,
				delectImg: false,
				pageSizeOpts: [9, 18, 27],
				pageInfo: {
					total: 9,
					size: 9,
					current: 1,
					pages: 1
				},
        roles:[],
				dom: {
					type: 1,
					img: null,
					qiyeImg: null,
					operatorId: null,
					name: null,
					size: 9,
					current: 1,
					orderBy: 0
				},
				form: {
					type: 1,
					img: null,
					qiyeImg: null,
					operatorId: null,
					dataLabelId: null,
					dataLabelName: null,
					name: null,
					size: 9,
					current: 1,
					content: null
				},
				orderByList: [{
					value: 0,
					text: "默认排序"
				}, {
					value: 4,
					text: "使用量"
				}, {
					value: 2,
					text: "浏览量"
				}, {
					value: 5,
					text: "点赞量"
				}, {
					value: 3,
					text: "订单量"
				}]
			}
		},
		created() {
      this.getAllRole();
			this.selectByCondition()
			this.selectByType('1')
			this.dom.operatorId = localStorage.getItem("id")
			this.roleId = localStorage.getItem("roleId")
		},
		methods: {
      getAllRole(){
        this.$getData("getAllRole").then(res => {
          let resultArr = [];
          res.data.forEach(v =>{
            let param = {
              value:v.id,
              label:v.name
            }
            resultArr.push(param);
          })
          this.roles = resultArr;
        });
      },
			edit(item) {
				this.form = item;
				this.dialogVisibleSC = true;
			},
			topping(id, top) {
				console.log("输出置顶ID", id)
				this.$postData("toppingQiyeData", {
					id: id,
					isTop: top
				}).then(res => {
					if (res.status === 200) {
						if (top == 1) {
							this.$message.success("置顶成功！");
						} else {
							this.$message.success("取消置顶成功！");
						}
						this.selectByCondition()
					} else {
						if (top == 1) {
							this.$message.error("置顶失败！");
						} else {
							this.$message.error("取消置顶失败！");
						}
					}
				})
			},
			// 排序
			changOrderBy(e) {
				this.dom.orderBy = e
				this.selectByCondition()
			},
			showDialog(item) {
				this.dialogVisible = true;
				this.contents = item.content;
			},
			//所有图片
			selectByCondition() {
				this.$postData("selectByCondition", this.dom).then(res => {
					this.loading = false;
					if (res.status === 200) {
						this.imgList = res.data.records;
						this.pageInfo.current = res.data.current;
						this.pageInfo.size = res.data.size;
						this.pageInfo.total = res.data.total
					}
				})
			},
			//删除图片
			determine() {
				this.delectImg = !this.delectImg
				this.$postData("delectQiyeData", this.checkList).then(res => {
					if (res.status === 200) {
						this.reset()
						if (res.data === 1) {
							this.$message.success("删除图片成功");
						} else {
							this.$message.error("删除图片失败");
						}
					}
				})
			},
			//选择图片
			determinePicture(img) {
				this.$emit("picture", img)
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.dom.current = index;
				this.selectByCondition();
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.dom.size = size;
				this.selectByCondition();
			},
			//重置
			reset() {
				this.dom.img = null
				this.dom.qiyeImg = null
				this.dom.name = null
				this.dom.operatorId = null
				this.dom.dataLabelId = null
				this.dom.dataLabelName = null
				this.quick = null
				this.dom.orderBy = 0
				this.selectByCondition()
			},
			//标签
			selectByType(type) {
				this.$getData("selectByType", {
					type: type
				}).then(res => {
					if (res.status === 200) {
						this.dynamicTags = res.data
					}
				})
			},
			//根据选中标签查找图片
			addPoliteness(index, id) {
				this.quick = index
				this.dom.current = 1;
				this.dom.dataLabelId = id
				this.selectByCondition()
			},
			//标签删除
			handleClose(tag) {
				this.delectQiyeDataLabe(tag.id)
				this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
			},
			//标签删除
			delectQiyeDataLabe(id) {
				this.$getData("delectQiyeDataLabe", {
					id: id
				}).then(res => {
					if (res.status === 200) {
						if (res.data === 1) {
							this.reset()
							this.$message({
								message: '删除成功',
								type: 'success'
							});
						}
					}
				})
			},
			//新建空白标签
			showInput() {
        this.dialogVisibleTag = true;
				// this.inputVisible = true;
				// this.$nextTick(_ => {
				// 	this.$refs.saveTagInput.$refs.input.focus();
				// });
			},
			//添加标签
			handleInputConfirm() {
				// let inputValue = this.inputValue;
				// if (null !== inputValue && '' !== inputValue) {
				// 	this.dynamicTags.push(inputValue);
				// 	//添加标签
				// 	let qiyeDataLabe = {
				// 		name: inputValue,
				// 		type: 1,
				// 	}
				//
				// }
				// this.inputVisible = false;
				// this.inputValue = '';
        if (this.selectRoles.length < 1){
          return this.$message({
            message: '角色分类不能为空',
            type: 'error'
          });
        }
        if (!this.inputValue){
          return this.$message({
            message: '标签名称不能为空',
            type: 'error'
          });
        }
        	let qiyeDataLabe = {
        		name: this.inputValue,
        		type: 1,
            roles:this.selectRoles.toString()
        	}
        this.dialogVisibleTag = false;
        this.$postData("addQiyeDataLabe", qiyeDataLabe).then(res => {
          if (res.status === 200) {
            this.qiyeLabelGroup = res.data
            this.selectByType('1')
          }
        })
        this.inputValue = '';
			},
			//覆盖默认的上传行为，可以自定义上传
			async upload(param) {
				var formData = new FormData();
				formData.append("flies", param.file);
				await axios.post(`/qiyeCustomer/ploadPicturesu`, formData, {
					headers: {
						'Content-Type': 'multipart/form-data'
					}
				}).then((res) => {
					if (res.status === 200) {
						this.form.img = res.data
					}
				})
				await axios.post(`/qiyeCustomer/qiyeUploadPictures`, formData, {
					headers: {
						'Content-Type': 'multipart/form-data'
					}
				}).then((res) => {
					if (res.status === 200) {
						this.form.qiyeImg = res.data
					}
				})
			},
			radioChange(name) {
				this.form.dataLabelName = name
			},
			addQiyeData() {
				this.$postData("addQiyeData", this.form).then(res => {
					if (res.status === 200) {
						this.$message.success("编辑成功");
						this.dialogVisibleSC = false;
						this.reload();
					}
				})
			},
			//手动上传文件列表
			submitForm() {
				if (null == this.form.dataLabelId) {
					return this.$message.error('请选择图片标签');
				}
				if (null == this.form.name) {
					return this.$message.error('请输入图片名称');
				}
				// this.$refs.upload.submit();
				this.addQiyeData()
			},
		}
	}
</script>

<style scoped>
	.image {
		width: 100%;
		height: 180px;
		display: block;
	}

	.button {
		float: right;
	}

	.j {
		background: white;
		border: 1px solid #999999;
		color: #999999;
	}

	.k {
		background: #FFE102;
		border: 1px solid #FFE102;
		color: #353535;
		display: inline-block;
		*display: inline;
		*zoom: 1;
	}

	.button-new-tag {

		height: 32px;
		line-height: 30px;
		padding-top: 0;
		padding-bottom: 0;
	}

	.input-new-tag {
		width: 90px;
		margin-left: 10px;
		vertical-align: bottom;
	}

	.el-tag+.el-tag {
		margin-left: 10px;
	}

	.itemtext {
		white-space: nowrap;
		/*强制单行显示*/
		text-overflow: ellipsis;
		/*超出部分省略号表示*/
		overflow: hidden;
		/*超出部分隐藏*/
		width: 260px;
		/*设置显示的最大宽度*/
		display: inline-block;
		font-size: 20px;
	}
</style>
