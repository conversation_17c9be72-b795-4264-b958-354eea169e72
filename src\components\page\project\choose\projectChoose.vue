<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col span="24">
                    <div>
                        <div id="operBtnDiv">
                            <div id="searchDiv">
                                <Form ref="formItem" :model="formItem" :label-width="80" label-position="right"
                                      :rules="ruleValidate">
                                    <Row>
                                        <Col :span="12">
                                            <FormItem label="项目编号" prop="projectNo">
                                                <Input placeholder="请输入项目编号" v-model="formItem.projectNo">
                                                </Input>
                                            </FormItem>
                                        </Col>
                                        <Col :span="12">
                                                <FormItem label="项目资金" prop="capital">
                                                    <Input placeholder="请输入项目资金" v-model="formItem.capital">
                                                    </Input>
                                                </FormItem>
                                        </Col>
                                        </Row>

                                    <Row>
                                        <Col :span="12">
                                            <FormItem label="项目名称" prop="name">
                                                <Input placeholder="请输入项目名称" v-model="formItem.name">
                                                </Input>
                                            </FormItem>
                                        </Col>

                                        <Col :span="12">
                                            <FormItem label="立项时间" prop="createTime">
                                                <DatePicker type="date" placeholder="请选择立项时间" v-model="formItem.createTime"
                                                            style="width: 200px"></DatePicker>
                                            </FormItem>
                                        </Col>
                                    </Row>

                                    <Row>
                                        <Col :span="12">
                                            <FormItem label="项目性质" prop="natureType">
                                                <Select v-model="formItem.natureType" filterable style="width: 200px">
                                                    <Option v-for="item in natureList" :value="item.id">{{ item.name}}</Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                        <Col :span="12">
                                            <FormItem label="收入类型" prop="incomeType">
                                                <Select v-model="formItem.incomeType" filterable style="width: 200px">
                                                    <Option v-for="item in incomeList" :value="item.id">{{ item.name}}</Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                    </Row>

                                    <Row>
                                        <Col :span="12">
                                            <FormItem label="扣减类型" prop="reduces">
                                                <Select v-model="formItem.reduces" filterable style="width: 200px"
                                                        multiple>
                                                    <Option v-for="item in reducesList" :value="item.id">{{
                                                        item.name}}
                                                    </Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                        <Col :span="12">
                                            <FormItem label="结算类型" prop="cycleType">
                                                <Select v-model="formItem.cycleType" filterable style="width: 200px">
                                                    <Option v-for="item in cycleList" :value="item.id">{{ item.name}}
                                                    </Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col :span="12">

                                            <FormItem label="负责人" prop="employeeId">
                                                <!--  <Input placeholder="请选择负责人" v-model="formItem.employeeName"
                                                         @on-focus="userModal=true" readonly="readonly">
                                                  </Input>-->
                                                <Select v-model="formItem.employeeId" filterable style="width: 200px">
                                                    <Option v-for="item in employees" :value="item.id">{{
                                                        item.realName}}
                                                    </Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                        <Col :span="12">
                                            <FormItem prop="cycleStartTime" label="周期时间">
                                                <DatePicker type="daterange" placeholder="周期时间" split-panels
                                                            v-model="formItem.cycleStartTime"></DatePicker>
                                            </FormItem>
                                        </Col>
                                    </Row>

                                    <Row>
                                        <Col :span="12">
                                            <FormItem label="所属门店" prop="stores">
                                                <Select v-model="formItem.stores" multiple filterable
                                                        style="width: 200px">
                                                    <Option :value="0">全部</Option>
                                                    <Option v-for="item in storeList" :value="item.id">{{ item.storeName}}</Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                        <Col :span="12">
                                            <FormItem label="产品类别">
                                                <Select v-model="formItem.productCategoryId" multiple filterable
                                                        style="width: 200px">
                                                    <Option :value="0">全部</Option>
                                                    <Option v-for="item in productList" :value="item.id">{{ item.name}}</Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                    </Row>

                                    <FormItem>
                                        <Button type="primary" @click="saveProject('formItem')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </FormItem>

                                </Form>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
        <!--  <Modal v-model="userModal" class="Modal" :width="screenWidth" title="负责人" :mask-closable="false">
              <div class="addBody">

                  <employee-choose v-if="userModal" @init-choose="initChooseProject"
                                   @close-modal="closeCurrModal"></employee-choose>
              </div>
              <div slot="footer">
                  <el-button @click="closeCurrModal">取消</el-button>
                  <el-button type="primary" :loading="userLoading" @click="closeCurrModal">确定</el-button>
              </div>
          </Modal>-->
    </div>
</template>


<script>
    import employeeChoose from '@/components/page/project/choose/employeeChoose.vue'

    export default {
        data() {
            return {
                userModal: false,
                userLoading: false,
                screenWidth: '30%',
                natureList: [],  //性质
                cycleList: [], //周期类型
                reducesList: [],  // 扣减类型
                incomeList: [], //收入类型
                productList: null,//产品类别
                storeList: null,//门店
                formItem: {
                    projectNo: null,
                    employeeId: null,   //负责人id
                    name: null,    //项目名称
                    createTime: null,
                    capital: null,
                    cycleStartTime: null,   //周期开始时间
                    cycleEndTime: null,   //周期结束时间
                    employeeName: null,
                    natureType: null,  //性质
                    cycleType: null, //周期类型
                    reduces: [],  // 扣减类型
                    incomeType: null, //收入类型
                    productCategoryId: [],
                    stores:[],
                },
                employees: null,
                ruleValidate: {
                    projectNo: [
                        {required: true, message: '项目编号错误请联系管理员', trigger: 'blur'}
                    ],
                    name: [
                        {required: true, message: '请输入项目名称', trigger: 'blur'},
                    ],
                    capital: [
                        {required: true, message: '请输入项目资金', trigger: 'blur'},
                        {required: true, pattern: /(^[\-0-9][0-9]*(.[0-9]+)?)$/, message: '请输入数字', trigger: 'change'}
                    ],
                    employeeId: [
                        {required: true, message: '请选择负责人', trigger: 'change', type: "number"}
                    ],
                    natureType: [
                        {required: true, message: '请选择项目性质', trigger: 'change', type: "number"}
                    ],
                    cycleType: [
                        {required: true, message: '请选择结算类型', trigger: 'change', type: "number"}
                    ],
                    incomeType: [
                        {required: true, message: '请选择收入类型', trigger: 'change', type: "number"}
                    ],
                    reduces: [
                        {required: true, message: '请选择扣减类型', trigger: 'change', type: "array"}
                    ],
                    createTime: [
                        {required: true, message: '请选择立项时间', trigger: 'change', type: "date"}
                    ],
                    cycleStartTime: [
                        {
                            required: true, trigger: 'change',
                            type: 'array', fields: {
                                0: {type: 'date', required: true, message: '请选择周期时间'},
                                1: {type: 'date', required: true, message: '请选择周期时间'}
                            }
                        }
                    ],
                }
            }
        },
        components: {
            'employeeChoose': employeeChoose
        },
        created: function () {
            //this.getData();
            this.getByEmployees()
            this.getDictionaryList();
            this.getProduct();
            this.getStore();
        },
        methods: {
            getDictionaryList() {
                this.$postData("dictionary_getList", {}, {}).then(res => {
                    if (res.status == 200) {
                        for (let i = 0; i < res.data.length; i++) {
                            let item = res.data[i];
                            if (item.type == 3) {
                                this.natureList.push(item)
                            } else if (item.type == 4) {
                                this.incomeList.push(item)
                            } else if (item.type == 5) {
                                this.reducesList.push(item)
                            } else if (item.type == 6) {
                                this.cycleList.push(item)
                            }
                        }
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getByEmployees() {
                this.$postData("employee_getByList", {}, {}).then(res => {
                    if (res.status == 200) {
                       ;
                        this.employees = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getData() {
                this.$getData("project_getByProjectNo", {}).then(res => {
                    if (res.meta.state == 200) {

                        this.formItem.projectNo = res.data.projectNo
                    } else {
                        this.$message.error("查询失败，" + res.meta.msg);
                    }
                })
            },

            getProduct() {
                this.$postData("category_queryList", {}, {}).then(res => {
                    if (res.status == 200) {

                        this.productList = res.data;
                    }
                });
            },
            getStore() {
                this.$postData("store_getByList", {}, {}).then(res => {
                    if (res.status == 200) {

                        this.storeList = res.data;
                    }
                });
            },
            /**
             * 关闭当前界面弹出的窗口
             * */
            closeCurrModal() {
                this.userModal = false;
            },
            initChooseProject(data) {
                this.formItem.employeeId = data.id;
                this.formItem.employeeName = data.realName;
            },
            saveProject(name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        if (this.formItem.cycleStartTime != null) {
                            this.formItem.cycleEndTime = this.formItem.cycleStartTime[1];
                            this.formItem.cycleStartTime = this.formItem.cycleStartTime[0];
                        }
                        this.$postData("project_saveProject", this.formItem, {}).then(res => {
                            if (res.meta.state == 200) {
                                this.$Message.success('添加成功');
                                this.chooseThisModel();
                            } else {
                                this.$message.error("添加失败，" + res.meta.msg);
                            }
                        })
                    }
                })
            },
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            }
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

