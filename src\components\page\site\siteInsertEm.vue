<template>
    <div class="table">
        <div class="container">
            <div class="handle-box" style="margin-left: 2rem">
                <Form :label-width="80" style="margin-top: 20px"
                      label-position="left">
                  <FormItem label="驻场员工" prop="">
                    <Input size="large" v-model="dto.employeeName" @on-focus="isService=true"
                           style="width: 180px"
                           placeholder="驻场员工"/>
                  </FormItem>
                    <FormItem label="调度时间">
                        <el-date-picker
                                v-model="dto.days"
                                type="datetimerange"
                                :picker-options="pickerOptions"
                                format="yyyy-MM-dd HH:mm:ss"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </FormItem>
                    <FormItem label="金额" prop="">
                        <el-input size="large" v-model="dto.amount"  type="text"
                               oninput="value=value.replace(/[^\d.]/g,'')"
                               style="width: 150px"      placeholder="金额"/>
                    </FormItem>
                </Form>
                <Button type="primary" :loading="loading" @click="batchMatter">新增</Button>
                <div style="padding: 22px">
                    注：暂不支持跨月，分多次操作
                </div>
            </div>
        </div>
      <Modal v-model="isService" class="Modal" :width="width" :z-index="9999"
             title="驻场员工"
             :mask-closable="false">
        <div class="addBody">
          <employee-quit v-if="isService" @init-choose="initChooseProject"
                         @close-modal="closeCurrModal"></employee-quit>
        </div>
        <div slot="footer">
        </div>
      </Modal>
    </div>
</template>

<script>
import employeeQuit from '@/components/page/site/employeeQuit.vue'
    export default {
        props: ['projectId'],
        data() {
            return {
              value:null,
              width: "32%",
              isService: false,
                loading: false,
                dto: {
                    projectId: null,
                    days: [],
                    startTime: null,
                    employeeId: null,
                    employeeName: null,
                    endTime: null,
                    amount: null,
                },
                projectList: [],
                employeeList: [],
                pickerOptions: {
                    onPick: ({maxDate, minDate}) => {
                        this.pickerMinDate = minDate.getTime();
                        if (maxDate) {
                            this.pickerMinDate = ''
                        }
                    },
                    disabledDate: time => {
                        if (this.pickerMinDate) {
                            console.log(this.pickerMinDate);
                            const startDay = (new Date(this.pickerMinDate).getDate() - 1) * 24 * 3600 * 1000;
                            const endDay = (new Date(new Date(this.pickerMinDate).getFullYear(),
                                new Date(this.pickerMinDate).getMonth() + 1, 0).getDate() - new Date(this.pickerMinDate).getDate()) * 24 * 3600 * 1000;
                            let minTime = this.pickerMinDate - startDay;
                            let maxTime = this.pickerMinDate + endDay;
                            return time.getTime() < minTime || time.getTime() > maxTime
                        }
                    }
                },
            };
        },
      components: {
        'employeeQuit': employeeQuit
      },
        watch: {
            projectId: {
                handler(newValue, oldValue) {
                    this.projectId = newValue;
                    this.getProject();
                }
            }
        },
        created() {
            this.getProject();
        },
        computed: {},
        methods: {
            getProject() {
                this.$postUrl("siteProject_selectList", this.projectId).then(res => {
                    if (res.status === 200) {
                        this.projectList = res.data
                    }
                })
            },
            getEmployee(value) {
                this.$postUrl("siteEmployee_getEmployee", this.dto.projectId).then(res => {
                    if (res.status === 200) {
                        this.employeeList = res.data
                    }
                })
            },
            batchMatter() {
                if (this.dto.days.length <= 0) {
                    this.$message.error("选择日期");
                    return
                }else if(this.dto.amount===null||this.dto.amount===''){
                  this.$message.error("输入金额");
                  return
                }
                if (this.dto.days.length > 0) {
                    this.dto.startTime = this.dto.days[0];
                    this.dto.endTime = this.dto.days[1];
                }
                let wagesMatter={
                    startTime: this.dto.startTime,
                    endTime: this.dto.endTime,
                    projectId:this.projectId,
                    type:4,
                    deductionType:1,
                    amount: this.dto.amount,
                };
                let siteEmployee={
                    projectId:this.projectId,
                    employeeId: this.dto.employeeId,
                    state:1,
                    type:2,
                    dispatchStartTime: this.dto.startTime,
                    dispatchEndTime: this.dto.endTime,
                };
                let query = {
                    employeeId: this.dto.employeeId,
                    projectId: this.projectId,
                    siteWagesMatter: wagesMatter,
                    siteEmployee:siteEmployee
                };
                this.loading = true;
                this.$postData("siteEmployee_insertEmployee", query).then(res => {
                    this.loading = false;
                    if (res.status === 200) {
                        this.dto.days = [];
                        this.dto.amount = null;
                        this.dto.employeeName =null;
                        this.$message({
                            type: 'success',
                            message: '操作成功!'
                        });
                    }
                })
            },
          initChooseProject(data) {
            this.dto.employeeId = data.id;
            this.dto.name = data.realName;
            this.dto.employeeNo = data.no;
            this.dto.employeeName = data.realName + data.no;
            this.closeCurrModal();
          },
          closeCurrModal(data) {
            this.isService = false;
          },
        }
    };
</script>

<style scoped>
    .handle-box {
        /*margin-bottom: 10px;*/
        font-weight: bold !important;
    }

    .el-form-item__label {
        font-weight: bold !important;
    }

    .handle-select {
        width: 120px;
    }

    .handle-input {
        width: 300px;
        display: inline-block;
    }

    .del-dialog-cnt {
        font-size: 16px;
        text-align: center;
    }

    .table {
        width: 100%;
        font-size: 13px;

    }

    .red {
        color: #ff0000;
    }

    .mr10 {
        margin-right: 10px;
    }

    .el-date-editor .el-range-separator {
        padding: 0 5px;
        line-height: 32px;
        width: 7%;
        color: #303133;
    }

    .container {
        padding: 0px !important;
        background: #fff;
        border: none !important;
        border-radius: 5px;
    }
</style>
