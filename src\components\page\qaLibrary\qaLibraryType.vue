<template>
	<div class="container" id="pdfDom">

		<!-- 搜索栏目 -->
		<div class="handle-box">
			<el-form ref="form" :model="pageInfo">
				<el-row>
					<el-col :span="6">
						<el-form-item label="搜索关键词" style="margin-right: 20px">
							<el-input v-model="quer.search" placeholder="问答库编号、类型名称"></el-input>
						</el-form-item>
					</el-col>

					<el-col :span="10" style="margin: 32px 0;">
						<el-button type="primary" @click="query()" icon="el-icon-search">搜索
						</el-button>

            <el-upload :action="excelUploadUrl" list-type="picture" :on-success="excelUploadSuccess"
                       :on-error="excelUploadError" :show-file-list="false" class="upload-demo inline-block">
              <el-button type="warning" size="small"
                         style="margin-left: 10px" icon="el-icon-upload2">导入问答类型</el-button>
            </el-upload>

            <el-button style="margin-left: 10px" type="warning" icon="el-icon-download" @click="excelDownload">下载导入模板
            </el-button>

						<el-button style="margin-left: 10px" type="success" icon="el-icon-circle-plus-outline" @click="certModal = true">添加
						</el-button>
					</el-col>
				</el-row>

			</el-form>
		</div>




		<!-- 数据表格 -->
		<el-table :data="list" v-loading="loading" border stripe
			:header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}" :row-key="getRowKeys"
			:expand-row-keys="expands" @expand-change="expandSelect">

			<el-table-column width="140" prop="certCodeFixed" label="问答库编号">
				<template slot-scope="scope">
					<span>{{scope.row.typeNo}}</span>
				</template>
			</el-table-column>

			<el-table-column width="140" prop="typeName" label="类型名称">
				<template slot-scope="scope">
					<span v-if="!scope.row.isEdit">{{scope.row.typeName}}</span>

					<el-input v-model="scope.row.typeName" style="width: 100%;" type="textarea" v-if="scope.row.isEdit"
						placeholder="请输入证书标题" :autosize="{ minRows: 4, maxRows: 10}"></el-input>
				</template>
			</el-table-column>

			<el-table-column width="140" prop="certContent" label="创建时间">
				<template slot-scope="scope">
					<span>{{scope.row.createTime}}</span>
				</template>
			</el-table-column>

			<el-table-column width="130" prop="creater" label="创建人">
				<template slot-scope="scope">
					<span >{{scope.row.founder}}</span>
				</template>
			</el-table-column>

			<el-table-column  min-width="180" label="操作" v-if="showEdit">
				<template slot-scope="scope">
					<el-button @click="openEdit(scope.row)" type="success" size="small" :disabled="scope.row.isEdit"
						v-if="!scope.row.isEdit" icon="el-icon-edit">修改
					</el-button>
					<el-button @click="updateUnionQAType(scope.row)" type="primary" size="small"
						v-if="scope.row.isEdit" icon="el-icon-circle-check">保存
					</el-button>
					<el-popconfirm title="此操作将会连同所有此类型的问答一起删除，确定删除吗？" @confirm="deleteUnionQAType(scope.row)">
						<el-button type="danger" size="small" slot="reference" style="margin-left: 10px" icon="el-icon-delete">删除
						</el-button>
					</el-popconfirm>

				</template>
			</el-table-column>

		</el-table>


		<div class="pagination">
			<Page :total="pageInfo.total" @on-change="onChange" :current="pageInfo.current" :show-total="true"
				:show-sizer="true" :page-size-opts="pageSizeOpts" @on-page-size-change="onPageSizeChange"
				:page-size="pageInfo.size" />
		</div>

		<!--添加问答类型-->
		<el-dialog :visible.sync="certModal" width="60%" title="添加问答类型" :mask-closable="false">
			<div style="height: 500px;overflow: hidden;overflow-y: scroll;">
				<el-row style="width:100%;height: auto;margin-bottom: 0px;">
					<el-col :span="12">
						<el-form ref="ruleForm" label-width="100px" class="demo-ruleForm" size="mini">

							<el-form-item label="类型名称：">
								<el-input v-model="unionQAType.typeName" type="textarea" class="handle-input mr10"
									placeholder="请输入类型名称">
								</el-input>
							</el-form-item>
						</el-form>
					</el-col>
				</el-row>

				<div style="margin: 0 400px;width: 100%;">
					<el-button @click="saveUnionQAType()" type="success" size="small">确定添加
					</el-button>
				</div>

			</div>
		</el-dialog>

    <el-link :href="hrefUrl" target="_blank" id="elLink"/>

	</div>

</template>

<script>

  export default {
    name: "qaLibraryType",
    components: {
    },
		data() {
			return {
				url: '',
        hrefUrl: "https://biapi.xiaoyujia.com/unionQa/importTemplateDownload",
				uploadId: 1,
				excelUploadId: 1,
        isModal: false,
				excelUploadUrl: "https://biapi.xiaoyujia.com/unionQa/importUnionQaType/"+localStorage.getItem("id"),
				isEdit: false,
				showEdit: true,
				certModal: false,
				list: [],
				loading: true,
				pageSizeOpts: [5, 10, 20],
        unionQAType: {
          founder: localStorage.getItem("id")
        },
				certType: {},
				pageInfo: {
					total: 10,
					size: 5,
					current: 1,
					pages: 1
				},
				expands: [],
				getRowKeys(row) {
					return row.id
				},
				quer: {
					"search": "",
					"current": 1,
					"size": 10
				},
			}
		},
		created() {

			this.getData()
		},
		methods: {
			getData() {
				// 获取证书列表
				this.$getData("getUnionQaType", this.quer).then(res => {
					this.loading = false
					if (res.code == 0) {
						this.list = res.data.records
						this.pageInfo.current = res.data.current
						this.pageInfo.size = res.data.size
						this.pageInfo.total = res.data.total
						// 追加编辑状态位
						for (let item of this.list) {
							this.$set(item, "isEdit", false)
						}
					} else {
						this.list = []
						this.$message.error('未查询到问答类型!')
					}
				})
			},
			// 搜索
			query() {
				this.loading = true
				this.quer.current = 1
				this.getData()
			},
			// 折叠面板打开
			expandSelect(row, expandedRows) {
				var that = this
				if (expandedRows.length) {
					that.expands = []
					if (row) {
						that.expands.push(row.id) // 每次push进去的是每行的ID
						this.showEdit = false
						this.isEdit = false
					}
				} else {
					that.expands = [] // 默认不展开
					this.showEdit = true
					this.isEdit = false
				}
			},
			// 页码大小
			onPageSizeChange(size) {
				this.loading = true;
				this.quer.size = size
				this.getData()
			},
			// 跳转页码
			onChange(index) {
				this.loading = true;
				this.quer.current = index
				this.getData()
			},
			// 证书Excel模板下载
			excelDownload() {
        setTimeout(() => {
          document.getElementById("elLink").click();
        }, 1000);
			},
			// Excel表格上传成功
			excelUploadSuccess(res, file) {
				this.$message.success('问答类型导入成功！')
				this.getData()
			},
			// Excel表格上传失败
			excelUploadError() {
				this.$message.error('问答类型解析失败！请使用正确的Excel模板进行上传！')
			},
			// 打开修改
			openEdit(val) {
				val.isEdit = true
			},
			// 添加问答信息
      saveUnionQAType() {
				// 数据校验
				if (!this.unionQAType.typeName) {
					this.$message.error('请输入类型名称！')
				} else {
					this.$postData("saveUnionQAType", this.unionQAType).then(res => {
						if (res.code == 0) {
							this.$message.success('添加成功!')
							this.certModal = false
              this.unionQAType.typeName = ''
							this.getData()
						} else {
							this.$message.error('添加失败！' + res.msg)
						}
					})
				}
			},
			// 删除证书
      deleteUnionQAType(val) {
				this.$postData("deleteUnionQAType", val).then(res => {
					if (res.code == 0) {
						this.$message.success('删除成功!')
						this.getData()
					} else {
						this.$message.error('删除失败！' + res.msg)
					}
				})
			},
			// 更改问答名称
      updateUnionQAType(val) {
        val.founder = localStorage.getItem("id")
        this.$postData("updateUnionQAType", val).then(res => {
					if (res.code == 0) {
						this.$message.success('更新成功!')
            this.getData()
						val.isEdit = false
					} else {
						this.$message.error('更新失败！' + res.msg)
					}
				})
			},
		},
	}
</script>

<style scoped>
	.handle-box {
		/*margin-bottom: 10px;*/
		font-weight: bold !important;
	}

	table {
		border-collapse: collapse;
		/*border-collapse:collapse合并内外边距(去除表格单元格默认的2个像素内外边距*/
		border-left: #C8B9AE solid 1px;
		border-top: #C8B9AE solid 1px;
	}

	table td {
		border-right: #C8B9AE solid 1px;
		border-bottom: #C8B9AE solid 1px;
	}

	th {
		background: #eee;
		font-weight: normal;
	}

	tr {
		background: #fff;
	}

	tr:hover {
		background: #cc0;
	}

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}

	>>>.el-upload--text {
		width: 200px;
		height: 150px;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}

	.avatar {
		width: 300px;
		height: 300px;
		display: block;
	}

	.detail-title {
		margin: 10px 20px;
	}

	.handle-input {
		width: 200px;
		display: inline-block;
	}

	.mr10 {
		margin-right: 10px;
	}

	.big-input {
		width: 200px;
	}

	.inline-block {
		display: inline-block;
	}
</style>