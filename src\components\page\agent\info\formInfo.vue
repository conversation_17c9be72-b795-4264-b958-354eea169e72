<template>
    <div class="table">
        <el-dialog
                :title="formShow.title"
                :visible.sync="showForm"
                width="80%"
               >
            <div>
                <h2>需要填写的问题： <el-button :type="showNeed?'primary':'info'" @click="showNeed=!showNeed">{{showNeed?'隐藏':'显示'}}</el-button></h2><br>
                <el-table
                        stripe="true"
                        v-show="showNeed"
                        :data="formByTaskTypeId"
                        style="width: 100%"
                        max-height="550">
                    <el-table-column
                            type="index"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="title"
                            label="问题内容"
                            width="500">
                    </el-table-column>
                    <el-table-column
                            prop="type"
                            label="问题回复类型">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.type==1" type="success">文本回复</el-tag>
                            <el-tag v-else-if="scope.row.type==2" type="primary">单选回复</el-tag>
                            <el-tag v-else-if="scope.row.type==3" type="primary">多选回复</el-tag>

                        </template>
                    </el-table-column>
                    <el-table-column
                            fixed="right"
                            label="操作"
                            width="120">
                        <template slot-scope="scope">
                            <el-button
                                    @click.native.prevent="taskTypeDeleteForm(scope.row)"
                                    type="text"
                                    size="small">
                                移除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <br>
                <h2>新增填写的问题：<el-button :type="showAdd?'primary':'info'" @click="showAdd=!showAdd">{{showAdd?'隐藏':'显示'}}</el-button></h2><br>
                <el-table
                        stripe="true"
                        v-show="showAdd"
                        :data="addFormByTaskTypeId"
                        style="width: 100%"
                        max-height="250">
                    <el-table-column
                            type="index"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="title"
                            label="问题内容"
                            width="500">
                    </el-table-column>
                    <el-table-column
                            prop="type"
                            label="问题回复类型">
                        <template slot-scope="scope">
                            <el-tag v-if="scope.row.type==1" type="success">文本回复</el-tag>
                            <el-tag v-else-if="scope.row.type==2" type="primary">单选回复</el-tag>
                            <el-tag v-else-if="scope.row.type==3" type="primary">多选回复</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                            fixed="right"
                            label="操作"
                            width="120">
                        <template slot-scope="scope">
                            <el-button
                                    @click.native.prevent="taskTypeAddForm(scope.row)"
                                    type="text"
                                    size="small">
                                添加
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <span slot="footer" class="dialog-footer">

            <el-button type="primary" @click="showForm = false">确 定</el-button>
          </span>
        </el-dialog>
        <div class="container">
            <el-tabs type="border-card">

                <el-tab-pane label="回访中心">
                    <div class="handle-box">
                        <el-collapse v-model="isForm" >
                            <el-collapse-item title="新增问题" name="1">
                                <template slot="title">
                                    <el-button type="primary" >新增问题</el-button>
                                </template>
                                <el-form ref="form" :model="taskTypeForm">
                                    <el-form-item   prop="workContent">
                                        <div class="label-name">表单标题:</div>
                                        <el-input placeholder="请输入表单标题" type="textarea" v-model="taskTypeForm.title" style="width: 70%">
                                        </el-input>
                                    </el-form-item >
                                    <el-form-item   prop="workContent">
                                        <div class="label-name">表单说明:</div>
                                        <el-input placeholder="请输入表单说明" type="textarea" v-model="taskTypeForm.details" style="width: 70%">
                                        </el-input>
                                    </el-form-item >
                                    <el-form-item   prop="workContent">
                                        <div class="label-name">处理天数:</div>
                                        <el-input placeholder="请输入处理天数" type="number" v-model="taskTypeForm.processingDays" style="width: 70%">
                                        </el-input>
                                    </el-form-item >



                                    <div style="margin-right: 100px;float: right">
                                        <Button type="primary" @click="saveTaskType(taskTypeForm)">确定</Button>
                                    </div>
                                </el-form>
                            </el-collapse-item>
                        </el-collapse>
                    </div>
                    <div class="handle-box">
                        <br>
                        <h2>已存回访表单：</h2><br>
                    </div>
                    <el-table
                            stripe="true"
                            type="expand"
                            :data="taskTypeList"
                            style="width: 100%"
                            @expand-change="getFormByTaskType"
                    >
                        <el-table-column
                                type="index"
                                width="50">
                        </el-table-column>
                        <el-table-column
                                prop="title"
                                label="表单名称">
                        </el-table-column>
                        <el-table-column

                                prop="details"
                                label="说明">
                        </el-table-column>
                            <el-table-column

                                prop="processingDays"
                                label="预期处理天数/天">
                        </el-table-column>

                        <el-table-column
                                fixed="right"
                                label="操作"
                                width="120">
                            <template slot-scope="scope">
                                <el-button
                                        @click.native.prevent="toTaskTypeInfo(scope.row.id)"
                                        type="text"
                                        size="small">
                                    分析
                                </el-button>
                                <el-button
                                        @click.native.prevent="formShow=scope.row,showForm=true,getFormByTaskTypeId(scope.row.id),getAddFormByTaskTypeId(scope.row.id)"
                                        type="text"
                                        size="small">
                                    查看表单
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>

                <el-tab-pane>
                    <span slot="label"><i class="el-icon-date"></i> 问题中心</span>
                    <div class="handle-box">
                        <el-collapse v-model="isForm" >

                            <el-collapse-item title="新增问题" name="1">
                                <template slot="title">
                                    <el-button type="primary" >新增问题</el-button>
                                </template>
                                <el-form ref="form" :model="saveForm">
                                    <el-form-item   prop="workContent">
                                        <div class="label-name">问题内容:</div>
                                        <el-input placeholder="请输入问题内容" type="textarea" v-model="saveForm.title" style="width: 70%">
                                        </el-input>
                                    </el-form-item >


                                    <el-form-item   prop="remark">
                                        <div class="label-name">问题回复类型:</div>
                                        <el-radio-group v-model="saveForm.type">
                                            <el-radio :label="1">文本回复</el-radio>
                                            <el-radio :label="2">单选回复</el-radio>
                                            <el-radio :label="3">多选回复</el-radio>
                                        </el-radio-group>
                                    </el-form-item >
                                    <el-form-item   prop="defaultAnswer">
                                        <div class="label-name">默认答案：<br>(多个答案请以逗号隔开,如 是,否 )</div>
                                        <el-input placeholder="请输入问题内容" type="textarea" v-model="saveForm.defaultAnswer" style="width: 70%">
                                        </el-input>
                                    </el-form-item >


                                    <div style="margin-right: 100px;float: right">
                                        <Button type="primary" @click="saveFormTo(saveForm)">确定</Button>
                                    </div>
                                </el-form>
                            </el-collapse-item>
                        </el-collapse>
                    </div>
                    <div class="handle-box">
                        <br>
                        <h2>已存问题列表：</h2><br>
                    </div>
                    <el-table
                            stripe="true"
                            :data="formList"
                            style="width: 100%"
                            max-height="550">
                        <el-table-column
                                type="index"
                                width="50">
                        </el-table-column>
                        <el-table-column
                                prop="title"
                                label="问题内容"
                                width="500">
                        </el-table-column>
                        <el-table-column
                                prop="type"
                                label="问题回复类型">
                            <template slot-scope="scope">
                                <el-tag v-if="scope.row.type==1" type="success">文本回复</el-tag>
                                <el-tag v-else-if="scope.row.type==2" type="primary">单选回复</el-tag>
                                <el-tag v-else-if="scope.row.type==3" type="primary">多选回复</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column
                                prop="defaultAnswer"
                                label="默认答案">
                        </el-table-column>
                        <el-table-column
                                fixed="right"
                                label="操作"
                                width="120">
                            <template slot-scope="scope">
                                <el-button
                                        @click.native.prevent="deleteRow(scope.row)"
                                        type="text"
                                        size="small">
                                    移除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>


            </el-tabs>

        </div>
    </div>
</template>

<script>

    export default {
        name: "formInfo",
        data() {
            return {
                showNeed:true,
                showAdd:true,
                formByTaskTypeId:[],
                addFormByTaskTypeId:[],
                formShow:{
                    id:null,
                    title:null,
                    formIdList:[],
                },
                showForm:false,
                isForm:false,
                saveForm:{
                    type:1,
                    title:null,
                    defaultAnswer:null,
                    creatPerson:localStorage.getItem("id"),
                    source:localStorage.getItem("storeId"),
                },
                formList:[],
                taskTypeList:[],
                taskTypeForm:{
                    title:null,
                    details:null,
                    processingDays:null,
                },
                form:{
                    type:null,
                    title:null,
                    creatPerson:null,
                },
                taskType:{},
            }
        },
        created(){
            this.getData();
            this.getTaskType();
        },
        methods:{
            toTaskTypeInfo(id){
                let routeData = this.$router.resolve({path:'/taskTypeInfo',query:{"id":id }})//path：跳转页面的相对路径，query：参数
                window.open(routeData.href, '_blank');
            },
            taskTypeDeleteForm(row){
                if (this.formByTaskTypeId.length==1){
                    return this.$message.error("至少保留一个问题")
                }
                let newList=[]
                this.formByTaskTypeId.forEach(v=>{
                    if (v.id!==row.id){
                        newList.push(v.id)
                    }
                })
                this.formShow.formIdList=newList

               this.saveTaskType(this.formShow)
            },
            taskTypeAddForm(row){
                console.log(this.formShow.formIdList)
                if (this.formShow.formIdList==null){
                    this.formShow.formIdList=[]
                }
                this.formShow.formIdList.push(row.id)
                this.saveTaskType(this.formShow)
            },
            getFormByTaskType(row,expanded){
                console.log(row)
                console.log(expanded)
            },
            deleteRow(row){
                console.log(row)
                row.status=0
                this.saveFormTo(row)
            },
            getData() {
                this.$postData("formList", this.form, {}).then(res => {
                    if (res.status == 200) {
                        this.formList=res.data
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getFormByTaskTypeId(id) {
                this.$postUrl("getFormByTaskTypeId",id,null,null).then(res => {
                    if (res.status == 200) {
                        this.formByTaskTypeId=res.data
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getAddFormByTaskTypeId(id) {
                this.$postUrl("getAddFormByTaskTypeId",id,null,null).then(res => {
                    if (res.status == 200) {
                        this.addFormByTaskTypeId=res.data
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            getTaskType() {
                this.$postData("taskTypeList", this.taskType, {}).then(res => {
                    if (res.status == 200) {
                        this.taskTypeList=res.data
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            saveFormTo(form) {
                this.$postData("saveForm", form, {}).then(res => {
                    if (res.status == 200) {
                        this.saveForm.title=null;
                        this.$message.success("操作成功");
                        this.getData();
                    } else {
                        this.$message.error("操作失败，" + res.msg);
                    }
                })
            },
            saveTaskType(form) {
                this.$postData("saveTaskType", form, {}).then(res => {
                    if (res.status == 200) {
                        this.$message.success("操作成功");
                        if (this.formShow.id!=null && this.formShow.id!=undefined){
                            this.getFormByTaskTypeId(this.formShow.id)
                            this.getAddFormByTaskTypeId(this.formShow.id)
                        }
                        this.taskTypeForm.title=null
                        this.taskTypeForm.details=null
                        this.taskTypeForm.processingDays=null
                        this.getTaskType();

                    } else {
                        this.$message.error("操作失败，" + res.msg);
                    }
                })
            },
        }
    }
</script>

<style scoped>

</style>
