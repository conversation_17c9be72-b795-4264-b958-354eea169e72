<template>
    <div class="login-wrap">
        <div class="ms-login">
            <div class="ms-title">小羽佳{{title}}数据分析</div>
            <el-form v-if="loginType === 0"
                :model="ruleForm"
                :rules="rules"
                ref="ruleForm"
                label-width="0px"
                class="ms-content"
            >
                <el-form-item>
                    <el-input v-model="ruleForm.phone" placeholder="手机号">
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        @keyup.enter.native="loginByPhone()"
                        placeholder="验证码"
                        v-model="ruleForm.textCode"
                    >
                      <el-button  slot="append" @click="sendCode" :disabled="isSending || countdown > 0">
                        {{ isSending ? '发送中...' : countdown > 0 ? `${countdown}秒后重试` : '发送验证码' }}
                      </el-button>
                    </el-input>

                </el-form-item>

                <div class="login-btn">
                    <el-button type="primary" @click="loginByPhone(ruleForm)">登录</el-button>
                </div>
                <p class="login-tips">{{loginTips}}</p>
            </el-form>

          <el-form v-if="loginType == 1"
                   :model="ruleForm"
                   :rules="rules"
                   ref="ruleForm"
                   label-width="0px"
                   class="ms-content"
          >
            <el-form-item prop="username">
              <el-input v-model="ruleForm.username" placeholder="username">
                <el-button slot="prepend" icon="el-icon-lx-people"></el-button>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                  type="password"
                  placeholder="password"
                  v-model="ruleForm.password"
                  @keyup.enter.native="submitForm()"
              >
                <el-button slot="prepend" icon="el-icon-lx-lock"></el-button>
              </el-input>
            </el-form-item>
            <div class="login-btn">
              <el-button type="primary" @click="submitForm(ruleForm)">登录</el-button>
            </div>
            <p class="login-tips">{{loginTips}}</p>
          </el-form>
        </div>
    </div>
</template>

<script>
export default {
    data: function() {
        return {
          loginType:0,
            title: "",
            loginTips: "",
          isSending: false,
          countdown: 0,
            ruleForm: {
                username: "",
                password: "",
                phone: "",
                textCode: "",
            },
            rules: {
                username: [
                    { required: true, message: "请输入用户名", trigger: "blur" }
                ],
                password: [
                    { required: true, message: "请输入密码", trigger: "blur" }
                ]
            }
        };
    },
    created() {
      console.log(this.$route.query.loginType);
      if (this.$route.query.loginType) {
            this.loginType = this.$route.query.loginType;
      }
        if (this.$route.query.type) {
            this.$getData("getChannel", {
                channelCode: this.$route.query.type
            }).then(res => {
                if (res.meta.state == 200) {
                    this.title = res.data[0].text || "";
                    document.title = "小羽佳" + this.title + "数据分析 - 登录";
                }
            });
        } else {
            document.title = "小羽佳数据分析 - 登录";
        }
    },
    methods: {
      loginByPhone(formName) {
        this.$postData(
            "sendVerificationCodeLogin",
            {
              phone: this.ruleForm.phone,
              code: this.ruleForm.textCode
            },
            {}
        ).then(res => {
          if (res.status == 200) {
            localStorage.setItem("id",res.data.id);
            localStorage.setItem("token", res.data.token);
            localStorage.setItem("realName", res.data.realName);
            localStorage.setItem("headImg", res.data.headImg);
            localStorage.setItem("channelName", res.data.channelName);
            localStorage.setItem("account", res.data.account);
            localStorage.setItem("roleId", res.data.roleId);
            localStorage.setItem("storeId", res.data.storeId);
            localStorage.setItem("userStoreId", res.data.storeId);
            this.$router.push("/");
          } else {
            this.$message.error(res.msg);
          }
        });
      },
      sendCode() {
        if (this.countdown > 0 || this.isSending) {
          return; // 防止重复点击发送
        }

        // 假设在这个方法中实现发送验证码的逻辑
        // 可以调用sendVerificationCode()方法发送验证码
        // 这里只是简单模拟发送过程
        this.isSending = true;
        this.$postData(
            "sendVerificationCode",
            {
              phone: this.ruleForm.phone,
            },
            {}
        ).then(res => {
          if (res.status == 200) {
            this.isSending = false;
            this.startCountdown();
            this.$message.success("验证码发送成功");
          } else {
            this.$message.error(res.msg);
          }
        });


        // setTimeout(() => {
        //   // 假设发送成功后将isSending重置为false
        //   this.isSending = false;
        // }, 2000); // 这里使用2秒的延迟来模拟发送过程，你需要替换为实际的发送逻辑
      },
      startCountdown() {
        this.countdown = 60;
        const timer = setInterval(() => {
          this.countdown--;
          if (this.countdown <= 0) {
            clearInterval(timer);
          }
        }, 1000);
      },

        submitForm(formName) {
        if(!formName){
          formName = this.ruleForm
        }
            // this.$refs[formName].validate((valid) => {
            //     if (valid) {
            //         localStorage.setItem('ms_username',this.ruleForm.username);
            //         this.$router.push('/');
            //     } else {
            //         console.log('error submit!!');
            //         return false;
            //     }
            // });
            this.$postData(
                "account",
                {
                    account: formName.username,
                    password: formName.password
                },
                {}
            ).then(res => {
                if (res.meta.state == 200) {
                    localStorage.setItem("id",res.data.id);
                    localStorage.setItem("token", res.data.token);
                    localStorage.setItem("realName", res.data.realName);
                    localStorage.setItem("headImg", res.data.headImg);
                    localStorage.setItem("channelName", res.data.channelName);
                    localStorage.setItem("account", res.data.account);
                    localStorage.setItem("roleId", res.data.roleId);
                    localStorage.setItem("storeId", res.data.storeId);
                    localStorage.setItem("userStoreId", res.data.storeId);
                    this.$router.push("/");
                } else {
                    this.loginTips = res.meta.msg;
                }
            });
        }
    }
};
</script>

<style scoped>
.login-wrap {
    position: relative;
    width: 100%;
    height: 100%;
    background-image: url(../../assets/img/login-bg.jpg);
    background-size: 100%;
}
.ms-title {
    width: 100%;
    line-height: 60px;
    text-align: center;
    font-size: 24px;
    color: #333;
    font-weight: 500;
    border-bottom: 1px solid #ddd;
}
.ms-login {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 350px;
    margin: -190px 0 0 -175px;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.3);
    overflow: hidden;
}
.ms-content {
    padding: 30px 30px;
}
.login-btn {
    text-align: center;
}
.login-btn button {
    width: 100%;
    height: 36px;
    margin-bottom: 10px;
}
.login-tips {
    font-size: 12px;
    line-height: 30px;
    color: red;
}
</style>
