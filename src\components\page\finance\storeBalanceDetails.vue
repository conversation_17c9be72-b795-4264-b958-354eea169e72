<script>
import {createLightStyleSheet} from "@antv/g2/lib/theme/style-sheet/light";

export default {
  name: 'StoreBalanceDetails',
  data() {
    return {
      // 加载状态
      loading: false,
      detailLoading: false,

      // 门店信息
      storeInfo: {
        storeId: '',
        storeName: '',
        storeType: '',
        balance: 0,
        accumulateIncome: 0,
        accumulateExpenditure: 0,
        frozenAmount: 0
      },

      // 查询表单
      queryForm: {
        storeId: '',
        changeType: '',
        changeDescribe: '',
        startTime: '',
        endTime: '',
        current: 1,
        size: 10
      },

      // 变动类型选项
      changeTypeOptions: [
        { label: '全部', value: '' },
        { label: '充值', value: '1' },
        { label: '订单转入', value: '2' },
        { label: '支出', value: '3' },
        { label: '提现', value: '4' },
        { label: '返还', value: '5' },
      ],
      payOptions: [
        { label: '全部', value: '' },
        { label: '线索费', value: '线索费' },
        { label: '合同保险', value: '合同保险' },
        { label: '品牌费', value: '品牌费' },
        { label: '人员认证', value: '4' }
      ],

      // 门店选项
      storeOptions: [],

      // 钱包明细数据
      balanceDetailsData: [],

      // 分页配置
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },

      // 统计信息
      summaryInfo: {
        topUpMoney: 0,
        orderTransferMoney: 0,
        returnMoney: 0,
        expenditureMoney: 0,
        expenditureClueMoney: 0,
        expenditureInsuranceMoney: 0,
        expenditureBrandFeeMoney: 0,
        totalStoreNum: 0,
      },

      // 时间选择器配置
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },

      // 时间范围
      timeRange: []
    }
  },

  mounted() {
    this.getAllFranchiseStore()
    this.getBalanceDetailsList()
    // 如果有路由参数传入的门店ID，设置默认值
    if (this.$route.query.storeId) {
      this.queryForm.storeId = this.$route.query.storeId
    }
  },

  watch: {
    timeRange(val) {
      if (val && val.length === 2) {
        this.queryForm.startTime = val[0]
        this.queryForm.endTime = val[1]
      } else {
        this.queryForm.startTime = ''
        this.queryForm.endTime = ''
      }
    }
  },

  methods: {
    // 获取所有门店
    getAllFranchiseStore() {
      this.$getData("getAllFranchiseStore", {type: 5}, {}).then(res => {
        if (res.code == 0) {
          this.storeOptions = res.data
        } else {
          this.$message.error("查询筛选门店失败!")
        }
      })
    },

    // 获取门店余额信息
    getStoreBalanceInfo() {
      if (!this.queryForm.storeId) {
        this.$message.warning('请先选择门店')
        return
      }

      this.detailLoading = true
      this.$getData("getStoreBalance", { storeId: this.queryForm.storeId }).then(res => {
        if (res.code === 0) {
          this.storeInfo = {
            ...this.storeInfo,
            ...res.data,
            storeId: this.queryForm.storeId
          }
          // 获取门店名称
          const store = this.storeOptions.find(item => item.id == this.queryForm.storeId)
          if (store) {
            this.storeInfo.storeName = store.storeName
            this.storeInfo.storeType = store.storeType
          }
        } else {
          this.$message.error(res.msg || "查询门店余额信息失败!")
        }
        this.detailLoading = false
      }).catch(() => {
        this.detailLoading = false
      })
    },

    // 获取钱包明细列表
    getBalanceDetailsList() {
      this.loading = true
      const params = {
        storeId: this.queryForm.storeId || '',
        changeType: this.queryForm.changeType || '',
        changeDescribe: this.queryForm.changeDescribe || '',
        startTime: this.queryForm.startTime || '',
        endTime: this.queryForm.endTime || '',
        current: this.queryForm.current,
        size: this.queryForm.size
      }

      this.$getData("getStoreBalanceDetails", params, {}).then(res => {
        if (res.code === 0) {
          this.balanceDetailsData = res.data.respList.records
          this.pagination.total = res.data.respList.total
          this.pagination.currentPage = res.data.respList.current
          this.pagination.pageSize = res.data.respList.size
          this.summaryInfo.totalStoreNum = res.data.totalStoreNum
          this.summaryInfo.expenditureBrandFeeMoney = res.data.expenditureBrandFeeMoney
          this.summaryInfo.expenditureInsuranceMoney = res.data.expenditureInsuranceMoney
          this.summaryInfo.expenditureClueMoney = res.data.expenditureClueMoney
          this.summaryInfo.expenditureMoney = res.data.expenditureMoney
          this.summaryInfo.returnMoney = res.data.returnMoney
          this.summaryInfo.orderTransferMoney = res.data.orderTransferMoney
          this.summaryInfo.topUpMoney = res.data.topUpMoney
        } else {
          this.$message.error(res.msg || '获取钱包明细失败')
        }
        this.loading = false
      }).catch(error => {
        this.$message.error('获取钱包明细失败')
        console.error('获取钱包明细失败:', error)
        this.loading = false
      })
    },


    // 搜索
    handleSearch() {
      this.queryForm.current = 1
      this.pagination.currentPage = 1
      this.getBalanceDetailsList()
    },

    // 重置搜索
    handleReset() {
      this.queryForm.storeId = ''
      this.queryForm.changeType = ''
      this.queryForm.changeDescribe = ''
      this.queryForm.startTime = ''
      this.queryForm.endTime = ''
      this.timeRange = []
      this.queryForm.current = 1
      this.pagination.currentPage = 1
      this.getBalanceDetailsList()
    },

    // 分页变化
    handlePageChange(page) {
      this.queryForm.current = page
      this.pagination.currentPage = page
      this.getBalanceDetailsList()
    },

    // 页面大小变化
    handleSizeChange(size) {
      this.queryForm.size = size
      this.queryForm.current = 1
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.getBalanceDetailsList()
    },

    // 格式化金额
    formatAmount(row) {
      console.log(row)
      const prefix = (row.changeType==1||row.changeType==2||row.changeType==5)?'+' : '-'
      return `${prefix}¥${Math.abs(row.changeAmount).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    },

    // 格式化余额
    formatBalance(amount) {
      return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    },

    // 获取变动类型标签样式
    getChangeTypeTagType(type) {
      switch (type) {
        case 1: // 充值
        case 4: // 退款
        case 5: // 奖励
          return 'success'
        case 2: // 消费
        case 6: // 扣费
          return 'danger'
        case 3: // 合同保险
          return 'warning'
        case 7: // 转账
          return 'info'
        default:
          return ''
      }
    },

    // 获取变动类型名称
    getChangeTypeName(type) {
      const typeMap = {
        1: '充值',
        2: '订单转入',
        3: '支出',
        4: '提现',
        5: '返还',
      }
      return typeMap[type] || '未知'
    },

    // 获取门店类型标签
    getStoreTypeLabel(type) {
      const labels = {
        1: '直营店',
        2: '加盟店',
        5: '承包店'
      }
      return labels[type] || '未知'
    },

    // 导出钱包明细
    handleExport() {
      const params = {
        storeId: this.queryForm.storeId || '',
        changeType: this.queryForm.changeType || '',
        startTime: this.queryForm.startTime || '',
        endTime: this.queryForm.endTime || ''
      }

      this.$message.success('导出功能开发中...')
      // 实际项目中调用导出接口
      // this.$getData("exportStoreBalanceDetails", params, {}).then(res => {
      //   // 处理导出逻辑
      // })
    },

    // 查看图标
    viewIcon(iconUrl) {
      if (iconUrl) {
        window.open(iconUrl, '_blank')
      } else {
        this.$message.warning('暂无图标')
      }
    }
  }
}
</script>

<template>
  <div class="store-balance-details-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>门店钱包明细</h2>
      <p class="page-description">查看所有门店的钱包变动明细和流水记录</p>
    </div>

    <!-- 统计概览 -->
    <div class="summary-overview">
      <div class="summary-cards">
        <div class="summary-card income" style="width: 400px;">
          <div class="card-icon">
            <i class="el-icon-top"></i>
          </div>
          <div class="card-content">
            <div class="card-title">总充值</div>
            <div class="card-amount positive">
              {{ formatBalance(summaryInfo.topUpMoney||0.00) }}
            </div>
          </div>
          <div class="card-content">
            <div class="card-title">总订单转入</div>
            <div class="card-amount positive">
              {{ formatBalance(summaryInfo.orderTransferMoney||0.00) }}
            </div>
          </div>
        </div>

        <div class="summary-card expenditure" style="width: 800px;margin-left: -50px">
          <div class="card-icon">
            <i class="el-icon-bottom"></i>
          </div>
          <div class="card-content">
            <div class="card-title">总支出</div>
            <div class="card-amount negative">
              {{ formatBalance(summaryInfo.expenditureMoney||0.00) }}
            </div>
          </div>
          <div class="card-content">
            <div class="card-title">线索费</div>
            <div class="card-amount negative">
              {{ formatBalance(summaryInfo.expenditureClueMoney||0.00) }}
            </div>
          </div>
          <div class="card-content">
            <div class="card-title">合同保险</div>
            <div class="card-amount negative">
              {{ formatBalance(summaryInfo.expenditureInsuranceMoney||0.00) }}
            </div>
          </div>
          <div class="card-content">
            <div class="card-title">品牌费</div>
            <div class="card-amount negative">
              {{ formatBalance(summaryInfo.expenditureBrandFeeMoney||0.00) }}
            </div>
          </div>
        </div>

        <div class="summary-card stores" style="width: 180px;margin-left: 300px">
          <div class="card-icon">
            <i class="el-icon-office-building"></i>
          </div>
          <div class="card-content">
            <div class="card-title">涉及门店</div>
            <div class="card-amount">
              {{ summaryInfo.totalStoreNum||0 }} 家
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 钱包明细查询区域 -->
    <div class="search-container">
      <el-form :model="queryForm" inline class="search-form">
        <el-form-item label="门店筛选">
          <el-select
            v-model="queryForm.storeId"
            filterable
            clearable
            placeholder="请选择门店"
            style="width: 200px"
          >
            <el-option
              v-for="item in storeOptions"
              :key="item.id"
              :label="item.storeName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="变动类型">
          <el-select
            v-model="queryForm.changeType"
            placeholder="请选择变动类型"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in changeTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="支出类型">
          <el-select
              v-model="queryForm.changeDescribe"
              placeholder="请选择支出类型"
              clearable
              style="width: 150px"
          >
            <el-option
                v-for="option in payOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="变动时间">
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            align="right"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <i class="el-icon-search"></i>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i>
            重置
          </el-button>
          <el-button type="success" @click="handleExport">
            <i class="el-icon-download"></i>
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 钱包明细表格 -->
    <div class="table-container">
      <el-table
        :data="balanceDetailsData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        empty-text="暂无明细记录"
      >
        <el-table-column
          prop="serialNumber"
          label="流水号"
          width="180"
          show-overflow-tooltip
        />

        <el-table-column
          prop="storeName"
          label="门店名称"
          width="200"
          show-overflow-tooltip
        />

        <el-table-column
          prop="storeType"
          label="门店类型"
          width="100"
          align="center"
        >
          <template slot-scope="{ row }">
            <el-tag
              :type="row.storeType === 1 ? 'success' : row.storeType === 2 ? 'warning' : 'info'"
              size="small"
            >
              {{ getStoreTypeLabel(row.storeType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="changeType"
          label="变动类型"
          width="100"
          align="center"
        >
          <template slot-scope="{ row }">
            <el-tag
              :type="getChangeTypeTagType(row.changeType)"
              size="small"
            >
              {{ getChangeTypeName(row.changeType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
            prop="storeBalance"
            label="门店余额"
            width="120"
            align="right"
        >
          <template slot-scope="{ row }">
            <span :class="{ 'negative': row.storeBalance < 0 }">
              {{ formatBalance(row.storeBalance) }}
            </span>
          </template>
        </el-table-column>


        <el-table-column
          prop="changeAmount"
          label="变动金额"
          width="120"
          align="right"
        >
          <template slot-scope="{ row }">
            <span :class="{ 'positive-amount': (row.changeType==1||row.changeType==2||row.changeType==5), 'negative-amount': (row.changeType==3||row.changeType==4) }">
              {{ formatAmount(row) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          prop="afterChangeAmount"
          label="变动后余额"
          width="120"
          align="right"
        >
          <template slot-scope="{ row }">
            <span :class="{ 'negative': row.afterChangeAmount < 0 }">
              {{ formatBalance(row.afterChangeAmount) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          prop="changeDescribe"
          label="变动描述"
          min-width="120"
          show-overflow-tooltip
        />

        <el-table-column
          prop="expensesVoucher"
          label="费用凭证"
          width="140"
          show-overflow-tooltip
        />

        <el-table-column
            prop="payType"
            label="充值类型"
            width="100"
            align="center"
        >
          <template slot-scope="{ row }">
            <el-tag
                :type="row.payType === 1 ? 'success' : row.payType === 2 ? 'warning' : 'info'"
                size="small"
            >
              {{ row.payType===1?'微信直充':row.payType===2?'扫码支付':row.payType===3?'对公转账':'未知' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="operate"
          label="操作人"
          width="100"
          align="center"
        />

        <el-table-column
          prop="createTime"
          label="变动时间"
          width="160"
          align="center"
        />

      </el-table>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<style scoped>
.store-balance-details-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.summary-overview {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.summary-card {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.summary-card.income {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.summary-card.expenditure {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.summary-card.stores {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.search-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.card-icon {
  font-size: 32px;
  margin-right: 15px;
  opacity: 0.8;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  margin-bottom: 8px;
  opacity: 0.9;
}

.card-amount {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.card-amount.negative {
  color: #ff6b6b;
}

.card-amount.positive {
  color: #51cf66;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.positive-amount {
  color: #67c23a;
  font-weight: 600;
}

.negative-amount {
  color: #f56c6c;
  font-weight: 600;
}

.negative {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .store-balance-details-container {
    padding: 10px;
  }

  .summary-cards {
    grid-template-columns: 1fr;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
</style>