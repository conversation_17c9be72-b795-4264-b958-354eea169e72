<script>
export default {
  name: 'StoreBalanceDetails',
  data() {
    return {
      // 加载状态
      loading: false,
      detailLoading: false,

      // 门店信息
      storeInfo: {
        storeId: '',
        storeName: '',
        storeType: '',
        balance: 0,
        accumulateIncome: 0,
        accumulateExpenditure: 0,
        frozenAmount: 0
      },

      // 查询表单
      queryForm: {
        storeId: '',
        transactionType: '',
        startTime: '',
        endTime: '',
        pageNum: 1,
        pageSize: 10
      },

      // 交易类型选项
      transactionTypeOptions: [
        { label: '全部', value: '' },
        { label: '充值', value: '1' },
        { label: '消费', value: '2' },
        { label: '提现', value: '3' },
        { label: '退款', value: '4' },
        { label: '奖励', value: '5' },
        { label: '扣费', value: '6' },
        { label: '转账', value: '7' }
      ],

      // 门店选项
      storeOptions: [],

      // 交易记录数据
      transactionData: [],

      // 分页配置
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },

      // 时间选择器配置
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },

      // 时间范围
      timeRange: []
    }
  },

  mounted() {
    this.getAllFranchiseStore()
    // 如果有路由参数传入的门店ID，直接查询
    if (this.$route.query.storeId) {
      this.queryForm.storeId = this.$route.query.storeId
      this.getStoreBalanceInfo()
      this.getTransactionList()
    }
  },

  watch: {
    timeRange(val) {
      if (val && val.length === 2) {
        this.queryForm.startTime = val[0]
        this.queryForm.endTime = val[1]
      } else {
        this.queryForm.startTime = ''
        this.queryForm.endTime = ''
      }
    }
  },

  methods: {
    // 获取所有门店
    getAllFranchiseStore() {
      this.$getData("getAllFranchiseStore", {type: 5}, {}).then(res => {
        if (res.code == 0) {
          this.storeOptions = res.data
        } else {
          this.$message.error("查询筛选门店失败!")
        }
      })
    },

    // 获取门店余额信息
    getStoreBalanceInfo() {
      if (!this.queryForm.storeId) {
        this.$message.warning('请先选择门店')
        return
      }

      this.detailLoading = true
      this.$getData("getStoreBalance", { storeId: this.queryForm.storeId }).then(res => {
        if (res.code === 0) {
          this.storeInfo = {
            ...this.storeInfo,
            ...res.data,
            storeId: this.queryForm.storeId
          }
          // 获取门店名称
          const store = this.storeOptions.find(item => item.id == this.queryForm.storeId)
          if (store) {
            this.storeInfo.storeName = store.storeName
            this.storeInfo.storeType = store.storeType
          }
        } else {
          this.$message.error(res.msg || "查询门店余额信息失败!")
        }
        this.detailLoading = false
      }).catch(() => {
        this.detailLoading = false
      })
    },

    // 获取交易流水列表
    getTransactionList() {
      if (!this.queryForm.storeId) {
        this.$message.warning('请先选择门店')
        return
      }

      this.loading = true
      const params = {
        storeId: this.queryForm.storeId,
        transactionType: this.queryForm.transactionType || '',
        startTime: this.queryForm.startTime || '',
        endTime: this.queryForm.endTime || '',
        current: this.queryForm.pageNum,
        size: this.queryForm.pageSize
      }

      // 模拟API调用 - 实际项目中替换为真实接口
      this.mockGetTransactionList(params)
    },

    // 模拟获取交易流水数据
    mockGetTransactionList(params) {
      setTimeout(() => {
        const mockData = [
          {
            id: 1,
            transactionNo: 'TXN202408200001',
            transactionType: '1',
            transactionTypeName: '充值',
            amount: 1000.00,
            balanceAfter: 15680.50,
            description: '门店充值',
            createTime: '2024-08-20 10:30:00',
            operator: '系统'
          },
          {
            id: 2,
            transactionNo: 'TXN202408200002',
            transactionType: '2',
            transactionTypeName: '消费',
            amount: -50.00,
            balanceAfter: 15630.50,
            description: '线索扣费',
            createTime: '2024-08-20 11:15:00',
            operator: '系统'
          },
          {
            id: 3,
            transactionNo: 'TXN202408200003',
            transactionType: '4',
            transactionTypeName: '退款',
            amount: 30.00,
            balanceAfter: 15660.50,
            description: '订单退款',
            createTime: '2024-08-20 14:20:00',
            operator: '客服'
          },
          {
            id: 4,
            transactionNo: 'TXN202408200004',
            transactionType: '5',
            transactionTypeName: '奖励',
            amount: 200.00,
            balanceAfter: 15860.50,
            description: '推广奖励',
            createTime: '2024-08-20 16:45:00',
            operator: '系统'
          },
          {
            id: 5,
            transactionNo: 'TXN202408200005',
            transactionType: '6',
            transactionTypeName: '扣费',
            amount: -180.00,
            balanceAfter: 15680.50,
            description: '违约扣费',
            createTime: '2024-08-20 18:10:00',
            operator: '财务'
          }
        ]

        // 根据交易类型筛选
        let filteredData = mockData
        if (params.transactionType) {
          filteredData = filteredData.filter(item => item.transactionType === params.transactionType)
        }

        this.transactionData = filteredData
        this.pagination.total = filteredData.length
        this.pagination.currentPage = params.current
        this.loading = false
      }, 500)
    },

    // 门店选择变化
    handleStoreChange() {
      this.queryForm.pageNum = 1
      this.pagination.currentPage = 1
      this.getStoreBalanceInfo()
      this.getTransactionList()
    },

    // 搜索
    handleSearch() {
      this.queryForm.pageNum = 1
      this.pagination.currentPage = 1
      this.getTransactionList()
    },

    // 重置搜索
    handleReset() {
      this.queryForm.transactionType = ''
      this.queryForm.startTime = ''
      this.queryForm.endTime = ''
      this.timeRange = []
      this.queryForm.pageNum = 1
      this.pagination.currentPage = 1
      this.getTransactionList()
    },

    // 分页变化
    handlePageChange(page) {
      this.queryForm.pageNum = page
      this.pagination.currentPage = page
      this.getTransactionList()
    },

    // 页面大小变化
    handleSizeChange(size) {
      this.queryForm.pageSize = size
      this.queryForm.pageNum = 1
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.getTransactionList()
    },

    // 格式化金额
    formatAmount(amount) {
      const prefix = amount >= 0 ? '+' : ''
      return `${prefix}¥${Math.abs(amount).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    },

    // 格式化余额
    formatBalance(amount) {
      return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    },

    // 获取交易类型标签样式
    getTransactionTypeTagType(type) {
      switch (type) {
        case '1': // 充值
        case '4': // 退款
        case '5': // 奖励
          return 'success'
        case '2': // 消费
        case '6': // 扣费
          return 'danger'
        case '3': // 提现
          return 'warning'
        case '7': // 转账
          return 'info'
        default:
          return ''
      }
    },

    // 获取门店类型标签
    getStoreTypeLabel(type) {
      const labels = {
        1: '直营店',
        2: '加盟店',
        5: '承包店'
      }
      return labels[type] || '未知'
    },

    // 导出交易记录
    handleExport() {
      if (!this.queryForm.storeId) {
        this.$message.warning('请先选择门店')
        return
      }

      this.$message.success('导出功能开发中...')
      // 实际项目中调用导出接口
    }
  }
}
</script>

<template>
  <div class="store-balance-details-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>门店余额详情</h2>
      <p class="page-description">查看门店钱包余额详情和交易流水记录</p>
    </div>

    <!-- 门店选择区域 -->
    <div class="store-select-container">
      <el-form :model="queryForm" inline class="select-form">
        <el-form-item label="选择门店">
          <el-select
            v-model="queryForm.storeId"
            filterable
            clearable
            placeholder="请选择门店"
            style="width: 300px"
            @change="handleStoreChange"
          >
            <el-option
              v-for="item in storeOptions"
              :key="item.id"
              :label="item.storeName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <!-- 门店余额概览 -->
    <div v-if="queryForm.storeId" class="balance-overview" v-loading="detailLoading">
      <div class="overview-header">
        <h3>{{ storeInfo.storeName }}</h3>
        <el-tag :type="storeInfo.storeType === 1 ? 'success' : storeInfo.storeType === 2 ? 'warning' : 'info'" size="small">
          {{ getStoreTypeLabel(storeInfo.storeType) }}
        </el-tag>
      </div>

      <div class="balance-cards">
        <div class="balance-card current-balance">
          <div class="card-icon">
            <i class="el-icon-wallet"></i>
          </div>
          <div class="card-content">
            <div class="card-title">当前余额</div>
            <div class="card-amount" :class="{ 'negative': storeInfo.balance < 0 }">
              {{ formatBalance(storeInfo.balance) }}
            </div>
          </div>
        </div>

        <div class="balance-card income">
          <div class="card-icon">
            <i class="el-icon-top"></i>
          </div>
          <div class="card-content">
            <div class="card-title">累计收入</div>
            <div class="card-amount positive">
              {{ formatBalance(storeInfo.accumulateIncome || 0) }}
            </div>
          </div>
        </div>

        <div class="balance-card expenditure">
          <div class="card-icon">
            <i class="el-icon-bottom"></i>
          </div>
          <div class="card-content">
            <div class="card-title">累计支出</div>
            <div class="card-amount negative">
              {{ formatBalance(storeInfo.accumulateExpenditure || 0) }}
            </div>
          </div>
        </div>

        <div class="balance-card frozen">
          <div class="card-icon">
            <i class="el-icon-lock"></i>
          </div>
          <div class="card-content">
            <div class="card-title">冻结金额</div>
            <div class="card-amount">
              {{ formatBalance(storeInfo.frozenAmount || 0) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 交易记录查询区域 -->
    <div v-if="queryForm.storeId" class="transaction-search-container">
      <el-form :model="queryForm" inline class="search-form">
        <el-form-item label="交易类型">
          <el-select
            v-model="queryForm.transactionType"
            placeholder="请选择交易类型"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in transactionTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="交易时间">
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            align="right"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <i class="el-icon-search"></i>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i>
            重置
          </el-button>
          <el-button type="success" @click="handleExport">
            <i class="el-icon-download"></i>
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 交易记录表格 -->
    <div v-if="queryForm.storeId" class="transaction-table-container">
      <el-table
        :data="transactionData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        empty-text="暂无交易记录"
      >
        <el-table-column
          prop="transactionNo"
          label="交易流水号"
          width="180"
          show-overflow-tooltip
        />

        <el-table-column
          prop="transactionTypeName"
          label="交易类型"
          width="100"
          align="center"
        >
          <template slot-scope="{ row }">
            <el-tag
              :type="getTransactionTypeTagType(row.transactionType)"
              size="small"
            >
              {{ row.transactionTypeName }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="amount"
          label="交易金额"
          width="120"
          align="right"
        >
          <template slot-scope="{ row }">
            <span :class="{ 'positive-amount': row.amount > 0, 'negative-amount': row.amount < 0 }">
              {{ formatAmount(row.amount) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          prop="balanceAfter"
          label="交易后余额"
          width="120"
          align="right"
        >
          <template slot-scope="{ row }">
            <span :class="{ 'negative': row.balanceAfter < 0 }">
              {{ formatBalance(row.balanceAfter) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          prop="description"
          label="交易描述"
          min-width="150"
          show-overflow-tooltip
        />

        <el-table-column
          prop="operator"
          label="操作人"
          width="100"
          align="center"
        />

        <el-table-column
          prop="createTime"
          label="交易时间"
          width="160"
          align="center"
        />
      </el-table>
    </div>

    <!-- 分页组件 -->
    <div v-if="queryForm.storeId" class="pagination-container">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<style scoped>
.store-balance-details-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.store-select-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.select-form {
  margin: 0;
}

.balance-overview {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.overview-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.overview-header h3 {
  margin: 0 10px 0 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.balance-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.balance-card {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.balance-card.current-balance {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.balance-card.income {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.balance-card.expenditure {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.balance-card.frozen {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-icon {
  font-size: 32px;
  margin-right: 15px;
  opacity: 0.8;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  margin-bottom: 8px;
  opacity: 0.9;
}

.card-amount {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.card-amount.negative {
  color: #ff6b6b;
}

.card-amount.positive {
  color: #51cf66;
}

.transaction-search-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.transaction-table-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.positive-amount {
  color: #67c23a;
  font-weight: 600;
}

.negative-amount {
  color: #f56c6c;
  font-weight: 600;
}

.negative {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .store-balance-details-container {
    padding: 10px;
  }

  .balance-cards {
    grid-template-columns: 1fr;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
</style>