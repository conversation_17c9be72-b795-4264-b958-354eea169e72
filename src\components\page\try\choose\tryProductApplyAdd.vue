<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col span="24">
                    <div>
                        <div id="operBtnDiv">
                            <div id="searchDiv">
                                <Form ref="formItem" :model="formItem" :label-width="80" label-position="left"
                                      :rules="ruleValidate">
                                    <FormItem label="商品名称" prop="tryProductId">
                                        <el-select v-model="formItem.tryProductId" filterable  placeholder="请选择" @change="getByMembers">
                                            <el-option
                                                    v-for="item in productList"
                                                    :key="item.id"
                                                    :label="item.firstTitle"
                                                    :value="item.id">
                                            </el-option>
                                        </el-select>
                                    </FormItem>

                                    <FormItem label="申请人员" prop="memberId">
                                        <el-select v-model="formItem.memberId" filterable  placeholder="请选择" @change="getByApply">
                                            <el-option
                                                    v-for="(item, index) in memberList"
                                                    :key="index"
                                                    :label="item.member.account"
                                                    :value="item.memberId">
                                                <span style=" color: #8492a6; font-size: 13px">{{ item.name }}</span>
                                                <!--<span style="float: left">{{ item.member.account }}</span>-->
                                            </el-option>
                                        </el-select>
                                    </FormItem>

                                    <div style="margin-left: 80px">
                                        <Button type="primary" @click="save('formItem')">确定</Button>
                                        <Button @click="chooseThisModel" style="margin-left: 15px">取消</Button>
                                    </div>
                                </Form>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
    </div>
</template>


<script>

    export default {
        data() {
            return {
                productList: null,
                memberList: null,
                isQuery:true,
                formItem: {
                    tryProductId: null,
                    memberId: null,
                    tryProductApplyId:null
                },
                ruleValidate: {
                    tryProductId: [
                        {required: true, message: '请选择商品', trigger: 'change',type:"number"}
                    ],
                    memberId: [
                        {required: true, message: '请选择人员', trigger: 'change',type:"number"}
                    ],
                },
            }
        },
        components: {
        },
        created: function () {
            this.getByProducts();
        },
        methods: {
            getByProducts(){
                let res={
                    status:1
                }
                this.$postData("product_getByIsWinList",res).then(res => {
                    if (res.status == 200) {
                    this.productList=res.data;
                    this.getByMembers();
                    } else {
                        this.$message.error("获取数据失败，" + res.msg);
                    }
                })
            },

            getByMembers(){
                this.formItem.memberId=null;
                let member={
                    tryProductId: this.formItem.tryProductId,
                }
                this.$postData("apply_getByApplyList", member, {}).then(res => {
                    if (res.status == 200) {
                        this.memberList=res.data;
                        if(res.data.length==0){
                            this.formItem.memberId=null
                        }
                            this.getByApply();
                    } else {
                        this.$message.error("获取数据失败，" + res.msg);
                    }
                })
            },
            getByApply(){
                console.log(this.formItem)
                this.$postData("apply_getByApplyList", this.formItem, {}).then(res => {
                    if (res.status == 200) {
                        this.formItem.tryProductApplyId=res.data[0].id;
                    } else {
                        this.$message.error("获取数据失败，" + res.msg);
                    }
                })
            },

            save(name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        if(this.formItem.tryProductApplyId){
                            this.$confirm('此操作存在风险性, 是否继续?', '提示', {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }).then(() => {
                                this.$postData("applyCoupon_save", this.formItem, {}).then(res => {
                                    if (res.status == 200) {
                                        this.$Message.success('中奖名单内定成功，请及时绑定优惠券');
                                        this.chooseThisModel()
                                    } else {
                                        this.$message.error("中奖名单内定失败，" + res.msg);
                                    }
                                })

                            }).catch(() => {

                            });

                        }else {
                            this.$Message.success('请联系管理员');
                        }

                    }
                })
            },
            /*
         * 关闭当前窗口
         * */
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            },

        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        padding-left: 20px;
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

