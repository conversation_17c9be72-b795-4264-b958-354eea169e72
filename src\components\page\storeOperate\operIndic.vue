<!-- 运营指标 Operational Indicators -->
<template>
    <div class="container">
        <div class="handle-box">
            <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="选择门店">
                    <el-select ref="storeNameSel" filterable v-model="dom.storeId" placeholder="请选择门店" clearable>
                        <el-option
                                v-for="(item,index) in storeList"
                                :key="index"
                                :label="item.text"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="员工工号">
                    <el-input v-model="dom.employeeNo" placeholder="请输入所辖门店员工工号" clearable @keyup.enter.native="onQuery"></el-input>
                </el-form-item>
                <el-form-item label="时间区间">
                    <el-date-picker
                            :clearable="false"
                            v-model="dom.times"
                            type="daterange"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            range-separator="~"
                            @change="changeTime"
                            :picker-options="pickeroptions"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-search" type="primary" @click="onQuery" v-loading="loading">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-refresh" @click="reset()" v-loading="loading">
                        重置
                    </el-button>
                </el-form-item>
                <el-form-item>
                    <el-button icon="el-icon-download" type="primary" @click="exportExcel" v-loading="loading">导出</el-button>
                </el-form-item>
            </el-form>

            <el-table :data="indicDataList" height="500px" v-loading="loading"
                      :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
                <el-table-column
                        label="序号"
                        type="index"
                        width="80"
                        align="center">
                </el-table-column>
                <el-table-column
                        prop="storeName"
                        label="门店名称"
                        align="center"
                ></el-table-column>
                <el-table-column
                        prop="realName"
                        label="员工姓名"
                        align="center">
                </el-table-column>
                <el-table-column
                        prop="no"
                        label="员工工号"
                        align="center">
                </el-table-column>
                <el-table-column
                        width="120"
                        prop="goCusNum"
                        label="上户数"
                        align="left">
                </el-table-column>
                <el-table-column
                        width="120"
                        prop="recrNum"
                        label="招工数"
                        align="left">
                </el-table-column>
                <el-table-column
                        width="120"
                        prop="clueNum"
                        label="线索数"
                        align="left">
                </el-table-column>
                <el-table-column
                        width="120"
                        prop="devNum"
                        label="开发数"
                        align="left">
                </el-table-column>
                <el-table-column
                        prop="addWxNum"
                        width="120"
                        label="加微数"
                        align="left">
                </el-table-column>
            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChangePage"
                      :current="dom.current"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="dom.size"/>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: "operIndic",
        data() {
            return {
                // 选择60天以内的数据，其余的禁止
                pickeroptions: {
                    disabledDate: time => {
                        const sd = new Date().getTime() - (1000 * 60 * 60 * 24 * 60);
                        const ed = new Date().getTime();
                        return time.getTime() < sd || time.getTime() > ed
                    }
                },
                loading: false,
                dom: {
                    storeId: null,
                    startTime: null,
                    endTime: null,
                    times: null,
                    employeeNo: null,
                    eId: localStorage.getItem('id'),
                    storeList: [],
                    size: 15,
                    current: 1
                },

                pageSizeOpts: [10, 15, 20, 30, 50, 100],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                storeList: [],
                indicDataList: [],

            }
        },
        async created() {
            this.dom.times = this.timeDefault();
            await this.getStoreData();
            this.getData();
        },
        mounted() {
        },
        methods: {
            changeTime(v, s) {
                this.dom.startTime = v[0];
                this.dom.endTime = v[1];
            },
            timeDefault() {
                const invTime = (1000 * 60 * 60 * 24);
                const endDateNs = new Date();
                const startDateNs = new Date(endDateNs.getTime() - (invTime * 7));
                // 月，日 不够10补0
                let defaultStartTime = startDateNs.getFullYear() + '-' + ((startDateNs.getMonth() + 1) >= 10 ? (startDateNs.getMonth() + 1) : '0' + (startDateNs.getMonth() + 1)) + '-' + (startDateNs.getDate() >= 10 ? startDateNs.getDate() : '0' + startDateNs.getDate());
                let defaultEndTime = endDateNs.getFullYear() + '-' + ((endDateNs.getMonth() + 1) >= 10 ? (endDateNs.getMonth() + 1) : '0' + (endDateNs.getMonth() + 1)) + '-' + (endDateNs.getDate() >= 10 ? endDateNs.getDate() : '0' + endDateNs.getDate());
                this.dom.startTime = defaultStartTime;
                this.dom.endTime = defaultEndTime;
                return [defaultStartTime, defaultEndTime];
            },
            async getStoreData() {
                await this.$postData("getStoreDataByEid", JSON.stringify({id: this.dom.eId}), {}).then((res) => {
                    this.dom.storeIds = [];
                    if (res.status === 200) {
                        this.storeList = res.data;
                        this.storeList.forEach(e => {
                            this.dom.storeList.push(Number(e.value));
                        });
                    } else {
                        this.$message.error(res.msg);
                    }
                });
            },
            exportExcel() {
                this.loadStart();
                this.$postData("getIndicDataExport", JSON.stringify(this.dom), {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tableName: "门店员工指标数据",
                        res: res
                    });
                })
            },
            onQuery(){
                this.pageInfo.current = 1;
                this.dom.current = 1;
                this.getData();
            },
            getData() {
                this.loadStart();
                this.indicDataList = [];
                this.$postData("getIndicDataPage", JSON.stringify(this.dom), {}).then((res) => {
                    if (res.status === 200) {
                        this.indicDataList = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total;
                        this.loadEnd();
                    } else {
                        this.$message.error(res.msg);
                        this.pageInfo.current = 1;
                        this.pageInfo.size = 0;
                        // this.page.total = 10;
                        this.loadEnd();
                    }
                });
            },
            reset() {
                this.pageInfo.current = 1;
                this.dom.current = 1;
                this.dom.storeId = null;
                this.dom.employeeNo = null;
                this.dom.times = this.timeDefault();
                this.getData();
            },
            onChangePage(index) {
                this.dom.current = index;
                this.getData();
            },
            onPageSizeChange(size) {
                this.loading = true;
                this.dom.size = size;
                this.getData();
            },
            loadStart() {
                this.loading = true;
            },
            loadEnd() {
                this.loading = false;
            },
            blobExport({tableName, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tableName + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
                this.loadEnd();
            },
        }

    };
</script>

<style scoped>
    /*滚动条的宽度*/
    /deep/ .el-table__body-wrapper::-webkit-scrollbar {
        /*横向滚动条*/
        width: 6px;
        /*纵向滚动条 必写*/
        height: 6px;

    }

    /*滚动条的滑块*/
    /deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 3px;
    }

    .el-row {
        margin-bottom: 20px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .el-col {
        border-radius: 4px;
    }

    .bg-purple-dark {
        background: #99a9bf;
    }

    .bg-purple {
        background: #d3dce6;
    }

    .bg-purple-light {
        background: #e5e9f2;
    }

    .grid-content {
        border-radius: 4px;
        min-height: 36px;
    }

    .row-bg {
        padding: 10px 0;
        background-color: #f9fafc;
    }

</style>
