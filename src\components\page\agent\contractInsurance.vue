<template>
  <div class="container">
    <div class="handle-box">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="合同结束时间">
          <el-date-picker
              v-model="time"
              type="datetimerange"
              range-separator="至"
              value-format="yyyy-MM-dd HH:mm:ss"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="上保时间">
          <el-date-picker
              v-model="time1"
              type="datetimerange"
              range-separator="至"
              value-format="yyyy-MM-dd HH:mm:ss"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="合同号">
          <el-input v-model="dom.contractNo"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="onQuery" >
            查询
          </el-button>
          <el-button  type="success" @click="exportExcel" >
            导出
          </el-button>
          <el-button type="warning" @click="showReportDialog = true">保费报表</el-button>
        </el-form-item>

      </el-form>
    </div>

    <el-table :data="dataList" v-loading="loading" style="width: 100%"
              :header-cell-style="{background:'#f8f8f9',fontWeight:600,color:'#000000'}">
      <el-table-column
          prop="contractNo"
          label="合同号"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="agentName"
          label="经纪人"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="employeeName"
          label="员工姓名"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="employeeCardId"
          label="员工身份证"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="employeePhone"
          label="员工手机号"
          align="center"
      ></el-table-column>
      <el-table-column
          label="合同类型"
          align="center"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.contractType === 1">正常合同</el-tag>
          <el-tag v-if="scope.row.contractType === 2">补签合同</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="insuranceNo"
          label="保单号"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="upTime"
          label="上保险时间"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="serviceStarDate"
          label="合同开始时间"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="serviceEndDate"
          label="合同结束时间"
          align="center"
      ></el-table-column>
      <el-table-column
          prop="contractDays"
          label="合同天数"
          align="center"
      ></el-table-column>
      <el-table-column
          label="保单链接"
          align="center"
      >
        <template slot-scope="scope">
         <el-button @click="showUrl(scope.row.policyUrl)">查看</el-button>
        </template>
      </el-table-column>

    </el-table>
    <div class="pagination">
      <Page :total="pageInfo.total"
            @on-change="onChangePage"
            :current="dom.current"
            :show-total="true"
            :show-sizer="true"
            :page-size-opts="pageSizeOpts"
            @on-page-size-change="onPageSizeChange"
            :page-size="dom.size"/>
    </div>

    <el-dialog title="保费报表" :visible.sync="showReportDialog" width="80%">
      <el-form :inline="true" class="demo-form-inline" style="margin-bottom: 10px;">
        <el-form-item label="上保时间">
          <el-date-picker
              v-model="reportQuery.upTime"
              type="datetimerange"
              range-separator="至"
              value-format="yyyy-MM-dd HH:mm:ss"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="合同开始时间">
          <el-date-picker
              v-model="reportQuery.startTime"
              type="datetimerange"
              range-separator="至"
              value-format="yyyy-MM-dd HH:mm:ss"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onReportQuery">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onReportExport">导出</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="reportList" v-loading="reportLoading" style="width: 100%">
        <el-table-column prop="insuranceNo" label="保单号" align="center"/>
        <el-table-column prop="contractNo" label="合同号" align="center"/>
        <el-table-column prop="storeType" label="门店类型" align="center"/>
        <el-table-column prop="upTime" label="上保险时间" align="center"/>
        <el-table-column prop="serviceStarDate" label="合同开始时间" align="center"/>
        <el-table-column prop="serviceEndDate" label="合同结束时间" align="center"/>
        <el-table-column prop="insuranceAmount" label="保费" align="center"/>
      </el-table>
      <div class="pagination">
        <Page :total="reportPageInfo.total"
              @on-change="onReportPageChange"
              :current="reportQuery.current"
              :show-total="true"
              :show-sizer="true"
              :page-size-opts="pageSizeOpts"
              @on-page-size-change="onReportPageSizeChange"
              :page-size="reportQuery.size"/>
      </div>
    </el-dialog>
  </div>
</template>


<script >
export default {
  data() {
    return {
      loading:false,
      dataList:[],
      dom:{
        size: 15,
        current: 1,
        startDate:null,
        endDate:null,
        contractNo:null,
        upStartTime:null,
        upEndTime:null
      },
      time:null,
      time1:null,
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      pageSizeOpts: [10, 20, 50, 100, 150, 200],
      // 新增保费报表相关
      showReportDialog: false,
      reportLoading: false,
      reportList: [],
      reportQuery: {
        size: 10,
        current: 1,
        upTime: null,      // 上保时间区间
        startTime: null,   // 合同开始时间区间
        upStartTime: null,
        upEndTime: null,
        startDate: null,
        endDate: null,
      },
      reportPageInfo: { total: 0, size: 10, current: 1, pages: 1 },
    }

  },
  created() {
    this.getDataList();

  },
  methods: {
    onReportExport() {
      // 处理时间区间
      if (this.reportQuery.upTime) {
        this.reportQuery.upStartTime = this.reportQuery.upTime[0];
        this.reportQuery.upEndTime = this.reportQuery.upTime[1];
      } else {
        this.reportQuery.upStartTime = null;
        this.reportQuery.upEndTime = null;
      }
      if (this.reportQuery.startTime) {
        this.reportQuery.startDate = this.reportQuery.startTime[0];
        this.reportQuery.endDate = this.reportQuery.startTime[1];
      } else {
        this.reportQuery.startDate = null;
        this.reportQuery.endDate = null;
      }
      this.reportQuery.current = 1;

      this.$postData("exportInsuranceReport", JSON.stringify(this.reportQuery), {
        responseType: "arraybuffer"
      }).then(res => {
        this.blobExport({
          tableName: "平安保单保费报表",
          res: res
        });
      });
    },
    showUrl(url) {
     window.open(url)
    },
    getDataList() {
      this.loading = true;
      this.$postData("contractInsuranceData", this.dom).then(res => {
        if (res.status === 200) {
          this.loading = false;
          this.dataList = [];
          this.dataList = res.data.records;
          this.pageInfo.total = res.data.total;
        }
      })
    },
    onQuery() {
      if (this.time){
        this.dom.startDate = this.time[0];
        this.dom.endDate = this.time[1];
      } else {
        this.dom.startDate = null;
        this.dom.endDate = null;
      }

      if (this.time1){
        this.dom.upStartTime = this.time1[0];
        this.dom.upEndTime = this.time1[1];
      } else {
        this.dom.upStartTime = null;
        this.dom.upEndTime = null;
      }
      this.dom.current = 1;
      this.getDataList();

    },
    exportExcel() {
      if (this.time){
        this.dom.startDate = this.time[0];
        this.dom.endDate = this.time[1];
      }
      if (this.time1){
        this.dom.upStartTime = this.time1[0];
        this.dom.upEndTime = this.time1[1];
      }
      this.loading = true;
      this.$postData("exportContractInsuranceData", JSON.stringify(this.dom), {
        responseType: "arraybuffer"
      }).then(res => {
        this.blobExport({
          tableName: "平安保险(三嫂)保单数据",
          res: res
        });
      });
    },
    onChangePage(index) {
      this.dom.current = index;
      this.getDataList();
    },
    onPageSizeChange(size) {
      this.dom.size = size;
      this.getDataList();
    },
    blobExport({tableName, res}) {
      const aLink = document.createElement("a");
      let blob = new Blob([res], {type: "application/vnd.ms-excel"});
      aLink.href = URL.createObjectURL(blob);
      aLink.download = tableName + ".xlsx";
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
      const that = this;
      setTimeout(function () {
        that.loading = false;
      }, 2500);

    },
    // 保费报表相关方法
    onReportQuery() {
      // 处理时间区间
      if (this.reportQuery.upTime) {
        this.reportQuery.upStartTime = this.reportQuery.upTime[0];
        this.reportQuery.upEndTime = this.reportQuery.upTime[1];
      } else {
        this.reportQuery.upStartTime = null;
        this.reportQuery.upEndTime = null;
      }
      if (this.reportQuery.startTime) {
        this.reportQuery.startDate = this.reportQuery.startTime[0];
        this.reportQuery.endDate = this.reportQuery.startTime[1];
      } else {
        this.reportQuery.startDate = null;
        this.reportQuery.endDate = null;
      }
      this.reportQuery.current = 1;
      this.getReportList();
    },
    onReportPageChange(page) {
      this.reportQuery.current = page;
      this.getReportList();
    },
    onReportPageSizeChange(size) {
      this.reportQuery.size = size;
      this.getReportList();
    },
    getReportList() {
      this.reportLoading = true;
      this.$postData("getInsuranceReportList", this.reportQuery).then(res => {
        if (res.status === 200) {
          this.reportList = res.data.records;
          this.reportPageInfo.total = res.data.total;
          this.reportPageInfo.size = res.data.size;
          this.reportPageInfo.current = res.data.current;
          this.reportPageInfo.pages = res.data.pages;
        }
        this.reportLoading = false;
      }).catch(() => {
        this.reportLoading = false;
      });
    },

  },
  watch: {
    showReportDialog(val) {
      if (val) {
        this.onReportQuery();
      }
    }
  },

}

</script>


<style scoped>
.container {
  padding: 0px !important;
  background: #fff;
  border: none !important;
  border-radius: 5px;
}

</style>
