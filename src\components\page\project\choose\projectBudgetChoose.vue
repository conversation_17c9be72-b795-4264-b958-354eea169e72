<template style="background-color: #000">
    <div ref="contenBox">
        <div id="bodyDiv">
            <Row>
                <Col span="24">
                    <div>
                        <div id="operBtnDiv">
                            <div id="searchDiv">
                                <el-form ref="form" :model="pageInfo">
                                    <el-row>
                                        <el-col v-if="isAdd" :span="13">
                                            <el-form-item label="名称">
                                                <Select v-model="formItem.dictionaryId" filterable style="width: 120px"
                                                        @on-change="change" :label-in-value="true">
                                                    <Option v-for="item in list" :value="item.id">{{ item.name}}</Option>
                                                </Select>
                                                <span style="margin-left: 10px">金额</span>
                                                <el-input
                                                        v-model="formItem.amount"
                                                        placeholder="金额"
                                                        style="width:120px;margin-left: 10px"
                                                        class="handle-input mr10"
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col style="text-align:right" :span="addSpan">
                                            <el-button v-if="isAdd" @click="save">确定</el-button>
                                            <el-button v-if="isAdd" @click="cancel">取消</el-button>
                                            <el-button type="success" v-if="!isAdd" @click="addBudget">新增预算</el-button>

                                            <el-button type="success" @click="exportList">导出Excel</el-button>
                                        </el-col>


                                    </el-row>
                                    <el-row :span="6" style="text-align:right">
                                        <span style="font-size: 14px">合计金额:{{budgetList.amount}}</span>
                                    </el-row>
                                </el-form>
                                <el-table :data="data" border class="table" ref="multipleTable">
                                    <el-table-column
                                            prop=""
                                            label="序号"
                                            width="55">
                                        <template slot-scope="scope">
                                            <span>{{scope.$index+1}}</span>

                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                            prop="name"
                                            label="预算科目"
                                            width="210"
                                    ></el-table-column>
                                    <el-table-column
                                            prop="amount"
                                            label="预算金额"
                                            width="150"
                                    ></el-table-column>
                                    <el-table-column
                                            label="操作"
                                            width="200">
                                        <template slot-scope="scope">
                                          <!--  <el-button size="mini" @click="update(scope.row.id)" type="primary">编辑
                                            </el-button>-->
                                            <el-button size="mini" @click="deleteBudget(scope.row.id,scope.row.amount)" type="danger">删除
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <div class="pagination">
                                    <Page :total="pageInfo.total"
                                          @on-change="onChange"
                                          :show-total="true"
                                          :show-sizer="true"
                                          :page-size-opts="pageSizeOpts"
                                          @on-page-size-change="onPageSizeChange"
                                          :page-size="pageInfo.size"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
    </div>
</template>


<script>

    export default {
        props: ['budgetList'],
        data() {
            return {
                amount: null,
                projectName:this.budgetList.projectName,
                isAdd: false,
                addSpan: 24,
                data: null,
                pageSizeOpts: [5, 10, 15],
                pageInfo: {total: 0, size: 5, current: 1, pages: 1},
                formItem: {
                    dictionaryId: null,
                    name: null,
                    amount: null,
                    projectId: null,
                },
                budget: {
                    projectId: this.budgetList.projectId,
                    pageSize: 5,
                    pageNum: 1
                },
                list: null,

                ruleValidate: {
                    type: [
                        {required: true, message: '请选择类型', trigger: 'blur', type: "number"}
                    ],
                    name: [
                        {required: true, message: '请输入名称', trigger: 'change'},
                    ],
                }
            }
        },
        components: {},
        created: function () {
            this.formItem.projectId = this.budgetList.projectId
            this.getData();
        },
        methods: {
            getDictionaryList() {
                let list = {
                    type: 1
                }
                this.$postData("dictionary_getList", list, {}).then(res => {
                    if (res.status == 200) {
                        this.list = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            exportList(){
                this.$postData("budget_export", this.budget, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tablename: this.projectName+"-项目预算表",
                        res: res
                    });
                });
            },
            blobExport({ tablename, res }) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], { type: "application/vnd.ms-excel" });
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },


            getData() {
                this.$postData("budget_getByProjectId", this.budget, {}).then(res => {
                    if (res.status == 200) {
                        this.data = res.data.list;
                      //  this.getAmount();//计算合计
                        this.pageInfo.current = res.data.pageNum;
                        this.pageInfo.size = res.data.pageSize;
                        this.pageInfo.total = res.data.total
                    } else {
                        this.$message.error("获取失败，" + res.msg);
                    }
                })
            },
            deleteBudget(id,amount){
                this.$confirm('此操作将永久性删除, 是否继续?', '提示', {
                    confirmButtonText: '删除',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$getData("budget_delete", {id:id}).then(res => {
                        if (res.status == 200) {
                            this.$message.success("删除成功");
                            this.budgetList.amount=this.budgetList.amount-amount;
                            this.getData();
                        } else {
                            this.$message.error("删除失败" + res.msg);
                        }
                    })
                }).catch(() => {

                });
            },
          /*  getAmount() {
                if (this.data) {
                    for (let i = 0; i < this.data.length; i++) {
                        let res = this.data[i];
                        this.amount += res.amount
                    }
                }
            },*/
            save() {
                if (this.formItem.dictionaryId != null && this.formItem.amount != null) {
                    this.$postData("budget_save", this.formItem, {}).then(res => {
                        if (res.status == 200) {
                            this.budgetList.amount=Number(this.budgetList.amount)+Number(this.formItem.amount);
                            this.$Message.success('添加成功');
                            this.cancel();
                            this.getData()
                            this.formItem.dictionaryId = null;
                            this.formItem.name = null;
                            this.formItem.amount = null
                        } else {
                            this.$message.error("添加失败，" + res.msg);
                        }
                    })
                } else {
                    this.$Message.success('请补全新增信息');
                }


            },
            chooseThisModel(name) {
                this.$emit('init-choose', "");
            },
            // 页码大小
            onPageSizeChange(size) {
                console.log(size)
                this.budget.pageSize = size;
                this.getData();
            },
            // 跳转页码

            onChange(index) {
                console.log(index)
                this.budget.pageNum = index;
                this.getData();
            },
            addBudget() {
                this.isAdd = true
                this.addSpan = 11
                this.getDictionaryList();
            },
            cancel() {
                this.isAdd = false;
                this.addSpan = 24;
                this.formItem.dictionaryId = null;
                this.formItem.name = null;
                this.formItem.amount = null
            },
            change(data) {
                console.log(data)
                this.formItem.name = data.label;
            }
        },

    }
</script>


<style scoped>
    .contentBox {
        overflow: hidden;
    }

    .addProject {
        width: 200px;
    }

    #operBtnDiv button {
        margin: 0px 0px 10px 5px;
    }

    /* 查询div*/
    #searchDiv {
        /*padding-top: 20px;*/
        /* padding-left: 20px;*/
        font-weight: bolder;
        /*padding-bottom: 85px;*/
    }

    /*分页按钮*/
    #bodyDiv {
        /*width: 1339px;*/
        background-color: #fff;

    }

    /*分页按钮*/
    #footPage {
        background-color: #fff;
        padding: 5px;
        padding-top: 15px;
    }

    #left_body_div, #right_body_div {
        float: left;
    }

    #left_body_div {
        width: 20%;
        /*border: 1px solid #000;*/
        overflow: auto;
        height: 800px;
    }

    #right_body_div {
        width: 80%;
    }

    .text_align {
        text-align: center;
    }

</style>

