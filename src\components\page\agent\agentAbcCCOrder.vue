<template>
    <div class="table">
        <div class="container">
            <div class="handle-box">
                <el-form ref="form">
                    <div style="color: #f00f14">注意：完成时间搜索按照如：查询1月1号的结算内容，开始日期为1月1号，结束日期为1月2号。</div>

                    <el-row>

                        <el-col :span="8">
                            <el-form-item label="完成时间">
                                <el-date-picker
                                        v-model="showDate"
                                        type="daterange"
                                        :picker-options="pickerOptions"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        value-format="yyyy-MM-dd"
                                        align="right">
                                </el-date-picker>&nbsp;
                            </el-form-item>
                        </el-col>

                        <!--                        <el-col :span="8">-->
                        <!--                            <el-form-item label="经纪人">-->
                        <!--                                <el-input-->
                        <!--                                        v-model="model.realName"-->
                        <!--                                        placeholder="经纪人"-->
                        <!--                                        style="width:190px"-->
                        <!--                                        class="handle-input mr10"-->
                        <!--                                ></el-input>-->
                        <!--                            </el-form-item>-->
                        <!--                        </el-col>-->

                        <el-col :span="8" v-show="showSelect">
                            <el-form-item label="门店">
                                <el-select filterable style="width:190px" v-model="model.storeId">
                                    <el-option value="">请选择</el-option>
                                    <el-option value="1">平台</el-option>
                                    <el-option v-for="(item,index) in storeList" :value="item.id" :key="index"
                                               :label="item.storeName"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="客户手机号">
                                <el-input
                                        v-model="model.bindTel"
                                        placeholder="客户手机号"
                                        style="width:200px"
                                        class="handle-input mr10"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">

                            <el-form-item>
                                <el-button type="success" round @click="query()">搜索</el-button>
                                <el-button type="info" round @click="re()">重置</el-button>
                                <el-button type="success" round @click="exportExcel" :loading="exportExcelLoading">
                                    导出Excel
                                </el-button>
                            </el-form-item>

                        </el-col>
                    </el-row>

                </el-form>
            </div>
            <el-row>
                <center>
                    <el-col :span="12">
                        <el-card shadow="hover">
                            订单数：<span class="allAmount">{{pageInfo.total}}</span>

                        </el-card>
                    </el-col>

                    <el-col :span="12">
                        <el-card shadow="hover">
                            订单金额：￥<span class="allAmount">￥{{sum}}</span>
                            <span style="color: #6f7180">（均价： ￥<span style="color: #f00f14">{{(sum/pageInfo.total).toFixed(2)}}</span> ）</span>
                            <!--                        抽成比例：￥<span class="allAmount">{{(settlementData.allBonus/settlementData.allAmount).toFixed(2)}}%</span>-->
                        </el-card>
                    </el-col>

                </center>
            </el-row>
            <el-table :data="list" :header-cell-style="{background:'#d2d3d5'}" border class="table" ref="multipleTable"
                      v-loading="loading">
                <el-table-column
                        fixed="left"
                        prop="billno"
                        label="订单编号"
                        width="180"
                ></el-table-column>
                <!--                <el-table-column-->
                <!--                        prop="No"-->
                <!--                        label="经纪人"-->
                <!--                        width="120"-->
                <!--                >-->
                <!--                    <template slot-scope="scope">-->

                <!--                        {{scope.row.RealName}}({{scope.row.NO}})-->
                <!--                    </template>-->
                <!--                </el-table-column>-->

                <el-table-column
                        prop="tel"
                        label="会员信息"
                        width="180"
                >
                    <template slot-scope="scope">

                        <el-link type="primary">{{scope.row.tel}}</el-link>
                    </template>
                </el-table-column>


                <!--                <el-table-column-->
                <!--                        prop="totalAmount"-->
                <!--                        label="订单金额"-->
                <!--                        width="120"-->
                <!--                        sortable="custom"-->
                <!--                ></el-table-column>-->
                <el-table-column
                        prop="productName"
                        label="服务产品"
                        width="120"
                >
                </el-table-column>

                <el-table-column
                        prop="realTotalAmount"
                        label="付款金额"
                        width="120"
                ></el-table-column>

                <!--                <el-table-column-->
                <!--                        prop="realTotalAmount"-->
                <!--                        label="最终金额"-->
                <!--                        width="120"-->
                <!--                        sortable="custom"-->
                <!--                ></el-table-column>-->

                <el-table-column
                        prop="createTime"
                        label="下单时间"
                        width="150"

                ></el-table-column>
                <el-table-column
                        prop="paySettlementTime"
                        label="完成时间"
                >
                </el-table-column>
                <el-table-column
                        prop="cName"
                        label="商圈地址"
                >
                </el-table-column>


                <!--                <el-table-column-->
                <!--                        prop="workRemark"-->
                <!--                        label="工作记录"-->
                <!--                        width="150"-->
                <!--                >-->
                <!--                </el-table-column>-->

                <!--                <el-table-column-->
                <!--                        prop="skuFixedTime"-->
                <!--                        label="下单时间"-->
                <!--                        width="180"-->
                <!--                        sortable="custom"-->
                <!--                ></el-table-column>-->

            </el-table>
            <div class="pagination">
                <Page :total="pageInfo.total"
                      @on-change="onChange"
                      :show-total="true"
                      :show-sizer="true"
                      :page-size-opts="pageSizeOpts"
                      @on-page-size-change="onPageSizeChange"
                      :page-size="pageInfo.size"/>
            </div>
        </div>
    </div>

</template>

<script>
    export default {
        name: "agentAbcOrder",
        data() {
            return {
                exportExcelLoading: false,
                showSelect: false,
                role: localStorage.getItem("roleId"),
                account: localStorage.getItem("account"),
                loading: false,
                sum: 0,
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                showDate: null,
                list: [],
                storeList: [],
                pageInfo: {total: 10, size: 10, current: 1, pages: 1},
                pageSizeOpts: [10, 20, 30],
                model: {
                    storeId: localStorage.getItem("storeId"),
                    orderBy: null,
                    bindTel: null,
                    startTime: null,
                    endTime: null,
                    realName: null,
                    current: 1,
                    size: 10
                },
            }
        },
        created() {
            if (this.role == "1" || this.account == "005") {
                this.model.storeId = null;
                this.showSelect = true
            }

            this.getStore();
            this.getData();
        },
        methods: {
            onPageSizeChange(size) {
                // console.log(size)
                this.model.size = size;
                this.getData();
            },
            onChange(index) {
                // console.log(index)
                this.model.current = index;
                this.getData();
            },
            query() {

                if (this.showDate != null) {
                    this.model.startTime = this.showDate[0];
                    this.model.endTime = this.showDate[1];
                } else {
                    this.model.startTime = null;
                    this.model.endTime = null;
                }
                this.getData();
            },
            re() {
                this.showDate = null;
                this.model.storeId = null;
                this.model.startTime = null;
                this.model.endTime = null;
                this.model.bindTel = null;
                this.model.realName = null;
                this.getData();
            },
            getStore() {
                this.$postData("store_getByList", {}, {}).then(res => {
                    if (res.status == 200) {
                        this.storeList = res.data;
                    }
                });
            },
            exportExcel() {
                if (localStorage.getItem("roleId") == "42") {
                    this.model.id = localStorage.getItem("id");
                }
                if (localStorage.getItem("roleId") == '66' && localStorage.getItem("account") !== '005') {
                    this.model.storeId = localStorage.getItem("storeId")
                }
                this.$message.info("请稍等。数据整理中。马上进入自动下载");
                this.exportExcelLoading = true;
                this.$postData("getAgentCCExcel", this.model, {
                    responseType: "arraybuffer"
                }).then(res => {
                    this.blobExport({
                        tablename: "交付订单",
                        res: res
                    });
                    this.exportExcelLoading = false;
                    this.$message.success("下载成功")
                });
            },
            blobExport({tablename, res}) {
                const aLink = document.createElement("a");
                let blob = new Blob([res], {type: "application/vnd.ms-excel"});
                aLink.href = URL.createObjectURL(blob);
                aLink.download = tablename + ".xlsx";
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink);
            },
            getData() {
                this.loading = true;
                if (localStorage.getItem("roleId") == "42") {
                    this.model.id = localStorage.getItem("id");
                }
                if (localStorage.getItem("roleId") == '66' && localStorage.getItem("account") !== '005') {
                    this.model.storeId = localStorage.getItem("storeId");
                }
                const storeId = this.model.storeId;
                if (storeId != null && storeId !== '') {
                    this.model.storeId = Number(storeId);
                }
                this.$postData("getAgentCC", this.model, {}).then(res => {
                    if (res.status == 200) {
                        this.list = res.data.records;
                        this.pageInfo.current = res.data.current;
                        this.pageInfo.size = res.data.size;
                        this.pageInfo.total = res.data.total;
                        this.loading = false;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                });
                this.$postData("getAgentCCSum", this.model, {}).then(res => {
                    if (res.status == 200) {
                        this.sum = res.data;
                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
        }
    }
</script>

<style scoped>

</style>
