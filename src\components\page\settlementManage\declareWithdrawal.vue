<template>
  <div class="table">
    <div class="container">
      <el-form ref="form">
        <el-row>
          <el-col :span="9">
            <el-form-item label="提现时间">
              <el-date-picker v-model="days" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="开户人">
              <el-input
                  clearable
                  v-model="form.accountPeople"
                  placeholder="请输入开户人"
                  style="width:200px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-button type="success" round @click="form.current=1,getData()">搜索</el-button>
            <el-button type="info"
                       style="margin-bottom: 15px"
                       @click="exportDeclareWithdrawalData"
                       icon="el-icon-download">导出
            </el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="9">
            <el-form-item label="到账时间">
              <el-date-picker v-model="queryDays" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="提现人">
              <el-input
                  clearable
                  v-model="form.cashier"
                  placeholder="请输入提现人"
                  style="width:160px"
                  class="handle-input mr10"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item  label="状态">
              <el-select style="width:160px" v-model="form.state" clearable placeholder="请选择状态">
                <el-option v-for="item in options"
                           :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
              <el-button type="info"
                         style="margin-bottom: 15px"
                         @click="czmxDialogFlag = true"
                         icon="el-icon-download">导出出账明细
              </el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs type="border-card">
        <el-tab-pane label="提现记录">
          <el-table :data="logList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">
            <el-table-column prop="createTime" label="提现时间" width="150"></el-table-column>
            <el-table-column prop="receivedTime" label="到账时间" width="90"></el-table-column>
            <el-table-column prop="state" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.state===1" type="warning">待审核</el-tag>
                <el-tag v-if="scope.row.state===2" type="success">通过</el-tag>
                <el-tag v-if="scope.row.state===3" type="danger">不通过</el-tag>
                <el-tag v-if="scope.row.state===4" type="success">到账成功</el-tag>
                <el-tag v-if="scope.row.state===5" type="danger">到账失败</el-tag>
                <el-tag v-if="scope.row.state===6" type="danger">冻结</el-tag>
                <el-tag v-if="scope.row.state===7" type="warning">解冻</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="withdrawalAmount" label="提现金额" width="100"></el-table-column>
            <el-table-column prop="realName" label="提现人" width="80"></el-table-column>
            <el-table-column prop="bankNo" label="银行卡号" width="170"></el-table-column>
            <el-table-column prop="accountPeople" label="开户人" width="110"></el-table-column>
            <el-table-column prop="accountBank" label="所属银行" width="100"></el-table-column>
            <el-table-column prop="accountOpenBank" label="开户支行" width="140"></el-table-column>
            <el-table-column prop="serialNumber" label="交易流水号" width="180"></el-table-column>
            <el-table-column label="提现金额来源" width="140">
              <template slot-scope="scope">
              <el-tag type="primary" @click="getDeclareWithdrawalDetails(scope.row.id)">点击查看</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="operatorPeopleRealName" label="处理人" width="80"></el-table-column>
            <el-table-column prop="remark" label="备注" width="100"></el-table-column>
            <el-table-column prop="financeRemark" label="审批备注" width="100">
              <template slot-scope="scope">
                <div style="color: red">{{scope.row.financeRemark}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="operateTime" label="处理时间" width="90"></el-table-column>
            <el-table-column prop="refusalReason" label="未通过原由" width="100"></el-table-column>

            <el-table-column label="操作" fixed="right" width="100">
              <template slot-scope="scope">
                <div v-if="roleId==57||roleId==100||roleId==1">
                  <el-button v-if="roleId==57" :disabled="scope.row.state===1||scope.row.state===4||scope.row.state===5" @click.native.prevent="showRow(scope.row.id,scope.row.state)" type="text" size="small">操作</el-button>
                  <el-button  v-if="roleId==100" :disabled="scope.row.state===4||scope.row.state===5||scope.row.state===7" @click.native.prevent="showRow(scope.row.id,scope.row.state)" type="text" size="small">操作</el-button>
                </div>

              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <Page :total="pageInfo.total"
                  @on-change="onChange"
                  :show-total="true"
                  :show-sizer="true"
                  :page-size-opts="pageSizeOpts"
                  @on-page-size-change="onPageSizeChange"
                  :page-size="pageInfo.size"/>
          </div>
        </el-tab-pane>
        <el-dialog :visible.sync="dialogVisible">
          <el-form>
            <el-form-item label="处理结果">
              <el-tag v-if="dom.state===1" type="warning">待审核</el-tag>
              <el-tag v-if="dom.state===2" type="success">通过</el-tag>
              <el-tag v-if="dom.state===3" type="danger">不通过</el-tag>
              <el-tag v-if="dom.state===4" type="success">到账成功</el-tag>
              <el-tag v-if="dom.state===5" type="danger">到账失败</el-tag>
              <el-tag v-if="dom.state===6" type="danger">冻结</el-tag>
              <el-tag v-if="dom.state===7" type="warning">解冻</el-tag>
            </el-form-item>
          </el-form>
          <el-button type="primary" @click="innerVisible = true">更新</el-button>
          <br><br>
        </el-dialog>
        <el-dialog title="处理" :visible.sync="innerVisible" append-to-body center>
          <el-form>
            <el-form-item label="状态">
              <el-radio-group v-model="dom.state">
                <el-radio v-if="roleId==100" label="2">通过</el-radio>
                <el-radio v-if="roleId==100||roleId==2" label="3">不通过</el-radio>
                <el-radio v-if="roleId==57" label="4">到账成功</el-radio>
                <el-radio v-if="roleId==57" label="5">到账失败</el-radio>
                <el-radio v-if="roleId==1" label="6">冻结</el-radio>
                <el-radio v-if="roleId==1" label="7">解冻</el-radio>
              </el-radio-group>
            </el-form-item>
            <br>
            <el-form-item v-if="dom.state==='2'" label="审批备注">
              <el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="dom.financeRemark"> </el-input>
            </el-form-item>
            <el-form-item v-if="dom.state==='3'" label="未通过原由">
              <el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="dom.refusalReason"> </el-input>
            </el-form-item>
            <el-form-item v-if="dom.state==='6'" label="冻结原由">
              <el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="dom.remark"> </el-input>
            </el-form-item>
            <el-form-item v-if="dom.state==='5'" label="到账失败原由">
              <el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="dom.remark"> </el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="updateDeclareWithdrawalData">确定</el-button>
          </div>
        </el-dialog>
      </el-tabs>
    </div>
    <el-dialog
        title="导出出账明细："
        :visible.sync="czmxDialogFlag"
        width="80%">
      <el-form ref="form">
        <el-row>
          <el-col :span="9">
            <el-form-item label="提现时间">
              <el-date-picker v-model="dcmxDays" type="daterange" unlink-panels :picker-options="pickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="到账时间">
              <el-date-picker v-model="dzDays" type="daterange" unlink-panels :picker-options="dzPickerOptions"
                              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="3" style="margin-top: 30px;margin-left: 50px">
            <el-button type="info"
                       style="margin-bottom: 15px"
                       @click="exportDeclareWithdrawalPaymentOut"
                       icon="el-icon-download">导出
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
    <el-dialog
        title="提现来源："
        :visible.sync="dialog"
        width="90%">
      <div style="display: flex">
        <div>申报总金额：{{sumDeclareMoney||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<!--        <div>扣减总金额：{{sumDeductionsAmount||0.00}}</div>-->
<!--        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-->
        <div>结算总金额：{{sumWithdrawalAmount||0.00}}</div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <el-button type="info"
                   style="margin-bottom: 15px"
                   @click="exportDeclareWithdrawalDetails"
                   icon="el-icon-download">导出
        </el-button>
      </div>
<!--      <div style="font-size: 20px;color: red">申报类型员工/公司分成比率：</div>-->
<!--      <div style="font-size: 20px;color: red">1、空调加氨（7.5:2.5）  2、其他类材料（8.8:1.2）  3、车费（全返）</div>-->
      <el-table  :data="detailsList" :header-cell-style="{background:'#ddd'}" border class="table" ref="multipleTable">

        <el-table-column
            prop="productName"
            label="服务项目"
            width="100"
        ></el-table-column>
        <el-table-column
            prop="employeeNo"
            label="服务人员"
            width="75"
        ></el-table-column>
        <el-table-column prop="billNo" label="订单编号" width="170">
          <template slot-scope="scope">
            <el-link :href="'https://yun.xiaoyujia.com/order/baseinfo?BillNo='+scope.row.billNo" type="primary">{{scope.row.billNo}}</el-link>
          </template>
        </el-table-column>
        <el-table-column
            prop="amount"
            label="订单金额"
            width="80"
        ></el-table-column>
        <el-table-column
            prop="orderWage"
            label="人工费用"
            width="80"
        ></el-table-column>
        <el-table-column
            prop="realWage"
            label="人工实际结算"
            width="100"
        ></el-table-column>
        <el-table-column
            prop="proportion"
            label="人工结算比例"
            width="100"
        ></el-table-column>
        <el-table-column prop="ifAbnormal" label="是否异常" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.ifAbnormal===2" type="success">否</el-tag>
            <el-tag v-if="scope.row.ifAbnormal===1" type="danger">是</el-tag>
          </template>
        </el-table-column>
<!--        <el-table-column-->
<!--            prop="declareName"-->
<!--            label="申报类型"-->
<!--            width="80"-->
<!--        ></el-table-column>-->
        <el-table-column
            prop="declareMoney"
            label="申报金额"
            width="80"
        ></el-table-column>
<!--        <el-table-column-->
<!--            prop="deductionsAmount"-->
<!--            label="扣减金额"-->
<!--            width="80"-->
<!--        ></el-table-column>-->
        <el-table-column
            prop="withdrawalAmount"
            label="结算金额"
            width="80"
        ></el-table-column>
        <el-table-column
            prop="createTime"
            label="申报时间"
            width="150"
        ></el-table-column>
        <el-table-column prop="declareImgUrl" label="申报凭证" width="220">
          <template slot-scope="scope">
            <img :src="scope.row.declareImgUrl||blankImg"
                 style="width: 200px;height: 200px;" @click="lookImg(scope.row.declareImgUrl)">
          </template>
        </el-table-column>
        <el-table-column
            prop="remark"
            label="申报备注"
        ></el-table-column>
      </el-table>
    </el-dialog>
    <!--图片预览-->
    <el-dialog :visible.sync="imgModal" width="40%" title="图片预览" :mask-closable="false">
      <img :src="imageUrl!=null?imageUrl:blankImg" style="width: 100%;height: auto;margin: 0 auto;"/>
    </el-dialog>
    <el-link :href="hrefUrl" target="_blank" id="elLink"/>

  </div>
</template>

<script>
import moment from "moment";
import log from "echarts/src/scale/Log";
export default {
  name: "register",
  data() {
    return {
      logList: [],
      imgModal: false,
      handler: [],
      czmxDialogFlag: false,
      detailsList: [],
      imageUrl: '',
      roleId: localStorage.getItem('roleId'),
      hrefUrl: "",
      blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
      dialog: false,
      getOrderObj: {
        id: null,
      },
      storeId: null,
      recruitReturn: {},
      sumDeclareMoney: '',
      sumWithdrawalAmount: '',
      sumDeductionsAmount: '',
      dialogVisible: false,
      innerVisible: false,
      pageSizeOpts: [10, 20, 30],
      pageInfo: {total: 10, size: 10, current: 1, pages: 1},
      days: [],
      queryDays: [],
      dcmxDays: [],
      dzDays: [],
      form: {
        state: '',
        startTime: '',
        endTime: '',
        dzStartTime: '',
        dzEndTime: '',
        roleId: localStorage.getItem('roleId'),
        accountPeople: '',
        cashier: '',
        current: 1,
        size: 10
      },
      dom: {
        state: null,
        employeeId: localStorage.getItem("id"),
        id: null,
        refusalReason: null,
        financeRemark: null,
        remark: null,
      },
      options: [],
      dcmxQuery: {
        startTime: '',
        endTime: '',
        dzStartTime: '',
        dzEndTime: '',
      },
      dzPickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },

  created() {
    let obj = {
      value:  '1',
      label: '待审核'
    }
    this.options.push(obj)
      obj = {}
      obj.value = '2'
      obj.label='通过'
      this.options.push(obj)
      obj = {}
      obj.value = '3'
      obj.label='不通过'
      this.options.push(obj)
      obj = {}
      obj.value = '4'
      obj.label='到账成功'
      this.options.push(obj)
      obj = {}
      obj.value = '5'
      obj.label='到账失败'
      this.options.push(obj)
    if(this.roleId==1){
      obj = {}
      obj.value = '6'
      obj.label='冻结'
      this.options.push(obj)
      obj = {}
      obj.value = '7'
      obj.label='解冻'
      this.options.push(obj)
    }
    this.getData();
  },
  methods: {
    lookImg(val){
      this.imgModal = true
      this.imageUrl = val
    },
    exportDeclareWithdrawalPaymentOut(){
      if (this.dcmxDays != null && this.dcmxDays.length > 0) {
        this.dcmxQuery.startTime = moment(this.dcmxDays[0]).format("YYYY-MM-DD");
        this.dcmxQuery.endTime = moment(this.dcmxDays[1]).format("YYYY-MM-DD")
      }else{
        this.dcmxQuery.startTime = ""
        this.dcmxQuery.endTime = ""
      }
      if (this.dzDays != null && this.dzDays.length > 0) {
        this.dcmxQuery.dzStartTime = moment(this.dzDays[0]).format("YYYY-MM-DD");
        this.dcmxQuery.dzEndTime = moment(this.dzDays[1]).format("YYYY-MM-DD")
      }else{
        this.dcmxQuery.dzStartTime = ""
        this.dcmxQuery.dzEndTime = ""
      }
      let url = "https://biapi.xiaoyujia.com/storeWithdrawal/exportDeclareWithdrawalPaymentOut"
      if (this.dcmxQuery.startTime){
        url = url+"?startTime="+this.dcmxQuery.startTime+"&endTime="+ this.dcmxQuery.endTime
      }
      if (this.dcmxQuery.dzStartTime&&!this.dcmxQuery.startTime){
        url = url+"?dzStartTime="+this.dcmxQuery.dzStartTime+"&dzEndTime="+ this.dcmxQuery.dzEndTime
      }
      if(this.dcmxQuery.dzStartTime&&this.dcmxQuery.startTime){
        url = url+"&dzStartTime="+this.dcmxQuery.dzStartTime+"&dzEndTime="+ this.dcmxQuery.dzEndTime
      }
      this.hrefUrl = url
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    exportDeclareWithdrawalData(){
      this.form.startTime = '';
      this.form.endTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      if (this.queryDays != null && this.queryDays.length > 0) {
        this.form.dzStartTime = moment(this.queryDays[0]).format("YYYY-MM-DD");
        this.form.dzEndTime = moment(this.queryDays[1]).format("YYYY-MM-DD")
      }
      this.hrefUrl = "https://biapi.xiaoyujia.com/storeWithdrawal/exportDeclareWithdrawalData?state="+this.form.state+
          "&startTime="+this.form.startTime+"&endTime="+this.form.endTime+
          "&roleId="+this.roleId+"&accountPeople="+this.form.accountPeople+"&cashier="+this.form.cashier+
          "&dzStartTime=" + this.form.dzStartTime+"&dzEndTime=" + this.form.dzEndTime
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    exportDeclareWithdrawalDetails() {
      this.hrefUrl = "https://biapi.xiaoyujia.com/storeWithdrawal/exportDeclareWithdrawalDetails?id="+this.getOrderObj.id
      setTimeout(() => {
        document.getElementById("elLink").click();
      }, 1000);
    },
    getDeclareWithdrawalDetails(id){
      this.dialog = true;
      this.getOrderObj.id = id
      this.$getData("getDeclareWithdrawalDetails", this.getOrderObj, {}).then(res => {
        if (res.code == 0) {
          this.detailsList = res.data.employeePayDeclares
          this.sumWithdrawalAmount = res.data.sumWithdrawalAmount
          this.sumDeclareMoney = res.data.sumDeclareMoney
          this.sumDeductionsAmount = res.data.sumDeductionsAmount
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    },
    showRow(id, state) {
      this.dialogVisible = true;
      this.dom.state = state;
      this.dom.id = id;
    },
    updateDeclareWithdrawalData() {
      this.innerVisible = false;
      this.$postData("updateDeclareWithdrawalData", this.dom).then(res => {
        if (res.code === 0) {
          this.$message({message: res.data, type: 'success'});
          this.dom.financeRemark = ""
          this.dom.refusalReason = ""
          this.dom.remark = ""
        } else {
          this.$message({message: res.msg, type: 'warning'});
        }
        this.getData();
      })
      this.dialogVisible = false;
    },
    // 页码大小
    onPageSizeChange(size) {
      this.form.size = size;
      this.getData();
    },
    onChange(index) {
      this.form.current = index;
      this.getData();
    },
    getData() {
      this.form.startTime = '';
      this.form.endTime = '';
      this.form.dzStartTime = '';
      this.form.dzEndTime = '';
      if (this.days != null && this.days.length > 0) {
        this.form.startTime = moment(this.days[0]).format("YYYY-MM-DD");
        this.form.endTime = moment(this.days[1]).format("YYYY-MM-DD")
      }
      if (this.queryDays != null && this.queryDays.length > 0) {
        this.form.dzStartTime = moment(this.queryDays[0]).format("YYYY-MM-DD");
        this.form.dzEndTime = moment(this.queryDays[1]).format("YYYY-MM-DD")
      }
      this.$getData("getDeclareWithdrawalData", this.form, {}).then(res => {
        if (res.code == 0) {
          this.logList = res.data.records;
          this.pageInfo.current = res.data.current;
          this.pageInfo.size = res.data.size;
          this.pageInfo.total = res.data.total
        } else {
          this.$message.error("查询失败，" + res.msg);
        }
      })
    }
  }
}
</script>

<style scoped>
.num-box {
  padding: 10px;
  border: 1px solid #ddd;
}
</style>
