<template>
    <div v-loading="fullscreenLoading">
        <div class="container">

                <Row class="head-order">
                    <h3>订单信息</h3><divider></divider>
                    <Col span="6">
                        订单编号：{{dom.billNo}}
                    </Col>
                    <Col span="6">
                        服务项目：{{dom.productName}}
                    </Col>
                    <Col span="6">
                        订单状态：{{returnState(Number(dom.orderState))}}
                    </Col>
                    <Col span="6">
                        订单金额：
                        <span class="amount">
                          {{dom.realTotalAmount}}
                        </span>

                    </Col>
                    <Col span="6">
                        会员账号：{{dom.account}}
                    </Col>
                    <Col span="6">
                        会员号码：{{dom.bindTel}}
                    </Col>
                    <Col span="6">
                        会员名称：{{dom.name}}
                    </Col>


                </Row>
            <br>
            <Row>
                <el-tabs v-model="activeName" @tab-click="handleClick"  type="border-card">
                    <el-tab-pane label="订单信息" name="first">
                        <Row class="head-order">
                            <h4>开发信息：</h4>
                            <Col span="6">
                                开单人：{{dom.agentName}}({{dom.agentNo}})
                            </Col>
                            <Col span="6">
                                开发人(工号)：{{dom.channel}}
                            </Col>
                            <Col span="6">
                                申请时间：{{dom.createTime}}
                            </Col>
<!--                            <Col span="6">-->
<!--                                业务渠道：{{returnState(dom.orderState)}}-->
<!--                            </Col>-->
                            <br>
                            <br>
                            <h4>开发信息：</h4>
                            <Col span="6">
                                预约时间：{{dom.startTime}}
                            </Col>
                            <Col span="6">
                                服务地址：{{dom.street}}
                            </Col>

                            <Col span="24">
                                订单备注(客户要求)：
                                <el-input
                                        type="textarea"
                                        :rows="3"
                                        placeholder="请输入内容"
                                        v-model="dom.remark">
                                </el-input>
                                <el-button type="primary" @click="updateRemark()" style="float: right">确 定</el-button>

                                <div style="color: red">发布订单备注（服务内容，阿姨及客户可见）：</div>
                                <el-input
                                        type="textarea"
                                        :rows="3"
                                        placeholder="请输入内容"
                                        v-model="dom.serviceRemark">
                                </el-input>
                                <el-button type="primary" @click="updateServiceRemark()" style="float: right">确 定</el-button>
                                <br>
                            </Col>


                            <div style="width: 100%;height: 20px">&nbsp;</div>
                            <br>
                            <br>
                            <br>
                            <br>
                            <br>
                            <br>

                            <h4>金额信息</h4>
                            <Col span="6">
                                订单金额：
                                <span class="amount">
                                    ￥{{dom.realTotalAmount}}
                                </span>

                            </Col>
                            <Col span="6">
                                优惠金额：
                                <span class="amount">
                                    ￥{{dom.preferentialAmount}}
                                </span>
                            </Col>
                            <Col span="6">
                                已支付金额：
                                <span class="amount">
                                    ￥{{dom.amount}}
                                </span>
                            </Col>
                            <Col span="6">
                                可退款金额：
                                <span class="amount">
                                    ￥{{dom.qamount}}
                                </span>
                            </Col>
                            <br>
                            <br>

                            <h4>业务状态</h4>
                            <Col span="6">
                                业务状态：{{returnYwState(Number( dom.ywStatus))}}

                            </Col>
                            <Col span="6">
                                接单人：{{dom.jdPerpson}}
                            </Col>
                            <Col span="6">
                                指定服务员工(工号):{{dom.serviceNo}}
                            </Col>
                            <br>
                            <Col span="6">
                                可拆单总数：
                                {{dom.totalSplitNum}}
                            </Col>
                            <Col span="6">
                                剩余可拆单数：
                                {{dom.totalSplitNum-dom.splitNum}}
                            </Col>
                            <Col span="12">
                                <div class="block">
                                    <span class="demonstration">合同日期:</span>
                                    <el-date-picker
                                            v-model="serviceDate"
                                            type="datetimerange"
                                            range-separator="至"
                                            start-placeholder="开始日期"
                                            end-placeholder="结束日期"
                                            value-format="yyyy-MM-dd HH:mm:ss"
                                    >
                                    </el-date-picker>
                                    <el-button type="primary" @click="updateServiceDate()">更新</el-button>
                                </div>
<!--                                合同日期：-->
<!--                                {{dom.RealStartTime}}  - 至 - -->
<!--                                {{dom.RealEndTime}}-->
                            </Col>

                            <el-card class="box-card" v-show="dom.isGroup">
                                <h4>合同绑定信息：</h4>
                                <Col span="6">
                                    合同编号：
                                    {{contract.no}}
                                </Col>
                                <Col span="6">
                                    服务开始时间：
                                    {{contract.serviceStarDate}}
                                </Col>
                                <Col span="6">
                                    服务结束时间：
                                    {{contract.serviceEndDate}}
                                </Col>

                                <br>
                                <Col span="6">
                                    接单员工：
                                    {{contract.employeeName}}
                                </Col>
                                <Col span="6">
                                    员工工号：
                                    {{contract.employeeNo}}
                                </Col>
                                <Col span="6" v-show="dom.ywStatus==null">
                                    <el-button type="primary"  @click="setYw()" :disabled="dom.orderState>30"
                                               v-if="contract.employeeNo!==null
                                               && contract.employeeName!==null
                                               && contract.serviceEndDate!==null
                                               && contract.serviceStarDate!==null
                                               && contract.no!==null ">合同信息传入订单业务信息</el-button>
                                </Col>
                                <br>
<!--                                {{contract}}-->
                            </el-card>

                            <br>
                            <br>
                            <div v-show="dom.isGroup">
                            <h4>拆单信息</h4>
                                <Col span="6">
                                    拆单人：{{dom.splitUserName}}

                                </Col>
                                <Col span="6">
                                    拆单时间： {{dom.SplitTime}}
                                </Col>

                                <Col span="6">
                                    拆单数量：
                                    {{dom.splitNum}}
                                </Col>
                                <el-table
                                        :header-cell-style="{background:'#ddd'}"
                                        :data="list2"
                                        border
                                        style="width: 100%">
                                    <el-table-column
                                            fixed
                                            prop="billNo"
                                            label="订单号">
                                    </el-table-column>
                                    <el-table-column
                                            prop="orderState"
                                            label="订单状态">
                                        <template slot-scope="scope">
                                            {{returnState(Number(scope.row.orderState))}}
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                            prop="realTotalAmount"
                                            label="订单金额">
                                    </el-table-column>
<!--                                    <el-table-column-->
<!--                                            label="操作"-->
<!--                                            fixed="right">-->
<!--                                        <template slot-scope="scope">-->
<!--                                            <el-button size="mini" @click="todetail(scope.row.billNo)" type="primary">查看详情</el-button>-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
                                </el-table><br>
                            </div>
                            <div v-show="!dom.isGroup">
                            <h4>服务人员</h4>
                                <el-table
                                        :header-cell-style="{background:'#ddd'}"
                                        :data="waiterList"
                                        border
                                        style="width: 100%">
                                    <el-table-column
                                            fixed
                                            prop="serviceName"
                                            label="服务员工">
                                    </el-table-column>
                                    <el-table-column
                                            prop="serviceNo"
                                            label="员工工号">
                                    </el-table-column>
                                    <el-table-column
                                            prop="tel"
                                            label="手机号">
                                    </el-table-column>

                                </el-table><br>
                            </div>
                            <div v-show="!dom.isGroup">
                            <h4>服务薪酬</h4>
                                <el-table
                                        :header-cell-style="{background:'#ddd'}"
                                        :data="wagesList"
                                        border
                                        style="width: 100%">
                                    <el-table-column
                                            fixed
                                            prop="serviceName"
                                            label="服务员工">
                                    </el-table-column>
                                    <el-table-column
                                            prop="serviceNo"
                                            label="员工工号">
                                    </el-table-column>
                                    <el-table-column
                                            prop="realWage"
                                            label="员工薪酬">
                                    </el-table-column>

                                </el-table><br>
                            </div>
<!--                            {{list2}}-->
<!--                            {{dom}}-->
                           <center v-show="dom.isGroup">
                               <el-button type="warning" round size="danger" @click="changeRealTotalAmount()" v-show="!dom.isSplit">订单改价</el-button>
                               <el-button type="warning" round size="danger" @click="payImgVisible=true">收款码</el-button>
                               <el-button type="warning" round size="danger" @click="splitOrder()" v-show="dom.isGroup">拆单</el-button>
                           </center>


                            <divider></divider>

                            <el-dialog

                                    :visible.sync="changeRealTotalAmountVisible"
                                    >
                                <h4>订单编号：{{dom.billNo}}</h4>
                                    <center>
                                        <div>
                                            <el-input-number v-model="changeAmount" :precision="2" :step="0.1"  :min="dom.realTotalAmount"></el-input-number>
                                        </div>
                                        <Col span="6">
                                            订单金额：￥{{dom.realTotalAmount}}
                                        </Col>
                                        <Col span="6">
                                            改后金额：￥{{changeAmount}}
                                        </Col>
                                    </center>

                                <span slot="footer" class="dialog-footer">
                                <el-button @click="changeRealTotalAmountVisible = false">取 消</el-button>
                                <el-button type="primary" @click="changeRealTotal(),fullscreenLoading=true">确 定</el-button>
                              </span>
                            </el-dialog>
                            <el-dialog
                                    :visible.sync="payImgVisible"
                                    >
                                <center>
                                    <h4>付款码</h4>
                                    <img :src="payImg">
                                </center>

                            </el-dialog>




                        </Row>
                    </el-tab-pane>
                    <el-tab-pane label="操作日志" name="second">
                        <el-table
                                :header-cell-style="{background:'#ddd'}"
                                :data="logList"
                                border
                                style="width: 100%">
                            <el-table-column
                                    width="180"
                                    fixed
                                    prop="createTime"
                                    label="操作时间">
                            </el-table-column>
                            <el-table-column
                                    fixed
                                    width="100"
                                    prop="operator"
                                    label="操作人">
                            </el-table-column>
                            <el-table-column
                                    fixed
                                    width="180"
                                    prop="title"
                                    label="标题">
                            </el-table-column>
                            <el-table-column
                                    fixed
                                    prop="message"
                                    label="操作内容">
                            </el-table-column>
                        </el-table>
                    </el-tab-pane>
                    <el-tab-pane label="服务模块" name="third">
                        <el-table
                                :header-cell-style="{background:'#ddd'}"
                                :data="logList2"
                                border
                                style="width: 100%">
                            <el-table-column
                                    width="180"
                                    fixed
                                    prop="createTime"
                                    label="操作时间">
                            </el-table-column>
                            <el-table-column
                                    fixed
                                    width="100"
                                    prop="operator"
                                    label="操作人">
                            </el-table-column>
                            <el-table-column
                                    fixed
                                    width="180"
                                    prop="employeeNo"
                                    label="员工工号">
                            </el-table-column>
                            <el-table-column
                                    fixed
                                    prop="remark"
                                    label="说明">
                            </el-table-column>
                        </el-table>
                    </el-tab-pane>
<!--                    <el-tab-pane label="操作日志" name="third">操作日志</el-tab-pane>-->
                </el-tabs>

            </Row>

        </div>

    </div>
</template>

<script>

    export default {
        props:['orderBillNo'],
        name: "order",
        data() {
            return {
                billNo:this.orderBillNo,
                serviceDate:[],
                fullscreenLoading:false,
                payImgVisible:false,
                payImg:null,
                changeAmount:0,
                changeRealTotalAmountVisible:false,
                contract:{
                    no:null,
                },
                activeName: 'first',
                isPayoffoptions:[{
                    value:false,
                    label: '未结算'
                }, {
                    value: true,
                    label: '已结算'
                },],
                list:[],
                list2:[],
                logList:[],
                logList2:[],
                waiterList:[],
                wagesList:[],
                dom:{
                    serviceRemark:null,
                },
                model: {
                    //storeId:Number(localStorage.getItem("storeId")) ,
                    billNo:this.$route.query.billNo,
                    current: 1,
                    size: 10,
                },
                modelList: {
                    //storeId:Number(localStorage.getItem("storeId")) ,
                    sourceBillNo:this.$route.query.billNo,
                    current: 1,
                    size: 10,
                },
            }
        },
        created(){
            if (this.billNo!==null && this.billNo!=='' && this.billNo!==undefined ){
                this.model.billNo=this.billNo
                this.modelList.sourceBillNo=this.billNo
            }
            this.getData()
            this.getData2()
            this.orderOperationLogList()
            this.baomuEmployeeList()
            this.getOrderWaiterList()
            this.getOrderWagesList()

        },
        methods:{
            updateServiceRemark(){
                this.$postData("updateServiceRemark", this.dom, {}).then(res => {
                    if (res.status == 200) {
                        this.getData()
                        this.getData2()
                        this.$message.success("更新成功，");
                    }else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            updateRemark(){
                this.$postData("updateRemark", this.dom, {}).then(res => {
                    if (res.status == 200) {
                        this.getData()
                        this.getData2();
                        this.$message.success("更新成功，");
                    }else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            updateServiceDate(){
                this.dom.realStartTime=this.serviceDate[0]
                this.dom.realEndTime=this.serviceDate[1]
                console.log(this.serviceDate)
                this.$postData("updateServiceDate", this.dom, {}).then(res => {
                    if (res.status == 200) {
                        // this.getData()
                        // this.getData2()
                        this.$message.success("更新成功，");
                    }else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })
            },
            splitOrder(){
                if (this.dom.totalSplitNum-this.dom.splitNum<1){
                    return this.$message.error("没有拆单次数");
                }
                if (this.dom.realTotalAmount-this.dom.amount>0){
                    return this.$message.error("该订单还未付款");
                }
                this.fullscreenLoading=true;
                let splitOrder={
                    billNo:this.dom.billNo,
                    employeeNo:localStorage.getItem("account")
                };

                this.$getData("splitOrder", splitOrder, {}).then(res => {
                    if (res.status == 200) {
                        this.getData();
                        this.getData2();

                    }else {
                        this.fullscreenLoading=false
                        this.$message.error("拆单失败，" + res.msg);
                    }
                })
            },
            changeRealTotal(){
                let orderDto={
                    billNo:this.dom.billNo,
                    realTotalAmount:this.changeAmount,
                    oldRealTotalAmount:this.dom.realTotalAmount,
                    operator:localStorage.getItem("realName"),
                    operatorId:localStorage.getItem("id"),
                }
                this.$postData("changeRealTotalAmount", orderDto, {}).then(res => {
                    if (res.status == 200) {
                        this.getData()
                        this.getData2()
                        this.changeRealTotalAmountVisible=false
                    }
                })
            },
            changeRealTotalAmount(){
                this.changeRealTotalAmountVisible=true
            },
            setYw(){
                let ywDom={
                    ywStatus:3,
                    jdPerpson:this.contract.employeeName,
                    serviceNo:this.contract.employeeNo,
                    totalSplitNum:1,
                    RealStartTime:this.contract.serviceStarDate,
                    RealEndTime:this.contract.serviceEndDate,
                    billNo:this.dom.billNo,
                    operator:localStorage.getItem("realName"),
                    operatorId:localStorage.getItem("id"),
                }
                this.$postData("contractToOrder", ywDom, {}).then(res => {
                    if (res.status == 200) {
                        this.getData()
                        this.getData2()
                    }else {
                        this.$notify({
                            title: '提示',
                            message:res.msg,
                            duration: 0,
                            type: 'warning'
                        });
                    }
                })
                console.log(ywDom)
            },
            getContract(){
                this.$getData("getContractByOrderId", {orderId:this.dom.id}, {}).then(res => {
                    if (res.status == 200) {
                        if (res.data!==null) {
                            this.contract = res.data
                        }
                    }else {
                        this.$notify({
                            title: '提示',
                            message:res.msg,
                            duration: 0,
                            type: 'warning'
                        });
                    }
                })
            },
            getOrderWaiterList(){
                this.$getData("getOrderWaiterList", {billNo:this.$route.query.billNo}, {}).then(res => {
                    if (res.status == 200) {
                        if (res.data!==null){
                            this.waiterList = res.data
                        }

                    }
                })
            },
            getOrderWagesList(){
                this.$getData("getOrderWagesList", {billNo:this.$route.query.billNo}, {}).then(res => {
                    if (res.status == 200) {
                        this.wagesList = res.data
                    }
                })
            },
            todetail(billNo){
                let url=window.location.href
                window.open(url.substring(0,url.length-16)+billNo)
                //this.$router.push({path: 'orderInfo', query: {billNo: billNo}})
            },
            handleClick(tab, event) {
                console.log(tab, event);
            },
            returnYwState(value){
                switch (value) {
                    case 1:
                        return "咨询";
                    case 2:
                        return "已试用";
                    case 3:
                        return "已签单";
                    case 4:
                        return "已接单";
                    case 5:
                        return "已面试";
                    default:
                        return "未知";

                }
            },
            returnState(value){
                switch (value) {
                    case 10:
                        return '已接单';
                    case 20:
                        return '派单待确认';
                    case 30:
                        return '拒绝接单';
                    case 40:
                        return '已派单';
                    case 50:
                        return '执行中';
                    case 60:
                        return '开始服务';
                    case 70:
                        return '服务结束';
                    case 80:
                        return '已完成';
                    case 90:
                        return '已评价';
                    case 99:
                        return '已取消';
                    default:
                        return "未知"

                }
            },
            getData() {
                this.loading=true
                this.fullscreenLoading=true
                this.$postData("agentOrderPageOne", this.model, {}).then(res => {
                    if (res.status == 200) {

                        this.list = res.data;
                        if (this.list!==null){
                            this.dom=this.list[0]
                            this.changeAmount =this.dom.realTotalAmount
                        }
                        this.serviceDate= [new Date(this.dom.RealStartTime), new Date(this.dom.RealEndTime)],
                        this.getPay(this.dom.billNo)
                        this.getContract()




                        if (this.dom.RealStartTime==null){

                        }
                        this.fullscreenLoading=false
                    } else {
                        this.fullscreenLoading=false
                        this.$message.error("查询失败，" + res.msg);
                    }
                })


            },
            getData2() {
                this.loading=true
                this.$postData("agentOrderPageOne", this.modelList, {}).then(res => {
                    if (res.status == 200) {

                        this.list2 = res.data;

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })

            },
            orderOperationLogList() {
                this.loading=true
                this.$getData("orderOperationLogList", {
                    billNo:this.$route.query.billNo,
                }, {}).then(res => {
                    if (res.status == 200) {

                        this.logList = res.data;

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })

            },
            baomuEmployeeList() {
                this.loading=true
                this.$postData("baomuEmployeeList", {
                    billNo:this.$route.query.billNo,
                }, {}).then(res => {
                    if (res.status == 200) {

                        this.logList2 = res.data;

                    } else {
                        this.$message.error("查询失败，" + res.msg);
                    }
                })

            },
            getPay(billNo){
                let self=this;
                $.ajax({
                    type: "post",//type可以为post也可以为get
                    url: "https://appapi.xiaoyujia.com/api/Account/MakeQRCode?no="+billNo,
                    data:{},//这行不能省略，如果没有数据向后台提交也要写成data:{}的形式
                    dataType: "json",//这里要注意如果后台返回的数据不是json格式，那么就会进入到error:function(data){}中
                    success: function (data) {
                        console.log(data.code)
                        if (data.Meta.State==200){
                            self.payImg=data.Data.img

                        }else {
                            if (data.Meta.Msg=="订单不存在欠款"){
                                //self.active=0
                            }else {
                                //alert(""+data.Meta.Msg);
                            }

                        }

                    },
                    error: function (data) {
                        alert("出现了错误！");
                    }
                })
            },

        }
    }
</script>

<style scoped>
    h4{
        padding-left: 10px;
        border-left: 5px solid #409eff;
    }
    .amount{
        color: red;
    }
    .head-order{
        font-size: 14px;
        line-height: 30px;
    }

</style>
